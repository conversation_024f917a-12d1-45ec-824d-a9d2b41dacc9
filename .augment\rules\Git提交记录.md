---
type: "agent_requested"
description: "Example description"
---
# Git 提交规范

## 概述

本规范旨在统一项目的Git提交格式，提高代码管理效率和团队协作质量。所有提交必须遵循此规范。

## Commit Message 格式

### 基本格式
```
<类型>(<范围>): <简短描述>

[可选的详细描述]

[可选的脚注]
```

### 类型 (Type)
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 代码重构
- **perf**: 性能优化
- **build**: 构建系统或依赖变更
- **chore**: 其他维护性工作

### 范围 (Scope) - 可选
根据配音助手项目模块划分：
- **ui**: 用户界面
- **audio**: 音频处理
- **voice**: 语音合成
- **tts**: 文本转语音
- **player**: 播放器
- **editor**: 编辑器
- **export**: 导出功能
- **config**: 配置管理
- **api**: 接口相关
- **utils**: 工具函数
- **docs**: 文档

### 描述规则
- **简短描述**: 50字符以内，使用中文，动词开头，不加句号
- **详细描述**: 可选，72字符换行，说明what和why，不是how
- **脚注**: 可选，用于关联issue或breaking changes

## 提交示例

### 新功能提交
```bash
feat(voice): 添加Azure语音合成引擎支持

集成Microsoft Azure认知服务，提供更高质量的语音合成能力
- 支持50+种语言和200+种音色
- 实现SSML标记语言支持，可控制语速、音调、停顿
- 添加音色预览功能，用户可试听后选择
- 优化音频缓存机制，提升合成速度30%
- 增加批量合成功能，支持长文本分段处理

技术实现：
- 使用Azure Speech SDK进行API调用
- 实现音频流式传输，减少等待时间
- 添加错误重试机制和降级策略

Closes #123
```

### 修复提交
```bash
fix(player): 修复音频播放器在Safari浏览器下的兼容性问题

解决Safari浏览器下音频播放器进度条不更新和音量控制失效的问题

问题描述：
- Safari浏览器下进度条显示卡顿，不能实时更新播放进度
- 音量滑块拖拽后音量不生效
- 播放完成后状态未正确重置

解决方案：
- 使用requestAnimationFrame替代setInterval更新进度条
- 修复Safari下audio元素volume属性设置时机问题
- 添加ended事件监听，确保播放完成状态正确处理
- 增加浏览器兼容性检测和降级处理

测试覆盖：Safari 14+, Chrome, Firefox, Edge

Fixes #456
```

### 性能优化提交
```bash
perf(audio): 优化音频文件加载和缓存策略

通过预加载和智能缓存机制，将音频加载时间减少60%

优化内容：
- 实现音频文件预加载，用户选择音色时提前加载常用音频
- 添加LRU缓存算法，智能管理音频缓存，避免内存溢出
- 使用Web Workers处理音频数据，避免主线程阻塞
- 实现音频文件压缩，在保证质量前提下减少文件大小40%
- 添加CDN加速支持，根据用户地理位置选择最近节点

性能提升：
- 首次加载时间：从3.2s降至1.2s
- 切换音色响应时间：从800ms降至200ms
- 内存占用减少35%

测试数据基于1000个用户样本的A/B测试结果
```

### 重构提交
```bash
refactor(editor): 重构文本编辑器组件架构

将单体编辑器组件拆分为多个可复用的子组件，提升代码可维护性

重构内容：
- 将TextEditor组件拆分为：
  * TextInput: 负责文本输入和基础编辑
  * FormatToolbar: 处理文本格式化工具栏
  * SpeechMarker: 管理语音标记和SSML标签
  * PreviewPanel: 实时预览和播放控制
- 使用Context API管理编辑器状态，减少props传递
- 实现组件懒加载，提升初始化性能
- 统一事件处理机制，简化组件间通信
- 添加完整的TypeScript类型定义

代码质量提升：
- 代码行数减少25%
- 组件复用率提升40%
- 单元测试覆盖率达到85%
- ESLint警告从47个降至0个

无功能变更，向后兼容
```

### 构建相关提交
```bash
build(deps): 升级React到18.2.0并优化构建配置

升级核心依赖并优化Webpack配置，提升开发体验和构建性能

更新内容：
- React: 16.14.0 → 18.2.0
- React-DOM: 16.14.0 → 18.2.0  
- TypeScript: 4.5.2 → 4.9.4
- Webpack: 5.65.0 → 5.75.0

构建优化：
- 启用React 18的并发特性，提升UI响应性
- 配置Webpack 5的持久化缓存，构建速度提升50%
- 使用SWC替代Babel进行代码转换，编译速度提升3倍
- 优化代码分割策略，减少首屏加载资源大小
- 添加Bundle Analyzer，可视化分析打包结果

兼容性处理：
- 更新所有React Hooks用法适配新版本
- 修复TypeScript类型检查问题
- 确保所有第三方库兼容性

构建结果：
- 开发环境启动时间：8s → 3s
- 生产环境构建时间：120s → 45s
- 打包文件大小减少15%
```

### 文档更新提交
```bash
docs: 完善配音助手API文档和用户使用指南

新增详细的API文档和用户操作指南，提升开发者和用户体验

新增内容：
- API文档：
  * 语音合成接口完整参数说明和示例
  * 音频处理API使用方法和最佳实践
  * 错误码对照表和常见问题解决方案
  * Postman测试集合和环境配置

- 用户指南：
  * 快速入门教程，10分钟上手配音助手
  * 高级功能使用说明（SSML标记、批量处理等）
  * 音色选择和调优技巧
  * 导出格式和质量设置建议
  * 常见问题FAQ和故障排除

- 开发文档：
  * 项目架构说明和技术选型理由
  * 本地开发环境搭建指南
  * 代码规范和提交流程说明
  * 单元测试编写指南

文档特色：
- 所有示例代码均可直接运行
- 配有详细的截图和操作步骤
- 支持中英文双语版本
- 提供在线交互式演示

更新README.md，添加项目徽章和贡献指南
```

## 提交流程

### 1. 提交前检查
```bash
# 检查当前状态
git status

# 查看具体变更
git diff

# 测试环境打包
yarn uat

# 正式环境打包
yarn pro
```

### 2. 暂存文件
```bash
# 添加特定文件
git add <文件名>

# 添加所有变更（谨慎使用）
git add .

# 交互式添加
git add -p
```

### 3. 提交变更
```bash
# 标准提交
git commit -m "feat(voice): 添加新的语音合成引擎"

# 带详细描述的提交
git commit -m "feat(voice): 添加新的语音合成引擎" -m "支持更多语音类型和音色选择"
```

### 4. 推送到远程
```bash
# 推送到当前分支
git push

# 首次推送新分支
git push -u origin <分支名>
```

## 分支管理策略

### 分支命名规范
- **main**: 正式环境分支，稳定版本
- **test**: 测试环境分支
- **feature/<功能名>**: 功能分支，根据需求创建
- **hotfix/<修复名>**: 紧急修复分支

### 分支工作流程
1. 根据需求创建feature分支进行开发
2. 开发完成后使用merge合并到test分支

### 合并策略
- 统一使用merge进行分支合并
- 保持提交历史的完整性
- 合并前确保目标分支是最新状态

### 构建部署
```bash
# 测试环境打包
yarn uat

# 正式环境打包  
yarn pro
```


## 规范遵循

- 所有提交应遵循此规范格式
- 保持提交信息的清晰和一致性
- 定期回顾和优化提交质量

---

*此规范会根据项目发展和团队反馈持续更新*