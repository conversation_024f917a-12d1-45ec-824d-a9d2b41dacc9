# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Playwright测试相关
global-setup.js
playwright.config.js
screenshots/
tests/
test-results/
playwright-report/
.cursor/



# 开发调试文件
temp/

# MCP 相关文件
.augment/mcp-config-local.json
.augment/mcp-sessions/
.augment/mcp-logs/

# 注意：docs/ 文件夹包含项目文档，会被版本控制但不会打包到生产环境
# 注意：.augment/mcp-config.json 和 .augment/mcp-config-desktop.json 会被版本控制作为模板
