# 视频编辑器应用 - Vue 3 + Vite

这是一个基于Vue 3和Vite构建的现代视频编辑器Web应用，旨在提供直观、高效的视频内容创作和编辑体验。

## 项目概述

该应用提供了以下核心功能：
- 内容创作编辑
- 视频素材管理和编辑
- 音频/音乐添加与管理
- 配音角色选择
- 视频导入到云剪辑进行精准编辑
- 保存和导出功能

## 最近更新

### 2025年1月最新功能

#### 数字人编辑器交互优化
- **右键菜单功能**: 新增右键上下文菜单，支持一键删除数字人、装饰图片、字幕等元素，大幅提升编辑效率。
- **导航栏定制化**: 为数字人编辑器页面专门设计的导航栏，包含专用返回按钮和项目标题实时编辑功能。
- **项目标题管理**: 支持在编辑过程中随时修改项目名称，提供内联编辑体验和智能占位符提示。

#### 主页展示功能增强
- **轮播图Banner系统**: 在主页新增响应式轮播图组件，支持多图轮播、外链跳转和智能页面定位。
- **响应式布局优化**: 实现了完整的响应式Banner定位系统，从移动设备到大型桌面显示器的全设备适配。
- **视觉体验提升**: 根据不同屏幕尺寸动态调整界面布局，确保在各种设备上的最佳展示效果。

#### 用户体验及流程优化
- **登录校验增强**: 为首页的"数字人"功能入口增加了前置登录校验。未登录用户在访问该功能时，会被自动引导至登录弹窗，确保了核心功能的使用流程完整性。
- **用户声明"不再提示"**: 优化了数字人编辑器中"生成视频"前的用户声明弹窗。用户同意声明后，该选择将被持久化记录在浏览器中，后续操作将不再重复弹出，提升了用户体验和操作效率。

### 2025年7月最新功能

#### 数字人编辑器拉伸功能修复
- **问题修复**: 解决了背景层和数字人拉伸时第一次正常，再次拉伸突然变大的问题
- **技术改进**:
  - 修正拉伸基准计算逻辑，使用当前实际尺寸而不是原始尺寸
  - 完整实现背景模块拉伸功能，支持保持宽高比的智能缩放
  - 优化所有可拉伸元素（数字人角色、背景模块、装饰图片、字幕）的拉伸逻辑
- **用户体验**: 拉伸操作更加流畅和可预期，避免了意外的尺寸跳跃

### 2025年3月最新功能

#### 口型同步预览提示
- **功能描述**: 在数字人预览编辑器中，首次点击播放时，会显示一个提示，告知用户预览时暂不支持口型对齐，但最终生成时会匹配。
- **用户体验**: 用户可以选择"不再提示"，此选择将被浏览器`localStorage`记住，后续不再弹出。
- **UI实现**: 提示框经过多次样式迭代，最终实现了与产品UI风格一致的视觉效果。

#### 视频素材管理优化
- **视频删除与选中状态同步**：优化视频删除逻辑，确保在右侧预览区删除视频时，只取消左侧对应视频的选中状态
  - 使用videoId作为唯一标识符实现精确匹配
  - 改进了PreviewPanel和VideoEditing组件间的通信机制
  - 优化了用户交互体验，减少误操作
  - 完善了持久化存储管理，确保从localStorage彻底删除视频数据，防止页面刷新后恢复选中状态
- **修复视频选中状态持久化问题**：解决了删除视频后刷新页面仍会恢复选中状态的问题
  - 将selectedVideoIds添加到Pinia的持久化存储配置中
  - 确保刷新页面后状态保持一致
  - 优化了状态恢复逻辑，提升用户体验

#### HeaderBar组件优化
- **数据安全保障**：添加未保存更改检测和提示功能，防止用户意外丢失数据
- **美化弹窗样式**：重新设计消息框和确认对话框，提供更精美的视觉体验
  - 添加了动画效果和过渡
  - 优化了标题和关闭按钮的对齐
  - 改进了按钮交互体验和悬浮效果
- **增强错误处理**：添加详细的错误信息提取和显示功能
- **改进加载状态**：优化加载状态管理，防止重复操作
- **响应式设计**：添加移动设备适配，确保在各种屏幕尺寸下的良好体验

#### 数字人预览编辑器位置数据系统
- **位置数据获取功能**：新增PreviewEditor组件完整的位置数据管理系统
  - 实时位置数据收集：支持数字人角色、装饰图片、字幕的精确位置追踪
  - 自动事件发射机制：拖拽/拉伸结束时自动向父组件发送位置数据
  - 主动获取接口：提供`getAllPositionsData()`等方法供父组件主动调用
  - 完整状态管理：包含位置(x,y)、尺寸(width,height)、缩放比例、显示状态等
  - 接口集成就绪：数据格式可直接传递给后端API保存
- **组件通信优化**：使用defineEmits和defineExpose提供完整的父子组件通信方案
  - 4种事件类型：positionUpdate、characterMoved、secondImageMoved、subtitleMoved
  - 15个暴露方法：涵盖数据获取、显示控制、选择控制、位置重置等功能
- **数据持久化支持**：支持布局配置的保存、恢复、导入导出功能

#### 用户体验提升
- **操作安全性**：重要操作前添加确认对话框，防止误操作
- **视觉反馈**：优化成功/错误消息提示样式，提供更直观的操作反馈
- **交互优化**：按钮状态优化，提供加载和禁用状态的视觉区分

## 技术栈

- **前端框架**：Vue 3 (使用Composition API和setup语法糖)
- **构建工具**：Vite
- **UI库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **样式处理**：SCSS with scoped
- **HTTP客户端**：Axios

## 项目结构

### 顶层目录结构
```
d:\dubbing-assistant\
├── README.md            # 项目文档
├── index.html           # 入口HTML文件
├── package.json         # 项目依赖配置
├── public\              # 公共资源目录
├── server\              # 服务器相关代码
└── src\                 # 源代码目录
```

### src 目录结构 (核心代码)
```
src\
├── App.vue              # 根组件
├── api\                 # API接口目录
│   ├── account.js       # 账户相关API
│   ├── creation.js      # 创作相关API
│   ├── dubbing.js       # 配音相关API
│   ├── index.js         # API入口
│   ├── material.js      # 素材相关API
│   ├── music.js         # 音乐相关API
│   ├── videoEditing.js  # 视频编辑API
│   └── voiceOver.js     # 配音相关API
├── api_my\              # 额外API接口目录
│   ├── AlDubb\          # AI配音相关
│   ├── auth\            # 认证相关
│   └── commercialDubb\  # 商业配音相关
├── assets\              # 静态资源
│   ├── images\          # 图片资源
│   ├── img\             # 图片资源
│   └── sass\            # SASS样式文件
├── common\              # 公共代码
│   ├── constants\       # 常量定义
│   ├── enums\           # 枚举定义
│   └── utils\           # 工具函数
├── components\          # 公共组件目录
│   ├── AlbumDialog\     # 专辑对话框组件
│   ├── AudioUpload\     # 音频上传组件
│   ├── SubtitleDialog\  # 字幕对话框组件
│   ├── VideoPreview\    # 视频预览组件
│   ├── common\          # 通用组件
│   └── global\          # 全局组件
├── config\              # 配置文件目录
├── router\              # 路由配置
│   └── modules\         # 路由模块
├── stores\              # Pinia状态管理
│   ├── index.js         # 状态管理入口
│   ├── previewStore.js  # 预览状态管理
│   ├── modules\         # 状态管理模块
│   │   ├── musicStore.js    # 音乐状态管理
│   │   ├── AIDubbing.js     # AI配音状态
│   │   └── soundStore.js    # 音效状态管理
├── styles\              # 全局样式
├── utils\               # 工具函数
└── views\               # 视图/页面组件
    ├── Editor\          # 编辑器页面
    │   ├── ContentCreation\ # 内容创作
    │   ├── MusicAudio\      # 音乐音频
    │   ├── VideoEditing\    # 视频编辑
    │   ├── VoiceOver\       # 配音功能
    │   └── components\      # 编辑器组件
    ├── VideoEditing\    # 视频编辑页面
    ├── account\         # 账户相关页面
    ├── layout\          # 布局组件
    └── modules\         # 功能模块
        ├── AIDubbing\       # AI配音模块
        ├── compose\         # 编辑组合模块
        ├── home\            # 首页模块
        ├── extraction\      # 内容提取模块
        ├── income\          # 收入模块
        ├── mySpace\         # 个人空间模块
        └── soundStore\      # 音效商店模块
```

## 核心功能说明

### 1. 预览功能 (PreviewStore)

预览功能是应用的核心，允许用户在不同编辑阶段看到内容效果。

**主要功能**:
- 显示内容预览
- 管理预览标题和内容
- 跟踪更新时间
- 管理选中的角色、视频和音乐
- 提取视频内容和时间戳

### 2. 标题栏 (HeaderBar)

标题栏提供应用的主要操作按钮和导航功能。

**主要功能**:
- 显示当前页面标题
- 提供返回首页按钮（带未保存内容检测）
- 提供保存功能（带错误处理和加载状态）
- 提供"去精剪"功能，可将视频导入到云剪辑中进行精准剪辑
- 美观的UI设计和响应式布局

### 3. 音乐管理 (MusicStore)

音乐管理功能允许用户添加、删除和控制背景音乐。

**主要功能**:
- 添加音乐
- 删除音乐
- 控制音乐播放状态
- 管理音乐音量
- 更新音乐URL

### 4. 视频编辑功能 (VideoEditing)

VideoEditing是视频编辑器中的视频成片页面，负责视频素材选择、智能匹配和视频生成功能。

#### 主要功能区域

##### 4.1 视频成片工具
- **匹配模式切换**：支持专辑模式和视频模式
- **视频模式特性**：
  - 筛选栏：支持日期范围、资源库选择
  - 视频素材网格：展示可选择的视频素材
  - 文件上传功能：支持上传自定义视频
- **专辑模式特性**：
  - 专辑选择：以专辑为单位选择视频集合

##### 4.2 预览工具
- 视频播放器：预览选中的视频素材

##### 4.3 匹配设置工具
- 匹配度设置：控制视频匹配的精确程度
- 匹配优先级：设置匹配的优先考量因素
- 导出设置：分辨率、音频、配音、字幕和特效选项
- 存储选项：自动存储至云空间等设置

#### 数据流

##### 视频数据流
- **输入源**：视频素材列表、用户上传、用户选择
- **中间处理**：选中状态管理、预览列表更新
- **输出目标**：生成最终视频、预览展示

##### 设置数据流
- **设置管理**：参数配置与本地存储

##### 提取内容数据流
- **处理流程**：视频JSON数据处理、提取文本内容与时间信息

#### 主要功能方法

- **初始化相关**：视频列表获取、设置加载、状态恢复
- **交互处理**：工具切换、视频选择、专辑选择
- **视频生成**：处理生成请求、媒体制作API调用
- **素材管理**：添加/删除视频、对话框交互

#### 字幕处理功能

- **SRT文件解析**：从文件或URL获取字幕内容
- **时间码处理**：解析和格式化SRT格式时间码
- **字幕转换**：将字幕转换为应用可用的时间线格式
- **特殊情况处理**：编码转换、格式修复、时间重叠处理

### 5. 数字人功能 (DigitalHuman)

数字人功能提供完整的数字人视频合成与编辑能力，核心组件是PreviewEditor，支持数字人角色、装饰图片和字幕的精确位置控制。

#### 核心组件：PreviewEditor

##### 5.1 交互系统
- **智能点击选择**：支持多层级元素的精确选择，自动处理重叠区域优先级
- **悬停自动选中**：1秒悬停自动选中机制，优化用户体验
- **拖拽系统**：高性能拖拽实现，支持跨预览窗口边界操作
- **拉伸控制**：多方向拉伸支持，保持宽高比的智能缩放
  - **拉伸功能修复** (2025年7月最新):
    - 修复了第一次拉伸正常，再次拉伸时突然变大的问题
    - 改进拉伸基准计算，使用当前实际尺寸而不是原始尺寸
    - 完整实现背景模块拉伸功能，支持3:2宽高比保持
    - 优化所有元素（数字人、背景、装饰图片、字幕）的拉伸逻辑
- **口型同步预览提示** (新功能):
  - **一次性提示**: 首次播放时显示提示，告知用户预览时口型暂不对齐。
  - **可关闭**: 用户可选择"不再提示"，该选择将被`localStorage`持久化记录，避免重复打扰。

##### 5.2 元素管理
- **数字人角色**：
  - 响应式尺寸适配（16:9、9:16等比例）
  - 智能边界限制与位置优化
  - 实时位置跟踪和状态管理
- **装饰图片**：
  - Logo、图标等辅助元素管理
  - 独立的层级控制和交互逻辑
  - 正方形比例保持和尺寸控制
- **字幕系统**：
  - 文本内容编辑与样式控制
  - 6方向拉伸支持（左上、左中、左下、右上、右中、右下）
  - 动态字体和对齐设置

##### 5.3 位置数据系统 ⭐ (最新功能)
- **实时数据收集**：
  ```javascript
  // 获取所有元素完整位置数据
  const positionsData = getAllPositionsData();
  // 返回格式包含：x, y, width, height, scaleX, scaleY, offsetX, offsetY, visible, active等
  ```
- **事件发射机制**：
  - `@position-update`: 全局位置更新事件
  - `@character-moved`: 数字人角色移动事件  
  - `@second-image-moved`: 装饰图片移动事件
  - `@subtitle-moved`: 字幕移动事件
- **主动获取接口**：
  ```javascript
  // 父组件可通过ref调用
  previewEditorRef.value.getAllPositionsData()
  previewEditorRef.value.getElementPosition('character')
  previewEditorRef.value.resetAllPositions()
  ```
- **数据格式**：
  - 位置坐标(x, y)、尺寸(width, height)
  - 缩放比例(scaleX, scaleY)、用户偏移(offsetX, offsetY)
  - 显示状态(visible)、选中状态(active)
  - 预览窗口信息、时间戳等元数据

##### 5.4 状态管理
- **digitalHumanStore**：集中管理数字人模块状态
- **时间轴控制**：播放状态、当前时间、总时长管理
- **字幕同步**：字幕加载状态和内容同步
- **背景管理**：动态背景色变更和事件处理

#### 技术实现特性

##### 性能优化
- 使用原生JavaScript变量避免响应式开销
- 智能边界检测和限制算法
- 事件监听器的及时清理防止内存泄漏

##### 响应式设计
- 多宽高比支持（16:9横屏、9:16竖屏）
- 预览窗口智能尺寸计算
- 操作区域动态扩展

##### 数据持久化
- 位置配置的保存与恢复
- 用户操作历史记录
- 项目状态的导入导出

### 6. 预览面板 (PreviewPanel)

PreviewPanel是视频编辑器右侧的预览面板，负责显示视频标题、文本内容预览，并提供配音角色、音乐和视频素材的添加与管理功能。

#### 主要功能区域

##### 6.1 顶部标题栏
- **标题输入框**：用户可以输入视频的标题
- **功能按钮**：包含生成视频等核心功能按钮

##### 6.2 素材选择区
- **配音角色管理**：
  - 添加/删除音色
  - 音量调节
  - 选中状态显示
- **音乐素材管理**：
  - 添加/删除音乐
  - 音量调节
  - 多音乐支持
- **视频素材管理**：
  - 添加/删除视频
  - 显示已选视频信息
- **播放控制**：
  - 预览播放/暂停
  - 统一控制音频播放

##### 6.3 内容预览区
- **可编辑文本区域**：用户可以直接编辑视频文案
- **提取内容展示**：时间线模式下显示带时间戳的内容
- **使用指南**：内容为空时显示引导信息
- **快速清空功能**：一键清空内容

#### 交互功能

##### 文本编辑
- 支持直接编辑文本内容
- 自动过滤表情符号
- 自动滚动到内容底部
- 支持复制粘贴操作
- 中文输入法组合输入支持

##### 音频预览
- 播放/暂停配音和音乐
- 音频同步播放
- 配音结束后自动停止

#### 数据持久化

##### Pinia Store存储
- **标题与内容**：同步到PreviewStore
- **配音角色信息**：同步角色名称、声音类型和音频URL
- **音乐列表**：同步到MusicStore
- **视频列表**：同步到PreviewStore
- **提取内容与时间线状态**：保存时间线模式和内容

##### localStorage存储
- **音量设置**：保存各类素材的音量设置
- **持久化时机**：组件挂载、数据变化、路由变化和组件卸载前

#### 交互事件
- **内容管理**：输入、粘贴、中文输入处理
- **音频控制**：音频预览、音量调节
- **素材管理**：添加/删除角色、音乐和视频
- **对外接口**：提供内容提取的接口方法

## 开发指南

### 常见问题解决

#### 时间相关问题
如果遇到日期计算、时区处理、日历系统等时间相关问题，请优先查看 [`progress.md`](./progress.md) 文件中的"时间相关问题解决经验总结"章节。该文档记录了项目中已解决的时间问题及其解决方案，包括：

- JavaScript时间创建最佳实践
- Monday-first日历系统实现
- 日期范围计算注意事项
- 时区问题处理方案

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 环境配置
项目支持多环境配置:
- 开发环境: `npm run dev`
- 生产环境: `npm run build`
- UAT环境: `npm run uat`
- PRO环境: `npm run pro`

## 项目规范

- 使用Vue 3的Composition API进行开发
- 使用setup语法糖简化组件编写
- 组件样式使用SCSS并添加scoped属性隔离
- 使用Pinia进行状态管理
- 模块化组织代码，保持组件的可复用性
- 增强用户体验：添加加载状态、操作确认和错误处理

## Playwright 自动化测试

### 安装

已安装Playwright和必要的浏览器，包括Chromium、Firefox和WebKit。

### 运行测试

使用以下命令运行测试：

```bash
# 运行所有测试
npx playwright test

# 在UI模式下运行测试
npx playwright test --ui

# 运行特定的测试文件
npx playwright test tests/example.spec.js

# 运行标记的测试
npx playwright test -g "test name"

# 在特定浏览器上运行测试
npx playwright test --project=chromium

# 启动交互式界面
npx playwright test --debug
```

### 查看测试报告

运行测试后，可以使用以下命令查看HTML报告：

```bash
npx playwright show-report
```

### 测试脚本示例

`tests/example.spec.js` 文件包含基本的测试示例，包括：
- 打开应用
- 验证标题
- 在不同设备尺寸下测试UI布局
- 截图保存