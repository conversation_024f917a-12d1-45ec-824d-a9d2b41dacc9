# AI 配音功能详解

## 功能概述

AI 配音是应用的核心功能之一，提供媲美真人的 AI 配音服务。该功能允许用户输入文本，选择音色，调整语速、语调等参数，生成高质量的配音音频。同时，系统还支持多音字处理、读音替换、停顿调整、背景音乐和音效添加等高级功能，满足不同场景的配音需求。

## 核心功能模块

### 1. 文本编辑器

#### 1.1 基本功能
- **文本输入**：支持直接输入文本内容
- **字数统计**：实时显示文本字数，限制最大字数为 5000 字
- **自动保存**：定时自动保存文本内容
- **文本格式化**：自动过滤表情符号，保持文本格式一致

#### 1.2 高级编辑功能
- **导入文案**：支持从外部导入文本内容
- **读音替换**：支持选中文字，指定特定读音
- **停顿调整**：支持在文本中插入停顿标记
- **数字符号处理**：支持数字的智能读法调整
- **查看拼音**：支持显示文本对应的拼音
- **查找替换**：支持文本内容的批量查找替换
- **多语种适配**：支持多种语言的混合输入和处理

#### 1.3 技术实现
- 使用 `contenteditable` 实现富文本编辑
- 使用 `pinyin-pro` 库处理中文拼音
- 使用 `nanoid` 生成唯一标识符
- 自定义文本处理算法处理特殊情况

### 2. 音色选择系统

#### 2.1 音色分类
- **音色类型**：SVIP、VIP、普通音色等
- **性别/年龄**：男、女、青年、中年、老年、少年等
- **场景分类**：新闻、解说、情感、广告等
- **推荐标签**：热门、新上线、精品等

#### 2.2 音色筛选
- **多级筛选**：支持四级联动筛选
- **搜索功能**：支持按名称、情感标签、场景分类搜索
- **排序功能**：支持按匹配度、热门程度排序
- **收藏功能**：支持收藏常用音色

#### 2.3 音色预览
- **试听功能**：支持音色试听
- **音色详情**：显示音色的详细信息
- **音色推荐**：基于用户选择推荐相似音色

#### 2.4 技术实现
- 使用 Pinia 进行状态管理
- 使用响应式计算属性实现筛选逻辑
- 使用虚拟滚动优化大量音色的显示性能

### 3. 参数调整系统

#### 3.1 基本参数
- **语速调整**：支持调整语速，范围 0.5-2.0 倍
- **语调调整**：支持调整语调，实现不同情感效果
- **音量调整**：支持调整配音音量

#### 3.2 高级参数
- **情感选择**：支持选择不同情感，如开心、悲伤、愤怒等
- **语言增强**：支持多语言混合文本的处理
- **停顿控制**：支持精确控制停顿时长

#### 3.3 技术实现
- 使用滑块组件实现参数调整
- 使用 API 参数映射实现参数转换
- 使用本地存储记住用户偏好设置

### 4. 背景音乐与音效

#### 4.1 背景音乐
- **音乐库**：提供丰富的背景音乐选择
- **音量控制**：支持调整背景音乐音量
- **循环设置**：支持设置音乐循环播放

#### 4.2 音效添加
- **音效库**：提供各类音效素材
- **音效插入**：支持在文本特定位置插入音效
- **音效预览**：支持音效试听

#### 4.3 技术实现
- 使用 Web Audio API 处理音频
- 使用 Pinia 存储音频状态
- 使用事件总线实现组件间通信

### 5. 合成与下载

#### 5.1 合成功能
- **试听合成**：快速合成预览效果
- **正式合成**：生成高质量配音
- **批量合成**：支持多段文本批量合成

#### 5.2 下载选项
- **MP3 格式**：标准音质，文件小
- **WAV 格式**：高音质，文件大
- **字幕下载**：支持导出字幕文件

#### 5.3 技术实现
- 使用 API 调用云端合成服务
- 使用 Blob 和 URL.createObjectURL 处理下载
- 使用加载状态管理用户体验

### 6. AI 辅助功能

#### 6.1 AI 音色匹配
- **功能描述**：根据文本内容自动推荐适合的音色
- **技术实现**：使用 NLP 分析文本情感和主题
- **用户体验**：减少用户选择成本，提高配音效果

#### 6.2 AI 文案创作
- **功能描述**：提供 AI 生成的文案模板
- **技术实现**：使用模板系统和 AI 生成技术
- **用户体验**：解决用户"不知道写什么"的问题

## 数据流

### 1. 文本处理流程
1. 用户输入/导入文本
2. 文本预处理（过滤表情符号、格式化等）
3. 特殊处理（多音字、读音替换、停顿等）
4. 生成处理后的文本
5. 发送到合成 API

### 2. 音色选择流程
1. 加载音色列表
2. 应用筛选条件
3. 用户选择音色
4. 更新选中状态
5. 保存到 Pinia store

### 3. 合成流程
1. 收集文本和参数
2. 发送合成请求
3. 接收合成结果
4. 处理音频数据
5. 播放或下载

## API 接口

### 1. 音色相关接口
- `Sound_ListApi`：获取音色列表
- `Sound_tabs_listApi`：获取音色分类标签
- `filter_sound_listApi`：筛选音色
- `bookmarkList`：获取收藏音色列表
- `bookmarkToggle`：切换音色收藏状态

### 2. 合成相关接口
- `synthesized_speechApi`：合成语音
- `batchCreateAPI`：批量创建合成任务
- `extractWavByMp3`：从 MP3 提取 WAV 格式

### 3. 辅助功能接口
- `queryTextAPI`：查询文本信息
- `selectVoiceByAI`：AI 选择音色
- `templateList`：获取文案模板列表
- `chekSensitive_Api`：敏感内容检查

## 状态管理

### 1. AIDubbing Store
- 管理配音相关状态
- 存储选中的音色信息
- 存储背景音乐 URL
- 存储合成参数

### 2. Sound Store
- 管理音色列表
- 存储音色筛选条件
- 存储收藏音色列表

### 3. 本地存储
- 存储用户偏好设置
- 存储最近使用的音色
- 存储草稿内容

## 组件结构

### 1. 主要组件
- `AIDubbing.vue`：主组件
- `audioPlayer.vue`：音频播放器
- `trialListeningDialog.vue`：试听对话框
- `aiMatch.vue`：AI 匹配组件
- `findReplacement.vue`：查找替换组件
- `multilingualPopup.vue`：多语种适配组件
- `InsertEmotion.vue`：插入情感组件

### 2. 辅助组件
- `GlobalimportLetter.vue`：导入文案组件
- `aiCopyWritingCreate.vue`：AI 文案创作组件
- `aiCopyWritingTempalteMore.vue`：更多模板组件

## 用户交互流程

### 1. 基本使用流程
1. 输入或导入文本
2. 选择音色
3. 调整参数
4. 点击试听
5. 满意后点击合成下载

### 2. 高级使用流程
1. 输入或导入文本
2. 处理多音字和读音替换
3. 添加停顿和音效
4. 选择音色并调整参数
5. 添加背景音乐
6. 试听并调整
7. 合成并下载

## 最近更新

### 1. 多语种适配功能
- **功能描述**：支持多种语言混合文本的处理
- **技术实现**：使用语言检测和适配算法
- **用户体验**：提升多语言场景下的配音质量

### 2. 查找替换功能
- **功能描述**：支持文本内容的批量查找替换
- **技术实现**：使用正则表达式和文本处理
- **用户体验**：提高文本编辑效率

### 3. 情感插入功能
- **功能描述**：支持在文本中插入情感标记
- **技术实现**：使用特殊标记和 API 参数
- **用户体验**：实现更丰富的情感表达

## 使用指南

### 1. 基本操作
- **输入文本**：直接在编辑区域输入文本
- **选择音色**：从右侧面板选择音色
- **调整参数**：使用滑块调整语速、语调等参数
- **试听**：点击试听按钮预览效果
- **合成下载**：点击合成下载按钮生成音频

### 2. 高级功能
- **导入文案**：点击导入文案按钮，从外部导入文本
- **读音替换**：选中文字，点击读音替换按钮，输入替换读音
- **停顿调整**：点击停顿按钮，在文本中插入停顿标记
- **数字符号处理**：选中数字，点击数字符号按钮，选择读法
- **背景音乐**：点击背景音乐按钮，选择并添加背景音乐
- **音效添加**：点击音效按钮，选择并添加音效
- **查看拼音**：点击查看拼音按钮，显示文本对应的拼音

### 3. 快捷键
- **Ctrl+Z**：撤销操作
- **Ctrl+Y**：重做操作
- **Ctrl+S**：保存草稿
- **Ctrl+F**：查找替换
- **Ctrl+Space**：试听

## 常见问题解决

### 1. 多音字处理
- **问题**：中文中存在多音字，可能导致读音错误
- **解决方案**：使用读音替换功能指定正确读音

### 2. 数字读法
- **问题**：数字有多种读法，如个位数、整数、小数等
- **解决方案**：使用数字符号功能选择合适的读法

### 3. 停顿控制
- **问题**：默认停顿可能不符合语义需求
- **解决方案**：使用停顿功能在适当位置添加停顿标记

### 4. 音量平衡
- **问题**：配音和背景音乐音量不平衡
- **解决方案**：使用音量滑块调整各部分音量

### 5. 合成失败
- **问题**：文本过长或包含特殊字符可能导致合成失败
- **解决方案**：分段合成或去除特殊字符

## 未来规划

### 1. 功能增强
- **批量处理**：支持多段文本批量处理
- **语音编辑**：支持对合成后的语音进行编辑
- **更多语言支持**：增加更多语言的支持
- **自定义音色**：支持用户上传自定义音色

### 2. 性能优化
- **合成速度提升**：优化合成算法，提高合成速度
- **界面响应优化**：提升界面交互流畅度
- **资源加载优化**：优化资源加载策略

### 3. 用户体验提升
- **更智能的 AI 推荐**：提升 AI 推荐的准确性
- **更丰富的模板**：增加更多文案模板
- **更直观的操作**：优化操作流程，减少学习成本