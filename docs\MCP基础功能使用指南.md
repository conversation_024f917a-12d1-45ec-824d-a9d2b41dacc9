# MCP 基础功能使用指南

## 当前状态

✅ **Python 环境**：Python 3.13.5 已安装并正常工作
✅ **MCP 基础设施**：项目已有 @modelcontextprotocol/server-filesystem
✅ **配置文件**：已创建 .augment/mcp-config-temp.json
⏳ **等待激活**：需要重命名配置文件并在 Augment 中配置

## 立即可用的解决方案

### 第一步：激活 MCP 配置

```powershell
# 在项目根目录运行
mv .augment/mcp-config-temp.json .augment/mcp-config.json
```

### 第二步：在 Augment 中配置

1. 打开 Augment 设置
2. 找到 MCP 服务器配置
3. 导入 `.augment/mcp-config.json` 文件
4. 启用 filesystem MCP 服务器

### 第三步：开始使用

现在您可以在 AI 对话中使用以下功能：

#### 文件操作
- **读取文件**：AI 可以读取项目中的任何文件
- **写入文件**：AI 可以创建和修改文件
- **列出目录**：AI 可以查看目录结构

#### 实际应用场景
1. **代码审查**：AI 可以读取您的代码文件并提供建议
2. **文档生成**：AI 可以基于代码自动生成文档
3. **配置管理**：AI 可以帮助修改配置文件
4. **项目分析**：AI 可以分析整个项目结构

## 使用示例

### 示例 1：代码审查
```
请帮我审查 src/components/PreviewEditor.vue 文件，检查是否有性能问题
```

### 示例 2：文档更新
```
请根据 src/api 目录下的文件，更新 docs/API文档.md
```

### 示例 3：配置优化
```
请检查 vite.config.js 文件，看看是否可以优化构建配置
```

## 与配音助手项目的集成

### 项目特定功能
- **Vue 组件分析**：AI 可以分析您的 Vue 组件结构
- **API 接口检查**：AI 可以检查 API 调用的一致性
- **样式优化**：AI 可以分析 CSS/SCSS 文件
- **配置验证**：AI 可以验证各种配置文件

### 开发工作流程
1. **需求分析**：描述需要实现的功能
2. **代码实现**：AI 帮助编写或修改代码
3. **测试验证**：AI 帮助创建测试用例
4. **文档更新**：AI 自动更新相关文档

## 后续升级计划

### 当网络问题解决后
1. **安装完整版 mcp-feedback-enhanced**
2. **启用 Web UI 界面**
3. **添加图片上传功能**
4. **启用提示词管理**
5. **开启会话追踪功能**

### 升级命令
```powershell
# 清理缓存
pip cache purge

# 重新安装
pip install --user --no-cache-dir mcp-feedback-enhanced

# 更新配置
# 将 .augment/mcp-config-desktop.json 内容复制到 .augment/mcp-config.json
```

## 故障排除

### 常见问题

**Q: MCP 服务器无法启动**
A: 检查 Node.js 是否正常工作：`node --version`

**Q: 文件路径错误**
A: 确保配置文件中的路径使用双反斜杠：`d:\\配音助手\\dubbing-assistant`

**Q: 权限问题**
A: 确保 Augment 有读写项目目录的权限

**Q: AI 无法访问文件**
A: 检查 MCP 服务器状态是否为绿色（已连接）

## 技术细节

### 当前配置说明
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "node",
      "args": [
        "node_modules/@modelcontextprotocol/server-filesystem/dist/index.js",
        "d:\\配音助手\\dubbing-assistant"
      ],
      "timeout": 600,
      "autoApprove": [
        "read_file",
        "write_file", 
        "list_directory"
      ]
    }
  }
}
```

### 安全设置
- **autoApprove**：自动批准文件操作，提高使用效率
- **timeout**：10分钟超时，适合大文件操作
- **路径限制**：仅限项目目录，确保安全性

## 下一步

1. **立即执行**：`mv .augment/mcp-config-temp.json .augment/mcp-config.json`
2. **配置 Augment**：导入 MCP 配置文件
3. **开始使用**：尝试让 AI 读取项目文件
4. **反馈问题**：如有问题请及时反馈

现在您就可以开始享受 MCP 带来的强大功能了！
