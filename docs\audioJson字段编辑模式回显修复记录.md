# audioJson字段编辑模式回显修复记录

## 📋 问题概述

修复了数字人编辑器在编辑模式下，`audioJson` 中的 `wav_text`、`wav_url` 和 `type` 字段无法正确从 `getDigitalWork` 接口返回的原始数据中获取和赋值的问题。

## 🎯 问题现象

### 用户反馈
在编辑现有数字人作品时，发现以下问题：
```javascript
audioJson: {
    type: "text_captions", 
    tts: {text: ["欢迎使用蝉镜数字人视频合成服务！"], speed: 1, audio_man: "", pitch: 100},
    duration: 0,
    language: "cn",
    // ... 其他字段
    wav_name: "",
    wav_text: "",      // ❌ 应该包含原始字幕文本，但为空
    wav_url: "",       // ❌ 应该包含原始音频URL，但为空
    type: "text_captions"  // ❌ 类型不正确，应该从原始数据获取
}
```

### 预期行为
编辑模式下，`audioJson` 应该包含从 `getDigitalWork` 接口返回的原始数据：
- `wav_text`: 原始字幕文本内容
- `wav_url`: 原始音频文件URL
- `type`: 正确的音频类型

## 🔍 问题分析

### 根本原因
在编辑模式下，`buildSaveParams` 方法重新构建了 `audioJson`，而没有使用从 `getDigitalWork` 接口返回的原始数据。

### 问题流程
```
编辑模式流程：
1. loadWorkData 获取原始的 workData（包含正确的 audioJson）
2. getCurrentEditorData 没有传递原始数据  ❌
3. buildSaveParams 重新构建 audioJson        ❌
4. 丢失了原始的 wav_text、wav_url 和 type   ❌
```

### 数据丢失环节
- **原始数据获取**：`getDigitalWork` 接口返回完整的 `audioJson`
- **数据传递断层**：`getCurrentEditorData` 未传递原始工作数据
- **数据重构问题**：`buildSaveParams` 基于当前编辑器状态重新构建，丢失原始字段

## 🔧 解决方案

### 整体修复策略
通过保存原始工作数据并在生成视频时优先使用原始 `audioJson`，确保编辑模式下数据的完整性。

### 修复步骤

#### 步骤1：数字人编辑页面添加原始数据存储

**文件**：`src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

**新增变量**：
```javascript
// 存储原始工作数据（编辑模式时从接口获取的完整数据）
const originalWorkData = ref(null);
```

#### 步骤2：loadWorkData 中保存原始数据

**在获取作品数据后立即保存**：
```javascript
// 调用接口获取作品详情数据
const workData = await getDigitalWork({ id: parseInt(workId) });

if (!workData) {
    loadingError.value = '获取作品数据失败，请稍后重试';
    ElMessage.error('获取作品数据失败，请稍后重试');
    return;
}

// 🔄 保存原始工作数据（供生成视频时使用）
originalWorkData.value = workData;
console.log('💾 已保存原始工作数据:', {
    audioJson: workData.audioJson,
    audioJsonKeys: workData.audioJson ? Object.keys(workData.audioJson) : null
});
```

#### 步骤3：getCurrentEditorData 传递原始数据

**在收集编辑器数据时传递原始数据**：
```javascript
// 收集所有配置数据
const editorData = {
    // ... 现有字段
    
    // 🔄 原始工作数据（编辑模式专用）
    originalWorkData: originalWorkData.value,
    
    // ... 其他字段
};
```

#### 步骤4：buildSaveParams 优先使用原始 audioJson

**文件**：`src/views/layout/components/headbar/components/action/index.vue`

**关键修复逻辑**：
```javascript
// 🔄 检查是否为编辑模式且有原始工作数据
const originalWorkData = editorData?.originalWorkData;
let audioJson = {};

if (isEditMode.value && originalWorkData && originalWorkData.audioJson) {
    // 🎯 编辑模式：优先使用原始audioJson，确保wav_text、wav_url、type等字段完整保留
    audioJson = {
        ...originalWorkData.audioJson,  // 保留所有原始字段
        // 可以在这里覆盖特定字段，如果需要的话
    };
    
    console.log('🔄 编辑模式：使用原始audioJson数据', {
        来源: 'originalWorkData.audioJson',
        包含字段: Object.keys(audioJson),
        wav_url: audioJson.wav_url,
        wav_text: audioJson.wav_text,
        type: audioJson.type
    });
} else {
    console.log('🆕 新建模式或无原始数据：重新构建audioJson');
}

// 🔄 只有在新建模式或编辑模式无原始数据时才重新构建audioJson
if (!originalWorkData || !originalWorkData.audioJson || !isEditMode.value) {
    if (modeType === 'aduio_captions') {
        // ... 原有的重新构建逻辑
    }
}
```

#### 步骤5：新建模式数据清理

**确保新建模式下原始数据被正确清空**：
```javascript
// 新建模式检测
if (!workId) {
    // 🆕 新建模式：清空原始工作数据
    originalWorkData.value = null;
    console.log('🆕 新建模式：已清空原始工作数据');
    
    // ... 其他清理逻辑
}
```

**clearMiddleAreaData 方法中也清空原始数据**：
```javascript
const clearMiddleAreaData = () => {
    try {
        // ... 现有清理逻辑
        
        // 6. 清空原始工作数据
        originalWorkData.value = null;

    } catch (error) {
        // 清空中间预览区域数据失败，保持原有状态
    }
};
```

## 📊 修复效果对比

### 修复前（问题状态）
```javascript
// 编辑模式下的 audioJson（❌ 数据丢失）
{
    type: "text_captions",           // 固定值，不是原始值
    wav_url: "",                     // ❌ 丢失原始音频URL
    wav_text: "",                    // ❌ 丢失原始字幕文本
    wav_name: "",                    // ❌ 丢失原始音频名称
    // ... 其他重新构建的字段
}
```

### 修复后（正确状态）
```javascript
// 编辑模式下的 audioJson（✅ 数据完整）
{
    type: "audio",                   // ✅ 统一使用"audio"类型
    wav_url: "https://example.com/audio.wav",  // ✅ 原始音频URL
    wav_text: "原始字幕文本内容",     // ✅ 原始字幕文本
    wav_name: "original_audio.wav",  // ✅ 原始音频名称
    duration: 120,                   // ✅ 原始音频时长
    // ... 所有原始字段完整保留
}
```

## 🎯 技术要点

### 1. 原始数据保存机制
- 在 `loadWorkData` 中立即保存完整的原始数据
- 使用独立的 `originalWorkData` 变量避免与编辑状态混淆
- 新建模式下确保原始数据为空

### 2. 数据传递链路
- `DigitalHumanEditorPage` → `getCurrentEditorData` → `action/index.vue`
- 通过 `editorData.originalWorkData` 字段传递
- 保持数据流的清晰和可追踪

### 3. 优先级处理逻辑
- **编辑模式 + 有原始数据**：优先使用原始 `audioJson`
- **新建模式 + 无原始数据**：重新构建 `audioJson`
- **异常情况**：降级到重新构建逻辑

### 4. 向后兼容性
- 不影响新建模式的正常功能
- 保持原有的 `audioJson` 构建逻辑作为备选
- 现有API和数据格式完全兼容

## ✅ 验证要点

### 编辑模式验证
1. **数据完整性**：
   - `wav_url` 包含原始音频URL
   - `wav_text` 包含原始字幕文本
   - `type` 为原始音频类型（如 "audio_drive"）

2. **功能正常性**：
   - 编辑模式下生成视频正常
   - 音频播放功能正常
   - 字幕显示正确

3. **日志验证**：
   ```
   💾 已保存原始工作数据: { audioJson: {...}, audioJsonKeys: [...] }
   🔄 编辑模式：使用原始audioJson数据 { 来源: "originalWorkData.audioJson", ... }
   ```

### 新建模式验证
1. **数据清理**：
   - `originalWorkData.value` 为 `null`
   - 使用重新构建的 `audioJson` 逻辑

2. **功能正常性**：
   - 新建作品功能不受影响
   - 音频生成和处理正常

3. **日志验证**：
   ```
   🆕 新建模式：已清空原始工作数据
   🆕 新建模式或无原始数据：重新构建audioJson
   ```

## 📚 相关文档

- `docs/commonJson添加第二层数字人图片URL功能.md` - 类似的编辑模式数据回显功能
- `docs/bgJson和personJson位置坐标保存回显功能.md` - 位置数据回显实现
- `docs/getDigitalWork接口数据存储和获取指南.md` - 数据获取和存储指南

## 🔧 补充修复：type字段统一

### 问题说明
在两个接口（`saveDigitalWork` 和 `editDigitalWork`）传递的 `audioJson.type` 字段中，发现存在不一致的问题：
- 音频驱动模式：使用 `"aduio_captions"`
- 输入文本模式：使用 `"text_captions"`

### 修复内容
将两种模式下的 `type` 字段统一修改为 `"audio"`：

**修改前**：
```javascript
// 音频驱动模式
type: "aduio_captions"

// 输入文本模式  
type: "text_captions"
```

**修改后**：
```javascript
// 两种模式统一使用
type: "audio"
```

### 修复文件
- `src/views/layout/components/headbar/components/action/index.vue`
  - 第305行：音频驱动模式的type字段
  - 第347行：输入文本模式的type字段

### 修复原因
- 确保两个接口传递参数的一致性
- 避免因type字段不同导致的后端处理差异
- 简化音频类型的识别逻辑

## 🎉 完成状态

✅ 原始工作数据保存机制实现完成  
✅ 数据传递链路修复完成  
✅ audioJson 优先级使用逻辑完成  
✅ 编辑模式字段完整性修复完成  
✅ 新建模式兼容性保证完成  
✅ 详细调试日志记录完成  
✅ 功能验证测试完成  
✅ **type字段统一修复完成** ⭐ **新增**

该修复确保了编辑模式下 `audioJson` 中 `wav_text`、`wav_url`、`type` 等关键字段能够正确从原始数据中获取，解决了编辑模式下数据丢失的问题，同时保持了对新建模式的完整兼容性。同时统一了两种模式下的 `type` 字段值，确保接口参数的一致性。 