# bgJson和personJson位置坐标保存回显功能实现记录

## 📋 功能概述

实现了数字人编辑器中第一层背景（bgJson）和第二层数字人（personJson）的x、y坐标保存和回显功能，确保用户手动调整的位置能够正确保存并在重新打开作品时恢复。

## 🎯 需求背景

用户反馈：
- bgJson里面的x和y是中间第一层背景层的x和y坐标
- personJson里面的x和y是中间第二层数字人层的x和y坐标
- 需要在保存作品时记录当前位置，在重新打开作品时恢复位置

## 🔧 技术实现

### 1. PreviewEditor组件增强

#### 1.1 getAllPositionsData方法扩展
**文件：** `src/views/modules/digitalHuman/components/PreviewEditor.vue`

**修改内容：**
- 在getAllPositionsData方法中添加backgroundModule（第一层背景）的位置数据
- 确保返回完整的位置信息包括背景模块、数字人角色、装饰图片和字幕

**代码要点：**
```javascript
const getAllPositionsData = () => {
    return {
        // 背景模块数据（第一层背景）
        backgroundModule: {
            x: backgroundModuleX.value,
            y: backgroundModuleY.value,
            width: backgroundModuleWidth.value,
            height: backgroundModuleHeight.value,
            // ...其他属性
        },
        // 数字人角色数据（第二层数字人）
        character: {
            x: characterX.value,
            y: characterY.value,
            width: characterWidth.value,
            height: characterHeight.value,
            // ...其他属性
        },
        // ...其他元素
    };
};
```

#### 1.2 位置设置方法添加
**新增方法：**
- `setBackgroundPosition(bgJson)` - 设置背景模块位置
- `setCharacterPosition(personJson)` - 设置数字人位置

**功能特点：**
- 基于初始位置计算偏移量，确保位置设置的准确性
- 支持位置和尺寸的同时设置
- 包含完整的错误处理和调试日志
- 调用后自动触发位置更新事件

#### 1.3 defineExpose暴露新方法
**文件：** `src/views/modules/digitalHuman/components/PreviewEditor.vue`

**新增暴露方法：**
```javascript
defineExpose({
    // 现有方法...
    // 🎨 位置设置方法（用于数据回显）
    setBackgroundPosition,      // 设置背景模块位置
    setCharacterPosition,       // 设置数字人位置
    setSubtitleConfig,          // 设置字幕配置
});
```

### 2. 保存逻辑优化

#### 2.1 action/index.vue修改
**文件：** `src/views/layout/components/headbar/components/action/index.vue`

**主要改进：**
- 将获取位置数据的来源从`canvasData`改为`positionsData`
- 使用PreviewEditor组件的实时位置数据而非缓存数据
- 确保bgJson和personJson包含准确的x、y坐标

**关键修改：**
```javascript
// 构建 personJson - 使用PreviewEditor实时位置数据
const personJson = {
    id: leftPanelData.digitalHuman?.id || editorData?.digitalHumanConfig?.id || "8217cb18710849d7acbf2c6da9d002e9",
    x: positionsData.character?.x || 0,        // 实时X坐标
    y: positionsData.character?.y || 480,      // 实时Y坐标
    width: digitalHumanWidth,
    height: digitalHumanHeight,
    // ...其他属性
};

// 构建 bgJson - 使用PreviewEditor实时位置数据
const bgJson = {
    src_url: backgroundImageUrl,
    x: positionsData.backgroundModule?.x || 0, // 实时X坐标
    y: positionsData.backgroundModule?.y || 0, // 实时Y坐标
    width: backgroundWidth,
    height: backgroundHeight
};
```

### 3. 回显逻辑实现

#### 3.1 DigitalHumanEditorPage.vue增强
**文件：** `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

**在loadWorkData方法中新增：**

**3.1.1 背景层位置回显**
```javascript
// 6. 背景层位置回显处理
if (workData.bgJson && typeof workData.bgJson === 'object') {
    await nextTick();
    if (previewEditorRef.value && previewEditorRef.value.setBackgroundPosition) {
        previewEditorRef.value.setBackgroundPosition(workData.bgJson);
        console.log('✅ 已通知PreviewEditor设置背景位置');
    }
}
```

**3.1.2 数字人层位置回显**
```javascript
// 7. 数字人层位置回显处理
if (workData.personJson && typeof workData.personJson === 'object') {
    await nextTick();
    if (previewEditorRef.value && previewEditorRef.value.setCharacterPosition) {
        previewEditorRef.value.setCharacterPosition(workData.personJson);
        console.log('✅ 已通知PreviewEditor设置数字人位置');
    }
}
```

#### 3.2 getCurrentEditorData方法优化
**新增positionsData数据传递：**
```javascript
// 收集所有配置数据
const editorData = {
    // 现有数据...
    // 🎯 实时位置数据（背景、数字人、字幕的完整位置信息）
    positionsData: positionsData,
    // 保持向后兼容的canvasData
    canvasData: canvasData
};
```

## 📊 数据流程

### 保存流程
1. 用户点击保存按钮
2. action/index.vue调用getCurrentEditorData()获取编辑器数据
3. getCurrentEditorData()从PreviewEditor获取实时位置数据
4. 构建bgJson和personJson时使用实时位置坐标
5. 发送到后端API保存

### 回显流程
1. 页面加载时调用loadWorkData()
2. 通过getDigitalWork API获取作品数据
3. 解析bgJson和personJson中的位置信息
4. 调用PreviewEditor的setBackgroundPosition和setCharacterPosition方法
5. PreviewEditor根据位置信息计算偏移量并更新显示

## 🔍 测试验证

### 测试步骤
1. **创建作品并调整位置**
   - 新建数字人作品
   - 拖拽背景层到新位置
   - 拖拽数字人层到新位置
   - 保存作品

2. **验证保存数据**
   - 检查保存请求的bgJson.x、bgJson.y字段
   - 检查保存请求的personJson.x、personJson.y字段
   - 确认坐标值与实际位置一致

3. **验证回显功能**
   - 重新打开保存的作品
   - 确认背景层位置正确恢复
   - 确认数字人层位置正确恢复
   - 验证位置与保存前一致

### 日志监控
- 保存时：观察action/index.vue中的位置数据来源日志
- 回显时：观察DigitalHumanEditorPage.vue中的位置设置日志
- PreviewEditor：观察位置计算和设置的详细日志

## 🚀 优势特点

### 1. 实时数据获取
- 不依赖缓存数据，直接从PreviewEditor获取当前位置
- 避免位置数据不一致问题

### 2. 精确位置计算
- 基于初始位置和用户偏移量计算最终位置
- 支持位置和尺寸的同时处理

### 3. 完整错误处理
- 每个环节都有try-catch保护
- 失败时不影响其他功能的正常执行

### 4. 向后兼容
- 保持原有的canvasData格式
- 新增positionsData不影响现有代码

### 5. 详细日志
- 完整的调试信息输出
- 便于问题排查和功能验证

## 📝 注意事项

1. **组件依赖**：确保PreviewEditor组件已正确挂载后再调用位置设置方法
2. **坐标系统**：所有坐标都是相对于预览窗口左上角(0,0)的像素坐标
3. **异步处理**：使用nextTick确保DOM更新完成后再进行位置设置
4. **类型检查**：对传入的bgJson和personJson进行类型验证

## 🔧 相关文件

- `src/views/modules/digitalHuman/components/PreviewEditor.vue` - 位置管理核心组件
- `src/views/layout/components/headbar/components/action/index.vue` - 保存逻辑
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` - 回显逻辑
- `docs/commonJson添加第二层数字人图片URL功能.md` - 相关功能文档

## 🎉 完成状态

✅ PreviewEditor组件位置数据获取和设置方法完成  
✅ 保存逻辑使用实时位置数据完成  
✅ 回显逻辑实现完成  
✅ 错误处理和日志完成  
✅ 向后兼容处理完成  
✅ 文档记录完成  

该功能现已完整实现，用户可以正常使用背景层和数字人层的位置保存和回显功能。 