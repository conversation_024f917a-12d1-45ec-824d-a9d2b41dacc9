# bgJson背景图片真实尺寸修复

## 问题描述

用户反馈保存数字人作品时，`bgJson`下面的`height`和`width`字段不应该是硬编码的1920×1080，而应该使用背景图片的真实尺寸数据。

问题出现在保存接口传参时，`bgJson`的`width`和`height`使用的是：
1. `positionsData.backgroundModule?.width` - 用户拖拽调整后的尺寸（优先级最高）
2. 回退到 `editorData?.screenWidth || 1080` 和 `editorData?.screenHeight || 1920`（硬编码默认值）

但实际的背景图片原始尺寸可能并非1920×1080，导致回显数据不正确。

## 修复方案

### 解决思路
**动态获取背景图片的真实尺寸**，而不是依赖硬编码的默认值。

### 技术实现

在`src/views/layout/components/headbar/components/action/index.vue`的`buildSaveParams`函数中，修改bgJson的构建逻辑：

#### 1. 新增图片尺寸获取函数

```javascript
/**
 * 动态获取背景图片真实尺寸的辅助函数
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<{width: number, height: number}>} 图片的真实尺寸
 */
const getImageRealSize = (imageUrl) => {
    return new Promise((resolve) => {
        if (!imageUrl || imageUrl.trim() === '') {
            // 如果没有图片URL，返回默认尺寸
            resolve({ width: 1080, height: 1920 });
            return;
        }
        
        const img = new Image();
        img.onload = function() {
            console.log('🖼️ 背景图片真实尺寸获取成功:', {
                图片URL: imageUrl,
                真实宽度: this.naturalWidth,
                真实高度: this.naturalHeight
            });
            resolve({ 
                width: this.naturalWidth, 
                height: this.naturalHeight 
            });
        };
        img.onerror = function() {
            console.warn('⚠️ 背景图片加载失败，使用默认尺寸:', imageUrl);
            // 图片加载失败时，使用默认尺寸
            resolve({ width: 1080, height: 1920 });
        };
        // 设置跨域属性，避免CORS问题
        img.crossOrigin = 'anonymous';
        img.src = imageUrl;
    });
};
```

#### 2. 修改尺寸获取逻辑

实施三级优先级策略：

```javascript
// 🔧 关键修复：获取背景图片的真实尺寸
let backgroundWidth, backgroundHeight;

// 优先级1：使用用户拖拽调整后的尺寸（如果存在）
if (positionsData.backgroundModule?.width && positionsData.backgroundModule?.height) {
    backgroundWidth = positionsData.backgroundModule.width;
    backgroundHeight = positionsData.backgroundModule.height;
    // 用户已经调整过背景尺寸，直接使用
} 
// 优先级2：动态获取背景图片的真实尺寸
else if (backgroundImageUrl && backgroundImageUrl.trim() !== '') {
    try {
        const realSize = await getImageRealSize(backgroundImageUrl);
        backgroundWidth = realSize.width;
        backgroundHeight = realSize.height;
        // 动态获取图片原始尺寸
    } catch (error) {
        console.error('❌ 获取背景图片真实尺寸失败:', error);
        // 获取失败时使用默认值
        backgroundWidth = editorData?.screenWidth || 1080;
        backgroundHeight = editorData?.screenHeight || 1920;
    }
} 
// 优先级3：使用默认尺寸（向后兼容）
else {
    backgroundWidth = editorData?.screenWidth || 1080;
    backgroundHeight = editorData?.screenHeight || 1920;
}
```

#### 3. 函数异步化处理

由于新增了异步图片加载逻辑，需要将相关函数标记为async：

```javascript
// 修改前
const buildSaveParams = (editorData) => {

// 修改后  
const buildSaveParams = async (editorData) => {
```

```javascript
// 修改调用处
// 修改前
const saveParams = buildSaveParams(editorData);

// 修改后
const saveParams = await buildSaveParams(editorData);
```

## 修复效果

### 数据流程优化

```
背景图片尺寸获取流程：
1. 检查用户是否拖拽调整过背景
   ├─ 是：直接使用用户调整的尺寸
   └─ 否：进入自动获取流程
       ├─ 2. 检查是否有背景图片URL
       │   ├─ 是：动态加载图片获取真实尺寸
       │   └─ 否：使用默认尺寸
       └─ 3. 异常处理：图片加载失败时降级使用默认尺寸
```

### 修复前后对比

| 场景 | 修复前 | 修复后 |
|------|--------|--------|
| 用户调整背景 | ✅ 使用调整尺寸 | ✅ 使用调整尺寸 |
| 有背景图片 | ❌ 使用1920×1080 | ✅ **获取图片真实尺寸** |
| 无背景图片 | ✅ 使用默认尺寸 | ✅ 使用默认尺寸 |
| 图片加载失败 | ❌ 使用错误尺寸 | ✅ 优雅降级到默认尺寸 |

### 关键改进

1. **数据准确性**：`bgJson.width`和`bgJson.height`现在反映背景图片的真实尺寸
2. **优先级合理**：用户调整 > 图片真实尺寸 > 默认值
3. **异常处理**：图片加载失败时有合理的降级策略
4. **向后兼容**：保持现有功能不受影响
5. **详细日志**：增加完整的调试信息，便于问题排查

## 技术细节

### 跨域处理
设置`img.crossOrigin = 'anonymous'`避免CORS问题

### 性能考虑
- 图片加载是异步的，不会阻塞主线程
- 加载失败有快速降级机制
- 只在真正需要时才进行图片加载

### 错误处理
- 网络异常：优雅降级到默认尺寸
- 图片格式错误：同样降级处理
- URL无效：直接返回默认尺寸

## 验证方式

1. **选择背景图片**：左侧面板选择不同尺寸的背景图
2. **保存作品**：点击生成视频保存作品
3. **检查数据**：查看保存的`bgJson.width`和`bgJson.height`是否为图片真实尺寸
4. **回显验证**：重新打开作品，验证背景显示是否正确

通过此修复，确保了`bgJson`字段中的尺寸数据与实际背景图片保持一致，解决了数据不匹配导致的回显问题。

## 后续优化：使用预览区域尺寸替代硬编码默认值

### 问题发现
用户反馈：获取不了背景图片尺寸时，不要使用硬编码的1920×1080，而应该使用预览区域的实际尺寸作为默认值。

### 根本原因
硬编码的1920×1080与预览区域的实际尺寸不匹配：
- **16:9横屏模式**：预览区域实际为 901.333 × 507px
- **9:16竖屏模式**：预览区域实际为 403 × 700px  
- **其他比例**：预览区域实际为 492 × 749px

### 优化方案

修改`getImageRealSize`函数，根据长宽比返回对应的预览区域尺寸：

```javascript
/**
 * 动态获取背景图片真实尺寸的辅助函数
 * @param {string} imageUrl - 图片URL
 * @param {string} aspectRatio - 画布长宽比，用于确定预览区域尺寸
 * @returns {Promise<{width: number, height: number}>} 图片的真实尺寸
 */
const getImageRealSize = (imageUrl, aspectRatio = '9:16') => {
    return new Promise((resolve) => {
        /**
         * 🔧 获取预览区域的真实尺寸作为默认值
         * 替换硬编码的1920×1080，使用与PreviewEditor组件一致的尺寸
         */
        const getPreviewAreaSize = (ratio) => {
            if (ratio === '16:9') {
                return { width: 901.333, height: 507 };
            } else if (ratio === '9:16') {
                return { width: 403, height: 700 };
            } else {
                return { width: 492, height: 749 };
            }
        };
        
        const defaultSize = getPreviewAreaSize(aspectRatio);
        
        if (!imageUrl || imageUrl.trim() === '') {
            resolve(defaultSize);
            return;
        }
        
        const img = new Image();
        img.onload = function() {
            resolve({ 
                width: this.naturalWidth, 
                height: this.naturalHeight 
            });
        };
        img.onerror = function() {
            // 图片加载失败时，使用预览区域尺寸
            resolve(defaultSize);
        };
        img.crossOrigin = 'anonymous';
        img.src = imageUrl;
    });
};
```

### 修复效果

| 长宽比 | 修复前默认值 | 修复后默认值 | 说明 |
|--------|--------------|--------------|------|
| 16:9 | ❌ 1920×1080 | ✅ 901.333×507 | 与横屏预览区域一致 |
| 9:16 | ❌ 1920×1080 | ✅ 403×700 | 与竖屏预览区域一致 |
| 其他 | ❌ 1920×1080 | ✅ 492×749 | 与默认预览区域一致 |

### 数据一致性

现在保存的`bgJson`尺寸完全与预览区域保持一致：
- **图片存在**：使用图片真实尺寸
- **图片不存在或加载失败**：使用对应长宽比的预览区域尺寸
- **用户调整过**：优先使用用户调整的尺寸

这确保了保存的数据与用户在预览区域看到的效果完全匹配。

## 最终简化：去掉动态获取，使用双优先级策略

### 需求变更
用户要求进一步简化逻辑：**"可以把动态获取的尺寸去掉了，第一个是使用拉伸的，如果没拉伸的话就展示预览区的默认宽度和高度"**

### 简化原因
- 避免异步处理的复杂性
- 提升性能，减少网络请求  
- 用户拉伸尺寸最重要，预览区域尺寸足够作为默认值
- 代码更简洁易维护

### 最终简化方案

#### 1. 删除动态获取函数
```javascript
// 🚫 删除：getImageRealSize异步函数

// ✅ 保留：简单的尺寸获取辅助函数
const getPreviewAreaSize = (aspectRatio) => {
    if (aspectRatio === '16:9') {
        return { width: 901.333, height: 507 };
    } else if (aspectRatio === '9:16') {
        return { width: 403, height: 700 };
    } else {
        return { width: 492, height: 749 };
    }
};
```

#### 2. 简化为双优先级策略
```javascript
// 优先级1：使用用户拖拽调整后的尺寸
if (positionsData.backgroundModule?.width && positionsData.backgroundModule?.height) {
    backgroundWidth = positionsData.backgroundModule.width;
    backgroundHeight = positionsData.backgroundModule.height;
    console.log('🎯 使用用户调整的背景尺寸（优先级1）');
} 
// 优先级2：使用预览区域尺寸
else {
    const aspectRatio = editorData?.aspectRatio || '9:16';
    const previewSize = getPreviewAreaSize(aspectRatio);
    backgroundWidth = previewSize.width;
    backgroundHeight = previewSize.height;
    console.log('📐 使用预览区域尺寸（优先级2）');
}
```

#### 3. 移除异步处理
```javascript
// 🚫 修改前：async/await
const buildSaveParams = async (editorData) => {
const saveParams = await buildSaveParams(editorData);

// ✅ 修改后：同步处理
const buildSaveParams = (editorData) => {
const saveParams = buildSaveParams(editorData);
```

### 最终修复效果

| 场景 | 简化前 | 简化后 |
|------|--------|--------|
| 用户拉伸背景 | ✅ 使用拉伸尺寸 | ✅ **使用拉伸尺寸（优先级1）** |
| 未拉伸背景 | ✅ 动态获取图片尺寸 | ✅ **使用预览区域尺寸（优先级2）** |
| 图片加载失败 | ✅ 降级到预览区域尺寸 | ✅ **直接使用预览区域尺寸** |
| 性能表现 | ❌ 异步处理，可能较慢 | ✅ **同步处理，性能更好** |
| 代码复杂度 | ❌ 较复杂，有异步逻辑 | ✅ **简洁明了** |

### 核心优势

1. **简洁高效**：去掉异步处理，代码更简洁，性能更好
2. **数据准确**：用户拉伸的尺寸得到最高优先级保证
3. **预览一致**：预览区域尺寸作为合理的默认值
4. **维护友好**：代码逻辑清晰，易于维护和调试

通过此次简化，实现了用户要求的"第一优先级使用拉伸尺寸，第二优先级使用预览区域默认尺寸"的目标。 