# commonJson.subtitle_json字幕数据权威性修复

## 问题描述

在数字人编辑器中，即使保存时 `commonJson.subtitle_json` 是空数组 `[]`，回显时仍然会从 `audioJson.wav_text` 获取字幕数据并显示，导致保存的字幕状态与回显状态不一致。

用户要求："**一切以 subtitle_json 这个为准**"，如果保存时是空的，回显时也应该是空的，不应该从其他地方获取字幕。

## 原有逻辑问题

```javascript
// 🚫 问题逻辑：只有当subtitle_json存在且长度>0时才标记为已处理
if (workData.commonJson && workData.commonJson.subtitle_json && 
    Array.isArray(workData.commonJson.subtitle_json) && 
    workData.commonJson.subtitle_json.length > 0) {
    // 处理字幕数据
    subtitleDataProcessed = true;
}

// 如果subtitle_json是空数组，subtitleDataProcessed仍为false
// 导致会继续从audioJson.wav_text获取字幕
if (!subtitleDataProcessed && workData.audioJson && workData.audioJson.wav_text) {
    // 🚫 不应该执行的备选方案
}
```

## 修复方案

### 核心修复原则
**完全以 `commonJson.subtitle_json` 字段的存在性为准，而不是其内容多少**

### 修复后逻辑

```javascript
// ✅ 修复逻辑：检查subtitle_json字段是否存在（不管内容）
if (workData.commonJson && workData.commonJson.hasOwnProperty('subtitle_json') && 
    Array.isArray(workData.commonJson.subtitle_json)) {
    
    console.log('🎬 发现commonJson.subtitle_json字段，以此为准:', {
        原始数组: workData.commonJson.subtitle_json,
        数组长度: workData.commonJson.subtitle_json.length,
        说明: '完全以subtitle_json为准，不从其他源获取字幕'
    });

    // 🔧 关键修复：即使subtitle_json是空数组，也标记为已处理
    subtitleDataProcessed = true;

    if (workData.commonJson.subtitle_json.length > 0) {
        // 处理有内容的字幕数据
        const formattedSubtitleData = workData.commonJson.subtitle_json
            .filter(item => item && item.text && item.text.trim() !== '')
            .map((item, index) => {
                // 字幕数据格式转换...
            });
        
        if (formattedSubtitleData.length > 0) {
            digitalHumanStore.setSubtitleData(formattedSubtitleData);
        } else {
            digitalHumanStore.setSubtitleData([]);
        }
    } else {
        // ✅ 关键：subtitle_json为空数组，清空字幕数据
        digitalHumanStore.setSubtitleData([]);
        console.log('🎬 commonJson.subtitle_json为空数组，已清空字幕数据，不从其他源获取');
    }
}
// 备选方案：只有当完全没有subtitle_json字段时才执行
else if (!subtitleDataProcessed && workData.audioJson && workData.audioJson.wav_text) {
    // 使用audioJson.wav_text作为备选方案
}
```

## 修复文件
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` - `loadWorkData` 方法

## 修复效果对比

| 场景 | 保存时数据 | 修复前回显 | 修复后回显 | 符合预期 |
|------|------------|------------|------------|----------|
| 有字幕 | `subtitle_json: [{text:"hello"}]` | 显示字幕 | 显示字幕 | ✅ |
| 空字幕 | `subtitle_json: []` | ❌ 显示audioJson字幕 | ✅ 无字幕 | ✅ |
| 无字段 | 没有`subtitle_json`字段 | 显示audioJson字幕 | 显示audioJson字幕 | ✅ |

## 新的字幕数据处理优先级

```
1. 检查 commonJson.subtitle_json 字段是否存在
   ├─ 存在且是数组 → 以此为准（权威数据源）
   │  ├─ 长度 > 0：处理并显示字幕数据
   │  └─ 长度 = 0：清空字幕数据，不显示任何字幕
   └─ 不存在 → 使用备选方案
      └─ 从 audioJson.wav_text 获取字幕（向后兼容）
```

## 关键修复点

1. **字段存在性检查**: 使用 `hasOwnProperty('subtitle_json')` 而不是检查长度
2. **空数组处理**: `subtitle_json: []` 时立即标记为已处理并清空字幕
3. **权威性保证**: 一旦发现 `subtitle_json` 字段，就不再从其他源获取字幕
4. **向后兼容**: 只有完全没有 `subtitle_json` 字段时才使用备选方案

## 测试验证

### 测试用例1：空字幕数组
```javascript
workData = {
    commonJson: {
        subtitle_json: []  // 空数组
    },
    audioJson: {
        wav_text: "这是音频文本"
    }
}
// 预期结果：无字幕显示，不使用wav_text
```

### 测试用例2：有效字幕数组
```javascript
workData = {
    commonJson: {
        subtitle_json: [{text: "字幕内容", start: 0, end: 2}]
    }
}
// 预期结果：显示字幕内容
```

### 测试用例3：无subtitle_json字段
```javascript
workData = {
    // 没有commonJson.subtitle_json字段
    audioJson: {
        wav_text: "这是音频文本"
    }
}
// 预期结果：显示"这是音频文本"作为字幕
```

## 技术要点

- **字段检测**: `hasOwnProperty()` 比 `&&` 链式检查更准确
- **数据清理**: 明确调用 `digitalHumanStore.setSubtitleData([])` 清空数据
- **处理标记**: 提前设置 `subtitleDataProcessed = true` 防止备选方案执行
- **日志记录**: 详细记录处理决策，便于调试

## 最终效果

✅ **完全以 `subtitle_json` 为准**：如果该字段存在，就不会从其他地方获取字幕数据  
✅ **空数组正确处理**：`subtitle_json: []` 时回显无字幕  
✅ **向后兼容保证**：没有该字段时仍使用原有备选方案  
✅ **数据一致性**：保存时的字幕状态与回显时完全一致 