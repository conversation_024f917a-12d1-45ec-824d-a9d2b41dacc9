# 数字人编辑器作品数据回显功能完整实现

## 概述
实现在数字人编辑器中保存和恢复作品数据的完整功能，包括：
1. **保存功能**：生成视频时将选中的数字人URL保存到 `commonJson.secondDigitalHumanUrl`
2. **回显功能**：编辑作品时恢复第二层数字人和第一层背景的选择状态
   - 第二层数字人：从 `commonJson.secondDigitalHumanUrl` 恢复
   - 第一层背景：从 `bgJson.src_url`（优先）或 `bgColor` 恢复
3. **字幕数组传递功能**：将`createVideo`接口返回的`subtitle_json`字幕数组正确传递到生成视频接口 ✅
4. **字幕数组回显功能**：从`getDigitalWork`接口返回的`commonJson.subtitle_json`恢复字幕时间轴展示 ⭐ **新增**
5. **作品标题回显功能**：从`getDigitalWork`接口返回的`workData.title`恢复顶部作品标题显示 ⭐ **新增**
6. **字幕样式配置回显功能**：从`getDigitalWork`接口返回的`workData.subtitleConfigJson`恢复字幕样式、位置和尺寸 ⭐ **新增**

## 需求背景
用户在数字人编辑器中进行编辑操作后，需要在生成视频时将所有选择保存，同时在重新编辑作品时能够正确回显之前的选择：
- 第二层数字人图片需要保存到 `commonJson.secondDigitalHumanUrl`
- 第一层背景图片需要保存到 `bgJson.src_url`，背景颜色保存到 `bgColor`
- 字幕JSON数组需要从`createVideo`接口返回的`subtitle_json`传递到生成视频接口

## 数据流程分析
```
用户选择操作 → 编辑器状态 → 保存到服务器
              ↓             ↓
          数字人选择      背景选择
              ↓             ↓
    currentDigitalHumanConfig  currentBackgroundConfig
              ↓             ↓
    commonJson.secondDigitalHumanUrl  bgJson.src_url/bgColor
              ↓             ↓
编辑作品时加载 ← getDigitalWork接口返回的作品数据

// 🎬 字幕数组完整数据流
点击"保存并生成音频" → createVideo接口 → subtitle_json字幕数组
                      ↓
              右侧操作面板 → digital_human_right_option
                      ↓
              生成视频参数 → commonJson.subtitle_json（保存）
                      ↓
              getDigitalWork接口 → commonJson.subtitle_json（回显）
                      ↓
              字幕组件显示 → 时间轴字幕恢复
```

## 实现详情

### 1. 保存功能实现 ✅
**文件**: `src/views/layout/components/headbar/components/action/index.vue`
**函数**: `buildSaveParams`
**实现时间**: 2025-01-09

```javascript
// 🎭 添加第二层数字人图片URL到commonJson
const secondDigitalHumanUrl = editorData?.digitalHumanConfig?.url || '';
if (commonJsonData && typeof commonJsonData === 'object') {
    commonJsonData.secondDigitalHumanUrl = secondDigitalHumanUrl;
}
```

### 2. 回显功能实现 ✅
**文件**: `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
**函数**: `loadWorkData`
**实现时间**: 2025-01-09

#### 第二层数字人图片URL回显

```javascript
// 🎭 6. 第二层数字人图片URL回显处理
if (workData.commonJson && workData.commonJson.secondDigitalHumanUrl && workData.commonJson.secondDigitalHumanUrl.trim() !== '') {
    try {
        const digitalHumanUrl = workData.commonJson.secondDigitalHumanUrl.trim();
        
        // 设置数字人配置
        const digitalHumanConfig = {
            type: 'picture',
            url: digitalHumanUrl,
            index: null  // 索引可能需要根据实际情况设置
        };

        currentDigitalHumanConfig.value = digitalHumanConfig;

        console.log('🎭 第二层数字人图片URL回显成功:', {
            来源: 'commonJson.secondDigitalHumanUrl',
            URL: digitalHumanUrl,
            配置: digitalHumanConfig
        });

        // 强制触发响应式更新
        await nextTick();

        // 通知左侧面板同步选择状态
        if (digitalHumanLeftPanelRef.value && digitalHumanLeftPanelRef.value.syncDigitalHumanSelection) {
            digitalHumanLeftPanelRef.value.syncDigitalHumanSelection(digitalHumanConfig);
            console.log('✅ 已通知左侧面板同步数字人选择状态');
        }
    } catch (error) {
        console.error('❌ 第二层数字人图片URL回显失败:', error);
        // 继续执行，不影响其他数据加载
    }
} else {
    console.log('💡 commonJson中没有第二层数字人图片URL，跳过回显');
}
```

#### 第一层背景图片回显

```javascript
// 1. 背景处理（优先背景图片，其次背景颜色）

// 🖼️ 首先检查背景图片
if (workData.bgJson && workData.bgJson.src_url && workData.bgJson.src_url.trim() !== '') {
    const backgroundConfig = {
        type: 'image',
        value: workData.bgJson.src_url.trim()
    };

    currentBackgroundConfig.value = backgroundConfig;

    console.log('🖼️ 第一层背景图片回显成功:', {
        来源: 'bgJson.src_url',
        URL: workData.bgJson.src_url.trim(),
        配置: backgroundConfig
    });

    // 强制触发响应式更新
    await nextTick();
}
// 🎨 如果没有背景图片，检查背景颜色
else if (workData.bgColor && typeof workData.bgColor === 'string' && workData.bgColor.trim() !== '') {
    const backgroundConfig = {
        type: 'color',
        value: workData.bgColor.trim()
    };

    currentBackgroundConfig.value = backgroundConfig;

    console.log('🎨 背景颜色回显成功:', {
        来源: 'bgColor',
        颜色: workData.bgColor.trim(),
        配置: backgroundConfig
    });

    // 强制触发响应式更新
    await nextTick();
} else {
    // 如果接口没有返回有效的背景配置，设置为默认白色
    currentBackgroundConfig.value = {
        type: 'color',
        value: '#FFFFFF'
    };
    console.log('💡 没有找到背景配置，使用默认白色背景');
}
```

### 3. 字幕JSON数组传递功能实现 ✅
**实现时间**: 2025-01-09

#### 修改文件1: `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue`
**函数**: `emit_data`

```javascript
digital_human_right_option({
    type:'text_captions',
    aduio_data:request_video.value,
    open_captions:input_text_obj.value?.captions?.open_captions || false,
    choose_music:input_text_obj.value?.choose_music?.current_music,
    // 🎬 新增：传递字幕JSON数组到右侧操作面板
    subtitle_json: request_video.value?.subtitle_json || [],//接口返回的字幕JSON数组，用于生成视频时的字幕处理
    audioJson:{
        // ... 其他字段
        // 🎬 新增：将字幕JSON数组添加到audioJson中，确保在构建保存参数时能访问到
        subtitle_json: request_video.value?.subtitle_json || []//字幕JSON数组，包含详细的时间轴和文本信息
    }
})
```

#### 修改文件2: `src/views/layout/components/headbar/components/action/index.vue`
**函数**: `buildSaveParams`

```javascript
// 🎬 获取字幕JSON数组 - 从右侧操作面板传递的数据中提取
const subtitleJsonArray = digitalHumanRightOption.subtitle_json || 
                         digitalHumanRightOption.audioJson?.subtitle_json || 
                         digitalHumanRightOption.aduio_data?.subtitle_json || 
                         [];

// ⚠️ 重要修改：根据用户要求，字幕数组只添加到commonJson中，不添加到audioJson中
// audioJson 不再包含 subtitle_json 字段

// 在commonJson中添加字幕数组
commonJsonData.subtitle_json = subtitleJsonArray.length > 0 ? subtitleJsonArray : [];
```

### 4. 字幕JSON数组回显功能实现 ✅ 新增
**文件**: `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
**函数**: `loadWorkData`
**实现时间**: 2025-01-09

### 5. 作品标题回显功能实现 ✅ 新增
**文件**: 
- `src/views/modules/mainPage/components/headbar/index.vue` (添加setProjectTitle方法)
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` (调用方法设置标题)
**实现时间**: 2025-01-09

### 6. 字幕样式配置回显功能实现 ⭐ **新增**
**文件**: 
- `src/views/modules/digitalHuman/components/PreviewEditor.vue` (添加setSubtitleConfig方法)
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` (处理subtitleConfigJson数据)
**实现时间**: 2025-01-09

```javascript
// 5. 字幕样式配置处理
if (workData.subtitleConfigJson && typeof workData.subtitleConfigJson === 'object') {
    try {
        console.log('🎨 开始处理字幕样式配置:', workData.subtitleConfigJson);
        
        // 🎯 样式配置映射
        const subtitleStyleConfig = {
            fontFamily: currentSubtitleConfig.value.fontFamily, // 保持原有字体ID
            fontName: currentSubtitleConfig.value.fontName,     // 保持原有字体名称
            fontSize: workData.subtitleConfigJson.font_size || currentSubtitleConfig.value.fontSize,
            textColor: workData.subtitleConfigJson.color || currentSubtitleConfig.value.textColor,
            borderColor: workData.subtitleConfigJson.stroke_color || currentSubtitleConfig.value.borderColor,
            borderWidth: workData.subtitleConfigJson.stroke_width || currentSubtitleConfig.value.borderWidth,
            fontUrl: currentSubtitleConfig.value.fontUrl // 保持原有字体URL
        };

        // 🔄 更新字幕样式配置
        currentSubtitleConfig.value = subtitleStyleConfig;

        // 🎯 位置和尺寸配置处理
        await nextTick();
        if (previewEditorRef.value && previewEditorRef.value.setSubtitleConfig) {
            previewEditorRef.value.setSubtitleConfig(workData.subtitleConfigJson);
            console.log('✅ 已通知PreviewEditor设置字幕位置和尺寸');
        }

        console.log('🎨 字幕样式配置更新完成:', subtitleStyleConfig);
    } catch (error) {
        console.error('❌ 字幕样式配置处理失败:', error);
    }
}
```

**PreviewEditor组件新增方法**:
```javascript
// 🎨 字幕配置设置方法（用于数据回显）
setSubtitleConfig: (subtitleConfigJson) => {
    try {
        if (!subtitleConfigJson) return;

        console.log('🎨 开始设置字幕配置:', subtitleConfigJson);

        // 🎯 处理位置配置
        if (subtitleConfigJson.x !== undefined && subtitleConfigJson.y !== undefined) {
            const initialPosition = getInitialSubtitlePosition();
            const targetX = parseFloat(subtitleConfigJson.x) || 0;
            const targetY = parseFloat(subtitleConfigJson.y) || 0;
            
            userSubtitleOffsetX.value = targetX - initialPosition.x;
            userSubtitleOffsetY.value = targetY - initialPosition.y;
        }

        // 🎯 处理尺寸配置
        if (subtitleConfigJson.width !== undefined && subtitleConfigJson.height !== undefined) {
            const defaultWidth = 380;
            const defaultHeight = 80;
            
            const targetWidth = parseFloat(subtitleConfigJson.width) || defaultWidth;
            const targetHeight = parseFloat(subtitleConfigJson.height) || defaultHeight;
            
            userSubtitleScaleX.value = targetWidth / defaultWidth;
            userSubtitleScaleY.value = targetHeight / defaultHeight;
        }

        emitPositionUpdate();
        console.log('✅ 字幕配置设置完成');
    } catch (error) {
        console.error('❌ 设置字幕配置失败:', error);
    }
},
```

```javascript
// 3. 作品标题处理
if (workData.title && workData.title.trim() !== '') {
    // 通过nextTick确保组件已完全挂载后再设置标题
    await nextTick();
    try {
        // 🏷️ 使用headbar组件暴露的setProjectTitle方法设置标题
        if (headbarRef.value && headbarRef.value.setProjectTitle) {
            headbarRef.value.setProjectTitle(workData.title);
            console.log('🎬 作品标题回显成功:', {
                来源: 'workData.title',
                标题: workData.title
            });
        } else {
            console.warn('❌ Headbar组件的setProjectTitle方法不可用');
        }
    } catch (error) {
        console.error('❌ 设置作品标题失败:', error);
    }
}
```

**Headbar组件新增方法**:
```javascript
// 🔧 暴露设置标题的方法，供父组件调用
const setProjectTitle = (title) => {
    projectTitle.value = title || '';
    console.log('📝 Headbar组件设置标题:', title);
};

// 暴露方法给父组件
defineExpose({
    getProjectTitle,
    setProjectTitle  // 新增暴露的方法
});
```

```javascript
// 4. 字幕数据处理（优先级：详细字幕数组 > 简单文本 > 无字幕）
let subtitleDataProcessed = false;

// 🎬 优先处理commonJson中的详细字幕数组（subtitle_json）
if (workData.commonJson && workData.commonJson.subtitle_json && Array.isArray(workData.commonJson.subtitle_json) && workData.commonJson.subtitle_json.length > 0) {
    try {
        // 转换字幕数据格式，确保与字幕组件兼容
        const formattedSubtitleData = workData.commonJson.subtitle_json
            .filter(item => item && item.text && item.text.trim() !== '') // 过滤空数据
            .map((item, index) => {
                const text = (item.text || '').trim();
                const startTime = item.start || item.startTime || 0;
                const endTime = item.end || item.endTime || (startTime + 1);
                
                return { 
                    text, 
                    startTime: parseFloat(startTime), 
                    endTime: parseFloat(endTime) 
                };
            });

        if (formattedSubtitleData.length > 0) {
            digitalHumanStore.setSubtitleData(formattedSubtitleData);
            subtitleDataProcessed = true;

            console.log('🎬 commonJson字幕数组回显成功:', {
                来源: 'commonJson.subtitle_json',
                字幕条数: formattedSubtitleData.length,
                首条字幕: formattedSubtitleData[0],
                末条字幕: formattedSubtitleData[formattedSubtitleData.length - 1]
            });
        }
    } catch (error) {
        console.error('❌ commonJson字幕数组处理失败:', error);
        // 处理失败时继续使用简单文本模式
    }
}

// 🎵 备选方案：处理audioJson中的简单文本字幕
if (!subtitleDataProcessed && workData.audioJson && workData.audioJson !== null) {
    // 检查是否有字幕文本数据
    if (workData.audioJson.wav_text && workData.audioJson.wav_text.trim() !== '') {
        // 创建简单的字幕数据结构
        const subtitleData = [{
            text: workData.audioJson.wav_text,
            startTime: 0,
            endTime: workData.audioJson.duration || 10 // 使用音频时长或默认10秒
        }];

        digitalHumanStore.setSubtitleData(subtitleData);
        subtitleDataProcessed = true;

        console.log('🎵 audioJson简单字幕回显成功:', {
            来源: 'audioJson.wav_text',
            字幕文本: workData.audioJson.wav_text.substring(0, 50) + '...',
            时长: workData.audioJson.duration || 10
        });
    }
}
```

### 回显功能特点

#### 第二层数字人回显特点
- ✅ 从commonJson恢复数字人选择
- ✅ 设置currentDigitalHumanConfig状态
- ✅ 通知左侧面板同步选择状态
- ✅ 完整的错误处理机制
- ✅ 不影响其他数据加载流程

#### 第一层背景回显特点
- ✅ 优先处理背景图片（bgJson.src_url）
- ✅ 降级处理背景颜色（bgColor）
- ✅ 智能类型判断（image/color）
- ✅ 默认白色背景兜底
- ✅ 详细的调试日志输出

#### 字幕数组回显特点 ⭐ **新增**
- ✅ 优先使用详细字幕数组（commonJson.subtitle_json）
- ✅ 备选使用简单文本字幕（audioJson.wav_text）
- ✅ 智能数据格式转换和字段兼容
- ✅ 完整的时间轴字幕恢复
- ✅ 错误处理和向后兼容性
- ✅ 详细的恢复状态日志
- ✅ **智能时间字段映射**：支持多种时间字段格式
  - 优先级1：`start`/`end`（秒）
  - 优先级2：`startTime`/`endTime`（秒）
  - 优先级3：`time_begin`/`time_end`（毫秒，自动转换为秒）
  - 默认处理：按索引生成时间轴

#### 作品标题回显特点 ⭐ **新增**
- ✅ 从getDigitalWork接口返回的`workData.title`恢复作品标题
- ✅ 通过Headbar组件的`setProjectTitle`方法设置标题
- ✅ 组件封装性：使用方法调用而非直接属性访问
- ✅ 完整错误处理和状态日志
- ✅ nextTick确保组件完全挂载后再设置
- ✅ 支持空标题处理和默认值设置

#### 字幕样式配置回显特点 ⭐ **新增**
- ✅ 从getDigitalWork接口返回的`workData.subtitleConfigJson`恢复字幕样式
- ✅ **样式字段映射**：
  - `color` → `textColor`（字体颜色）
  - `font_size` → `fontSize`（字体大小）
  - `stroke_color` → `borderColor`（字体描边色）
  - `stroke_width` → `borderWidth`（字体描边大小）
- ✅ **位置和尺寸映射**：
  - `x`/`y` → 字幕X/Y坐标位置
  - `width`/`height` → 字幕边框大小
- ✅ 智能偏移量计算：基于初始位置计算用户拖拽偏移
- ✅ 智能缩放比例计算：基于默认尺寸计算用户拉伸比例
- ✅ 保持原有字体配置：fontFamily、fontName、fontUrl等
- ✅ 通过PreviewEditor组件的`setSubtitleConfig`方法设置位置和尺寸
- ✅ 完整错误处理和向后兼容性

#### 字幕JSON数组传递特点
- ✅ 从createVideo接口返回数据中提取subtitle_json
- ✅ 在emit_data中双重传递（顶级字段 + audioJson内）
- ✅ 在buildSaveParams中优先级处理多个数据源
- ✅ 支持输入文本模式和音频驱动模式
- ⚠️ **重要变更**：字幕数组只添加到commonJson中，不添加到audioJson中
- ✅ 详细的调试日志输出

### 完整流程测试

1. **保存测试**:
   - 在数字人编辑器中选择第二层数字人图片
   - 在左侧面板选择背景图片或背景颜色
   - 输入文本并点击"保存并生成音频"按钮
   - 点击"生成视频"按钮
   - 检查控制台日志确认URL和字幕数组已保存到commonJson（不保存到audioJson）

2. **回显测试**:
   - 从"我的作品"重新编辑已保存的作品
   - 检查第二层数字人是否正确显示
   - 检查第一层背景是否正确显示（图片/颜色）
   - 检查左侧面板选择状态是否正确
   - 检查控制台回显日志

3. **优先级测试**:
   - 测试同时有背景图片和背景颜色的作品
   - 确认优先显示背景图片
   - 测试只有背景颜色的作品
   - 确认正确显示背景颜色

4. **字幕数组测试**:
   - 点击"保存并生成音频"生成字幕数组
   - 检查emit_data中的传递日志
   - 点击"生成视频"检查buildSaveParams中的处理日志
   - 确认commonJson.subtitle_json包含正确的字幕数据（不再保存到audioJson）

### 回显时的日志输出

#### 作品标题回显日志 ⭐ **新增**
```
🎬 作品标题回显成功: {
    来源: "workData.title",
    标题: "我的数字人作品"
}
📝 Headbar组件设置标题: 我的数字人作品
```

#### 字幕样式配置回显日志 ⭐ **新增**
```
🎨 开始处理字幕样式配置: {
    color: "#ffffff",
    font_size: 24,
    stroke_color: "#000000",
    stroke_width: 2,
    x: 100,
    y: 600,
    width: 400,
    height: 60
}
🎨 字幕样式配置更新完成: {
    fontFamily: "1",
    fontSize: 24,
    textColor: "#ffffff",
    borderColor: "#000000",
    borderWidth: 2,
    ...
}
🎨 开始设置字幕配置: { x: 100, y: 600, width: 400, height: 60, ... }
📍 字幕位置设置完成: {
    初始位置: { x: 11.5, y: 610 },
    目标位置: { x: 100, y: 600 },
    偏移量: { x: 88.5, y: -10 }
}
📏 字幕尺寸设置完成: {
    默认尺寸: { width: 380, height: 80 },
    目标尺寸: { width: 400, height: 60 },
    缩放比例: { x: 1.05, y: 0.75 }
}
✅ 字幕配置设置完成
✅ 已通知PreviewEditor设置字幕位置和尺寸
```

#### 第二层数字人回显日志
```
🎭 第二层数字人图片URL回显成功: {
    来源: "commonJson.secondDigitalHumanUrl",
    URL: "https://example.com/digital-human.jpg",
    配置: {type: "picture", url: "...", index: null}
}
✅ 已通知左侧面板同步数字人选择状态
```

#### 第一层背景回显日志

**背景图片模式**：
```
🖼️ 第一层背景图片回显成功: {
    来源: "bgJson.src_url",
    URL: "https://example.com/background.jpg",
    配置: {type: "image", value: "https://example.com/background.jpg"}
}
```

**背景颜色模式**：
```
🎨 背景颜色回显成功: {
    来源: "bgColor",
    颜色: "#FF5733",
    配置: {type: "color", value: "#FF5733"}
}
```

**默认背景模式**：
```
💡 没有找到背景配置，使用默认白色背景
```

#### 字幕数组传递日志

**emit_data阶段**：
```
📝 字幕数据传递情况: {
    接口返回的字幕数组: [{text: "...", start: 0, end: 1000}, ...],
    字幕数组长度: 5,
    字幕数组样例: {text: "同一束光", start: 6600, end: 9900},
    传递状态: "✅ 已传递"
}
```

**buildSaveParams阶段**：
```
✅ 已添加第二层数字人图片URL和字幕数组到commonJson: {
    secondDigitalHumanUrl: "https://example.com/digital-human.jpg",
    subtitle_json: [...],
    字幕数组长度: 5,
    来源: "编辑器数字人配置"
}
```

## 安全特性

### 数据验证
- 所有URL字段都进行了trim()处理
- 对配置对象进行了存在性检查
- 使用了safe navigation operator避免空值错误

### 错误处理
- 使用try-catch包装关键操作
- 错误不会影响其他数据的加载
- 提供了详细的错误日志

### 向后兼容
- 如果没有相关数据，使用合理的默认值
- 不会破坏现有的数据结构
- 保持与旧版本作品的兼容性

## 性能考虑

### 响应式更新
- 使用nextTick确保DOM更新完成
- 避免了不必要的重复渲染

### 内存管理
- 及时清理事件监听器
- 避免了内存泄漏风险

## 技术债务

### 已知限制
1. 第二层数字人的index字段暂时设为null，可能需要根据实际情况调整
2. 左侧面板同步逻辑依赖于面板组件暴露的方法
3. 字幕数组的具体数据结构依赖于createVideo接口的返回格式

### 后续优化建议
1. 考虑添加数据校验机制
2. 优化错误提示的用户友好性
3. 添加更多的边界情况处理
4. 考虑字幕数组的数据格式标准化 