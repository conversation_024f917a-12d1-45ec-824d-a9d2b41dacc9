# getDigitalWork接口数据存储和获取指南

## 📋 概述

本文档详细说明 `getDigitalWork` 接口返回的各个数据字段在系统中的存储位置和获取方式，供开发团队参考使用。

## 🗄️ 数据存储架构

`getDigitalWork` 接口返回的数据分散存储在两个主要位置：

1. **Pinia Store** (`digitalHumanStore`) - 存储音频和字幕时间轴数据
2. **组件响应式变量** - 存储界面配置数据（背景、数字人、字幕样式）

---

## 📊 具体数据字段存储分析

### 1. audioJson 数据存储

#### 原始数据结构
```javascript
workData.audioJson = {
    duration: 30.5,           // 音频时长
    wav_text: "这是字幕文本",   // 字幕文本
    wav_url: "audio.wav"      // 音频文件URL（如果有）
}
```

#### 存储位置
- **位置**: `digitalHumanStore` (Pinia)
- **存储方式**:
```javascript
const digitalHumanStore = useDigitalHumanStore();

// 音频时长存储
digitalHumanStore.totalDuration = Math.ceil(workData.audioJson.duration);

// 字幕数据存储  
digitalHumanStore.setSubtitleData([{
    text: workData.audioJson.wav_text,
    startTime: 0,
    endTime: workData.audioJson.duration
}]);

// 音频URL存储（如果有）
digitalHumanStore.setTtsAudioUrl(workData.videoUrl);
```

#### 获取方式
```javascript
import { useDigitalHumanStore } from '@/views/modules/digitalHuman/store/digitalHumanStore';

const digitalHumanStore = useDigitalHumanStore();

const audioJsonData = {
    duration: digitalHumanStore.totalDuration,
    wav_text: digitalHumanStore.subtitleData[0]?.text || '',
    wav_url: digitalHumanStore.ttsAudioUrl
};
```

---

### 2. bgJson 数据存储

#### 原始数据结构
```javascript
workData.bgJson = {
    src_url: "background.jpg",  // 背景图片URL
    x: 100,                     // X坐标
    y: 50,                      // Y坐标  
    width: 800,                 // 宽度
    height: 600                 // 高度
}
```

#### 存储位置
- **背景图片URL**: 组件响应式变量 `currentBackgroundConfig`
- **位置信息**: PreviewEditor内部状态

#### 存储方式
```javascript
// 1. 背景图片URL存储
const currentBackgroundConfig = ref({
    type: 'image',
    value: workData.bgJson.src_url
});

// 2. 位置信息存储
previewEditorRef.value.setBackgroundPosition(workData.bgJson);
```

#### 获取方式
```javascript
// 方式一：通过getCurrentEditorData()
const editorData = getCurrentEditorData();
const bgJsonData = {
    src_url: editorData.backgroundConfig.value,
    x: editorData.positionsData.backgroundModule?.x,
    y: editorData.positionsData.backgroundModule?.y,
    width: editorData.positionsData.backgroundModule?.width,
    height: editorData.positionsData.backgroundModule?.height
};

// 方式二：通过事件总线
import eventBus from '@/common/utils/eventBus';

eventBus.emit('request-digital-human-data', (data) => {
    const bgJson = {
        src_url: data.backgroundConfig.value,
        x: data.positionsData.backgroundModule?.x,
        y: data.positionsData.backgroundModule?.y,
        width: data.positionsData.backgroundModule?.width,
        height: data.positionsData.backgroundModule?.height
    };
});
```

---

### 3. commonJson 数据存储

#### 原始数据结构
```javascript
workData.commonJson = {
    subtitle_json: [...],              // 字幕数组
    fontStyle: {                       // 字体样式
        fontFamily: "1",
        fontName: "微软雅黑",
        fontUrl: "font.ttf",
        fontSize: 18,
        textColor: "#ffffff",
        borderColor: "#000000",
        borderWidth: 7
    },
    secondDigitalHumanUrl: "human.png" // 第二层数字人图片URL
}
```

#### 存储位置
数据分散存储在多个位置：

1. **subtitle_json** → `digitalHumanStore`
2. **fontStyle** → 组件响应式变量 `currentSubtitleConfig`
3. **secondDigitalHumanUrl** → 组件响应式变量 `currentDigitalHumanConfig`

#### 存储方式
```javascript
// 1. subtitle_json 存储
digitalHumanStore.setSubtitleData(workData.commonJson.subtitle_json);

// 2. fontStyle 存储
const currentSubtitleConfig = ref({
    fontFamily: workData.commonJson.fontStyle.fontFamily,
    fontName: workData.commonJson.fontStyle.fontName,
    fontUrl: workData.commonJson.fontStyle.fontUrl,
    fontSize: workData.commonJson.fontStyle.fontSize,
    textColor: workData.commonJson.fontStyle.textColor,
    borderColor: workData.commonJson.fontStyle.borderColor,
    borderWidth: workData.commonJson.fontStyle.borderWidth
});

// 3. secondDigitalHumanUrl 存储
const currentDigitalHumanConfig = ref({
    type: 'picture',
    url: workData.commonJson.secondDigitalHumanUrl,
    index: null
});
```

#### 获取方式
```javascript
const digitalHumanStore = useDigitalHumanStore();

const commonJsonData = {
    subtitle_json: digitalHumanStore.subtitleData,
    fontStyle: {
        fontFamily: currentSubtitleConfig.value.fontFamily,
        fontName: currentSubtitleConfig.value.fontName,
        fontUrl: currentSubtitleConfig.value.fontUrl,
        fontSize: currentSubtitleConfig.value.fontSize,
        textColor: currentSubtitleConfig.value.textColor,
        borderColor: currentSubtitleConfig.value.borderColor,
        borderWidth: currentSubtitleConfig.value.borderWidth
    },
    secondDigitalHumanUrl: currentDigitalHumanConfig.value.url
};
```

---

### 4. personJson 数据存储

#### 原始数据结构
```javascript
workData.personJson = {
    x: 200,        // X坐标
    y: 100,        // Y坐标
    width: 400,    // 宽度
    height: 600    // 高度
}
```

#### 存储位置
- **位置**: PreviewEditor内部状态

#### 存储方式
```javascript
previewEditorRef.value.setCharacterPosition(workData.personJson);
```

#### 获取方式
```javascript
const editorData = getCurrentEditorData();

const personJsonData = {
    x: editorData.positionsData.character?.x,
    y: editorData.positionsData.character?.y,
    width: editorData.positionsData.character?.width,
    height: editorData.positionsData.character?.height
};
```

---

### 5. subtitleConfigJson 数据存储

#### 原始数据结构
```javascript
workData.subtitleConfigJson = {
    font_size: 24,                    // 字体大小
    color: "#ffffff",                 // 文字颜色
    stroke_color: "#000000",          // 描边颜色
    stroke_width: 2,                  // 描边宽度
    x: 100, y: 500,                  // 位置坐标
    width: 800, height: 100          // 尺寸
}
```

#### 存储位置
- **样式信息**: 组件响应式变量 `currentSubtitleConfig`
- **位置信息**: PreviewEditor内部状态

#### 存储方式
```javascript
// 1. 样式信息存储
const currentSubtitleConfig = ref({
    fontSize: workData.subtitleConfigJson.font_size,
    textColor: workData.subtitleConfigJson.color,
    borderColor: workData.subtitleConfigJson.stroke_color,
    borderWidth: workData.subtitleConfigJson.stroke_width,
    // ...其他配置
});

// 2. 位置信息存储
previewEditorRef.value.setSubtitleConfig(workData.subtitleConfigJson);
```

#### 获取方式
```javascript
const editorData = getCurrentEditorData();

const subtitleConfigJsonData = {
    font_size: editorData.subtitleConfig.fontSize,
    color: editorData.subtitleConfig.textColor,
    stroke_color: editorData.subtitleConfig.borderColor,
    stroke_width: editorData.subtitleConfig.borderWidth,
    x: editorData.positionsData.subtitle?.x,
    y: editorData.positionsData.subtitle?.y,
    width: editorData.positionsData.subtitle?.width,
    height: editorData.positionsData.subtitle?.height
};
```

---

## 🔧 推荐的完整数据获取方案

### 方案一：分别从Store和组件获取数据

```javascript
import { useDigitalHumanStore } from '@/views/modules/digitalHuman/store/digitalHumanStore';

const digitalHumanStore = useDigitalHumanStore();

// 从Store获取音频和字幕数据
const audioData = {
    duration: digitalHumanStore.totalDuration,
    wav_text: digitalHumanStore.subtitleData[0]?.text || '',
    wav_url: digitalHumanStore.ttsAudioUrl
};

const subtitleTimelineData = digitalHumanStore.subtitleData;
```

### 方案二：通过事件总线获取完整数据（推荐⭐）

```javascript
import eventBus from '@/common/utils/eventBus';
import { useDigitalHumanStore } from '@/views/modules/digitalHuman/store/digitalHumanStore';

const digitalHumanStore = useDigitalHumanStore();

// 一次性获取所有数据
eventBus.emit('request-digital-human-data', (editorData) => {
    
    // 重构完整的原始数据格式
    const reconstructedData = {
        
        // audioJson 重构
        audioJson: {
            duration: digitalHumanStore.totalDuration,
            wav_text: digitalHumanStore.subtitleData[0]?.text || '',
            wav_url: digitalHumanStore.ttsAudioUrl
        },
        
        // bgJson 重构
        bgJson: {
            src_url: editorData.backgroundConfig.value,
            x: editorData.positionsData.backgroundModule?.x,
            y: editorData.positionsData.backgroundModule?.y,
            width: editorData.positionsData.backgroundModule?.width,
            height: editorData.positionsData.backgroundModule?.height
        },
        
        // commonJson 重构
        commonJson: {
            subtitle_json: digitalHumanStore.subtitleData,
            fontStyle: {
                fontFamily: editorData.subtitleConfig.fontFamily,
                fontName: editorData.subtitleConfig.fontName,
                fontUrl: editorData.subtitleConfig.fontUrl,
                fontSize: editorData.subtitleConfig.fontSize,
                textColor: editorData.subtitleConfig.textColor,
                borderColor: editorData.subtitleConfig.borderColor,
                borderWidth: editorData.subtitleConfig.borderWidth
            },
            secondDigitalHumanUrl: editorData.digitalHumanConfig.url
        },
        
        // personJson 重构
        personJson: {
            x: editorData.positionsData.character?.x,
            y: editorData.positionsData.character?.y,
            width: editorData.positionsData.character?.width,
            height: editorData.positionsData.character?.height
        },
        
        // subtitleConfigJson 重构
        subtitleConfigJson: {
            font_size: editorData.subtitleConfig.fontSize,
            color: editorData.subtitleConfig.textColor,
            stroke_color: editorData.subtitleConfig.borderColor,
            stroke_width: editorData.subtitleConfig.borderWidth,
            x: editorData.positionsData.subtitle?.x,
            y: editorData.positionsData.subtitle?.y,
            width: editorData.positionsData.subtitle?.width,
            height: editorData.positionsData.subtitle?.height
        }
    };
    
    console.log('重构的完整数据:', reconstructedData);
    
    // 在这里使用重构的数据
    return reconstructedData;
});
```

---

## 📝 使用建议

1. **获取音频和字幕时间轴数据** - 直接使用 `digitalHumanStore`
2. **获取界面配置数据** - 使用事件总线获取 `editorData`
3. **获取完整数据** - 推荐使用**方案二**，一次性获取所有数据

## ⚠️ 注意事项

- `positionsData` 中的坐标可能为 `undefined`，使用时需要加上可选链操作符 `?.`
- `digitalHumanStore.subtitleData` 是数组格式，需要根据实际需求处理
- 事件总线方式是异步的，需要在回调函数中处理数据

---

## 📞 技术支持

如有疑问，请联系前端开发团队或查看相关源码：
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
- `src/views/modules/digitalHuman/store/digitalHumanStore.js` 