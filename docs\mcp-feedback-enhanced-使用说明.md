# MCP Feedback Enhanced 使用说明

## 概述

MCP Feedback Enhanced 是一个强大的交互反馈 MCP 服务器，为配音助手项目提供智能的 AI 交互反馈功能。

## 功能特性

### 🌐 Web UI 界面
- 轻量级浏览器界面，适合开发环境
- 响应式设计，适配不同屏幕尺寸
- 完整的简体中文支持

### 📝 智能工作流程
- **提示词管理**：常用提示词的 CRUD 操作、使用统计、智能排序
- **自动定时提交**：1-86400 秒弹性计时器，支持暂停、恢复、取消
- **会话管理追踪**：本地文件存储、隐私控制、历史导出
- **连接监控**：WebSocket 状态监控、自动重连

### 🖼️ 图片与媒体支持
- 支持格式：PNG、JPG、JPEG、GIF、BMP、WebP
- 便捷上传：拖拽文件、剪贴板粘贴（Ctrl+V）
- 无限制处理：支持任意大小图片

### 🎨 现代化体验
- 音效通知：内建多种音效、支持自定义音效上传
- 智能记忆：输入框高度记忆、一键复制、设置持久化
- 系统通知：重要事件的系统级即时提醒

## 安装状态

✅ **配置文件已创建**
- 标准模式：`.augment/mcp-config.json`
- 桌面模式：`.augment/mcp-config-desktop.json`

✅ **安装脚本已准备**
- 快速安装：`scripts/install-mcp-feedback.ps1`

⏳ **等待 Python 环境**
- 需要先安装 Python 3.11+ 并重启终端
- 然后运行安装脚本完成配置

## 使用流程

### 1. 完成安装
```powershell
# 重启 VS Code/Cursor 后运行
.\scripts\install-mcp-feedback.ps1
```

### 2. AI 交互
1. 在 AI 助手中添加规则：
   ```
   # MCP Interactive Feedback 规则
   注意在任务完成前根据mcp-feedback-enhanced工具的要求进行工具调用
   ```

2. AI 调用 `mcp-feedback-enhanced` 工具
3. 自动打开浏览器界面（http://localhost:8765）
4. 在界面中提供反馈、上传图片、选择提示词
5. AI 根据反馈调整行为或结束任务

### 3. 界面操作
- **文字输入**：在文本框中输入反馈内容
- **图片上传**：拖拽文件或 Ctrl+V 粘贴
- **提示词选择**：点击预设提示词快速输入
- **自动提交**：设置定时器自动提交反馈
- **会话管理**：查看历史会话和统计信息

## 快捷键

- `Ctrl+Enter`：提交反馈
- `Ctrl+V`：粘贴剪贴板图片
- `Ctrl+I`：快速聚焦输入框

## 配置选项

### 环境变量
- `MCP_DEBUG`：调试模式（true/false）
- `MCP_WEB_HOST`：Web UI 主机绑定（默认：127.0.0.1）
- `MCP_WEB_PORT`：Web UI 端口（默认：8765）
- `MCP_LANGUAGE`：界面语言（zh-CN/zh-TW/en）
- `MCP_DESKTOP_MODE`：桌面应用程序模式（true/false）

### 切换桌面模式
如需使用桌面应用程序模式：
1. 复制 `.augment/mcp-config-desktop.json` 内容
2. 替换 `.augment/mcp-config.json` 内容
3. 重启 Augment

## 故障排除

### 常见问题

**Q: 浏览器无法访问 http://localhost:8765**
A: 检查防火墙设置，或修改 `MCP_WEB_PORT` 环境变量

**Q: 中文界面显示异常**
A: 确保 `MCP_LANGUAGE` 设置为 "zh-CN"

**Q: WebSocket 连接失败**
A: 直接刷新浏览器页面重新连接

**Q: AI 没有调用 MCP 工具**
A: 确认 MCP 工具状态为绿灯，可尝试重启 Augment

## 项目集成

### 配音助手项目特性
- 与现有 Vue.js + Vite 开发环境完美兼容
- 不影响现有项目功能和依赖
- 支持开发过程中的实时反馈和调试
- 可用于 UI 界面优化、功能测试、用户体验改进

### 开发工作流程
1. **需求分析**：使用 MCP 与 AI 讨论功能需求
2. **设计评审**：上传界面截图获取设计建议
3. **代码审查**：提交代码片段进行优化建议
4. **测试反馈**：报告 bug 和功能问题
5. **文档完善**：协助生成和优化项目文档

## 下一步

Python 环境准备完成后：
1. 运行 `.\scripts\install-mcp-feedback.ps1`
2. 测试 Web UI 功能
3. 在 Augment 中配置 MCP 服务器
4. 开始使用智能反馈功能
