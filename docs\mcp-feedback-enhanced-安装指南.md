# MCP Feedback Enhanced 安装配置指南

## 系统环境检查结果

当前系统状态：
- ✅ Node.js: v20.19.2
- ✅ npm: v10.8.2  
- ❌ Python: 未安装
- ❌ uv: 未安装
- ❌ winget: 未安装
- ❌ chocolatey: 未安装

## 安装步骤

### 第一步：安装 Python

由于系统缺少 Python 环境，需要先安装 Python。推荐以下方式之一：

#### 方式一：从官网下载安装（推荐）
1. 访问 [Python 官网](https://www.python.org/downloads/)
2. 下载 Python 3.11 或 3.12 版本
3. 安装时**务必勾选 "Add Python to PATH"**
4. 验证安装：打开新的 PowerShell 窗口，运行 `python --version`

#### 方式二：使用 Microsoft Store
1. 打开 Microsoft Store
2. 搜索 "Python 3.11" 或 "Python 3.12"
3. 点击安装
4. 验证安装：`python --version`

### 第二步：安装 uv 包管理器

Python 安装完成后，安装 uv：

```powershell
# 使用 pip 安装 uv
pip install uv

# 验证安装
uv --version
```

### 第三步：安装 mcp-feedback-enhanced

```powershell
# 安装 mcp-feedback-enhanced
uvx mcp-feedback-enhanced@latest version

# 测试 Web UI
uvx mcp-feedback-enhanced@latest test --web
```

### 第四步：创建 Augment 配置

在项目根目录创建 `.augment/mcp-config.json` 文件：

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "8765",
        "MCP_LANGUAGE": "zh-CN"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 第五步：添加 AI 助手规则

在 AI 助手中添加以下规则：

```
# MCP Interactive Feedback 规则
注意在任务完成前根据mcp-feedback-enhanced工具的要求进行工具调用
```

## 功能特性

- 🌐 **Web UI 界面**：轻量级浏览器界面，适合开发环境
- 📝 **智能工作流程**：提示词管理、自动定时提交、会话管理追踪
- 🎨 **现代化体验**：响应式设计、音效通知、智能记忆
- 🖼️ **图片支持**：支持 PNG、JPG、JPEG、GIF、BMP、WebP 格式
- 🌏 **中文界面**：完整的简体中文支持

## 使用方法

1. AI 调用 `mcp-feedback-enhanced` 工具
2. 自动打开浏览器界面（http://localhost:8765）
3. 在界面中提供反馈、上传图片、选择提示词
4. AI 根据反馈调整行为或结束任务

## 故障排除

### 常见问题

**Q: Python 安装后仍提示找不到命令**
A: 重新启动 PowerShell 或 VS Code，确保环境变量生效

**Q: uv 安装失败**
A: 尝试使用 `python -m pip install uv` 或升级 pip：`python -m pip install --upgrade pip`

**Q: 浏览器无法访问 http://localhost:8765**
A: 检查防火墙设置，或尝试更换端口（修改 MCP_WEB_PORT 环境变量）

**Q: 中文界面显示异常**
A: 确保 MCP_LANGUAGE 设置为 "zh-CN"

## 重要提示：Python 安装后的环境变量问题

✅ **您已成功安装 Python 3.13.5！**

但是，Python 安装后需要重启终端才能使环境变量生效。请按以下步骤操作：

### 立即解决方案

1. **完全关闭当前的 VS Code 或 Cursor**
2. **重新打开 VS Code/Cursor 和项目**
3. **打开新的终端窗口**
4. **验证 Python 安装**：
   ```powershell
   python --version
   # 应该显示：Python 3.13.5

   pip --version
   # 应该显示 pip 版本信息
   ```

### 如果仍然无法识别 Python

如果重启后仍然提示找不到 python 命令，请检查：

1. **手动添加到 PATH**：
   - 按 `Win + R`，输入 `sysdm.cpl`
   - 点击"环境变量"
   - 在"系统变量"中找到 `Path`，点击"编辑"
   - 添加 Python 安装路径（通常是 `C:\Users\<USER>\AppData\Local\Programs\Python\Python313\` 和 `C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\`）

2. **使用完整路径**：
   ```powershell
   # 查找 Python 安装位置
   Get-ChildItem -Path "C:\Users\<USER>\AppData\Local\Programs\Python\" -Recurse -Name "python.exe" 2>$null
   ```

### 下一步

**请先重启 VS Code/Cursor，然后在新终端中运行 `python --version` 验证安装。**

## 当前状态更新

✅ **Python 环境已验证**：Python 3.13.5 正常工作
❌ **网络安装问题**：由于网络连接超时和文件权限问题，无法直接安装 mcp-feedback-enhanced

## 临时解决方案

由于网络安装问题，我已为您准备了以下替代方案：

### 方案一：使用现有 MCP 基础设施（推荐）
您的项目已经有 `@modelcontextprotocol/server-filesystem`，可以立即使用：

1. **配置文件已准备**：`.augment/mcp-config-temp.json`
2. **重命名配置文件**：
   ```powershell
   mv .augment/mcp-config-temp.json .augment/mcp-config.json
   ```
3. **立即可用**：文件系统 MCP 服务器提供基础的文件读写功能

### 方案二：离线安装 mcp-feedback-enhanced
1. **下载离线包**：从其他网络环境下载 mcp-feedback-enhanced 的 wheel 文件
2. **本地安装**：使用 `pip install --user <wheel文件路径>`
3. **配置使用**：更新 MCP 配置文件

### 方案三：网络问题解决后重试
1. **检查网络连接**：确保可以访问 PyPI
2. **清理临时文件**：删除 `C:\Users\<USER>\AppData\Local\Temp\pip-*` 目录
3. **重新安装**：
   ```powershell
   pip install --user --no-cache-dir mcp-feedback-enhanced
   ```

## 推荐操作

**立即可用的方案**：
```powershell
# 重命名配置文件
mv .augment/mcp-config-temp.json .augment/mcp-config.json

# 在 Augment 中配置 MCP 服务器
# 现在就可以使用文件系统 MCP 功能了
```

这样您就可以立即开始使用 MCP 功能，包括：
- 文件读取和写入
- 目录列表
- 基础的代码库交互
