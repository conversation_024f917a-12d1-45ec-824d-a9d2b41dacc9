# 二维码路由判断功能实现方案

## 需求描述
根据当前页面路由显示不同的二维码：
- API服务页面：显示 `free_trial_sound_ercode.png`
- 其他页面：显示 `contact.png`

## 实现方案

### 技术方案
使用Vue 3的Composition API中的`useRoute`获取当前路由信息，通过计算属性动态判断显示哪个二维码。

### 修改文件
- `src/views/modules/realVoice/components/index/contact.vue`
- `src/views/modules/realVoice/components/index/free_trial_sound.vue`

### 关键代码实现

#### 1. 导入依赖
```javascript
// contact.vue
import { ref, defineExpose, reactive, computed } from 'vue';
import { useRoute } from 'vue-router';

// free_trial_sound.vue
import { ref, defineExpose, computed } from 'vue';
import { useRoute } from 'vue-router';

// 导入二维码图片（两个组件都需要）
import freeTrialSoundErcode from '@/assets/images/realVoice/free_trial_sound_ercode.png';
import contactErcode from '@/assets/images/realVoice/contact.png';
```

#### 2. 路由判断逻辑
```javascript
// 获取当前路由信息
const route = useRoute();

// 判断是否为API服务页面
const isApiServicePage = computed(() => {
    return route.name === 'apiService' || route.name === 'documentation';
});

// 根据路由动态设置二维码图片
const qrCodeImage = computed(() => {
    return isApiServicePage.value ? freeTrialSoundErcode : contactErcode;
});
```

#### 3. 模板绑定
```html
<!-- contact.vue 和 free_trial_sound.vue 都使用相同的绑定方式 -->
<img :src="qrCodeImage" alt="">
```

## 路由配置
- API服务页面路由名称：`apiService`
- API开发文档页面路由名称：`documentation`

## 测试验证
1. 在API服务页面打开联系客服弹窗（contact.vue），应显示 `free_trial_sound_ercode.png`
2. 在API服务页面打开免费试音弹窗（free_trial_sound.vue），应显示 `free_trial_sound_ercode.png`
3. 在其他页面（如真人配音、声音克隆等）打开联系客服弹窗，应显示 `contact.png`
4. 在其他页面打开免费试音弹窗，应显示 `contact.png`

## 影响范围
这两个组件被以下页面使用：

### contact.vue（联系客服弹窗）
- API服务页面
- 真人配音页面
- 声音克隆页面
- 商业配音页面
- 音色商店详情页
- 等多个页面

### free_trial_sound.vue（免费试音弹窗）
- 真人配音页面
- 其他需要展示免费试音二维码的页面

## 优势
1. 无需修改其他调用这些组件的页面
2. 组件内部自动判断路由，符合内聚原则
3. 两个组件使用相同的逻辑，保持一致性
4. 使用import导入图片，确保打包时正确处理资源
5. 易于维护和扩展

## 技术要点
- 使用import语句导入图片资源，而不是字符串路径
- 在Vue中动态绑定图片需要使用导入的变量
- 两个组件使用完全相同的路由判断逻辑

## 注意事项
- 确保路由名称准确匹配（`apiService`、`documentation`）
- 二维码图片文件存在且路径正确
- 两个组件在不同页面中的表现保持一致
- 图片导入使用相同的变量名，便于维护
