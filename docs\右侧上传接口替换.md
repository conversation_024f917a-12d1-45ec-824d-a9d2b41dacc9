# 右侧上传接口替换文档

## 修改概述

将数字人编辑器右侧上传音频功能中的接口从 `crawlTextByMediaFile` 替换为 `getDigiAudioJsonTxt`，参数保持一致。

## 修改详情

### 修改文件
- `src/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio.vue`

### 具体修改内容

#### 1. 导入接口替换
```javascript
// 修改前
import { extractFile } from "@/api/dubbing";

// 修改后  
import { getDigiAudioJsonTxt } from "@/api/digitalHuman";
```

#### 2. 接口调用替换
```javascript
// 修改前
let text = await extractFile({
    url: uploadRequest.url,
    userId: getUserId(),
});

// 修改后
let text = await getDigiAudioJsonTxt({
    url: uploadRequest.url,
    userId: getUserId(),
});
```

#### 3. 数据处理逻辑优化

添加了智能的字幕数据拼接逻辑：

```javascript
// 处理字幕数据拼接
let fullText = '';
if (text.content && text.content.result) {
    // 优先使用 txt 字段
    if (text.content.result.txt) {
        fullText = text.content.result.txt;
        console.log('使用 txt 字段:', fullText.substring(0, 50) + '...');
    }
    // 从 subtitle_json 数组中提取 text 字段并拼接
    else if (text.content.result.subtitle_json && Array.isArray(text.content.result.subtitle_json)) {
        const subtitleTexts = text.content.result.subtitle_json
            .map(item => item.text || '') // 直接使用 text 字段
            .filter(text => text && text.trim() !== ''); // 过滤空文本

        fullText = subtitleTexts.join(''); // 直接连接，不加空格
        console.log('从 subtitle_json 拼接文本:', {
            字幕条数: text.content.result.subtitle_json.length,
            拼接后文本: fullText.substring(0, 50) + '...'
        });
    }
}

if (!fullText) {
    console.warn('无法提取文本内容');
    ElMessage.warning('无法提取音频中的文本内容');
}
```

#### 4. 错误处理改进

```javascript
// 修改前
if (!text || text.status_code !== 200) {
    ElMessage.error(response.message || '处理失败');
}

// 修改后
if (!text || text.status_code !== 200) {
    ElMessage.error(text?.message || '处理失败');
    loading.value.close();
    return;
}
```

## 接口对比

### 参数
两个接口的参数完全一致：
- `url`: 音频文件URL
- `userId`: 用户ID

### 返回数据结构
`getDigiAudioJsonTxt` 接口支持更丰富的数据格式：

```javascript
{
  status_code: 200,
  content: {
    result: {
      txt: "完整文本内容",  // 优先使用
      subtitle_json: [     // 备选方案，从每个对象的text字段拼接
        {
          "id": 1,
          "seek": 0,
          "start": 6.6,
          "end": 9.9,
          "text": "字幕文本内容",
          "tokens": [...],
          "temperature": 0.0,
          "avg_logprob": -0.08410485982894897,
          "compression_ratio": 1.3826714801444044,
          "no_speech_prob": 0.4322985112667084,
          "time_begin": 6600.0,
          "time_end": 9900.0
        }
      ]
    }
  }
}
```

## 功能改进

1. **智能文本提取**：优先使用 `txt` 字段，如果没有则从 `subtitle_json` 数组拼接
2. **更好的错误处理**：添加了详细的日志和用户友好的错误提示
3. **数据验证**：对返回数据进行严格验证，确保数据完整性
4. **调试支持**：添加了详细的控制台日志，便于问题排查

## 测试建议

1. 上传不同格式的音频文件（mp3、wav、m4a）
2. 测试不同时长的音频文件
3. 验证字幕文本是否正确拼接和显示
4. 检查错误情况下的用户提示是否友好

## 注意事项

- 新接口返回的数据结构更加丰富，支持时间轴信息
- 字幕拼接逻辑会过滤空文本，确保输出质量
- 保持了与原有功能的完全兼容性
- 添加了更详细的日志输出，便于调试和维护
