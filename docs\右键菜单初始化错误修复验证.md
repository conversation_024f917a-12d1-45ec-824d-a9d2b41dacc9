# 右键菜单初始化错误修复验证

## 问题描述
用户报告在PreviewEditor.vue第3102行出现JavaScript错误：
```
Uncaught (in promise) ReferenceError: Cannot access 'hideContextMenu' before initialization
```

## 根本原因
函数定义顺序问题：
- `defineExpose` 在第3094行被调用
- `hideContextMenu` 函数定义在第3235行
- 导致在函数定义之前就尝试引用该函数

## 修复方案
将函数定义顺序重新排列：
1. 移除原有的`defineExpose`块（第3073-3174行）
2. 将`hideContextMenu`函数定义移到右键菜单状态管理部分（第3095行）
3. 将`defineExpose`块移到文件最后，在`</script>`标签之前（第3276行）

## 修复后的顺序
```javascript
// 第3095行：hideContextMenu函数定义
const hideContextMenu = () => {
    if (contextMenu.value.visible) {
        contextMenu.value.visible = false;
        console.log('🖱️ 右键菜单已隐藏');
    }
};

// ... 其他函数定义 ...

// 第3276行：defineExpose调用
defineExpose({
    // ... 包括hideContextMenu在内的所有方法
    hideContextMenu,
    // ...
});
```

## 验证步骤
1. **刷新浏览器页面**
2. **打开开发者工具控制台**
3. **检查是否还有初始化错误**
4. **测试右键菜单功能**：
   - 右键点击背景层/数字人层/字幕层
   - 确认菜单正常显示
   - 左键点击页面任何地方
   - 确认菜单正确隐藏

## 预期结果
- ✅ 无JavaScript初始化错误
- ✅ 右键菜单正常显示
- ✅ 左键点击正确隐藏菜单
- ✅ 控制台显示相关调试日志

## 如果仍有问题
请检查：
1. 浏览器缓存是否已清除
2. 是否有其他JavaScript错误
3. 控制台中的具体错误信息
4. 网络请求是否正常加载了最新的文件

## 相关文件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue`
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
