# 声音克隆页面功能更新文档

## 项目概述

本文档记录了声音克隆功能页面的最新更新和配置信息，包括用户案例更新、音频配置、UI优化等内容。

## 更新日期

**最后更新时间：** 2025-01-15

## 更新历史

### v1.7 (2025-01-15)
- **徽章层级优化：**
  - 降低快速克隆卡片"首充送一个月VIP"徽章的z-index层级
  - 修改内容：
    - 将`.quick-clone-badge`的z-index从10降低到1
    - 减少徽章的视觉突出程度，提升整体页面层次感
  - 涉及文件：
    - `src/views/modules/voiceClone/index.vue` (第890行)
  - 改进了页面视觉层次，让徽章不会过于抢眼

### v1.6 (2025-01-15)
- **术语统一更新：**
  - 将所有"复刻"术语统一改为"克隆"
  - 主要修改内容：
    - 卡片标题："精品复刻" → "精品克隆"，"SFT复刻" → "SFT克隆"
    - 按钮文字："立即复刻" → "立即克隆"
    - 代码注释：所有注释中的"复刻"改为"克隆"
    - CSS样式注释：更新样式类的注释说明
  - 涉及文件：
    - `src/views/modules/voiceClone/index.vue`
    - `src/views/modules/cloneService/index.vue`
  - 确保了整个应用中术语的一致性

### v1.5 (2025-01-15)
- **头像更新：**
  - 更新所有声音克隆页面的用户头像为真实角色头像
  - 头像URL映射：
    - 王也：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E7%8E%8B%E4%B9%9F.png`
    - 顾知夏：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E9%A1%BE%E7%9F%A5%E5%A4%8F.png`
    - 苏禾：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E8%8B%8F%E7%A6%BE.png`
    - 明远：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%98%8E%E8%BF%9C.png`
    - 李修远：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%9D%8E%E4%BF%AE%E8%BF%9C.png`
    - 沈知遥：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%B2%88%E7%9F%A5%E9%81%A5.png`
  - 实现动态头像显示：根据用户名称自动匹配对应头像
  - 涉及文件：
    - `src/views/modules/voiceClone/index.vue`
    - `src/views/modules/cloneService/index.vue`

### v1.4 (2025-01-14)
- **用户简介文字修正：**
  - 修正沈知遥用户简介：将"生椰配音录制"改为"商业配音录制"
  - 同步更新了两个页面的描述文字：
    - 体验入口页面 (`src\views\modules\voiceClone\index.vue`)
    - 声音克隆服务页面 (`src\views\modules\cloneService\index.vue`)
  - 确保了页面间描述文字的一致性

### v1.3 (2025-01-14)
- **UI样式优化：**
  - 修改声音克隆服务页面小圆点颜色：#51D19B → #353D49
  - 统一了页面设计色彩规范

### v1.2 (2025-01-14)
- **精品克隆和SFT克隆卡片用户信息更新：**
  - 精品克隆：沈知遥 → 苏禾，王也 → 明远
  - SFT克隆：苏禾 → 李修远，顾知夏 → 沈知遥
  - 为所有用户添加了具体的应用场景描述文字
  - 更新了对应的音频URL配置
  - 同步更新了两个页面的配置

### v1.1 (2025-01-14)
- **快速克隆卡片用户信息更新：**
  - 用户名称：明远 → 王也，李修远 → 顾知夏
  - 添加了具体的用户介绍文字
  - 更新了对应的音频URL配置
  - 同步更新了两个页面的配置

## 涉及文件

- `src/views/modules/voiceClone/index.vue` - 声音克隆主页面
- `src/views/modules/cloneService/index.vue` - 声音克隆服务页面

## 主要更新内容

### 1. 用户案例更新

#### 快速克隆卡片
- **用户1：** 王也
  - 原声音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E7%8E%8B%E4%B9%9F%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3`
  - 复刻音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E7%8E%8B%E4%B9%9F%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3`
  - 介绍文字：该案例适用于短视频创作及各类影视二创。

- **用户2：** 顾知夏
  - 原声音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E9%A1%BE%E7%9F%A5%E5%A4%8F%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3`
  - 克隆音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E9%A1%BE%E7%9F%A5%E5%A4%8F%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3`
  - 介绍文字：该案例适用于第一人称视角的独白或走心旁白。

#### 精品克隆卡片
- **用户1：** 苏禾
  - 原声音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E8%8B%8F%E7%A6%BE%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3`
  - 克隆音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E8%8B%8F%E7%A6%BE%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3`
  - 介绍文字：此案例适用于日常对话，广告、走心旁白等。

- **用户2：** 明远
  - 原声音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%98%8E%E8%BF%9C%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3`
  - 克隆音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%98%8E%E8%BF%9C%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3`
  - 介绍文字：此案例适用于短视频创作、书籍介绍、国学介绍等。

#### SFT克隆卡片
- **用户1：** 李修远
  - 原声音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%9D%8E%E4%BF%AE%E8%BF%9C%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3`
  - 克隆音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%9D%8E%E4%BF%AE%E8%BF%9C%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3`
  - 介绍文字：此案例适用于汇报片、宣传片等风格的商业配音录制。

- **用户2：** 沈知遥
  - 原声音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%B2%88%E7%9F%A5%E9%81%A5%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3`
  - 克隆音频：`https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%B2%88%E7%9F%A5%E9%81%A5%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3`
  - 介绍文字：此案例适用于纪录片，宣传片等风格的商业配音录制。（v1.4更新：修正"生椰"为"商业"）

### 2. 文案内容更新

#### 快速克隆
- **副标题：** "仅需录制5-30秒内语料"
- **详细描述：** "开放环境中录制秒级别录音即可极速拥有专属定制音色，可满足基本的配音需求，支持32个小语种。"

#### 精品克隆
- **副标题：** "棚录30分钟以上语料，精细语音复制"
- **详细描述：** "高品质声音克隆，95%音色还原，情感丰富，适用于各类场景的有声创作及日常对话，支持32个小语种。"

#### SFT克隆
- **副标题：** "棚录2-3小时语料，超精细监督微调语音复制"
- **详细描述：** "99%超细节还原真实音色，情感细腻，可完美进行各类场景的声音播报，支持32个小语种。"

### 3. UI优化

#### SFT卡片头部背景
- **渐变色配置：** `linear-gradient(135deg, #2D1B69 0%, #0D36B1 100%)`
- **背景覆盖：** 修复了背景图片不完全覆盖的问题
- **圆角适配：** 添加了顶部圆角以匹配卡片样式

#### 悬停效果优化
- **移除边框：** 去掉了鼠标悬停时的蓝色边框线
- **保留动效：** 保持卡片上浮和阴影效果

### 4. 音频播放功能

#### 互斥播放机制
- **同时播放控制：** 确保同一时间只有一个音频在播放
- **跨卡片控制：** 不同卡片之间的音频播放会互相停止
- **状态同步：** 播放按钮的视觉状态正确更新

#### 全局点击停止功能
- **点击其他区域停止：** 用户点击页面非播放按钮区域时自动停止音频播放
- **智能检测：** 避免点击播放按钮本身时意外停止播放
- **事件清理：** 页面卸载时自动清理事件监听器

## 技术实现细节

### 音频URL映射
```javascript
// 快速克隆音频配置
const getQuickAudioUrl = (container, button) => {
    const audioUrls = {
        '1_1': '明远原声案例.mp3',
        '1_2': '明远复刻案例.mp3',
        '2_1': '李修远原声案例.mp3',
        '2_2': '李修远复刻案例.mp3'
    }
    return audioUrls[`${container}_${button}`]
}
```

### 全局点击事件处理
```javascript
const handleGlobalClick = (event) => {
    const isPlayButton = event.target.closest('.play-img') || 
                        event.target.closest('.play-button-item')
    if (!isPlayButton) {
        stopAllAudio()
    }
}
```

## 用户体验改进

### 播放控制
1. **多种停止方式：**
   - 点击其他播放按钮
   - 点击正在播放的按钮
   - 点击页面其他区域

2. **视觉反馈：**
   - 播放状态图标切换
   - 悬停效果优化
   - 无干扰的边框设计

### 内容展示
1. **真实案例：** 使用真实用户的声音克隆案例
2. **详细描述：** 提供准确的功能描述和技术参数
3. **多语种支持：** 明确标注支持32个小语种

## 维护说明

### 音频文件管理
- 所有音频文件存储在阿里云OSS：`miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com`
- 文件命名规范：`{用户名}{原声/复刻}案例.mp3`
- 建议定期检查音频文件的可访问性

### 代码维护
- 音频URL配置集中在各自的播放函数中
- 用户名字配置在 `getUserName` 函数中
- 全局事件监听器需要在页面卸载时正确清理

## 后续优化建议

1. **性能优化：** 考虑音频预加载机制
2. **错误处理：** 添加音频加载失败的提示
3. **可访问性：** 添加键盘导航支持
4. **移动端适配：** 优化移动设备上的播放体验

---

**文档维护者：** AI Assistant
**创建日期：** 2025-01-14
**版本：** v1.7
