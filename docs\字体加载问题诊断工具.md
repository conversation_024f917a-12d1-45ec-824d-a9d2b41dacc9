# 字体加载问题诊断工具

## 问题现象
在数字人编辑器左侧面板选择字体后，字幕文字没有发生变化。

## 诊断步骤

### 1. 检查API数据结构
在浏览器控制台中执行以下命令来查看字体API返回的数据结构：

```javascript
// 查看字体列表数据
window.debugFontSystem()
```

这将显示：
- 字体列表的完整数据结构
- 当前选中的字体详情
- 字体URL字段的检查结果
- 字体加载状态

### 2. 验证字体选择事件传递
选择一个字体后，在控制台中查看以下日志：

```
🎨 字体选择变更: {fontId, fontName, fontUrl}
🎨 准备发射字幕样式变更事件: {...}
🎨 发射字幕样式变更事件: {...}
```

### 3. 检查父组件接收
在主组件中查看以下日志：

```
🎨 收到字幕样式变更事件: {...}
✅ 字幕样式更新成功: {fontFamily, fontName, fontUrl, fontSize}
```

### 4. 验证PreviewEditor组件
在PreviewEditor组件中查看：

```
🎨 字体配置变化检测: {新字体名称, 新字体URL, ...}
🎨 字体家族计算: {fontName, fontId, fontUrl, ...}
🎨 检测到字体变化，准备加载: "字体名称" URL: 字体URL
```

### 5. 检查字体加载器
查看fontLoader的加载日志：

```
🎨 使用动态字体URL: "字体名称" → 字体URL
✅ 字体 "字体名称" 加载成功
```

## 常见问题和解决方案

### 问题1: API返回的字体数据中没有URL字段
**症状**: 控制台显示"⚠️ 警告：选中的字体没有找到URL字段"

**✅ 已解决**:
经过分析发现，getFontList API返回的字体数据中包含 `ttf_path` 字段，这就是字体文件的URL。

**解决方案**:
1. ✅ 已更新代码优先使用 `ttf_path` 字段
2. ✅ 已更新调试日志显示 `ttf_path` 字段
3. ✅ 保持对其他可能字段名的兼容性

### 问题2: 字体URL获取正确但没有传递到PreviewEditor
**症状**: 左侧面板日志正常，但PreviewEditor没有收到fontUrl

**解决方案**:
1. 检查DigitalHumanEditorPage.vue中的handleSubtitleStyleChange函数
2. 确认currentSubtitleConfig.value包含fontUrl字段
3. 验证props传递是否正确

### 问题3: 字体加载成功但样式没有应用
**症状**: fontLoader显示加载成功，但字幕样式没有变化

**解决方案**:
1. 检查CSS字体家族是否正确设置
2. 验证字体文件格式是否支持
3. 检查浏览器字体缓存

### 问题4: 字体文件无法访问
**症状**: 网络错误或CORS问题

**解决方案**:
1. 检查字体文件URL是否可访问
2. 验证CORS设置
3. 尝试使用不同的字体文件格式

## 调试命令

在浏览器控制台中使用以下命令进行调试：

```javascript
// 查看字体系统状态
window.debugFontSystem()

// 查看左侧面板选择状态
window.debugLeftPanel()

// 手动测试字体加载
FontLoader.loadFont('测试字体名称', 'https://example.com/font.ttf')

// 查看当前加载的字体
FontLoader.getLoadedFonts()

// 清除字体缓存
FontLoader.clearCache()
```

## 预期的正常流程

1. **字体选择**: 用户在左侧面板选择字体
2. **数据获取**: 从fontListData中获取字体URL
3. **事件发射**: 发射subtitle-style-change事件，包含fontUrl
4. **父组件接收**: DigitalHumanEditorPage接收并更新currentSubtitleConfig
5. **Props传递**: 通过props传递给PreviewEditor组件
6. **字体加载**: PreviewEditor监听到变化，调用loadFont
7. **样式应用**: 字体加载成功后，更新CSS样式
8. **界面更新**: 字幕文字显示新字体

## 测试用例

### 测试用例1: 基本字体切换
1. 打开数字人编辑器
2. 在左侧面板选择不同字体
3. 观察字幕文字是否变化
4. 检查控制台日志

### 测试用例2: 网络异常处理
1. 断开网络连接
2. 选择需要下载的字体
3. 观察错误处理和降级机制

### 测试用例3: 字体缓存机制
1. 选择一个字体（首次加载）
2. 切换到其他字体
3. 再次切换回第一个字体
4. 验证是否使用缓存

## 修复检查清单

- [x] API返回数据包含字体URL字段（ttf_path字段）
- [x] 左侧面板正确获取字体URL
- [x] 字体选择事件正确发射
- [ ] 父组件正确接收和处理事件
- [ ] Props正确传递给PreviewEditor
- [ ] PreviewEditor正确监听配置变化
- [ ] fontLoader正确加载字体文件
- [ ] CSS样式正确应用
- [ ] 字幕文字显示新字体

## 代码清理记录

### 2024-12-19 清理完成
**删除的冗余代码**:
- ❌ `getFontUrl` 函数（45行代码）- 不再需要额外API调用
- ❌ `fontUrlCache` 变量 - 只被已删除的函数使用
- ❌ `generateMockFontUrl` 函数（16行代码）- 有真实数据，不需要模拟
- ❌ `getFontFileUrl` API导入 - 不再使用该API

**简化的代码**:
- ✅ `handleFontStyleChange` 函数 - 直接使用 `ttf_path` 字段，移除异步逻辑
- ✅ 保留所有调试功能和字体加载状态管理

**清理效果**:
- 📉 减少代码行数：约61行
- ⚡ 提升性能：减少不必要的API调用
- 🧹 提高可维护性：逻辑更简洁清晰
- 🔧 保持功能完整：核心功能和调试工具完全保留
