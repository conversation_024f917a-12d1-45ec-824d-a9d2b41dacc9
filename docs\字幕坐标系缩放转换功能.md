# 字幕坐标系缩放转换功能实现

## 功能概述

实现字幕在不同坐标系之间的缩放转换，确保前端页面显示效果与生成视频中的字幕效果完全一致。

### 核心问题
- **前端页面**：在403×700（9:16模式）或901.333×507（16:9模式）的预览窗口中显示
- **接口数据**：基于1080×1920标准坐标系
- **需求**：字体大小、宽度、高度需要按比例缩放转换

## 实现时间
2025-01-16

## 涉及文件

### 1. 接口数据发送时的正向缩放转换
**文件**: `src/views/layout/components/headbar/components/action/index.vue`

#### 字体大小缩放转换（第707-735行）
```javascript
// 🎯 字幕宽高和字体大小坐标系缩放转换
let scaledFontSize = editorData?.subtitleConfig?.fontSize || 18;
let scaledSubtitleWidth = subtitleWidth;
let scaledSubtitleHeight = subtitleHeight;

// 获取页面尺寸和标准尺寸，计算缩放比例
const originalPageSize = standardPositionsData.previewWindow?.originalPageSize;
if (originalPageSize && originalPageSize.width > 0 && originalPageSize.height > 0) {
    const standardWidth = 1080;
    const standardHeight = 1920;

    // 计算X轴和Y轴缩放比例
    const scaleX = standardWidth / originalPageSize.width;
    const scaleY = standardHeight / originalPageSize.height;

    // 应用缩放比例到字体大小（按Y轴缩放）
    scaledFontSize = Math.round(scaledFontSize * scaleY);

    // 应用缩放比例到字幕宽高
    scaledSubtitleWidth = Math.round(subtitleWidth * scaleX);
    scaledSubtitleHeight = Math.round(subtitleHeight * scaleY);

    console.log('🎯 字幕尺寸和字体大小坐标系缩放转换:', {
        页面尺寸: `${originalPageSize.width}×${originalPageSize.height}`,
        标准尺寸: `${standardWidth}×${standardHeight}`,
        缩放比例: `X轴:${scaleX.toFixed(4)}, Y轴:${scaleY.toFixed(4)}`,
        字体大小: `${editorData?.subtitleConfig?.fontSize || 18} → ${scaledFontSize}`,
        字幕宽度: `${subtitleWidth} → ${scaledSubtitleWidth}`,
        字幕高度: `${subtitleHeight} → ${scaledSubtitleHeight}`
    });
}
```

#### subtitleConfigJson配置（第737-748行）
```javascript
const subtitleConfigJson = {
    show: subtitleShow,
    x: subtitleX,
    y: subtitleY,
    width: scaledSubtitleWidth,      // 🎯 使用缩放后的字幕宽度
    height: scaledSubtitleHeight,    // 🎯 使用缩放后的字幕高度
    font_size: scaledFontSize,       // 🎯 使用缩放后的字体大小
    color: editorData?.subtitleConfig?.textColor || "#FFFFFF",
    stroke_color: editorData?.subtitleConfig?.borderColor || "",
    stroke_width: editorData?.subtitleConfig?.borderWidth || 0,
    font_id: editorData?.subtitleConfig?.fontFamily || "font_001"
};
```

#### fontStyleInfo配置（第867-887行）
```javascript
// 🎯 计算缩放后的字体大小（与subtitleConfigJson保持一致）
let fontStyleFontSize = subtitleConfig.fontSize || 18;
const originalPageSize = standardPositionsData.previewWindow?.originalPageSize;
if (originalPageSize && originalPageSize.width > 0 && originalPageSize.height > 0) {
    const standardHeight = 1920;
    const scaleY = standardHeight / originalPageSize.height;
    fontStyleFontSize = Math.round(fontStyleFontSize * scaleY);
}

const fontStyleInfo = {
    fontFamily: subtitleConfig.fontFamily || '1',
    fontName: subtitleConfig.fontName || '微软雅黑',
    fontUrl: subtitleConfig.fontUrl || '',
    fontSize: fontStyleFontSize,                         // 🎯 使用缩放后的字体大小
    textColor: subtitleConfig.textColor || '#ffffff',
    borderColor: subtitleConfig.borderColor || '',
    borderWidth: subtitleConfig.borderWidth || 0
};
```

### 2. 数据回显时的反向缩放转换
**文件**: `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

#### PreviewEditor组件暴露预览窗口尺寸
**文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`（第4236-4244行）
```javascript
defineExpose({
    // ... 其他暴露的方法
    
    // 📐 预览窗口尺寸信息
    previewWindowWidth,         // 当前预览窗口宽度
    previewWindowHeight,        // 当前预览窗口高度
    
    // ... 其他暴露的方法
});
```

#### subtitleConfigJson字体大小反向缩放（第1427-1465行）
```javascript
// 🎯 字体大小反向缩放转换（标准坐标系 → 页面坐标系）
let pageFontSize = workData.subtitleConfigJson.font_size || currentSubtitleConfig.value.fontSize;

// 获取当前预览窗口尺寸，计算反向缩放比例
if (workData.subtitleConfigJson.font_size && previewEditorRef.value) {
    try {
        const previewWindowWidth = previewEditorRef.value.previewWindowWidth;
        const previewWindowHeight = previewEditorRef.value.previewWindowHeight;
        
        if (previewWindowWidth && previewWindowHeight && previewWindowHeight > 0) {
            const standardHeight = 1920;
            const scaleY = standardHeight / previewWindowHeight;
            
            // 反向缩放：页面字体大小 = 标准字体大小 ÷ scaleY
            pageFontSize = Math.round(workData.subtitleConfigJson.font_size / scaleY);
            
            console.log('🎯 字体大小反向缩放转换:', {
                标准字体大小: workData.subtitleConfigJson.font_size,
                页面尺寸: `${previewWindowWidth}×${previewWindowHeight}`,
                标准尺寸: `1080×${standardHeight}`,
                Y轴缩放比例: scaleY.toFixed(4),
                转换后页面字体大小: pageFontSize
            });
        }
    } catch (error) {
        console.warn('⚠️ 字体大小反向缩放转换失败，使用原始值:', error);
    }
}

// 🎯 样式配置映射
const subtitleStyleConfig = {
    fontFamily: currentSubtitleConfig.value.fontFamily,
    fontName: currentSubtitleConfig.value.fontName,
    fontSize: pageFontSize,                             // 🎯 使用反向缩放后的字体大小
    textColor: workData.subtitleConfigJson.color || currentSubtitleConfig.value.textColor,
    borderColor: workData.subtitleConfigJson.stroke_color || currentSubtitleConfig.value.borderColor,
    borderWidth: workData.subtitleConfigJson.stroke_width || currentSubtitleConfig.value.borderWidth,
    fontUrl: currentSubtitleConfig.value.fontUrl
};
```

#### commonJson.fontStyle字体大小反向缩放（第1373-1411行）
```javascript
// 🎯 字体大小反向缩放转换（标准坐标系 → 页面坐标系）
let pageFontSize = fontStyleData.fontSize || currentSubtitleConfig.value.fontSize;

// 获取当前预览窗口尺寸，计算反向缩放比例
if (fontStyleData.fontSize && previewEditorRef.value) {
    try {
        const previewWindowWidth = previewEditorRef.value.previewWindowWidth;
        const previewWindowHeight = previewEditorRef.value.previewWindowHeight;
        
        if (previewWindowWidth && previewWindowHeight && previewWindowHeight > 0) {
            const standardHeight = 1920;
            const scaleY = standardHeight / previewWindowHeight;
            
            // 反向缩放：页面字体大小 = 标准字体大小 ÷ scaleY
            pageFontSize = Math.round(fontStyleData.fontSize / scaleY);
            
            console.log('🎯 commonJson字体大小反向缩放转换:', {
                标准字体大小: fontStyleData.fontSize,
                页面尺寸: `${previewWindowWidth}×${previewWindowHeight}`,
                标准尺寸: `1080×${standardHeight}`,
                Y轴缩放比例: scaleY.toFixed(4),
                转换后页面字体大小: pageFontSize
            });
        }
    } catch (error) {
        console.warn('⚠️ commonJson字体大小反向缩放转换失败，使用原始值:', error);
    }
}

// 🎯 从commonJson.fontStyle恢复完整的字体配置
const fontStyleConfig = {
    fontFamily: fontStyleData.fontFamily || currentSubtitleConfig.value.fontFamily,
    fontName: fontStyleData.fontName || currentSubtitleConfig.value.fontName,
    fontUrl: fontStyleData.fontUrl || currentSubtitleConfig.value.fontUrl,
    fontSize: pageFontSize,                                                 // 🎯 使用反向缩放后的字体大小
    textColor: fontStyleData.textColor || currentSubtitleConfig.value.textColor,
    borderColor: fontStyleData.borderColor || currentSubtitleConfig.value.borderColor,
    borderWidth: fontStyleData.borderWidth || currentSubtitleConfig.value.borderWidth
};
```

### 3. 默认字体大小配置更新

#### DigitalHumanEditorPage.vue
- **第609行**：`currentSubtitleConfig`初始默认值 `fontSize: 30` → `fontSize: 18`
- **第1048行**：清空数据时的重置默认值 `fontSize: 30` → `fontSize: 18`

#### PreviewEditor.vue
- **第341行**：Props默认值 `fontSize: 30` → `fontSize: 18`

## 缩放计算公式

### 正向缩放（发送接口时）
```javascript
// 计算缩放比例
const scaleX = 1080 / pageWidth;
const scaleY = 1920 / pageHeight;

// 应用缩放
标准字体大小 = 页面字体大小 × scaleY;
标准宽度 = 页面宽度 × scaleX;
标准高度 = 页面高度 × scaleY;
```

### 反向缩放（数据回显时）
```javascript
// 计算缩放比例
const scaleY = 1920 / pageHeight;

// 应用反向缩放
页面字体大小 = 标准字体大小 ÷ scaleY;
```

## 缩放示例

### 9:16模式
- **页面尺寸**：403×700
- **标准尺寸**：1080×1920
- **缩放比例**：X轴 ≈ 2.68，Y轴 ≈ 2.74

**正向缩放示例**：
- 页面字体大小18 → 标准字体大小49
- 页面宽度380 → 标准宽度1018
- 页面高度80 → 标准高度219

**反向缩放示例**：
- 标准字体大小49 → 页面字体大小18

### 16:9模式
- **页面尺寸**：901.333×507
- **标准尺寸**：1080×1920
- **缩放比例**：X轴 ≈ 1.20，Y轴 ≈ 3.79

**正向缩放示例**：
- 页面字体大小18 → 标准字体大小68
- 页面宽度380 → 标准宽度456
- 页面高度80 → 标准高度303

**反向缩放示例**：
- 标准字体大小68 → 页面字体大小18

## 数据流向

### 完整的数据流向图
```
保存时（正向缩放）：
页面坐标系(403×700) → 缩放转换 → 标准坐标系(1080×1920) → 接口

回显时（反向缩放）：
接口 → 标准坐标系(1080×1920) → 反向缩放转换 → 页面坐标系(403×700)
```

### 处理的数据类型
1. **字体大小**：fontSize / font_size
2. **字幕宽度**：width
3. **字幕高度**：height
4. **位置坐标**：x, y（通过现有的坐标转换方法处理）

## 功能特点

1. **双向转换**：支持正向缩放（保存时）和反向缩放（回显时）
2. **多数据源支持**：处理subtitleConfigJson和commonJson.fontStyle两种数据源
3. **精确计算**：使用Math.round确保像素级精度
4. **错误处理**：包含完整的异常处理和降级机制
5. **调试支持**：提供详细的控制台日志输出
6. **一致性保证**：确保前端显示与视频生成效果完全一致

## 测试验证

### 验证方法
1. 在不同宽高比模式下设置字幕样式
2. 保存并重新加载作品
3. 检查前端显示效果是否与保存前一致
4. 生成视频并验证字幕大小是否正确

### 预期结果
- 前端页面显示效果保持不变
- 生成的视频中字幕大小与前端预览完全一致
- 数据回显时字幕样式完全恢复到保存时的状态
