# 字幕实时更新问题解决方案

## 问题描述

在数字人编辑器中，当音频播放时，控制台显示字幕数据在正确更新，但界面上的字幕文字内容没有跟着实时变化。用户希望字幕文字能够随着播放进度实时更新，就像视频播放器的字幕一样。

## 问题分析

### 原始问题
1. **Store计算正确**：`digitalHumanStore.js` 中的 `activeSubtitle` getter 能够正确计算当前时间对应的字幕文本
2. **控制台输出正常**：调试信息显示字幕数据在正确匹配和更新
3. **界面不更新**：Vue组件中的 `subtitleText` 计算属性没有响应 `activeSubtitle` 的变化

### 根本原因
Vue的响应式系统在某些情况下可能无法正确检测到Pinia store中getter的变化，特别是当getter依赖于多个状态变量时。

## 解决方案

### 方案一：直接计算字幕（推荐）
在组件的计算属性中直接进行字幕匹配计算，而不是依赖store的getter：

```javascript
// 文本字幕内容和样式 - 直接从store计算字幕
const subtitleText = computed(() => {
    // 直接访问store状态，确保响应式依赖
    const currentTimeValue = currentTime.value;
    const subtitleDataArray = store.subtitleData;
    const isLoaded = isSubtitleLoaded.value;
    const manualSubtitle = store.currentSubtitle;

    // 如果没有加载字幕数据，返回手动设置的字幕文本
    if (!isLoaded || subtitleDataArray.length === 0) {
        return manualSubtitle || '右侧区域编辑文本字幕';
    }

    // 查找当前时间范围内的字幕（直接在computed中计算）
    const currentSubtitleItem = subtitleDataArray.find(
        item => currentTimeValue >= item.startTime && currentTimeValue < item.endTime
    );

    if (currentSubtitleItem) {
        // 清理文本格式
        const cleanText = currentSubtitleItem.text
            .replace(/[\r\n]+/g, ' ')  // 替换换行符为空格
            .replace(/\s+/g, ' ')      // 合并多个空格为一个
            .trim();                   // 移除首尾空白

        return cleanText;
    }

    return '';
});
```

### 方案二：强制响应式更新（备选）
如果需要保持使用store的getter，可以添加强制更新机制：

```javascript
// 强制响应式更新 - 监听currentTime变化
watch(currentTime, (newTime) => {
    if (newTime > 0 && isSubtitleLoaded.value) {
        nextTick(() => {
            const currentActiveSubtitle = activeSubtitle.value;
            console.log(`强制字幕更新检查: 时间=${newTime.toFixed(2)}s, 字幕="${currentActiveSubtitle}"`);
        });
    }
}, { immediate: false });
```

## 实施步骤

### 1. 修改PreviewEditor.vue
文件路径：`src/views/modules/digitalHuman/components/PreviewEditor.vue`

- 导入nextTick（如果使用方案二）
- 修改subtitleText计算属性使用直接计算方式
- 添加调试信息确保更新正常

### 2. 测试验证
1. 启动开发服务器：`npm run dev`
2. 进入数字人编辑器页面
3. 加载音频和字幕数据
4. 播放音频，观察字幕是否实时更新
5. 检查控制台调试信息

## 技术要点

### 响应式依赖
- 确保计算属性直接依赖需要监听的响应式状态
- 避免通过多层getter传递响应式依赖
- 使用`storeToRefs`正确解构store状态

### 调试技巧
- 在计算属性中添加console.log确认触发频率
- 监听多个相关状态的变化
- 使用Vue DevTools检查响应式依赖关系

### 性能考虑
- 直接计算避免了额外的getter调用开销
- 减少了响应式系统的复杂度
- 保持了代码的可读性和维护性

## 预期效果

修改完成后，字幕文字应该能够：
1. 随着音频播放进度实时更新
2. 在正确的时间点显示对应的字幕文本
3. 在没有字幕的时间段显示空白
4. 保持良好的性能和响应速度

## 相关文件

- `src/views/modules/digitalHuman/components/PreviewEditor.vue` - 主要修改文件
- `src/views/modules/digitalHuman/store/digitalHumanStore.js` - 字幕数据管理
- `docs/字幕开关功能实现.md` - 相关功能文档
