# 字幕开关功能实现文档

## 功能概述

实现了使用 `digital_human_right_option` 对象中的 `open_captions` 字幕开关来控制数字人编辑器中字幕显示/隐藏的功能。

**✨ 新增实时控制功能：**
- 用户切换字幕开关时立即生效，无需点击"保存并生成音频"按钮
- 支持文本输入模式和音频驱动模式的实时字幕开关
- 提供即时的视觉反馈，提升用户体验

**🎵 新增音频和字幕数据加载功能：**
- 自动接收并处理 `digital_human_right_option` 传递的音频和字幕数据
- 将音频文件URL (`audio_file`) 加载到HTML5 Audio元素并同步播放
- 将字幕数据 (`subtitle_json`) 转换并加载到时间轴事件
- 支持音频时长自动设置和字幕时间轴同步
- 播放按钮现在可以真正播放传过来的音频URL ⚡

## 实现方案

采用 Vue 组件间通信的 emit 事件机制，建立从右侧操作面板到预览编辑器的字幕开关控制链路。

## 数据流向

### 实时字幕开关流程（新增）
```
字幕组件 (watch open_captions)
    ↓ digital_human_right_option({realtime: true})
右侧操作面板 (处理实时事件)
    ↓ emit('subtitle-toggle')
主页面组件 (handleSubtitleToggle)
    ↓ :subtitleVisible prop
预览编辑器 (showSubtitle computed)
    ↓ v-if="showSubtitle"
字幕立即显示/隐藏 ⚡
```

### 保存生成音频流程（增强）
```
保存按钮点击
    ↓ digital_human_right_option({type: 'text_captions', aduio_data: {...}})
右侧操作面板 (处理音频和字幕数据)
    ↓ emit('subtitle-toggle') + emit('audio-data-loaded')
主页面组件 (handleSubtitleToggle + handleAudioDataLoaded)
    ↓ 更新字幕显示状态 + 加载音频和字幕到store
数字人状态store (setTtsAudioUrl + setSubtitleData)
    ↓ 创建时间轴事件 + 设置音频时长
时间轴组件 (显示音频轨道和字幕事件) 🎵
    ↓ :subtitleVisible prop
预览编辑器 (showSubtitle computed)
    ↓ v-if="showSubtitle"
字幕显示/隐藏 + 音频轨道显示
```

## 核心代码修改

### 1. 字幕组件实时监听 (`src/views/modules/digitalHuman/components/right_operate/input_text/captions.vue`)

```javascript
// 注入父组件提供的字幕开关处理函数
let digital_human_right_option = inject('digital_human_right_option');

// 监听字幕开关状态变化，实时控制字幕显示
watch(open_captions, (newValue) => {
  console.log('字幕开关状态变化:', newValue);

  // 实时调用 digital_human_right_option 传递字幕开关状态
  if (digital_human_right_option) {
    digital_human_right_option({
      type: 'text_captions_toggle',  // 标识这是实时字幕开关事件
      open_captions: newValue,       // 字幕开关状态
      realtime: true                 // 标识这是实时更新
    });
  }
}, { immediate: false }); // 不立即执行，只在用户操作时触发
```

### 2. 音频上传组件实时监听 (`src/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio.vue`)

```javascript
// 注入父组件提供的字幕开关处理函数
let digital_human_right_option = inject('digital_human_right_option');

// 监听字幕开关状态变化，实时控制字幕显示（音频驱动模式）
watch(open_captions, (newValue) => {
  console.log('音频模式字幕开关状态变化:', newValue);

  // 实时调用 digital_human_right_option 传递字幕开关状态
  if (digital_human_right_option) {
    digital_human_right_option({
      type: 'audio_captions_toggle',  // 标识这是音频模式实时字幕开关事件
      open_captions: newValue,        // 字幕开关状态
      realtime: true                  // 标识这是实时更新
    });
  }
}, { immediate: false }); // 不立即执行，只在用户操作时触发
```

### 3. 右侧操作面板 (`src/views/modules/digitalHuman/components/right_operate/index.vue`)

**🎵 新增音频数据处理功能：**

```javascript
// 定义事件发射器 - 新增音频数据加载事件
const emit = defineEmits(['subtitle-toggle', 'audio-data-loaded']);

let digital_human_right_option = (data) => {
  console.log('父组件收到数据:', data);

  // 处理实时字幕开关事件（文本模式和音频模式）
  if (data.realtime && (data.type === 'text_captions_toggle' || data.type === 'audio_captions_toggle')) {
    emit('subtitle-toggle', data.open_captions);
    console.log('实时字幕开关状态:', data.open_captions, '模式:', data.type);
    return; // 实时事件直接返回，不执行后续逻辑
  }

  // 处理保存生成音频时的完整数据
  if (data.type === 'text_captions' && data.aduio_data) {
    // 发射字幕开关状态变更事件
    if (data.open_captions !== undefined) {
      emit('subtitle-toggle', data.open_captions);
      console.log('字幕开关状态:', data.open_captions);
    }

    // 🎵 发射音频和字幕数据加载事件
    emit('audio-data-loaded', {
      audioUrl: data.aduio_data.audio_file,           // 音频文件URL
      subtitleData: data.aduio_data.subtitle_json,    // 字幕JSON数组
      subtitleFile: data.aduio_data.subtitle_file,    // 字幕文件URL
      srtContent: data.aduio_data.srt,                // SRT字幕内容
      audioLength: data.aduio_data.extra_info?.audio_length, // 音频时长（毫秒）
      openCaptions: data.open_captions,               // 字幕开关状态
      chooseMusic: data.choose_music                  // 背景音乐数据
    });

    console.log('音频和字幕数据已加载:', {
      audioUrl: data.aduio_data.audio_file,
      subtitleCount: data.aduio_data.subtitle_json?.length || 0,
      audioLength: data.aduio_data.extra_info?.audio_length
    });
  }

  // 兼容处理其他情况的字幕开关
  else if (data.open_captions !== undefined) {
    emit('subtitle-toggle', data.open_captions);
    console.log('字幕开关状态:', data.open_captions);
  }
};
```

### 4. 主页面组件 (`src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`)

**🎵 新增音频数据处理功能：**

```javascript
// 导入数字人状态管理store
import { useDigitalHumanStore } from './store/digitalHumanStore';

// 初始化数字人状态管理store
const digitalHumanStore = useDigitalHumanStore();

// 字幕显示开关状态
const isSubtitleVisible = ref(true);

// 处理字幕开关切换事件
const handleSubtitleToggle = (isVisible) => {
  try {
    isSubtitleVisible.value = isVisible;
    console.log(`✅ 字幕显示状态更新：${isVisible ? '显示' : '隐藏'}`);
  } catch (error) {
    console.error('❌ 字幕开关处理失败:', error);
  }
};

// 🎵 处理音频和字幕数据加载事件
const handleAudioDataLoaded = (audioData) => {
  try {
    console.log('🎵 接收到音频和字幕数据:', audioData);

    // 更新字幕显示状态
    if (audioData.openCaptions !== undefined) {
      isSubtitleVisible.value = audioData.openCaptions;
    }

    // 设置音频URL到store
    if (audioData.audioUrl) {
      digitalHumanStore.setTtsAudioUrl(audioData.audioUrl);
      console.log('✅ 音频URL已设置:', audioData.audioUrl);
    }

    // 设置字幕文件URL到store
    if (audioData.subtitleFile) {
      digitalHumanStore.setSubtitleUrl(audioData.subtitleFile);
      console.log('✅ 字幕文件URL已设置:', audioData.subtitleFile);
    }

    // 处理字幕JSON数据并加载到store和时间轴
    if (audioData.subtitleData && Array.isArray(audioData.subtitleData)) {
      console.log('🔍 原始字幕数据结构:', audioData.subtitleData);

      // 转换字幕数据格式以匹配store期望的格式
      const formattedSubtitleData = audioData.subtitleData.map((item, index) => {
        const text = item.text || item.content || item.subtitle || `字幕片段${index + 1}`;
        const startTime = item.start_time || item.startTime || item.start || 0;
        const endTime = item.end_time || item.endTime || item.end || startTime + 1;

        return { text, startTime, endTime };
      });

      // 设置字幕数据到store（这会自动创建时间轴事件）
      digitalHumanStore.setSubtitleData(formattedSubtitleData);
      console.log('✅ 字幕数据已加载到时间轴:', formattedSubtitleData.length, '条字幕');
    }

    // 如果有音频时长信息，更新总时长
    if (audioData.audioLength) {
      const durationInSeconds = Math.ceil(audioData.audioLength / 1000);
      digitalHumanStore.totalDuration = durationInSeconds;
      console.log('✅ 音频总时长已设置:', durationInSeconds, '秒');
    }

    console.log('✅ 音频和字幕数据处理完成');
  } catch (error) {
    console.error('❌ 音频数据处理失败:', error);
  }
};
```

**模板更新：**
```html
<RightOperate @subtitle-toggle="handleSubtitleToggle" @audio-data-loaded="handleAudioDataLoaded" />
```

```vue
<RightOperate @subtitle-toggle="handleSubtitleToggle" />
<PreviewEditor :subtitleVisible="isSubtitleVisible" />
```

### 5. 预览编辑器 (`src/views/modules/digitalHuman/components/PreviewEditor.vue`)

```javascript
const props = defineProps({
  // ... 其他 props
  subtitleVisible: {
    type: Boolean,
    default: true  // 默认显示字幕
  }
});

// 字幕显示/隐藏控制 - 使用父组件传入的状态
const showSubtitle = computed(() => props.subtitleVisible);
```

## 使用方式

### 🚀 实时字幕开关（推荐）
1. 用户在右侧操作面板切换字幕开关
2. 字幕立即在预览编辑器中显示或隐藏 ⚡
3. 无需点击任何保存按钮，即时生效
4. 支持文本输入模式和音频驱动模式

### 📝 保存生成音频时的字幕开关（原有功能）
1. 用户在右侧操作面板设置字幕开关
2. 点击"保存并生成音频"按钮
3. 字幕开关状态通过 `digital_human_right_option` 函数传递
4. 系统更新预览编辑器中的字幕显示状态

## 字幕开关数据结构

### 实时字幕开关数据结构（新增）
```javascript
// 文本模式实时字幕开关
digital_human_right_option({
  type: 'text_captions_toggle',    // 类型：文本模式实时字幕开关
  open_captions: true/false,       // 字幕开关：true显示，false隐藏
  realtime: true                   // 标识：实时更新
})

// 音频模式实时字幕开关
digital_human_right_option({
  type: 'audio_captions_toggle',   // 类型：音频模式实时字幕开关
  open_captions: true/false,       // 字幕开关：true显示，false隐藏
  realtime: true                   // 标识：实时更新
})
```

### 保存生成音频数据结构（增强）
```javascript
digital_human_right_option({
  type: 'text_captions',           // 类型：文本字幕
  aduio_data: {                    // 🎵 音频数据对象
    audio_file: "https://...",     // 音频文件URL（用于时间轴轨道）
    subtitle_json: [...],          // 字幕JSON数组（用于时间轴事件）
    subtitle_file: "https://...",  // 字幕文件URL
    srt: "1\n00:00:00,000...",     // SRT字幕内容
    extra_info: {
      audio_length: 23436,         // 音频时长（毫秒）
      audio_sample_rate: 32000,    // 音频采样率
      audio_size: 376513,          // 音频文件大小
      bitrate: 128000,             // 比特率
      word_count: 140              // 字数统计
    }
  },
  open_captions: true/false,       // 字幕开关：true显示，false隐藏
  choose_music: musicData          // 背景音乐数据
})
```

## 测试验证

### 🔥 实时字幕开关测试（重点）
1. 打开数字人编辑器页面
2. 在右侧操作面板找到字幕开关（文本输入模式或音频驱动模式）
3. **直接切换字幕开关状态**
4. **立即观察预览编辑器中字幕的显示/隐藏效果** ⚡
5. 检查浏览器控制台的实时日志输出
6. 测试两种模式：
   - 文本输入模式的字幕开关
   - 音频驱动模式的字幕开关

### 📝 保存生成音频测试（原有功能）
1. 设置字幕开关状态
2. 点击"保存并生成音频"按钮
3. 观察字幕显示状态变化
4. 检查控制台日志

## 注意事项

- 字幕开关默认状态为显示（true）
- **实时字幕开关无需点击保存按钮，立即生效** ⚡
- 开关状态变化会在控制台输出详细日志便于调试
- 使用响应式计算属性确保状态同步
- 错误处理机制保证功能稳定性
- 支持文本输入模式和音频驱动模式的实时字幕开关
- 实时事件和保存事件分别处理，互不干扰

## 相关文件

- `src/views/modules/digitalHuman/components/right_operate/index.vue` - 右侧操作面板主组件
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` - 主页面组件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue` - 预览编辑器组件
- `src/views/modules/digitalHuman/components/right_operate/input_text/captions.vue` - 文本模式字幕组件 ⭐
- `src/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio.vue` - 音频模式字幕组件 ⭐

**⭐ 标记的文件包含实时字幕开关的核心逻辑**
