# 字幕描边默认值修复功能

## 📋 问题描述

### 问题现象
用户在数字人编辑器中未显式设置字幕描边时，系统仍然会传递默认的描边参数给后端接口：
- `stroke_color: "#000000"`（黑色描边）
- `stroke_width: 12`（描边粗细12）

### 期望行为
用户未设置描边时，应该传递空值参数：
- `stroke_color: ""`（空字符串）
- `stroke_width: 0`（数字零）

### 问题根源
存在多套不一致的默认值定义，导致数据流中出现"幽灵"默认值：

1. **左侧操作面板的旧默认值**：
   ```javascript
   const borderColor = ref('#000000') // 黑色描边
   const setThickness = ref('7')      // 粗细为7
   ```

2. **主页面的正确默认值**：
   ```javascript
   borderColor: '',        // 空描边色
   borderWidth: 0          // 粗细为0
   ```

3. **数据回显时覆盖问题**：编辑旧作品时，从 `subtitleConfigJson` 中读取的历史数据会覆盖默认值。

## 🔧 解决方案

### 修改文件列表
1. `src/views/modules/digitalHuman/components/left_operate/index.vue`
2. `src/views/layout/components/headbar/components/action/index.vue`

### 修复步骤

#### 1. 统一左侧操作面板默认值
**文件**: `src/views/modules/digitalHuman/components/left_operate/index.vue`

**修改位置**: 第425-426行
```javascript
// 修改前
const borderColor = ref('#000000') //描边色
const setThickness = ref('7') //粗细，与PreviewEditor默认值保持一致

// 修改后
const borderColor = ref('') //描边色，默认为空（无描边）
const setThickness = ref(0) //粗细，默认为0（无描边）
```

**修改位置**: 第98行（UI控件限制）
```javascript
// 修改前
<el-input-number v-model="setThickness" :min="12" :max="100" ...>

// 修改后
<el-input-number v-model="setThickness" :min="0" :max="100" ...>
```

**修改位置**: 第511行（数据类型处理）
```javascript
// 修改前
borderWidth: setThickness.value

// 修改后
borderWidth: Number(setThickness.value) || 0
```

#### 2. 修复数据回显逻辑
**文件**: `src/views/modules/digitalHuman/components/left_operate/index.vue`

**修改位置**: 第483-486行
```javascript
// 修改前
setThickness.value = fontData.stroke_width
borderColor.value = fontData.stroke_color

// 修改后
// 🔧 修复：处理旧的默认描边值，转换为新的空值
setThickness.value = (fontData.stroke_width === 7 || fontData.stroke_width === 12) ? 0 : (fontData.stroke_width || 0)
// 🔧 修复：处理旧的默认描边色，转换为新的空值
borderColor.value = (fontData.stroke_color === '#000000') ? '' : (fontData.stroke_color || '')
```

#### 3. 接口传参双重保护
**文件**: `src/views/layout/components/headbar/components/action/index.vue`

**修改位置**: subtitleConfigJson 参数构建（第745-746行）
```javascript
// 修改前
stroke_color: editorData?.subtitleConfig?.borderColor || "",
stroke_width: editorData?.subtitleConfig?.borderWidth || 0,

// 修改后
stroke_color: (editorData?.subtitleConfig?.borderColor === '#000000') ? "" : (editorData?.subtitleConfig?.borderColor || ""),
stroke_width: (editorData?.subtitleConfig?.borderWidth === 7 || editorData?.subtitleConfig?.borderWidth === 12) ? 0 : (editorData?.subtitleConfig?.borderWidth || 0),
```

**修改位置**: fontStyleInfo 参数构建（第891-892行）
```javascript
// 修改前
borderColor: subtitleConfig.borderColor || '',
borderWidth: subtitleConfig.borderWidth || 0

// 修改后
borderColor: (subtitleConfig.borderColor === '#000000') ? '' : (subtitleConfig.borderColor || ''),
borderWidth: (subtitleConfig.borderWidth === 7 || subtitleConfig.borderWidth === 12) ? 0 : (subtitleConfig.borderWidth || 0)
```

## 📊 数据流保护机制

### 完整数据流
```
新建作品:
左侧面板默认值(空) → 主页面配置(空) → 接口传参(空) ✅

编辑旧作品:
数据回显(过滤旧默认值) → 左侧面板(空) → 主页面配置(空) → 接口传参(空) ✅

任何遗漏:
接口传参时最后过滤 → 最终传参(空) ✅
```

### 三层保护机制
1. **源头保护**: 左侧操作面板默认值统一为空
2. **回显保护**: 数据回显时过滤和转换旧的默认值
3. **传参保护**: 接口传参时最后一层过滤，确保万无一失

## 🎯 预期效果

### 新建作品
- 字幕默认无描边效果
- 传递给接口的参数：`stroke_color: ""`, `stroke_width: 0`

### 编辑旧作品
- 自动转换旧的默认描边值为空值
- 用户主动设置的描边效果保持不变
- 传递给接口的参数：用户设置值或空值

### 用户体验
- 未设置描边时：真正的无描边效果
- 设置描边时：正常显示用户选择的描边效果
- 数据一致性：避免"幽灵"默认值的出现

## 🔍 验证方法

1. **新建数字人作品**：检查字幕是否无描边效果
2. **编辑旧作品**：检查是否正确处理历史默认值
3. **接口传参**：检查生成视频时的参数是否为空值
4. **用户设置**：检查主动设置描边后是否正常工作

## 📝 注意事项

### 兼容性处理
- 保持对用户主动设置描边的完全支持
- 自动识别和转换多种旧默认值格式
- 确保新旧作品的一致性体验

### 扩展性考虑
- 过滤逻辑支持多种旧默认值（7, 12等）
- 可轻松添加新的过滤规则
- 保持代码的可维护性和可读性

## 🛠️ 技术实现细节

### 关键代码片段

#### 左侧面板初始化逻辑
```javascript
// onMounted 时发送初始配置
onMounted(() => {
    // 获取数字人列表
    getDigitalList(digitalCurrentPage.value, 100)
    // 获取背景图
    getBgList()
    // 获取字体列表
    getFontListData()
    // 🎨 发射初始字幕样式配置
    emitSubtitleStyleChange()
})
```

#### 数据类型转换处理
```javascript
const emitSubtitleStyleChange = () => {
    const eventData = {
        fontFamily: setFontStyle.value,
        fontName: fontName,
        fontUrl: fontUrl,
        fontSize: setFontSize.value,
        textColor: textColor.value,
        borderColor: borderColor.value,
        borderWidth: Number(setThickness.value) || 0  // 确保数字类型
    };
    emit('subtitle-style-change', eventData);
}
```

#### 旧默认值检测逻辑
```javascript
// 检测并转换旧的描边粗细默认值
const isOldDefaultWidth = (value) => {
    return value === 7 || value === 12;
};

// 检测并转换旧的描边色默认值
const isOldDefaultColor = (value) => {
    return value === '#000000';
};
```

### 数据结构说明

#### subtitleConfig 对象结构
```javascript
{
    fontFamily: '',        // 字体样式ID
    fontName: '微软雅黑',    // 字体名称
    fontSize: 18,          // 字体大小
    textColor: '#ffffff',  // 文字颜色
    borderColor: '',       // 描边颜色（空表示无描边）
    borderWidth: 0,        // 描边粗细（0表示无描边）
    fontUrl: ''           // 字体文件URL
}
```

#### subtitleConfigJson 接口参数结构
```javascript
{
    show: true,           // 字幕显示开关
    x: 100,              // X坐标位置
    y: 500,              // Y坐标位置
    width: 800,          // 字幕宽度
    height: 100,         // 字幕高度
    font_size: 24,       // 字体大小
    color: "#ffffff",    // 文字颜色
    stroke_color: "",    // 描边颜色（空表示无描边）
    stroke_width: 0,     // 描边粗细（0表示无描边）
    font_id: "font_001"  // 字体ID
}
```

## 🔄 版本兼容性

### 支持的旧默认值格式
- **描边粗细**: `7`, `12`, `"7"`, `"12"`
- **描边颜色**: `"#000000"`, `"#000"`

### 转换规则
```javascript
// 粗细转换
旧值: 7 或 12 → 新值: 0
旧值: 其他数值 → 新值: 保持原值

// 颜色转换
旧值: "#000000" → 新值: ""
旧值: 其他颜色 → 新值: 保持原值
```

## 📈 性能影响

### 计算复杂度
- **时间复杂度**: O(1) - 简单的值比较和转换
- **空间复杂度**: O(1) - 不增加额外存储开销

### 执行频率
- **初始化**: 组件挂载时执行一次
- **数据回显**: 编辑作品时执行一次
- **接口传参**: 生成视频时执行一次

### 性能优化
- 使用简单的值比较，避免复杂的正则表达式
- 在关键路径上进行最小化的数据处理
- 保持向后兼容性的同时确保高效执行

## 🧪 测试用例

### 测试场景1：新建作品
**操作步骤**：
1. 进入数字人编辑器
2. 不设置任何字幕描边
3. 点击"生成视频"

**预期结果**：
- 接口传参：`stroke_color: ""`, `stroke_width: 0`
- 字幕显示：无描边效果

### 测试场景2：编辑旧作品（包含旧默认值）
**操作步骤**：
1. 打开包含旧默认描边值的作品
2. 不修改字幕样式
3. 点击"生成视频"

**预期结果**：
- 左侧面板显示：描边色为空，粗细为0
- 接口传参：`stroke_color: ""`, `stroke_width: 0`
- 字幕显示：无描边效果

### 测试场景3：用户主动设置描边
**操作步骤**：
1. 进入数字人编辑器
2. 设置描边色为红色，粗细为5
3. 点击"生成视频"

**预期结果**：
- 接口传参：`stroke_color: "#FF0000"`, `stroke_width: 5`
- 字幕显示：红色描边，粗细5

### 测试场景4：数据回显验证
**操作步骤**：
1. 编辑包含用户自定义描边的作品
2. 检查左侧面板显示
3. 点击"生成视频"

**预期结果**：
- 左侧面板正确显示用户设置的描边值
- 接口传参保持用户设置的值
- 字幕显示与用户设置一致

## 🔧 故障排除

### 问题1：接口仍然传递旧默认值
**可能原因**：
- 浏览器缓存未清理
- 组件状态未正确更新
- 数据回显逻辑未生效

**解决方法**：
1. 清理浏览器缓存并刷新页面
2. 检查控制台是否有JavaScript错误
3. 验证左侧面板的默认值是否正确

### 问题2：用户设置的描边被清空
**可能原因**：
- 过滤逻辑过于严格
- 数据类型转换错误
- 事件触发时机问题

**解决方法**：
1. 检查过滤条件是否正确
2. 验证数据类型转换逻辑
3. 确认事件监听器正常工作

### 问题3：编辑旧作品时描边显示异常
**可能原因**：
- 数据回显逻辑未覆盖所有旧格式
- watch监听器未正确触发
- 组件初始化顺序问题

**解决方法**：
1. 添加更多旧默认值格式的支持
2. 检查watch监听器的配置
3. 调整组件初始化顺序

## 📋 维护指南

### 代码审查要点
1. **默认值一致性**：确保所有组件的默认值保持一致
2. **数据类型安全**：验证数值类型转换的正确性
3. **向后兼容性**：测试对旧数据格式的支持
4. **性能影响**：评估修改对系统性能的影响

### 未来扩展建议
1. **配置化过滤规则**：将过滤逻辑抽取为可配置的规则
2. **数据迁移工具**：提供批量转换旧数据的工具
3. **监控和日志**：添加数据转换的监控和日志记录
4. **单元测试**：为关键逻辑添加自动化测试

### 相关文档
- [字幕坐标系缩放转换功能.md](./字幕坐标系缩放转换功能.md)
- [getDigitalWork接口数据存储和获取指南.md](./getDigitalWork接口数据存储和获取指南.md)
- [commonJson添加第二层数字人图片URL功能.md](./commonJson添加第二层数字人图片URL功能.md)
