# 字幕文字位置调整记录

## 📝 调整概述

**日期**: 2024-12-19  
**目标**: 调整数字人编辑器中字幕文字的垂直位置，让文字在字幕框内向下偏移  
**文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`

## 🎯 具体修改

### 修改位置
- **文件**: `PreviewEditor.vue`
- **函数**: `subtitleContentStyle` 计算属性
- **行数**: 1782-1787

### 修改内容

#### 修改前
```javascript
// 使用flex布局实现完整的水平和垂直居中
display: 'flex',
alignItems: 'center',          // 垂直居中
justifyContent: 'center',      // 水平居中
flexDirection: 'row',          // 水平方向排列，确保单行文本居中
```

#### 修改后
```javascript
// 使用flex布局实现水平居中，文字向下偏移
display: 'flex',
alignItems: 'flex-start',      // 顶部对齐，配合padding实现向下偏移
justifyContent: 'center',      // 水平居中
flexDirection: 'row',          // 水平方向排列，确保单行文本居中
paddingTop: '8px',             // 文字向下偏移8像素
```

## 🔧 技术实现说明

### 调整方案
- **方法**: 使用 `alignItems: 'flex-start'` + `paddingTop: '8px'`
- **原理**: 将垂直对齐从居中改为顶部对齐，然后通过顶部内边距实现向下偏移
- **偏移量**: 8像素

### 优势
1. **保持水平居中**: `justifyContent: 'center'` 确保文字水平居中
2. **精确控制**: 通过 `paddingTop` 可以精确控制向下偏移的像素数
3. **响应式兼容**: 在不同屏幕比例（16:9、9:16）下都有一致的效果
4. **功能完整**: 不影响字体、颜色、边框等其他字幕功能

## 📱 适配说明

### 不同比例下的表现
- **16:9 比例**: 文字向下偏移8px，保持水平居中
- **9:16 比例**: 文字向下偏移8px，保持水平居中
- **其他比例**: 文字向下偏移8px，保持水平居中

### 兼容性
- ✅ 字体动态加载功能正常
- ✅ 字体颜色设置正常
- ✅ 文字描边效果正常
- ✅ 字幕拖拽和缩放功能正常
- ✅ 字幕显示/隐藏切换正常

## 🧪 测试验证

### 测试步骤
1. 打开数字人编辑器
2. 添加字幕文本
3. 观察字幕文字在字幕框内的位置
4. 切换不同的屏幕比例（16:9 ↔ 9:16）
5. 验证文字位置是否向下偏移约8像素

### 预期效果
- 字幕文字应该在字幕框内稍微向下偏移
- 文字保持水平居中对齐
- 在不同屏幕比例下效果一致
- 其他字幕功能不受影响

## 📋 相关文件

- **主要文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`
- **相关组件**: `src/views/modules/digitalHuman/components/left_operate/index.vue`
- **样式影响**: 仅影响字幕文字在容器内的垂直位置

## 🔄 回滚方案

如需回滚此修改，可以将代码恢复为：
```javascript
alignItems: 'center',          // 恢复垂直居中
// 移除 paddingTop: '8px',     // 删除这行
```

## 📝 备注

- 此调整仅影响字幕文字在字幕框内的位置
- 不影响字幕框本身的位置
- 偏移量可根据需要调整（修改 `paddingTop` 的值）
- 建议在实际使用中测试不同长度的字幕文本效果
