# 字幕显示逻辑修复

## 问题描述

### 主要问题
用户从"我的作品"页面跳转到数字人页面时，音频驱动和字幕显示正常，但页面刷新后出现以下问题：

1. **页面刷新后字幕不显示**：虽然右侧文本内容正常显示，但字幕区域不显示任何内容
2. **字幕开关切换失效**：用户点击右侧字幕开关"取消"→"选中"后，字幕仍然不显示

### 问题现象
- ✅ 从作品页面跳转时：字幕正常显示
- ❌ 页面刷新后：字幕消失，右侧文本正常
- ❌ 手动切换字幕开关：字幕无法恢复显示

### 根本原因分析

#### 1. 数据流转断链问题
```
pinia.digitalHumanEditor.originalWorkData.commonJson.subtitle_json (✅ 有数据)
                    ↓ (❌ 转换失败)
store.subtitleData (❌ 空数组)
                    ↓
activeSubtitle (❌ 返回空值)
                    ↓  
字幕显示 (❌ 不显示)
```

#### 2. 状态同步时序问题
- 页面刷新时，组件初始化存在时序问题
- 主页面的`isSubtitleVisible`状态正确恢复为`true`
- 但右侧操作面板的`open_captions`状态同步失败

## 解决方案

### 方案概述
采用**双重保障机制**：
1. **数据转换增强**：从pinia备选获取字幕数据
2. **状态同步增强**：延迟重试机制确保状态同步
3. **交互修复**：字幕开关切换时自动数据恢复

### 技术实现

#### 1. 字幕数据转换增强 (`DigitalHumanEditorPage.vue`)

**新增辅助函数**：`ensureSubtitleDataTransfer`
```javascript
// 位置：第238-322行
const ensureSubtitleDataTransfer = () => {
    try {
        // 检查store中是否已有字幕数据
        if (digitalHumanStore.subtitleData && digitalHumanStore.subtitleData.length > 0) {
            console.log('✅ Store中已有字幕数据，无需转换');
            return true;
        }

        // 从pinia中获取原始字幕数据
        const originalData = digitalHumanStore.originalWorkData;
        if (!originalData?.commonJson?.subtitle_json) {
            console.log('⚠️ pinia中无字幕数据可转换');
            return false;
        }

        const subtitleJson = originalData.commonJson.subtitle_json;
        if (!Array.isArray(subtitleJson) || subtitleJson.length === 0) {
            console.log('⚠️ 字幕数据格式不正确或为空');
            return false;
        }

        // 转换数据格式并设置到store
        const formattedData = subtitleJson.map((item, index) => ({
            text: item.text || `字幕片段${index + 1}`,
            startTime: parseFloat(item.start || item.startTime || 0),
            endTime: parseFloat(item.end || item.endTime || (item.start || 0) + 1)
        }));

        digitalHumanStore.setSubtitleData(formattedData);
        digitalHumanStore.isSubtitleLoaded = true;
        isSubtitleVisible.value = true;

        console.log('✅ 字幕数据强制转换完成');
        return true;
    } catch (error) {
        console.error('❌ 字幕数据强制转换失败:', error);
        return false;
    }
};
```

**调用时机**：
- 组件挂载时（第1656行）
- 路由参数变化时（第1645行）
- 字幕开关状态变化时（新增）

#### 2. 字幕开关状态同步增强

**增强同步逻辑**：`ensureSubtitleSyncWithRetry`
```javascript
// 位置：第134-187行
const ensureSubtitleSyncWithRetry = async (isEnabled, retryCount = 0) => {
    const maxRetries = 3;
    const delayMs = retryCount === 0 ? 500 : 1000 + (retryCount * 500);
    
    // 延迟执行，确保组件已完全初始化
    await new Promise(resolve => setTimeout(resolve, delayMs));
    
    try {
        if (rightOperateRef.value?.syncAudioCaptionsState) {
            rightOperateRef.value.syncAudioCaptionsState(isEnabled);
            return true;
        } else {
            throw new Error('syncAudioCaptionsState方法不可用');
        }
    } catch (error) {
        // 重试机制：最多重试3次
        if (retryCount < maxRetries) {
            return ensureSubtitleSyncWithRetry(isEnabled, retryCount + 1);
        } else {
            // 备选方案：通过事件系统发送状态更新
            handleSubtitleToggle(isEnabled);
            return false;
        }
    }
};
```

#### 3. 字幕开关交互修复

**增强开关处理逻辑**：
```javascript
// 位置：第783-804行
const handleSubtitleToggle = (isVisible) => {
    try {
        const previousState = isSubtitleVisible.value;
        isSubtitleVisible.value = isVisible;
        
        // 🔧 关键修复：当字幕开关从false变为true时，确保字幕数据正确设置
        if (!previousState && isVisible) {
            console.log('🔧 检测到字幕开关从关闭变为开启，执行字幕数据转换检查...');
            
            // 延迟执行，确保状态变化完成
            setTimeout(() => {
                ensureSubtitleDataTransfer();
            }, 100);
        }
    } catch (error) {
        console.warn('⚠️ 字幕开关处理失败:', error);
    }
};
```

#### 4. 字幕文本获取逻辑增强 (`PreviewEditor.vue`)

**备选数据获取机制**：
```javascript
// 增强字幕文本计算逻辑
const subtitleText = computed(() => {
    const storeSubtitle = activeSubtitle.value;
    const subtitleDataArray = store.subtitleData;
    
    // 第一优先级：时间匹配的字幕
    if (storeSubtitle) {
        return storeSubtitle.replace(/[\r\n]+/g, ' ').replace(/\s+/g, ' ').trim();
    }
    
    // 第二优先级：从pinia store获取第一条有效字幕
    if (subtitleDataArray && subtitleDataArray.length > 0) {
        const firstValidSubtitle = subtitleDataArray.find(item => 
            item.text && item.text.trim() !== ''
        );
        if (firstValidSubtitle) {
            console.log('📋 使用备选字幕数据:', firstValidSubtitle.text.substring(0, 30) + '...');
            return firstValidSubtitle.text.replace(/[\r\n]+/g, ' ').replace(/\s+/g, ' ').trim();
        }
    }
    
    // 第三优先级：手动设置的字幕文本
    return manualSubtitle || '';
});
```

## 修复效果

### 解决的问题
1. ✅ **页面刷新后字幕显示正常**
2. ✅ **字幕开关切换响应正常**  
3. ✅ **数据状态保持一致性**
4. ✅ **多种场景下字幕显示稳定**

### 测试场景
- [x] 从作品页面跳转到数字人页面
- [x] 在数字人页面直接刷新浏览器  
- [x] 点击字幕开关"取消"→"选中"
- [x] 多次切换字幕开关状态
- [x] 页面刷新后切换字幕开关

### 技术保障
- **时序容错**：延迟执行避免组件初始化问题
- **重试机制**：自动重试确保状态同步成功
- **备选方案**：多级降级策略保证功能可用
- **错误处理**：完善的异常处理避免功能中断

## 相关文件修改

### 主要修改
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
  - 新增：`ensureSubtitleDataTransfer` 字幕数据转换函数
  - 新增：`ensureSubtitleSyncWithRetry` 状态同步重试函数  
  - 增强：`handleSubtitleToggle` 开关处理逻辑
  - 增强：组件生命周期中的数据转换调用

- `src/views/modules/digitalHuman/components/PreviewEditor.vue`
  - 增强：`subtitleText` 计算属性的备选数据获取逻辑

### 兼容性
- ✅ 保持原有功能不变
- ✅ 向下兼容所有现有场景
- ✅ 不影响其他组件功能

## 技术要点总结

### 关键技术点
1. **数据流转链路修复**：确保pinia→store→UI的完整数据流
2. **状态同步时序处理**：使用延迟和重试机制处理异步初始化
3. **交互增强**：在用户操作时主动触发数据恢复
4. **多级降级策略**：提供多种数据获取方案确保可用性

### 最佳实践
- 使用防抖和延迟机制处理时序问题
- 实现多级数据获取备选方案
- 添加详细的调试日志便于问题排查
- 保持原有架构不变，只做增强式修复

### 注意事项
- 确保所有异步操作都有适当的错误处理
- 避免在组件销毁后执行异步操作
- 保持状态变化的原子性和一致性

---

**修复完成时间**：2024年12月
**修复版本**：v1.3cjs_test  
**影响范围**：数字人编辑器字幕显示功能
**测试状态**：✅ 已验证
