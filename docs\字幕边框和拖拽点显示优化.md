# 字幕边框和拖拽点显示优化

## 问题描述

用户反馈字幕的边框和拖拽圆点只有在鼠标点击后才会显示，默认状态下是隐藏的，这导致用户不容易发现字幕是可以拖拽和调整的。

![字幕显示问题](../screenshots/字幕边框显示问题.png)

## 问题分析

### 原有逻辑
- 字幕元素的选中边框和拖拽圆点只有在 `isSubtitleActive && !isPlaying` 条件下才显示
- `isSubtitleActive` 默认为 `false`，只有在以下情况下才变为 `true`：
  1. 用户点击字幕时立即选中
  2. 用户悬停字幕1秒后自动选中

### 用户体验问题
1. **可发现性差**：用户不知道字幕是可以操作的
2. **操作延迟**：需要悬停1秒才能看到操作界面
3. **学习成本高**：新用户不知道需要点击才能编辑

## 解决方案

### 修改策略
将字幕边框和拖拽点的显示条件从"仅选中时显示"改为"悬停或选中时显示"，提升用户体验和操作的可发现性。

### 核心修改

#### 1. 拖拽圆点显示条件修改
**文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`

**修改前**:
```vue
<div v-if="isSubtitleActive && !isPlaying" class="resize-handles">
```

**修改后**:
```vue
<div v-if="(isSubtitleActive || isSubtitleHovering) && !isPlaying" class="resize-handles">
```

#### 2. 边框样式显示条件修改
**文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`

**修改前**:
```vue
:class="{ active: isSubtitleActive && !isPlaying, hovering: isSubtitleHovering && !isPlaying, playing: isPlaying }"
```

**修改后**:
```vue
:class="{ active: (isSubtitleActive || isSubtitleHovering) && !isPlaying, hovering: isSubtitleHovering && !isPlaying && !isSubtitleActive, playing: isPlaying }"
```

### 改进效果

#### 用户体验提升
1. **即时反馈**：鼠标悬停时立即显示边框和拖拽点
2. **降低学习成本**：用户一眼就能看出字幕是可操作的
3. **保持简洁**：不操作时界面依然简洁，没有多余元素

#### 交互逻辑
1. **默认状态**：字幕显示，无边框和拖拽点
2. **悬停状态**：显示边框和拖拽点，提示可操作
3. **选中状态**：显示边框和拖拽点，可以拖拽和调整大小
4. **播放状态**：隐藏所有操作界面，专注播放效果

## 技术实现

### 状态管理
- `isSubtitleActive`: 字幕选中状态（点击或悬停1秒后选中）
- `isSubtitleHovering`: 字幕悬停状态（鼠标进入/离开时切换）
- `isPlaying`: 播放状态（播放时隐藏所有操作界面）

### CSS类名逻辑
- `active`: 悬停或选中时应用（显示边框和拖拽点）
- `hovering`: 仅悬停且未选中时应用（可添加额外的悬停效果）
- `playing`: 播放时应用（隐藏操作界面，专注内容）

## 兼容性说明

### 保持向后兼容
- 原有的点击选中逻辑保持不变
- 原有的1秒自动选中逻辑保持不变
- 原有的拖拽和调整大小功能完全兼容

### 渐进增强
- 在原有功能基础上增加悬停显示功能
- 不影响现有的任何操作逻辑
- 提升用户体验，降低操作难度

## 测试建议

### 功能测试
1. **悬停测试**：鼠标悬停字幕时应立即显示边框和拖拽点
2. **点击测试**：点击字幕时应选中并显示边框和拖拽点
3. **拖拽测试**：悬停状态下应可以直接拖拽字幕
4. **调整大小测试**：悬停状态下应可以直接调整字幕大小
5. **播放测试**：播放时应隐藏所有操作界面

### 兼容性测试
1. **原有功能**：确保所有原有字幕功能正常工作
2. **其他元素**：确保数字人和背景的操作逻辑不受影响
3. **状态切换**：确保各种状态切换正常，无异常行为

## 总结

通过将字幕边框和拖拽点的显示条件从"仅选中时"修改为"悬停或选中时"，显著提升了字幕操作的可发现性和用户体验。用户现在可以通过简单的悬停动作就发现字幕是可以操作的，大大降低了学习成本和操作难度。

这个改进保持了界面的简洁性（默认隐藏），同时提供了更好的交互反馈（悬停显示），是一个平衡用户体验和界面设计的优秀方案。 