# 字幕高度比例优化调整

## 修改概述
**日期：** 2024年最新  
**文件：** `src/views/modules/digitalHuman/components/PreviewEditor.vue`  
**功能：** 数字人编辑器字幕显示区域高度比例优化  

## 问题描述
在数字人编辑器中，不同宽高比模式下的字幕显示高度需要进一步优化，以提供更好的视觉效果和用户体验。

## 解决方案

### 修改内容
针对`getInitialSubtitleSize()`函数中的字幕高度比例进行了调整：

#### 16:9横屏模式优化
- **修改前：** 字幕高度占页面窗口高度的12% (≈ 61px)
- **修改后：** 字幕高度占页面窗口高度的14% (≈ 71px)
- **效果：** 横屏模式下字幕区域更加饱满，文字显示更清晰

#### 9:16竖屏模式优化  
- **修改前：** 字幕高度占页面窗口高度的12% (≈ 84px)
- **修改后：** 字幕高度占页面窗口高度的10% (≈ 70px)
- **效果：** 竖屏模式下字幕区域更加紧凑，为其他内容留出更多空间

### 代码变更

```javascript
// 16:9横屏模式
if (props.aspectRatio === '16:9') {
    // 字幕宽度约占页面窗口宽度的50%，高度约14%
    const pageWidth_subtitle = Math.round(pageWidth * 0.5);  // 901 * 0.5 ≈ 450
    const pageHeight_subtitle = Math.round(pageHeight * 0.14); // 507 * 0.14 ≈ 71
    
    return { width: pageWidth_subtitle, height: pageHeight_subtitle };
} else {
    // 9:16竖屏模式：字幕宽度约占页面窗口宽度的90%，高度约10%
    const pageWidth_subtitle = Math.round(pageWidth * 0.9);    // 403 * 0.9 ≈ 363
    const pageHeight_subtitle = Math.round(pageHeight * 0.1);  // 700 * 0.1 ≈ 70
    
    return { width: pageWidth_subtitle, height: pageHeight_subtitle };
}
```

## 技术细节

### 影响范围
1. **字幕初始尺寸计算** - `getInitialSubtitleSize()`函数
2. **字幕显示区域** - 前端页面坐标系下的字幕容器
3. **接口数据传输** - 通过坐标转换函数自动适配标准坐标系

### 坐标系统
- **页面坐标系：** 直接基于预览窗口尺寸的比例计算
- **标准坐标系：** 通过`pageToStandardCoord()`函数自动转换
- **宽高比适配：** 
  - 16:9模式：901×507页面窗口 → 1920×1080标准坐标
  - 9:16模式：403×700页面窗口 → 1080×1920标准坐标

### 兼容性
- ✅ 支持动态宽高比切换
- ✅ 支持字幕位置拖拽和缩放
- ✅ 支持数据回显和保存
- ✅ 保持与其他元素的层级关系

## 验证要点

### 功能验证
1. **16:9模式：** 字幕高度约71px，显示效果更饱满
2. **9:16模式：** 字幕高度约70px，显示效果更紧凑
3. **拖拽缩放：** 字幕拖拽和缩放功能正常
4. **数据保存：** 接口传输的标准坐标数据正确

### 视觉效果
- [x] 16:9模式字幕不会过小，文字清晰可读
- [x] 9:16模式字幕不会过大，不占用过多空间
- [x] 字幕居中显示效果良好
- [x] 与数字人图层的视觉平衡合理

## 相关文件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue` - 主要修改文件
- 字幕坐标转换相关函数：`pageToStandardCoord()`、`standardToPageCoord()`
- 字幕配置回显函数：`setSubtitleConfig()`

## 注意事项
1. 此修改仅影响字幕的**初始尺寸**，用户仍可通过拖拽手柄自由调整
2. 坐标转换逻辑自动处理不同坐标系间的转换，无需额外处理
3. 数据回显时会根据保存的标准坐标自动计算正确的页面显示尺寸
4. 如需进一步调整，建议以0.01-0.02为步长进行微调 