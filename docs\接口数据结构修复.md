# 接口数据结构修复文档

## 问题描述

在替换 `getDigiAudioJsonTxt` 接口后，发现右侧文本框显示为空。经过调试发现，接口返回的数据结构是 `content.result` 直接为数组，而不是之前预期的 `content.result.subtitle_json` 格式。

## 实际返回数据结构

```javascript
{
  status_code: 200,
  content: {
    result: [  // 直接是数组，不是对象
      {
        "id": 0,
        "seek": 0,
        "start": 0.0,
        "end": 6.140000000000001,
        "text": "这不过是新疆平常的一天,太阳从塔什库尔干的世界屋脊照常升起,",
        "tokens": [...],
        "temperature": 0.0,
        "avg_logprob": -0.08410485982894897,
        "compression_ratio": 1.3826714801444044,
        "no_speech_prob": 0.4322985112667084,
        "time_begin": 0.0,
        "time_end": 6140.000000000001
      }
      // ... 更多字幕对象
    ]
  }
}
```

## 解决方案

修改数据处理逻辑，添加对 `result` 直接为数组的支持，同时保持对其他格式的兼容性。

### 修改的文件
- `src/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio.vue`

### 修改内容

```javascript
// 处理字幕数据拼接
let fullText = '';
if (text.content && text.content.result) {
    // 优先使用 txt 字段
    if (text.content.result.txt) {
        fullText = text.content.result.txt;
        console.log('使用 txt 字段:', fullText.substring(0, 50) + '...');
    }
    // 从 result 数组中提取 text 字段并拼接（新的数据结构）
    else if (Array.isArray(text.content.result)) {
        const subtitleTexts = text.content.result
            .map(item => item.text || '') // 直接使用 text 字段
            .filter(text => text && text.trim() !== ''); // 过滤空文本

        fullText = subtitleTexts.join(''); // 直接连接，不加空格
        console.log('从 result 数组拼接文本:', {
            字幕条数: text.content.result.length,
            拼接后文本: fullText.substring(0, 50) + '...'
        });
    }
    // 从 subtitle_json 数组中提取 text 字段并拼接（兼容旧格式）
    else if (text.content.result.subtitle_json && Array.isArray(text.content.result.subtitle_json)) {
        const subtitleTexts = text.content.result.subtitle_json
            .map(item => item.text || '') // 直接使用 text 字段
            .filter(text => text && text.trim() !== ''); // 过滤空文本

        fullText = subtitleTexts.join(''); // 直接连接，不加空格
        console.log('从 subtitle_json 拼接文本:', {
            字幕条数: text.content.result.subtitle_json.length,
            拼接后文本: fullText.substring(0, 50) + '...'
        });
    }
}
```

## 支持的数据格式

现在代码支持以下三种数据格式，按优先级处理：

### 1. 直接文本格式（最高优先级）
```javascript
{
  content: {
    result: {
      txt: "完整的文本内容"
    }
  }
}
```

### 2. Result 数组格式（新增支持）
```javascript
{
  content: {
    result: [
      {
        "text": "字幕片段1",
        "start": 0.0,
        "end": 6.14
      },
      {
        "text": "字幕片段2", 
        "start": 6.14,
        "end": 12.5
      }
    ]
  }
}
```

### 3. Subtitle_json 格式（兼容旧格式）
```javascript
{
  content: {
    result: {
      subtitle_json: [
        {
          "text": "字幕片段1",
          "start": 0.0,
          "end": 6.14
        }
      ]
    }
  }
}
```

## 处理逻辑

1. **优先检查 `txt` 字段**：如果存在直接使用
2. **检查 `result` 是否为数组**：如果是，从每个对象的 `text` 字段提取并拼接
3. **检查 `subtitle_json` 数组**：兼容旧的数据格式
4. **过滤空文本**：确保输出质量
5. **直接连接**：不添加空格，保持原始文本连贯性

## 调试信息

添加了详细的控制台日志输出：
- 显示使用的数据源（txt字段、result数组、subtitle_json数组）
- 显示字幕条数
- 显示拼接后文本的前50个字符

## 测试结果

修复后，上传音频文件能够正确提取并显示字幕文本，解决了右侧文本框为空的问题。

## 注意事项

- 保持了对所有可能数据格式的兼容性
- 添加了详细的调试日志，便于问题排查
- 文本拼接不添加空格，保持原始语义连贯性
- 过滤空文本，确保输出质量
