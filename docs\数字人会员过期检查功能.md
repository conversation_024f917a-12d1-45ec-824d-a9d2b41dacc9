# 数字人会员过期检查功能

## 📋 概述

在数字人编辑器的生成视频功能中添加了会员过期检查，确保用户在生成视频前拥有有效的数字人会员资格。

## 🎯 功能需求

1. **检查时机**：在用户点击生成视频按钮时，首先进行会员过期验证
2. **数据来源**：从 Pinia store 中获取用户数字人会员信息
   - 路径：`store.user.digital_human.end_time`
   - 实际访问：`loginStore.memberInfo?.digital_human?.end_time`
3. **过期判断逻辑**：
   - 获取当前时间戳
   - 将 `end_time` 与当前时间进行比较
   - 如果 `end_time` 小于等于当前时间，则判定为已过期
4. **过期处理**：
   - 使用 Element Plus 的 Toast 组件显示提示信息："数字人会员已过期"
   - 阻止继续执行生成视频的操作
5. **未过期处理**：
   - 如果会员未过期，则正常执行生成视频的流程

## 🔧 实现方案

### 修改文件
- **位置**：`src/views/layout/components/headbar/components/action/index.vue`
- **方法**：`handleSaveDigitalWork()`

### 实现位置
在 `handleSaveDigitalWork()` 方法中，在所有其他检查之前添加会员过期检查逻辑。

### 核心实现代码

```javascript
// 🔒 数字人会员过期检查 - 在所有其他检查之前进行
console.log('🔒 开始进行数字人会员过期检查...');

try {
    // 从 Pinia store 中获取用户数字人会员信息
    const digitalHumanEndTime = loginStore.memberInfo?.digital_human?.end_time;
    
    if (digitalHumanEndTime) {
        // 获取当前时间戳
        const currentTime = Date.now();
        
        // 将 end_time 转换为时间戳进行比较
        // end_time 格式通常为 "2025-01-01 23:59:59"
        const endTimeTimestamp = new Date(digitalHumanEndTime).getTime();
        
        console.log('🔒 数字人会员信息检查:', {
            endTime: digitalHumanEndTime,
            endTimeTimestamp: endTimeTimestamp,
            currentTime: currentTime,
            isExpired: endTimeTimestamp <= currentTime
        });
        
        // 如果 end_time 小于等于当前时间，则判定为已过期
        if (endTimeTimestamp <= currentTime) {
            console.log('❌ 数字人会员已过期，禁止生成视频');
            ElMessage.error('数字人会员已过期');
            return; // 阻止继续执行生成视频的操作
        }
        
        console.log('✅ 数字人会员未过期，继续执行生成视频流程');
    } else {
        console.log('⚠️ 未获取到数字人会员信息，跳过会员过期检查');
    }
} catch (memberCheckError) {
    console.error('❌ 数字人会员过期检查失败:', memberCheckError);
    ElMessage.error('会员状态检查失败，请稍后重试');
    return; // 阻止继续执行
}
```

## 📍 代码插入位置

在 `handleSaveDigitalWork()` 方法中的具体位置：
1. **之前**：检查是否为数字人编辑器页面
2. **之后**：获取数字人编辑器数据

### 执行顺序
```javascript
handleSaveDigitalWork() {
    // 1. 检查是否为数字人编辑器页面
    // 2. 🆕 数字人会员过期检查 (新增)
    // 3. 获取数字人编辑器数据
    // 4. 构建保存参数
    // 5. 时长权限检查
    // 6. 音频URL验证
    // 7. 调用生成视频接口
}
```

## 🔒 会员过期检查实现

### 检查流程
1. **获取会员信息**：从 `loginStore.memberInfo?.digital_human?.end_time` 获取过期时间
2. **时间比较**：将过期时间转换为时间戳与当前时间比较
3. **结果处理**：
   - 过期：显示错误提示并阻止执行
   - 未过期：继续正常流程
   - 无会员信息：跳过检查（记录警告日志）

### 错误处理
- **会员过期**：显示提示"数字人会员已过期"
- **检查异常**：显示提示"会员状态检查失败，请稍后重试"
- **两种情况都会阻止视频生成流程继续执行**

## 🎨 技术特点

### 安全性
- 使用可选链操作符 `?.` 安全访问嵌套属性
- 完整的 try-catch 错误处理机制
- 在 finally 块中确保状态重置

### 用户体验
- 清晰的错误提示信息
- 及时的状态反馈
- 不会影响其他功能的正常使用

### 代码质量
- 详细的控制台日志便于调试
- 与现有代码风格保持一致
- 遵循现有的错误处理模式

## 📊 数据流程图

```
用户点击生成视频按钮
        ↓
handleGenerateVideoClick()
        ↓
handleSaveDigitalWork()
        ↓
检查是否为数字人编辑器页面
        ↓
🆕 数字人会员过期检查
        ↓
    会员已过期？
   ↙️        ↘️
 是          否
 ↓           ↓
显示错误     继续执行
提示并      后续流程
返回        (时长检查等)
```

## ✅ 测试建议

### 测试场景
1. **会员未过期**：验证正常生成视频流程
2. **会员已过期**：验证错误提示和流程阻止
3. **无会员信息**：验证跳过检查的处理
4. **异常情况**：验证错误处理机制

### 验证要点
- 错误提示信息是否正确显示
- 生成视频流程是否被正确阻止
- 加载状态是否正确重置
- 控制台日志是否完整记录

## 🔄 版本信息

- **创建时间**：2025-01-18
- **修改文件**：`src/views/layout/components/headbar/components/action/index.vue`
- **影响功能**：数字人生成视频
- **依赖组件**：Element Plus (ElMessage)、Pinia store (loginStore)
