# 数字人层删除不应影响字幕层问题修复记录

## 问题描述
用户反馈：在数字人编辑器中，当右键删除数字人层（第二层）时，字幕层也会意外消失。这是不正确的行为。

**期望行为**：删除特定层时，只有目标层应该被移除，其他层保持不变和可见。

**实际问题行为**：删除数字人层导致字幕层也消失。

## 问题分析

### 根本原因
在PreviewEditor.vue的第695-698行，存在一个监听数字人配置变化的watch函数，当数字人配置被清空时（URL为空），它会自动清除字幕数据：

```javascript
// 当没有数字人时，清除字幕数据
store.clearSubtitleData();
console.log('已清除字幕数据');
```

### 问题链路分析
1. **用户右键删除数字人层** → `deleteElement`函数执行`secondImage`分支
2. **发射数字人清空事件** → `emit('digital-human-cleared')`
3. **父组件处理清空事件** → `handleDigitalHumanCleared`清空数字人配置
4. **数字人配置变化触发watch** → 监听到数字人URL为空
5. **错误的关联逻辑执行** → `store.clearSubtitleData()`清除字幕数据
6. **字幕层消失** → 用户看到字幕意外消失

### 设计问题
数字人层和字幕层应该是独立的UI层，它们之间不应该有强制的依赖关系。删除数字人不应该影响字幕的显示状态。

## 解决方案

### 修复方法
移除数字人配置监听器中不当的字幕数据清除逻辑。

### 修改的文件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue`

### 具体修改内容

#### 修复前的问题代码
```javascript
// 监听数字人配置变化
watch(() => props.digitalHumanConfig, (newConfig) => {
    console.log('数字人配置变化:', newConfig);

    // 当有数字人URL时显示第二图层，没有时隐藏
    if (newConfig && newConfig.url && newConfig.url.trim() !== '') {
        showSecondImage.value = true;
        console.log('显示数字人第二图层');
    } else {
        showSecondImage.value = false;
        isSecondImageActive.value = false;
        console.log('隐藏数字人第二图层');
        
        // ❌ 问题代码：不当的字幕数据清除逻辑
        store.clearSubtitleData();
        console.log('已清除字幕数据');
    }
}, { immediate: true, deep: true });
```

#### 修复后的正确代码
```javascript
// 监听数字人配置变化
watch(() => props.digitalHumanConfig, (newConfig) => {
    console.log('数字人配置变化:', newConfig);

    // 当有数字人URL时显示第二图层，没有时隐藏
    if (newConfig && newConfig.url && newConfig.url.trim() !== '') {
        showSecondImage.value = true;
        console.log('显示数字人第二图层');
    } else {
        showSecondImage.value = false;
        isSecondImageActive.value = false;
        console.log('隐藏数字人第二图层');
        
        // ✅ 移除不当的字幕数据清除逻辑
        // 数字人层和字幕层应该独立管理，删除数字人不应影响字幕
        // store.clearSubtitleData(); // 已移除
        // console.log('已清除字幕数据'); // 已移除
    }
}, { immediate: true, deep: true });
```

## 修复效果

### 层级独立性
修复后，各层的删除行为完全独立：

1. ✅ **右键删除背景层** → 只有背景层被移除
2. ✅ **右键删除数字人层** → 只有数字人层被移除
3. ✅ **右键删除字幕层** → 只有字幕层被移除
4. ✅ **其他层保持不变** → 删除任何单个层时，其他层完全不受影响

### 功能验证
- **数字人层管理**：数字人的显示/隐藏仍然正常工作
- **字幕层管理**：字幕的显示/隐藏完全独立于数字人状态
- **背景层管理**：背景的显示/隐藏不受其他层影响
- **右键菜单功能**：所有层的右键删除功能正常工作

## 测试步骤

### 功能测试
1. **添加多个层**：
   - 添加背景（图案或纯色）
   - 添加数字人
   - 添加字幕文本

2. **测试独立删除**：
   - 右键点击数字人层 → 选择删除 → 验证只有数字人消失，字幕和背景保持显示
   - 右键点击字幕层 → 选择删除 → 验证只有字幕消失，数字人和背景保持显示
   - 右键点击背景层 → 选择删除 → 验证只有背景消失，数字人和字幕保持显示

3. **验证状态独立性**：
   - 删除数字人后重新添加 → 字幕应该仍然存在
   - 删除字幕后重新添加 → 数字人应该仍然存在
   - 各层的选中状态应该独立管理

### 边界测试
1. **连续删除操作**：快速连续删除和添加不同层
2. **混合操作**：删除某层后立即添加其他层
3. **状态恢复**：删除所有层后重新添加，验证功能正常

## 设计原则

### 层级独立性
- 每个UI层（背景、数字人、字幕）应该有独立的状态管理
- 层与层之间不应该有不必要的强制依赖关系
- 删除操作应该只影响目标层，不影响其他层

### 用户体验
- 用户操作应该符合直觉：删除什么就消失什么
- 不应该有意外的副作用或关联删除
- 每个层的生命周期应该独立可控

### 代码维护性
- 避免层与层之间的紧耦合
- 使用清晰的事件命名和处理逻辑
- 保持代码的可读性和可维护性

## 相关文件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue`
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
- `src/views/modules/digitalHuman/store/digitalHumanStore.js`
