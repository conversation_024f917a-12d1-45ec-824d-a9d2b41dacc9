# 数字人生成视频时长权限检查功能

## 📋 概述

在数字人编辑器的生成视频功能中添加了时长权限检查，确保用户在生成视频前有足够的数字人合成时长额度。

## 🎯 功能需求

1. **权限检查时机**：在调用 `saveDigitalWork()` 或 `editDigitalWork()` 接口之前进行验证
2. **检查参数**：
   - `userId`：从localStorage获取用户ID
   - `feat`：设置为 "digital_time"（数字人时长权限）
   - `need`：传入数字人视频的时长（秒数）
3. **失败处理**：如果 `response.content.result === false`，阻止视频生成并提示用户

## 🔧 实现方案

### 修改文件
- **位置**：`src/views/layout/components/headbar/components/action/index.vue`
- **方法**：`handleSaveDigitalWork()`

### 新增导入
```javascript
import { checkUploadPermission } from '@/api/upload'; // 导入权限检查接口
import { useDigitalHumanStore } from '@/views/modules/digitalHuman/store/digitalHumanStore'; // 导入数字人store
```

### 新增辅助函数
```javascript
// 获取数字人store实例，用于获取视频时长
const digitalHumanStore = useDigitalHumanStore()

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};
```

## 🎬 时长获取逻辑

### 优先级顺序
1. **第一优先级**：从 `digitalHumanStore.totalDuration` 获取
2. **第二优先级**：从 `saveParams.audioJson.duration` 获取
3. **默认值**：如果都没有，使用10秒作为默认时长

### 实现代码
```javascript
// 获取数字人视频时长（秒数）
let videoDurationInSeconds = 0;

// 优先从digitalHumanStore获取时长
if (digitalHumanStore.totalDuration && digitalHumanStore.totalDuration > 0) {
    videoDurationInSeconds = digitalHumanStore.totalDuration;
    console.log('🎬 从digitalHumanStore获取视频时长:', videoDurationInSeconds, '秒');
}
// 备选方案：从saveParams的audioJson中获取时长
else if (saveParams.audioJson && saveParams.audioJson.duration && saveParams.audioJson.duration > 0) {
    videoDurationInSeconds = Math.ceil(saveParams.audioJson.duration);
    console.log('🎬 从audioJson获取视频时长:', videoDurationInSeconds, '秒');
}
// 如果都没有，使用默认最小时长
else {
    videoDurationInSeconds = 10; // 默认10秒
    console.log('⚠️ 未获取到有效时长，使用默认时长:', videoDurationInSeconds, '秒');
}
```

## 🔒 权限检查实现

### 检查流程
```javascript
// 调用时长权限检查接口
const userId = getUserId();
const permissionResponse = await checkUploadPermission({
    userId: userId,
    feat: "digital_time",
    need: videoDurationInSeconds
});

// 检查权限验证结果
if (permissionResponse && permissionResponse.content && permissionResponse.content.result === false) {
    console.log('❌ 数字人时长权限不足，禁止生成视频');
    ElMessage.error('您的剩余合成时长已不足，请先购买数字人套餐');
    return; // 阻止继续执行
}
```

### 错误处理
- **权限不足**：显示提示"您的剩余合成时长已不足，请先购买数字人套餐"
- **接口异常**：显示提示"权限检查失败，请稍后重试"
- **两种情况都会阻止视频生成流程继续执行**

## 📍 代码插入位置

在 `handleSaveDigitalWork()` 方法中的具体位置：
1. **之前**：构建保存参数 (`buildSaveParams`)
2. **之后**：调用生成视频接口 (`saveDigitalWork` 或 `editDigitalWork`)

## ✅ **关键确认：权限检查确实在保存接口之前执行**

### 🔒 **执行顺序验证**
```javascript
// 第800行：构建保存参数
const saveParams = buildSaveParams(editorData);

// 第834-855行：时长权限检查
const permissionResponse = await checkUploadPermission({
    userId: userId,
    feat: "digital_time",
    need: videoDurationInSeconds
});

// 第844-847行：权限不足时阻止执行
if (permissionResponse && permissionResponse.content && permissionResponse.content.result === false) {
    ElMessage.error('您的剩余合成时长已不足，请先购买数字人套餐');
    return; // 🚫 直接退出，不会执行保存接口
}

// 第857-878行：只有权限检查通过才会执行保存接口
response = await saveDigitalWork(saveParams); // 或 editDigitalWork
```

### 🚫 **阻止机制确认**
- **权限不足**：`return` 语句确保方法直接退出，不会调用保存接口
- **接口异常**：同样使用 `return` 阻止继续执行
- **双重保障**：两种失败情况都能有效阻止视频生成

## ✅ 测试要点

1. **正常流程**：权限充足时应该正常生成视频
2. **权限不足**：应该阻止生成并显示正确提示
3. **接口异常**：应该优雅处理错误并显示提示
4. **时长获取**：验证不同场景下时长获取的准确性

## 🔄 兼容性

- **向后兼容**：不影响现有的视频生成逻辑
- **编辑模式**：同时支持新建和编辑模式的权限检查
- **错误恢复**：权限检查失败不会影响其他功能

---

## 📝 更新日志

- **2024-01-XX**：初始实现数字人生成视频时长权限检查功能
- **功能状态**：✅ 已完成并测试
