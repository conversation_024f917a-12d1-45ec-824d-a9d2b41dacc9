# 数字人第二图层实现记录

## 问题描述
用户反馈：左侧的数字人有数据了，从接口返回来的，需要将选中的数字人图片复制到中间的第二图层，并删除现有默认的第二图层图片。

## 实现方案

### 1. 删除默认图片
- ✅ 修改 `DigitalHumanEditorPage.vue` 中的 `currentDigitalHumanConfig.url` 为空字符串
- ✅ 修改 `PreviewEditor.vue` 中的 `digitalHumanConfig.default().url` 为空字符串

### 2. 修改数字人选择逻辑
- ✅ 修改 `left_operate/index.vue` 中的 `chooseList1` 函数
- ✅ 添加数字人选择事件发射逻辑
- ✅ 从 `item.figures` 数组中提取非 `circle_view` 类型的图片URL
- ✅ 使用 `figure.cover` 作为图片URL

### 3. 修改第二图层显示逻辑
- ✅ 在 `PreviewEditor.vue` 中添加数字人配置监听器
- ✅ 当有数字人URL时显示第二图层 (`showSecondImage = true`)
- ✅ 当没有数字人URL时隐藏第二图层 (`showSecondImage = false`)

### 4. 修复初始化错误
- ✅ 将 `showSecondImage` 和 `isSecondImageActive` 的定义移到 `watch` 监听器之前
- ✅ 解决 "Cannot access 'showSecondImage' before initialization" 错误

## 代码修改详情

### DigitalHumanEditorPage.vue
```javascript
// 第253-257行
const currentDigitalHumanConfig = ref({
    type: 'picture',
    url: '',  // 删除默认数字人图片，等待用户选择
    index: null
});
```

### PreviewEditor.vue
```javascript
// 第314-321行
digitalHumanConfig: {
    type: Object,
    default: () => ({
        type: 'picture',
        url: '',  // 删除默认数字人图片，等待用户选择
        index: null
    })
},

// 第648-656行 - 添加第二图层状态管理
const isSecondImageActive = ref(false); // 选中状态
const showSecondImage = ref(false);     // 显示/隐藏控制 - 默认隐藏，等待用户选择数字人

// 第657-670行 - 监听数字人配置变化
watch(() => props.digitalHumanConfig, (newConfig) => {
    console.log('数字人配置变化:', newConfig);
    
    // 当有数字人URL时显示第二图层，没有时隐藏
    if (newConfig && newConfig.url && newConfig.url.trim() !== '') {
        showSecondImage.value = true;
        console.log('显示数字人第二图层');
    } else {
        showSecondImage.value = false;
        isSecondImageActive.value = false;
        console.log('隐藏数字人第二图层');
    }
}, { immediate: true, deep: true });
```

### left_operate/index.vue
```javascript
// 第180-204行 - 修改数字人选择逻辑
const chooseList1 = (index, item) => {
    activeIndex1.value = index
    choosedDigitalItem.value = item
    
    // 🎭 通知父组件数字人选择变更
    console.log('选中数字人:', index, item)
    
    // 从数字人项目中获取图片URL
    let digitalHumanUrl = ''
    if (item.figures && item.figures.length > 0) {
        // 查找非circle_view类型的图片
        const imageItem = item.figures.find(figure => figure.type !== 'circle_view')
        if (imageItem && imageItem.cover) {
            digitalHumanUrl = imageItem.cover
        }
    }
    
    // 发送数字人变更事件
    emit('digital-human-change', {
        type: 'picture',
        url: digitalHumanUrl,
        index: index,
        name: item.name
    })
}
```

## 测试步骤
1. ✅ 启动开发服务器 (`npm run dev`)
2. ✅ 访问数字人编辑器页面 (`http://localhost:5174/digital-human-editor`)
3. ✅ 解决初始化错误
4. 🔄 测试点击左侧数字人列表项是否能在第二图层显示对应图片
5. 🔄 验证默认状态下第二图层是否为空

## 当前状态
- 代码修改已完成
- 初始化错误已解决
- 需要在浏览器中测试实际功能效果

## 下一步
- 在浏览器中测试数字人选择功能
- 验证第二图层是否正确显示选中的数字人图片
- 如有问题，进一步调试和优化
