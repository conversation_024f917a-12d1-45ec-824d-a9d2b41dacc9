# 数字人编辑器功能详解

## 功能概述

数字人编辑器是应用中的核心功能之一，提供完整的数字人视频合成与编辑能力。用户可以通过该功能创建包含数字人角色、装饰图片和字幕的视频内容，实现专业级的视频制作效果。

## 核心组件：PreviewEditor

PreviewEditor 是数字人编辑器的核心组件，负责处理用户交互、元素管理和位置数据系统。

### 1. 交互系统

#### 1.1 智能点击选择
- **功能描述**：支持多层级元素的精确选择，自动处理重叠区域优先级
- **技术实现**：使用事件冒泡和捕获机制，结合元素层级管理
- **用户体验**：用户可以直接点击想要选择的元素，系统会智能识别用户意图

#### 1.2 悬停自动选中
- **功能描述**：1秒悬停自动选中机制，优化用户体验
- **技术实现**：使用定时器实现悬停延迟，避免误触
- **用户体验**：减少用户操作步骤，提高编辑效率

#### 1.3 拖拽系统
- **功能描述**：高性能拖拽实现，支持跨预览窗口边界操作
- **技术实现**：使用 vue-draggable-resizable 库，结合自定义边界检测
- **用户体验**：流畅的拖拽体验，支持精确定位

#### 1.4 拉伸控制
- **功能描述**：多方向拉伸支持，保持宽高比的智能缩放
- **技术实现**：
  - 修正拉伸基准计算逻辑，使用当前实际尺寸而不是原始尺寸
  - 完整实现背景模块拉伸功能，支持3:2宽高比保持
  - 优化所有元素（数字人、背景、装饰图片、字幕）的拉伸逻辑
- **用户体验**：拉伸操作更加流畅和可预期，避免了意外的尺寸跳跃

#### 1.5 口型同步预览提示
- **功能描述**：首次点击播放时显示提示，告知用户预览时暂不支持口型对齐
- **技术实现**：使用 localStorage 记录用户选择，避免重复提示
- **用户体验**：用户可以选择"不再提示"，提升用户体验

### 2. 元素管理

#### 2.1 数字人角色
- **功能描述**：
  - 响应式尺寸适配（16:9、9:16等比例）
  - 智能边界限制与位置优化
  - 实时位置跟踪和状态管理
- **技术实现**：
  - 使用响应式计算属性动态调整尺寸
  - 边界检测算法确保元素不会超出可视区域
  - 使用 Pinia 进行状态管理
- **用户体验**：数字人角色可以自适应不同屏幕比例，保持良好的视觉效果

#### 2.2 装饰图片
- **功能描述**：
  - Logo、图标等辅助元素管理
  - 独立的层级控制和交互逻辑
  - 正方形比例保持和尺寸控制
- **技术实现**：
  - 使用 z-index 控制层级
  - 自定义比例保持算法
  - 独立的事件处理系统
- **用户体验**：装饰图片可以灵活添加和调整，丰富视频内容

#### 2.3 字幕系统
- **功能描述**：
  - 文本内容编辑与样式控制
  - 6方向拉伸支持（左上、左中、左下、右上、右中、右下）
  - 动态字体和对齐设置
- **技术实现**：
  - 使用 contenteditable 实现文本编辑
  - 自定义拉伸控制点
  - 动态加载字体
- **用户体验**：字幕可以灵活调整位置和样式，满足不同场景需求

### 3. 位置数据系统

#### 3.1 实时数据收集
- **功能描述**：支持数字人角色、装饰图片、字幕的精确位置追踪
- **技术实现**：
  ```javascript
  // 获取所有元素完整位置数据
  const positionsData = getAllPositionsData();
  // 返回格式包含：x, y, width, height, scaleX, scaleY, offsetX, offsetY, visible, active等
  ```
- **用户体验**：位置数据实时更新，确保编辑效果准确

#### 3.2 事件发射机制
- **功能描述**：
  - `@position-update`: 全局位置更新事件
  - `@character-moved`: 数字人角色移动事件  
  - `@second-image-moved`: 装饰图片移动事件
  - `@subtitle-moved`: 字幕移动事件
- **技术实现**：使用 Vue 3 的 emit 机制
- **用户体验**：实时响应用户操作，提供流畅的编辑体验

#### 3.3 主动获取接口
- **功能描述**：提供多种方法供父组件调用
- **技术实现**：
  ```javascript
  // 父组件可通过ref调用
  previewEditorRef.value.getAllPositionsData()
  previewEditorRef.value.getElementPosition('character')
  previewEditorRef.value.resetAllPositions()
  ```
- **用户体验**：支持复杂的交互场景和数据管理

#### 3.4 数据格式
- **功能描述**：
  - 位置坐标(x, y)、尺寸(width, height)
  - 缩放比例(scaleX, scaleY)、用户偏移(offsetX, offsetY)
  - 显示状态(visible)、选中状态(active)
  - 预览窗口信息、时间戳等元数据
- **技术实现**：使用标准化的数据结构
- **用户体验**：数据格式统一，便于保存和恢复

### 4. 状态管理

#### 4.1 digitalHumanStore
- **功能描述**：集中管理数字人模块状态
- **技术实现**：使用 Pinia 进行状态管理，支持持久化
- **用户体验**：状态一致性，支持页面刷新后恢复

#### 4.2 时间轴控制
- **功能描述**：播放状态、当前时间、总时长管理
- **技术实现**：使用响应式状态和计算属性
- **用户体验**：视频播放控制流畅，支持精确定位

#### 4.3 字幕同步
- **功能描述**：字幕加载状态和内容同步
- **技术实现**：使用事件监听和状态同步
- **用户体验**：字幕与音频/视频同步显示

#### 4.4 背景管理
- **功能描述**：动态背景色变更和事件处理
- **技术实现**：使用响应式属性和事件系统
- **用户体验**：背景可以根据需求动态调整

## 技术实现特性

### 1. 性能优化
- **原生JavaScript变量**：避免响应式开销
- **智能边界检测**：使用高效的边界检测算法
- **事件监听器清理**：及时清理防止内存泄漏

### 2. 响应式设计
- **多宽高比支持**：16:9横屏、9:16竖屏等
- **预览窗口智能尺寸计算**：根据屏幕尺寸自动调整
- **操作区域动态扩展**：根据内容自动调整操作区域

### 3. 数据持久化
- **位置配置的保存与恢复**：使用 localStorage 和 Pinia
- **用户操作历史记录**：支持操作撤销和重做
- **项目状态的导入导出**：支持项目保存和加载

## 最近更新

### 1. 右键菜单功能
- **功能描述**：新增右键上下文菜单，支持一键删除数字人、装饰图片、字幕等元素
- **技术实现**：自定义右键菜单组件，结合元素选择系统
- **用户体验**：大幅提升编辑效率，操作更加直观

### 2. 导航栏定制化
- **功能描述**：为数字人编辑器页面专门设计的导航栏，包含专用返回按钮和项目标题实时编辑功能
- **技术实现**：自定义导航栏组件，与编辑器状态集成
- **用户体验**：提供更专业的编辑界面，增强用户体验

### 3. 项目标题管理
- **功能描述**：支持在编辑过程中随时修改项目名称，提供内联编辑体验和智能占位符提示
- **技术实现**：使用 contenteditable 和状态管理
- **用户体验**：项目管理更加灵活，支持实时编辑

### 4. 拉伸功能修复
- **功能描述**：解决了背景层和数字人拉伸时第一次正常，再次拉伸突然变大的问题
- **技术实现**：
  - 修正拉伸基准计算逻辑，使用当前实际尺寸而不是原始尺寸
  - 完整实现背景模块拉伸功能，支持保持宽高比的智能缩放
  - 优化所有可拉伸元素的拉伸逻辑
- **用户体验**：拉伸操作更加流畅和可预期，避免了意外的尺寸跳跃

## 使用指南

### 1. 基本操作
- **添加数字人**：从右侧面板选择数字人角色，点击添加
- **添加装饰图片**：从右侧面板选择图片，点击添加
- **添加字幕**：点击字幕按钮，输入文本内容
- **调整位置**：拖拽元素到desired位置
- **调整大小**：使用拉伸控制点调整元素大小
- **删除元素**：选中元素，按Delete键或使用右键菜单删除

### 2. 高级功能
- **保存项目**：点击保存按钮，输入项目名称
- **导出视频**：点击导出按钮，选择导出格式和质量
- **预览效果**：点击预览按钮，查看实时效果
- **调整字幕样式**：选中字幕，使用右侧面板调整字体、大小、颜色等

### 3. 快捷键
- **Ctrl+Z**：撤销操作
- **Ctrl+Y**：重做操作
- **Delete**：删除选中元素
- **Ctrl+S**：保存项目
- **Space**：播放/暂停预览

## 常见问题解决

### 1. 拉伸问题
- **问题**：拉伸元素时尺寸突然变大
- **解决方案**：已在最新版本中修复，使用当前实际尺寸作为基准

### 2. 字幕同步问题
- **问题**：预览时字幕与口型不同步
- **解决方案**：预览模式下暂不支持口型同步，最终生成时会自动匹配

### 3. 性能问题
- **问题**：编辑复杂场景时可能出现卡顿
- **解决方案**：减少同时显示的元素数量，关闭不必要的预览效果

### 4. 浏览器兼容性
- **问题**：在某些浏览器中可能出现显示异常
- **解决方案**：推荐使用 Chrome 或 Edge 最新版本，已针对 Safari 进行特殊优化

## 未来规划

### 1. 功能增强
- **多轨道编辑**：支持多个数字人角色同时出现
- **动画效果**：添加入场、退场和过渡动画
- **AI辅助编辑**：智能推荐布局和样式
- **模板系统**：预设模板快速创建内容

### 2. 性能优化
- **渲染引擎升级**：提升复杂场景的渲染性能
- **资源预加载**：优化资源加载策略
- **WebGL加速**：使用WebGL提升渲染性能

### 3. 用户体验提升
- **更丰富的快捷键**：支持更多快捷操作
- **历史记录管理**：更详细的操作历史
- **协作编辑**：支持多人同时编辑
- **云端同步**：跨设备同步项目