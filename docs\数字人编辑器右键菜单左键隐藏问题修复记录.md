# 数字人编辑器右键菜单左键隐藏问题修复记录

## 问题描述
用户反馈：在数字人编辑器中，右键点击背景层、数字人层或字幕层弹出删除菜单后，点击鼠标左键并没有隐藏右键菜单。

## 问题复现步骤
1. 在数字人编辑器中添加背景或数字人
2. 右键点击元素弹出删除菜单
3. 点击页面其他区域的鼠标左键
4. 观察右键菜单是否消失

**期望结果**：左键点击页面任何地方（除了右键菜单内部）都应该立即隐藏右键菜单
**实际结果**：左键点击没有隐藏右键菜单

## 问题分析

### 根本原因
**事件冒泡被阻止**：预览窗口使用了 `@click.stop="onPreviewWindowClick"`，这阻止了点击事件向上冒泡到document级别，导致全局点击监听器无法接收到事件。

### 技术细节
1. **预览窗口事件阻止**：
   ```vue
   <div class="preview-window" @click.stop="onPreviewWindowClick">
   ```
   这个 `@click.stop` 阻止了所有点击事件向document冒泡

2. **全局监听器失效**：
   ```javascript
   document.addEventListener('click', handleGlobalClickForContextMenu);
   ```
   由于事件被阻止冒泡，document级别的监听器无法接收到事件

3. **设计冲突**：
   - 预览窗口需要 `@click.stop` 来处理元素选择逻辑
   - 右键菜单隐藏功能依赖于document级别的事件监听
   - 两个功能产生了冲突

## 解决方案

### 采用方案：在预览窗口点击处理中添加右键菜单隐藏逻辑
**技术原理**：在现有的 `onPreviewWindowClick` 函数中添加右键菜单隐藏逻辑，同时保持现有的元素选择功能不变。

### 实施步骤

#### 1. 修改预览窗口点击处理函数
**文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`

在 `onPreviewWindowClick` 函数开始处添加右键菜单隐藏逻辑：

```javascript
const onPreviewWindowClick = (event) => {
    console.log('🖱️ 预览窗口被点击');

    // 🖱️ 优先处理：隐藏右键菜单（如果可见）
    if (contextMenu.value.visible) {
        hideContextMenu();
        console.log('🖱️ 预览窗口点击：隐藏右键菜单');
    }

    // 🚫 防误触：拖拽或拉伸进行中时不处理点击
    if (isDraggingCharacterImage || isDraggingSecondImage || isDraggingSubtitle || isDraggingBackgroundModule ||
        isResizingCharacter || isResizingSecondImage || isResizingSubtitle) {
        console.log('🚫 拖拽进行中，忽略点击');
        return;
    }
    
    // ... 现有的元素选择逻辑保持不变
};
```

#### 2. 暴露右键菜单隐藏方法给父组件
在 `defineExpose` 中添加 `hideContextMenu` 方法：

```javascript
defineExpose({
    // 📊 位置数据获取方法
    getAllPositionsData,
    getElementPosition,
    emitPositionUpdate,
    // 🎯 选中状态控制方法
    clearAllSelections,
    // 🖱️ 右键菜单控制方法
    hideContextMenu,            // 隐藏右键菜单
    // ... 其他方法
});
```

#### 3. 修改父组件全局点击处理
**文件**: `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

在父组件的全局点击处理中添加右键菜单隐藏逻辑：

```javascript
const onGlobalClick = (event) => {
    console.log('🌍 主页面全局点击事件触发');

    // 调用预览编辑器的方法
    if (previewEditorRef.value) {
        // 隐藏右键菜单（如果可见）
        if (previewEditorRef.value.hideContextMenu) {
            previewEditorRef.value.hideContextMenu();
        }
        
        // 清除所有元素选中状态
        if (previewEditorRef.value.clearAllSelections) {
            previewEditorRef.value.clearAllSelections();
        }
    }
};
```

#### 4. 增强调试日志
在 `hideContextMenu` 函数中添加条件日志：

```javascript
const hideContextMenu = () => {
    if (contextMenu.value.visible) {
        contextMenu.value.visible = false;
        console.log('🖱️ 右键菜单已隐藏');
    }
};
```

## 修复效果

### 功能覆盖
1. ✅ **预览窗口内点击**：直接在 `onPreviewWindowClick` 中处理，立即隐藏右键菜单
2. ✅ **预览窗口外点击**：通过父组件的 `onGlobalClick` 处理，调用子组件方法隐藏菜单
3. ✅ **左侧/右侧操作面板点击**：通过父组件全局点击处理覆盖
4. ✅ **页面其他区域点击**：通过父组件全局点击处理覆盖

### 事件处理流程
1. **预览窗口内点击** → `onPreviewWindowClick` → 隐藏右键菜单 + 处理元素选择
2. **预览窗口外点击** → `onGlobalClick` → 隐藏右键菜单 + 清除元素选中状态
3. **右键菜单内点击** → 保持菜单显示（现有逻辑不变）
4. **删除按钮点击** → 执行删除 + 隐藏菜单（现有逻辑不变）

## 调试指南

### 验证步骤
1. **打开浏览器开发者工具**，查看控制台输出
2. **右键点击任意元素**，确认菜单正常显示
3. **左键点击预览窗口内空白处**，观察控制台是否输出：
   ```
   🖱️ 预览窗口被点击
   🖱️ 预览窗口点击：隐藏右键菜单
   🖱️ 右键菜单已隐藏
   ```
4. **左键点击左侧/右侧操作面板**，观察控制台是否输出：
   ```
   🌍 主页面全局点击事件触发
   🖱️ 右键菜单已隐藏
   ```

### 常见问题排查
1. **如果预览窗口内点击无效**：
   - 检查 `onPreviewWindowClick` 函数是否被正确调用
   - 确认 `contextMenu.value.visible` 的值是否正确

2. **如果预览窗口外点击无效**：
   - 检查父组件的 `onGlobalClick` 是否被触发
   - 确认 `previewEditorRef.value.hideContextMenu` 方法是否存在

3. **如果控制台有错误**：
   - 检查是否有JavaScript语法错误
   - 确认所有引用的变量和方法都已正确定义

### 性能验证
- 验证事件监听器正确注册和清理
- 检查是否有内存泄漏
- 确认大量点击操作的响应性能

## 技术要点

### 双重保障机制
- **预览窗口内**：直接在点击处理函数中隐藏菜单
- **预览窗口外**：通过父组件全局点击处理隐藏菜单

### 功能协调
- **保持现有逻辑**：元素选择、拖拽、缩放等功能不受影响
- **增强用户体验**：右键菜单在任何地方点击都能正确隐藏
- **调试友好**：详细的控制台日志便于问题排查

### 兼容性考虑
- 不影响现有的事件处理机制
- 保持与父组件的良好协调
- 支持所有浏览器的标准事件处理

## 紧急修复：函数定义顺序问题

### 问题描述
在实施修复后出现错误：
```
ReferenceError: Cannot access 'hideContextMenu' before initialization
```

### 根本原因
`defineExpose` 在第3094行被调用，但 `hideContextMenu` 函数定义在第3235行，导致在函数定义之前就尝试引用该函数。

### 解决方案
将 `hideContextMenu` 函数定义移动到 `defineExpose` 之前：

```javascript
// 在 showContextMenu 函数之后立即定义
const hideContextMenu = () => {
    if (contextMenu.value.visible) {
        contextMenu.value.visible = false;
        console.log('🖱️ 右键菜单已隐藏');
    }
};
```

### 修复步骤
1. 移除原有的 `hideContextMenu` 函数定义（第3235行）
2. 在 `showContextMenu` 函数之后添加 `hideContextMenu` 函数定义
3. 确保函数在 `defineExpose` 调用之前被定义

## 相关文件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue`
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`
