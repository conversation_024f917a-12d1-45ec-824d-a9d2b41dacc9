# 数字人编辑器字体动态加载功能实现

## 功能概述

在数字人编辑器的左侧操作面板中，用户可以从字体下拉选择器中选择字体，系统会自动获取API返回的TTF字体文件链接并动态加载到字幕组件上，实现字幕字体的实时更新。

## 核心功能特性

1. **动态字体加载**：从API获取字体文件URL并实时加载
2. **字体缓存机制**：避免重复加载相同字体
3. **降级处理**：字体加载失败时使用系统回退字体
4. **加载状态指示**：提供用户友好的加载反馈
5. **实时预览**：字体选择后立即在预览区域生效

## 技术实现

### 1. 左侧面板字体选择器 (left_operate/index.vue)

#### 关键改动：
- 扩展 `handleFontStyleChange` 方法，支持字体URL传递
- 增强 `emitSubtitleStyleChange` 事件，包含 `fontUrl` 字段
- 添加字体加载状态管理和用户反馈

```javascript
// 字体选择变更处理
const handleFontStyleChange = async () => {
    const selectedFont = fontListData.value.find(font => font.id === setFontStyle.value);
    if (selectedFont) {
        console.log('🎨 字体选择变更:', {
            fontId: selectedFont.id,
            fontName: selectedFont.name,
            fontUrl: selectedFont.fontUrl || selectedFont.url
        });
        
        if (selectedFont.fontUrl || selectedFont.url) {
            fontLoadingStatus.value[selectedFont.name] = 'loading';
        }
    }
    
    emitSubtitleStyleChange()
}

// 字幕样式变更事件发射
const emitSubtitleStyleChange = () => {
    const selectedFont = fontListData.value.find(font => font.id === setFontStyle.value);
    const fontName = selectedFont ? selectedFont.name : '微软雅黑';
    
    // 支持多种可能的字体URL字段名
    const fontUrl = selectedFont ? (
        selectedFont.fontUrl || 
        selectedFont.url || 
        selectedFont.ttfUrl || 
        selectedFont.fileUrl ||
        selectedFont.downloadUrl
    ) : null;
    
    emit('subtitle-style-change', {
        fontFamily: setFontStyle.value,
        fontName: fontName,
        fontUrl: fontUrl,    // 新增字体URL字段
        fontSize: setFontSize.value,
        textColor: textColor.value,
        borderColor: borderColor.value,
        borderWidth: setThickness.value
    })
}
```

### 2. 字体加载器扩展 (utils/fontLoader.js)

#### 关键改动：
- 扩展 `loadFont` 方法，优先使用传入的 `fontUrl` 参数
- 保持现有CDN映射作为降级方案

```javascript
async loadFont(fontName, fontUrl = null) {
    // 优先使用传入的fontUrl，这是动态字体加载的核心
    let fontUrls = null;
    
    if (fontUrl) {
        // 如果提供了fontUrl，优先使用（支持API返回的动态URL）
        fontUrls = [fontUrl];
        console.log(`🎨 使用动态字体URL: "${fontName}" → ${fontUrl}`);
    } else {
        // 降级到预定义的CDN映射
        fontUrls = this.fontCDNMap[fontName];
    }
    
    // ... 其余加载逻辑保持不变
}
```

### 3. 预览编辑器字体应用 (PreviewEditor.vue)

#### 关键改动：
- 更新 `loadFont` 函数支持 `fontUrl` 参数
- 监听字体配置变化，包括 `fontUrl` 字段
- 组件挂载时支持动态字体预加载

```javascript
// 字体加载函数
const loadFont = async (fontName, fontUrl = null) => {
    // ... 加载逻辑
    const success = await FontLoader.loadFont(fontName, fontUrl);
    // ... 后续处理
}

// 监听字体配置变化
watch(() => [props.subtitleConfig.fontName, props.subtitleConfig.fontFamily, props.subtitleConfig.fontUrl], 
    async ([newFontName, newFontId, newFontUrl], [oldFontName, oldFontId, oldFontUrl] = []) => {
    
    if (newFontName && (newFontName !== oldFontName || newFontUrl !== oldFontUrl)) {
        const loadResult = await loadFont(newFontName, newFontUrl);
        // ... 处理加载结果
    }
})
```

## API数据结构支持

系统支持多种可能的字体URL字段名，确保与不同API返回格式的兼容性：

```javascript
// 实际的字体数据结构（基于API返回）
{
    id: "font_001",
    name: "阿里巴巴普惠体",
    ttf_path: "https://example.com/font.ttf",    // ✅ 实际使用的字段
    fontUrl: "https://example.com/font.ttf",     // 备选1
    url: "https://example.com/font.woff2",       // 备选2
    ttfUrl: "https://example.com/font.ttf",      // 备选3
    fileUrl: "https://example.com/font.woff",    // 备选4
    downloadUrl: "https://example.com/font.otf"  // 备选5
}
```

## 用户体验优化

1. **加载状态指示**：字体选择器显示"加载中..."提示
2. **选择器禁用**：加载期间禁用字体选择器，防止重复操作
3. **实时预览**：字体加载完成后立即在预览区域生效
4. **错误处理**：加载失败时自动降级到系统字体

## 错误处理和降级机制

1. **字体URL无效**：自动尝试预定义CDN映射
2. **网络加载失败**：使用系统回退字体
3. **格式不支持**：提供多格式支持（TTF/WOFF2/WOFF）
4. **加载超时**：设置合理的超时时间和重试机制

## 测试建议

1. **API数据测试**：验证不同字体URL字段名的兼容性
2. **网络异常测试**：模拟网络中断和慢速连接
3. **字体格式测试**：测试TTF、WOFF2、WOFF等格式支持
4. **缓存机制测试**：验证字体缓存和重复加载处理
5. **用户体验测试**：确保加载状态指示和错误提示正常

## 后续优化方向

1. **字体预加载**：在组件初始化时预加载常用字体
2. **加载进度**：显示字体文件下载进度
3. **字体预览**：在选择器中显示字体样式预览
4. **批量加载**：支持多个字体同时加载
5. **本地缓存**：利用浏览器缓存机制提升加载速度
