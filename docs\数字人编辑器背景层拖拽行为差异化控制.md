# 数字人编辑器背景层拖拽行为差异化控制

## 需求描述

在数字人编辑器的背景层功能中，需要修改拖拽行为的逻辑，实现图案背景和纯色背景的差异化交互控制。

### 原始状态
- 点击图案背景时，可以进行拖拽操作
- 点击纯色背景时，也可以进行拖拽操作

### 修改目标
- 点击图案背景时，保持可以拖拽的功能
- 点击纯色背景时，禁用拖拽功能（不允许拉伸/移动），也不允许展示对应的边框和圆标

## 技术分析

### 背景类型区分
- **图案背景**: `backgroundConfig.type === 'image'`
- **纯色背景**: `backgroundConfig.type === 'color'`

### 涉及的交互功能
1. 拖拽移动功能
2. 拉伸缩放功能
3. 选中边框显示
4. 拉伸手柄显示
5. 悬停效果
6. 点击选中

## 实施方案

采用**条件性禁用拖拽事件**方案，在现有拖拽逻辑基础上添加背景类型检查，确保纯色背景不触发任何交互行为。

### 优势
- 实现简单，风险最低
- 不需要修改模板结构
- 只需在现有拖拽逻辑中添加类型判断
- 保持图案背景功能完全不变

## 具体修改内容

### 1. 禁用纯色背景的拖拽功能
**文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`  
**位置**: `startBackgroundModuleDrag` 函数 (第2509行)

```javascript
// 开始背景模块拖拽
const startBackgroundModuleDrag = (event) => {
    // 🚫 纯色背景禁用拖拽功能
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        console.log('🚫 纯色背景禁用拖拽功能');
        return;
    }
    
    // 原有拖拽逻辑...
};
```

### 2. 禁用纯色背景的拉伸功能
**位置**: `startBackgroundModuleResize` 函数 (第2591行)

```javascript
// 背景模块拉伸功能
const startBackgroundModuleResize = (direction, event) => {
    // 🚫 纯色背景禁用拉伸功能
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        console.log('🚫 纯色背景禁用拉伸功能');
        return;
    }
    
    // 原有拉伸逻辑...
};
```

### 3. 禁用纯色背景的拉伸手柄显示
**位置**: 模板中的拉伸手柄条件 (第151行)

```vue
<!-- 拉伸手柄 - 只在选中且非播放状态时显示，且仅对图案背景显示，纯色背景不显示 -->
<div v-if="isBackgroundModuleActive && !isPlaying && backgroundConfig?.type === 'image'" class="resize-handles">
    <div class="resize-handle resize-handle-tl" @mousedown.stop="startBackgroundModuleResize('tl', $event)"></div>
    <div class="resize-handle resize-handle-tr" @mousedown.stop="startBackgroundModuleResize('tr', $event)"></div>
    <div class="resize-handle resize-handle-bl" @mousedown.stop="startBackgroundModuleResize('bl', $event)"></div>
    <div class="resize-handle resize-handle-br" @mousedown.stop="startBackgroundModuleResize('br', $event)"></div>
</div>
```

### 4. 禁用纯色背景的边框显示
**位置**: `backgroundModuleDisplayStyle` 计算属性 (第1700行)

```javascript
// 只有在背景配置有效且为图案背景时才显示边框和圆角，纯色背景不显示边框
if (hasValidBackgroundConfig.value && props.backgroundConfig?.type === 'image') {
    borderRadius = '12px'; // 有效配置时显示圆角

    if (isBackgroundModuleActive.value) {
        borderColor = '#4CAF50';      // 绿色边框表示选中
        borderWidth = '2px';
        boxShadow = '0 0 0 1px rgba(76, 175, 80, 0.3)';
    } else if (isBackgroundModuleHovering.value) {
        borderColor = 'rgba(76, 175, 80, 0.6)'; // 半透明绿色边框表示悬停
        borderWidth = '2px';
        boxShadow = '0 0 0 1px rgba(76, 175, 80, 0.3)';
    }
}
```

### 5. 禁用纯色背景的悬停效果
**位置**: `onBackgroundModuleMouseEnter` 和 `onBackgroundModuleMouseLeave` 函数

```javascript
// 背景模块鼠标进入事件
const onBackgroundModuleMouseEnter = () => {
    // 🚫 纯色背景禁用悬停效果
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        return;
    }
    
    // 原有悬停逻辑...
};

// 背景模块鼠标离开事件
const onBackgroundModuleMouseLeave = () => {
    // 🚫 纯色背景禁用悬停效果
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        return;
    }
    
    // 原有悬停逻辑...
};
```

### 6. 禁用纯色背景的点击选中
**位置**: `onPreviewWindowClick` 函数中的背景选中逻辑 (第2282行)

```javascript
} else if (isInBackgroundModuleBounds && props.backgroundConfig?.type === 'image') {
    // 背景模块区域 - 仅图案背景可以被选中，纯色背景不可选中
    isBackgroundModuleActive.value = true;
    isCharacterActive.value = false;
    isSecondImageActive.value = false;
    isSubtitleActive.value = false;
```

## 功能验证结果

### ✅ 图案背景 (`type: 'image'`)
- ✅ 可以拖拽移动
- ✅ 可以拉伸缩放
- ✅ 显示选中边框和拉伸手柄
- ✅ 支持悬停效果
- ✅ 可以点击选中

### ❌ 纯色背景 (`type: 'color'`)
- ❌ 禁止拖拽移动
- ❌ 禁止拉伸缩放  
- ❌ 不显示边框和拉伸手柄
- ❌ 不支持悬停效果
- ❌ 不可点击选中

## 技术要点

1. **类型判断**: 通过 `props.backgroundConfig?.type` 区分背景类型
2. **早期返回**: 在函数开头进行类型检查，纯色背景直接返回
3. **条件渲染**: 在模板中使用条件判断控制UI元素显示
4. **一致性**: 所有相关功能都采用相同的判断逻辑
5. **向后兼容**: 不影响现有图案背景的任何功能

## 测试建议

1. **图案背景测试**: 验证所有拖拽、拉伸、选中功能正常
2. **纯色背景测试**: 验证所有交互功能都被正确禁用
3. **切换测试**: 验证在图案背景和纯色背景之间切换时行为正确
4. **边界测试**: 验证无背景配置或异常配置时的行为

## 后续修复记录

### 修复16:9模式下纯色背景显示问题
**日期**: 2025-01-16
**问题**: 16:9模式下纯色背景错误地以全屏方式显示，而不是按照背景模块尺寸显示
**原因**: `previewWindowStyle` 计算属性将纯色背景应用到整个预览窗口容器上
**修复**: 修改背景色设置逻辑，只有在不显示背景模块时才将纯色背景应用到预览窗口

**修复代码**:
```javascript
// 🔧 修复：只有在不显示背景模块时，才将纯色背景应用到预览窗口
// 当显示背景模块时，背景由背景模块独立控制，预览窗口保持默认背景色
if (props.backgroundConfig && props.backgroundConfig.type === 'color' && props.backgroundConfig.value && !showBackgroundModule.value) {
    // 只有在没有背景模块显示时，才将纯色背景应用到预览窗口
    backgroundColor = props.backgroundConfig.value;
}
```

**效果**: 确保16:9模式下纯色背景与图案背景使用相同的尺寸和位置，不再铺满整个预览窗口

## 修改日期
2025-01-16

## 修改人员
AI Assistant (Augment Agent)
