# 数字人音频字幕拼接功能实现文档

## 功能概述

实现了数字人编辑器中音频上传后，将分段字幕数据拼接成完整文本并在右侧文本框中回显的功能。

## 问题描述

之前的实现中，字幕数据是分开的片段，用户希望能够将这些分段的字幕拼接成完整的文本，然后在右侧的文本框中回显出来。

## 解决方案

### 1. 接口替换
- 将原来的 `crawlTextByMediaFile` 接口替换为 `getDigiAudioJsonTxt`
- 新接口路径：`/material/api/getDigiAudioJsonTxt`
- 参数保持一致：`{url, userId}`

### 2. 数据处理逻辑优化
实现了智能的字幕数据拼接逻辑：

```javascript
// 处理字幕数据拼接
let fullText = '';
if (text.content && text.content.result) {
    // 优先使用 txt 字段
    if (text.content.result.txt) {
        fullText = text.content.result.txt;
    }
    // 从 subtitle_json 数组中提取 text 字段并拼接
    else if (text.content.result.subtitle_json && Array.isArray(text.content.result.subtitle_json)) {
        const subtitleTexts = text.content.result.subtitle_json
            .map(item => item.text || '') // 直接使用 text 字段
            .filter(text => text && text.trim() !== ''); // 过滤空文本

        fullText = subtitleTexts.join(''); // 直接连接，不加空格
    }
}
```

### 3. 错误处理和用户体验
- 添加了详细的控制台日志，便于调试
- 实现了优雅的错误处理
- 当无法提取文本时，显示友好的提示信息

## 技术实现

### 修改的文件

1. **src/api/digitalHuman.js**
   - 添加了 `getDigiAudioJsonTxt` 接口定义

2. **src/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio.vue**
   - 替换了接口调用
   - 实现了字幕拼接逻辑
   - 优化了错误处理

### 数据流程

```
音频文件上传 
    ↓
调用 getDigiAudioJsonTxt 接口
    ↓
接收返回数据 {content: {result: {txt?, subtitle_json?}}}
    ↓
优先使用 txt 字段
    ↓
如果没有 txt，从 subtitle_json 数组拼接
    ↓
过滤空文本，用空格连接
    ↓
回显到右侧文本框
```

### 支持的数据格式

接口返回数据支持以下格式：

```javascript
{
  status_code: 200,
  content: {
    result: {
      txt: "完整文本内容",  // 优先使用
      subtitle_json: [     // 备选方案，从每个对象的text字段拼接
        {
          "id": 1,
          "seek": 0,
          "start": 6.6,
          "end": 9.9,
          "text": "同一束光,照亮每个人心中不同的新疆。",
          "tokens": [...],
          "temperature": 0.0,
          "avg_logprob": -0.08410485982894897,
          "compression_ratio": 1.3826714801444044,
          "no_speech_prob": 0.4322985112667084,
          "time_begin": 6600.0,
          "time_end": 9900.0
        }
      ]
    }
  }
}
```

## 功能特点

1. **智能处理**：优先使用完整文本，备选拼接方案
2. **兼容性强**：支持多种字段名称（text/content/subtitle）
3. **错误处理**：完善的错误提示和日志记录
4. **用户友好**：无缝的文本回显体验

## 调试信息

实现中添加了详细的控制台日志：
- 接口返回数据结构
- 字幕拼接过程
- 最终文本内容
- 错误和警告信息

## 使用效果

用户上传音频文件后：
1. 系统自动调用新接口提取字幕
2. 将分段字幕智能拼接成完整文本
3. 在右侧文本框中实时回显
4. 提供清晰的处理状态反馈

这样用户就能看到完整的、连贯的文本内容，而不是分散的字幕片段。
