# 数字人页面智能返回功能实现

## 功能概述

实现了数字人页面的智能返回功能，用户从任何页面进入数字人编辑器后，点击返回按钮能够回到原来的页面，而不是直接跳转到首页。

## 实现方案

### 1. 来源页面记录机制

#### 1.1 主要记录方式
- **URL Query参数**：在跳转到数字人编辑器时，通过URL的query参数传递来源页面路径
- **localStorage备用**：作为备用机制，在localStorage中记录来源页面信息

#### 1.2 记录时机
- 从数字人中转页面点击"创建数字人作品"时记录当前页面
- 数字人编辑器页面加载时，如果没有query参数则记录浏览器referrer

### 2. 返回逻辑实现

#### 2.1 优先级顺序
1. 优先使用URL中的`from`参数
2. 如果没有query参数，从localStorage获取备用记录
3. 如果都没有，默认返回首页

#### 2.2 适用场景
- 返回按钮点击
- 数字人标题点击
- 统一使用相同的返回逻辑

## 代码实现

### 1. 数字人中转页面 (DigitalHumanTransition.vue)

```javascript
// 导航到数字人作品页面
const navigateToDigitalHuman = () => {
    // 记录来源页面，用于返回时使用
    const fromPage = route.path
    router.push({
        path: '/digital-human-editor-page',
        query: { from: fromPage }
    })
}
```

### 2. 顶部导航组件 (Headbar/index.vue)

#### 2.1 返回按钮处理函数
```javascript
const handleBack = async () => {
    // 防止重复点击
    if (isNavigating.value) {
        return;
    }

    isNavigating.value = true;

    try {
        // 如果在数字人页面，先暂停播放
        if (isDigitalHumanPage.value && digitalHumanStore.isPlaying) {
            digitalHumanStore.pause();
            console.log('🎵 已暂停数字人播放');
        }

        // 添加埋点
        umeng.trackEvent('数字人编辑器', '点击返回按钮', '返回来源页面', '');

        // 获取来源页面
        let targetPath = '/home'; // 默认返回首页
        
        // 优先从query参数获取来源页面
        if (route.query.from) {
            targetPath = route.query.from;
        }
        // 如果没有query参数，尝试从localStorage获取
        else {
            const savedFromPage = localStorage.getItem('digitalHumanFromPage');
            if (savedFromPage) {
                targetPath = savedFromPage;
                // 清除localStorage中的记录
                localStorage.removeItem('digitalHumanFromPage');
            }
        }

        // 跳转到目标页面
        await router.push(targetPath);

        console.log('🔄 成功返回到来源页面:', targetPath);
    } catch (error) {
        console.error('返回失败:', error);
        ElMessage.error('返回失败，请重试');
    } finally {
        // 延迟重置状态，防止快速连续点击
        setTimeout(() => {
            isNavigating.value = false;
        }, 500);
    }
};
```

#### 2.2 页面加载时的备用记录
```javascript
onMounted(() => {
    // ... 其他初始化代码 ...
    
    // 如果是数字人页面且没有来源页面记录，则记录referrer作为备用
    if (isDigitalHumanPage.value && !route.query.from) {
        const referrer = document.referrer;
        if (referrer && referrer.includes(window.location.origin)) {
            // 提取referrer中的路径部分
            const referrerPath = new URL(referrer).pathname;
            if (referrerPath && referrerPath !== '/digital-human-editor-page') {
                localStorage.setItem('digitalHumanFromPage', referrerPath);
                console.log('📝 记录数字人页面来源:', referrerPath);
            }
        }
    }
});
```

## 布局优化

### 数字人网格布局改进

#### 问题描述
原使用CSS Grid布局导致文字无法与图片居中对齐，文字出现偏右现象。

#### 解决方案
将布局从CSS Grid改为flexbox平铺布局：

```scss
// 修改前
.humans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    width: 1600px;
}

// 修改后
.humans-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
    width: 1535px;
}

.human-item {
    text-align: center;
    cursor: pointer;
    width: 160px; // 固定宽度，与图片宽度一致
    flex-shrink: 0; // 防止收缩
}
```

#### 优化效果
- 每个数字人项目固定160px宽度
- 文字与图片完美居中对齐
- 使用`justify-content: space-between`实现均匀分布
- 容器宽度调整为1535px以适应布局

## 技术要点

### 1. 防重复点击
- 使用`isNavigating`状态防止快速连续点击
- 延迟500ms重置状态

### 2. 错误处理
- 完善的try-catch错误处理
- 用户友好的错误提示

### 3. 埋点记录
- 记录用户行为数据
- 便于后续数据分析

### 4. 状态管理
- 暂停数字人播放状态
- 清理localStorage记录

## 测试场景

1. **从首页进入**：点击返回应回到首页
2. **从其他页面进入**：点击返回应回到原页面
3. **直接访问**：点击返回应回到首页
4. **快速点击**：防止重复跳转
5. **网络异常**：显示错误提示

## 后续优化建议

1. 考虑使用Vue Router的导航守卫统一处理
2. 添加返回历史记录，支持多级返回
3. 优化移动端返回体验
4. 添加返回确认对话框（可选） 