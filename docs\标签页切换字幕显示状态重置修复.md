# 标签页切换字幕显示状态重置修复文档

## 问题描述

在数字人编辑器中发现一个字幕显示问题：

1. **问题现象**：
   - 用户在音频驱动模式下完成操作后，预览区域中间显示了字幕内容
   - 当用户切换到"输入文本"标签页时，预览区域中间的字幕内容仍然存在
   - 音频驱动模式的字幕内容在切换到文本输入模式后残留

2. **预期行为**：
   - 切换到输入文本模式时，中间预览区域的字幕应该被清除
   - 两种模式之间的字幕显示状态应该完全独立
   - 用户应该看到干净的文本输入模式界面

## 问题分析

### 根本原因

1. **字幕显示逻辑**：
   - `subtitleText` 计算属性依赖于 `store.activeSubtitle`
   - `activeSubtitle` getter 在没有加载字幕数据时会返回 `state.currentSubtitle`
   - 这导致音频驱动模式设置的字幕文本会在切换模式后继续显示

2. **标签页切换逻辑不完整**：
   - 原有的轻量重置只清理了播放状态和音频资源
   - 没有清除字幕缓存数据（`currentSubtitle`、`subtitleData` 等）
   - 没有重置字幕显示状态

3. **状态管理问题**：
   - 字幕显示状态（`isSubtitleVisible`）在模式切换时没有被重置
   - 导致即使清除了字幕数据，显示状态仍然保持之前的设置

## 解决方案

### 修改的文件
- `src/views/modules/digitalHuman/components/right_operate/index.vue`

### 具体修改内容

#### 原有的轻量重置逻辑
```javascript
// 如果要切换到输入文本模式(id=1)，执行轻量重置
else if (newMode === 1) {
    digitalHumanStore.stop();
    digitalHumanStore.clearAudio();
    digitalHumanStore.setCurrentTime(0);
    console.log('✅ 输入文本模式：轻量重置完成');
}
```

#### 修复后的完整重置逻辑
```javascript
// 如果要切换到输入文本模式(id=1)，执行轻量重置并清除字幕缓存
else if (newMode === 1) {
    digitalHumanStore.stop();
    digitalHumanStore.clearAudio();
    digitalHumanStore.setCurrentTime(0);
    
    // 🧹 清除音频驱动模式的字幕缓存，确保预览区域字幕被清除
    digitalHumanStore.clearSubtitleData();
    console.log('🧹 已清除音频驱动模式的字幕缓存数据');
    
    // 🎭 重置字幕显示状态，确保预览区域字幕被隐藏
    emit('subtitle-toggle', false);
    console.log('🎭 已重置字幕显示状态为隐藏');
    
    console.log('✅ 输入文本模式：轻量重置和字幕清理完成');
}
```

## 修复的关键点

### 1. 清除字幕缓存数据
- 调用 `digitalHumanStore.clearSubtitleData()` 清除所有字幕相关数据
- 包括：`subtitleData`、`currentSubtitle`、`isSubtitleLoaded`、`subtitleUrl`、`ttsAudioUrl`
- 清除时间轴上的字幕事件

### 2. 重置字幕显示状态
- 通过 `emit('subtitle-toggle', false)` 重置字幕显示状态
- 确保主页面组件的 `isSubtitleVisible` 状态被重置为 false
- 预览编辑器的 `showSubtitle` 计算属性会响应这个状态变化

### 3. 完整的状态清理
- 保持原有的播放状态清理（stop、clearAudio、setCurrentTime）
- 新增字幕数据清理
- 新增字幕显示状态重置

## 数据流向

### 修复前的问题流程
```
音频驱动模式设置字幕 → currentSubtitle保存字幕文本
    ↓
切换到输入文本模式 → 只清理播放状态，字幕数据残留
    ↓
预览区域显示 → activeSubtitle返回残留的currentSubtitle
    ↓
用户看到音频驱动模式的字幕内容 ❌
```

### 修复后的正确流程
```
音频驱动模式设置字幕 → currentSubtitle保存字幕文本
    ↓
切换到输入文本模式 → 完整清理：播放状态 + 字幕数据 + 显示状态
    ↓
预览区域显示 → activeSubtitle返回空字符串，showSubtitle为false
    ↓
用户看到干净的输入文本模式界面 ✅
```

## 清除的数据范围

### Store状态清理
- `subtitleData: []` - 字幕数据数组
- `currentSubtitle: ''` - 当前显示的字幕文本
- `isSubtitleLoaded: false` - 字幕加载状态
- `subtitleUrl: ''` - 字幕文件URL
- `ttsAudioUrl: ''` - TTS音频文件URL

### 时间轴清理
- 清除所有类型为 'SUBTITLE' 的时间轴事件
- 保留其他类型的事件不受影响

### 显示状态重置
- `isSubtitleVisible: false` - 字幕显示状态重置为隐藏

## 测试验证

### 测试步骤
1. **进入音频驱动模式**：
   - 上传音频文件
   - 确认预览区域显示字幕内容

2. **切换到输入文本模式**：
   - 点击"输入文本"标签页
   - 检查预览区域字幕是否被清除
   - 检查控制台日志确认清理过程

3. **验证独立性**：
   - 在输入文本模式下输入新文本
   - 确认不受之前音频驱动模式的影响

### 预期结果
- 切换到输入文本模式时，预览区域字幕立即消失
- 控制台显示清理日志：
  ```
  🧹 已清除音频驱动模式的字幕缓存数据
  🎭 已重置字幕显示状态为隐藏
  ✅ 输入文本模式：轻量重置和字幕清理完成
  ```

## 注意事项

### 1. 保持向后兼容
- 不影响音频驱动模式的正常功能
- 不影响输入文本模式的原有逻辑
- 只在模式切换时执行清理

### 2. 错误处理
- 清理操作包含在现有的try-catch结构中
- 即使清理失败也不影响模式切换的基本功能

### 3. 性能考虑
- 清理操作只在真正切换模式时执行
- 避免重复点击同一标签页时的不必要操作

## 相关文件

- `src/views/modules/digitalHuman/components/right_operate/index.vue` - 主要修改文件
- `src/views/modules/digitalHuman/store/digitalHumanStore.js` - 字幕数据管理
- `src/views/modules/digitalHuman/components/PreviewEditor.vue` - 字幕显示组件
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` - 主页面组件

## 修复效果

修复后，用户在数字人编辑器中切换模式时将获得更好的体验：
- 模式之间完全独立，无数据残留
- 预览区域状态清晰，符合用户预期
- 字幕显示逻辑更加健壮和可靠
