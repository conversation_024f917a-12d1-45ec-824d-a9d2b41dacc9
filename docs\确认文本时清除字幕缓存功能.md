# 确认文本时清除字幕缓存功能实现文档

## 功能概述

在数字人编辑器的右侧操作面板中，当用户点击"保存并生成音频"按钮时，在执行现有逻辑的基础上，额外清除之前加载的字幕缓存数据，确保文本输入模式能够正常工作，不受之前音频驱动模式的字幕数据影响。

## 问题背景

在数字人编辑器中，用户可能会在音频驱动模式和文本输入模式之间切换。当从音频驱动模式切换到文本输入模式时，之前加载的字幕数据可能会影响新的文本输入模式的正常工作。因此需要在确认文本时清除这些缓存数据。

## 实现方案

### 修改的文件
- `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue`

### 具体修改内容

#### 1. 导入数字人状态管理store
```javascript
// 导入数字人状态管理store
import { useDigitalHumanStore } from '../../../store/digitalHumanStore'
```

#### 2. 初始化store实例
```javascript
let loginStore = useloginStore() 
// 初始化数字人状态管理store
const digitalHumanStore = useDigitalHumanStore()
```

#### 3. 修改save函数，添加字幕缓存清理逻辑
```javascript
let save=async()=>{
    get_data()
    console.log(input_text_obj.value,'input_text_obj');
    if (!input_text_obj.value.captions.textInfo) return ElMessage.error('请输入字幕内容');
    if (!input_text_obj.value.choose_dub.current_character) return ElMessage.error('请选择配音角色');

    // 🧹 在确认文本时清除之前的字幕缓存数据
    console.log('🧹 确认文本：清除之前的字幕缓存数据...');
    try {
        digitalHumanStore.clearSubtitleData();
        console.log('✅ 字幕缓存清理完成，包括：');
        console.log('   - subtitleData（字幕数据数组）');
        console.log('   - 时间轴上的字幕事件');
        console.log('   - 音频驱动模式相关的字幕缓存');
    } catch (error) {
        console.error('❌ 字幕缓存清理失败:', error);
        // 即使清理失败也继续执行，不影响主要功能
    }

    // 原有的创建视频逻辑保持不变
    let data=await createVideo({
        userId:loginStore?.userId|| '',
        voiceId: input_text_obj.value.choose_dub.current_character.character_id,
        text: input_text_obj.value.captions.textInfo,
        speed: parseFloat(input_text_obj.value.choose_dub.speech),//语速
        vol: parseFloat(input_text_obj.value.choose_dub.volume/100),//音量
        pitch: parseFloat(input_text_obj.value.choose_dub.intonation)//语调
    })
    request_video.value=data.data
    emit_data()
}
```

## 清除的字幕缓存数据

通过调用 `digitalHumanStore.clearSubtitleData()` 方法，会清除以下数据：

### 1. 字幕数据数组
- `subtitleData: []` - 解析后的字幕数据数组
- `currentSubtitle: ''` - 当前显示的字幕文本
- `isSubtitleLoaded: false` - 字幕加载状态重置

### 2. 音频相关数据
- `subtitleUrl: ''` - 字幕文件URL
- `ttsAudioUrl: ''` - TTS音频文件URL
- 清理HTML5 Audio元素资源

### 3. 时间轴事件
- 清除所有类型为 'SUBTITLE' 的时间轴事件
- 保留其他类型的时间轴事件不受影响

## 功能特点

### 1. 保持现有功能不变
- 完全保留"保存并生成音频"按钮的原有逻辑
- 只在执行前添加字幕缓存清理步骤
- 不影响其他功能模块的正常运行

### 2. 错误处理机制
- 使用try-catch包装清理逻辑
- 即使清理失败也不影响主要功能的执行
- 提供详细的控制台日志用于调试

### 3. 清理范围精确
- 只清除字幕相关的缓存数据
- 不影响背景、数字人等其他配置
- 确保文本输入模式的独立性

## 执行流程

```
用户点击"保存并生成音频"按钮
    ↓
获取表单数据 (get_data)
    ↓
验证必要字段（文本内容、配音角色）
    ↓
🧹 清除字幕缓存数据 (digitalHumanStore.clearSubtitleData)
    ↓
调用createVideo API创建音频
    ↓
保存返回数据 (request_video.value)
    ↓
发射数据到父组件 (emit_data)
```

## 使用场景

### 1. 模式切换场景
- 用户从音频驱动模式切换到文本输入模式
- 确保新的文本输入不受之前音频字幕的影响

### 2. 重新生成场景
- 用户修改文本内容后重新生成音频
- 清除之前的字幕数据，避免数据混乱

### 3. 多次操作场景
- 用户多次使用文本输入功能
- 每次确认时都会清理缓存，保证数据干净

## 测试建议

### 1. 基本功能测试
1. 在文本输入模式下输入文本
2. 点击"保存并生成音频"按钮
3. 检查控制台是否显示清理日志
4. 验证音频生成功能是否正常

### 2. 模式切换测试
1. 先使用音频驱动模式加载音频和字幕
2. 切换到文本输入模式
3. 输入新文本并点击"保存并生成音频"
4. 验证之前的字幕数据是否被清除
5. 验证新的文本转语音功能是否正常

### 3. 错误处理测试
1. 在清理过程中模拟错误情况
2. 验证错误处理是否正常
3. 确认主要功能不受影响

## 注意事项

- 清理操作在数据验证之后、API调用之前执行
- 使用详细的控制台日志便于调试和问题排查
- 错误处理确保功能的稳定性
- 不影响其他组件和功能模块的正常运行

## 相关文件

- `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue` - 主要修改文件
- `src/views/modules/digitalHuman/store/digitalHumanStore.js` - 字幕数据管理store
- `docs/字幕开关功能实现.md` - 相关功能文档
- `docs/音频驱动字幕时间轴数据传递.md` - 音频驱动模式文档
