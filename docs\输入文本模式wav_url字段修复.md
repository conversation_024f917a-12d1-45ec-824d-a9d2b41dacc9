# 输入文本模式 wav_url 字段修复文档

## 问题描述

在数字人编辑器的输入文本模式中，发现 `audioJson` 字段的 `wav_url` 属性为空字符串，导致音频URL无法正确传递到音频驱动系统。

### 具体问题表现

**用户提供的数据对象分析：**
- `audioJson.type`: "tts" (文本转语音模式)
- `audioJson.wav_url`: "" (空字符串 - 问题所在)
- `audioJson.wav_text`: 包含完整文本内容
- `audioJson.tts.text`: 包含文本数组
- `audioJson.voiceId`: 11796
- `audioJson.voicePerson`: "伏夏-慵懒"

**影响范围：**
- 音频驱动模式无法获取到正确的音频URL
- 时间轴组件无法加载音频轨道
- 预览播放功能受到影响

## 问题分析

### 根本原因

在 `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue` 文件的 `emit_data` 函数中，`audioJson` 对象的构建逻辑存在问题：

```javascript
// 问题代码
audioJson:{
    type: "tts",
    tts: { /* ... */ },
    wav_url:'',          // ❌ 硬编码为空字符串
    wav_name:"",         // ❌ 硬编码为空字符串
    wav_text:"",         // ❌ 硬编码为空字符串
    // ...
}
```

### 数据流向分析

1. **TTS音频生成流程**：
   ```
   用户输入文本 → createVideo API → 返回音频数据 → request_video.value
   ```

2. **数据传递流程**：
   ```
   request_video.value → audioJson构建 → digital_human_right_option → 右侧操作面板处理
   ```

3. **问题环节**：
   - `createVideo` API 正确返回了音频数据（包含 `audio_file` 字段）
   - 但在构建 `audioJson` 时，没有从 `request_video.value` 中提取音频URL
   - 导致 `wav_url` 字段为空

### API数据结构分析

**createVideo API 返回数据结构：**
```javascript
{
  audio_file: "https://...",        // 生成的音频文件URL
  subtitle_file: "https://...",     // 字幕文件URL
  subtitle_json: [...],             // 字幕JSON数组
  extra_info: {
    audio_length: 23436,            // 音频时长（毫秒）
    audio_sample_rate: 32000,       // 音频采样率
    audio_size: 376513,             // 音频文件大小
    bitrate: 128000,                // 比特率
    word_count: 140                 // 字数统计
  }
}
```

## 解决方案

### 修改的文件
- `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue`

### 具体修改内容

#### 修复前的问题代码
```javascript
audioJson:{
    type: "tts",
    tts: {
        text: [input_text_obj.value?.captions?.textInfo || ""],
        speed: parseFloat(input_text_obj.value?.choose_dub?.speech || 1.0),
        audio_man: "", 
        pitch: parseFloat(input_text_obj.value?.choose_dub?.intonation || 100)
    },
    wav_url:'',                     // ❌ 硬编码空字符串
    wav_name:"",                    // ❌ 硬编码空字符串
    wav_text:"",                    // ❌ 硬编码空字符串
    volume: Math.round(input_text_obj.value?.choose_dub?.volume || 100),
    language: "cn",
    voiceId: characterInfo.id || 1,
    voicePerson: characterInfo.voiceName || "默认音色",
    voiceImg: characterInfo.avatarUrl || ""
}
```

#### 修复后的正确代码
```javascript
// 🔍 调试：检查createVideo API返回的数据结构
console.log('🔍 createVideo API返回数据:', request_video.value);

audioJson:{
    type: "tts",
    tts: {
        text: [input_text_obj.value?.captions?.textInfo || ""],
        speed: parseFloat(input_text_obj.value?.choose_dub?.speech || 1.0),
        audio_man: "", 
        pitch: parseFloat(input_text_obj.value?.choose_dub?.intonation || 100)
    },
    // 🔧 修复：从createVideo API返回数据中获取音频URL
    wav_url: request_video.value?.audio_file || '',
    wav_name: request_video.value?.audio_name || "",
    wav_text: input_text_obj.value?.captions?.textInfo || "",
    // 🔧 修复：从createVideo API返回数据中获取音频时长
    duration: request_video.value?.extra_info?.audio_length || 0,
    volume: Math.round(input_text_obj.value?.choose_dub?.volume || 100),
    language: "cn",
    voiceId: characterInfo.id || 1,
    voicePerson: characterInfo.voiceName || "默认音色",
    voiceImg: characterInfo.avatarUrl || ""
}
```

### 修复的关键点

1. **音频URL获取**：
   - `wav_url: request_video.value?.audio_file || ''`
   - 从 `createVideo` API 返回的 `audio_file` 字段获取音频URL

2. **音频名称获取**：
   - `wav_name: request_video.value?.audio_name || ""`
   - 从API返回数据中获取音频文件名

3. **文本内容填充**：
   - `wav_text: input_text_obj.value?.captions?.textInfo || ""`
   - 使用用户输入的文本内容

4. **音频时长获取**：
   - `duration: request_video.value?.extra_info?.audio_length || 0`
   - 从API返回的额外信息中获取音频时长

5. **调试信息添加**：
   - 添加控制台日志输出API返回数据，便于调试

## 数据流向修复

### 修复前的问题流程
```
createVideo API → request_video.value (包含audio_file)
    ↓
audioJson构建 → wav_url: '' (硬编码空字符串)
    ↓
右侧操作面板 → audioUrl = data.audioJson.wav_url (空字符串)
    ↓
音频驱动系统 → 无法获取音频URL ❌
```

### 修复后的正确流程
```
createVideo API → request_video.value (包含audio_file)
    ↓
audioJson构建 → wav_url: request_video.value.audio_file (正确URL)
    ↓
右侧操作面板 → audioUrl = data.audioJson.wav_url (正确URL)
    ↓
音频驱动系统 → 成功加载音频 ✅
```

## 影响的功能模块

### 1. 音频驱动系统
- 右侧操作面板能够正确获取音频URL
- 时间轴组件能够加载音频轨道
- 音频播放功能正常工作

### 2. 预览编辑器
- 音频同步播放功能
- 字幕与音频的时间轴同步
- 播放控制功能

### 3. 数据传递链路
- `audioJson` → 右侧操作面板 → 主页面组件 → 数字人Store
- 确保音频URL在整个数据链路中正确传递

## 测试验证

### 测试步骤
1. **输入文本并生成音频**：
   - 在输入文本模式下输入文本内容
   - 选择配音角色和参数
   - 点击"保存并生成音频"按钮

2. **检查数据结构**：
   - 查看控制台输出的 `createVideo API返回数据`
   - 确认 `audio_file` 字段包含正确的音频URL

3. **验证音频功能**：
   - 检查时间轴是否显示音频轨道
   - 测试音频播放功能
   - 验证字幕与音频的同步

### 预期结果
- `audioJson.wav_url` 包含正确的音频URL
- 音频驱动系统能够正常工作
- 预览播放功能正常
- 控制台显示正确的调试信息

## 注意事项

### 1. 向后兼容
- 使用可选链操作符 `?.` 确保安全访问
- 提供默认值避免undefined错误
- 不影响其他功能模块

### 2. 错误处理
- 当API返回数据不完整时，使用空字符串作为默认值
- 添加调试日志便于问题排查

### 3. 数据一致性
- 确保 `audioJson` 和 `aduio_data` 中的数据保持一致
- 两个数据源都指向同一个音频文件

## 相关文件

- `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue` - 主要修改文件
- `src/views/modules/digitalHuman/components/right_operate/index.vue` - 数据处理逻辑
- `src/api/digitalHuman.js` - createVideo API定义
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` - 主页面组件
