# 输入文本模式字幕显示时机修复

## 问题描述

### 问题现象
在数字人编辑器的输入文本模式中，存在字幕显示时机的逻辑错误：

- **错误行为**：用户在右侧输入文本框中输入内容时，中间预览区域的字幕会实时显示
- **问题影响**：这种实时显示行为是不正确的，因为此时还没有生成对应的音频和字幕时间轴数据

### 期望的正确行为
- 用户在输入文本框中输入内容时，字幕不应该在中间预览区域实时显示
- 字幕应该只在用户点击"生成音频"按钮，并且createVideo接口调用完成后才显示
- 生成音频后，字幕应该基于接口返回的subtitle_json数组数据，按照时间轴正确显示

## 问题分析

### 数据流分析
问题的根本原因在于实时字幕显示的数据流链路：

```
用户输入文本 
    ↓
captions.vue 的 textInfo 更新
    ↓
input_text/index.vue 的实时监听器触发
    ↓
digital_human_right_option() 调用
    ↓
RightOperate/index.vue 处理并发射 audio-data-loaded 事件
    ↓
DigitalHumanEditorPage.vue 的 handleAudioDataLoaded() 更新 currentInputText
    ↓
PreviewEditor.vue 的 props.inputText 更新
    ↓
subtitleText 计算属性的第四优先级逻辑触发
    ↓
字幕实时显示（错误行为）
```

### 根本原因
在 `PreviewEditor.vue` 的 `subtitleText` 计算属性中，第四优先级逻辑没有判断音频生成状态：

```javascript
// 🔧 问题代码：输入文本时立即显示字幕
if (!isPlaying.value && props.inputText && props.inputText.trim() !== '') {
    return cleanInputText; // ❌ 输入时立即显示，不管是否已生成音频
}
```

## 解决方案

### 方案概述
采用**修改字幕显示条件**的方案，在第四优先级逻辑中添加音频生成状态判断。

### 技术实现

#### 1. 修改字幕显示条件
文件：`src/views/modules/digitalHuman/components/PreviewEditor.vue`

**修复前：**
```javascript
// 🔧 第四优先级：使用输入文本作为字幕（输入文本模式的兜底方案，仅在非播放状态时使用）
if (!isPlaying.value && props.inputText && props.inputText.trim() !== '') {
    const cleanInputText = props.inputText
        .replace(/[\r\n]+/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

    if (process.env.NODE_ENV === 'development') {
        console.log('📝 使用输入文本作为字幕（静态显示）:', cleanInputText.substring(0, 50) + '...');
    }
    return cleanInputText;
}
```

**修复后：**
```javascript
// 🔧 第四优先级：使用输入文本作为字幕（仅在音频生成完成后显示，避免输入时实时显示）
if (!isPlaying.value && props.inputText && props.inputText.trim() !== '' && 
    (subtitleDataArray.length > 0 || store.ttsAudioUrl)) {
    const cleanInputText = props.inputText
        .replace(/[\r\n]+/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

    if (process.env.NODE_ENV === 'development') {
        console.log('📝 使用输入文本作为字幕（音频已生成）:', cleanInputText.substring(0, 50) + '...');
    }
    return cleanInputText;
}
```

#### 2. 关键修改点
**新增判断条件：**
```javascript
(subtitleDataArray.length > 0 || store.ttsAudioUrl)
```

**两个音频生成状态指标：**
1. `subtitleDataArray.length > 0`：有字幕数据数组（createVideo接口返回的subtitle_json）
2. `store.ttsAudioUrl`：有TTS音频URL（音频文件已生成）

#### 3. 调试信息优化
增强调试信息，清晰区分不同状态：

```javascript
if (process.env.NODE_ENV === 'development') {
    if (isPlaying.value) {
        console.log('⏸️ 播放中无匹配字幕，显示空白');
    } else if (props.inputText && props.inputText.trim() !== '' && !subtitleDataArray.length && !store.ttsAudioUrl) {
        console.log('⏳ 输入文本存在但音频未生成，等待音频生成后显示字幕');
    } else {
        console.log('⚠️ 所有字幕数据源都为空');
    }
}
```

## 修复效果

### 输入文本模式
- ✅ **用户输入文本时**：字幕不显示（等待音频生成）
- ✅ **点击"生成音频"后**：字幕正常显示
- ✅ **字幕开关功能**：正常工作

### 音频驱动模式
- ✅ **保持原有功能不变**：字幕按时间轴正确显示
- ✅ **不受影响**：修复只针对输入文本模式

### 静态显示模式
- ✅ **预览功能正常**：非播放状态下的字幕预览功能保持正常

## 技术要点

### 字幕显示优先级
修复后的字幕显示逻辑优先级：

1. **第一优先级**：时间匹配的字幕（播放时使用）
2. **第二优先级**：备选字幕数据（仅非播放状态）
3. **第三优先级**：手动设置字幕（仅非播放状态）
4. **第四优先级**：输入文本字幕（仅在音频生成完成后）

### 状态判断逻辑
```javascript
// 完整的第四优先级判断条件
!isPlaying.value &&                           // 非播放状态
props.inputText && props.inputText.trim() !== '' &&  // 有输入文本
(subtitleDataArray.length > 0 || store.ttsAudioUrl)  // 音频已生成
```

### 兼容性保证
- ✅ **向后兼容**：不影响现有的音频驱动模式功能
- ✅ **功能完整**：保持字幕开关、样式设置等功能正常
- ✅ **数据一致**：字幕数据仍然基于digitalHumanStore的subtitleData

## 测试验证

### 测试场景
1. **输入文本实时性测试**：
   - 在输入框中输入文本
   - 验证预览区域不显示字幕

2. **音频生成后显示测试**：
   - 点击"生成音频"按钮
   - 验证音频生成完成后字幕正常显示

3. **字幕开关测试**：
   - 切换字幕开关状态
   - 验证字幕显示/隐藏功能正常

4. **模式切换测试**：
   - 在输入文本模式和音频驱动模式间切换
   - 验证两种模式的字幕显示逻辑相互独立

### 预期结果
- ✅ 输入文本时不会实时显示字幕
- ✅ 音频生成完成后字幕正常显示
- ✅ 音频驱动模式功能不受影响
- ✅ 字幕开关和样式设置功能正常

## 相关文件

### 主要修改文件
- `src/views/modules/digitalHuman/components/PreviewEditor.vue`

### 相关文件（未修改，仅供参考）
- `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue`
- `src/views/modules/digitalHuman/components/right_operate/input_text/captions.vue`
- `src/views/modules/digitalHuman/components/right_operate/index.vue`
- `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

## 总结

这次修复精确地解决了输入文本模式下字幕实时显示的问题，通过添加音频生成状态判断，确保字幕只在适当的时机显示。修复方案风险低、影响范围小，保持了系统的完整性和向后兼容性。

用户现在可以正常在输入文本框中输入内容，而不会看到实时的字幕显示，只有在点击"生成音频"并且接口调用完成后，字幕才会出现在预览区域。
