# 预览编辑器键盘删除功能文档

## 文档信息

- **创建时间**: 2024年12月19日
- **修改类型**: 功能增强
- **影响模块**: 数字人编辑器 - PreviewEditor组件
- **修改文件**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`

## 功能概述

### 功能作用与目的

键盘删除功能为数字人编辑器提供了便捷的图层删除操作方式，用户可以通过键盘快捷键快速删除当前选中的图层，提升编辑效率。

### 与现有功能的关系

键盘删除功能与现有的右键删除功能**并存而非替换**，为用户提供多种删除操作选择：
- **右键删除**: 通过右键菜单选择删除选项
- **键盘删除**: 通过键盘快捷键直接删除

两种方式功能相同，用户可根据个人习惯选择使用。

### 支持的快捷键

- **Delete键**: 删除当前选中的图层
- **Backspace键**: 删除当前选中的图层（与Delete键功能相同）

## 实现细节

### 核心函数

#### 1. `handleKeyboardDelete` 函数

**位置**: 第4024-4079行

**功能**: 监听键盘事件，处理Delete和Backspace键的删除操作

**核心逻辑**:
```javascript
const handleKeyboardDelete = (event) => {
    // 只处理 Delete 和 Backspace 键
    if (event.key !== 'Delete' && event.key !== 'Backspace') {
        return;
    }

    // 播放状态下禁用键盘删除
    if (isPlaying.value) {
        console.log('⌨️ 播放状态下禁用键盘删除');
        return;
    }

    // 防止在输入框等可编辑元素中误触发删除
    const activeElement = document.activeElement;
    if (activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true'
    )) {
        console.log('⌨️ 在输入框中，跳过键盘删除');
        return;
    }

    // 按优先级检查当前选中的图层
    let targetElement = null;
    if (isSubtitleActive.value && showSubtitle.value) {
        targetElement = 'subtitle';
    } else if (isCharacterActive.value && showCharacter.value) {
        targetElement = 'character';
    } else if (isSecondImageActive.value && showSecondImage.value) {
        targetElement = 'secondImage';
    } else if (isBackgroundModuleActive.value && showBackgroundModule.value) {
        targetElement = 'backgroundModule';
    }

    // 执行删除操作
    if (targetElement) {
        event.preventDefault();
        event.stopPropagation();
        console.log(`⌨️ 键盘删除：准备删除 ${targetElement}`);
        deleteSelectedElement(targetElement);
    }
};
```

#### 2. `deleteSelectedElement` 函数

**位置**: 第3986-4020行

**功能**: 根据图层类型执行具体的删除操作

**实现逻辑**:
```javascript
const deleteSelectedElement = (elementType) => {
    switch (elementType) {
        case 'character':
            showCharacter.value = false;
            isCharacterActive.value = false;
            console.log('⌨️ 键盘删除：数字人角色已删除');
            break;
        case 'secondImage':
            showSecondImage.value = false;
            isSecondImageActive.value = false;
            console.log('⌨️ 键盘删除：装饰图片已删除');
            emit('digital-human-cleared');
            break;
        case 'subtitle':
            showSubtitle.value = false;
            isSubtitleActive.value = false;
            console.log('⌨️ 键盘删除：字幕已删除');
            break;
        case 'backgroundModule':
            showBackgroundModule.value = false;
            isBackgroundModuleActive.value = false;
            console.log('⌨️ 键盘删除：背景模块已删除');
            emit('background-cleared');
            break;
    }
    
    // 发射位置更新事件
    emitPositionUpdate();
};
```

### 事件监听器注册与清理

#### 注册位置

**位置**: `onMounted` 生命周期钩子（第4098行）

```javascript
onMounted(() => {
    // 其他事件监听器...
    
    // ⌨️ 添加键盘删除事件监听器
    document.addEventListener('keydown', handleKeyboardDelete);
    
    console.log('✅ 键盘删除事件监听器已注册');
});
```

#### 清理位置

**位置**: `onUnmounted` 生命周期钩子（第4141行）

```javascript
onUnmounted(() => {
    // 其他清理操作...
    
    // ⌨️ 清理键盘删除事件监听器
    document.removeEventListener('keydown', handleKeyboardDelete);
    
    console.log('🧹 键盘删除事件监听器已清理');
});
```

### 组件暴露接口

**位置**: `defineExpose` 中（第4573-4574行）

```javascript
defineExpose({
    // 其他暴露的方法...
    
    // ⌨️ 键盘删除控制方法
    handleKeyboardDelete,       // 键盘删除事件处理函数
    deleteSelectedElement,      // 删除选中的图层
    
    // 其他方法...
});
```

## 使用方法

### 用户操作步骤

1. **选中图层**: 点击要删除的图层，使其处于选中状态（显示绿色边框）
2. **使用快捷键**: 按下 `Delete` 键或 `Backspace` 键
3. **确认删除**: 图层将立即被删除，边框消失

### 支持删除的图层类型

| 图层类型 | 删除优先级 | 删除效果 |
|----------|------------|----------|
| 字幕层 | 最高 | 隐藏字幕显示，清除选中状态 |
| 数字人层 | 高 | 隐藏数字人角色，清除选中状态 |
| 装饰图片层 | 中 | 隐藏装饰图片，清除选中状态，发射清空事件 |
| 背景层 | 低 | 隐藏背景模块，清除选中状态，发射背景清空事件 |

### 删除优先级顺序

键盘删除功能按照以下优先级顺序检查当前选中的图层：

1. **字幕层** (`subtitle`) - 最高优先级
2. **数字人层** (`character`) - 高优先级  
3. **装饰图片层** (`secondImage`) - 中优先级
4. **背景层** (`backgroundModule`) - 低优先级

当多个图层同时处于选中状态时，系统会按照优先级顺序删除第一个符合条件的图层。

## 技术特性

### 播放状态禁用机制

**功能**: 在音频播放状态下自动禁用键盘删除功能

**实现**: 
```javascript
// 播放状态下禁用键盘删除
if (isPlaying.value) {
    console.log('⌨️ 播放状态下禁用键盘删除');
    return;
}
```

**目的**: 防止用户在播放过程中误删图层，确保播放体验的稳定性。

### 输入框防误触发保护

**功能**: 防止在输入框、文本域等可编辑元素中误触发删除操作

**实现**:
```javascript
// 防止在输入框等可编辑元素中误触发删除
const activeElement = document.activeElement;
if (activeElement && (
    activeElement.tagName === 'INPUT' ||
    activeElement.tagName === 'TEXTAREA' ||
    activeElement.contentEditable === 'true'
)) {
    console.log('⌨️ 在输入框中，跳过键盘删除');
    return;
}
```

**保护范围**:
- `<input>` 输入框
- `<textarea>` 文本域
- `contentEditable="true"` 的可编辑元素

### 与右键删除功能的兼容性

**完全兼容**: 键盘删除功能与现有的右键删除功能完全兼容，不会产生冲突。

**功能一致性**: 两种删除方式执行相同的删除逻辑，确保行为一致。

**事件处理**: 键盘删除会阻止默认行为和事件冒泡，避免与其他键盘事件冲突。

### 事件处理和状态同步机制

**事件处理流程**:
1. 监听 `keydown` 事件
2. 验证按键类型（Delete/Backspace）
3. 检查播放状态和输入框状态
4. 确定目标图层
5. 执行删除操作
6. 更新组件状态
7. 发射位置更新事件

**状态同步**:
- 删除操作会同时更新显示状态和选中状态
- 自动发射 `positionUpdate` 事件通知父组件
- 对于特定图层会发射额外的清空事件

## 代码变更摘要

### 修改的文件

- **文件路径**: `src/views/modules/digitalHuman/components/PreviewEditor.vue`
- **修改行数**: 约150行代码

### 新增函数列表

| 函数名 | 行号范围 | 功能描述 |
|--------|----------|----------|
| `handleKeyboardDelete` | 4024-4079 | 键盘删除事件处理函数 |
| `deleteSelectedElement` | 3986-4020 | 删除选中图层的核心逻辑 |

### 修改的生命周期钩子

| 钩子名 | 修改内容 | 行号 |
|--------|----------|------|
| `onMounted` | 添加键盘事件监听器 | 4098 |
| `onUnmounted` | 清理键盘事件监听器 | 4141 |

### 更新的组件暴露接口

在 `defineExpose` 中新增了两个方法：

```javascript
// ⌨️ 键盘删除控制方法
handleKeyboardDelete,       // 键盘删除事件处理函数
deleteSelectedElement,      // 删除选中的图层
```

### 具体代码修改位置

| 修改类型 | 行号范围 | 描述 |
|----------|----------|------|
| 新增函数 | 3986-4020 | `deleteSelectedElement` 函数定义 |
| 新增函数 | 4024-4079 | `handleKeyboardDelete` 函数定义 |
| 事件注册 | 4098 | 在 `onMounted` 中添加键盘事件监听 |
| 事件清理 | 4141 | 在 `onUnmounted` 中清理键盘事件监听 |
| 接口暴露 | 4573-4574 | 在 `defineExpose` 中暴露新方法 |

## 测试验证

### 功能测试

1. **基本删除测试**
   - 选中不同图层，使用Delete键删除
   - 选中不同图层，使用Backspace键删除
   - 验证删除后图层正确隐藏

2. **优先级测试**
   - 同时选中多个图层，验证按优先级删除
   - 测试字幕层优先级最高
   - 测试背景层优先级最低

3. **状态保护测试**
   - 播放状态下尝试删除，验证功能被禁用
   - 在输入框中尝试删除，验证功能被跳过
   - 在文本域中尝试删除，验证功能被跳过

### 兼容性测试

1. **右键删除兼容性**
   - 验证键盘删除与右键删除功能一致
   - 测试两种方式不会产生冲突

2. **事件处理测试**
   - 验证键盘事件不会影响其他功能
   - 测试事件冒泡被正确阻止

### 边界情况测试

1. **无选中图层**
   - 没有选中任何图层时按删除键，验证无操作

2. **隐藏图层**
   - 选中已隐藏的图层，验证删除操作正确执行

3. **快速操作**
   - 快速连续按删除键，验证操作稳定性

## 性能影响

### 内存使用

- **事件监听器**: 新增一个全局键盘事件监听器，内存占用极小
- **函数开销**: 新增两个函数，代码体积增加约150行

### 性能优化

- **事件过滤**: 只处理Delete和Backspace键，其他按键快速返回
- **状态检查**: 优先检查播放状态，避免不必要的处理
- **元素检查**: 快速检查输入框状态，防止误触发

### 兼容性

- **浏览器支持**: 支持所有现代浏览器的键盘事件
- **Vue版本**: 兼容Vue 3 Composition API
- **框架依赖**: 无额外依赖，使用原生DOM事件

## 总结

键盘删除功能为数字人编辑器提供了便捷的图层删除操作方式，通过合理的优先级设计、完善的状态保护和事件处理机制，确保了功能的稳定性和用户体验。该功能与现有右键删除功能完全兼容，为用户提供了更多操作选择，提升了编辑效率。

---

**文档维护**: 如有后续功能更新或优化，请及时更新本文档。 