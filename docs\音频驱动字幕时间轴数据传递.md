# 音频驱动字幕时间轴数据传递功能实现文档

## 功能概述

实现了音频驱动模式下，将新接口返回的详细字幕数据（包含时间信息）传递给中间预览编辑器的字幕组件，支持精确的时间轴显示。

## 问题描述

之前的音频驱动模式只传递了拼接后的完整文本，没有利用新接口返回的详细时间信息。用户希望能够将每个字幕片段的时间信息（start、end）传递给中间的字幕组件，实现精确的时间轴显示。

## 数据结构

### 新接口返回的数据格式
```javascript
{
  status_code: 200,
  content: {
    result: [
      {
        "id": 0,
        "seek": 0,
        "start": 0.0,
        "end": 6.140000000000001,
        "text": "这不过是新疆平常的一天,太阳从塔什库尔干的世界屋脊照常升起,",
        "tokens": [...],
        "temperature": 0.0,
        "avg_logprob": -0.08410485982894897,
        "compression_ratio": 1.3826714801444044,
        "no_speech_prob": 0.4322985112667084,
        "time_begin": 0.0,
        "time_end": 6140.000000000001
      }
      // ... 更多字幕片段
    ]
  }
}
```

### 提取的关键字段
- `text`: 字幕文本内容
- `start`: 开始时间（秒）
- `end`: 结束时间（秒）

## 实现方案

### 1. 数据保存和处理

#### 修改文件：`upload_aduio.vue`

**添加字幕数据存储：**
```javascript
// 保存新接口返回的详细字幕数据（包含时间信息）
let subtitle_data_with_time=ref([])
```

**处理接口返回数据：**
```javascript
// 保存详细的字幕数据（包含时间信息）
subtitle_data_with_time.value = text.content.result.map(item => ({
    text: item.text || '',
    start: item.start || 0,
    end: item.end || 0,
    startTime: item.start || 0,  // 兼容字段
    endTime: item.end || 0       // 兼容字段
})).filter(item => item.text && item.text.trim() !== '');
```

**更新数据结构：**
```javascript
current_upload_aduio.value = {
    aduio: aduio_ref.value?.aduio_obj,
    textarea: textarea_ref?.value?.textarea,
    subtitle_data: subtitle_data_with_time.value  // 添加详细的字幕数据
}
```

### 2. 数据传递

#### 修改文件：`input_aduio/index.vue`

**传递详细字幕数据：**
```javascript
digital_human_right_option({
    type: 'aduio_captions',
    aduio_data: upload_aduio_ref.value?.current_upload_aduio,
    open_captions: upload_aduio_ref.value?.open_captions,
    choose_music: choose_music_ref.value?.current_music,
    // 新增：详细的字幕数据（包含时间信息）
    subtitle_data_with_time: upload_aduio_ref.value?.current_upload_aduio?.subtitle_data || [],
    audioJson: {
        // ... 其他字段
    }
})
```

### 3. 右侧操作面板处理

#### 修改文件：`right_operate/index.vue`

**传递字幕数据到预览编辑器：**
```javascript
const audioData = {
    audioUrl: audioUrl,
    subtitleData: data.subtitle_data_with_time || null,  // 音频驱动的详细字幕时间轴数据
    subtitleFile: "",
    srtContent: "",
    audioLength: audioLength > 0 ? audioLength * 1000 : null,
    openCaptions: data.open_captions,
    chooseMusic: data.choose_music,
    mode: 'audio_drive',
    extractedText: subtitleText
};
```

### 4. 预览编辑器处理

#### 修改文件：`DigitalHumanEditorPage.vue`

**优先使用详细字幕数据：**
```javascript
// 🎵 音频驱动模式的特殊处理
if (isAudioDriveMode) {
    // 优先使用详细的字幕数据（包含时间信息）
    if (audioData.subtitleData && Array.isArray(audioData.subtitleData) && audioData.subtitleData.length > 0) {
        console.log('🎯 使用详细的字幕时间轴数据:', audioData.subtitleData);
        
        // 转换字幕数据格式以匹配store期望的格式
        const formattedSubtitleData = audioData.subtitleData.map((item, index) => {
            const text = item.text || `字幕片段${index + 1}`;
            const startTime = item.start || item.startTime || 0;
            const endTime = item.end || item.endTime || startTime + 1;
            
            return { text, startTime, endTime };
        });
        
        digitalHumanStore.setSubtitleData(formattedSubtitleData);
    }
    // 如果没有详细数据，使用简单的文本模式（向后兼容）
    else if (audioData.extractedText) {
        // ... 简单文本模式处理
    }
}
```

## 数据流向

```
音频文件上传
    ↓
getDigiAudioJsonTxt 接口返回详细数据
    ↓
upload_aduio.vue 保存 subtitle_data_with_time
    ↓
input_aduio/index.vue 传递 subtitle_data_with_time
    ↓
right_operate/index.vue 包装为 audioData.subtitleData
    ↓
DigitalHumanEditorPage.vue 优先使用详细数据
    ↓
digitalHumanStore.setSubtitleData() 设置到时间轴
    ↓
PreviewEditor.vue 显示精确的时间轴字幕
```

## 功能特点

### 1. 智能数据处理
- **优先级处理**：优先使用详细的时间轴数据，如果没有则回退到简单文本模式
- **数据兼容**：同时支持新的详细数据格式和旧的简单文本格式
- **字段映射**：自动处理不同的时间字段名称（start/startTime, end/endTime）

### 2. 精确时间轴
- **分段显示**：每个字幕片段都有精确的开始和结束时间
- **时间同步**：字幕显示与音频播放完全同步
- **动态切换**：支持字幕在时间轴上的动态显示和隐藏

### 3. 向后兼容
- **兼容旧格式**：保持对原有简单文本模式的支持
- **渐进增强**：新功能不影响现有功能的正常使用
- **错误处理**：当详细数据不可用时，自动回退到简单模式

## 测试建议

1. **上传音频文件**：测试不同格式和时长的音频文件
2. **验证时间轴**：检查字幕是否按照正确的时间显示
3. **播放同步**：验证字幕与音频播放的同步性
4. **开关控制**：测试字幕开关的实时控制功能
5. **兼容性测试**：测试在没有详细数据时的回退机制

## 注意事项

- 只影响音频驱动模式，不修改输入文本模式的逻辑
- 保持了完整的向后兼容性
- 添加了详细的调试日志，便于问题排查
- 数据处理过程中会过滤空文本，确保数据质量
