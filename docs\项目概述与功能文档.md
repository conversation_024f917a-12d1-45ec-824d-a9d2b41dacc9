# 视频编辑器应用 - 项目概述与功能文档

## 项目简介

这是一个基于 Vue 3 和 Vite 构建的现代视频编辑器 Web 应用，旨在提供直观、高效的视频内容创作和编辑体验。该应用集成了 AI 配音、视频编辑、数字人生成等多种功能，为用户提供一站式的视频内容创作解决方案。

## 技术栈

- **前端框架**：Vue 3 (使用 Composition API 和 setup 语法糖)
- **构建工具**：Vite 6.1.0
- **UI 库**：Element Plus 2.9.4
- **状态管理**：Pinia 3.0.1 (使用 pinia-plugin-persistedstate 实现状态持久化)
- **路由管理**：Vue Router 4.5.0
- **样式处理**：SCSS with scoped
- **HTTP 客户端**：Axios 1.7.9
- **其他关键依赖**：
  - pinyin-pro：用于中文拼音处理
  - vue-draggable-resizable：实现拖拽和调整大小功能
  - swiper：轮播图组件
  - marked：Markdown 渲染
  - wasm-media-encoders：媒体编码处理

## 核心功能模块

### 1. AI 配音功能

AI 配音是应用的核心功能之一，提供媲美真人的 AI 配音服务。

**主要特点**：
- 200+ 精品 AI 音色
- 20+ 精细调节功能
- 13 个配音场景
- 支持多音字处理
- 支持读音替换
- 支持停顿调整
- 支持背景音乐和音效添加
- 支持拼音查看
- 支持查找替换
- 支持多语种适配

### 2. 商业配音功能

商业配音功能提供更高质量的配音服务，适用于广告和商业片配音。

**主要特点**：
- 99% 真人音色
- 100% 真人授权
- 专业级音质

### 3. 视频编辑功能

视频编辑功能允许用户进行视频素材选择、智能匹配和视频生成。

**主要功能区域**：
- **匹配模式切换**：支持专辑模式和视频模式
- **视频素材管理**：筛选、选择和上传视频素材
- **预览工具**：实时预览选中的视频素材
- **匹配设置**：控制视频匹配的精确程度和优先级
- **导出设置**：分辨率、音频、配音、字幕和特效选项

### 4. 数字人功能

数字人功能提供完整的数字人视频合成与编辑能力，支持数字人角色、装饰图片和字幕的精确位置控制。

**核心组件**：PreviewEditor

**主要功能**：
- **智能点击选择**：支持多层级元素的精确选择
- **悬停自动选中**：优化用户体验
- **拖拽系统**：高性能拖拽实现
- **拉伸控制**：多方向拉伸支持，保持宽高比
- **元素管理**：数字人角色、装饰图片、字幕系统
- **位置数据系统**：实时位置数据收集和管理
- **状态管理**：集中管理数字人模块状态

### 5. AI 工具集

应用提供多种 AI 辅助工具，提升内容创作效率。

**主要工具**：
- **一键解析视频**：输入视频链接一键解析
- **视频去水印字幕**：智能消除视频中的水印和字幕
- **文案提取**：从视频中提取文案
- **文案创作**：多样化的视频文案模板创作
- **声音克隆**：快速克隆用户专属音色

### 6. 预览功能

预览功能是应用的核心，允许用户在不同编辑阶段看到内容效果。

**主要功能**：
- 显示内容预览
- 管理预览标题和内容
- 跟踪更新时间
- 管理选中的角色、视频和音乐
- 提取视频内容和时间戳

## 项目结构

### 顶层目录结构
```
项目根目录/
├── README.md            # 项目文档
├── index.html           # 入口HTML文件
├── package.json         # 项目依赖配置
├── public/              # 公共资源目录
├── server/              # 服务器相关代码
└── src/                 # 源代码目录
```

### src 目录结构 (核心代码)
```
src/
├── App.vue              # 根组件
├── api/                 # API接口目录
│   ├── account.js       # 账户相关API
│   ├── creation.js      # 创作相关API
│   ├── dubbing.js       # 配音相关API
│   ├── index.js         # API入口
│   ├── material.js      # 素材相关API
│   ├── music.js         # 音乐相关API
│   ├── videoEditing.js  # 视频编辑API
│   └── voiceOver.js     # 配音相关API
├── api_my/              # 额外API接口目录
├── assets/              # 静态资源
├── common/              # 公共代码
├── components/          # 公共组件目录
├── config/              # 配置文件目录
├── router/              # 路由配置
├── stores/              # Pinia状态管理
├── styles/              # 全局样式
├── utils/               # 工具函数
└── views/               # 视图/页面组件
    ├── Editor/          # 编辑器页面
    ├── VideoEditing/    # 视频编辑页面
    ├── account/         # 账户相关页面
    ├── layout/          # 布局组件
    └── modules/         # 功能模块
```

## 最近更新功能

### 数字人编辑器交互优化
- **右键菜单功能**: 支持一键删除数字人、装饰图片、字幕等元素
- **导航栏定制化**: 专门设计的导航栏，包含返回按钮和项目标题实时编辑功能
- **项目标题管理**: 支持在编辑过程中随时修改项目名称

### 主页展示功能增强
- **轮播图Banner系统**: 响应式轮播图组件，支持多图轮播和外链跳转
- **响应式布局优化**: 完整的响应式Banner定位系统
- **视觉体验提升**: 根据不同屏幕尺寸动态调整界面布局

### 用户体验及流程优化
- **登录校验增强**: 为核心功能入口增加前置登录校验
- **用户声明"不再提示"**: 优化用户声明弹窗，提升用户体验

### 数字人编辑器拉伸功能修复
- **问题修复**: 解决背景层和数字人拉伸问题
- **技术改进**: 修正拉伸基准计算逻辑，优化所有可拉伸元素的拉伸逻辑

### 口型同步预览提示
- **功能描述**: 首次点击播放时显示提示，告知预览时暂不支持口型对齐
- **用户体验**: 用户可选择"不再提示"，此选择将被浏览器记住

### 视频素材管理优化
- **视频删除与选中状态同步**: 优化视频删除逻辑
- **修复视频选中状态持久化问题**: 解决删除视频后刷新页面仍会恢复选中状态的问题

### HeaderBar组件优化
- **数据安全保障**: 添加未保存更改检测和提示功能
- **美化弹窗样式**: 重新设计消息框和确认对话框
- **增强错误处理**: 添加详细的错误信息提取和显示功能
- **改进加载状态**: 优化加载状态管理，防止重复操作
- **响应式设计**: 添加移动设备适配

### 数字人预览编辑器位置数据系统
- **位置数据获取功能**: 支持数字人角色、装饰图片、字幕的精确位置追踪
- **组件通信优化**: 使用defineEmits和defineExpose提供完整的父子组件通信方案
- **数据持久化支持**: 支持布局配置的保存、恢复、导入导出功能

## 开发指南

### 安装依赖
```bash
npm install
# 或
yarn
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 环境配置
项目支持多环境配置:
- 开发环境: `npm run dev`
- 生产环境: `npm run build`
- UAT环境: `npm run uat`
- PRO环境: `npm run pro`

## 项目规范

- 使用Vue 3的Composition API进行开发
- 使用setup语法糖简化组件编写
- 组件样式使用SCSS并添加scoped属性隔离
- 使用Pinia进行状态管理
- 模块化组织代码，保持组件的可复用性
- 增强用户体验：添加加载状态、操作确认和错误处理

## 自动化测试

项目使用 Playwright 进行自动化测试，支持多浏览器测试和UI模式测试。

### 运行测试
```bash
# 运行所有测试
npx playwright test

# 在UI模式下运行测试
npx playwright test --ui

# 运行特定的测试文件
npx playwright test tests/example.spec.js
```

## 常见问题解决

### 时间相关问题
如果遇到日期计算、时区处理、日历系统等时间相关问题，请参考 `progress.md` 文件中的"时间相关问题解决经验总结"章节。

### 浏览器兼容性
项目针对不同浏览器（特别是Safari）进行了特殊处理，包括缩放、全屏和布局适配等。

### 响应式设计
项目使用了响应式设计，可以适配不同屏幕尺寸，从移动设备到大型桌面显示器。