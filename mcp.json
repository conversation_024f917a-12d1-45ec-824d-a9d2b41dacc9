        "sequential-thinking": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-sequential-thinking"
            ]
        },
        "JIL": {
            "command": "uvx",
            "args": ["mcp-feedback-enhanced@latest"],
            "timeout": 600,
            "env": {
                "MCP_DEBUG": "false",
                "MCP_WEB_HOST": "127.0.0.1",
                "MCP_WEB_PORT": "8765",
                "MCP_LANGUAGE": "zh-CN"
            },
            "autoApprove": ["interactive_feedback"]
        },
        "shrimp-task-manager": {
            "command": "npx",
            "args": [
                "-y",
                "mcp-shrimp-task-manager"
            ],
            "env": {
                "DATA_DIR": "/Users/<USER>/Dev/mcp-shrimp-task-manager/data",
                "TEMPLATES_USE": "en",
                "ENABLE_GUI": "true"
            }
        },
        "mcp-server-time": {
            "command": "uvx",
            "args": [
                "mcp-server-time",
                "--local-timezone=Asia/Shanghai"
            ]
        } 