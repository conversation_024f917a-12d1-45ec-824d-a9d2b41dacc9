{"name": "vite-project-pc-js", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "pro": "vite build --mode pro", "uat": "vite build --mode uat", "serve_pro": "vite --mode pro --port 8888", "serve_uat": "vite --mode uat --port 8099", "preview": "vite preview --mode staging", "subset": "font-spider ./index.html"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@formily/vue": "^2.3.3", "@playwright/test": "^1.52.0", "@stagewise-plugins/vue": "^0.6.2", "@vue-dnd-kit/core": "^1.6.0", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "core-js": "^3.41.0", "domhandler": "^5.0.3", "element-plus": "^2.9.4", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "marked": "^15.0.7", "mitt": "^3.0.1", "pinia": "^3.0.1", "pinyin": "^4.0.0-alpha.2", "pinyin-pro": "^3.26.0", "qrcode.vue": "^3.6.0", "qs": "^6.14.0", "regenerator-runtime": "^0.14.1", "swiper": "^11.2.5", "vue": "^3.5.13", "vue-draggable-resizable": "^3.0.0", "vue-router": "^4.5.0", "vue-typer-next": "^1.0.1", "wasm-media-encoders": "^0.7.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@stagewise/toolbar-vue": "^0.6.2", "@types/pinyin": "^2.10.2", "@vitejs/plugin-legacy": "^6.1.0", "@vitejs/plugin-vue": "^5.2.1", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "font-spider": "^1.3.5", "htmlparser2": "^10.0.0", "husky": "^9.1.7", "nanoid": "^5.1.2", "pinia-plugin-persistedstate": "^4.2.0", "sass": "^1.85.0", "sass-loader": "^16.0.5", "terser": "^5.39.0", "vite": "^6.1.0", "vite-plugin-vue-inspector": "^5.3.1", "vite-plugin-vue-mcp": "^0.3.2", "vue-svg-loader": "^0.16.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}