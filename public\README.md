# 配音助手/视频编辑器应用 - Vue 3 + Vite

这是一个基于Vue 3和Vite构建的现代视频编辑器Web应用，旨在提供直观、高效的视频内容创作和编辑体验。

## 项目概述

该应用提供了以下核心功能：
- 内容创作编辑
- 视频素材管理和编辑
- 音频/音乐添加与管理
- 配音角色选择
- 视频导入到云剪辑进行精准编辑
- 保存和导出功能

## 最近更新

### 2025年3月29日最新功能

#### 云剪跳转功能优化
- **完善云剪跳转功能**：
  - 修复了首页"开始剪辑"按钮的跳转功能，现在能够正确传递 token 到外部云剪网站
  - 修复了"我的作品"页面中点击专业云剪类型作品的跳转功能
  - 确保从 localStorage 中获取用户 token，而不是使用硬编码的值
  - 从 API 返回的作品数据中提取 projectId 参数，并传递给外部云剪网站

### 2023年功能

#### HeaderBar组件优化
- **数据安全保障**：添加未保存更改检测和提示功能，防止用户意外丢失数据
- **美化弹窗样式**：重新设计消息框和确认对话框，提供更精美的视觉体验
  - 添加了动画效果和过渡
  - 优化了标题和关闭按钮的对齐
  - 改进了按钮交互体验和悬浮效果
- **增强错误处理**：添加详细的错误信息提取和显示功能
- **改进加载状态**：优化加载状态管理，防止重复操作
- **响应式设计**：添加移动设备适配，确保在各种屏幕尺寸下的良好体验

#### 用户体验提升
- **操作安全性**：重要操作前添加确认对话框，防止误操作
- **视觉反馈**：优化成功/错误消息提示样式，提供更直观的操作反馈
- **交互优化**：按钮状态优化，提供加载和禁用状态的视觉区分

## 技术栈

- **前端框架**：Vue 3.5.13 (使用Composition API和setup语法糖)
- **构建工具**：Vite 6.1.0
- **UI库**：Element Plus 2.9.4
- **状态管理**：Pinia 3.0.1
- **路由管理**：Vue Router 4.5.0
- **样式处理**：SCSS with scoped
- **HTTP客户端**：Axios 1.7.9
- **其他主要依赖**：
  - js-cookie 3.0.5
  - lodash 4.17.21
  - mitt 3.0.1 (事件总线)
  - pinyin-pro 3.26.0
  - swiper 11.2.5

## 项目结构

```
├── src/                     # 源代码
│   ├── api/                 # API接口
│   ├── assets/              # 静态资源
│   ├── common/              # 通用工具和功能
│   │   └── utils/           # 工具函数，包括事件总线
│   ├── components/          # 通用组件
│   │   └── global/          # 全局组件
│   ├── config/              # 配置文件
│   ├── router/              # 路由配置
│   ├── stores/              # Pinia状态管理
│   │   ├── modules/         # 模块化的store
│   │   └── previewStore.js  # 预览内容状态管理
│   ├── utils/               # 工具函数
│   ├── views/               # 页面组件
│   │   ├── constant/        # 常量组件（如登录弹窗、音乐弹窗等）
│   │   ├── layout/          # 布局组件
│   │   └── Editor/          # 编辑器相关页面
│   │       ├── components/  # 编辑器组件
│   │       │   └── common/  # 通用编辑器组件
│   │       ├── ContentCreation/ # 内容创作模块
│   │       ├── MusicAudio/  # 音乐音频模块 
│   │       ├── VideoEditing/ # 视频编辑模块
│   │       └── VoiceOver/   # 配音模块
│   ├── App.vue              # 根组件
│   └── main.js              # 应用入口
├── public/                  # 公共资源
├── .env.development         # 开发环境配置
├── .env.production          # 生产环境配置
├── .env.pro                 # PRO环境配置
├── .env.uat                 # UAT环境配置
├── .env.staging             # 预览环境配置
└── vite.config.js           # Vite配置
```

## 核心功能说明

### 1. 预览功能 (PreviewStore)

预览功能是应用的核心，允许用户在不同编辑阶段看到内容效果。

**主要功能**:
- 显示内容预览
- 管理预览标题和内容
- 跟踪更新时间
- 管理选中的角色、视频和音乐
- 提取视频内容和时间戳

### 2. 标题栏 (HeaderBar)

标题栏提供应用的主要操作按钮和导航功能。

**主要功能**:
- 显示当前页面标题
- 提供返回首页按钮（带未保存内容检测）
- 提供保存功能（带错误处理和加载状态）
- 提供"去精剪"功能，可将视频导入到云剪辑中进行精准剪辑
- 美观的UI设计和响应式布局

### 3. 音乐管理 (MusicStore)

音乐管理功能允许用户添加、删除和控制背景音乐。

**主要功能**:
- 添加音乐
- 删除音乐
- 控制音乐播放状态
- 管理音乐音量
- 更新音乐URL

### 4. 登录和用户管理

应用集成了用户登录和授权功能：
- 全局登录弹窗
- 用户token管理
- 基于localStorage的持久化登录状态

## 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 环境配置
项目支持多环境配置:
- 开发环境: `npm run dev`
- 生产环境: `npm run build`
- UAT环境: `npm run uat`
- PRO环境: `npm run pro`
- 本地预览Pro环境: `npm run serve_pro`
- 本地预览UAT环境: `npm run serve_uat`

## 项目规范

- 使用Vue 3的Composition API进行开发
- 使用setup语法糖简化组件编写
- 组件样式使用SCSS并添加scoped属性隔离
- 使用Pinia进行状态管理
- 使用eventBus (mitt库) 进行跨组件通信
- 模块化组织代码，保持组件的可复用性
- 增强用户体验：添加加载状态、操作确认和错误处理

## 待开发功能

1. 继续优化用户体验
2. 完善其他功能模块
3. 增强应用性能和稳定性
4. 检查其他跳转功能，确保参数传递正确
5. 测试各种用户场景下的功能稳定性
