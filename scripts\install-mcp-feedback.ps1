# MCP Feedback Enhanced 快速安装脚本
# 使用方法：在 PowerShell 中运行 .\scripts\install-mcp-feedback.ps1

Write-Host "🚀 开始安装 MCP Feedback Enhanced..." -ForegroundColor Green

# 检查 Python 环境
Write-Host "📋 检查 Python 环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python 已安装: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python 未安装或未添加到 PATH" -ForegroundColor Red
    Write-Host "请先安装 Python 并重启终端" -ForegroundColor Red
    exit 1
}

# 检查 pip
Write-Host "📋 检查 pip..." -ForegroundColor Yellow
try {
    $pipVersion = pip --version 2>&1
    Write-Host "✅ pip 已安装: $pipVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ pip 未找到" -ForegroundColor Red
    exit 1
}

# 安装 uv
Write-Host "📦 安装 uv 包管理器..." -ForegroundColor Yellow
try {
    pip install uv
    Write-Host "✅ uv 安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ uv 安装失败" -ForegroundColor Red
    exit 1
}

# 验证 uv 安装
Write-Host "📋 验证 uv 安装..." -ForegroundColor Yellow
try {
    $uvVersion = uv --version 2>&1
    Write-Host "✅ uv 验证成功: $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ uv 验证失败，请重启终端后重试" -ForegroundColor Red
    exit 1
}

# 安装 mcp-feedback-enhanced
Write-Host "📦 安装 mcp-feedback-enhanced..." -ForegroundColor Yellow
try {
    uvx mcp-feedback-enhanced@latest version
    Write-Host "✅ mcp-feedback-enhanced 安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ mcp-feedback-enhanced 安装失败" -ForegroundColor Red
    exit 1
}

# 测试 Web UI
Write-Host "🌐 测试 Web UI..." -ForegroundColor Yellow
Write-Host "即将打开 Web UI 测试界面，请在浏览器中验证功能" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 可以停止测试" -ForegroundColor Cyan
Start-Sleep -Seconds 3

try {
    uvx mcp-feedback-enhanced@latest test --web
} catch {
    Write-Host "❌ Web UI 测试失败" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 MCP Feedback Enhanced 安装完成！" -ForegroundColor Green
Write-Host "📝 配置文件已创建在 .augment/mcp-config.json" -ForegroundColor Cyan
Write-Host "🔧 如需桌面模式，请使用 .augment/mcp-config-desktop.json" -ForegroundColor Cyan
