const fs = require('fs');
const path = require('path');

/**
 * 图片优化脚本
 * 用于在构建过程中优化图片资源
 */
class ImageOptimizer {
    constructor() {
        this.imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'];
        this.totalOriginalSize = 0;
        this.totalOptimizedSize = 0;
        this.processedFiles = 0;
    }

    /**
     * 递归扫描目录中的图片文件
     */
    async scanDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        const imageFiles = [];

        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            
            try {
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                    // 跳过node_modules等目录
                    if (item !== 'node_modules' && item !== '.git' && item !== 'dist' && item !== 'temp') {
                        const subImages = await this.scanDirectory(fullPath);
                        imageFiles.push(...subImages);
                    }
                } else if (stat.isFile()) {
                    const ext = path.extname(item).toLowerCase();
                    if (this.imageExtensions.includes(ext)) {
                        imageFiles.push({
                            path: fullPath,
                            name: item,
                            size: stat.size,
                            extension: ext
                        });
                    }
                }
            } catch (error) {
                // 跳过无法访问的文件
                console.warn(`无法访问文件: ${fullPath}`);
            }
        }

        return imageFiles;
    }

    /**
     * 获取图片信息摘要
     */
    getImageSummary(images) {
        const summary = {
            total: images.length,
            totalSize: 0,
            byExtension: {},
            largeFiles: []
        };

        images.forEach(img => {
            summary.totalSize += img.size;
            
            if (!summary.byExtension[img.extension]) {
                summary.byExtension[img.extension] = { count: 0, size: 0 };
            }
            summary.byExtension[img.extension].count++;
            summary.byExtension[img.extension].size += img.size;

            // 标记大于500KB的文件
            if (img.size > 500 * 1024) {
                summary.largeFiles.push(img);
            }
        });

        return summary;
    }

    /**
     * 格式化文件大小
     */
    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 生成优化建议
     */
    generateOptimizationSuggestions(summary) {
        const suggestions = [];

        // 检查大文件
        if (summary.largeFiles.length > 0) {
            suggestions.push({
                type: 'large-files',
                message: `发现 ${summary.largeFiles.length} 个大于500KB的图片文件，建议压缩`,
                files: summary.largeFiles.map(f => ({
                    name: f.name,
                    path: f.path,
                    size: this.formatSize(f.size)
                }))
            });
        }

        // 检查格式优化
        if (summary.byExtension['.png'] && summary.byExtension['.png'].size > 1024 * 1024) {
            suggestions.push({
                type: 'format-optimization',
                message: 'PNG图片总大小超过1MB，建议转换为WebP格式以减小体积'
            });
        }

        if (summary.byExtension['.jpg'] || summary.byExtension['.jpeg']) {
            const jpegSize = (summary.byExtension['.jpg']?.size || 0) + 
                           (summary.byExtension['.jpeg']?.size || 0);
            if (jpegSize > 2 * 1024 * 1024) {
                suggestions.push({
                    type: 'jpeg-optimization',
                    message: 'JPEG图片总大小超过2MB，建议调整压缩质量到75%'
                });
            }
        }

        return suggestions;
    }

    /**
     * 执行图片分析和优化建议
     */
    async analyze() {
        console.log('🔍 开始分析项目中的图片资源...\n');

        const projectRoot = path.resolve(__dirname, '..');
        const images = await this.scanDirectory(projectRoot);
        const summary = this.getImageSummary(images);

        // 输出分析结果
        console.log('📊 图片资源分析报告:');
        console.log(`┌─ 总计: ${summary.total} 个图片文件`);
        console.log(`├─ 总大小: ${this.formatSize(summary.totalSize)}`);
        console.log('├─ 按格式分布:');
        
        Object.entries(summary.byExtension).forEach(([ext, data]) => {
            console.log(`│  ${ext}: ${data.count} 个文件, ${this.formatSize(data.size)}`);
        });

        if (summary.largeFiles.length > 0) {
            console.log(`└─ 大文件 (>500KB): ${summary.largeFiles.length} 个\n`);
            
            console.log('📝 大文件详情:');
            summary.largeFiles.forEach((file, index) => {
                const relativePath = path.relative(projectRoot, file.path);
                console.log(`${index + 1}. ${relativePath} (${this.formatSize(file.size)})`);
            });
        } else {
            console.log('└─ 未发现超过500KB的大文件\n');
        }

        // 生成优化建议
        const suggestions = this.generateOptimizationSuggestions(summary);
        if (suggestions.length > 0) {
            console.log('💡 优化建议:');
            suggestions.forEach((suggestion, index) => {
                console.log(`${index + 1}. ${suggestion.message}`);
                if (suggestion.files) {
                    suggestion.files.forEach(file => {
                        console.log(`   - ${file.name} (${file.size})`);
                    });
                }
            });
        } else {
            console.log('💡 图片资源已经相对优化，无需特殊处理');
        }

        console.log('\n✨ 当前Vite配置已优化:');
        console.log('├─ 小于2KB的图片将内联为base64');
        console.log('├─ 图片文件将添加hash用于缓存');
        console.log('├─ 生产环境启用Terser代码压缩');
        console.log('├─ CSS和SCSS启用压缩');
        console.log('└─ 静态资源按类型分类存放');

        console.log('\n🚀 构建优化提示:');
        console.log('- 运行 npm run build 进行生产构建');
        console.log('- 运行 npm run pro 进行生产环境构建');
        console.log('- 运行 npm run uat 进行测试环境构建');
        console.log('- 所有构建模式都将应用图片优化配置\n');

        return summary;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const optimizer = new ImageOptimizer();
    optimizer.analyze().catch(console.error);
}

module.exports = ImageOptimizer; 