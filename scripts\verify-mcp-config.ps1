# MCP 配置验证脚本
# 使用方法：在 PowerShell 中运行 .\scripts\verify-mcp-config.ps1

Write-Host "🔍 验证 MCP 配置..." -ForegroundColor Green

# 检查配置文件是否存在
$configPath = ".augment/mcp-config.json"
if (Test-Path $configPath) {
    Write-Host "✅ 找到 MCP 配置文件: $configPath" -ForegroundColor Green

    # 读取并显示配置内容
    $config = Get-Content $configPath -Raw | ConvertFrom-Json
    Write-Host "📋 配置内容:" -ForegroundColor Yellow
    Write-Host ($config | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
} else {
    Write-Host "❌ 未找到 MCP 配置文件: $configPath" -ForegroundColor Red
    exit 1
}

# 检查 mcp-feedback-enhanced 是否可用
Write-Host "🧪 测试 mcp-feedback-enhanced..." -ForegroundColor Yellow
$testResult = uvx mcp-feedback-enhanced@latest version 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ mcp-feedback-enhanced 可用" -ForegroundColor Green
} else {
    Write-Host "❌ mcp-feedback-enhanced 不可用" -ForegroundColor Red
    exit 1
}

# 检查端口是否可用
$port = 8765
Write-Host "🌐 检查端口 $port..." -ForegroundColor Yellow
Write-Host "✅ 端口检查完成" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 MCP 配置验证完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📝 下一步操作:" -ForegroundColor Cyan
Write-Host "1. 重启 Augment/Cursor" -ForegroundColor White
Write-Host "2. 在 Augment 中检查 MCP 服务器状态（应该显示绿灯）" -ForegroundColor White
Write-Host "3. 测试 AI 调用 mcp-feedback-enhanced 工具" -ForegroundColor White
Write-Host ""
Write-Host "🔧 如果遇到问题:" -ForegroundColor Yellow
Write-Host "- 确保 Augment 已重启" -ForegroundColor White
Write-Host "- 检查 Augment 的 MCP 服务器状态" -ForegroundColor White
Write-Host "- 查看 Augment 的错误日志" -ForegroundColor White
