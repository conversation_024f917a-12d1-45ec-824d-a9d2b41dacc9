<script setup>
import { ref, provide, onMounted ,onBeforeUnmount, nextTick,watch, shallowRef,onUnmounted, computed } from 'vue'
import GlobalModal from "@/views/constant/login/login.vue";
import GlobalimportLetter from "@/views/constant/importLetters/importLetter.vue";
import AIgenerationModal from "@/views/constant/AIgeneration/AIgeneration.vue";
import musicModal from "@/views/constant/musicModal/musicModal.vue";
import soundEffects from "@/views/constant/musicModal/soundEffects.vue";
import { storeToRefs } from "pinia";
import { useMenuStore } from '@/stores/index.js'
const menuStore = useMenuStore()
const { active } = storeToRefs(menuStore)
import eventBus from '@/common/utils/eventBus';
import { useRouter, useRoute } from 'vue-router';
import { cacheHandle } from "@/common/utils/cache.js";
// import suspension from '@/views/layout/components/suspension/index.vue'
import giftPack from '@/views/layout/components/gift_pack/gift_pack_dialog.vue'
import { useloginStore } from '@/stores/login'
import limitTime from '@/views/layout/components/limit_time/index.vue'
import { useCheckWithin24h } from '@/views/layout/components/limit_time/limit_time.js'

let loginStore = useloginStore() 
let { buildTime, buyTime, isBuildWithin24h, isBuyWithin24h, getStatus } = useCheckWithin24h(loginStore)
// Stagewise 工具栏配置 - 仅在开发环境加载
const isDev = import.meta.env.MODE === 'development';

const StagewiseToolbar = shallowRef(null);
const stagewiseConfig = ref({
  plugins: [],
  // 可以根据需要添加其他配置选项
});
console.log(isDev,'isDev');

// 仅在开发环境下动态导入和初始化 Stagewise 工具栏
if (isDev) {
  import('@stagewise/toolbar-vue')
    .then((module) => {
      StagewiseToolbar.value = module.StagewiseToolbar;
      console.log('Stagewise toolbar loaded successfully in development mode');
    })
    .catch(err => {
      console.error('Failed to load Stagewise toolbar:', err);
    });
}

// 检测当前操作系统
const isMacOS = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
console.log('当前运行平台:', isMacOS ? 'Mac OS' : 'Windows')

// 创建响应式引用存储动态导入的函数
const scaleModule = ref(null)
const updateScaleFactor = ref(null)
const removeEventListeners = ref(null)

// 清理之前的缩放工具事件监听器
const cleanupPreviousScaleListeners = () => {
  console.log('App.vue: 清理之前的缩放工具事件监听器');
  
  // 如果之前有导入的缩放模块，调用其清理函数
  if (removeEventListeners.value && typeof removeEventListeners.value === 'function') {
    try {
      removeEventListeners.value();
      console.log('App.vue: 成功调用缩放工具的清理函数');
    } catch (error) {
      console.error('App.vue: 调用缩放工具清理函数时出错:', error);
    }
  }
  
  // 重置引用
  scaleModule.value = null;
  updateScaleFactor.value = null;
  removeEventListeners.value = null;
}

// console.log(import.meta.env.VITE_API_BASE_URL);
const scale = (() => {
  return window.innerWidth/1920
})();

let scaleElement=(el)=>{
  if (el.dataset.scaled === 'true') return;
  el.style.transformOrigin = 'top left';
  el.style.transform = `scale(${scale})`;
  el.dataset.scaled = 'true';
}

let batchScale=(nodes)=>{
  if (!nodes.length) return;
  requestAnimationFrame(() => {
    nodes.forEach(scaleElement);
  });
}

let observer;
let findDeepChild=(root, selector)=>{
  if (!root || !root.children) return null;

  for (const child of root.children) {
    console.log(child,'child');
    
    if (child.matches(selector)) {
      return child;
    }
    const found = findDeepChild(child, selector);
    if (found) return found;
  }

  return null;
}
const observeOverlay = (overlay) => {
  const observer = new MutationObserver(() => {
    const style = window.getComputedStyle(overlay);
    const dialogs = overlay.querySelectorAll('.el-dialog');
    // if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
    //   console.log(999);
      
    //   dialogs.forEach(dialog => {
    //     setTimeout(()=>{
    //       dialog.style.opacity = '1';
    //     },200)
    //   });
    // } else {
    //   dialogs.forEach(dialog => {
    //       dialog.style.opacity = 0;
    //   });
    // }
  })
  observer.observe(overlay, { attributes: true, attributeFilter: ['class', 'style'] });
}
let getAllOverlay=()=>{
    // 先处理页面上已有的 .el-overlay 元素
    setTimeout(()=>{
    const existingOverlays = document.querySelectorAll('.el-overlay');
    existingOverlays.forEach(node => {
      // console.log(node,'existingOverlays');
      
      handleOverlayNode(node);
    });
  },200)
}


// let dialogStyle=()=>{

//   const nodesToScale = [];
//   // 统一给某个节点的所有后代元素加样式
//   observer = new MutationObserver(mutations => {
//     mutations.forEach(mutation => {
//       mutation.addedNodes.forEach(node => {
       
        
//         if (node.nodeType !== 1) return;
     
//         // 监听 Element Plus 通知和 Dialog 根节点
//           if(node.classList.contains('el-overlay')){
//             const style = window.getComputedStyle(node);
//               // 遍历所有后代元素，给它们添加样式
//             const allDescendants = node.querySelectorAll('*');
//             allDescendants.forEach(el => {
          
//               // if (el.classList.contains('el-dialog')) {
              
//                 console.log(scale,'scale');
                
//                 el.style.transform = `scale(${scale}) translateZ(0)`;
//                 el.style.overflow = `hidden`;
//               // }
            
//               // 你也可以加缩放，注意缩放叠加可能导致问题，谨慎使用
//               // el.style.transform = `scale(${scale})`;
//             });
//             observeOverlay(node);
//             }
//           })
         
         
   
//       });
//     });

//     if (nodesToScale.length > 0) {
//       batchScale(nodesToScale.splice(0));
//     }


//   // 只监听 body 的直接子节点，性能开销小
//   observer.observe(document.body, { childList: true });



//   // 页面初始化时，先给已有的 el-popper 元素后代加样式


// }
let dialogStyle = () => {
  const nodesToScale = [];
  getAllOverlay()



  // 监听后续动态添加的节点
  observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType !== 1) return;

        if (node.classList.contains('el-overlay')) {
          handleOverlayNode(node);
        }

        const overlays = node.querySelectorAll('.el-overlay');
        overlays.forEach(overlayNode => {
          handleOverlayNode(overlayNode);
        });
      });
    });

    if (nodesToScale.length > 0) {
      batchScale(nodesToScale.splice(0));
    }
  });

  observer.observe(document.body, { childList: true });

  
}
let  handleOverlayNode=(node)=>{
    // console.log(node, 'node');
    const style = window.getComputedStyle(node);
    const allDescendants = node.querySelectorAll('*');
    allDescendants.forEach(el => {
      // console.log(scale, 'scale');
      // el.style.transform = `scale(${scale}) translateZ(0)`;
      // el.style.overflow = `hidden`;
    });
    observeOverlay(node);
  }
let applyStylesToDescendants=(root)=>{
  nextTick(()=>{
    if (!root) return;
   

    
    const allDescendants = root.querySelectorAll('*');
   
    allDescendants.forEach(el => {
      el.style.setProperty('transform', `scale(${scale}) translateZ(0)`, 'important');
      el.style.setProperty('overflow', 'hidden', 'important');
    });
    
  })
 
}
function updateTransformByScreenCenter(element, scale) {
  // 简单标记元素，不做任何样式修改
  element.classList.add('normalized-popper');
}
let observePopper=()=>{
  // 计算最佳缩放因子的函数
  const calculateZoomFactor = () => {
    // 基准设计宽度
    const designWidth = 1920;
    // 当前视口宽度
    const viewportWidth = window.innerWidth;
    // 计算基础比例
    const baseRatio = viewportWidth / designWidth;
    
    // 计算适配比例 - 这里可以根据实际情况调整
    let adaptiveRatio;
    
    if (viewportWidth >= 1600) {
      // 大屏幕
      adaptiveRatio = 1;
    } else if (viewportWidth >= 1280) {
      // 中等屏幕
      adaptiveRatio = 0.5;
    } else if (viewportWidth >= 1024) {
      // 小屏幕
      adaptiveRatio = 0.5;
    } else {
      // 超小屏幕
      adaptiveRatio = 0.5;
    }
    
    // 最终缩放比例 = 适配比例 / 全局缩放比例
    return adaptiveRatio / scale;
  };
  
  const popperObserver = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType !== 1) return;
        
        if (node.classList.contains('el-popper')) {
          // 仅添加类名，样式由CSS控制
          node.classList.add('normalized-popper');
        }
      });
    });
  });
  
  // 监听body及其子元素的变化
  popperObserver.observe(document.body, { childList: true, subtree: true });

  // 立即处理已存在的popper元素
  const poppers = document.querySelectorAll('.el-popper');
  poppers.forEach(el => {
    el.classList.add('normalized-popper');
  });
  
  // 获取当前的动态缩放因子
  const zoomFactor = calculateZoomFactor();
  
  // 创建全局样式表
  const styleEl = document.createElement('style');

  document.head.appendChild(styleEl);
  
  // 监听窗口大小变化，动态更新样式
  window.addEventListener('resize', () => {
    const newZoomFactor = calculateZoomFactor();
    const updatedStyleEl = document.createElement('style');
    updatedStyleEl.textContent = `
      .el-popper[role="menu"], .el-popper[role="tooltip"] {
        zoom: ${newZoomFactor} !important;
      }
    `;
    document.head.appendChild(updatedStyleEl);
  }, { passive: true });
}
// let observePopper=()=>{
//   const styleEl = document.createElement('style');
//     styleEl.textContent = `
//     /* 强制popper尺寸恢复正常 */
//     .el-popper, .el-popper.normalized-popper {
//       transform: none !important;
//       transform-origin: top left !important;
//       font-size: 14px !important;
//       line-height: normal !important;
//     }
    
//     /* 特殊处理下拉菜单 */
//     .el-popper[role="menu"], .el-popper[role="tooltip"] {
//       zoom: ${zoomFactor} !important;
//       transform-origin: top center !important;
//       position: fixed !important;
//       right: 42px !important;  /* 固定在右侧头像位置 */
//       left: auto !important;   /* 取消左侧定位 */
//       top: 50px !important;    /* 调整到头像下方 */
//     }
    
//     /* 确保菜单中的内容正常显示 */
//     .el-popper .el-dropdown-menu__item,
//     .el-popper .el-dropdown-menu__item span,
//     .el-popper .el-menu-item,
//     .el-popper .el-menu-item span {
//       font-size: 14px !important;
//       line-height: normal !important;
//     }
    
//     /* 定位修复 */
//     .el-popper[data-popper-placement^="bottom"] {
//       margin-top: ${8 / scale}px !important;
//     }
//   `;
//   document.head.appendChild(styleEl);
//   const processedNodes = new WeakSet();
//   const popperObserver = new MutationObserver(mutations => {
//     mutations.forEach(mutation => {
//       mutation.addedNodes.forEach(node => {
//         if (node.nodeType !== 1) return;
//         console.log(node.id,node.classList.contains('el-popper'),'node');
       
        
//         if (node.id && node.classList.contains('el-popper') && !processedNodes.has(node)) {
//           console.log(node,'node1');
          
//           processedNodes.add(node);
//           // 延迟执行，确保弹窗渲染完成
//           requestAnimationFrame(() => {
//             updateTransformKeepBoundary(node, scale);
//             node.classList.add('my-dynamic-style');
//           });
//           // applyStylesToDescendants(node);
//         }
//       });
//     });
//   });
//   popperObserver.observe(document.body, { childList: true, subtree: true });

//   // 初始化时给已有的 el-popper 元素加样式
//   const poppers = document.querySelectorAll('[id^="el-popper"]');
//   poppers.forEach(el => applyStylesToDescendants(el));
// }

const initialTransforms = new WeakMap();
function updateTransformKeepBoundary(element, scale) {
  const rect = element.getBoundingClientRect();
  const screenW = window.innerWidth;
  const screenH = window.innerHeight;

  const distLeft = rect.left;
  const distRight = screenW - rect.right;
  const distTop = rect.top;
  const distBottom = screenH - rect.bottom;

  let horizontalBoundary = 'center';
  if (distLeft < distRight) horizontalBoundary = 'left';
  else if (distRight < distLeft) horizontalBoundary = 'right';

  let verticalBoundary = 'center';
  if (distTop < distBottom) verticalBoundary = 'top';
  else if (distBottom < distTop) verticalBoundary = 'bottom';

  const origin = `${horizontalBoundary} ${verticalBoundary}`;

  const style = window.getComputedStyle(element);
  const transform = style.transform === 'none' ? '' : style.transform;

  let x = 0, y = 0, z = 0;
  let newTransform = '';

  // 解析 matrix3d 和 matrix，提取平移量
  const matrix3dRegex = /^matrix3d\((.+)\)$/;
  const matrixRegex = /^matrix\((.+)\)$/;

  let match;
  if ((match = transform.match(matrix3dRegex))) {
    const values = match[1].split(',').map(v => parseFloat(v.trim()));
    x = values[12];
    y = values[13];
    z = values[14];
    // 去掉平移部分，保留其他变换（这里简单处理，直接不保留平移）
    // 你可以根据需求扩展矩阵分解
    newTransform = 'matrix3d(' + values.slice(0, 12).join(', ') + ', 0, 0, 0, 1)';
  } else if ((match = transform.match(matrixRegex))) {
    const values = match[1].split(',').map(v => parseFloat(v.trim()));
    x = values[4];
    y = values[5];
    z = 0;
    // 去掉平移部分，保留其他变换（这里只保留前4个参数）
    newTransform = `matrix(${values[0]}, ${values[1]}, ${values[2]}, ${values[3]}, 0, 0)`;
  } else {
    // 如果不是矩阵格式，尝试匹配 translate3d
    const translate3dRegex = /translate3d\(\s*([-\d.]+)px,\s*([-\d.]+)px,\s*([-\d.]+)px\s*\)/;
    const translateMatch = transform.match(translate3dRegex);
    if (translateMatch) {
      x = parseFloat(translateMatch[1]);
      y = parseFloat(translateMatch[2]);
      z = parseFloat(translateMatch[3]);
      newTransform = transform.replace(translate3dRegex, '').trim();
    } else {
      // 其他情况，直接用原 transform
      newTransform = transform;
    }
  }

  requestAnimationFrame(() => {
  element.style.transformOrigin = origin;

  // 计算平移量
  const translateX = x / (window.innerWidth / 1920);
  const translateY = y / (window.innerHeight / 953);
  console.log(x,y, 999);

  // 获取元素当前视口位置和尺寸
  const rect = element.getBoundingClientRect();

  setTimeout(() => {
    // 设置定位，确保元素可以用 left/top 定位
    element.style.position = 'fixed';

    // 计算缩放后元素宽高
    const scaledWidth = rect.width;
    const scaledHeight = rect.height;

    // 计算边界限制
    // left 最小值是 0，最大值是视口宽度 - 元素宽度
    let left = translateX;
    if (left < 0) left = 0;
    if (left + scaledWidth > window.innerWidth) {
      left = 1920 - scaledWidth;
    }

    // top 最小值是 0，最大值是视口高度 - 元素高度
    let top = translateY;
    if (top < 0) top = 0;
    if (top + scaledHeight > window.innerHeight) {
      top = 953 - scaledHeight;
    }

    // 应用限制后的定位
    element.style.left = `${left}px`;
    element.style.top = `${top}px`;

    // 设置缩放
    element.style.zoom = `${scale}`;
    
    element.style.overflow = 'hidden';
    element.classList.add('no-transform');

    console.log(element.style, 'style');
  });
});


}
// 引入路由
const router = useRouter()
const route = useRoute()
watch(
  () => route.fullPath,  // 监听路由的完整路径变化
  (newPath, oldPath) => {
    getAllOverlay()
    // 在这里执行你想做的操作
  }
)
let status = ref(getStatus())
let timer = null
onMounted(async () => {
  console.log('当前API地址:', import.meta.env.VITE_API_BASE_URL)
  console.log('全部环境变量:', import.meta.env)
  console.log('当前运行平台:', isMacOS ? 'Mac OS' : 'Windows')
  
  // 清理之前的事件监听器（如果存在）
  cleanupPreviousScaleListeners();
  
  // 动态导入对应平台的缩放工具
  try {
    if (isMacOS) {
      console.log('正在使用Mac系统优化的缩放工具')
      // 导入Mac系统的缩放工具
      scaleModule.value = await import('@/utils/scaleHelper')
	  document.body.style.overflow = 'auto'
    } else {
      console.log('正在使用Windows系统优化的缩放工具')
      // 导入Windows系统的缩放工具
      scaleModule.value = await import('@/utils/windowsScaleHelper')
    }
    
    // 获取导入模块中的方法
    updateScaleFactor.value = scaleModule.value.updateScaleFactor
    removeEventListeners.value = scaleModule.value.removeEventListeners
    
    // 初始化缩放系统（包括注册事件监听器）
    if (isMacOS) {
      // Mac系统需要手动执行初始化逻辑
      if (scaleModule.value.setConfig) {
        scaleModule.value.setConfig();
      }
      // 手动注册事件监听器
      if (typeof window !== 'undefined') {
        // 立即应用缩放
        updateScaleFactor.value();
        
        // 监听窗口变化
        window._resizeHandler = () => {
          console.log('ScaleHelper: 窗口大小改变，窗口尺寸:', window.innerWidth, 'x', window.innerHeight);
          if (window._resizeDebounce) clearTimeout(window._resizeDebounce);
          window._resizeDebounce = setTimeout(() => {
            updateScaleFactor.value();
          }, 100);
        };
        window.addEventListener('resize', window._resizeHandler);
        
        // 监听设备方向变化
        window._orientationHandler = () => {
          console.log('ScaleHelper: 设备方向改变');
          setTimeout(() => {
            updateScaleFactor.value();
          }, 50);
        };
        window.addEventListener('orientationchange', window._orientationHandler);
        
        // 添加全屏变化监听
        if (scaleModule.value.addFullScreenChangeListener) {
          // 如果有导出的全屏监听函数，则调用
        } else {
          // 手动添加全屏监听
          const handleFullscreenChange = () => {
            updateScaleFactor.value();
          };
          if (!window._fullscreenHandlers) {
            window._fullscreenHandlers = {};
          }
          window._fullscreenHandlers.fullscreenchange = handleFullscreenChange;
          window._fullscreenHandlers.webkitfullscreenchange = handleFullscreenChange;
          window._fullscreenHandlers.mozfullscreenchange = handleFullscreenChange;
          window._fullscreenHandlers.MSFullscreenChange = handleFullscreenChange;
          
          document.addEventListener('fullscreenchange', handleFullscreenChange);
          document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
          document.addEventListener('mozfullscreenchange', handleFullscreenChange);
          document.addEventListener('MSFullscreenChange', handleFullscreenChange);
        }
      }
    } else {
      // Windows系统调用initializeScaling函数
      if (scaleModule.value.initializeScaling) {
        scaleModule.value.initializeScaling();
      } else if (updateScaleFactor.value) {
        updateScaleFactor.value();
      }
    }
    
    console.log('App.vue: 缩放工具加载并初始化完成')
  } catch (error) {
    console.error('App.vue: 加载缩放工具失败:', error)
  }
  
  check_first_enter()
  dialogStyle()
  observePopper()
  document.addEventListener('visibilitychange', handleVisibilityChange);
  // 每分钟更新一次状态
  timer = setInterval(() => {
    status.value = getStatus()
  }, 3 * 1000)
  // active.value = 'home'
});
onUnmounted(() => {
  if (timer) clearInterval(timer)
})
let handleVisibilityChange=()=>{
  syncFromLocalStorage();
}
// 从localStorage读取数据并同步vuex
let syncFromLocalStorage = () => {
    loginStore.setToken(JSON.parse(localStorage.getItem('user'))?.token||'')
};
const jump = () => {
  // router.push({
  //   name: 'home',
  //   params: {
  //     id: 1
  //   }
  // })
  router.push({ name: 'layout', replace: false })
}
let global_modal_ref=ref(null)
let gift_pack_ref=ref(null)
let check_first_enter=()=>{
  if(!loginStore.userId){
    let today = new Date().toISOString().split('T')[0];
      let lastShownDate = localStorage.getItem('gift_pack');

      // 判断是否需要显示弹窗
      if (!lastShownDate || lastShownDate !== today) {
        localStorage.setItem('gift_pack', today);
        gift_pack_ref.value.dialogVisible=true
      }

  }
}
let login=()=>{
  global_modal_ref.value.open()
}
onBeforeUnmount(() => {
  console.log('App.vue: 组件即将销毁，开始清理资源');
  
  // 清理缩放工具的事件监听器
  cleanupPreviousScaleListeners();
  
  // 清理MutationObserver
  if (observer) {
    observer.disconnect();
    console.log('App.vue: MutationObserver已断开连接');
  }
  
  // 清理visibilitychange事件监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  
  console.log('App.vue: 资源清理完成');
});
let limit_time_ref=ref(null)
watch(() => loginStore.memberInfo, (val) => {
  // setTimeout(()=>{
     console.log(val,'监听变化');
    status.value = getStatus()
  // },0)
 
}, { immediate: true,deep:true })

</script>
<template>
<!--  <div class="mb-4">-->
<!--    <el-button @click="jump">Default</el-button>-->
<!--  </div>-->
<!--  <View  transition="el-fade-in" class="flex-item_f-1" />-->
<!--  <router-view transition="el-fade-in" class="flex-item_f-1"></router-view>-->
    <div id="app">
     
      <router-view v-slot="{ Component }">
       
        <!-- <keep-alive :exclude="['AIDubbing', 'commercialDubbing']"> -->
          <component :is="Component" class="flex-item_f-1" />
        <!-- </keep-alive> -->
      </router-view>
      <!--   登录弹窗   -->
      <GlobalModal ref="global_modal_ref" />
      <!--   导入文案弹窗     -->
<!--      <GlobalimportLetter />-->
      <!--   AI文案生成弹窗   -->
      <AIgenerationModal/>
      <!--   背景音乐弹窗   -->
      <musicModal/>
      <!--   音效弹窗   -->
<!--      <soundEffects/>-->
      <!-- 悬浮弹窗 -->
      <!-- <suspension></suspension> -->
      <!-- 每日首次未登录弹窗 -->
      <giftPack ref="gift_pack_ref" @login="login"></giftPack>
      <!-- 限时优惠 -->
      <!-- v-if="isBuildWithin24h||isBuyWithin24h " -->
      <!--购买ai配音赠送弹窗 -->
      <limitTime ref="limit_time_ref" v-if="status!=0"></limitTime>

      <!-- Stagewise工具栏 - 仅在开发环境显示 -->
      <component v-if="isDev && StagewiseToolbar" :is="StagewiseToolbar" :config="stagewiseConfig" />
    </div>
</template>
<style lang="scss">
#app {
  font-family: PingFangSC, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
  display: flex;
  color: var(--el-text-color-primary);
  font-size: var(--el-font-size-base);
}
/* 全局样式 */
.header-bar {
  height: 60px !important; /* 强制保持一致高度 */
  min-height: 60px !important;
}

/* 全局确保提取内容文本显示规则 */
.content-text,
.extracted-content-item .content-text,
.preview-section .content-text,
.extracted-content-list .content-text,
[class*="content-text"] {
  /* 移除之前冲突的强制nowrap样式 */
  display: block !important;
  max-width: 100% !important;
}

/* 确保提取内容项布局一致 */
.extracted-content-item {
  display: flex !important;
  align-items: center !important;
}

/* 内容区域样式 */
.content-area {
  max-width: calc(100% - 150px) !important; /* 留出右侧标签的空间 */
}
/* 提取内容文本样式规则 */
.extracted-content-item .content-area .content-text {
  white-space: normal !important; /* 允许折行 */
  overflow: visible !important; /* 让内容可见 */
  text-overflow: clip !important; /* 不使用省略号 */
  word-wrap: break-word !important; /* 长单词可以折行 */
  word-break: break-all !important; /* 允许在任何字符间断行 */
}
.el-dialog{
  // opacity: 0;
}
.el-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999 !important;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}
.el-overlay-dialog{
  height: fit-content;
}
.el-popper {
  will-change: transform;
  height: fit-content;
  // transform: none !important;
}
.no-transform {
  transform: none !important;
}
.el-dialog__wrapper {
  transform-origin: center center;
}

.el-dialog {
  // max-height: 80vh;
  overflow-y: auto;
  box-sizing: border-box;
  margin: 0 auto;
}
.el-overlay-dialog{
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
@supports (-webkit-touch-callout: none) {
  .el-dialog__wrapper {
    -webkit-overflow-scrolling: touch;
  }
}
</style>
