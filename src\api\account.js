// 收益相关接口
import { post } from './index'
import { globalConfig } from '@/config/index'
//会员使用情况接口
// export const showUserBenefits = (params) =>{
//     // const baseUrl = globalConfig.getServiceApiBase('income')
//     const baseUrl = globalConfig.getServiceApiBase('income')
//     console.log(baseUrl,'baseUrl');
    
//     return post(`${baseUrl}/showUserBenefits`, params)
// } 
export const showUserBenefits = (params) => post('/userAuth/auth/showUserBenefits', {...params,need_code:true})
//查询会员计划接口
export const planList = (params) => post('/userAuth/subscription/plans/active/list', params)
//查询加油包接口
export const productList = (params) => post('/userAuth/product/query',  {...params,no_encode:true})
//支付接口
export const ordersCreate = (params) => post('/userAuth/orders/create', {...params,no_encode:true})
//支付轮询查询订单状态
export const queryOrder = (params) => post('/userAuth/orders/queryOrder',  {...params,no_encode:true,need_code:true})
//支付状态通知
// export const notify = (params) => post('/userAuth/orders/notify',  {...params,no_encode:true,need_code:true})
//账户信息修改
export const userUpdate = (params) => post('/userAuth/user/update',  {...params,no_encode:true,need_code:true})
//账户信息密码修改
export const resetPassword = (params) => post('/userAuth/api/user/reset-password', params)
//账户信息密码修改-发送验证码
export const resetSendCode = (params) => post(`/userAuth/api/user/reset-password/send-code/${params}`, {need_code:true})
//获取用户信息接口
export const userInfo = (params) => post(`/userAuth/user/info`,params)
//退出登录
export const userExit = (params) => post(`/userAuth/auth/userExit`, {...params,no_encode:true,})
//邀请码接口
export const bindCommit = (params) => post(`/userAuth/user/bindCommit`, {...params,need_code:true,})
//注销账户接口
export const cancelByUserId = (params) => post(`/userAuth/auth/cancelByUserId`, {...params,need_code:true,})
//数字人列表
export const digitalPlanList = (params) => post(`/userAuth/subscription/plans/active/digitalPlanList`, {...params,need_code:true,})
//获取svip-3天权益
export const getPlanDetail = (params) => post(`/userAuth/subscription/plans/getPlanDetail`, {...params,need_code:true,})
//获取数字人3天体验
export const getDetail = (params) => post(`/userAuth/subscription/plans/getDetail`, {...params,need_code:true,})


