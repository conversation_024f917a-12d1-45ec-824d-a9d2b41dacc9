// 通话接口
import { post, get } from './index'

// 文案创作首页接口
export const creationList = (params) => post('/material/template/list', params)


// 对话页面
export const questionAPl = (params) => post('/material/api/ask',params,{ encode: false })

// 历史记录
export const getHistoryApi = (params) => post('/material/api/getHistory',params)


// 历史记录回显跳转
export const getHistoryByDayApi = (params) => post('/material/api/getHistoryByDay',params)


// 保存对话记录
export const getChatRecordApi = (params) => post('/material/api/saveMaterial',params)

// 获取对话记录
export const getChatRecordListApi = (params) => get('/material/api/tags-map/11',params)


