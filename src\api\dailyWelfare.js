// 签到福利相关接口
import { post } from './index'

/**
 * 获取每日福利列表
 * @param {Object} params - 请求参数，可能包含日期、用户信息等
 * @returns {Promise} - 返回每日福利数据的Promise对象
 * 
 * 参数示例:
 * {
 *   userId: "11",      // 用户ID
 *   date: "2024-01-01" // 查询日期(可选)
 * }
 */
export const getDailyWelfareList = (params) => post('/userAuth/user/signin/list', params)

/**
 * 获取已领取福利列表
 * @param {Object} params - 请求参数，包含分页信息等
 * @returns {Promise} - 返回已领取福利列表的Promise对象
 * 
 * 参数示例:
 * {
 *   userId: "202504181056397713",  // 用户ID
 *   page: 1,                      // 当前页码
 *   size: 5                       // 每页条数
 * }
 */
export const getClaimedWelfareList = (params) => post('/userAuth/user/signin/exchangeList', params)

/**
 * 用户签到
 * @param {Object} params - 请求参数，包含用户信息等
 * @returns {Promise} - 返回签到结果的Promise对象
 * 
 * 参数示例:
 * {
 *   userId: "11"       // 用户ID
 * }
 */
export const signIn = (params) => post('/userAuth/user/signin/add', params)

/**
 * 获取兑换列表
 * @param {Object} params - 请求参数，包含用户信息等
 * @returns {Promise} - 返回兑换列表的Promise对象
 * 
 * 参数示例:
 * {
 *   userId: "202504181056397713"  // 用户ID
 * }
 */
export const getExchangePlanList = (params) => post('/userAuth/user/signin/planList', params)

/**
 * 用户兑换福利
 * @param {Object} params - 请求参数，包含用户ID和计划ID
 * @returns {Promise} - 返回兑换结果的Promise对象
 * 
 * 参数示例:
 * {
 *   userId: "202504181056397713",  // 用户ID
 *   planId: "20"                    // 计划ID
 * }
 */
export const exchangeItem = (params) => post('/userAuth/user/signin/exchange', params) 