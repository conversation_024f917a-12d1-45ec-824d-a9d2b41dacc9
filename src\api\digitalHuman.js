import { post, get } from './index'

// 数字人生成音频
export let createVideo = (params) => post('/material/digital/createVideo',{...params,no_encode:true })

// 保存数字人作品
export const saveDigitalWork = (params) => post('/material/digital/saveDigitalWork', {...params, no_encode: true})

// 数字人音频提取文本和JSON数据
export const getDigiAudioJsonTxt = (params) => post('/material/api/getDigiAudioJsonTxt', params)

/**
 * 获取数字人作品详情
 *
 * 通过作品ID获取数字人作品的详细信息，用于数字人编辑器页面展示和编辑
 *
 * @param {Object} params - 请求参数对象
 * @param {number} params.id - 数字人作品ID
 * @returns {Promise} 返回包含作品详情数据的Promise对象
 *
 * @example
 * // 获取ID为19的数字人作品详情
 * getDigitalWork({ id: 19 })
 *   .then(data => {
 *     console.log('作品详情:', data);
 *   })
 *   .catch(error => {
 *     console.error('获取作品详情失败:', error);
 *   });
 */
export const getDigitalWork = (params) => post('/material/digital/getDigitalWork', {...params, no_encode: true})

/**
 * 编辑数字人作品并重新生成视频
 *
 * 用于编辑现有的数字人作品并重新生成视频，在保存数字人作品接口的基础上增加了作品ID参数
 * 该接口适用于"我的作品-编辑数字人作品-重新生成视频"的业务场景
 *
 * @param {Object} params - 请求参数对象，包含所有保存参数加上作品ID
 * @param {number} params.id - 数字人作品ID（必需，用于标识要编辑的作品）
 * @param {string} params.title - 作品标题
 * @param {string} params.userId - 用户ID
 * @param {number} params.digitalHumanId - 数字人ID
 * @param {Object} params.personJson - 数字人配置对象
 * @param {Object} params.audioJson - 音频配置对象
 * @param {Object} params.bgJson - 背景配置对象
 * @param {Object} params.subtitleConfigJson - 字幕配置对象
 * @param {string} params.text - 文本内容
 * @param {string} params.bgColor - 背景颜色
 * @param {number} params.screenWidth - 屏幕宽度
 * @param {number} params.screenHeight - 屏幕高度
 * @param {string} params.videoUrl - 视频URL
 * @param {string} params.bgmiAudioUrl - 背景音乐URL
 * @param {Object} params.commonJson - 右侧操作面板完整配置数据
 * @returns {Promise} 返回包含编辑结果的Promise对象
 *
 * @example
 * // 编辑ID为19的数字人作品
 * const editParams = {
 *   id: 19, // 作品ID（必需）
 *   title: "编辑后的作品标题",
 *   userId: "user123",
 *   digitalHumanId: 1,
 *   // ... 其他保存参数
 * };
 *
 * editDigitalWork(editParams)
 *   .then(data => {
 *     console.log('作品编辑成功:', data);
 *   })
 *   .catch(error => {
 *     console.error('作品编辑失败:', error);
 *   });
 */
export const editDigitalWork = (params) => post('/material/digital/editDigitalWork', {...params, no_encode: true})