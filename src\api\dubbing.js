// 引入接口基础请求方法
import { post, get } from './index'

// 获取上传签名的接口
// params: 请求参数
// 返回: Promise对象,包含签名信息
export const dubbing = (params) => post('/material/api/upload/signature', params,{encode: false})

// 回调oss接口
export const callbackOss = (params) => post('/material/api/upload/callback', params)


// 提取文件
export const extractFile = (params) => post('/material/api/crawlTextByMediaFile', params)

// 空间提取文件
export const spaceExtractFile = (params) => post('/material/api/getMaterialUrl', params)

// 视频去水印
export const videoWaterMark = (params) => post('/material/api/removeSubtitle', params)

// 一键解析视频
export const oneKeyParseVideo = (params) => post('/material/api/crawlTextByVideoPageWithoutAITools', params)

// 文案提取解析接口
export const crawlTextByVideoPage = (params) => post('/material/api/crawlTextByVideoPage', params)
