import service from '@/utils/request.js'


export const createApi = (method = 'get') => (url, data = {}, config = {}) => {
    const requestConfig = {
        url,
        method,
        ...config
    }

    method.toLowerCase() === 'get'
        ? requestConfig.params = data
        : requestConfig.data = data

    return new Promise((resolve, reject) => {
        service(requestConfig)
            .then(res => resolve(res))
            .catch(err => {
                // 统一错误处理（可扩展）
                console.error(`API Error: ${url}`, err)
                reject(err)
            })
    })
}

// 生成不同请求方法
export const get = createApi('get')
export const post = createApi('post')
export const put = createApi('put')
export const del = createApi('delete')