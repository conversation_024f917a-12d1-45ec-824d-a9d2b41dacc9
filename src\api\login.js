//登录相关
import { post, get } from './index'
import { globalConfig } from '@/config/index'



//注册获取验证码
export const getRegisterCode = (params) => post(`/userAuth/api/user/send-code/${params}`,{need_code:true})

//注册接口
export const getRegister = (params) => post(`/userAuth/api/user/register`,{...params,need_code:true})

//登录获取验证码
export const getLoginCode = (params) => post(`/userAuth/auth/sendCode`,{...params,need_code:true})

//短信登录接口
export const LoginCode = (params) => post(`/userAuth/auth/loginBySms`,{...params,need_code:true})

//合并注册与登录接口
export let loginByCode = (params) => post(`/userAuth/auth/loginByCode`,{...params,need_code:true})
//密码登录接口
export const LoginPassword = (params) => post(`/userAuth/auth/loginByPwd`,{...params,need_code:true})

//找回密码获取验证码
export const getFindPassword = (params) => post(`/userAuth/api/user/reset-password/send-code/${params}`,{need_code:true})

//找回密码密码重置
export const FindPassword = (params) => post(`/userAuth/api/user/reset-password`,{...params,need_code:true})

//获取用户信息接口
export const userInfo = (params) => post(`/userAuth/user/info`,params)

//获取会员剩余
// export const showUserBenefits = (params) =>{
//     const baseUrl = globalConfig.getServiceApiBase('income')
// console.log(globalConfig.getServiceApiBase(''));

//    return post(`${baseUrl}/showUserBenefits`, params)
// } 
export const showUserBenefits = (params) => post('/userAuth/auth/showUserBenefits', {...params,need_code:true})
//微信登陆接口
export let qrcode = (params) => get('/userAuth/auth/login/qrcode',{...params,need_code:true})
//微信轮询接口
export let wxStatus = (params) => get('/userAuth/auth/login/status',{...params,need_code:true,})

//发送数据接口
// export const sendData = (params) => post(`/userAuth/auth/sendData`,{...params,encode:false})
export const sendData = (params) => post('/userAuth/auth/sendData', params, { encode: false })