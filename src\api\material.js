// 素材相关接口
import { post, get } from './index'

/**
 * 添加视频素材
 * @param {Object} params - 请求参数，包含视频素材信息
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   title: "视频标题",
 *   description: "视频描述",
 *   fileUrl: "视频文件URL",
 *   coverUrl: "封面图URL",
 *   duration: 120, // 视频时长(秒)
 *   tags: ["标签1", "标签2"],
 *   category: "分类ID"
 * }
 */
export const addVideoMaterial = (params) => post('/material/video/add', params,{ encode: false })

