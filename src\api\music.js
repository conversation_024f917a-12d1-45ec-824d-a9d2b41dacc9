// 音乐音效相关接口
import { post, get } from './index'

/**
 * 查询背景音乐库
 * @param {Object} params - 请求参数，如分页、筛选条件等
 * @returns {Promise} - 返回API请求Promise
 */
export const queryBGM = (params) => post('/material/api/queryBGMI', params)

/**
 * 查询音效库
 * @param {Object} params - 请求参数，如分页、筛选条件等
 * @returns {Promise} - 返回API请求Promise
 */
export const querySFX = (params) => post('/material/api/querySFX', params)

/**
 * 查询我的音乐
 * @param {Object} params - 请求参数，如分页、筛选条件等
 * @returns {Promise} - 返回API请求Promise
 */
export const queryUserAudio = (params) => post('/material/api/queryUAudio', params) 