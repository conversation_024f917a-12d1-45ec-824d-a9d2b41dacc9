// 我的素材相关接口
import { post, get } from './index'

/**
 * 获取我的素材分页列表
 * @param {Object} params - 请求参数，包含分页信息、筛选条件等
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   pageNum: 1,        // 当前页码
 *   pageSize: 10,      // 每页条数
 *   categoryId: "",    // 分类ID(可选)
 *   keyword: "",       // 搜索关键词(可选)
 *   sortField: "",     // 排序字段(可选)
 *   sortOrder: ""      // 排序顺序(可选)
 * }
 */
export const getMyMaterialList = (params) => post('/material/api/page', params, { encode: false })

/**
 * 根据类型和标签获取素材列表
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11",      // 用户ID
 *   materialType: "audio", // 素材类型：audio(音频)、video(视频)、tts(配音)，不传则获取所有类型
 *   tagId: "59"        // 标签/专辑ID，不传则获取所有专辑
 * }
 */
// export const listByTypeAndTag = (params) => post('/material/api/listByTypeAndTag', params, { encode: false })

/**
 * 重命名专辑
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   albumId: 1,       // 专辑ID
 *   name: "新名称",    // 新的专辑名称
 *   userId: "11"      // 用户ID
 * }
 */
export const updateAlbumName = (params) => post('/material/api/album/update', params, { encode: false })

/**
 * 删除素材或专辑
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例(删除素材):
 * {
 *   materialId: 1,    // 素材ID
 *   userId: "11",     // 用户ID
 *   type: "material"  // 删除类型：material(素材)
 * }
 * 
 * 参数示例(删除专辑):
 * {
 *   albumId: 1,       // 专辑ID
 *   userId: "11",     // 用户ID
 *   type: "album"     // 删除类型：album(专辑)
 * }
 */
export const deleteMaterial = (params) => post('/material/api/delete', params, { encode: false }) 

/**
 * 获取用户专辑列表
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11"      // 用户ID
 * }
 */
export const getUserAlbum = (params) => post('/material/api/tag/list', params, { encode: false })

/**
 * 删除列表素材
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11",     // 用户ID
 *   materialIds: [1, 2, 3]  // 需要删除的素材ID列表
 * }
 */
export const deleteMaterialList = (params) => post('/material/api/deleteMaterial', params, { encode: false })

/**
 * 创建新专辑
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11",      // 用户ID
 *   tagName: "新专辑名称" // 专辑名称
 * }
 */
export const createAlbum = (params) => post('/material/api/tag/create', params, { encode: false })

/**
 * 更新素材信息
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11",           // 用户ID
 *   materialId: 1,          // 素材ID
 *   materialName: "新标题",  // 素材名称/标题
 *   description: "新描述"    // 素材描述
 * }
 */
export const updateMaterialInfo = (params) => post('/material/api/update', params, { encode: false })

/**
 * 移动素材到指定专辑
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11",          // 用户ID
 *   materialId: 1,         // 素材ID
 *   targetAlbumId: 2       // 目标专辑ID
 * }
 * 
 * 参数示例(批量移动):
 * {
 *   userId: "11",          // 用户ID
 *   materialIds: [1, 2, 3], // 素材ID列表
 *   targetAlbumId: 2       // 目标专辑ID
 * }
 */
export const updateMaterialAlbum = (params) => post('/material/api/updateMaterialAlbum', params, { encode: false })

/**
 * 保存完整素材信息
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回API请求Promise
 * 
 * 参数示例:
 * {
 *   userId: "11",          // 用户ID
 *   materialId: 1,         // 素材ID
 *   materialName: "素材名称", // 素材名称
 *   description: "素材描述", // 素材描述
 *   fileUrl: "文件URL",    // 文件URL
 *   coverUrl: "封面URL",   // 封面URL
 *   duration: 120,        // 时长(秒)
 *   tagId: "59"           // 专辑/标签ID
 * }
 */
export const saveFullMaterial = (params) => post('/material/api/saveMaterialNew', params, { encode: false })




