// 我的空间相关接口
import { post, get } from './index'

/**
 * 根据专辑获取内容
 * @param {Object} params - 请求参数，可能包含专辑ID、过滤条件等
 * @returns {Promise} - 返回API请求Promise
 */
export const getByAlbum = (params) => post('/tts/work/getByAlbum', params, { encode: false })

/**
 * 查询我的作品接口
 * @param {Object} params - 请求参数，可能包含创建者ID、分页信息等
 * @returns {Promise} - 返回API请求Promise
 */
export const getAlbumsByCreator = (params) => post('/tts/album/getAlbumsByCreator', params, { encode: false })

/**
 * 创建专辑接口
 * @param {Object} params - 请求参数，包含专辑信息如标题、描述等
 * @returns {Promise} - 返回API请求Promise
 */
export const createAlbum = (params) => post('/tts/album/createAlbum', params, { encode: false })

/**
 * 删除专辑接口
 * @param {Object} params - 请求参数，包含专辑ID
 * @returns {Promise} - 返回API请求Promise
 */
export const deleteAlbum = (params) => post('/tts/album/delete', params, { encode: false })

/**
 * 更新/重命名专辑接口
 * @param {Object} params - 请求参数，包含专辑ID和新名称等更新信息
 * @returns {Promise} - 返回API请求Promise
 */
export const updateAlbum = (params) => post('/tts/album/updateAlbum', params, { encode: false })

/**
 * 更改作品所属专辑接口
 * @param {Object} params - 请求参数，包含作品ID和目标专辑ID
 * @returns {Promise} - 返回API请求Promise
 */
export const changeAlbum = (params) => post('/tts/work/changeAlbum', params, { encode: false })

/**
 * 删除作品接口
 * @param {Object} params - 请求参数，包含作品ID
 * @returns {Promise} - 返回API请求Promise
 */
export const deleteWork = (params) => post('/tts/work/delete', params, { encode: false })

/**
 * 更新作品接口
 * @param {Object} params - 请求参数，包含作品ID和需要更新的信息
 * @returns {Promise} - 返回API请求Promise
 */
export const updateWork = (params) => post('/tts/work/update', params, { encode: false })

/**
 * 获取工作类型接口
 * @param {Object} params - 请求参数，可能包含用户ID或其他过滤条件
 * @returns {Promise} - 返回包含所有工作类型的Promise
 */
export const getWorkTypes = (params) => post('/material/work/types', params, { encode: false })

/**
 * mp3转换wav接口
 * @param {Object} params - 请求参数，包含userId和audioUrl
 * @returns {Promise} - 返回API请求Promise
 */
export const extractWavByMp3 = (params) => post('/material/api/extractWavByMp3', params, { encode: false })

// 我的作品-数字人作品
// 获取数字人作品列表
export const getDigitalHumanWorksList = (params) => post('/material/digital/pageDigitalHumanList', params, { encode: false })
// 查询生成中作品状态更新
export const updateWorksStatus = (params) => post('/material/digital/getDigitalWorkStatus', params, { encode: false })
// 删除数字人作品
export const deleteDigitalHumanWorks = (params) => post('/material/digital/deleteDigitalWork', params)
// 重命名数字人作品
export const renameDigitalHumanWorks = (params) => post('/material/digital/updateDigitalTitle', params, { encode: false })
// 获取数字人作品详情
export const getDigitalHumanWorksDetails = (params) => post('/material/digital/getDigitalWork', params)
// 移动数字人作品到指定文件夹
export const moveDigitalHumanWorks = (params) => post('/material/digital/moveDigitalWork', params)
