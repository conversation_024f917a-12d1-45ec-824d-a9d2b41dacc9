import { post, get } from './index'

// 声音商店筛选标签列表
export let getAllMetadata = (params) => post('/material/voices/getAllMetadata', params)
// 声音商店列表
export let getAll = (params) => post('/material/voices/getAll', {...params,no_encode:true})
// 声音商店列表收藏
export let bookmarkToggle = (params) => post('/material/voices/bookmark/toggle', {...params,need_code:true})
// 声音商店列表只看收藏
export let bookmarkList = (params) => post('/material/voices/bookmark/list', params)
//声音商店获取声音包列表
export let queryVoiceWithPackage = (params) => post('/material/voices/queryVoiceWithPackage', params)
//声音商店声音包购买声音
export let packagesBuy = (params) => post('/material/voices/packages', {...params,no_encode:true})
//点击筛选项查询声音商店列表
export let query = (params) => post('/material/voices/query', {...params,no_encode:true})
//点击筛选项查询声音商店列表
export let productQuery = (params) => post('/userAuth/product/query', {...params,no_encode:true})
//声音商店单个包列表购买获取二维码
export let packageByCode = (params) => post('/userAuth/orders/create', {...params,no_encode:true})
//支付轮询查询订单状态
export const queryOrder = (params) => post('/userAuth/orders/queryOrder',  {...params,no_encode:true,need_code:true})
//支付状态通知
// export const notify = (params) => post('/userAuth/orders/notify',  {...params,no_encode:true,need_code:true})
//使用过的音色（历史）
export let queryUserBuyVoiceName=(params)=>post('/material/api/queryUserBuyVoiceName',params)
//获取API列表
export const getApiList = () => post('/material/voices/getApiList', {no_encode:true})

