// 引入接口基础请求方法
import { post, get } from './index'

/**
 * 获取用户专辑列表
 * @param {Object} params - 请求参数，可能包含用户ID、页码、每页数量等
 * @returns {Promise} 返回包含用户专辑数据的Promise对象
 */
export const getUserAlbum = (params) => post('/material/api/getUserAlbum', params)

/**
 * 搜索视频接口
 * @param {Object} params - 搜索参数，可能包含关键词、分类、标签、页码等
 * @returns {Promise} 返回包含视频搜索结果的Promise对象
 */
export const searchVideos = (params) => post('/material/api/searchVideos', params)

/**
 * 获取视频输出配置参数
 * @param {Object} params - 请求参数，包含用户相关配置信息
 * @returns {Promise} 返回包含视频输出配置的Promise对象
 */
export const getVideoConfig = (params) => post('/material/instant_media_config/selectByUser', params)

/**
 * 保存一键成片配置
 * @param {Object} params - 需要保存的配置参数
 * @returns {Promise} 返回保存配置结果的Promise对象
 */
export const saveVideoConfig = (params) => post('/material/instant_media_config/save', params)

/**
 * 提交一键成片生成任务
 * @param {Object} params - 请求参数，包含成片生成任务的相关信息
 * @returns {Promise} 返回任务提交结果的Promise对象
 */
export const submitGenerateJob = (params) => post('/material/produce/submitJob', params, { encode: false })

/**
 * 预览查询视频接口
 * @param {Object} params - 查询参数，可能包含视频ID、类型、状态等
 * @returns {Promise} 返回包含视频查询结果的Promise对象
 */
export const getVideos = (params) => post('/material/video/getVideos', params)

/**
 * 生成视频快照URL接口
 * @param {Object} params - 请求参数，可能包含视频ID、时间点、尺寸等信息
 * @returns {Promise} 返回包含视频快照URL的Promise对象
 */
export const generateVideoSnapshotUrls = (params) => post('/material/produce/generateVideoSnapshotUrls', params, { encode: false })

/**
 * 检查用户权益(算力、会员状态、云空间等)
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID
 * @param {number} params.requiredCompute - 所需算力数量
 * @returns {Promise} 返回包含用户权益状态的Promise对象
 */
export const checkUserBenefits = (params) => post('/userAuth/user/benefits/check', params)





