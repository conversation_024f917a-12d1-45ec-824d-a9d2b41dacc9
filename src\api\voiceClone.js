// 声音克隆相关接口
import { post, get } from './index'

/**
 * 获取声音克隆列表
 * @param {Object} params - 请求参数，如分页、筛选条件等
 * @returns {Promise} - 返回声音克隆列表数据
 */
export const getCloneList = (params) => post('/userAuth/clone/getPage', params, {encode: false})

/**
 * 声音克隆接口
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回声音克隆结果
 */
export const cloneVoice = (params) => post('/material/api/cloneVoice', params, {encode: false})







// /**
//  * 创建声音克隆
//  * @param {Object} params - 请求参数，如声音名称、音频文件等
//  * @returns {Promise} - 返回创建结果
//  */
// export const createClone = (params) => post('/userAuth/clone/create', params, {encode: false})

// /**
//  * 使用声音克隆音色
//  * @param {Object} params - 请求参数，包含声音克隆ID
//  * @returns {Promise} - 返回操作结果
//  */
// export const useCloneVoice = (params) => post('/userAuth/clone/use', params, {encode: false})

// /**
//  * 删除声音克隆
//  * @param {Object} params - 请求参数，包含声音克隆ID
//  * @returns {Promise} - 返回删除结果
//  */
// export const deleteClone = (params) => post('/userAuth/clone/delete', params, {encode: false})

// /**
//  * 上传声音克隆音频
//  * @param {Object} params - 请求参数，包含音频文件数据
//  * @returns {Promise} - 返回上传结果
//  */
// export const uploadCloneAudio = (params) => post('/userAuth/clone/upload', params, {encode: false}) 