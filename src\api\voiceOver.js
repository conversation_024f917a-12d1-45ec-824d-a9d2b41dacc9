// 配音相关接口
import { post, get } from './index'

/**
 * 获取所有配音音色
 * @param {Object} params - 请求参数，如分页、筛选条件等
 * @returns {Promise} - 返回所有可用的配音音色列表
 */
export const getAllVoices = (params) => post('/material/voices/getAll', params,{encode:false})

/**
 * 获取配音分类元数据
 * @param {Object} params - 请求参数，可能包含过滤条件等
 * @returns {Promise} - 返回配音分类的元数据
 */
export const getAllVoiceMetadata = (params) => post('/material/voices/getAllMetadata', params,{encode:false})

/**
 * 获取声音特效
 * @param {Object} params - 请求参数，可能包含分类、标签等过滤条件
 * @returns {Promise} - 返回可用的声音特效列表
 */
export const getSoundEffects = (params) => post('/material/api/getSFX', params,{encode:false})

/**
 * 根据分类查询配音
 * @param {Object} params - 查询参数，可能包含分类ID、标签、性别等筛选条件
 * @returns {Promise} - 返回符合查询条件的配音列表
 */
export const queryVoicesByCategory = (params) => post('/material/voices/query', params, {encode:false})

/**
 * 生成音频
 * @param {Object} params - 请求参数，包含文本内容、选择的音色ID、语调设置等
 * @returns {Promise} - 返回生成的音频信息，如音频URL、时长等
 */
export const generateAudio = (params) => post('/material/api/generateAudio', params, {encode:false})

/**
 * 提交音频合成任务
 * @param {Object} params - 请求参数，可能包含文本内容、音色选择、合成参数等
 * @returns {Promise} - 返回任务提交结果，包含任务ID等信息
 */
export const submitAudioSynthesisJob = (params) => post('/tts/audio/submitJob', params, {encode:false})



/**
 * 根据用户ID获取克隆次数
 * @param {Object} params - 请求参数，包含userId和type
 * @param {string} params.userId - 用户ID
 * @param {number} params.type - 类型，值为1
 * @returns {Promise} - 返回用户的克隆次数信息
 */
export const getCloneTimesByUserId = (params) => post('/userAuth/clone/getTimesByUserId', params, {encode:false})

/**
 * 扣除克隆音色次数
 * @param {Object} params - 请求参数，与create接口入参相同
 * @returns {Promise} - 返回扣除结果
 */
export const deductCloneVoice = (params) => post('/userAuth/orders/deductCloneVoice', params, {encode:false})

