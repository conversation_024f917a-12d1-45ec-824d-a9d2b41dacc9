import service from '..'
/**
 * 登录验证码
 * @param {*} params
 * @returns
 */
// export function synthesized_speechApi(params) {
//     return service({
//         url: '/admin/auth/captcha',
//         method: 'get',
//         params
//     })
// }
/**
 * 文本转语音
 * @param {*} data
 * @returns
 */
export function synthesized_speechApi(data) {
    return service({
        url: '/material/api/generateAudio',
        method: 'post',
        data
    })
}
// 音色列表
export function Sound_ListApi(data) {
    return service({
        url: '/material/voices/getAll',
        method: 'post',
        data
    })
}


// 过滤音色列表
export function filter_sound_listApi(data) {
    return service({
        url: '/material/voices/query',
        method: 'post',
        data
    })
}



export function getSFXApi(data) {
    return service({
        url: '/material/api/getSFX',
        method: 'post',
        data
    })
}


// 背景音乐弹窗中展示列表以及标签页面
export function getBGMIApi(data) {
    return service({
        url: '/material/api/getBGMI',
        method: 'post',
        data:{...data}
    })
}

// 音色标签列表
export function Sound_tabs_listApi(data) {
    return service({
        url: '/material/voices/getAllMetadata',
        method: 'post',
        data
    })
}

// 上传接口之前oss获取签名
export function get_signature(data) {
    return service({
        url: '/material/api/upload/signature',
        method: 'post',
        data
    })
}
// oss 回调

export function upload_callback(data) {
    return service({
        url: '/material/api/upload/callback',
        method: 'post',
        data
    })
}


// 根据链接提取文案
export function crawlTextByVideoPage(data) {
    return service({
        url: '/material/api/crawlTextByVideoPage',
        method: 'post',
        data
    })
}
// 根据音视频提取文案
export function crawlByVideoPage(data) {
    return service({
        url: '/material/api/crawlTextByMediaFile',
        method: 'post',
        data
    })
}

// 保存文本信息
export function batchCreateAPI(data) {
    return service({
        url: '/tts/work/batchCreate',
        method: 'post',
        data
    })
}

// 根据地址栏id查询需要回显的数据
export function queryTextAPI(data) {
    return service({
        url: '/material/work/query',
        method: 'post',
        data
    })
}


//使用过的音色（历史）
export function queryUserUsedVoiceName(data) {
    return service({
        url: '/material/api/queryUserUsedVoiceName',
        method: 'post',
        data
    })
}
//获取AI单配已购列表

export function queryUserBuyVoiceName(data) {
    return service({
        url: '/material/api/queryUserBuyVoiceName',
        method: 'post',
        data
    })
}
//AI智能匹配接口
export function selectVoiceByAI(data) {
    return service({
        url: '/material/api/selectVoiceByAI',
        method: 'post',
        data
    })
}
//AI文案模板列表
export function templateList(data) {
    return service({
        url: '/material/template/list',
        method: 'post',
        data
    })
}
//导入文案
export function extractTextByWorkTxt(data) {
    return service({
        url: '/material/api/extractTextByWorkTxt',
        method: 'post',
        data
    })
}
//mp3转wav格式接口
export function extractWavByMp3(data) {
    return service({
        url: '/material/api/extractWavByMp3',
        method: 'post',
        data
    })
}
//ai配音-我的银色-克隆音色
export function cloneList(data) {
    return service({
        url: '/userAuth/clone/getList',
        method: 'post',
        data
    })
}







