import service from '..'



export function generateAudiosApi(data) {
    return service({
        url: '/material/api/generateAudios',
        method: 'post',
        data
    })
}
// 编辑修改
export function clip_audio_file_Api(data) {
    return service({
        url: '/material/api/clip_audio_file',
        method: 'post',
        data
    })
}

// 第四步合成最后一部
export function handle_merge_audio_mp3(data) {
    return service({
        url: '/material/api/handle_merge_audio_mp3',
        method: 'post',
        data
    })
}

//商配和单配文字检测敏感词
export function chekSensitive_Api(data) {
    return service({
        url: '/material/api/chekSensitive',
        method: 'post',
        data
    })
}
//商配保存到我的空间
export function batchSave(data) {
    return service({
        url: '/tts/work/batchSave',
        method: 'post',
        data
    })
}
//获取我的空间跳转商配保存内容
export function getListForSave(data) {
    return service({
        url: '/tts/work/getListForSave',
        method: 'post',
        data
    })
}
//使用过的音色（历史）
export function queryUserUsedVoiceName(data) {
    return service({
        url: '/material/api/queryUserUsedVoiceName',
        method: 'post',
        data
    })
}
//获取AI商配已购列表
export function queryUserBuyVoiceName(data) {
    return service({
        url: '/material/api/queryUserBuyVoiceName',
        method: 'post',
        data
    })
}
//AI智能匹配接口
export function selectVoiceByAI(data) {
    return service({
        url: '/material/api/selectVoiceByAI',
        method: 'post',
        data
    })
}

