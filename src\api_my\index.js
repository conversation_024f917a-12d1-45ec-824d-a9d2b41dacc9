'use strict'
import axios from 'axios'
import qs from 'qs'
import router from '@/router'
import { ElMessage } from 'element-plus'
import { ContentType } from '@/common/enums'
import { blob2Json } from '@/common/utils'
import { globalConfig } from '@/config/index' // 引入全局配置




import { MAPPING, CONTENT_TYPE, SUCCESS_CODE, TIME_OUT, AUTH_KEY, TNEANT_KEY } from '@/common/constants'
import { useAuthStore } from '../stores/modules/auth'
let CancelToken = axios.CancelToken;
// import {useloginStore} from "../stores/index.js";
// let  loginStore = useloginStore() 
/**
 * @description: 异常消息提示
 * @param {string} string
 * @return {*}
 * @author: gumingchen
 */
let isHandling401 = false
let not_error=false;
const prompt = (message) => {
  return ElMessage({
    message: message,
    type: 'warning',
    duration: 3000,
    grouping: true
  })
}

/**
 * @description: code处理
 * @param {number} code
 * @param {string} msg
 * @return {*}
 * @author: gumingchen
 */
const codeHandle = (code, message) => {
  switch (code) {
    case 4000:
    case 4001:
      // 不再跳转到login，只清除状态
      prompt(message)
      useAuthStore().clear()
      break
    case 401:
      router.replace({
        name: '401'
      })
      break
    case 404:
      router.replace({
        name: '404'
      })
      break
    case 500:
      // router.replace({
      //   name: '500'
      // })
      break
      
    default:
      prompt(message, false)
      break
  }
}

/**
 * @description: axios创建
 * @param {*}
 * @return {*}
 * @author: gumingchen
 */
let  headers = {
  'Content-Type': CONTENT_TYPE,
};





const service = axios.create({
  // baseURL: MAPPING,import.meta.env.VITE_API_BASE_URL
  baseURL: globalConfig.apiBase, // 使用全局配置中的动态URL
  withCredentials: false,
  timeout: TIME_OUT,
  headers
})

// 输出API基础URL，用于调试
console.log('api_my - 当前API基础URL:', globalConfig.apiBase);
console.log('api_my - 当前环境类型:', import.meta.env.VITE_ENV_TYPE);
console.log('api_my - 当前域名:', window.location.hostname);

/**
 * @description: axios请求拦截器
 * @param {*}
 * @return {*}
 * @author: gumingchen
 */
service.interceptors.request.use(
  config => {
       // 处理取消请求
    if (config.data && config.data.cancelToken) {
        config.cancelToken = config.data.cancelToken; // 直接使用传入的 cancelToken
    }
    let user = JSON.parse(localStorage.getItem('user'));
    let token1 = user?.token;
    console.log(token1,'token1');
    if (token1) {
        config.headers.Authorization = `Bearer ${token1}`
    }
    const { token, tenantId } = useAuthStore()
    // 设置 token
   
    not_error=false
    if(config?.data?.not_error||config?.params?.not_error){
        not_error=true
        delete config?.data?.not_error;
        delete config?.params?.not_error;
    }
    // 设置租户ID
    if (tenantId) {
      config.headers[TNEANT_KEY] = tenantId
    }
    // 如果存在 request_timeout，则单独设置超时时间
    if (config.data && config.data.request_timeout) {
      config.timeout = config.data.request_timeout; // 设置超时时间
      delete  config.data.request_timeout
    }
    if (config.data) {
      if (config.headers['Content-Type'] === ContentType.FORM) {
        config.data = qs.stringify(config.data)
      }
    }
    config.data?.cancelToken&& delete config.data.cancelToken
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

/**
 * @description: axios响应拦截器
 * @param {*}
 * @return {*}
 * @author: gumingchen
 */
service.interceptors.response.use(
  async response => {
    if (response.headers['content-type'] === ContentType.STREAM) {
      if (!response.data.code) {
        return {
          blob: response.data,
          name: decodeURI(response.headers['content-disposition'].replace('attachment;filename=', ''))
        }
      } else {
        if (res.code === 401) {
            return loginOut('请重新登录')
        }
        return response.data || null
      }
    }
    const { responseType } = response.config
    if (responseType === 'blob') {
      response.data = await blob2Json(response.data)
    }
    const { code, message } = response.data
    if (!SUCCESS_CODE.includes(code)) {
      codeHandle(code, message)
      return null
    }
    return response.data || null
  },
  error => {
    console.log(not_error,error,error.response,'error');
    
    let msg=''
    if (error && error.response) {
      switch (error.response.status) {
        case 400:
           console.log('请求参数错误')
          break
        case 401:
          console.log(not_error,401);
           !not_error&&loginOut('请重新登录',not_error)
          // console.log('未授权，请重新登录')
          break
        case 403:
          console.log('拒绝访问')
          break
        case 404:
          console.log('请求错误,未找到该资源')
          break
        case 405:
          console.log('请求方法未允许')
          break
        case 408:
          msg='请求超时'
          console.log('请求超时')
          break
        case 411:
          console.log('需要知道长度')
          break
        case 413:
          console.log('请求的实体太大')
          break
        case 414:
          console.log('请求的URL太长')
          break
        case 415:
          console.log('不支持的媒体类型')
          break
        case 500:
          console.log('服务器端出错')
          break
        case 501:
          console.log('网络未实现')
          break
        case 502:
          console.log('网络错误')
          break
        case 503:
          console.log('服务不可用')
          break
        case 504:
          msg='网络超时'
          console.log('网络超时')
          break
        case 505:
          console.log('http版本不支持该请求')
          break
        case 511:
          !not_error&&loginOut('该账号已被他人登录',not_error)
        default:
          console.log(`连接错误${ error.response.status }`)
      }
      
    } else {
      //  msg='连接到服务器失败'
      console.log('连接到服务器失败')
      // router.replace({
      //   name: '500'
      // })
    }
    if(msg!=''&&!not_error){
      
      debouncedShowTip(msg)
    }

    return Promise.reject(error)
  }
)
let showTip=(message)=>{
  if(message&&message.trim()!=''){
    ElMessage.error(message)
  }
  
}
let debounce=(fn, delay = 300)=>{
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
  }
//公共防抖
const debouncedShowTip = debounce(showTip, 1000);

//511多端登录  401重新登录
let loginOut = (message = '', not_error = false) => {
  const now = Date.now();
  const lastTimeStr = localStorage.getItem('lastHandling401Time');
  const lastTime = lastTimeStr ? parseInt(lastTimeStr, 10) : 0;
  const interval = 60 * 1000; // 1分钟防抖间隔

  if (now - lastTime < interval) {
    console.log('防抖，1分钟内不重复处理');
    return;
  }

  localStorage.setItem('lastHandling401Time', now.toString());

  if (localStorage.getItem('user')) {
    try {
      localStorage.removeItem('user'); // 清除用户信息
      setTimeout(() => {
        ElMessage.error(message);
        window.location.reload();
      }, 200);
    } catch (e) {
      console.error('解析user失败', e);
    }
  }
}
export default service
