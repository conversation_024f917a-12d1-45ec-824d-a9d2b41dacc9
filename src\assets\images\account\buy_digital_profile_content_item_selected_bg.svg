<svg width="268" height="175" viewBox="0 0 268 175" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9505_50199)">
<rect width="268" height="175" rx="12" fill="white"/>
<g filter="url(#filter0_d_9505_50199)">
<g clip-path="url(#clip1_9505_50199)">
<rect width="268" height="175" rx="12" fill="white"/>
<circle cx="51.5" cy="147.5" r="207.5" fill="url(#paint0_linear_9505_50199)"/>
<circle cx="51.5" cy="147.5" r="207.5" stroke="url(#paint1_linear_9505_50199)"/>
<circle cx="188" cy="355" r="370" fill="url(#paint2_linear_9505_50199)"/>
<circle cx="188" cy="355" r="370" stroke="url(#paint3_radial_9505_50199)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_9505_50199" x="-40" y="-14" width="348" height="255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="26"/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.741875 0 0 0 0 1 0 0 0 0 0.7375 0 0 0 0.13 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9505_50199"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9505_50199" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9505_50199" x1="51.5" y1="-60" x2="51.5" y2="355" gradientUnits="userSpaceOnUse">
<stop stop-color="#ACFF89"/>
<stop offset="1" stop-color="#EFF2FF" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint1_linear_9505_50199" x1="268.835" y1="126.87" x2="159.28" y2="175.366" gradientUnits="userSpaceOnUse">
<stop stop-color="#76F876"/>
<stop offset="1" stop-color="#B5FBBD" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_9505_50199" x1="188" y1="39.7421" x2="188" y2="725" gradientUnits="userSpaceOnUse">
<stop stop-color="#A9FB75" stop-opacity="0.5"/>
<stop offset="1" stop-color="#DAFFA1" stop-opacity="0.08"/>
</linearGradient>
<radialGradient id="paint3_radial_9505_50199" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(188 355) rotate(89.968) scale(766.52)">
<stop offset="0.046875" stop-color="#B5C2FB" stop-opacity="0"/>
<stop offset="1" stop-color="#B5FBBB"/>
</radialGradient>
<clipPath id="clip0_9505_50199">
<rect width="268" height="175" rx="12" fill="white"/>
</clipPath>
<clipPath id="clip1_9505_50199">
<rect width="268" height="175" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
