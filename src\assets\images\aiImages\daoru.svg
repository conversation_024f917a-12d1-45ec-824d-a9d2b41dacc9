<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9712_46620)">
<g filter="url(#filter0_f_9712_46620)">
<ellipse cx="10" cy="13.5" rx="9" ry="4.5" fill="#C6FFC6"/>
</g>
<path d="M18.3327 3.33333C18.3327 2.8731 17.9596 2.5 17.4993 2.5L2.49935 2.5C2.03912 2.5 1.66602 2.8731 1.66602 3.33333L1.66602 16.6667C1.66602 17.1269 2.03912 17.5 2.49935 17.5H17.4993C17.9596 17.5 18.3327 17.1269 18.3327 16.6667L18.3327 3.33333ZM3.33268 12.5H6.17938C6.82235 13.9716 8.29075 15 9.99935 15C11.7079 15 13.1763 13.9716 13.8193 12.5H16.666V15.8333L3.33268 15.8333L3.33268 12.5ZM3.33268 4.16667L16.666 4.16667V10.8333H12.4993C12.4993 12.2141 11.3801 13.3333 9.99935 13.3333C8.6186 13.3333 7.49935 12.2141 7.49935 10.8333L3.33268 10.8333L3.33268 4.16667ZM13.3327 7.5H10.8327V5H9.16602V7.5H6.66602L9.99935 11.25L13.3327 7.5Z" fill="black"/>
<path d="M13.3659 7.5H10.8659V5L9.19922 5V7.5H6.69922L10.0326 11.25L13.3659 7.5Z" fill="#0AAF60"/>
</g>
<defs>
<filter id="filter0_f_9712_46620" x="-1" y="7" width="22" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_9712_46620"/>
</filter>
<clipPath id="clip0_9712_46620">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>
