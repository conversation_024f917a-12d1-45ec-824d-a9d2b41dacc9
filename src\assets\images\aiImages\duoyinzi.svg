<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9712_46625)">
<g filter="url(#filter0_f_9712_46625)">
<circle cx="6" cy="10" r="6" fill="#C6FFC6"/>
</g>
<path d="M4.05795 9.16667H2.22266L5.00057 2.5L6.66724 2.5L9.44507 9.16667H7.60985L7.26832 8.33333H4.39948L4.05795 9.16667ZM5.08254 6.66667H6.58526L5.83391 4.83333L5.08254 6.66667ZM9.16716 10.8333L2.50051 10.8333L2.50051 12.5H6.54569L2.50051 15.8333V17.5H9.16716L9.16716 15.8333L5.12228 15.8333L9.16716 12.5V10.8333Z" fill="black"/>
<path d="M15.0013 2.5L18.3346 6.66667L15.8346 6.66667L15.8346 17.5H14.168L14.168 6.66667H11.668L15.0013 2.5Z" fill="#0AAF60"/>
</g>
<defs>
<filter id="filter0_f_9712_46625" x="-2" y="2" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_9712_46625"/>
</filter>
<clipPath id="clip0_9712_46625">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>
