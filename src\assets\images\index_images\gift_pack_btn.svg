<svg width="389" height="160" viewBox="0 0 389 160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_diiii_8643_32065)">
<rect x="30" y="9" width="329" height="92.1473" rx="46.0737" fill="url(#paint0_linear_8643_32065)"/>
<rect x="27.5" y="6.5" width="334" height="97.1473" rx="48.5737" stroke="url(#paint1_linear_8643_32065)" stroke-width="5"/>
</g>
<g filter="url(#filter1_f_8643_32065)">
<path d="M68.5093 10.3672C47.2412 10.3672 30 28.7809 30 51.4953C30 63.9864 35.214 75.177 43.4444 82.7199C38.665 75.2866 35.8681 66.2858 35.8681 56.5873C35.8681 31.0606 55.2439 10.3672 79.1452 10.3672H68.5093Z" fill="#FFBBEE"/>
</g>
<g filter="url(#filter2_f_8643_32065)">
<path d="M68.2232 10.3672C48.0132 14.6556 32.7966 33.416 32.7966 55.9097C32.7966 57.5879 32.8813 59.2453 33.0464 60.8776C32.3924 57.8886 32.0469 54.7759 32.0469 51.5783C32.0469 29.87 47.9742 12.0693 68.2232 10.3672Z" fill="#FFF5F5"/>
</g>
<path d="M136.157 50.111C138.171 50.111 139.178 51.118 139.178 53.132V53.344C139.178 55.358 138.171 56.365 136.157 56.365H93.6513C91.6373 56.365 90.6303 55.358 90.6303 53.344V53.132C90.6303 51.118 91.6373 50.111 93.6513 50.111H136.157ZM133.454 30.448C135.468 30.448 136.475 31.455 136.475 33.469V33.681C136.475 35.695 135.468 36.702 133.454 36.702H96.3543C94.3403 36.702 93.3333 35.695 93.3333 33.681V33.469C93.3333 31.455 94.3403 30.448 96.3543 30.448H133.454ZM126.299 33.575C128.349 33.575 129.373 34.5997 129.373 36.649V74.226C129.373 76.2753 128.349 77.3 126.299 77.3H126.087C124.038 77.3 123.013 76.2753 123.013 74.226V36.649C123.013 34.5997 124.038 33.575 126.087 33.575H126.299ZM104.887 33.575C106.937 33.575 107.961 34.5997 107.961 36.649V51.966C107.961 56.63 107.131 61.0113 105.47 65.11C103.845 69.2087 101.336 72.9363 97.9443 76.293C97.2023 76.9997 96.4603 77.3353 95.7183 77.3C95.0116 77.2647 94.3226 76.8583 93.6513 76.081L93.5453 75.869C92.238 74.3497 92.3263 72.8657 93.8103 71.417C96.531 68.6963 98.5096 65.746 99.7463 62.566C100.983 59.386 101.601 55.8527 101.601 51.966V36.649C101.601 34.5997 102.626 33.575 104.675 33.575H104.887ZM159.318 34.211C161.368 34.211 162.392 35.2357 162.392 37.285V70.675H176.331V68.131C176.331 66.0817 177.356 65.057 179.405 65.057H179.617C181.667 65.057 182.691 66.0817 182.691 68.131V71.417C182.691 73.325 182.197 74.7737 181.207 75.763C180.253 76.7523 178.805 77.247 176.861 77.247H161.862C159.954 77.247 158.506 76.7523 157.516 75.763C156.527 74.809 156.032 73.3603 156.032 71.417V37.285C156.032 35.2357 157.057 34.211 159.106 34.211H159.318ZM148.665 38.981C150.679 38.981 151.686 39.988 151.686 42.002V51.33C151.686 56.8067 151.139 62.0713 150.043 67.124C149.584 69.1027 148.365 69.8447 146.386 69.35L146.174 69.244C144.266 68.7847 143.542 67.548 144.001 65.534C144.955 61.1173 145.432 56.3827 145.432 51.33V42.002C145.432 39.988 146.439 38.981 148.453 38.981H148.665ZM187.355 38.981C189.369 38.981 190.376 39.988 190.376 42.002V51.33C190.376 56.4533 190.853 61.1703 191.807 65.481C192.267 67.4597 191.542 68.714 189.634 69.244L189.422 69.35C188.433 69.6327 187.638 69.5973 187.037 69.244C186.437 68.8553 186.013 68.1663 185.765 67.177C184.67 62.1243 184.122 56.842 184.122 51.33V42.002C184.122 39.988 185.129 38.981 187.143 38.981H187.355ZM170.077 29.07C172.056 28.8227 173.187 29.706 173.469 31.72L175.218 47.355C175.466 49.3337 174.582 50.4643 172.568 50.747H172.356C170.378 50.9943 169.247 50.111 168.964 48.097L167.215 32.462C166.968 30.4833 167.851 29.3527 169.865 29.07H170.077ZM199.969 32.621C201.877 32.621 202.831 33.575 202.831 35.483V63.096H207.919C210.11 63.096 211.205 64.1913 211.205 66.382V66.594C211.205 68.7847 210.11 69.88 207.919 69.88H202.619C200.747 69.88 199.316 69.403 198.326 68.449C197.372 67.495 196.895 66.064 196.895 64.156V35.483C196.895 33.575 197.849 32.621 199.757 32.621H199.969ZM211.311 29.07C213.325 29.07 214.332 30.077 214.332 32.091V74.809C214.332 76.823 213.325 77.83 211.311 77.83H211.099C209.085 77.83 208.078 76.823 208.078 74.809V32.091C208.078 30.077 209.085 29.07 211.099 29.07H211.311ZM238.765 37.232C240.885 37.232 241.945 38.292 241.945 40.412V49.475C241.945 53.2203 241.115 56.842 239.454 60.34C237.829 63.838 235.426 67.0533 232.246 69.986C229.066 72.9187 225.215 75.4097 220.692 77.459C218.749 78.307 217.389 77.724 216.611 75.71L216.505 75.498C215.763 73.59 216.382 72.1767 218.36 71.258C224.014 68.6787 228.254 65.5163 231.08 61.771C233.942 58.0257 235.373 53.927 235.373 49.475V40.412C235.373 38.292 236.433 37.232 238.553 37.232H238.765ZM222.547 43.433C224.667 43.433 225.727 44.493 225.727 46.613V49.475C225.727 54.033 227.158 58.1847 230.02 61.93C232.882 65.6753 237.299 68.82 243.27 71.364C245.249 72.1767 245.885 73.5547 245.178 75.498L245.072 75.71C244.366 77.724 243.023 78.3247 241.044 77.512C236.239 75.498 232.193 73.0247 228.907 70.092C225.657 67.1593 223.219 63.944 221.593 60.446C219.968 56.948 219.155 53.291 219.155 49.475V46.613C219.155 44.493 220.215 43.433 222.335 43.433H222.547ZM222.547 29.07C224.667 29.07 225.727 30.13 225.727 32.25V34.052H242.21C244.26 34.052 245.284 35.0767 245.284 37.126V37.338C245.284 39.3873 244.26 40.412 242.21 40.412H224.985C223.077 40.412 221.629 39.935 220.639 38.981C219.65 37.9917 219.155 36.5253 219.155 34.582V32.25C219.155 30.13 220.215 29.07 222.335 29.07H222.547ZM294.68 32.356C296.73 32.356 297.754 33.3807 297.754 35.43V35.642C297.754 37.6913 296.73 38.716 294.68 38.716H253.128C251.079 38.716 250.054 37.6913 250.054 35.642V35.43C250.054 33.3807 251.079 32.356 253.128 32.356H294.68ZM271.466 35.536C273.586 35.536 274.646 36.596 274.646 38.716V74.65C274.646 76.77 273.586 77.83 271.466 77.83H271.254C269.134 77.83 268.074 76.77 268.074 74.65V38.716C268.074 36.596 269.134 35.536 271.254 35.536H271.466ZM282.49 46.772C284.257 45.8887 285.6 46.348 286.518 48.15L293.885 62.884C294.769 64.6507 294.309 65.9933 292.507 66.912L292.295 67.018C290.529 67.9013 289.186 67.442 288.267 65.64L280.9 50.906C280.017 49.1393 280.476 47.7967 282.278 46.878L282.49 46.772Z" fill="white"/>
<g filter="url(#filter3_ii_8643_32065)">
<path d="M364.739 63.2239C368.962 64.8705 370.348 69.3119 368.571 73.8691C367.192 77.4056 362.473 80.0525 354.462 81.9703C353.936 82.0956 353.384 82.0575 352.881 81.8611C352.377 81.6647 351.945 81.3194 351.643 80.8712C347.045 74.0364 345.363 68.8939 346.742 65.3574C348.519 60.8002 352.546 58.4694 356.769 60.116C358.294 60.7107 359.121 61.6469 360.075 63.4118C361.972 62.7591 363.214 62.6292 364.739 63.2239Z" fill="url(#paint2_radial_8643_32065)"/>
</g>
<g filter="url(#filter4_ii_8643_32065)">
<path d="M350.363 72.1591C357.383 74.0166 360.393 80.8364 358.389 88.4126C356.833 94.2919 349.8 99.3592 337.393 103.861C336.579 104.156 335.694 104.195 334.857 103.974C334.019 103.752 333.27 103.28 332.708 102.622C324.151 92.5728 320.544 84.6903 322.1 78.811C324.104 71.2348 330.093 66.7959 337.113 68.6534C339.648 69.3242 341.134 70.6651 342.972 73.3021C345.874 71.9198 347.828 71.4884 350.363 72.1591Z" fill="url(#paint3_radial_8643_32065)"/>
</g>
<defs>
<filter id="filter0_diiii_8643_32065" x="-20" y="4" width="409" height="155.146" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="28"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.0219671 0 0 0 0 0.256695 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8643_32065"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8643_32065" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="16" dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_8643_32065"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-45"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_8643_32065" result="effect3_innerShadow_8643_32065"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-10"/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_8643_32065" result="effect4_innerShadow_8643_32065"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_8643_32065" result="effect5_innerShadow_8643_32065"/>
</filter>
<filter id="filter1_f_8643_32065" x="20" y="0.367188" width="69.1445" height="92.3535" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_8643_32065"/>
</filter>
<filter id="filter2_f_8643_32065" x="30.0469" y="8.36719" width="40.1758" height="54.5098" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_8643_32065"/>
</filter>
<filter id="filter3_ii_8643_32065" x="345.275" y="54.5879" width="24.0508" height="31.4551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_8643_32065"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-5"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.953999 0 0 0 0 0.959519 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_8643_32065" result="effect2_innerShadow_8643_32065"/>
</filter>
<filter id="filter4_ii_8643_32065" x="320.748" y="63.2324" width="38.25" height="46.8828" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_8643_32065"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-5"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.953999 0 0 0 0 0.959519 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_8643_32065" result="effect2_innerShadow_8643_32065"/>
</filter>
<linearGradient id="paint0_linear_8643_32065" x1="41.945" y1="24.6992" x2="346.714" y2="90.5674" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB7C5"/>
<stop offset="0.505049" stop-color="#E32CFF"/>
<stop offset="1" stop-color="#FF49C5"/>
</linearGradient>
<linearGradient id="paint1_linear_8643_32065" x1="44.6753" y1="22.6515" x2="344.666" y2="90.5674" gradientUnits="userSpaceOnUse">
<stop stop-color="#F3D5FF"/>
<stop offset="0.515" stop-color="#D53FFF"/>
<stop offset="1" stop-color="#EBB8FF"/>
</linearGradient>
<radialGradient id="paint2_radial_8643_32065" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(351.397 62.493) rotate(85.6196) scale(19.4249 20.9999)">
<stop stop-color="#FF8FF9"/>
<stop offset="1" stop-color="#AD16FF"/>
</radialGradient>
<radialGradient id="paint3_radial_8643_32065" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(328.991 73.4091) rotate(79.1369) scale(31.1221 33.6455)">
<stop stop-color="#FF8FF9"/>
<stop offset="1" stop-color="#AD16FF"/>
</radialGradient>
</defs>
</svg>
