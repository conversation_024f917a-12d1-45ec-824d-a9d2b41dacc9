@forward "element-plus/theme-chalk/src/common/var.scss" with (
  $colors: (
    'white': #fff,
    'black': #000,
    'primary': (
      'base': #409eff,
    ),
    'success': (
      'base': #67c23a,
    ),
    'warning': (
      'base': #e6a23c,
    ),
    'danger': (
      'base': #f56c6c,
    ),
    'error': (
      'base': #f56c6c,
    ),
    'info': (
      'base': #909399,
    ),
  ),
  $text-color: (
    (
      'primary': #303133,
      'regular': #606266,
      'secondary': #909399,
      'placeholder': #a8abb2,
      'disabled': #c0c4cc,
    )
  ),
);

@use "element-plus/theme-chalk/src/index.scss";

/* Element - 暗黑主题 */
@use 'element-plus/theme-chalk/src/dark/css-vars.scss';
html.dark {
  --gl-sidebar-background-color: var(--el-bg-color);

  --gl-headbar-background-color: var(--el-bg-color);

  --gl-tabsbar-background-color: var(--el-bg-color);

  --gl-content-background-color: #222222;
  --gl-content-panel-background-color: var(--el-bg-color);

  .el-table .el-table__body-wrapper {background: var(--el-bg-color);}
}

// 修复 消息确认弹窗样式
.el-message-box__status {
  position: absolute !important;
}
// 修复 通知弹窗样式
.el-notification {
  &__icon {
    height: var(--el-notification-icon-size)!important;
    width: var(--el-notification-icon-size)!important;
    font-size: var(--el-notification-icon-size)!important;
  }
  &--success, &--warning, &--info, &--error {
    color: var(--el-notification-icon-color)!important;
  }
  &__closeBtn {
    position: absolute !important;
  }
}
// 弹窗处理 添加滚动条
.el-dialog {
  // max-height: 100%;
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    // 移除 弹窗 body 的上下padding
    // padding-top: 0px !important;
    // padding-bottom: 1px !important; // 设置为1是为了处理滚动条的问题
    overflow: auto;
  }
}

// 确保弹窗遮罩层正确显示
.el-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000 !important;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}

