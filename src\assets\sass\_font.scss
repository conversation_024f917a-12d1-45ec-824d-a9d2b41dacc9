@font-face {
  font-family: 'BarbaraHand';
  //src: url("../fonts/BarbaraHand.ttf") format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'AppleChancery';
  //src: url("../fonts/AppleChancery.ttf") format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'SimpleScript';
  //src: url("../fonts/SimpleScript.ttf") format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Yuanti SC";
  src: url("../fonts/STYuanti-SC-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("../fonts/Inter.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 已注释：改用系统自带 PingFang SC 字体，无需加载自定义字体文件
@font-face {
  font-family: 'PingFangSC';
  src: url("../fonts/PingFangSC-Regular.woff2") format('woff2'),
       url("../fonts/PingFangSC-Regular.ttf") format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
*/
