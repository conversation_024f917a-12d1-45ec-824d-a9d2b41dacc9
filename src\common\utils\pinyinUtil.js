// pinyinUtil.js
import { pinyin } from 'pinyin-pro';
let lastCharIndex = -1; 
export function getPinyinWithCharacters(text) {
    // return text.split('').map(char => ({
    //     character: char,
    //     // pinyin: pinyin(char, { style: pinyin.STYLE_NORMAL })[0], // 使用普通风格，不带音调
    //     pinyin: pinyin(char), // 使用普通风格，不带音调
    // }));
  const container = document.createElement('div');
  container.innerHTML = text;

  const result = [];
  lastCharIndex = -1; // 每次调用重置

  traverse(container, result);

  console.log(result, 999);
  return result;

}
function traverse(node, result) {
  if (node.nodeType === 3) {
    // 文本节点，判断父节点是否是多音字节点或按钮，跳过这些节点的文本
    const parent = node.parentNode;
    if (
      parent &&
      (parent.tagName.toLowerCase() === 'button' ||
        (parent.getAttribute('data-type') === 'number' && parent.getAttribute('data-attr') === 'duoyinzi'))
    ) {
      // 跳过多音字节点和按钮内的文本
      return;
    }

    const text = node.nodeValue;
    for (const char of text) {
      if (char.trim() === '') continue;
      const py = pinyin(char, { toneType: 'mark' }) || char;
      result.push({ character: char, pinyin: py });
      lastCharIndex = result.length - 1;
    }
  } else if (node.nodeType === 1) {
    const tagName = node.tagName.toLowerCase();
    const dataType = node.getAttribute('data-type');
    const dataAttr = node.getAttribute('data-attr');

    // 删除不需要的 div 节点，保留多音字节点
    if (
      tagName === 'div' &&
      (dataAttr || dataType) &&
      !(dataType === 'number' && dataAttr === 'duoyinzi')
    ) {
      if (node.parentNode) {
        node.parentNode.removeChild(node);
      }
      return;
    }

    // 多音字节点处理，覆盖最后一个字符拼音
    if (dataType === 'number' && dataAttr === 'duoyinzi') {
      const span = node.querySelector('span[data-side]');
      if (span && lastCharIndex >= 0) {
        result[lastCharIndex].pinyin = span.textContent.trim();
      }
      // 不遍历多音字节点内部，避免把拼音和按钮字符加入结果
      return;
    }

    // 跳过按钮节点
    if (tagName === 'button') {
      return;
    }

    // 遍历子节点
    const children = Array.from(node.childNodes);
    for (const child of children) {
      traverse(child, result);
    }
  }
}

// function traverse(node,result) {
//     if (node.nodeType === 3) {
//         // 文本节点
//         const text = node.nodeValue;
//         for (const char of text) {
//           if (char.trim() === '') continue;
//           const py = pinyin(char, { toneType: 'mark' }) || char;
//           result.push({ character: char, pinyin: py });
//           lastCharIndex = result.length - 1;
//         }
//       } else if (node.nodeType === 1) {

        
//         if (node.getAttribute('data-type') === 'number' && node.getAttribute('data-attr') === 'duoyinzi') {
//           // 多音字节点，覆盖最后一个字符的拼音
//           const span = node.querySelector('span[data-side]');
//           if (span && lastCharIndex >= 0) {
//             result[lastCharIndex].pinyin = span.textContent.trim();
//           }
//         } else {
//           const tagName = node.tagName.toLowerCase();
//           if (tagName === 'div'&&(node.getAttribute('data-attr')||node.getAttribute('data-type'))){
//             console.log(node,222);
//               node.parentNode.removeChild(node)
       
//           }
//           node.childNodes.forEach(child => traverse(child, result));
//           // const tagName = node.tagName.toLowerCase();

//           // // 先遍历子节点，避免删除当前节点后子节点无法遍历
//           // const children = Array.from(node.childNodes);
//           // for (const child of children) {
//           //   traverse(child, result);
//           // }
      
//           // // 删除条件
//           // if (
//           //   tagName === 'div' &&
//           //   (node.getAttribute('data-attr') || node.getAttribute('data-type'))
//           // ) {
//           //   if (node.parentNode) {
//           //     node.parentNode.removeChild(node);
//           //   }
//           //   return;
//           // }
//         }
//       }
//   }
  