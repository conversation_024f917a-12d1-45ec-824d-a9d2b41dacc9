<!-- AlbumSelector.vue -->
<template>
    <div class="album-dialog-container">
        <el-dialog :close-on-click-modal="false" :close-on-press-escape="false" v-model="visible" title="选择视频/音频"
            width="600px" :show-close="false" custom-class="album-selector-dialog">
            <!-- 固定操作区 -->
            <div class="fixed-header">
                <el-select v-model="selectedId" filterable placeholder="选择专辑" class="album-select" 
                    :loading="loading" no-data-text="暂无专辑数据" 
                    remote-show-suffix loading-text="加载中..."
                    :remote-method="filterAlbums"
                    @change="handleAlbumChange">
                    <template #empty v-if="!loading">
                        <div class="empty-options">
                            <el-empty description="暂无专辑数据" :image-size="60"></el-empty>
                        </div>
                    </template>
                    <el-option
                        v-for="album in albums"
                        :key="album.id"
                        :label="album.name"
                        :value="album.id">
                        <div class="album-option">
                            <span>{{ album.name }}</span>
                        </div>
                    </el-option>
                </el-select>
                
                <el-input v-model="searchText" :prefix-icon="Search" placeholder="搜索视频和音频名称" clearable
                    class="search-input" @input="filterMaterials" />
            </div>

            <!-- 素材搜索和列表展示区 -->
            <div class="materials-section">
                <div class="materials-container" v-loading="materialsLoading">
                    <template v-if="filteredMaterialsList.length > 0">
                        <div class="materials-list">
                            <div v-for="material in filteredMaterialsList" 
                                :key="material.materialId" 
                                class="material-item"
                                @click="handleMaterialSelect(material)">
                                <div class="custom-radio">
                                    <span class="radio-circle" :class="{'radio-active': selectedMaterialId === material.materialId}"></span>
                                </div>
                                
                                <div class="material-icon" v-if="!material.thumbnailPath">
                                    <el-icon><Document /></el-icon>
                                </div>
                                <div class="material-thumbnail" v-else>
                                    <el-image 
                                        :src="material.thumbnailPath" 
                                        fit="cover"
                                        :preview-src-list="[material.thumbnailPath]">
                                        <template #error>
                                            <el-icon><Document /></el-icon>
                                        </template>
                                    </el-image>
                                </div>
                                
                                <div class="material-info">
                                    <div class="material-name">{{ material.materialName || '未命名' }}</div>
                                    <div class="material-meta">
                                        {{ getTypeFromMaterial(material) }} {{ material.duration ? '| ' + formatDuration(material.duration) : '' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <el-empty v-else description="暂无视频/音频素材" :image-size="80"></el-empty>
                </div>
            </div>

            <!-- 固定底部 -->
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="info" class="cancel-btn" @click="handleCancel">
                        取消
                    </el-button>
                    <el-button type="success" class="confirm-btn" @click="handleConfirm" :disabled="!selectedId || !selectedMaterialId"
                        :loading="submitting">
                        确定
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, defineExpose } from "vue";
import { Calendar, Search, Document } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { spaceExtractFile } from "@/api/dubbing";
import { getUserAlbum, getMyMaterialList } from "@/api/myMaterial";
import {
    keepInterface,
} from "@/api/interfaceList";
import { useloginStore } from '@/stores/login'

// 组件属性定义
const props = defineProps({
    modelValue: Boolean, // 控制弹窗显示（v-model绑定）
});
// 事件定义（建议添加JSDoc说明）
const emit = defineEmits([
    "update:modelValue", // 用于v-model同步
    "confirm", // 确认选择时触发
    "cancel", // 取消操作时触发
]);

// 1. 先声明所有响应式变量
const searchText = ref("");
const selectedId = ref('all'); // 初始值设为'all'，默认选中全部专辑
const albums = ref([]);
const filteredAlbums = ref([]);
const loading = ref(false);
const allAlbums = ref([]); // 保存所有专辑数据
const selectedMaterialId = ref(null); // 新增：选中的素材ID

// 2. 再声明计算属性
const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
});

// 添加 dialog loading 状态
const dialogLoading = ref(false);

// 新增：素材列表相关状态
const materialsList = ref([]);
const filteredMaterialsList = ref([]); // 新增：过滤后的素材列表
const materialsLoading = ref(false);

// 监听专辑选择变化
const handleAlbumChange = (albumId) => {
    console.log('选择的专辑ID:', albumId);
    // 每次专辑切换时立即调用page接口
    loadMaterialsList();
};

// 添加监听searchText变化的逻辑
watch(searchText, (newValue) => {
    // 只过滤素材列表，不过滤专辑列表
    filterMaterials();
});

// 3. 监听visible是否为true
watch(
    visible,
    async (newVal) => {
        if (newVal) {
            dialogLoading.value = true;
            resetSelection(); // 重置选择状态
            try {
                // 清空搜索文本
                searchText.value = "";
                // 移除这里对loadAlbums的调用，避免重复请求
                // await loadAlbums(); <- 删除此行
                
                // 注意：loadAlbums现在只由父组件在打开弹窗前调用
            } finally {
                dialogLoading.value = false;
            }
        }
    },
    { immediate: true }
);



// 我的空间列表接口 - 直接加载专辑列表
const loadAlbums = async (retries = 3) => {
    try {
        loading.value = true;
        console.log('开始加载专辑列表，剩余重试次数:', retries);
        
        // 直接调用，不传参数
        const res = await getUserAlbum();
        console.log('API响应:', res);
        
        // 处理返回的专辑列表数据 - 专注于ID和名称
        let albumArr = [];
        if (Array.isArray(res)) {
            albumArr = res.map(item => ({
                id: item.tagId,
                name: item.tagName,
            }));
        } else if (res && Array.isArray(res.data)) {
            albumArr = res.data.map(item => ({
                id: item.tagId,
                name: item.tagName,
            }));
        } else if (res && typeof res === 'object') {
            // 尝试找到响应中的数据数组
            const dataArray = Object.values(res).find(val => Array.isArray(val));
            if (dataArray) {
                albumArr = dataArray.map(item => ({
                    id: item.tagId || item.id,
                    name: item.tagName || item.name,
                }));
            } else {
                albumArr = [];
                console.warn('无法在响应中找到数组数据');
            }
        } else {
            albumArr = [];
            console.warn('API返回的数据格式不正确');
        }
        // 在最前面插入"全部专辑"
        allAlbums.value = [{ id: 'all', name: '全部专辑' }, ...albumArr];
        albums.value = [...allAlbums.value];
        console.log('处理后的专辑数据:', albums.value);
        // 如果当前没有选中任何专辑，默认选中全部专辑
        if (selectedId.value === null || selectedId.value === undefined || selectedId.value === '') {
            selectedId.value = 'all';
        }
        // 只要list接口成功，无条件直接调page接口
        loadMaterialsList();
        
    } catch (error) {
        console.error("加载失败:", error);
        ElMessage.error("加载专辑列表失败");
        
        // 重试机制
        if (retries > 0) {
            console.log(`加载失败，${retries}秒后重试...`);
            setTimeout(() => loadAlbums(retries - 1), 1000);
            return; // 提前返回，避免设置loading为false
        }
    } finally {
        loading.value = false;
    }
};

// 新增：加载素材列表方法
const loginStore = useloginStore()
const getUserId = () => loginStore.userId || ''
// 添加标记变量防止重复请求
let isLoadingMaterialsList = false;
const loadMaterialsList = async () => {
    if (isLoadingMaterialsList) {
        console.log('正在加载素材列表，忽略重复请求');
        return;
    }
    isLoadingMaterialsList = true;
    try {
        materialsLoading.value = true;
        // 构造新参数格式
        const params = {
            userId: getUserId(),
            pageParam: {
                pageNum: 1,
                pageSize: 100
            }
        };
        // 只根据当前selectedId请求（可能为'all'）
        if (selectedId.value !== undefined && selectedId.value !== null && selectedId.value !== '' && selectedId.value !== 'all') {
            params.tagIds = selectedId.value;
            console.log('handleAlbumChange传递的tagId:', selectedId.value);
        } else {
            console.log('选择全部专辑，tagIds不传');
        }
        if (!params.userId) {
            console.warn('未能获取到用户ID');
        }
        console.log('获取素材列表参数:', params);
        const response = await getMyMaterialList(params);
        console.log('获取素材列表成功:', response);
        if (response) {
            console.log('获取素材列表成功, 条数:', response.list?.length || 0);
            materialsList.value = response.list || [];
            filterMaterials();
        } else {
            console.warn('获取素材列表失败:', response);
            materialsList.value = [];
            filteredMaterialsList.value = [];
            ElMessage.warning('获取素材列表失败');
        }
    } catch (error) {
        console.error('加载素材列表出错:', error);
        ElMessage.error('加载素材列表出错');
        materialsList.value = [];
        filteredMaterialsList.value = [];
    } finally {
        materialsLoading.value = false;
        isLoadingMaterialsList = false;
    }
};

// 辅助方法：获取素材类型
const getTypeFromMaterial = (material) => {
    switch (material.materialType) {
        case 'audio': return '音频';
        case 'video': return '视频';
        case 'bgm': return '背景音乐';
        case 'effect': return '特效音';
        case 'text': return '文本';
        case 'image': return '头像';
        case 'other': return '其他';
        default: return '未知';
    }
};

// 辅助方法：格式化时长
const formatDuration = (seconds) => {
    if (!seconds) return '--';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 筛选专辑，仅当专辑下拉框使用时触发
const filterAlbums = (query) => {
    if (!query) {
        albums.value = [...allAlbums.value];
        return;
    }
    
    albums.value = allAlbums.value.filter(album => 
        album.name.toLowerCase().includes(query.toLowerCase())
    );
};

// 重用searchText变量，不使用materialSearchText
const filterMaterials = () => {
    // 使用searchText变量进行过滤
    const query = searchText.value;
    console.log('过滤素材列表，原始数据:', materialsList.value);
    if (!query) {
        // 如果没有搜索文本，只显示视频和音频素材
        filteredMaterialsList.value = materialsList.value.filter(material => 
            material.materialType === 'audio' || material.materialType === 'video'
        );
        return;
    }
    
    // 模糊搜索：根据素材名称过滤，只搜索视频和音频
    const lowerQuery = query.toLowerCase();
    filteredMaterialsList.value = materialsList.value.filter(material => {
        // 只保留视频和音频类型
        if (material.materialType !== 'audio' && material.materialType !== 'video') {
            return false;
        }
        
        const name = material.materialName || '';
        return name.toLowerCase().includes(lowerQuery);
    });
};

// 添加响应式状态
const submitting = ref(false);

// 新增：处理素材选择的方法
const handleMaterialSelect = (material) => {
    console.log('选中素材:', material);
    selectedMaterialId.value = material.materialId;
};

// 确认选择（建议添加参数校验）
const handleConfirm = async () => {
    if (!selectedId.value) {
        ElMessage.warning("请选择要操作的专辑");
        return;
    }

    // 检查是否选择了素材
    if (!selectedMaterialId.value) {
        ElMessage.warning("请选择视频或音频素材");
        return;
    }

    submitting.value = true;
    try {
        // 查找选中的素材
        const selectedMaterial = materialsList.value.find(item => item.materialId === selectedMaterialId.value);
        if (!selectedMaterial) {
            throw new Error("未找到选中的素材");
        }

        // 验证是否为视频或音频类型
        if (selectedMaterial.materialType !== 'audio' && selectedMaterial.materialType !== 'video') {
            ElMessage.warning("只能选择视频或音频类型的素材");
            return;
        }

        // 返回正确的素材信息，而不是专辑信息
        const updatedMaterial = {
            // 使用素材的名称和ID，而不是专辑的
            name: selectedMaterial.materialName || '未命名', 
            id: selectedMaterial.materialId,
            // 保留专辑信息，可能在其他地方需要
            tagId: selectedId.value,
            tagName: allAlbums.value.find(album => album.id === selectedId.value)?.name || '',
            // 使用素材URL
            url: selectedMaterial.storagePath,
            // 其他素材相关信息
            materialType: selectedMaterial.materialType,
            duration: selectedMaterial.duration,
            thumbnailPath: selectedMaterial.thumbnailPath,
            // 完整素材对象
            material: selectedMaterial
        };

        console.log('确认选择, 返回数据:', updatedMaterial);
        emit("confirm", updatedMaterial);
        // 清空搜索文本
        searchText.value = "";
        visible.value = false;
    } catch (error) {
        console.error("操作失败:", error);
        ElMessage.error("操作失败，请重试");
    } finally {
        submitting.value = false;
    }
};

// 添加清理方法
const resetSelection = () => {
    selectedMaterialId.value = null;
};

// 更新取消方法，清空选择状态
const handleCancel = () => {
    selectedId.value = null;
    searchText.value = ""; // 清空搜索文本
    resetSelection();
    emit("cancel", "取消"); // 触发取消事件
    visible.value = false;
};

// 添加一个默认选项
onMounted(() => {
    // 等数据加载完成后再执行操作
    watch(allAlbums, (newValue) => {
        if (newValue.length > 0 && !selectedId.value) {
            // 可以默认选中第一个专辑
            selectedId.value = newValue[0].id;
        }
    });
});

// 添加在脚本末尾，暴露方法供外部调用
defineExpose({
    loadAlbums
});
</script>

<style lang="scss" scoped>
/* 样式部分（使用深度选择器覆盖element样式） */
.album-dialog-container {
    :deep(.el-dialog),
    :deep(.el-dialog__wrapper),
    :deep(.el-dialog__body),
    :deep(.el-overlay) {
        overflow: visible !important;
    }

    :deep(.el-dialog) {
        border-radius: 10px;
        padding: 0;
        max-height: 600px;

        /* 头部样式定制 */
        .el-dialog__header {
            margin: 0;
            padding: 16px 20px;
            // border-bottom: 1px solid #e4e7ed;

            .el-dialog__title {
                color: #000000;
                font-size: 18px;
            }
        }

        /* 主体内容布局 */
        .el-dialog__body {
            padding: 20px;
            display: flex;
            flex-direction: column;

            /* 顶部操作栏样式 */
            .fixed-header {
                display: flex;
                gap: 12px;
                margin-bottom: 20px;

                .album-select {
                    width: 280px;
                }

                .search-input {
                    width: 280px;
                }
            }

            /* 素材区域样式 */
            .materials-section {
                height: 380px;
            }

            /* 素材列表容器 */
            .materials-container {
                height: 100%;
                overflow-y: auto;
                min-height: unset;
                border: 1px solid #e4e7ed;
                border-radius: 6px;
                padding: 10px;
                
                .materials-list {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    
                    .material-item {
                        display: flex;
                        align-items: center;
                        padding: 10px;
                        border-radius: 4px;
                        background-color: #f5f7fa;
                        cursor: pointer;
                        transition: background-color 0.2s;
                        
                        &:hover {
                            background-color: #e9ecf2;
                        }
                        
                        .custom-radio {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 10px;
                            
                            .radio-circle {
                                width: 16px;
                                height: 16px;
                                border: 1px solid #DCDFE6;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                background-color: #FFF;
                                position: relative;
                                transition: all 0.2s;
                                
                                &.radio-active {
                                    border-color: #4caf50;
                                    
                                    &::after {
                                        content: '';
                                        width: 8px;
                                        height: 8px;
                                        border-radius: 50%;
                                        background-color: #4caf50;
                                        position: absolute;
                                    }
                                }
                            }
                        }
                        
                        .material-icon {
                            margin-right: 12px;
                            color: #909399;
                            font-size: 20px;
                            width: 40px;
                            height: 40px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        
                        .material-thumbnail {
                            margin-right: 12px;
                            width: 40px;
                            height: 40px;
                            border-radius: 4px;
                            overflow: hidden;
                            
                            .el-image {
                                width: 100%;
                                height: 100%;
                                display: block;
                            }
                        }
                        
                        .material-info {
                            flex: 1;
                            
                            .material-name {
                                font-size: 14px;
                                margin-bottom: 4px;
                                color: #303133;
                                font-weight: 500;
                            }
                            
                            .material-meta {
                                font-size: 12px;
                                color: #909399;
                            }
                        }
                    }
                }
            }

            /* 专辑选项样式 */
            .album-option {
                display: flex;
                align-items: center;
                width: 100%;
            }
            
            /* 空数据状态样式 */
            .empty-options {
                text-align: center;
                padding: 10px 0;
            }
        }

        /* 底部按钮布局 */
        .el-dialog__footer {
            padding: 16px 20px;
            // border-top: 1px solid #e4e7ed;

            .dialog-footer {
                display: flex;
                justify-content: center;
                gap: 20px;

                .cancel-btn {
                    width: 100px;
                }

                .confirm-btn {
                    width: 100px;
                    background: #4caf50;
                    border-color: #4caf50;
                }
            }
        }
    }

    :deep(.materials-container) {
        /* Webkit 滚动条美化 */
        &::-webkit-scrollbar {
            width: 6px;
            background: #f0f0f0;
        }
        &::-webkit-scrollbar-thumb {
            background: #d4d4d4;
            border-radius: 6px;
            transition: background 0.2s;
        }
        &::-webkit-scrollbar-thumb:hover {
            background: #bdbdbd;
        }
        &::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 6px;
        }
        /* Firefox 滚动条美化 */
        scrollbar-width: thin;
        scrollbar-color: #d4d4d4 #f0f0f0;
    }
}
</style>