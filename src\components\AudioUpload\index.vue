<template>
    <el-upload class="video-uploader" ref="uploadRef" :accept="acceptType === 'video' ? '.mp4' : '.mp3,.wav'"
        :http-request="customUpload" :auto-upload="false" :show-file-list="false" :before-upload="beforeUpload"
        @change="handleFileChange">
        <!-- 修改上传进度遮罩 -->
        <div v-if="isUploading" class="upload-mask">
            <div class="upload-progress">
                <el-progress :percentage="uploadProgress" :stroke-width="20" :show-text="false" />
                <div class="progress-text">正在上传中 {{ uploadProgress }}%</div>
            </div>
        </div>

        <!-- 上传后状态（非触发区域） -->
        <template v-if="currentFile">
            <div class="file-container" @click.stop="handleUploadClick">
                <!-- 左侧图标+文字 -->
                <div class="left-group">
                    <!-- <el-icon class="file-icon"><VideoCamera /></el-icon> -->
                    <img class="file-icon" src="../../assets//img/1.png" alt="" />
                    <span class="file-name">{{ currentFile.name }}</span>
                </div>

                <!-- 右侧操作按钮 -->
                <div class="right-group">
                    <el-button @click.stop="triggerUpload" link type="primary" class="action-btn">
                        重新上传
                    </el-button>
                    <el-divider direction="vertical" />
                    <el-button @click.stop="handleRemove" link type="danger" class="action-btn">
                        删除
                    </el-button>
                </div>
            </div>
        </template>

        <!-- 上传前状态（全区域可触发） -->
        <template v-else>
            <div class="upload-card" @click.stop="handleUploadClick">
                <div class="text-group">
                    <div class="merger">
                        <div class="plus-layout">+</div>
                        <div class="main-text">{{ upperText }}</div>
                    </div>
                    <div class="sub-text">
                        {{ lowerTextBefore }}
                        <span class="highlight-text">{{ highlightText }}</span>
                        {{ lowerTextAfter }}
                    </div>
                </div>
            </div>
        </template>
    </el-upload>

    <!-- 添加空间不足对话框 -->
    <AlertDialog v-model:visible="insufficientSpaceDialogVisible" type="warning" :title="insufficientSpaceTitle"
        :sub-title="insufficientSpaceSubTitle" :message="insufficientSpaceMessage" show-cancel-button
        cancel-button-text="暂不购买" confirm-button-text="立即购买" :show-fee-explanation="false" :custom-confirm-class="true"
        @confirm="handleBuySpace" @cancel="handleCancelBuy" />
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { VideoCameraFilled, Close } from "@element-plus/icons-vue";
import { dubbing, callbackOss } from '@/api/dubbing'
import { globalConfig } from '@/config'
import { useRoute, useRouter } from 'vue-router';
import { checkUploadPermission } from '@/api/upload'; // 导入校验空间接口
import AlertDialog from '@/views/components/AlertDialog.vue' // 导入AlertDialog组件
// 导入友盟埋点hook和模块
import { useUmeng } from '@/utils/umeng/hook';
import { createAudioUploadAnalytics } from '@/utils/umeng/modules/audioUpload';

// 获取组件实例，用于访问$modal
const { proxy } = getCurrentInstance();

// 初始化埋点
const umeng = useUmeng();
const audioUploadAnalytics = createAudioUploadAnalytics(umeng);

const route = useRoute();
const router = useRouter();

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

/**
 * 用户登录检查函数
 * 从localStorage获取用户信息并检查是否登录
 * @returns {boolean} 是否已登录
 */
const checkUserLogin = () => {
    const userStorage = localStorage.getItem('user');
    if (userStorage) {
        try {
            const userData = JSON.parse(userStorage);
            // 检查token是否为null或空
            return userData && userData.token && userData.token.trim() !== '';
        } catch (e) {
            console.error('解析用户数据失败', e);
            return false;
        }
    }
    return false;
}

const props = defineProps({
    // 动态文本配置（提供国际化扩展能力）
    upperText: {
        type: String,
        default: "上传视频",
    },
    lowerTextBefore: {
        type: String,
        default: "上传",
    },
    highlightText: {
        type: String,
        default: "非静音",
    },
    lowerTextAfter: {
        type: String,
        default: "的MP4文件",
    },
    // 加号图标尺寸配置
    plusSize: {
        type: String,
        default: "28px",
    },
    // 上传类型控制（video/audio）
    acceptType: {
        type: String,
        default: "video", // 或 'audio'
        validator: (value) => ["video", "audio"].includes(value),
    },
    // 前置拦截函数（可用于权限校验等场景）
    beforeTrigger: {
        type: Function,
        default: null,
    },
    maxSizeMB: {
        type: Number,
        default: 1024, // 默认1G
    },
    // 添加新的props
    type: {
        type: String,
        required: true
    },
    id: {
        type: [String, Number],
        required: true
    },
    externalFile: {
        type: Object,
        default: null
    },
    // 新增：是否跳过空间限制检查（仅用于指定页面）
    skipSpaceCheck: {
        type: Boolean,
        default: false
    }
});

// 定义事件
const emit = defineEmits([
    "upload-success",
    "upload-error",
    "upload-complete"  // 添加新的事件
]);

// Upload组件实例引用
const uploadRef = ref(null);
// 当前文件对象（包含name和url）
const currentFile = ref(null);

watch(() => props.externalFile, (newVal) => {
    if (newVal && (newVal.url || newVal.name)) { // 检查 newVal 是否有效
        currentFile.value = { ...newVal };
    } else {
        currentFile.value = null; // 如果无效，则设为 null
    }
}, { immediate: true, deep: true });

const UPLOAD_SUCCESS_MESSAGE = '上传成功';
const UPLOAD_ERROR_MESSAGE = '上传失败';

// 添加上传状态和进度变量
const isUploading = ref(false);
const uploadProgress = ref(0);

// 添加空间不足对话框相关变量
const insufficientSpaceDialogVisible = ref(false);
const insufficientSpaceTitle = ref('');
const insufficientSpaceSubTitle = ref('您的个人空间容量已不足');
const insufficientSpaceMessage = ref('如需使用请购买空间额度！');

// 添加文件类型判断的公共函数
const getFileType = (fileName) => {
    const fileExtension = fileName.split('.').pop().toLowerCase();
    return fileExtension === 'mp4' ? 'mp4' : 'mp3';
};

const setExternalFile = (file) => {
    if (file && (file.url || file.name)) {
        currentFile.value = { ...file };
    } else {
        currentFile.value = null;
    }
};

// 自定义上传方法
const customUpload = async (options) => {
    try {
        const fileType = getFileType(file.name);
        const response = await dubbing({ userId: getUserId(), fileType }, { hideSuccessMessage: true });
        const fileUrl = `${response.host}/${response.key}?Expires=${response.expire}&OSSAccessKeyId=${response.accessid}&Signature=${encodeURIComponent(response.signature)}`;

        currentFile.value = {
            name: file.name,
            url: fileUrl
        };

        emit("upload-success", {
            file,
            url: fileUrl,
            name: file.name,
            size: file.size,
            type: file.type
        });

        ElMessage.success(UPLOAD_SUCCESS_MESSAGE);
    } catch (error) {
        handleUploadError(error, options.file);
    }
};

// 文件状态变更处理
const handleFileChange = async (file) => {
    try {
        // 检查用户登录状态
        if (!checkUserLogin()) {
            // 用户未登录，弹出登录弹窗
            proxy.$modal.open('组合式标题');
            return;
        }

        // 添加埋点记录 - 文件选择
        audioUploadAnalytics.trackFileSelect(props.acceptType, props.type);

        // 显示全局 loading
        const loadingInstance = ElLoading.service({
            lock: true,
            text: '权限检查中...',
            background: 'rgba(0, 0, 0, 0.7)'
        });

        // 空间权限检查 - 根据skipSpaceCheck属性决定是否检查
        if (!props.skipSpaceCheck) {
            const fileSizeMB = Math.ceil(file.raw.size / 1024 / 1024); // 转换为MB并向上取整
            const userId = getUserId();
            const response = await checkUploadPermission({
                userId: userId,
                feat: "空间",
                need: fileSizeMB
            });
            console.log(response, 'response');

            // 关闭 loading
            loadingInstance.close();

            // 检查返回结果
            if (response && response.content && response.content.result === false) {
                // 显示空间不足对话框而不是错误消息
                insufficientSpaceDialogVisible.value = true;
                return;
            }
        } else {
            // 跳过空间检查，直接关闭loading
            loadingInstance.close();
        }

        isUploading.value = true;
        uploadProgress.value = 0;

        const fileType = getFileType(file.name);
        const response2 = await dubbing({ userId: getUserId(), fileType });

        // 生成时间戳序列名
        const timestamp = new Date().getTime();
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const newFileName = `${timestamp}.${fileExtension}`;
        // 去掉文件名的后缀
        const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');

        const formData = new FormData();
        // 确保 key 的名称完全正确
        formData.append('OSSAccessKeyId', response2.accessKeyId);
        formData.append('policy', response2.policy);
        formData.append('signature', response2.signature);
        formData.append('key', `${response2.key.replace(/[^\/]+$/, '')}${file.name}`); // 替换原文件名为新的序列名
        formData.append('file', file.raw);

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    uploadProgress.value = Math.round(event.loaded / event.total * 100);
                }
            };

            xhr.onload = async () => {
                const fileUrl = `${response2.host}/${response2.key}?Expires=${response2.expire}&OSSAccessKeyId=${response2.accessid}&Signature=${encodeURIComponent(response2.signature)}`;


                const fileExtension = file.name.split('.').pop().toLowerCase();


                // 判断文件类型
                const materialType = fileExtension === 'mp4' ? 'video' : 'audio';
                const userId = getUserId();

                // 尝试获取文件时长
                let durationInSeconds = 0;
                try {
                    console.log(`尝试获取${materialType}文件时长...`);
                    const durationInfo = await getMediaDuration(file.raw, materialType);
                    durationInSeconds = durationInfo.durationInSeconds;
                    console.log(`成功获取${materialType}时长:`, durationInSeconds, '秒,', durationInfo.formattedDuration);
                } catch (error) {
                    console.error(`获取${materialType}时长失败，使用默认值0:`, error);
                }

                try {
                    // 调用 callbackOss 接口并获取返回值
                    const callbackParams = {
                        userId: userId,
                        materialName: fileNameWithoutExt,
                        ossPath: response2.key.replace(/[^\/]+$/, '') + file.name, // 使用新的文件名
                        fileSize: String(file.size),
                        fileExtension: fileExtension,
                        tagNames: '1',
                        materialType: materialType,
                        isPrivate: '1',
                        storage_path: `/material/${userId}/${file.name}`,
                        duration: durationInSeconds // 添加时长参数
                    };

                    // 仅在文案提取和去水印功能时添加 type: "1" 参数
                    if (props.type === 'extraction' || props.type === 'watermark') {
                        callbackParams.type = "1";
                    }

                    const callbackResponse = await callbackOss(callbackParams);


                    // 使用回调接口返回的 filename 更新显示
                    currentFile.value = {
                        name: callbackResponse.filename || file.name, // 如果返回的 filename 不存在则使用原文件名作为后备
                        url: callbackResponse.url
                    };

                    console.log(currentFile.value);

                    emit("upload-success", {
                        file,
                        url: currentFile.value.url,
                        name: currentFile.value.name,
                        size: file.size,
                        type: file.type
                    });

                    // 发送上传完成事件，包含所有必要的信息
                    emit("upload-complete", {
                        success: true,
                        fileInfo: {
                            url: fileUrl,
                            name: callbackResponse.filename || file.name,
                            size: file.size,
                            type: file.type,
                            materialType: materialType,
                            response: callbackResponse  // 包含回调接口的返回信息
                        }
                    });

                    // 添加埋点记录 - 上传成功
                    const fileSizeMB = Math.ceil(file.size / 1024 / 1024); // 仅用于埋点
                    audioUploadAnalytics.trackUploadSuccess(props.acceptType, fileSizeMB, props.type);

                    isUploading.value = false;
                    uploadProgress.value = 100;
                    resolve();
                } catch (error) {
                    emit("upload-complete", {
                        success: false,
                        error: error
                    });
                    ElMessage.error('文件上传成功，但回调处理失败');
                }
            };

            xhr.onerror = (error) => {
                isUploading.value = false;
                uploadProgress.value = 0;
                handleUploadError(error, file);
                reject(new Error(UPLOAD_ERROR_MESSAGE));
            };

            xhr.open('POST', response2.host, true);
            xhr.send(formData);
        });
    } catch (error) {
        isUploading.value = false;
        uploadProgress.value = 0;
        handleUploadError(error, file);
    }
};

// 处理上传成功
const handleUploadSuccess = async (response, file, fileList) => {
    // 尝试使用备用方案
    if (currentFile.value && currentFile.value.url) {
        emit("upload-success", {
            file: file,
            url: currentFile.value.url,
            name: file.name,
            size: file.size,
            type: file.type,
            isBackup: true
        });
        ElMessage.info('使用本地URL作为备用');
    } else {
        // 如果实在没有可用URL，报告错误
        emit("upload-error", new Error('无法获取有效的文件URL'));
    }
};

// 处理上传错误
const handleUploadError = (error, file) => {
    // 添加埋点记录 - 上传失败
    audioUploadAnalytics.trackUploadError(props.acceptType, error.message || '未知错误', props.type);

    ElMessage.error(`${UPLOAD_ERROR_MESSAGE}: ${error.message || '未知错误'}`);
    emit("upload-error", error);
};

// 重新上传逻辑
const triggerUpload = async () => {
    try {
        // 检查用户登录状态
        if (!checkUserLogin()) {
            // 用户未登录，弹出登录弹窗
            proxy.$modal.open('组合式标题');
            return;
        }

        // 添加埋点记录 - 重新上传
        audioUploadAnalytics.trackReupload(props.acceptType, props.type);

        // 执行前置条件检查（如存在）
        if (props.beforeTrigger) {
            await props.beforeTrigger();
        } else {
            // 直接触发文件选择
            uploadRef.value?.$el.querySelector(".el-upload__input")?.click();
        }
    } catch (error) {
        // 拦截上传流程
    }
};

// 文件删除处理
const handleRemove = () => {
    // 添加埋点记录 - 删除文件
    audioUploadAnalytics.trackFileRemove(props.acceptType, props.type);

    if (currentFile.value?.url) {
        URL.revokeObjectURL(currentFile.value.url);
    }
    currentFile.value = null;
    uploadRef.value.clearFiles();
};

// 文件类型校验（Element Upload规范）
const beforeUpload = async (file) => {
    const extension = file.name.split(".").pop().toLowerCase();
    // 视频类型校验
    const isValidType = {
        video: extension === "mp4" && file.type === "video/mp4",
        audio:
            ["mp3", "wav"].includes(extension) &&
            ["audio/mpeg", "audio/wav"].includes(file.type),
    }[props.acceptType];

    // 音频类型校验
    if (!isValidType) {
        ElMessage.error(
            `请上传有效的${props.acceptType === "video" ? "MP4视频" : "MP3/WAV音频"
            }文件`
        );
        return true;
    }

    // 大小校验
    const isSizeValid = file.size / 1024 / 1024 < props.maxSizeMB;
    if (!isSizeValid) {
        ElMessage.error(`文件大小不能超过 ${props.maxSizeMB}MB`);
        return false;
    }

    // 通过所有校验，允许上传
    return true;
};

// 统一点击处理（兼容已上传状态）
const handleUploadClick = async () => {
    try {
        // 检查用户登录状态
        if (!checkUserLogin()) {
            // 用户未登录，弹出登录弹窗
            proxy.$modal.open('组合式标题');
            return;
        }

        // 执行前置条件检查
        if (props.beforeTrigger) {
            await props.beforeTrigger();
        } else {
            triggerUpload();
        }
    } catch (error) {
        // 中断上传流程
    }
};

// 添加手动上传方法
const submitUpload = () => {
    uploadRef.value.submit();
};

// 工具函数：多次decode确保OSS链接可用
function decodeOSSUrl(url) {
    try {
        url = decodeURIComponent(url);
        if (url.includes('%')) url = decodeURIComponent(url);
    } catch { }
    return url;
}

// 获取媒体文件时长
const getMediaDuration = (file, mediaType) => {
    return new Promise((resolve, reject) => {
        // 根据不同的媒体类型采用不同的处理方式
        if (mediaType === 'video') {
            // 获取视频时长
            const video = document.createElement('video');
            video.preload = 'metadata';

            video.onloadedmetadata = () => {
                const durationInSeconds = Math.round(video.duration);
                const minutes = Math.floor(durationInSeconds / 60);
                const seconds = durationInSeconds % 60;
                const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                // 清理资源
                video.src = '';

                resolve({
                    durationInSeconds,
                    formattedDuration
                });
            };

            video.onerror = () => {
                reject(new Error('无法加载视频元数据'));
            };

            video.src = URL.createObjectURL(file);
        } else if (mediaType === 'audio') {
            // 获取音频时长
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const reader = new FileReader();

            reader.onload = (event) => {
                audioContext.decodeAudioData(event.target.result)
                    .then((audioBuffer) => {
                        const durationInSeconds = Math.round(audioBuffer.duration);
                        const minutes = Math.floor(durationInSeconds / 60);
                        const seconds = durationInSeconds % 60;
                        const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                        resolve({
                            durationInSeconds,
                            formattedDuration
                        });
                    })
                    .catch(reject);
            };

            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
        } else {
            reject(new Error('不支持的媒体类型'));
        }
    });
};

// 处理购买空间
const handleBuySpace = () => {
    insufficientSpaceDialogVisible.value = false;
    // 在新标签页打开购买空间页面
    const route = router.resolve({ name: 'membership', query: { nav: 'space' } });
    window.open(route.href, '_blank');
}

// 处理取消购买
const handleCancelBuy = () => {
    insufficientSpaceDialogVisible.value = false;
}

onMounted(() => {
    let url = route.query.url;
    let title = route.query.title;
    if (url) {
        url = decodeOSSUrl(url);
        if (!title) {
            // 自动从url中提取文件名
            try {
                title = decodeURIComponent(url.split('/').pop().split('?')[0]);
            } catch {
                title = '从链接传入的视频';
            }
        }
        currentFile.value = {
            name: title,
            url
        };
    }
});

// 暴露当前文件状态给父组件
defineExpose({
    currentFile,
    submitUpload,
    setExternalFile // 新增暴露的方法
});
</script>

<style lang="scss" scoped>
.video-uploader {
    width: 640px; // 修改宽度为640px
    height: 160px; // 修改高度为160px
    position: relative;

    // 深度选择器穿透Element样式
    :deep(.el-upload) {
        width: 100%;
        height: 100%;

        .uploaded-item {
            width: 100%;
            height: 100%;
            padding: 12px;
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border-radius: 8px;
            border: 1px solid #ebeef5;

            .file-icon {
                width: 28px;
                height: 28px;
                font-size: 28px;
                color: #606266;
                margin-right: 16px;
            }

            .file-info {
                flex: 1;
                min-width: 0;

                .file-name {
                    font-size: 14px;
                    color: #303133;
                    // @include text-ellipsis;
                }

                .file-actions {
                    margin-top: 6px;
                    display: flex;
                    align-items: center;
                    gap: 16px;

                    .reupload {
                        color: #409eff;
                        font-size: 12px;
                        cursor: pointer;

                        &:hover {
                            color: #79bbff;
                        }
                    }

                    .delete-btn {
                        color: #f56c6c;
                        cursor: pointer;
                        padding: 4px;
                        border-radius: 50%;

                        &:hover {
                            background: #fef0f0;
                        }
                    }
                }
            }
        }

        .upload-card {
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            height: 100%;
            border: 1px dashed #D9DCE1; // 修改边框颜色为#D9DCE1
            border-radius: 8px;
            background: #FFFFFF;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: border-color 0.3s;

            &:hover {
                border-color: #0AAF60; // 修改hover状态下的边框颜色与主色调一致
            }

            .plus-layout {
                font-size: v-bind("props.plusSize");
                color: #0AAF60;
                margin-bottom: 7px;
            }

            .text-group {
                text-align: center;

                .merger {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .main-text {
                    font-size: 14px;
                    color: #0AAF60;
                    padding-left: 2px;
                }

                .sub-text {
                    font-size: 14px;
                    color: #606266;

                    .highlight-text {
                        color: #f56c6c;
                        font-weight: 500;
                        margin: 0 2px;
                    }
                }
            }
        }

        // 上传后状态（非触发区域）
        .file-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%; // 确保高度充满父容器
            padding: 26px;
            border: 1px solid #D9DCE1; // 与upload-card保持一致的边框
            border-radius: 8px;
            background: #ffffff;

            .left-group {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;
                min-width: 0;

                .file-icon {
                    width: 28px;
                    height: 28px;
                    font-size: 24px;
                    color: #606266;
                }

                .file-name {
                    font-size: 14px;
                    color: #303133;
                    // @include text-ellipsis;
                }
            }

            .right-group {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-left: 16px;

                :deep(.el-divider--vertical) {
                    margin: 0 4px;
                    height: 1em;
                }

                .action-btn {
                    width: 98px;
                    height: 36px;
                    border: 1px solid #DCDCDC;
                    padding: 0;
                    font-size: 13px;
                    color: #000;
                }
            }
        }

        @mixin text-ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .upload-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #fbf9f9; // 使用与上传按钮相同的背景色
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100;
        border-radius: 8px;
        border: 2px dashed #fbf9f9; // 添加与上传按钮相同的边框

        .upload-progress {
            width: 80%;
            text-align: center;
            padding: 26px; // 添加与上传按钮相同的内边距

            :deep(.el-progress-bar__outer) {
                background-color: #f0f0f0;
                border-radius: 4px;
                height: 20px !important; // 确保进度条高度一致
            }

            :deep(.el-progress-bar__inner) {
                border-radius: 4px;
                background-color: #bfe8bb; // 使用 Element Plus 主题色
            }

            .progress-text {
                margin-top: 12px;
                color: #303133; // 使用与上传按钮文字相同的颜色
                font-size: 14px;
                text-align: center;
            }
        }
    }
}

// 自定义空间不足对话框样式
:deep(.alert-dialog) {

    // 启用标题区域并设置样式
    :deep(.el-dialog__header) {
        display: block;
        padding: 15px 20px;
        position: relative;
        height: 30px; // 设置固定高度
    }

    // 修改图标定位，将其移到标题区域
    .alert-icon {
        position: absolute !important;
        top: 15px;
        left: 20px;
        z-index: 10;
    }

    // 通过CSS调整内容区域的布局
    .alert-content {
        padding-left: 0; // 移除内容区域左边距
        margin-top: -30px; // 向上移动以隐藏图标原位置

        .alert-message {
            margin-left: 45px; // 为标题区域图标留出空间

            .alert-title {
                font-weight: bold;
                color: #303133;
                font-size: 16px;
                margin-bottom: 10px;
                padding-top: 5px; // 调整垂直位置
            }
        }
    }

    .dialog-footer {
        justify-content: flex-end;

        .dialog-buttons {
            gap: 5px; // 减小按钮间距

            .custom-confirm-btn {
                background-color: #18ad25 !important; // 使用指定的绿色
                border-color: #18ad25 !important;

                &:hover,
                &:focus {
                    background-color: #1cc42f !important;
                    border-color: #1cc42f !important;
                }

                &:active {
                    background-color: #159b21 !important;
                    border-color: #159b21 !important;
                }
            }
        }
    }
}
</style>