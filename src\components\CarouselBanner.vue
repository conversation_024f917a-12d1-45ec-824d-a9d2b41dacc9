<template>
	<div class="carousel-banner" >
		<el-carousel ref="carouselRef" :height="bannerHeight" :interval="3000" arrow="never" indicator-position="none"
			:autoplay="true">
			<el-carousel-item v-for="(item, index) in items" :key="index" @click="handleItemClick(item)">
				<img :src="item.imageUrl" class="carousel-image"   :alt="item.alt || 'banner'" />
			</el-carousel-item>
		</el-carousel>
		<div class="banner-button" @click="handleButtonClick">
			<img :src="buttonImg" alt="button" class="button-image" />
		</div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useUmeng } from '@/utils/umeng/hook'; // 导入友盟埋点

const props = defineProps({
	items: {
		type: Array,
		required: true
	},
	buttonImg: {
		type: String,
		required: true
	},
	visible: {
		type: Boolean,
		default: true
	},
	trackingCategory: {
		type: String,
		default: '头部Banner'
	}
});

const emit = defineEmits(['item-click', 'button-click']);

// 创建轮播组件引用
const carouselRef = ref(null);

// 初始化埋点
const umeng = useUmeng();

// 当前窗口状态
const windowState = ref({
	width: typeof window !== 'undefined' ? window.innerWidth : 1024,
	pixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio : 1
});

// 保存窗口调整大小的事件监听器引用
let resizeListener = null;

// 计算banner高度，根据屏幕尺寸动态调整
const bannerHeight = computed(() => {
	// 基于设备类型优化高度
	if (windowState.value.width <= 768) {
		return '36px'; // 移动设备
	} else if (windowState.value.width <= 1280) {
		return '40px'; // 小型笔记本
	} else if (windowState.value.width <= 1440) {
		return '45px'; // 标准笔记本
	} else if (windowState.value.width <= 1680) {
		return '50px'; // 大型笔记本/小型桌面
	} else {
		return '60px'; // 大型桌面显示器
	}
});

// 处理轮播项点击
const handleItemClick = (item) => {
	// 添加埋点统计
	const index = props.items.indexOf(item);
	umeng.trackEvent(
		props.trackingCategory,
		'点击轮播图',
		`轮播图内容: ${index >= 0 ? index : '未知'}`,
		''
	);

	emit('item-click', item);

	if (item && item.link) {
		window.open(item.link, '_blank');
	}
};

// 处理banner按钮点击
const handleButtonClick = () => {
	// 获取当前活动的轮播图索引
	const activeIndex = carouselRef.value ? carouselRef.value.activeIndex : 0;

	// 获取当前轮播图项目
	const currentItem = props.items[activeIndex];

	// 添加埋点统计
	umeng.trackEvent(
		props.trackingCategory,
		'点击按钮',
		`轮播图索引: ${activeIndex}`,
		''
	);

	emit('button-click', { item: currentItem, index: activeIndex });

	// 如果没有链接，则切换到下一张轮播图
	if (carouselRef.value) {
		carouselRef.value.next();
	}
};

onMounted(() => {
	// 窗口调整大小时更新状态
	resizeListener = () => {
		windowState.value = {
			width: window.innerWidth,
			pixelRatio: window.devicePixelRatio
		};
	};

	window.addEventListener('resize', resizeListener);

	// 初始设置
	resizeListener();
});

onBeforeUnmount(() => {
	// 清理事件监听器
	if (resizeListener) {
		window.removeEventListener('resize', resizeListener);
	}
});

// 暴露轮播组件引用
defineExpose({
	carouselRef
});
</script>

<style lang="scss" scoped>
.carousel-banner {
	position: fixed;
	width: 100%;
	overflow: hidden;
	z-index: 300; // 提高z-index确保显示在其他所有元素之上
	left: 0;
	// box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
	
	// 默认蓝色背景作为图片加载失败的备选
	// background: linear-gradient(90deg, #1890ff, #1677ff); 
}

.carousel-image {
	max-width: 100%;
	max-height: 100%;
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	cursor: pointer;
	
	&:hover {
		opacity: 0.9;
	}
}

.banner-button {
	position: absolute;
	right: 20px;
	top: 50%;
	transform: translateY(-50%);
	cursor: pointer;
	z-index: 310; // 提高z-index确保显示在轮播图之上
	width: 30px;
	height: 30px;
	// background-color: rgba(255, 255, 255, 0.2); // 添加半透明背景增加可见度
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	transition: all 0.3s;
	
	&:hover {
		transform: translateY(-50%) scale(1.1);
		background-color: rgba(255, 255, 255, 0.4);
	}
	
	.button-image {
		width: 28px;
		height: 28px;
	}
	
	@media (max-width: 1280px) {
		right: 15px;
		width: 24px;
		height: 24px;
		
		.button-image {
			width: 22px;
			height: 22px;
		}
	}
	
	@media (max-width: 768px) {
		right: 10px;
		width: 20px;
		height: 20px;
		
		.button-image {
			width: 18px;
			height: 18px;
		}
	}
}


</style>