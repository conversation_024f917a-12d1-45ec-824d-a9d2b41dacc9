<template>
    <div class="image-container">
        <div class="input-wrapper">
            <el-input
                v-model="inputValue"
                :placeholder="placeholder"
                :class="{ 'has-content': inputValue }"
                @keyup.enter="handleSubmit"
            />
            <div
                v-if="inputValue"
                @click="handleClear"
                class="action-buttons"
            >
                清空
            </div>
        </div>
    </div>
</template>
  


<script setup>
import { ref, computed } from "vue";
import { ElInput } from "element-plus";

const props = defineProps({
    modelValue: {
        type: String,
        default: "",
    },
    placeholder: {
        type: String,
        default: "请输入视频链接",
    },
    showClear: {
        type: Boolean,
        default: true,
    },
});

const emit = defineEmits(["update:modelValue", "submit", "clear"]);

// 使用计算属性实现双向绑定
const inputValue = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
});

const handleClear = () => {
    inputValue.value = "";
    emit("clear");
};
console.log(inputValue.value)

const handleSubmit = () => {
    emit("submit", inputValue.value);
};
</script>
  
  <style lang="scss" scoped>
.image-container {
    position: relative;
    background: url("your-image.jpg") center/cover no-repeat;
    border-radius: 8px;
    overflow: hidden;

    .input-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        // padding: 12px;

        :deep(.el-input) {
            .el-input__wrapper {
                background: rgba(255, 255, 255, 0.9);
                border-radius: 4px;
                // padding-top: 24px; // 为placeholder留出顶部空间
            }
        }

        // 输入时有内容时的样式
        .has-content :deep(.el-input__inner::placeholder) {
            opacity: 0;
        }
    }

    .action-buttons {
        position: absolute;
        bottom: 0;
        right: 15px;
        display: flex;
        gap: 10px;
        color: #18ad25;
        cursor: pointer;
    }

    // 清空按钮动画
    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s;
    }
    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }
}
</style>