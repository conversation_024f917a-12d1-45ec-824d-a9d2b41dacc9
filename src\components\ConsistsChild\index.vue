<template>
    <!-- 结果展示主容器 -->
    <div class="result">
        <div class="result_count">
            <div class="result_head">
                <span>*</span>
                <span>生成结果：</span>
            </div>
            <div class="checkmark">
                <div class="checkmark-bold"></div>
                <div class="text">
                    <span>{{ videoInfo.name }}</span>
                    <!-- <span class="text_two" v-if="videoInfo.name1">{{
                        videoInfo.name1
                    }}</span> -->
                </div>
            </div>
        </div>
        <div class="TextOnOffToggle">
            <span>平台不做存储,请下载完成后在离开界面</span>
        </div>

        <div class="video-container">
            <div class="video_count">
                <!-- 左侧的视频 -->
                <div class="video_one">
                    <!-- 头部区域 -->
                    <div class="video-header">
                        <div class="left-section">
                            <!-- <el-icon class="video-icon"
                                ><VideoCamera
                            /></el-icon> -->
                            <img class="video-icon" src="../../assets//img/1.png" alt="" />
                            <div class="video-details">
                                <div class="title">{{ videoInfo.title }}</div>
                                <!-- <div class="meta">
                                    <span class="duration">{{ videoDuration }}</span>
                                    <span>|</span>
                                    <span class="size">{{ videoSize }}</span>
                                </div> -->
                            </div>
                        </div>
                        <div class="right-actions">
                            <el-button v-if="videoInfo.type != '去水印'" @click="SaveClick" class="btn"
                                type="text">保存到我的空间</el-button>
                            <el-button @click="handleCopyTitle" class="btn copy" type="text">复制标题</el-button>
                            <!-- <el-button @click="handlePreview('video', videoInfo.videoUrl)" class="btn preview"
                                type="text">预览</el-button> -->
                            <el-button @click="handleDownload('video', videoInfo)" type="primary"
                                class="download-btn">下载</el-button>
                        </div>
                    </div>
                    <!-- 视频区域 -->
                     <!-- {{ videoInfo.videoUrl }} -->
                    <video class="video-content" controls @error="handleVideoError"
                        @loadedmetadata="handleVideoLoad" v-if="videoInfo.videoUrl">
                        <source :src="videoInfo.videoUrl" type="video/mp4" />
                        您的浏览器不支持视频播放，或视频地址无效
                    </video>
                    <div v-else class="video-placeholder">
                        等待视频加载中...
                    </div>
                </div>
                <!-- 一件解析的右侧的封面 -->
                <div class="video_two" v-if="
                    videoInfo.type != '字幕' && videoInfo.type != '去水印'
                ">
                    <!-- 修改后的头部 -->
                    <div class="video-header">
                        <div class="left-section">
                            <el-icon class="video-icon">
                                <Picture />
                            </el-icon>
                            <div class="cover-text">视频封面</div>
                        </div>
                        <div class="right-actions">
                            <el-button @click="
                                handlePreview('image', videoInfo.coverUrl)
                                " class="btn preview" type="text">预览</el-button>
                            <el-button @click="handleDownload('image', videoInfo)" type="primary" size="small"
                                class="download-btn">下载</el-button>
                        </div>
                    </div>

                    <!-- 图片展示区域 -->
                    <img class="img-content" :src="videoInfo.coverUrl" alt="封面预览" />
                </div>
                <!-- 只能加字幕的右侧的封面 -->
                <div class="video_two" v-if="
                    videoInfo.type == '字幕' && videoInfo.type != '去水印'
                ">
                    <div class="video-header">
                        <div class="left-section">
                            <el-icon class="video-icon">
                                <VideoCamera />
                            </el-icon>
                            <div class="video-details">
                                <div class="title">视频字幕</div>
                                <div class="meta">
                                    <span class="duration">已选266/</span>
                                    <span class="size">共10225字</span>
                                </div>
                            </div>
                        </div>
                        <div class="right-actions">
                            <el-button @click="handleCopyTitle" class="btn preview" type="text">复制</el-button>
                            <el-button @click="handleDownload('字幕', videoInfo)" type="primary" size="small"
                                class="download-btn">下载</el-button>
                        </div>
                    </div>
                    <div class="container">
                        <!-- 可滚动内容区域 -->
                        <div class="scroll-content">
                            <div class="text-content">
                                <p>
                                    {{ extractedText }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用新的视频预览组件 -->
        <VideoPreview
            v-model="showPreview"
            :preview-url="previewUrl"
            :preview-type="previewType"
        />

        <!-- dialog弹窗 -->
        <SubtitleDialog 
            v-model="modelValue" 
            :material-info="materialInfo" 
            @save-success="handleSaveSuccess"
            @save-error="handleSaveError"
            @confirm="handleSave" 
        />
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import SubtitleDialog from "../SubtitleDialog/index.vue";
import VideoPreview from "../VideoPreview/index.vue";
import { usePreviewStore } from '@/stores/previewStore'; // 导入预览store
import { saveFullMaterial } from "@/api/myMaterial";

// 初始化预览store
const previewStore = usePreviewStore();

//保存到我的空间dialog
const modelValue = ref(false);
const defaultSelected = ref(["默认收藏夹"]);

// 处理保存成功事件
const handleSaveSuccess = (result, selectedAlbum) => {
    console.log("保存成功:", result);
    console.log("选择的专辑:", selectedAlbum);
    ElMessage.success(`已成功保存到"${selectedAlbum.tagName}"`);
};

// 处理保存失败事件
const handleSaveError = (error) => {
    console.error("保存失败:", error);
    ElMessage.error("保存失败，请重试");
};

// 接收选中结果
const handleSave = async (params) => {
    const required = ['userId', 'materialName', 'materialType', 'storagePath', 'thumbnailPath', 'tagId', 'duration', 'fileSize', 'fileExtension'];
    for (const key of required) {
        if (!params[key]) {
            ElMessage.error(`${key}为必填项`);
            return;
        }
    }
    try {
        await saveFullMaterial(params);
        ElMessage.success("保存成功");
    } catch (error) {
        ElMessage.error("保存失败");
        console.error("保存失败:", error);
    }
};

const extractedText = ref(
    Array(50).fill("文字内容文字内容文字内容文字内容") // 初始示例数据
);

// 保存到我的空间点击事件
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

const parseDurationToSeconds = (durationStr) => {
    if (!durationStr) return 0;
    // 支持格式: "mm:ss" 或 "hh:mm:ss"
    const parts = durationStr.split(':').map(Number);
    if (parts.length === 2) {
        return parts[0] * 60 + parts[1];
    } else if (parts.length === 3) {
        return parts[0] * 3600 + parts[1] * 60 + parts[2];
    }
    return 0;
};

async function getFileSize(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        const size = response.headers.get('content-length');
        return size ? parseInt(size, 10) : '';
    } catch (e) {
        return '';
    }
}

const SaveClick = async () => {
    const hasVideoInfoInProps = props.videoInfo && props.videoInfo.videoUrl;
    const parsedVideoInfo = previewStore.getParsedVideoInfo();
    const hasVideoInfoInPinia = parsedVideoInfo && (parsedVideoInfo.video || parsedVideoInfo.videoUrl);
    if (!hasVideoInfoInProps && !hasVideoInfoInPinia) {
        ElMessage.warning("视频信息不完整，无法保存");
        return;
    }
    const info = hasVideoInfoInProps ? props.videoInfo : parsedVideoInfo;
    const durationStr = info.duration || videoDuration.value;
    const storagePath = info.videoUrl || info.video || '';
    let fileSize = '';
    if (storagePath) {
        fileSize = await getFileSize(storagePath);
    }
    materialInfo.value = {
        userId: getUserId(),
        materialName: info.title || info.name || '',
        materialType: 'video',
        storagePath,
        thumbnailPath: info.coverUrl || info.thumbnail || '',
        duration: parseDurationToSeconds(durationStr),
        fileSize,
        fileExtension: 'mp4',
        // textContent不传
    };
    modelValue.value = true;
};

const props = defineProps({
    videoInfo: {
        type: Object,
        required: true,
        default: () => ({
            name: "已成功提取已下视频内容",
            type: "解析视频",
            title: "",
            duration: "00:00",
            size: "0MB",
            videoUrl: "",
            coverUrl: "",
        }),
    },
});

const emit = defineEmits(["save", "download", "preview", "handleCopy"]);

// 预览相关状态
const showPreview = ref(false);
const previewType = ref("video"); // video/cover
const previewUrl = ref("");
const previewVideo = ref(null);

// 添加新的响应式变量来存储视频信息
const videoDuration = ref('00:00');
const videoSize = ref('0MB');

// 格式化时间的辅助函数
const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化文件大小的辅助函数
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`;
};

// 处理视频加载完成事件
const handleVideoLoad = (event) => {
    try {
        // 获取视频时长
        const duration = event.target.duration;
        videoDuration.value = formatDuration(duration);

        // 由于跨域限制，不能直接获取视频文件大小
        // 这里可以选择不显示文件大小，或者从后端接口获取
        videoSize.value = '未知大小';

        // 更新视频信息
        props.videoInfo.duration = videoDuration.value;
        props.videoInfo.size = videoSize.value;
    } catch (error) {
        console.error('获取视频信息失败:', error);
        videoDuration.value = '00:00';
        videoSize.value = '未知大小';
    }
};

// 处理复制标题
const handleCopyTitle = async () => {
    emit("handleCopy", props.videoInfo);
};

// 预览处理
const handlePreview = (type, url) => {
    if (!url) {
        ElMessage.error('视频地址无效，无法预览');
        return;
    }
    previewType.value = type;
    previewUrl.value = url;
    showPreview.value = true;
};

// 处理预览视频加载完成
const handlePreviewVideoLoad = () => {
    if (previewVideo.value) {
        try {
            previewVideo.value.requestFullscreen();
        } catch (e) {
            console.error("全屏失败:", e);
            ElMessage.warning('全屏模式启动失败，但您仍可以观看视频');
        }
    }
};

// 处理预览视频加载错误
const handlePreviewVideoError = (e) => {
    console.error('预览视频加载错误:', e);
    ElMessage.error('视频加载失败，请检查视频链接是否有效');
    showPreview.value = false;
};

// 处理下载
const handleDownload = async (type, videoInfo) => {
    try {
        // 完整记录视频信息，帮助调试
        console.log('下载文件完整信息:', {
            type,
            title: videoInfo.title,
            name: videoInfo.name,
            videoUrl: videoInfo.videoUrl,
            coverUrl: videoInfo.coverUrl,
            fullVideoInfo: videoInfo
        });
        
        const url = type === "video" ? videoInfo.videoUrl : videoInfo.coverUrl;
        
        // 优先使用title，如果没有则尝试使用name，最后才用默认文件名
        const fileName = videoInfo.title || (videoInfo.name && videoInfo.name !== "已成功提取已下视频内容" ? videoInfo.name : "未命名视频");
        
        // 检查文件名是否是UUID格式
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(fileName);
        const finalFileName = isUUID ? "视频文件" : fileName;
        
        console.log('准备设置的文件名:', finalFileName);
        
        // 方法1: 使用Blob和强制MIME类型
        try {
            // 获取文件内容
            const response = await fetch(url);
            const fileData = await response.blob();
            
            // 获取文件的MIME类型
            const mimeType = type === "video" ? "video/mp4" : (type === "字幕" ? "text/plain" : "image/jpeg");
            
            // 创建新的Blob，强制指定MIME类型
            const fileExtension = type === "video" ? "mp4" : (type === "字幕" ? "txt" : "jpg");
            const blob = new Blob([fileData], { type: mimeType });
            
            // 使用window.saveAs(如果有)或创建URL和锚点元素
            console.log('window.saveAs:', window.saveAs);
            if (window.saveAs) {
                window.saveAs(blob, `${finalFileName}.${fileExtension}`);
            } else {
                // 创建对象URL
                const blobUrl = URL.createObjectURL(blob);
                
                // 创建一个a标签并设置属性
                const a = document.createElement('a');
                a.href = blobUrl;
                a.download = `${finalFileName}.${fileExtension}`;
                
                // 确保download属性生效
                a.setAttribute('download', `${finalFileName}.${fileExtension}`);
                
                // 记录生成的链接和下载属性
                console.log('生成的下载链接:', {
                    href: a.href,
                    download: a.download
                });
                
                // 添加到body、触发点击并移除
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                // 释放URL对象
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                }, 100);
            }
            
            ElMessage.success("开始下载");
        } catch (error) {
            console.error("Blob方法下载失败，尝试替代方法:", error);
            
            // 替代方法: 如果Blob方法失败，尝试直接使用URL
            const a = document.createElement("a");
            a.href = url;
            a.download = `${finalFileName}.${type === "video" ? "mp4" : (type === "字幕" ? "txt" : "jpg")}`;
            a.target = "_blank"; // 确保在新标签页打开
            
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            ElMessage.success("开始下载(备用方法)");
        }
    } catch (error) {
        console.error("下载失败:", error);
        ElMessage.error("下载失败，请重试");
    }
};

// 关闭预览
const closePreview = () => {
    if (previewVideo.value) {
        previewVideo.value.pause();  // 停止视频播放
    }
    showPreview.value = false;
    if (document.fullscreenElement) {
        document.exitFullscreen();
    }
};

const handleVideoError = (e) => {
    console.error('视频加载错误:', e);
    console.log('视频URL:', props.videoInfo.videoUrl);
    ElMessage.error('视频加载失败，请检查视频链接是否有效');
};

const materialInfo = ref({});

onMounted(() => {
    console.log('组件挂载，当前视频URL:', props.videoInfo.videoUrl);
    if (props.videoInfo.videoUrl) {
        // 可以尝试预加载或验证URL
        const testVideo = new Image();
        testVideo.src = props.videoInfo.videoUrl;
        testVideo.onerror = () => console.error('视频URL无法访问');
    }
});
</script>

<style lang="scss" scoped>
.result {
    margin-top: 60px;
    @media screen and (max-width: 1500px) {
        margin-top: 7px
    }
    @media screen and (max-width: 1480px) {
        margin-top: 7px
    }
    @media screen and (max-width: 1366px) {
        margin-top: 7px
    }
    @media screen and (max-width: 1024px) {
        margin-top: 7px
    }

    .result_count {
        display: flex;
        align-items: center;

        .checkmark {
            width: 475px;
            height: 35px;
            // margin-left: 20px;
            border-radius: 5px;
            // background-color: #eafbf3;
            display: flex;
            align-items: center;

            .checkmark-bold {
                background: #4caf50;
                width: 23px;
                height: 23px;
                border-radius: 50%;
                position: relative;
                left: 1px;
            }

            .checkmark-bold::after {
                content: "";
                position: absolute;
                left: 18%;
                /* 调整定位补偿线宽变化 */
                top: 25%;
                width: 14px;
                height: 9px;
                border: 4px solid #fff;
                /* 线宽从4px增加到6px */
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                box-sizing: border-box;
                /* 保持尺寸稳定 */
            }

            .text {
                margin: 2px 0 0 5px;
                font-size: 14px;
                font-weight: bold;
                color: #4caf50;

                .text_two {
                    font-size: 12px;
                    padding-left: 8px;
                    // font-weight: 300;
                }
            }
        }
    }

    .TextOnOffToggle {
        color: #18ad25;
        margin: 1px 0 0 104px;
        font-size: 12px;
    }

    .video-container {
        margin: 20px 0 0 80px;
        // width: 70%;
        background: #fff;

        .video_count {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .video_one {
                width: 50%;
                display: flex;
                flex-direction: column;

                .video-header {
                    background: #f8f7fc;
                    padding: 15px 12px;
                    border-radius: 5px;

                    .left-section {
                        display: flex;
                        align-items: center;
                        gap: 8px;

                        .video-icon {
                            font-size: 24px;
                            color: #409eff;
                            width: 32px;
                            height: 32px;
                        }

                        .video-details {
                            .title {
                                font-size: 14px;
                                line-height: 1.5;
                                font-weight: 500;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: 200px;
                                margin-bottom: 4px;
                            }

                            .meta {
                                font-size: 12px;
                                color: #909399;
                                margin-top: 4px;

                                span+span {
                                    margin-left: 8px;
                                }
                            }
                        }
                    }

                    .right-actions {
                        display: flex;
                        align-items: center;
                        justify-content: end;
                        // gap: 6px;
                        margin-top: 9px;

                        .btn {
                            width: 121px;
                            height: 37px;
                            padding: 6px;
                            border: 1px solid #ececef;
                            color: #606266;

                            &:hover {
                                background: #f0f2f5;
                            }
                        }

                        .el-button--primary {
                            background: #18ad25;
                            border: 1px solid #18ad25;
                            color: #fff;
                            border-radius: 4px;
                            padding: 6px 12px;
                        }

                        .preview,
                        .copy {
                            width: 89px;
                            height: 37px;
                        }

                        .download-btn {
                            width: 89px;
                            height: 37px;
                            color: #18ad25;
                            background: #d9efda;
                            border-color: #d9efda;
                            border-radius: 4px;
                            padding: 6px 12px;
                        }
                    }
                }

                .video-content {
                    width: 100%;
                    height: 235px;
                    margin-top: 10px;
                    object-fit: cover;
                    border-radius: 0 0 4px 4px;
                }
            }

            .video_two {
                width: 50%;
                margin-left: 40px;
                display: flex;
                flex-direction: column;

                .video-header {
                    background: #f8f7fc;
                    padding: 17px 12px;
                    border-radius: 5px;

                    .left-section {
                        display: flex;
                        align-items: center;
                        gap: 8px;

                        .video-icon {
                            font-size: 24px;
                            color: #409eff;
                            width: 32px;
                            height: 32px;
                        }

                        .cover-text {
                            font-size: 14px;
                            color: #606266;
                            font-weight: 500;
                        }
                    }

                    .right-actions {
                        display: flex;
                        align-items: center;
                        justify-content: end;
                        margin-top: 9px;

                        .btn {
                            padding: 6px;
                            color: #606266;
                            border: 1px solid #ececef;
                            color: #606266;

                            &:hover {
                                background: #f0f2f5;
                            }
                        }

                        .preview {
                            width: 89px;
                            height: 37px;
                        }

                        .download-btn {
                            width: 89px;
                            height: 37px;
                            font-size: 14px;
                            color: #18ad25;
                            background: #d9efda;
                            border-color: #d9efda;
                            border-radius: 4px;
                            padding: 6px 12px;
                        }
                    }
                }

                .container {
                    // margin-left: 40px;
                    margin-top: 10px;
                    height: 235px;
                    display: flex;
                    flex-direction: column;
                    background: #fff;
                    border: 1px solid #f5f2f2;

                    .scroll-content {
                        flex: 1;
                        overflow: auto;
                        padding: 20px;

                        .text-content {
                            p {
                                line-height: 1.8;
                                color: #666;
                                margin: 0 0 1em;
                            }
                        }
                    }

                    .fixed-footer {
                        background: #fff;
                        padding: 16px 0;
                        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
                        position: sticky;
                        bottom: 0;

                        .footer-wrapper {
                            max-width: 1200px;
                            // margin: 0 auto;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 0 20px;
                        }

                        .word-count {
                            color: #666;
                            font-size: 14px;
                            padding-right: 10px;

                            .number {
                                color: #19ad24;
                            }
                        }

                        .action-buttons {
                            display: flex;
                            // gap: 12px;

                            .btn {
                                width: 121px;
                                height: 37px;
                                border-color: #19ad24;

                                &:hover {
                                    color: #000;
                                    background-color: #f0faf1;
                                    border-color: #19ad24;
                                    transform: translateY(-2px);
                                }
                            }

                            .copy,
                            .download {
                                width: 80px;
                                // height: 40px;
                            }

                            .voice {
                                background: #19ad24;
                                border-color: #19ad24;
                                color: white;

                                &:hover {
                                    transform: translateY(-2px);
                                }
                            }
                        }
                    }

                    ::-webkit-scrollbar {
                        width: 6px;
                        background: #f5f5f5;
                    }

                    ::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;
                    }
                }

                .img-content {
                    width: 100%;
                    height: 235px;
                    margin-top: 10px;
                    object-fit: cover;
                    border-radius: 0 0 4px 4px;
                }
            }
        }
    }

    .preview-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.9);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .preview-content {
        position: relative;
        max-width: 90%;
        max-height: 90%;

        .preview-media {
            width: 100%;
            height: 100%;
            object-fit: contain;

            &::-webkit-media-controls-enclosure {
                background: rgba(0, 0, 0, 0.5);
            }
        }
    }

    .close-btn {
        position: absolute;
        top: -20px;
        right: -20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        :deep(svg) {
            width: 1.2em;
            height: 1.2em;
        }
    }
}

.video-placeholder {
    width: 100%;
    height: 235px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #666;
    border-radius: 0 0 4px 4px;
}

// 针对1500大小视频的适配
.result.video-1500 {
    margin-top: 7px;
}
</style>