<template>
    <!-- 底部更多推荐 -->
    <div class="bottom" v-if="!isHidden">
        <div class="recommend-container" v-show="showRecommend && (showCloudButton || (videoUrl && (showWatermarkButton || showExtractionButton)))">
            <span class="recommend-title">更多推荐：</span>
            <div class="button-group">
                <el-button class="recommend-btn" 
                    @click="navigateTo('watermark')" 
                    v-if="showWatermarkButton">
                    <span class="btn-content"
                        >视频去水印<span class="arrow"
                            ><el-icon><ArrowRight /></el-icon></span
                    ></span>
                </el-button>
                <el-button class="recommend-btn" 
                    @click="navigateTo('Extraction')" 
                    v-if="showExtractionButton">
                    <span class="btn-content"
                        >提取视频文案<span class="arrow"
                            ><el-icon><ArrowRight /></el-icon></span
                    ></span>
                </el-button>
                <el-button class="recommend-btn" v-show="showCloudButton">
                    <span class="btn-content"
                        >去专业云剪辑<span class="arrow"
                            ><el-icon><ArrowRight /></el-icon></span
                    ></span>
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ArrowRight } from '@element-plus/icons-vue';
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

// 获取当前路由和路由器实例
const router = useRouter();
const route = useRoute();

// 定义props，接收是否隐藏的标志
const props = defineProps({
    isHidden: {
        type: Boolean,
        default: false
    },
    showCloudButton: {
        type: Boolean,
        default: false  // 默认隐藏"去专业云剪辑"按钮
    },
    showRecommend: {
        type: Boolean,
        default: false
    },
    showWatermarkButton: {
        type: Boolean,
        default: true  // 默认显示"视频去水印"按钮
    },
    showExtractionButton: {
        type: Boolean,
        default: false  // 默认隐藏"提取视频文案"按钮
    },
    videoUrl: {
        type: String,
        default: ''  // 视频URL参数
    },
    videoTitle: {
        type: String,
        default: ''  // 视频标题参数
    }
});

// 导航方法
const navigateTo = (routeName) => {
    if (props.videoUrl) {
        const query = { url: props.videoUrl };
        if (props.videoTitle) query.title = props.videoTitle;
        router.push({ 
            name: routeName,
            query
        });
    } else {
        router.push({ name: routeName });
    }
};
</script>

<style lang="scss" scoped>
.bottom {
    position: fixed;
    bottom: 0;
    width: 87%;
    // left: 200px;
    .recommend-container {
        height: 70px;
        display: flex;
        align-items: center;
        background: #fdfdfd;
        border-radius: 6px;

        .recommend-title {
            padding-left: 10px;
            font-size: 14px;
            color: #606266;
            white-space: nowrap;
        }

        .button-group {
            margin-left: 18px;
            display: flex;
            gap: 8px;

            .recommend-btn {
                padding: 8px 16px;
                border-radius: 4px;
                border: 1px solid #dcdcdc;
                background: white;
                transition: all 0.2s;

                &:hover {
                    border: 1px solid #dcdcdc;
                    box-shadow: 0 2px 8px rgba(32, 160, 255, 0.1);
                }

                .btn-content {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    color: #303133;

                    .arrow {
                        margin-left: 8px;
                        font-weight: 600;
                    }
                }
            }
        }
    }
}
</style>