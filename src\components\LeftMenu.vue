<template>
  <div class="left-menu">
    <div 
      v-for="(item, index) in menuItems" 
      :key="index"
      class="menu-item" 
      :class="{ active: activeMenuItem === index }" 
      @click="handleMenuClick(index)"
    >
      <img :src="item.icon" :alt="item.name" class="menu-text">
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 导入图标
import wenanIcon from '@/assets/img/wenanIcon.png'
import peiyinIcon from '@/assets/img/peiyinIcon.png'
import yinleIcon from '@/assets/img/yinleIcon.png'
import shipinIcon from '@/assets/img/shipinIcon.png'

export default {
  name: 'LeftMenu',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const activeMenuItem = ref(0)

    // 菜单项数据
    const menuItems = [
      { name: '文案创作', icon: wenanIcon, path: '/ContentCreation' },
      { name: '配音', icon: peiyinIcon, path: '/VoiceOver' },
      { name: '音乐音效', icon: yinleIcon, path: '/MusicAudio' },
      { name: '视频剪片', icon: shipinIcon, path: '/VideoEditing' }
    ]

    // 根据当前路由设置活动菜单项
    const setActiveMenuItem = () => {
      const currentPath = route.path
      const index = menuItems.findIndex(item => item.path === currentPath)
      if (index !== -1) {
        activeMenuItem.value = index
      }
    }

    // 初始化时设置活动菜单项
    setActiveMenuItem()

    // 处理菜单点击事件
    const handleMenuClick = (index) => {
      activeMenuItem.value = index
      router.push(menuItems[index].path)
    }

    return {
      activeMenuItem,
      menuItems,
      handleMenuClick
    }
  }
}
</script>

<style lang="scss" scoped>
.left-menu {
  width: 109px;
  background: #fff;
  border-radius: 8px 0 0 8px;
  height: auto;
  border-right: 1px solid rgba(0, 0, 0, 0.2);
  padding: 20px 0;

  .menu-item {
    width: 100%;
    height: 83px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    background: #fff;
    margin: 10px 0;

    .menu-text {
      height: 81px;
      width: 81px;
      object-fit: none;
      transform: scale(1);
      max-width: none;
      max-height: none;
      position: relative;
      z-index: 2;
    }

    // 修改悬停效果，所有菜单项都使用相同的样式
    &:hover {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 14px;
        width: 81px;
        height: 83px;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        z-index: 1;
        transition: all 0.3s ease;
      }
    }

    // 修改选中效果，所有菜单项都使用相同的样式
    &.active {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 14px;
        width: 81px;
        height: 83px;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        z-index: 1;
      }
    }
  }
}
</style> 