<template>
    <div class="material-dialog">
        <el-dialog v-model="dialogVisible" :title="title" width="1400px" :show-close="true"
            :close-on-click-modal="false" :append-to-body="false">
            <template #title>
                <div class="dialog-title">
                    <img :src="titleIcon" :alt="title" class="title-icon">
                    <span>{{ title }}</span>
                </div>
            </template>

            <!-- 添加滚动容器 -->
            <div class="material-grid-container">
                <div class="material-grid">
                    <!-- 上传视频区域 -->
                    <div class="material-item upload-item" @click="triggerUpload">
                        <div class="empty-container">
                            <div class="upload-area">
                                <div class="plus-icon">+</div>
                            </div>
                            <div class="upload-text">
                                <img src="@/assets/img/movie.png" alt="movie" class="movie-icon">
                                添加视频，开始你的大作吧！
                            </div>
                        </div>
                        <input type="file" ref="fileInput" accept="video/*" style="display: none"
                            @change="handleFileUpload" />
                    </div>

                    <!-- 视频素材项 -->
                    <div v-for="(item, index) in localMaterialList" :key="item.id || index" class="material-item"
                        :class="{ 'selected': selectedMaterials.includes(item.id || index), 'local-upload': item.isLocal }"
                        @click="selectMaterial(item, index)">
                        <div class="material-preview">
                            <!-- 添加错误处理和默认背景色 -->
                            <img v-if="item.thumbnailUrl" 
                                :src="item.thumbnailUrl" 
                                alt="" 
                                class="preview-image"
                                @error="handleThumbnailError($event, item)">

                            <div v-else-if="item.url && !item.uploading" class="video-thumbnail">
                                <!-- 使用视频截帧作为缩略图 -->
                                <video 
                                    :src="item.url" 
                                    class="thumbnail-video" 
                                    preload="metadata" 
                                    @loadeddata="captureVideoThumbnail($event, item)"></video>
                            </div>
                            <div v-else class="no-thumbnail">
                                <i class="video-icon"></i>
                            </div>
                            
                            <!-- 上传进度显示 -->
                            <div v-if="item.uploading" class="upload-progress-overlay">
                                <div class="upload-progress-container">
                                    <div class="upload-progress-bar" :style="{ width: item.uploadProgress + '%' }"></div>
                                </div>
                                <div class="upload-progress-text">上传中 {{ item.uploadProgress }}%</div>
                            </div>
                            
                            <!-- 视频播放器 - 播放状态时显示 -->
                            <video 
                                v-if="item.isPlaying && item.url && !item.uploading" 
                                :src="item.url" 
                                controls 
                                autoplay
                                class="video-player"
                                @ended="onVideoEnded(index)"
                            ></video>
                            
                            <!-- 错误状态显示 -->
                            <div v-if="item.error" class="upload-error">
                                <i class="error-icon">!</i>
                                <span>上传失败</span>
                            </div>
                            
                            <div class="delete-btn" @click.stop="handleDelete(item, index)">
                                <img src="@/assets/img/Frame.png" alt="delete" class="delete-icon">
                            </div>
                            <div class="duration">{{ item.duration || '00:00' }}</div>
                            <div class="play-btn" 
                                 :class="{ 'is-playing': item.isPlaying }"
                                 v-if="!item.uploading && !item.error"
                                 @click.stop="togglePlay(index)">
                                <i class="play-icon"></i>
                            </div>
                        </div>
                        <div class="material-name">{{ item.name }}</div>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
                    <el-button class="confirm-btn" type="primary" @click="handleConfirm">
                        确 定
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    materialList: {
        type: Array,
        default: () => []
    },
    titleIcon: {
        type: String,
        required: true
    },
    multiple: {  // 是否允许多选
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:visible', 'close', 'confirm', 'remove', 'togglePlay', 'upload', 'generateThumbnail'])

const dialogVisible = ref(props.visible)
const title = computed(() => '视频素材')
const fileInput = ref(null)
const selectedMaterials = ref([])
const fileUrl = ref(null)

// 添加本地视频列表状态
const localMaterialList = ref([])

// 监听props.materialList变化，同步到本地列表
watch(() => props.materialList, (newList) => {
    // 深拷贝以避免直接修改props
    localMaterialList.value = JSON.parse(JSON.stringify(newList || []))
}, { immediate: true, deep: true })

// 监听 visible 属性变化
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})

// 监听 dialogVisible 变化
watch(() => dialogVisible.value, (newVal) => {
    if (newVal) {
        selectedMaterials.value = []
    }
    emit('update:visible', newVal)
})

// 修改处理关闭方法 - 添加关闭对话框的功能
const handleClose = () => {
    dialogVisible.value = false; // 直接关闭对话框
    emit('close');
}

// 修改处理确认方法 - 不再进行路由跳转
const handleConfirm = () => {
    const selectedItems = localMaterialList.value.filter(item =>
        selectedMaterials.value.includes(item.id || localMaterialList.value.indexOf(item))
    );
    dialogVisible.value = false; // 直接关闭对话框
    emit('confirm', selectedItems);
}

// 添加视频播放结束处理函数
const onVideoEnded = (index) => {
    // 如果存在该索引的材料
    if (index >= 0 && index < localMaterialList.value.length) {
        // 将播放状态设置为false
        localMaterialList.value[index].isPlaying = false
    }
}

// 修改togglePlay函数实现真正的播放功能
const togglePlay = (index) => {
    // 如果存在该索引的材料
    if (index >= 0 && index < localMaterialList.value.length) {
        // 切换播放状态
        const isCurrentlyPlaying = localMaterialList.value[index].isPlaying

        // 先停止所有正在播放的视频
        localMaterialList.value.forEach((item, i) => {
            if (i !== index && item.isPlaying) {
                item.isPlaying = false
            }
        })

        // 设置当前视频的播放状态
        localMaterialList.value[index].isPlaying = !isCurrentlyPlaying

        // 通知父组件
        emit('togglePlay', index)
    }
}

// 触发文件上传
const triggerUpload = () => {
    fileInput.value.click()
}

// 修改文件上传函数，实现OSS上传并显示进度
const handleFileUpload = (event) => {
    const files = event.target.files
    if (files && files.length > 0) {
        const file = files[0]
        // 检查文件类型
        if (!file.type.startsWith('video/')) {
            ElMessage.error('请上传视频文件')
            return
        }
        // 检查文件大小，限制为100MB
        if (file.size > 100 * 1024 * 1024) {
            ElMessage.error('文件大小不能超过100MB')
            return
        }
        
        // 创建本地预览URL
        const localUrl = URL.createObjectURL(file)
        
        // 生成唯一ID
        const newId = 'local_' + Date.now()
        
        // 添加到本地视频列表，标记为上传中状态
        const newVideoItem = {
            id: newId,
            name: file.name,
            url: localUrl, // 本地预览URL
            thumbnailUrl: '', // 暂无缩略图
            duration: '处理中',
            isLocal: true,
            uploading: true, // 标记为上传中
            uploadProgress: 0, // 上传进度
            file: file
        }
        
        localMaterialList.value.unshift(newVideoItem)
        
        // 这里不再自动选中新上传的视频
        // selectedMaterials.value = [newId]  <-- 注释或删除这行
        
        // 创建FormData对象用于OSS上传
        const formData = new FormData()
        formData.append('file', file)
        
        // 通知父组件处理上传，并传递完整的回调对象
        emit('upload', formData, {
            file,
            localUrl,
            onProgress: (progress) => {
                // 查找并更新上传项的进度
                const uploadItem = localMaterialList.value.find(item => item.id === newId)
                if (uploadItem) {
                    uploadItem.uploadProgress = progress
                    console.log(`视频 ${file.name} 上传进度: ${progress}%`)
                }
            },
            onSuccess: (response) => {
                // 上传成功，更新视频信息
                const uploadItem = localMaterialList.value.find(item => item.id === newId)
                if (uploadItem) {
                    uploadItem.uploading = false
                    uploadItem.uploadProgress = 100
                    
                    // 更新OSS URL和其他返回的信息
                    if (response) {
                        uploadItem.url = response.url || uploadItem.url
                        uploadItem.thumbnailUrl = response.thumbnailUrl || ''
                        uploadItem.duration = response.duration || '00:00'
                        uploadItem.id = response.id || uploadItem.id
                    }
                    
                    console.log(`视频 ${file.name} 上传完成`)
                    ElMessage.success('视频上传成功，请手动选择该视频')
                }
            },
            onError: (error) => {
                // 上传失败，更新状态并显示错误
                const uploadItem = localMaterialList.value.find(item => item.id === newId)
                if (uploadItem) {
                    uploadItem.uploading = false
                    uploadItem.error = true
                }
                
                console.error(`视频 ${file.name} 上传失败:`, error)
                ElMessage.error('视频上传失败: ' + (error?.message || '未知错误'))
            }
        })
        
        // 清空文件输入框，以便可以再次选择相同的文件
        event.target.value = ''
    }
}

// 添加尝试生成缩略图的函数
const tryGenerateThumbnail = (videoItem) => {
    // 如果服务器需要时间处理缩略图，可以添加轮询逻辑
    if (!videoItem || !videoItem.id) return
    
    // 模拟添加默认缩略图以立即显示某些内容
    videoItem.thumbnailUrl = videoItem.url // 先暂时使用视频URL代替缩略图
    
    // 通知服务器为视频生成缩略图
    emit('generateThumbnail', videoItem.id, (thumbnailUrl) => {
        // 当缩略图生成完成后的回调
        if (thumbnailUrl) {
            // 查找并更新视频项的缩略图
            const updatedItem = localMaterialList.value.find(item => item.id === videoItem.id)
            if (updatedItem) {
                updatedItem.thumbnailUrl = thumbnailUrl
            }
        }
    })
}

// 选择素材
const selectMaterial = (item, index) => {
    // 确保item有id属性，如果没有使用索引作为id
    const itemId = item.id || index;

    if (props.multiple) {
        // 多选模式
        const index = selectedMaterials.value.indexOf(itemId)
        if (index === -1) {
            selectedMaterials.value.push(itemId)
        } else {
            selectedMaterials.value.splice(index, 1)
        }
    } else {
        // 单选模式
        selectedMaterials.value = [itemId]
    }
    console.log('Selected materials:', selectedMaterials.value); // 添加调试日志
}

// 修改删除方法
const handleDelete = (item, index) => {
    console.log('MaterialDialog: 删除按钮点击', item, index);

    // 从选中列表中移除
    const itemId = item.id || index;
    const selectedIndex = selectedMaterials.value.indexOf(itemId);
    if (selectedIndex !== -1) {
        selectedMaterials.value.splice(selectedIndex, 1);
    }

    // 通知父组件移除视频，并提供需要取消全选的标志
    console.log('MaterialDialog: 发出remove事件', item);
    emit('remove', item, true); // 添加第二个参数，表示需要更新全选状态
}

// 添加处理缩略图加载错误的方法
const handleThumbnailError = (event, item) => {
    console.warn('缩略图加载失败:', item.name)
    // 标记缩略图加载失败，切换到视频缩略图模式
    item.thumbnailUrl = '' // 清空失败的URL
    
    // 如果有视频URL，将尝试从视频中获取缩略图
    if (item.url) {
        console.log('将使用视频URL作为备选:', item.url)
    }
}

// 从视频中捕获第一帧作为缩略图
const captureVideoThumbnail = (event, item) => {
    try {
        const video = event.target
        // 确保视频已加载元数据
        if (video.readyState >= 2) {
            // 设置视频时间到0.1秒处（避免黑屏）
            video.currentTime = 0.1
            
            // 监听视频寻找完成事件
            video.onseeked = () => {
                try {
                    // 创建一个canvas
                    const canvas = document.createElement('canvas')
                    canvas.width = video.videoWidth
                    canvas.height = video.videoHeight
                    
                    // 在canvas上绘制视频当前帧
                    const ctx = canvas.getContext('2d')
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
                    
                    // 转换为图片URL
                    const thumbnailUrl = canvas.toDataURL('image/jpeg')
                    
                    // 更新视频项的缩略图
                    item.thumbnailUrl = thumbnailUrl
                    console.log('成功从视频生成缩略图:', item.name)
                    
                    // 移除事件监听器
                    video.onseeked = null
                } catch (err) {
                    console.error('生成缩略图出错:', err)
                }
            }
        }
    } catch (err) {
        console.error('处理视频缩略图时出错:', err)
    }
}

// 组件销毁时清理URL
onBeforeUnmount(() => {
    // 如果有创建的URL，需要释放
    if (fileUrl.value) {
        URL.revokeObjectURL(fileUrl.value)
    }
})
</script>

<style lang="scss" scoped>
.material-dialog {
    :deep(.el-dialog) {
        border-radius: 8px;
        border: 1px solid #EBEEF5;
        padding: 0;

        .el-dialog__header {
            margin: 0;
            height: 56px;
            border-bottom: 1px solid #EBEEF5;
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 16px 20px;
            position: relative;

            .dialog-title {
                display: flex;
                align-items: center;
                gap: 8px;

                .title-icon {
                    width: 24px;
                    height: 24px;
                    margin-right: 8px;
                }

                span {
                    font-size: 16px;
                    font-weight: 500;
                    color: #303133;
                    line-height: 56px;
                }
            }

            .el-dialog__close {
                position: absolute;
                top: 16px;
                right: 20px;
                font-size: 20px;
                color: #909399;
                cursor: pointer;
                transition: color 0.2s;

                &:hover {
                    color: #409EFF;
                }
            }
        }

        .el-dialog__body {
            padding: 0;

            // 添加滚动容器样式
            .material-grid-container {
                max-height: 70vh; // 设置最大高度，避免对话框过高
                overflow-y: auto; // 允许垂直滚动
                overflow-x: hidden; // 禁止水平滚动
                padding: 20px;

                // 隐藏滚动条 - Webkit浏览器 (Chrome, Safari)
                &::-webkit-scrollbar {
                    width: 0;
                    background: transparent;
                }

                // 隐藏滚动条 - Firefox
                scrollbar-width: none;

                // 隐藏滚动条 - IE/Edge
                -ms-overflow-style: none;
            }

            .material-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(308px, 1fr));
                gap: 20px;

                .material-item {
                    cursor: pointer;
                    position: relative;

                    &.selected {
                        .material-preview {
                            border: 4px solid #0AAF60 !important;
                            box-shadow: 0 0 12px rgba(10, 175, 96, 0.5);
                            transition: all 0.2s ease;
                            outline: none;

                            &:before {
                                background-color: rgba(10, 175, 96, 0.1);
                                opacity: 1;
                            }
                        }

                        .material-name {
                            color: #0AAF60;
                            font-weight: 500;
                        }
                    }

                    .material-preview {
                        width: 308px;
                        height: 167px;
                        background: #606266;
                        border-radius: 4px;
                        position: relative;
                        overflow: hidden;
                        margin: 0 auto 8px;
                        border: 4px solid transparent;
                        transition: all 0.2s ease;
                        box-sizing: border-box;

                        &:before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background-color: rgba(10, 175, 96, 0.05);
                            opacity: 0;
                            transition: opacity 0.2s ease;
                            z-index: 1;
                            pointer-events: none;
                        }

                        .preview-image {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }

                        // 删除按钮 - 简化版
                        .delete-btn {
                            position: absolute;
                            top: 8px;
                            right: 8px;
                            width: 24px;
                            height: 24px;
                            // background: rgba(0, 0, 0, 0.5);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            z-index: 10;

                            &:hover {
                                background: rgba(0, 0, 0, 0.7);
                            }

                            .delete-icon {
                                width: 24px;
                                height: 24px;
                                object-fit: contain;
                            }
                        }

                        .duration {
                            position: absolute;
                            bottom: 8px;
                            left: 8px; // 改为左下角
                            // background: rgba(0, 0, 0, 0.5);
                            color: #fff;
                            padding: 2px 6px;
                            border-radius: 2px;
                            font-size: 12px;
                        }

                        .play-btn {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: rgba(0, 0, 0, 0.5);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;

                            .play-icon {
                                width: 0;
                                height: 0;
                                border-style: solid;
                                border-width: 8px 0 8px 12px;
                                border-color: transparent transparent transparent #fff;
                                margin-left: 2px;
                            }

                            &.is-playing {
                                .play-icon {
                                    width: 16px;
                                    height: 16px;
                                    border: none;
                                    margin: 0;
                                    background: #fff;
                                    border-radius: 2px;
                                }
                            }
                        }
                    }
                }

                // 上传视频样式
                .upload-item {
                    .empty-container {
                        width: 308px;
                        height: 167px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        border: 1px dashed #DCDFE6;
                        border-radius: 4px;
                        background-color: #F9F9F9;
                        margin: 0 auto;
                        padding: 0;

                        .upload-area {
                            width: 100px;
                            height: 52px;
                            background: #ECECEC;
                            border-radius: 4px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-bottom: 12px;
                            cursor: pointer;

                            .plus-icon {
                                width: 18px;
                                height: 18px;
                                font-size: 18px;
                                color: #909399;
                                font-weight: 300;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                line-height: 18px;
                            }

                            &:hover {
                                background: #E0E0E0;
                            }
                        }

                        .upload-text {
                            font-size: 14px;
                            color: #909399;
                            display: flex;
                            align-items: center;
                            gap: 4px;

                            .movie-icon {
                                width: 16px;
                                height: 16px;
                                object-fit: contain;
                            }
                        }
                    }
                }
            }
        }

        .el-dialog__footer {
            margin: 0;
            padding: 20px;
            border-top: 1px solid #EBEEF5;

            .dialog-footer {
                display: flex;
                justify-content: flex-end;
                gap: 12px;

                .cancel-btn {
                    width: 88px;
                    height: 32px;
                    border: 1px solid #DCDFE6;
                    color: #606266;

                    &:hover {
                        border-color: #C0C4CC;
                        color: #303133;
                    }
                }

                .confirm-btn {
                    width: 88px;
                    height: 32px;
                    background: #0AAF60;
                    border: none;

                    &:hover {
                        opacity: 0.9;
                    }
                }
            }
        }
    }

    // 添加视频播放器样式
    .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        background-color: #000;
        z-index: 5;
        border-radius: 4px;
    }

    // 预览图片样式
    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
    }

    .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 20px;
        cursor: pointer;
        z-index: 10;
        background-color: rgba(0, 0, 0, 0.4);
        border-radius: 50%;

        &:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .el-icon-close {
            font-size: 16px;
            line-height: 16px;
            position: relative;
            top: -1px;
        }
    }

    // 本地上传的视频样式
    .material-item.local-upload {
        .material-preview {
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(10, 175, 96, 0.1);
                z-index: 1;
                pointer-events: none;
            }
        }

        .no-thumbnail {
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;

            .video-icon {
                width: 48px;
                height: 48px;
                background-image: url('@/assets/img/movie.png');
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }
        }
    }

    // 添加上传进度样式
    .upload-progress-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        
        .upload-progress-container {
            width: 80%;
            height: 6px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
            
            .upload-progress-bar {
                height: 100%;
                background-color: #0AAF60;
                transition: width 0.3s;
            }
        }
        
        .upload-progress-text {
            color: #fff;
            font-size: 14px;
        }
    }

    // 添加错误状态样式
    .upload-error {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(245, 108, 108, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        color: #fff;
        
        .error-icon {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #fff;
            color: #F56C6C;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    // 添加视频缩略图样式
    .video-thumbnail {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #f5f5f5;
        
        .thumbnail-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.8; // 稍微降低不透明度，看起来更像缩略图
        }
    }
}
</style>