<template>
    <div class="music-dialog">
        <el-dialog
        v-model="dialogVisible"
        title="音乐素材"
        width="800px"
        :show-close="true"
        :close-on-click-modal="false"
        class="music-dialog"
        :append-to-body="false"
    >
        <template #header>
            <div class="dialog-title">
                <img src="@/assets/img/yinsu2.png" alt="音乐" class="title-icon">
                <span>音乐素材</span>
            </div>
        </template>
        <div class="add-music-container">
            <button class="add-music-btn" @click="handleAddMusic">
                <img src="@/assets/img/add-fill.png" alt="添加" class="add-icon">
                添加音乐
            </button>
        </div>
        <div class="music-list">
            <div v-if="materialList && materialList.length > 0">
                <div v-for="(music, index) in materialList" 
                     :key="index"
                     class="music-item"
                >
                    <div class="music-info">
                        <div class="play-btn" @click="togglePlay(index)">
                            <!-- 播放图标 -->
                            <i v-show="!localPlayingStates[index]" class="play-icon"></i>
                            <!-- 暂停图标 -->
                            <img v-show="localPlayingStates[index]" src="@/assets/img/hehe1.png" class="pause-icon" />
                        </div>
                        <span class="music-name">{{ music.name }}</span>
                        <span class="music-duration">{{ music.duration || '00:00' }}</span>
                    </div>
                    <span class="cancel-select" @click="removeMusicItem(index)">取消选择</span>
                </div>
            </div>
            <div v-else class="empty-tip">
                暂无音乐，请添加
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
                <el-button class="confirm-btn" type="primary" @click="handleConfirm">
                    确 定
                </el-button>
            </div>
        </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, defineProps, defineEmits,watch, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useMusicStore } from '@/stores/modules/musicStore'

// 获取音乐存储
const musicStore = useMusicStore()

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    materialList: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:visible', 'close', 'confirm', 'remove', 'togglePlay', 'add-music'])

const dialogVisible = ref(props.visible)

// 存储音频元素的映射表
const audioElements = ref(new Map())

// 本地播放状态管理
const localPlayingStates = ref({})

// 初始化本地播放状态
const initLocalStates = () => {
    if (props.materialList && props.materialList.length > 0) {
        props.materialList.forEach((item, idx) => {
            localPlayingStates.value[idx] = item.isPlaying || false
        })
    }
}

// 监听 visible 属性变化
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
    
    // 当对话框打开时预加载音频和初始化状态
    if (newVal && props.materialList && props.materialList.length > 0) {
        initLocalStates()
        props.materialList.forEach((item, index) => {
            if (item.url && !audioElements.value.has(index)) {
                preloadAudio(index, item)
            } else if (item.url && audioElements.value.has(index)) {
                // 如果音频元素已存在，更新音量
                const audio = audioElements.value.get(index)
                if (audio && item.volume !== undefined) {
                    audio.volume = item.volume / 100
                }
            }
        })
    } else {
        // 当对话框关闭时停止所有播放
        stopAllAudio()
    }
})

// 监听 materialList 变化
watch(() => props.materialList, (newVal) => {
    console.log('MusicDialog received materialList:', newVal)
    
    // 初始化本地状态
    initLocalStates()
    
    // 当列表更新时，预加载所有音频
    if (newVal && newVal.length > 0 && dialogVisible.value) {
        newVal.forEach((item, index) => {
            if (item.url && !audioElements.value.has(index)) {
                preloadAudio(index, item)
            } else if (item.url && audioElements.value.has(index)) {
                // 如果音频元素已存在，更新音量
                const audio = audioElements.value.get(index)
                if (audio && item.volume !== undefined) {
                    audio.volume = item.volume / 100
                }
            }
        })
    }
}, { immediate: true, deep: true })

// 监听 dialogVisible 变化
watch(() => dialogVisible.value, (newVal) => {
    emit('update:visible', newVal)
})

// 监听 musicStore 中的音量变化
watch(() => musicStore.musicList, (newVal) => {
    // 当 musicStore 中的音乐列表变化时，同步音量设置
    if (props.materialList && props.materialList.length > 0 && newVal && newVal.length > 0) {
        props.materialList.forEach((item, index) => {
            // 如果 musicStore 中有对应的音乐项
            if (index < newVal.length) {
                const storeVolume = newVal[index].volume
                
                // 只有当音量不同时才更新
                if (item.volume !== storeVolume) {
                    console.log(`正在同步 musicStore 的音量: 索引 ${index}, 音量 ${storeVolume}`)
                    item.volume = storeVolume
                    
                    // 同步到音频元素
                    const audio = audioElements.value.get(index)
                    if (audio) {
                        audio.volume = storeVolume / 100
                    }
                }
            }
        })
    }
}, { deep: true, immediate: true })

// 预加载音频函数
const preloadAudio = (index, item) => {
    if (!item.url) return
    
    try {
        const audio = new Audio()
        audio.preload = 'metadata'
        audio.src = item.url
        
        // 设置音量
        if (item.volume !== undefined) {
            audio.volume = item.volume / 100
            console.log(`预加载音频设置音量: 索引 ${index}, 音量 ${item.volume}`)
        } else if (musicStore.musicList[index] && musicStore.musicList[index].volume !== undefined) {
            // 如果item没有音量但store有，则使用store的音量
            const storeVolume = musicStore.musicList[index].volume
            audio.volume = storeVolume / 100
            // 同时更新item
            item.volume = storeVolume
            console.log(`预加载音频使用store音量: 索引 ${index}, 音量 ${storeVolume}`)
        } else {
            // 默认音量
            audio.volume = 0.7
            item.volume = 70
            console.log(`预加载音频使用默认音量: 索引 ${index}, 音量 70`)
        }
        
        // 监听播放结束事件
        audio.addEventListener('ended', () => {
            localPlayingStates.value[index] = false
            
            // 同步到props和store
            if (props.materialList[index]) {
                props.materialList[index].isPlaying = false
            }
            
            // 同时通知musicStore更新状态
            musicStore.togglePlay(index)
        })
        
        // 存储音频元素
        audioElements.value.set(index, audio)
    } catch (err) {
        console.error('预加载音频失败:', err)
    }
}

// 停止所有音频播放
const stopAllAudio = () => {
    audioElements.value.forEach((audio, idx) => {
        if (!audio.paused) {
            audio.pause()
        }
        localPlayingStates.value[idx] = false
    })
    
    // 更新状态
    if (props.materialList) {
        props.materialList.forEach((item, idx) => {
            item.isPlaying = false
            localPlayingStates.value[idx] = false
        })
    }
}

onMounted(() => {
    initLocalStates()
    
    // 立即同步musicStore中的音量
    if (props.materialList && props.materialList.length > 0 && musicStore.musicList.length > 0) {
        props.materialList.forEach((item, index) => {
            if (index < musicStore.musicList.length) {
                const storeVolume = musicStore.musicList[index].volume
                if (storeVolume !== undefined) {
                    item.volume = storeVolume
                }
            }
        })
    }
})

// 处理关闭
const handleClose = () => {
    // 停止所有播放
    stopAllAudio()
    emit('close')
}

// 处理确认
const handleConfirm = () => {
    // 停止所有播放
    stopAllAudio()
    emit('confirm')
}

// 删除音乐项
const removeMusicItem = (index) => {
    // 如果正在播放，先停止播放
    if (localPlayingStates.value[index]) {
        const audio = audioElements.value.get(index)
        if (audio) {
            audio.pause()
        }
        
        // 移除音频元素
        audioElements.value.delete(index)
        localPlayingStates.value[index] = false
    }
    
    // 通知父组件删除
    emit('remove', index)
}

// 切换播放状态
const togglePlay = (index) => {
    // 更新本地状态
    localPlayingStates.value[index] = !localPlayingStates.value[index]
    
    // 同步到store和props
    if (props.materialList[index]) {
        props.materialList[index].isPlaying = localPlayingStates.value[index]
    }
    
    // 更新 musicStore 中的状态
    musicStore.togglePlay(index)
    
    // 获取更新后的状态
    const isPlaying = localPlayingStates.value[index]

    // 暂停所有其他正在播放的音频
    audioElements.value.forEach((audio, idx) => {
        if (idx !== index && !audio.paused) {
            audio.pause()
            localPlayingStates.value[idx] = false
            if (props.materialList[idx]) {
                props.materialList[idx].isPlaying = false
            }
        }
    })

    // 获取或创建音频元素
    let audio = audioElements.value.get(index)
    if (!audio && props.materialList[index] && props.materialList[index].url) {
        preloadAudio(index, props.materialList[index])
        audio = audioElements.value.get(index)
    }

    // 播放或暂停音频
    if (audio) {
        if (isPlaying) {
            try {
                // 确保音量是最新的
                if (props.materialList[index] && props.materialList[index].volume !== undefined) {
                    audio.volume = props.materialList[index].volume / 100
                }
                
                const playPromise = audio.play()
                
                if (playPromise !== undefined) {
                    playPromise.catch(err => {
                        console.error('播放失败:', err)
                        ElMessage.error(`播放失败: ${props.materialList[index].name}`)
                        localPlayingStates.value[index] = false
                        
                        // 同步到store和props
                        if (props.materialList[index]) {
                            props.materialList[index].isPlaying = false
                        }
                        
                        // 同步回 musicStore
                        musicStore.togglePlay(index)
                    })
                }
            } catch (err) {
                console.error('播放音频时发生错误:', err)
                ElMessage.error(`无法播放 "${props.materialList[index].name}"`)
                localPlayingStates.value[index] = false
                
                // 同步到store和props
                if (props.materialList[index]) {
                    props.materialList[index].isPlaying = false
                }
                
                // 同步回 musicStore
                musicStore.togglePlay(index)
            }
        } else {
            audio.pause()
        }
    }
    
    // 同时通知父组件更新UI状态
    emit('togglePlay', index)
}

// 在组件卸载时清理音频资源
onUnmounted(() => {
  // 停止所有音频并释放资源
  audioElements.value.forEach((audio) => {
    audio.pause()
    audio.src = ''
  })
  audioElements.value.clear()
})

// 添加 router 实例声明
const router = useRouter()

// 添加方法
const handleAddMusic = () => {
  // 判断当前路由
  const currentRoute = router.currentRoute.value.path
  console.log('当前路由', currentRoute)
  if (currentRoute.includes('/MusicAudio')) {
    // 如果当前在音乐音效页面，直接关闭弹窗
    dialogVisible.value = false
    emit('close')
  } else {
    // 如果在其他页面，跳转到音乐音效页面
    ElMessage.info('正在跳转到音乐音效页面')
    router.push('/MusicAudio')
  }
  
  // 触发父组件中的处理方法
  emit('add-music')
};
</script>

<style lang="scss" scoped>
.music-dialog{
    :deep(.el-dialog) {
        border-radius: 8px;
        border: 1px solid #EBEEF5;
        padding: 0;
        .el-dialog__header {
            margin: 0;
            height: 56px;
            border-bottom: 1px solid #EBEEF5;
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 0 20px;
            position: relative;
            
            .el-dialog__title {
                font-size: 16px;
                font-weight: 500;
                color: #303133;
                line-height: 56px;
            }

            .el-dialog__headerbtn {
                position: static;
                top: auto;
                right: auto;
                height: 56px;
                display: flex;
                align-items: center;
                padding: 0 20px;
                margin-right: -20px;
                
                .el-dialog__close {
                    font-size: 18px;
                    color: #909399;
                    
                    &:hover {
                        color: #303133;
                    }
                }
            }

            .dialog-title {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .title-icon {
                    width: 20px;
                    height: 20px;
                    object-fit: contain;
                }
                
                span {
                    font-size: 16px;
                    font-weight: 500;
                    color: #303133;
                    line-height: 56px;
                }
            }
        }
        
        .el-dialog__body {
            padding: 0 20px;
            .music-list {
                max-height: 400px;
                overflow-y: auto;       
                .music-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 16px 0;
                    position: relative;
                    
                    &:last-child {
                    }
                    
                    .music-info {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        height: 24px;
                        
                        .play-btn {
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            background: transparent;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            position: relative;
                            
                            .play-icon {
                                width: 0;
                                height: 0;
                                border-style: solid;
                                border-width: 6px 0 6px 10px;
                                border-color: transparent transparent transparent #0AAF60;
                                margin-left: 2px;
                                transition: all 0.2s;
                            }
                            
                            .pause-icon {
                                width: 16px;
                                height: 16px;
                                object-fit: contain;
                            }
                            
                            &:hover {
                                opacity: 0.8;
                            }
                        }
                        
                        .music-name {
                            color: #303133;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 24px;
                        }
                        
                        .music-duration {
                            color: #909399;
                            font-size: 14px;
                            line-height: 24px;
                        }
                    }
                    
                    .cancel-select {
                        color: #0AAF60;
                        font-size: 14px;
                        cursor: pointer;
                        line-height: 24px;
                        opacity: 0;
                        transition: opacity 0.3s;
                        
                        &:hover {
                            opacity: 0.8;
                        }
                    }
                    
                    &:hover {
                        .cancel-select {
                            opacity: 1;
                        }
                    }
                }
            }
        }
        
        .el-dialog__footer {
            margin: 0;
            padding: 20px;
            border-top: 1px solid #EBEEF5;
            
            .dialog-footer {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                
                .cancel-btn {
                    width: 88px;
                    height: 32px;
                    border: 1px solid #DCDFE6;
                    color: #606266;
                    
                    &:hover {
                        border-color: #C0C4CC;
                        color: #303133;
                    }
                }
                
                .confirm-btn {
                    width: 88px;
                    height: 32px;
                    background: #0AAF60;
                    border: none;
                    
                    &:hover {
                        opacity: 0.9;
                    }
                }
            }
        }
    }
    
}

.add-music-container {
  padding: 16px 20px;
}

.add-music-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: 180px;
  height: 42px;
  padding: 0 16px;
  border: 1px solid #0AAF60;
  border-radius: 21px;
  background-color: transparent;
  color: #0AAF60;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  
  .add-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }
  
  &:hover {
    background-color: rgba(10, 175, 96, 0.05);
  }
  
  &:active {
    background-color: rgba(10, 175, 96, 0.1);
  }
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #909399;
  font-size: 14px;
  text-align: center;
}
</style> 