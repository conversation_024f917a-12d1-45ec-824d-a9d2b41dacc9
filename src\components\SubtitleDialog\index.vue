<template>
    <div class="dialog">
        <el-dialog
            center
            title="保存到专辑"
            v-model="visible"
            width="500px"
            :show-close="false"
            :z-index="9999"
        >
            <!-- 自定义标题区域 -->
            <template #header>
                <div class="custom-header">
                    <h3 class="title">保存到素材</h3>
                    <span class="close-btn" @click="visible = false"
                        >×</span
                    >
                </div>
            </template>
            <!-- 内容区域 -->
            <div class="material-list" v-loading="loading">
                <div v-if="materials.length === 0 && !loading" class="empty-state">
                    <el-empty description="暂无专辑数据" />
                </div>
                <div v-else>
                    <div v-for="material in materials" :key="material.materialId" class="material-item">
                        <div class="radio-wrapper">
                            <el-radio 
                                v-model="selectedTagId" 
                                :label="material.tagId"
                                class="custom-checkbox"
                            >{{ material.materialName }}</el-radio>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 自定义底部 -->
            <template #footer>
                <div class="custom-footer">
                    <el-button class="cancel-btn" @click="visible = false">
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        class="confirm-btn"
                        @click="handleConfirm"
                        :loading="submitting"
                    >
                        保存
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <!-- 添加空间不足对话框 -->
    <AlertDialog 
        v-model:visible="insufficientSpaceDialogVisible" 
        type="warning" 
        :title="insufficientSpaceTitle"
        :sub-title="insufficientSpaceSubTitle" 
        :message="insufficientSpaceMessage" 
        show-cancel-button
        cancel-button-text="暂不购买" 
        confirm-button-text="立即购买" 
        :show-fee-explanation="false" 
        :custom-confirm-class="true"
        @confirm="handleBuySpace" 
        @cancel="handleCancelBuy" 
    />
</template>
  
<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { getUserAlbum, saveFullMaterial } from "@/api/myMaterial";
import { ElMessage, ElLoading } from "element-plus";
import { usePreviewStore } from '@/stores/previewStore'; // 导入预览store
import { useUmeng } from '@/utils/umeng/hook'; // 导入友盟埋点
import { checkUploadPermission } from '@/api/upload'; // 导入校验空间接口
import AlertDialog from '@/views/components/AlertDialog.vue'; // 导入AlertDialog组件
import { useRoute, useRouter } from 'vue-router';

// 初始化埋点
const umeng = useUmeng();
const route = useRoute();
const router = useRouter();

// 初始化预览store，使用try-catch捕获可能的错误
let previewStore = null;
try {
    previewStore = usePreviewStore();
    console.log('SubtitleDialog: previewStore成功初始化');
} catch (error) {
    console.error('SubtitleDialog: 初始化previewStore失败:', error);
}

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

const props = defineProps({
    // 控制对话框显示
    modelValue: {
        type: Boolean,
        default: false,
    },
    materialInfo: {
        type: Object,
        required: true
    },
    sourcePage: {
        type: String,
        default: ''
    },
});

const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
});

const emit = defineEmits(["update:modelValue", "confirm", "close", "save-success", "save-error"]);

// 素材列表
const materials = ref([]);
// 选中的专辑ID
const selectedTagId = ref('');
// 加载状态
const loading = ref(true);
// 提交状态
const submitting = ref(false);

// 添加空间不足对话框相关变量
const insufficientSpaceDialogVisible = ref(false);
const insufficientSpaceTitle = ref('');
const insufficientSpaceSubTitle = ref('您的个人空间容量已不足');
const insufficientSpaceMessage = ref('如需使用请购买空间额度！');

// 加载专辑列表
const loadMaterials = async () => {
    loading.value = true;
    try {
        const response = await getUserAlbum({ userId: props.materialInfo.userId });
        console.log("专辑列表API响应:", response);
        
        // 处理API返回的数据
        if (Array.isArray(response)) {
            // 如果直接返回素材数组
            materials.value = response.map(item => ({
                ...item,
                materialId: item.tagId,
                materialName: item.tagName,
                materialType: '专辑',
                tagId: item.tagId,
                tagName: item.tagName
            }));
        } else if (response && Array.isArray(response.data)) {
            // 如果返回data字段包含数组
            materials.value = response.data.map(item => ({
                ...item,
                materialId: item.tagId,
                materialName: item.tagName,
                materialType: '专辑',
                tagId: item.tagId,
                tagName: item.tagName
            }));
        } else {
            materials.value = [];
        }
        
        // 如果有专辑数据且未选择，默认选择第一个
        if (materials.value.length > 0 && !selectedTagId.value) {
            selectedTagId.value = materials.value[0].tagId;
        }
        
        console.log("处理后的专辑数据:", materials.value);
    } catch (error) {
        console.error("加载专辑失败:", error);
        ElMessage.error("加载专辑列表失败");
        materials.value = [];
    } finally {
        loading.value = false;
    }
};

// 监听对话框可见性，加载数据
watch(() => visible.value, (newVal) => {
    if (newVal) {
        loadMaterials();
    }
}, { immediate: true });

// 确认操作时调用saveFullMaterial接口
const handleConfirm = async () => {
    if (!selectedTagId.value) {
        ElMessage.warning("请选择一个专辑");
        return;
    }

    try {
        // 显示全局 loading
        const loadingInstance = ElLoading.service({
            lock: true,
            text: '权限检查中...',
            background: 'rgba(0, 0, 0, 0.7)'
        });

        // 空间权限检查 - 获取素材大小
        let materialSizeMB = 0;
        if (props.materialInfo.fileSize) {
            // 如果有文件大小信息，转换为MB并向上取整
            materialSizeMB = Math.ceil(props.materialInfo.fileSize / 1024 / 1024);
        } else {
            // 如果没有大小信息，使用默认值或估算值
            materialSizeMB = 0; // 默认1MB，您可以根据实际情况调整
        }

        const userId = getUserId();
        const spaceCheckResponse = await checkUploadPermission({
            userId: userId,
            feat: "空间",
            need: materialSizeMB
        });

        // 关闭 loading
        loadingInstance.close();

        // 检查返回结果
        if (spaceCheckResponse && spaceCheckResponse.content && spaceCheckResponse.content.result === false) {
            // 显示空间不足对话框
            insufficientSpaceDialogVisible.value = true;
            return;
        }

        // 空间检查通过，继续保存逻辑
        // 组装参数对象
        const params = {
            ...props.materialInfo,
            tagId: selectedTagId.value
        };
        
        // 添加埋点代码
        umeng.trackEvent(
          '素材管理', 
          '保存素材', 
          `${props.sourcePage || '未知来源'}-${params.materialType || '未知类型'}`, 
          ''
        );
        
        emit('confirm', params);
        emit('update:modelValue', false);

    } catch (error) {
        console.error("空间检查失败:", error);
        ElMessage.error("空间检查失败，请稍后重试");
    }
};

// 处理购买空间
const handleBuySpace = () => {
    insufficientSpaceDialogVisible.value = false;
    // 在新标签页打开购买空间页面
    const routeData = router.resolve({ name: 'membership', query: { nav: 'space' } });
    window.open(routeData.href, '_blank');
};

// 处理取消购买
const handleCancelBuy = () => {
    insufficientSpaceDialogVisible.value = false;
};

// 从URL中提取文件扩展名
const getFileExtension = (url) => {
    if (!url) return "";
    const match = url.match(/\.([^.?#]+)(?:[?#]|$)/);
    return match ? match[1].toLowerCase() : "";
};

// 关闭时触发事件
const handleClose = () => {
    emit("close");
    emit("update:modelValue", false);
};
</script>
  
<style lang="scss" scoped>
.dialog {
    :deep(.el-dialog) {
        border-radius: 8px;
        z-index: 9999 !important;
        // 标题区域样式
        .el-dialog__header {
            border-bottom: 1px solid #e4e7ed;
            padding: 0;

            .custom-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 15px 20px;

                .title {
                    font-size: 16px;
                    color: #333;
                    margin: 0;
                    flex: 1;
                    text-align: center;
                    line-height: 1.5;
                }

                .close-btn {
                    font-size: 22px;
                    color: #666;
                    cursor: pointer;
                    line-height: 1;
                    padding: 0 5px;
                    &:hover {
                        color: #ff4d4f;
                    }
                }
            }
        }

        // 底部按钮区域
        .el-dialog__footer {
            padding: 10px 20px;
            
            .custom-footer {
                display: flex;
                justify-content: space-between;
                
                .cancel-btn {
                    width: 48%;
                    color: #333;
                    background: #f5f5f5;
                    border-color: #e4e7ed;
                    &:hover {
                        opacity: 0.8;
                    }
                }
                
                .confirm-btn {
                    width: 48%;
                    background: #18ad25;
                    border-color: #18ad25;
                    color: white;
                    
                    &:hover {
                        opacity: 0.9;
                        background: #18ad25;
                        border-color: #18ad25;
                    }
                }
            }
        }
        
        .el-dialog__body {
            padding: 0;
        }
    }
}

.material-list {
    padding: 15px 20px;
    max-height: 400px;
    overflow-y: auto;
    
    .material-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
            border-bottom: none;
        }
        
        .radio-wrapper {
            display: flex;
            align-items: center;
            min-height: 36px;
        }
    }

    .empty-state {
        padding: 30px 0;
    }

    :deep(.custom-checkbox) {
        margin: 0;
        width: 100%;
        
        &.el-radio.is-checked .el-radio__inner {
            background-color: #18ad25;
            border-color: #18ad25;
        }
        
        .el-radio__label {
            font-size: 14px;
            color: #333;
        }
    }
}

// 自定义空间不足对话框样式
:deep(.alert-dialog) {
    // 启用标题区域并设置样式
    :deep(.el-dialog__header) {
        display: block;
        padding: 15px 20px;
        position: relative;
        height: 30px; // 设置固定高度
    }

    // 修改图标定位，将其移到标题区域
    .alert-icon {
        position: absolute !important;
        top: 15px;
        left: 20px;
        z-index: 10;
    }

    // 通过CSS调整内容区域的布局
    .alert-content {
        padding-left: 0; // 移除内容区域左边距
        margin-top: -30px; // 向上移动以隐藏图标原位置

        .alert-message {
            margin-left: 45px; // 为标题区域图标留出空间

            .alert-title {
                font-weight: bold;
                color: #303133;
                font-size: 16px;
                margin-bottom: 10px;
                padding-top: 5px; // 调整垂直位置
            }
        }
    }

    .dialog-footer {
        justify-content: flex-end;

        .dialog-buttons {
            gap: 5px; // 减小按钮间距

            .custom-confirm-btn {
                background-color: #18ad25 !important; // 使用指定的绿色
                border-color: #18ad25 !important;

                &:hover,
                &:focus {
                    background-color: #1cc42f !important;
                    border-color: #1cc42f !important;
                }

                &:active {
                    background-color: #159b21 !important;
                    border-color: #159b21 !important;
                }
            }
        }
    }
}
</style>