<template>
	<div v-if="visible" class="system-notice-overlay" @click="handleOverlayClick">
		<div class="system-notice-dialog" @click.stop>
			<!-- 关闭按钮 -->
			<div class="close-btn" @click="handleClose">
				<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
					<line x1="18" y1="6" x2="6" y2="18"></line>
					<line x1="6" y1="6" x2="18" y2="18"></line>
				</svg>
			</div>

			<!-- 弹窗内容 -->
			<div class="dialog-content">
				<div class="notice-title">系统公告</div>
				<div class="notice-body">
					<p>【配音帮手】即将于6月1日正式上线推广！一路走来，感谢各位的支持与耐心陪伴，平台的每一步成长都离不开大家的努力。</p>
					<p>自2025年6月1日0时起，AI配音收益将正式开始计算，我们会全力保障大家的版权收益。因技术排期，后台登录系统预计6月6日左右上线。在此期间，若有任何疑问，欢迎随时与我们联系，我们将第一时间为您解答。</p>
					<p>未来，期待与您继续携手，在 AI 配音领域创造更多可能！再次感谢大家的理解与信任！</p>
				</div>
				<div class="notice-footer">
					<button class="confirm-btn" @click="handleClose">我知道了</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// 定义属性
const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	}
})

// 定义事件
const emit = defineEmits(['close'])

// 处理关闭弹窗
const handleClose = () => {
	emit('close')
}

// 处理遮罩层点击（点击遮罩层关闭弹窗）
const handleOverlayClick = () => {
	handleClose()
}
</script>

<style lang="scss" scoped>
.system-notice-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.system-notice-dialog {
	position: relative;
	width: 1004px;
	height: 285px;
	background-image: url('@/assets/img/gonggao1.png');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	border-radius: 12px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
	overflow: hidden;

	/* 添加渐入动画 */
	animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
	from {
		opacity: 0;
		transform: scale(0.9);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

.close-btn {
	position: absolute;
	top: 15px;
	right: 15px;
	width: 16px;
	height: 16px;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	color: #666;
	transition: all 0.2s ease;

	&:hover {
		color: #333;
		transform: scale(1.1);
	}
}

.dialog-content {
	height: 100%;
	display: flex;
	flex-direction: column;
	color: #333;

}

.notice-title {
	font-size: 16px;
	font-weight: bold;
	text-align: left;
	margin-left: 23px;
	margin-top: 30px;
	margin-bottom: 20px;
	color: #000000;
}

.notice-body {
	flex: 1;
	margin-left: 47px;
	margin-right: 46px;
	line-height: 1.6;

	p {
		font-size: 12px;
		color: #606266;
		text-align: left;
	}
}

.notice-footer {
	display: flex;
	justify-content: flex-end;
	margin-right: 26px;
	margin-bottom: 23px;
}

.confirm-btn {
	width: 126px;
	height: 40px;
	background-color: #0AAF60;
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		background-color: #099954;
		transform: translateY(-1px);
	}

	&:active {
		transform: translateY(0);
	}
}

/* 响应式设计 */
@media (max-width: 768px) {
	.system-notice-dialog {
		width: 90vw;
		height: 70vh;
	}

	.dialog-content {
		padding: 40px 20px 20px;
	}

	.notice-title {
		font-size: 24px;
		margin-bottom: 20px;
	}

	.notice-body {
		font-size: 14px;
	}
}
</style>