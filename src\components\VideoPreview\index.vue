<template>
    <div v-if="modelValue" class="preview-overlay" @click.self="handleClose">
        <div class="preview-content">
            <!-- 视频预览 -->
            <video 
                v-if="previewType === 'video'" 
                ref="previewVideo" 
                controls 
                class="preview-media"
                @loadeddata="handleVideoLoad"
                @error="handleVideoError"
            >
                <source :src="secureUrl" type="video/mp4" />
                <!-- <source :src="secureUrl" type="video/webm" />
                您的浏览器不支持视频播放，或视频地址无效 -->
            </video>

            <!-- 图片预览 -->
            <img 
                v-else
                :src="secureUrl" 
                alt="全屏预览" 
                class="preview-media"
                @error="handleImageError" 
            />

            <el-button class="close-btn" type="danger" circle @click="handleClose">
                <el-icon><Close /></el-icon>
            </el-button>
        </div>
    </div>
</template>

<script setup>
import { nextTick, ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true
    },
    previewUrl: {
        type: String,
        required: true
    },
    previewType: {
        type: String,
        default: 'video',
        validator: (value) => ['video', 'image'].includes(value)
    }
})

const emit = defineEmits(['update:modelValue'])

const previewVideo = ref(null)

// 处理视频加载完成
const handleVideoLoad = async () => {
    if (previewVideo.value) {
        try {
            await previewVideo.value.requestFullscreen();
            // 尝试播放视频
            try {
                await previewVideo.value.play();
            } catch (playError) {
                console.error("视频自动播放失败:", playError);
                // 自动播放失败不需要提示用户，让用户手动点击播放即可
            }
        } catch (e) {
            console.error("全屏失败:", e);
            ElMessage.warning('全屏模式启动失败，但您仍可以观看视频');
        }
    }
}

// 处理视频错误
const handleVideoError = (e) => {
    console.error('视频加载错误:', e)
    ElMessage.error('视频加载失败，请检查视频链接是否有效')
    emit('update:modelValue', false)
}

// 处理图片错误
const handleImageError = () => {
    ElMessage.error('图片加载失败，请检查图片链接是否有效')
    emit('update:modelValue', false)
}

// 关闭预览
const handleClose = () => {
    if (previewVideo.value) {
        previewVideo.value.pause() // 停止视频播放
    }
    if (document.fullscreenElement) {
        document.exitFullscreen()
    }
    emit('update:modelValue', false)
}

// 监听预览状态变化
watch(() => props.modelValue, (newVal) => {
    if (newVal && props.previewType === 'video') {
        nextTick(async () => {
            if (previewVideo.value) {
                // 重置视频
                previewVideo.value.load();
                try {
                    await previewVideo.value.requestFullscreen();
                    // 尝试播放视频
                    try {
                        await previewVideo.value.play();
                    } catch (playError) {
                        console.error("视频自动播放失败:", playError);
                        // 自动播放失败不需要提示用户，让用户手动点击播放即可
                    }
                } catch (e) {
                    console.error("全屏失败:", e);
                    ElMessage.warning('全屏模式启动失败，但您仍可以观看视频');
                }
            }
        });
    }
});

// 确保URL使用HTTPS
const secureUrl = computed(() => {
    if (props.previewUrl && props.previewUrl.startsWith('http:')) {
        return props.previewUrl.replace('http:', 'https:');
    }
    return props.previewUrl;
});
</script>

<style lang="scss" scoped>
.preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.preview-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;

    .preview-media {
        max-width: 100%;
        max-height: 90vh;
        object-fit: contain;

        &::-webkit-media-controls-enclosure {
            background: rgba(0, 0, 0, 0.5);
        }
    }
}

.close-btn {
    position: absolute;
    top: -20px;
    right: -20px;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    :deep(svg) {
        width: 1.2em;
        height: 1.2em;
    }
}
</style> 