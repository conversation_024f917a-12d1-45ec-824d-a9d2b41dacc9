export default {
    install: function (app) {
        const globalComponents = import.meta.glob('./**/index.(js|vue)', { eager: true })
        for (const key in globalComponents) {
            if (key === './index.js') return
            const component = globalComponents[key]
            let name = key.replace(/\.\/|\/index.vue/g, '')
            name = name.slice(0, 1).toUpperCase() + name.slice(1)
            
            // 跳过"View"组件，因为它在vue-router中已被注册
            if (name === 'View') {
                console.warn(`跳过全局组件 "${name}" 的注册，因为它与Vue Router的内置组件冲突。`)
                continue
            }
            
            app.component(`${ name }`, component.default || component)
        }
    }
}
