<template>
 <div class="pay_close_dialog" :style="`top:${props.top}px`" v-if="pay_close_show">
    <div class="pay_close_warn">
        <img src="@/assets/images/account/pay_close_warn.svg" alt="">
    </div>
    <div class="pay_close_info">
        <div class="pay_close_info_text">
            <h4>关闭提示</h4>
            <span>付款过程中关闭此页面可能导致付款失败，是否确认关闭？</span>
        </div>
        <div class="pay_close_info_btns">
            <el-button class="pay_close_info_btns_cancel" @click="cancel">取消</el-button>
            <el-button class="pay_close_info_btns_submit"  @click="submit">确定</el-button>
        </div>
    </div>
 </div>
</template>
<script setup>
import { ref,defineProps,defineExpose,defineEmits } from 'vue'
let pay_close_show=ref(false)
let emit=defineEmits(['close'])
let props = defineProps({
    top:{
        type:String,
    }
})
let cancel=()=>{
    pay_close_show.value=false
}
let submit=()=>{
    pay_close_show.value=false
    emit('close')
}
defineExpose({
    pay_close_show
})
</script>
<style lang="scss" scoped>
.pay_close_dialog{
    position: absolute;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    box-sizing: border-box;
    position: absolute;
    width: 720px;
    background: #FAECD8;
    border: 1px solid #FD9D26;
    border-radius: 4px;
    padding:13px;
    padding-right: 17px;
    display: flex;
    align-items: center;
    .pay_close_warn{
        width: 28px;
        height: 28px;
        margin-right: 12px;
    }
    .pay_close_info{
        display: flex;
        flex: 1;
        .pay_close_info_text{
            display: flex;
            flex-direction: column;
            h4{
                font-size: 14px;
                line-height: 22px;
                color: #353D49;
                margin: 0;
                margin-bottom: 4px;
            }
            span{
                font-size: 14px;
                line-height: 22px;
                letter-spacing: -0.01px;
                color: #353D49;
            }
        }
        .pay_close_info_btns{
            margin-left: auto;
            display: flex;
            align-items: center;
            .el-button{
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 5px 20px;
                
                border-radius: 4px;
                font-size: 14px;
                line-height: 22px;
                text-align: center;
                letter-spacing: -0.01px;


                &.pay_close_info_btns_cancel{
                    background: #FD9D26;
                    border: none;
                    color:#fff;
                }
                &.pay_close_info_btns_submit{
                    margin-left: 7px;
                    border: 1px solid #FD9D26;
                    color: #FD9D26;
                    background-color: transparent;

                }   
            }
            

        }

    }
}
</style>