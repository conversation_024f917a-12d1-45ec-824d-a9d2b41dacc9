<template>
    <el-dialog v-model="dialogVisible" class="pay_status_dialog" width="888px" append-to-body>
        <template #header>
            <img src="@/assets/images/account/member_pay_close.png" class="pay_status_dialog_close" @click="close">
        </template>
        <template #default>
            <div class="pay_status_content">
                <img :src="(status === 'success' && statusImg['success']) || statusImg['fail']" />
                <template v-if="status == 'success'">
                    <span>支付成功</span>
                    <p>您已完成支付，请尽快使用！</p>
                </template>
                <template v-else>
                    <span>支付失败</span>
                    <p>支付失败，请稍后再试！</p>
                </template>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, defineExpose, reactive, defineEmits, watch } from 'vue';
import successImg from '@/assets/images/account/pay_success.png';
import failImg from '@/assets/images/account/pay_fail.png';
import { userInfo } from '@/api/account.js';
import { sendData } from '@/api/login.js';
import { useloginStore } from '@/stores/login';
import { useUmeng } from '@/utils/umeng/hook';
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js';

// 定义百度支付成功埋点函数
const trackBaiduPaymentConversion = () => {
    // 基础URL
    let baseUrl = "https://peiyinbangshou.com/H4Home?utm_source=baidu&utm_medium=ocpc_baidu&utm_id=********";
    
    // 从当前浏览器URL中获取bd_vid参数
    const currentUrl = window.location.href;
    const bdVidMatch = currentUrl.match(/[?&]bd_vid=([^&#]*)/);
    const bd_vid = bdVidMatch ? bdVidMatch[1] : '';
    
    // 如果存在bd_vid参数，则添加到logidUrl中
    const logidUrl = bd_vid ? `${baseUrl}&bd_vid=${bd_vid}` : baseUrl;

    const params = {
        conversionTypes: [
            {
                logidUrl: logidUrl,
                newType: 26 // 支付使用26
            }
        ]
    };

    // 调用sendData接口
    sendData(params).catch(err => {
        console.error('百度埋点上报失败:', err);
    });
};

let dialogVisible = ref(false)
let loginStore = useloginStore()
const umeng = useUmeng()
const { fetchUserBenefits } = useUserBenefits()
let status = ref('fail')
let statusImg = reactive({
    success: successImg,
    fail: failImg
})
let emit = defineEmits(['status'])
let close = async () => {
    emit('status', status.value)
    dialogVisible.value = false
    await getUserInfo()

    // 支付成功后刷新用户权益信息
    if (status.value === 'success') {
        await refreshUserBenefits()
    }
}

let getUserInfo = async () => {
    return new Promise(async (resolve, reject) => {
        let user_data = await userInfo({userId:loginStore.userId})
        loginStore.setUserInfo(user_data)
        resolve(true)
    })
}

// 刷新用户权益信息
let refreshUserBenefits = async () => {
    try {
        console.log('🔄 支付成功，开始刷新用户权益信息...')
        await fetchUserBenefits()
        console.log('✅ 用户权益信息刷新完成')
    } catch (error) {
        console.error('❌ 刷新用户权益信息失败:', error)
        // 权益刷新失败不影响正常流程，只记录错误
    }
}
watch(status, (newName, oldName) => {
    if (newName == 'success') {
        // 添加支付成功埋点
        trackBaiduPaymentConversion();
        
        // 添加友盟支付成功埋点
        umeng.trackEvent('支付', '支付成功', '订单支付成功');
        
        setTimeout(() => {
            close()
        }, 2000)
    }
});
defineExpose({
    dialogVisible,
    status
});
</script>

<style lang="scss">
.pay_status_dialog {
    padding: 36px 32px 29px;
    box-sizing: border-box;
    background: linear-gradient(180deg, #DFFFF6 0%, #FFFFFF 30.57%);
    border-radius: 4px;
    position: relative;

    .el-dialog__header {
        padding-bottom: 0;

        .pay_status_dialog_close {
            position: absolute;
            right: 29px;
            top: 13px;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .el-dialog__headerbtn {
            display: none;
        }
    }

    .el-dialog__body {
        padding: 80px 0;

        .pay_status_content {
            display: flex;
            flex-direction: column;
            align-items: center;

            img {
                width: 150px;
                height: 150px;
                margin-bottom: 25px;
            }

            span {
                margin-bottom: 16px;
                font-size: 24px;
                line-height: 22px;
                color: #0AAF60;
            }

            p {
                margin: 0;
                font-size: 18px;
                line-height: 22px;
                color: #D3D3D2;
            }
        }
    }

}
</style>
