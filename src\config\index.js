const ENV_BASE = import.meta.env.VITE_API_BASE_URL;
const ENV_TYPE = import.meta.env.VITE_ENV_TYPE || 'development';

/**
 * 根据环境和当前访问URL确定协议
 * - 正式环境(pro)或访问peiyinbangshou.com域名时使用HTTPS
 * - 测试环境(uat)或其他环境使用HTTP
 */
const getProtocol = () => {
    // 检查当前环境类型或域名
    if (ENV_TYPE === 'pro' || window.location.hostname === 'peiyinbangshou.com') {
        return 'https://';
    } else {
        return 'http://';
    }
};

/**
 * 获取完整的API基础URL
 * - 在peiyinbangshou.com域名下，使用当前域名作为API基础URL
 * - 在其他环境或域名下，使用环境变量中配置的服务器地址
 * - 确保始终返回包含协议的绝对URL，防止被当作相对路径处理
 */
const getApiBase = () => {
    // 空字符串或空白字符 - 使用当前域名
    if (typeof ENV_BASE !== 'string' || ENV_BASE.trim() === '') {
        console.log('API基础URL未设置或为空，使用当前域名');
        return window.location.origin;
    }
    
    // 如果当前域名是peiyinbangshou.com，使用当前域名作为API基础URL
    if (window.location.hostname === 'peiyinbangshou.com') {
        return window.location.origin;
    }
    
    // 如果ENV_BASE已经包含完整协议，则直接返回
    if (ENV_BASE.startsWith('http://') || ENV_BASE.startsWith('https://')) {
        return ENV_BASE;
    }
    
    // 移除开头的双斜杠（如果有）
    let baseUrl = ENV_BASE;
    if (baseUrl.startsWith('//')) {
        baseUrl = baseUrl.substring(2);
    }
    
    // 添加适当的协议前缀，确保始终是绝对URL
    return `${getProtocol()}${baseUrl}`;
};

/**
 * 获取特定服务的API基础URL
 * 某些服务可能使用不同的服务器地址，此方法用于处理这些特殊情况
 * @param {string} serviceName - 服务名称，例如'income'表示收益相关服务
 * @returns {string} - 完整的API基础URL
 */
const getServiceApiBase = (serviceName) => {
    // 如果当前域名是peiyinbangshou.com，直接使用当前域名
    if (window.location.hostname === 'peiyinbangshou.com') {
        return 'https://peiyinbangshou.com';
    }
    
    // 根据不同服务返回特定的API基础URL
    switch(serviceName) {
        case 'income':
            console.log(ENV_TYPE,'ENV_TYPE');
            
            if (ENV_TYPE === 'pro'){
                return  window.location.origin;
            }else{
                return 'http://************:39000'
            }
            // 收益服务使用特定的API地址
           
        default:
            // 默认使用通用的API基础URL
            return getApiBase();
    }
};

export const globalConfig = Object.freeze({
    // 上传接口（冻结对象防止意外修改）
    uploadUrl: `${getApiBase().replace(/\/$/, '')}/material/api/upload/signature`,

    // 类型安全访问
    get apiBase() {
        return getApiBase();
    },
    
    // 获取特定服务的API基础URL
    getServiceApiBase
});