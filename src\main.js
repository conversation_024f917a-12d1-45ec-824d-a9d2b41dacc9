import { createApp,inject,ref } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'; // 引入路由配置文件
import '@/assets/sass/index.scss' // 全局样式
import '@/styles/menu.scss' // 自定义菜单样式
import '@/styles/responsive.css' // 响应式布局CSS（替代scaleHelper.js）
import components from '@/components/global/index' // 全局自定义组件
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import eventBus from '@/common/utils/eventBus'; // 导入事件总线
import pinia from './stores'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs' // 导入Element Plus中文语言包
import { initVersionCheck } from '@/utils/versionCheck' // 导入版本检测工具
import { initPreviewStoreSession } from '@/stores/previewStore' // 导入预览内容会话管理
// import GlobalLoginModal from "@/views/constant/login/login.vue";   //引入登录弹窗
// 导入友盟统计插件
import UmengPlugin from '@/utils/umeng'

// 全局缩放现在由响应式CSS控制，不再需要JS

const app = createApp(App)
// const { proxy } = getCurrentInstance();
// app.component('GlobalLoginModal', GlobalLoginModal); //注册全局登录弹窗

// 初始化版本检测 - 移到挂载后执行
// initVersionCheck();

// 监听导入文案弹窗
// app.config.globalProperties.$letterModal = {
//     openImportLetter:()=>{
//         eventBus.emit('openModal11');
//         console.log('5555555')
//     },
//     closeImportLetter:()=>{
//         // eventBus.emit('closeImportLetter11');
//     }
// }
// app.provide('eventBusOpen', eventBus);


// 监听登录弹窗
app.config.globalProperties.$modal = {
    open: (title) => {
        // const modal = document.querySelector('#global-modal')
        // console.log('lllllllllllllll',modal)
        // if (modal) modal.__vue__.open(title)
        // const modalRef = ref(null);
        // 假设 GlobalLoginModal 组件中有一个 ref="modal"
        // app.config.globalProperties.$refs.modal.open(title);
        eventBus.emit('openModal');
    },
    close: () => {
        // const modal = document.querySelector('#global-modal')
        // if (modal) modal.__vue__.close()
        // const modalRef = ref(null);
        // // 假设 GlobalLoginModal 组件中有一个 ref="modal"
        // if (app.config.globalProperties.$refs.modal) {
        //     app.config.globalProperties.$refs.modal.close();
        // }
        eventBus.emit('closeModal');
    }
}
app.provide('eventBus11', eventBus);


// 监听文案生成弹窗
app.config.globalProperties.$AImodal = {
    open: () => {
        eventBus.emit('openAIModal');
    },
    close: () => {
        eventBus.emit('closeAIModal');
    }
}
app.provide('eventBusAI', eventBus);

// 监听背景音乐弹窗
app.config.globalProperties.$musicmodal = {
    open: () => {
        eventBus.emit('openMusicModal');
    },
    close: () => {
        eventBus.emit('closeMusicModal');
    }
}
app.provide('eventBusMusic', eventBus);


// 监听音效弹窗
// app.config.globalProperties.$effectsmodal = {
//     open: () => {
//         eventBus.emit('eventBusEffects');
//     },
//     close: () => {
//         eventBus.emit('closeEffectsModal');
//     }
// }
// app.provide('eventBusEffects', eventBus);/



// 注册使用element图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

app.use(router)
    .use(components)
    .use(ElementPlus, { // 使用Element Plus并配置中文语言
        locale: zhCn
    })
    .use(pinia)
    .use(UmengPlugin, { // 使用友盟统计插件
        siteId: '1281415790', // 替换为您的站点ID
        debug: process.env.NODE_ENV !== 'production' // 开发环境输出日志
    })
    .mount('#app')

// 在应用挂载后初始化版本检测，避免干扰热更新
setTimeout(() => {
    // 不再需要设置localStorage会话标志，现在使用sessionStorage方案
    
    // initVersionCheck(); // 已禁用版本检查
    initPreviewStoreSession(); // 初始化预览内容会话管理
    // updateScaleFactor(); // 不再需要JS缩放更新
}, 500);

// 导出 app 实例
export default app

