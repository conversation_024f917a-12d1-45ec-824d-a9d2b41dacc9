import { createRouter, createWebHistory } from 'vue-router';
import { staticRouter, errorRouter } from "@/router/modules/staticRouter";
import { useMenuStore } from '@/stores/modules/menu.js'
// 导入友盟埋点相关模块
import { useUmeng } from '@/utils/umeng/hook';
import { createRouterAnalytics } from '@/utils/umeng/modules/router';

// const constant = [
//     // { path: '/', redirect: { name: 'login' }, meta: { name: '重定向' } },
//     // { path: '/login', name: 'login', component: () => import('@/views/constant/login.vue'), meta: { title: '登录' } },
//     // { path: '/401', name: '401', component: () => import('@/views/constant/401.vue'), meta: { title: '401' } },
//     // { path: '/404', name: '404', component: () => import('@/views/constant/404.vue'), meta: { title: '404' } },
//     // { path: '/500', name: '500', component: () => import('@/views/constant/500.vue'), meta: { title: '500' } }
// ]

const router = createRouter({
	history: createWebHistory(), // createWebHashHistory(import.meta.env.BASE_URL || '/'),
	// routes, // 短语法，等同于 `routes: routes`
	routes: [
		{
			path: '/',
			redirect: { name: 'home' }, // 添加根路径重定向到home页面
			meta: { name: '重定向' }
		},
		{
			path: '/login',
			redirect: { name: 'home' }, // 将登录路径重定向到home页面
			meta: { name: '登录重定向到首页' }
		},
		...staticRouter,
		...errorRouter,
		{
			path: '/MusicAudio',
			name: 'MusicAudio',
			meta: { title: '', no_scroll: true },
			component: () => import('@/views/Editor/MusicAudio/index.vue')
		},
		{
			path: '/documentation',
			name: 'documentation',
			meta: { title: 'API开发文档', fixedHeader: true },
			component: () => import('@/views/modules/apiService/documentation.vue')
		},

	],
});

// 注释掉Safari浏览器缩放和全屏监听初始化，避免与统一的事件管理器冲突
// initSafariResizeListener();

//

// router.beforeEach((to, from, next) => {
//    console.log('000000')
//     next()
// })
let app = document.getElementById('app');
let isIOS = () => {
	return /iP(ad|hone|od)/.test(navigator.userAgent);
}

// 全面检测Safari浏览器（所有平台）
function isSafari() {
	const ua = navigator.userAgent;

	// 特性检测 - Safari特有属性
	const hasSafariFeatures =
		typeof document !== 'undefined' &&
		'WebkitAppearance' in document.documentElement.style &&
		/^((?!chrome|android).)*safari/i.test(ua);

	// 检测Safari但排除Chrome等浏览器
	// 注意：Chrome、Edge等浏览器的UA也包含Safari
	const isSafariUA =
		/Safari/.test(ua) &&
		!/Chrome/.test(ua) &&
		!/Chromium/.test(ua) &&
		!/Android/.test(ua) &&
		!/Edge/.test(ua) &&
		!/Firefox/.test(ua);

	// 检测iOS/iPadOS上的Safari
	const isIOSSafari =
		/iPhone|iPad|iPod/i.test(ua) &&
		!/(CriOS|FxiOS|OPiOS|mercury)/i.test(ua);

	// 综合判断
	const result = hasSafariFeatures || isSafariUA || isIOSSafari;
	console.log('Safari检测结果:', result, 'UA:', ua);
	return result;
}

let isMacSafari = () => {
	const ua = navigator.userAgent;
	return /Macintosh/.test(ua) && /Safari/.test(ua) && !/Chrome/.test(ua);
}

// 注释掉重复的全屏监听器，避免与缩放工具中的监听器冲突
// 全屏变化监听现在由各平台的缩放工具统一管理（scaleHelper.js 或 windowsScaleHelper.js）
/*
// Safari浏览器刷新页面函数
function refreshSafariPage() {
	if (isSafari()) {
		console.log('Safari浏览器检测到缩放或全屏变化，刷新页面');
		window.location.reload();
	}
}

// 初始化Safari浏览器缩放和全屏监听
function initSafariResizeListener() {
	// 保存原始窗口尺寸
	let originalWidth = window.innerWidth;
	let originalHeight = window.innerHeight;

	// 监听窗口大小变化
	window.addEventListener('resize', () => {
		// 检测是否Safari浏览器
		if (isSafari()) {
			// 检测窗口大小变化是否足够大（排除微小变化）
			const widthDiff = Math.abs(window.innerWidth - originalWidth);
			const heightDiff = Math.abs(window.innerHeight - originalHeight);

			// 如果变化幅度超过阈值，判定为缩放或全屏操作
			if (widthDiff > 50 || heightDiff > 50) {
				console.log('Safari浏览器检测到窗口大小显著变化');
				refreshSafariPage();
			}

			// 更新原始尺寸
			originalWidth = window.innerWidth;
			originalHeight = window.innerHeight;
		}
	});

	// 监听全屏变化
	document.addEventListener('fullscreenchange', refreshSafariPage);
	document.addEventListener('webkitfullscreenchange', refreshSafariPage);
}
*/
let isAppleDevice = () => {
  const ua = navigator.userAgent;
  return /iP(ad|hone|od)/.test(ua) || /Macintosh/.test(ua);
}
// 针对苹果机设置高度的函数
let setAppHeightForApple=(app, aspectRatio, rate)=>{
    let height;
  console.log(isAppleDevice(),'setAppHeightForApple');
  
    if (isIOS()) {
      // iOS Safari 使用 window.innerHeight 计算高度，避免 100vh 导致溢出
      const vh = window.innerHeight;
      if(vh<=953){
        height = vh * (aspectRatio / rate);
      }else{
        height = vh
      }
     
    } else if (isMacSafari()) {
      // macOS Safari 使用 document.documentElement.clientHeight 计算高度
      const clientH = document.documentElement.clientHeight;
      if(clientH<=953){
        height = clientH * (aspectRatio / rate);
      }else{
        height =clientH
      }
    }else if(isAppleDevice()){
      height= window.innerHeight
    } else {
      // 非苹果机不处理，返回 false
      return false;
    }
  
    app.style.height = `${height}px`;
    console.log('苹果机设置高度:', height, '比例:', aspectRatio / rate);
    return true;
  }

// 初始化友盟埋点
let routerAnalytics = null;

// 路由全局前置守卫
router.beforeEach((to, from, next) => {
    console.log('路由跳转:', to.name)
    const app = document.getElementById('app');
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const aspectRatio = screenHeight / screenWidth;
    let rate=953/1920
    // 更新当前活动菜单
    const menuStore = useMenuStore();
    if (to.meta.no_scroll) {
        if (app) {
            // 只有苹果机调用特殊处理
            const handled = setAppHeightForApple(app, aspectRatio, rate);
            if (!handled) {
              // 非苹果机使用固定高度计算
              app.style.height = 953 * (aspectRatio / rate) + 'px';
              console.log('非苹果机设置高度:',953 * (aspectRatio / rate), aspectRatio / rate, aspectRatio, rate);
            }
          }
        // 根据浏览器类型设置不同的overflow
        if (isSafari()) {
          // Safari浏览器使用scroll
          document.body.style.overflow = 'hidden';
          console.log('Safari浏览器设置overflow: scroll');
        } else {
          // 其他浏览器使用hidden
          document.body.style.overflowY = 'hidden';
          console.log('非Safari浏览器设置overflow: hidden');
        }
      } else {
        if (app) {
            // 恢复默认高度
            app.style.height = '';
          }
        // 恢复默认滚动条
        document.body.style.overflowY = '';
      }
    // 导航栏是/ 时，重定向到home页面 
    if(to.name === '/'){
        menuStore.active = 'home'; // 设置menu的active状态
        next({ name: 'home' })
    } else {
        // 更新活动菜单
        if (to.name) {
            menuStore.active = to.name;
        }
        next();
    }

	// 监听窗口尺寸变化，苹果机动态调整高度
	window.addEventListener('resize', () => {
		if (to.meta.no_scroll && to.path !== '/digital-human-editor' && app) {
			setAppHeightForApple(app, aspectRatio, rate);
		}
	});
})

// 路由全局后置钩子 - 用于记录友盟PV埋点
router.afterEach((to, from) => {
	// 确保不跟踪重定向和错误页面
	if (to.name && !to.name.includes('redirect') && !to.name.includes('error')) {
		try {
			// 延迟一点执行，确保Vue实例已挂载
			setTimeout(() => {
				// 懒加载初始化友盟埋点，因为在路由初始化时可能还没有Vue实例
				if (!routerAnalytics && window.$umeng) {
					const umeng = typeof useUmeng === 'function' ? useUmeng() : window.$umeng;
					routerAnalytics = createRouterAnalytics(umeng);
				}

				// 如果埋点模块已初始化，记录路由变化
				if (routerAnalytics) {
					routerAnalytics.trackRouteChange(to, from);
					console.log('友盟埋点：记录路由变化', to.path);
				}
			}, 0);
		} catch (error) {
			console.error('友盟埋点异常:', error);
		}
	}
});

export default router;