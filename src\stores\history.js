// stores/history.js
import { defineStore } from 'pinia'
import { ref } from 'vue'

/**
 * 历史记录管理存储
 * 
 * 使用组合式API风格定义的Pinia存储。
 * 负责管理和持久化用户的聊天历史记录。
 * 提供添加新记录和加载历史记录的功能。
 * 通过localStorage实现数据的本地持久化存储。
 */
export const useHistoryStore = defineStore('history', () => {
	/**
	 * 历史记录列表
	 * 
	 * 响应式数组，从localStorage加载初始数据。
	 * 如果localStorage中没有数据，则初始化为空数组。
	 * 每条记录包含：content(内容)、time(时间)和isUser(是否用户消息)。
	 */
	const historyList = ref(JSON.parse(localStorage.getItem('chatHistory')) || [])

	/**
	 * 添加历史记录
	 * 
	 * 将新的聊天记录添加到历史列表的开头（最新的在最前面）。
	 * 自动生成当前时间戳，并将更新后的列表保存到localStorage。
	 * 
	 * @param {string} content - 要添加到历史记录的聊天内容
	 */
	const addHistory = (content) => {

		const newItem = {
			content,
			time: new Date().toLocaleTimeString('zh-CN', {
				hour: '2-digit',    // 显示2位数小时
				minute: '2-digit'   // 显示2位数分钟
			}),
			isUser: true          // 标记为用户消息
		}
		console.log(newItem);

		historyList.value.unshift(newItem)  // 在数组开头添加新记录（最新的在最前面）
		localStorage.setItem('chatHistory', JSON.stringify(historyList.value))  // 更新本地存储
	}

	/**
	 * 加载历史记录
	 * 
	 * 预留的方法，用于从外部源或执行其他历史记录加载操作。
	 * 目前此方法为空，可能计划在未来实现。
	 * 可能的用途包括：从服务器加载历史记录、筛选现有记录或重新格式化记录。
	 */
	const loadHistory = () => {
		// 目前为空实现，可能计划在未来添加功能
	}

	/**
	 * 返回存储的公共属性和方法
	 * 
	 * 暴露给组件使用的响应式数据和方法。
	 */
	return {
		historyList,   // 历史记录列表（响应式）
		addHistory,    // 添加历史记录的方法
		loadHistory    // 加载历史记录的方法（预留）
	}
})