import { defineStore } from 'pinia';
export const useloginStore = defineStore('user', {
    state: () => ({
      token: null, // 存储 token
      userInfo:null,//存储用户信息
      userId:null, // 存储 userId
      loginData:null,//存储登录信息
      memberInfo:null//存储用户权益信息
    }),
    actions: {
      saveToStorage() {
        const data = {
          token: this.token,
          userInfo: this.userInfo,
          userId: this.userId,
          loginData: this.loginData,
          memberInfo: this.memberInfo
        };
        localStorage.setItem('user', JSON.stringify(data));
      },
      setToken(newToken) {
        this.token = newToken; // 设置 token
        this.saveToStorage();
      },
      setUserId(userId){
        this.userId = userId; // 设置 userId
        this.saveToStorage();
      },
      setLoginData(data){
        this.loginData = data; // 设置 userId
        this.saveToStorage();
      },
      loginOut() {
        this.token = null; // 清除token
        this.userInfo = null // 清除用户信息
        this.userId = null // 清除用户信息
        this.loginData = null // 清除登录信息
        this.memberInfo =null
        localStorage.removeItem('user');
      },
      setUserInfo(data) {
        this.userInfo = data; // 设置存储用户信息
        this.saveToStorage();
      },
      setMemberInfo(data){
        this.memberInfo = data; // 设置存储用户信息
        this.saveToStorage();
      }
    },
    // 添加持久化配置
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'user',
                storage: localStorage,
                paths: ['token', 'userInfo','userId','loginData','memberInfo']// 只持久化 user 字段
            }
        ]
    }
  });