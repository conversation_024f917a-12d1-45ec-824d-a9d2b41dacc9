import { defineStore } from 'pinia';

/**
 * 消息管理存储
 * 
 * 用于管理应用中所有聊天消息的状态。
 * 负责存储用户和AI助手之间的对话记录，处理消息的添加和状态变更。
 * 提供集中的消息数据源，供UI组件展示使用。
 */
export const useMessageStore = defineStore('message', {
    /**
     * 状态定义
     * 包含所有消息相关的数据
     */
    state: () => ({
        // 消息列表 - 存储所有对话消息的数组
        // 每条消息包含：content(内容), time(时间), isUser(是否用户消息), 
        // isNew(是否新消息), isThinking(是否正在生成中)
        messages: [
            // 初始示例数据...
        ],
        // isGlobalThinking: false // 全局加载状态 - 表示整个应用是否处于等待AI响应状态
    }),
    
    /**
     * 操作方法集合
     * 提供添加和更新消息的功能
     */
    actions: {
        /**
         * 添加用户消息
         * 
         * 将用户发送的消息添加到消息列表中
         * 设置初始状态为"正在思考"，表示等待AI回复
         * 
         * @param {Object} message - 消息对象，包含内容和时间等信息
         * @param {string} message.content - 消息内容
         * @param {string} message.time - 消息时间，格式为本地时间字符串
         */
        addUserMessage(message) {
            console.log(message, 11111);
            this.messages.push({
                ...message,
                isUser: true,     // 标记为用户消息
                isNew: true,      // 标记为新消息，用于UI动画效果
                isThinking: true  // 标记为思考状态，等待回复
            });
        },
        
        /**
         * 添加机器人(AI)消息
         * 
         * 将AI助手的回复添加到消息列表中
         * 处理消息格式并设置适当的状态标记
         * 
         * @param {Object} message - 从API返回的消息对象
         * @param {Object} message.content - 包含AI回复的内容对象
         * @param {string} message.content.result.ai - AI生成的回复文本
         */
        addBotMessage(message) {
            console.log(message,'message');
            
            this.messages.push({
                content: message.content.result.ai,  // 从返回对象中提取AI回复文本
                time: new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',   // 显示2位数小时
                    minute: '2-digit'  // 显示2位数分钟
                }),
                isUser: false,      // 标记为非用户消息(AI消息)
                isNew: true,        // 标记为新消息，用于UI动画效果
                isThinking: false   // AI已回复，不再是思考状态
            });

            // 延时移除"新消息"标记，通常用于消息动画效果结束后
            setTimeout(() => {
                this.messages.forEach((msg) => (msg.isNew = false));
                // 注意：以下两行代码可能是重复的或错误的
                this.messages.isNew = false;
                this.messages.isNew = false;
                this.messages.isThinking = false
            }, 1000);
        },
        
        /**
         * 设置全局加载状态
         * 
         * 控制整个应用是否显示为等待AI响应的状态
         * 目前此功能被注释掉，未启用
         * 
         * @param {boolean} status - 是否处于思考/加载状态
         */
        // setThinkingStatus(status) {
        //     this.isGlobalThinking = status
        // }
    }
});