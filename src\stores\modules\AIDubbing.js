import { defineStore } from 'pinia';
export const useAIDubbingStore = defineStore('AIDubbing', {
    state: () => ({
        bgmusic_url:'',// 保存的背景音乐url
        sound_effects_url_obj:{},  //保存的背景音效obj
        bgmusicObj:{},  //选中的音乐的obj
        textInfo:'',//商配保存文本信息
        extraction:null,//一键
        bgmusic_volume:80//音量
    }),
    actions: {
        setExtraction(data) {
            this.extraction=data
        },
    },
    // 添加持久化配置
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'aibubbing-data',
                storage: localStorage,
                paths: ['extraction',] 
            }
        ]
    }
})