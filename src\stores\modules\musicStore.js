import { defineStore } from 'pinia';

export const useMusicStore = defineStore('musicStore', {
    state: () => ({
        // 音乐列表，所有页面共享这个列表
        musicList: [],
    }),
    actions: {
        /**
         * 添加音乐到列表
         * @param {Object} music - 音乐对象，应该包含name, duration等属性
         * @returns {Boolean} - 是否添加成功
         */
        addMusic(music) {
            // 检查是否已存在相同名称的音乐
            const exists = this.musicList.some(item => item.name === music.name);
            if (exists) {
                return false;
            }
            
            // 添加音乐到列表
            this.musicList.push({
                ...music,
                isPlaying: false,
                id: Date.now() // 生成唯一ID
            });
            return true;
        },
        
        /**
         * 从列表中移除音乐
         * @param {Number} index - 要移除的音乐索引
         */
        removeMusic(index) {
            if (index >= 0 && index < this.musicList.length) {
                this.musicList.splice(index, 1);
                return true;
            }
            return false;
        },
        
        /**
         * 切换音乐的播放状态
         * @param {Number} index - 音乐索引
         */
        togglePlay(index) {
            if (index >= 0 && index < this.musicList.length) {
                // 先停止所有其他音乐
                this.musicList.forEach((music, i) => {
                    if (i !== index && music.isPlaying) {
                        music.isPlaying = false;
                    }
                });
                
                // 切换当前音乐的播放状态
                this.musicList[index].isPlaying = !this.musicList[index].isPlaying;
                return true;
            }
            return false;
        },
        
        /**
         * 清空音乐列表
         */
        clearAll() {
            this.musicList = [];
        },
        
        /**
         * 更新音乐音量
         * @param {Number} index - 音乐索引
         * @param {Number} volume - 新的音量值
         */
        updateVolume(index, volume) {
            if (index >= 0 && index < this.musicList.length) {
                this.musicList[index].volume = volume;
                return true;
            }
            return false;
        },
        
        updateMusicList(list) {
            this.musicList = list;
        },
        
        updateMusicUrl(url) {
            if (this.musicList.length > 0) {
                this.musicList[0].url = url
            } else {
                this.musicList = [{ url }]
            }
        }
    },
    
    // 添加持久化配置
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'music-store-data',
                storage: localStorage,
                paths: ['musicList'] // 只持久化 musicList 字段
            }
        ]
    }
}); 