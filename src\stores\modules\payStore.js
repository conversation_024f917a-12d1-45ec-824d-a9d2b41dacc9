import { defineStore } from 'pinia';

export const usePayStore = defineStore('payStore', {
    state: () => ({
        //限时优惠信息
       limited_time_obj:null
    }),
    actions: {
        setLimitedTime(data) {
           this.limited_time_obj=data
        },        
    },
    // 添加持久化配置
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'pay-store-data',
                storage: localStorage,
                paths: ['limited_time_obj'] 
            }
        ]
    }
}); 