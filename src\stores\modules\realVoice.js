import { defineStore } from 'pinia';

export const useRealStore = defineStore('realVoice', {
    state: () => ({
        // 用他配音传参
        detail:{}
    }),
    actions: {
        setDetailData(data) {
           this.detail=data
        },
        
    },
    // 添加持久化配置
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'real-store-data',
                storage: localStorage,
                paths: ['detail'] 
            }
        ]
    }
}); 