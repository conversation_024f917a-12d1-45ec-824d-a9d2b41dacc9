import { defineStore } from 'pinia';

export const useSoundStore = defineStore('soundStore', {
    state: () => ({
        // 用他配音传参
        chooseData:null,
        packChooseData:[],
        all_package:[],
        open_sound_pack:'',
        buy_sound:{},
        cloneData:null,
        showPackage:null
    }),
    actions: {
        setCloneData(data) {
           this.cloneData=data
        },
        setChooseData(data) {
           this.chooseData=data
        },
        setPackChooseData(data) {
            this.packChooseData=data
        },
        setAllPackage(data) {
            this.all_package=data
        },
        setOpenSoundPack(data) {
            this.open_sound_pack=data
        },
        setbuySound(data){
            this.buy_sound=data
        },
        setShowPackage(data){
            this.showPackage=data
        }
        
    },
    // 添加持久化配置
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'sound-store-data',
                storage: localStorage,
                paths: ['chooseData','packChooseData','all_package','buy_sound','showPackage'] 
            }
        ]
    }
}); 