// stores/page.js
import { defineStore } from 'pinia'

/**
 * 页面状态管理存储
 * 
 * 用于管理应用程序的页面视图状态和导航控制。
 * 负责处理视图之间的切换、导航历史以及各页面组件的显示状态。
 * 作为应用程序的中央导航控制器，协调不同视图之间的交互。
 */
export const usePageStore = defineStore('page', {
    /**
     * 状态定义
     * 包含控制页面显示和导航所需的所有数据
     */
    state: () => ({
        // 当前视图标识 - 控制主要内容区域显示哪个视图
        // 可选值: 'home' (主页), 'chat' (聊天页), 'history' (历史记录页)
        currentView: 'home',
        
        // 消息内容 - 存储当前聊天或编辑的消息文本
        // 在视图切换时保持消息内容，避免用户输入丢失
        messageContent: '',
        
        // 返回按钮显示状态 - 控制导航栏是否显示返回按钮
        // 通常在离开主页进入其他页面时启用
        showBackButton: false,
        
        // 历史记录列表显示状态 - 控制是否在界面上显示历史记录列表
        // 用于在主页上叠加显示历史记录而不完全切换视图
        showHistoryList: false,
    }),
    
    /**
     * 操作方法集合
     * 提供修改状态的各种功能方法
     */
    actions: {
        /**
         * 切换到聊天视图
         * 
         * 将应用导航到聊天界面，并设置相关的状态参数
         * 显示返回按钮，确保历史记录列表关闭
         * 
         * @param {string} content - 要在聊天视图中显示的消息内容
         */
        switchToChat(content) {
            this.currentView = 'chat'
            this.messageContent = content
            this.showBackButton = true
            this.showHistoryList = false // 确保切换到聊天时关闭历史记录
        },
        
        /**
         * 返回主页
         * 
         * 将应用导航回主页视图，重置导航状态
         * 隐藏返回按钮和历史记录列表
         */
        returnToHome() {
            this.currentView = 'home'
            this.showBackButton = false
            this.showHistoryList = false // 确保返回主页时关闭历史记录
        },
        
        /**
         * 显示历史记录
         * 
         * 在主页视图上显示历史记录列表
         * 不完全切换视图，而是在当前视图上叠加显示历史记录
         */
        showHistory() {
            this.currentView = 'home' // 切换到主页
            this.showHistoryList = true // 显示历史记录
        }
    }
})