import { defineStore } from 'pinia';
import { useMusicStore } from './modules/musicStore'; // 导入音乐store

/**
 * 预览内容存储
 * 
 * 用于在页面导航之间持久化预览面板的标题和内容。
 * 该存储是整个应用中管理预览状态的中心位置，它允许不同组件共享预览状态。
 * 通过 localStorage 实现数据持久化，确保页面刷新后预览数据不会丢失。
 */
export const usePreviewStore = defineStore('preview', {
    /**
     * 状态定义
     * 包含预览功能所需的所有数据字段
     */
    state: () => ({
        // 预览标题 - 显示在预览面板顶部
        title: '',
        
        // 预览内容 - 显示在预览面板主体部分的文本或HTML内容
        content: '',
        
        // 上次更新时间 - ISO格式的时间字符串，用于跟踪内容的更新时间
        lastUpdated: null,
        
        // 选中角色 - 当前选中的角色对象，包含角色的详细信息
        // 可能包含如 {name, avatar, description, id} 等属性
        selectedRole: null,
        
        // 视频列表 - 存储当前选中的视频列表
        videoList: [],
        
        // 标记内容是否来自timeline - 用于应用特殊样式
        isTimelineContent: false,
        
        // 新增状态 - 存储提取的视频内容列表
        extractedContentList: [],
        
        // 添加这个新的状态变量
        selectedVideoIds: [],
        
        // 存储当前预览视频的URL
        currentVideoUrl: '',
        
        // 存储视频的关键时间点
        videoTimestamps: [],
        
        // 新增：标记是否已生成快照，避免重复调用API
        snapShotGenerated: false,
        
        // 新增：最后一次生成快照的视频URL
        lastSnapshotUrl: '',
        
        // 新增：最后一次刷新提取内容的标记
        lastRefreshTime: null,
        
        // 新增：配音时长（秒）
        dubbingDuration: 0,
        
        // 新增：存储从submitJob接口返回的projectId
        projectId: null,
        
        // 新增：存储submitGenerateJob接口的完整响应数据
        jobResponse: null,

        // 新增：浏览器会话标识，用于检测浏览器关闭
        browserSessionId: null,

        // 新增：存储解析的视频信息
        parsedVideoInfo: null,
    }),
    
    actions: {
        /**
         * 设置预览标题
         * 
         * 更新预览面板的标题文本，并记录更新时间
         * 
         * @param {string} title - 要设置的标题文本
         */
        setTitle(title) {
            console.log('设置预览标题:', title);
            // 明确处理空值情况，确保标题可以被完全清除
            this.title = title === undefined ? '' : title;
            this.updateTimestamp();
        },
        
        /**
         * 设置预览内容
         * 
         * 更新预览面板的主体内容，并记录更新时间
         * 内容可以是纯文本或HTML格式的字符串
         * 
         * @param {string} content - 要显示的内容文本或HTML
         */
        setContent(content) {
            console.log('设置预览内容:', content);
            this.content = content;
            this.updateTimestamp();
        },
        
        /**
         * 设置内容是否来自timeline
         * 
         * 用于标记内容来源，以便应用特殊样式
         * 
         * @param {boolean} isTimeline - 是否来自timeline
         */
        setIsTimelineContent(value) {
            console.log('设置内容是否来自timeline:', value);
            this.isTimelineContent = value;
        },
        
        /**
         * 清除所有预览数据
         * 
         * 重置标题和内容为空字符串，通常在切换页面或开始新任务时调用
         */
        clearAll() {
            console.log('清除所有预览数据');
            this.title = '';
            this.content = '';
            this.isTimelineContent = false;
            this.updateTimestamp();
        },
        
        /**
         * 更新时间戳
         * 
         * 内部方法，记录最后一次内容更新的时间
         * 用于跟踪内容变更和可能的缓存失效判断
         * 
         * @private
         */
        updateTimestamp() {
            this.lastUpdated = new Date().toISOString();
        },
        
        /**
         * 重置预览状态
         * 
         * 类似于clearAll，但专门用于在特定场景下重置预览状态
         * 与clearAll的区别是这个方法不更新时间戳
         */
        resetPreview() {
            this.title = '';
            this.content = '';
            this.isTimelineContent = false;
            // 重置其他可能需要清空的状态...
        },
        
        /**
         * 设置当前选中的角色
         * 
         * 用于在预览面板中显示与特定角色相关的内容
         * 执行角色相关的合法性检查，确保角色数据完整
         * 
         * @param {Object|null} role - 角色对象或null（取消选择）
         *                            角色对象应至少包含name属性
         */
        setRole(role) {
            if (role === null || role === undefined) {
                // 如果传入 null 或 undefined，明确设置为 null
                this.selectedRole = null;
            } else if (typeof role === 'object') {
                // 如果是对象，检查是否有必要的属性
                if (role.name) {
                    console.log('设置角色:', role);
                    
                    this.selectedRole = role;
                } else {
                    console.warn('尝试设置无效的角色对象:', role);
                    this.selectedRole = null;
                }
            } else {
                console.warn('尝试设置无效的角色类型:', typeof role);
                this.selectedRole = null;
            }
        },
        
        updateSelectedRoleUrl(url) {
            if (this.selectedRole) {
                this.selectedRole.audioUrl = url
            }
        },
        
        /**
         * 设置视频列表
         * 
         * 更新预览面板中显示的视频列表
         * 
         * @param {Array} videos - 视频对象数组
         */
        setVideoList(videos) {
            if (Array.isArray(videos)) {
                console.log('设置视频列表到Pinia store:', videos.length);
                
                // 使用深拷贝确保数据独立，避免引用问题
                try {
                    this.videoList = JSON.parse(JSON.stringify(videos));
                    console.log('成功保存视频列表到store, 当前长度:', this.videoList.length);
                } catch (e) {
                    console.error('保存视频列表时出错:', e);
                    // 如果深拷贝失败，直接赋值
                    this.videoList = [...videos];
                }
                
                this.updateTimestamp();
            } else {
                console.warn('尝试设置无效的视频列表:', videos);
            }
        },
        
        /**
         * 设置提取的内容列表
         * 
         * 存储从视频中提取的文本内容和时间信息
         * 
         * @param {Array} list - 提取的内容数组
         */
        setExtractedContentList(list) {
            console.log('保存提取内容到store, 数据类型:', typeof list, '数组长度:', list?.length || 0);
            
            // 确保收到的是有效数组
            if (Array.isArray(list) && list.length > 0) {
                // 使用JSON深拷贝确保数据独立
                this.extractedContentList = JSON.parse(JSON.stringify(list));
                console.log('成功保存提取内容到store, 当前长度:', this.extractedContentList.length);
                // 只有在有内容时才设置isTimelineContent为true
                this.isTimelineContent = true;
            } else {
                console.warn('尝试保存无效的提取内容:', list);
                // 设置为空数组而不是null/undefined
                this.extractedContentList = [];
                // 当设置空数组时，不自动将isTimelineContent设为true
                // 保持isTimelineContent的当前值不变
            }
            
            this.updateTimestamp();
        },
        
        /**
         * 强制刷新提取内容的视图
         * 
         * 当缩略图更新后，调用此方法可以强制刷新视图
         * 通过创建一个新的提取内容数组，确保Vue能检测到变化
         */
        refreshExtractedContent() {
            if (this.extractedContentList && this.extractedContentList.length > 0) {
                console.log('强制刷新提取内容视图');
                
                try {
                    // 尝试清除图片缓存的方法 - 添加时间戳到现有图片URL
                    this.extractedContentList.forEach(item => {
                        if (item && item.thumbnailUrl) {
                            // 检查是否已经添加了时间戳参数
                            const hasTimestamp = item.thumbnailUrl.includes('?t=');
                            if (hasTimestamp) {
                                // 如果已有时间戳，替换为新的时间戳
                                item.thumbnailUrl = item.thumbnailUrl.replace(/\?t=\d+/, `?t=${Date.now()}`);
                            } else {
                                // 如果没有时间戳，添加一个新的时间戳
                                item.thumbnailUrl = `${item.thumbnailUrl}?t=${Date.now()}`;
                            }
                            console.log('更新带时间戳的缩略图URL:', item.thumbnailUrl);
                        }
                    });
                } catch (e) {
                    console.error('尝试清除缩略图缓存时出错:', e);
                }
                
                // 创建一个全新的数组副本，确保Vue能检测到变化
                const refreshedContent = JSON.parse(JSON.stringify(this.extractedContentList));
                // 重新赋值给提取内容列表，触发响应式更新
                this.extractedContentList = refreshedContent;
                // 添加lastRefreshTime标记，方便调试
                this.lastRefreshTime = new Date().toISOString();
                
                // 尝试在localStorage中也保存一份更新后的数据
                try {
                    localStorage.setItem('manual-extracted-content', JSON.stringify({
                        content: this.extractedContentList,
                        timestamp: this.lastRefreshTime
                    }));
                } catch (e) {
                    console.error('保存提取内容到localStorage失败:', e);
                }
                
                return true;
            }
            return false;
        },
        
        /**
         * 清除提取的内容列表
         */
        clearExtractedContent() {
            console.log('清除store中的提取内容');
            this.extractedContentList = [];
            // 不自动重置isTimelineContent，因为内容可能仍然需要特殊样式
        },
        
        /**
         * 设置当前预览视频的URL
         * 
         * @param {string} url - 视频URL地址
         */
        setCurrentVideoUrl(url) {
            if (!url) {
                console.warn('尝试设置空的视频URL');
                return;
            }
            console.log('setCurrentVideoUrl被调用，设置前:', this.currentVideoUrl);
            console.log('准备设置的新URL:', url);
            this.currentVideoUrl = url;
            console.log('设置后的URL:', this.currentVideoUrl);
            // 手动触发localStorage保存
            try {
                const storeData = {
                    currentVideoUrl: url,
                    lastUpdated: new Date().toISOString()
                };
                console.log('尝试手动存储到localStorage');
                localStorage.setItem('manual-video-url', JSON.stringify(storeData));
            } catch (e) {
                console.error('手动存储到localStorage失败:', e);
            }
            this.updateTimestamp();
        },
        
        /**
         * 设置视频关键时间点
         * 
         * @param {Array} timestamps - 时间点数组，例如 ["00:07", "00:15", "01:30"]
         */
        setVideoTimestamps(timestamps) {
            if (!Array.isArray(timestamps)) {
                console.warn('尝试设置无效的时间点格式:', timestamps);
                this.videoTimestamps = [];
                return;
            }
            
            // 如果传入的是空数组，就使用空数组
            // 如果有真实数据，则使用真实数据
            console.log('设置视频关键时间点:', timestamps.length > 0 ? timestamps : '空数组');
            this.videoTimestamps = timestamps;
            this.updateTimestamp();
        },
        
        /**
         * 获取视频URL和时间点参数，用于API调用
         * 
         * @returns {Object} 包含storypath和times的对象
         */
        getVideoSnapshotParams() {
            console.log('getVideoSnapshotParams被调用，当前URL:', this.currentVideoUrl);
            
            // 检查localStorage中是否有手动存储的URL
            try {
                const manualStore = localStorage.getItem('manual-video-url');
                if (manualStore) {
                    const parsedStore = JSON.parse(manualStore);
                    console.log('从localStorage读取到的手动存储URL:', parsedStore.currentVideoUrl);
                    
                    // 如果Pinia中没有URL但localStorage中有，则使用localStorage中的
                    if (!this.currentVideoUrl && parsedStore.currentVideoUrl) {
                        console.log('使用localStorage中的URL替代Pinia中的空URL');
                        this.currentVideoUrl = parsedStore.currentVideoUrl;
                    }
                }
            } catch (e) {
                console.error('读取localStorage中的URL失败:', e);
            }
            
            // 如果videoTimestamps为空数组或不存在，使用默认时间点
            const defaultTimes = []; // 默认时间点，当没有提取内容时使用
            
            // 检查时间戳数组是否有内容
            if (this.videoTimestamps && this.videoTimestamps.length > 0) {
                console.log('使用存储的时间点作为API参数:', this.videoTimestamps);
                return {
                    storypath: this.currentVideoUrl,
                    times: this.videoTimestamps
                };
            } else {
                console.log('未找到存储的时间点，使用默认时间点:', defaultTimes);
                return {
                    storypath: this.currentVideoUrl,
                    times: defaultTimes
                };
            }
        },
        
        // 添加这个新方法
        setSelectedVideoIds(ids) {
            this.selectedVideoIds = ids;
        },
        
        /**
         * 设置配音时长
         * 
         * 保存配音生成后的时长，用于计算费用
         * 
         * @param {number} duration - 配音时长（秒）
         */
        setDubbingDuration(duration) {
            if (typeof duration === 'number' && duration >= 0) {
                console.log('设置配音时长:', duration, '秒');
                this.dubbingDuration = duration;
                this.updateTimestamp();
            } else {
                console.warn('尝试设置无效的配音时长:', duration);
            }
        },
        
        /**
         * 设置项目ID
         * 
         * 保存从submitJob接口返回的项目ID，用于跳转到云剪辑系统
         * 
         * @param {number|string} id - 项目ID
         */
        setProjectId(id) {
            console.log('设置projectId:', id);
            if (id) {
                this.projectId = Number(id);
            } else {
                this.projectId = null;
            }
        },
        
        /**
         * 获取项目ID
         * 
         * 返回当前存储的项目ID，如果无效则返回null
         * 
         * @returns {number|null} 项目ID或null
         */
        getProjectId() {
            return this.projectId && !isNaN(this.projectId) ? this.projectId : null;
        },
        
        /**
         * 设置完整的任务响应数据
         * 
         * 保存submitGenerateJob接口返回的完整响应数据
         * 
         * @param {Object} response - API响应数据对象
         */
        setJobResponse(response) {
            console.log('保存完整的任务响应数据到Pinia');
            if (response && typeof response === 'object') {
                // 使用深拷贝保存数据，避免引用问题
                try {
                    this.jobResponse = JSON.parse(JSON.stringify(response));
                } catch (e) {
                    console.error('保存任务响应数据失败:', e);
                    // 如果深拷贝失败，直接赋值
                    this.jobResponse = response;
                }
                
                // 同时更新projectId，确保一致性
                if (response.projectId) {
                    this.projectId = Number(response.projectId);
                }
            } else {
                console.warn('尝试保存无效的任务响应数据:', response);
                this.jobResponse = null;
            }
        },
        
        /**
         * 获取任务响应数据
         * 
         * @returns {Object|null} 任务响应数据对象或null
         */
        getJobResponse() {
            return this.jobResponse;
        },
        
        /**
         * 初始化浏览器会话
         * 
         * 检查是否为浏览器重新打开，如果是则清除内容
         * 在应用初始化时调用
         */
        initBrowserSession() {
            // 使用sessionStorage来检测浏览器会话状态
            // sessionStorage会在浏览器关闭后自动清除，而在刷新页面时保留
            const sessionMarker = sessionStorage.getItem('browserSessionActive');
            
            // 如果sessionStorage中没有标记，表示这是浏览器重新打开的第一次访问
            if (!sessionMarker) {
                console.log('检测到浏览器新会话，清除预览内容、配音角色、视频和音乐信息');
                
                // 清除内容相关数据
                this.title = '';
                this.content = '';
                this.isTimelineContent = false;
                this.extractedContentList = [];
                
                // 清除配音角色相关信息
                this.selectedRole = null;
                
                // 清除视频相关信息
                this.videoList = [];
                this.currentVideoUrl = '';
                
                // 清除音乐相关信息 - 使用musicStore清空音乐列表
                try {
                    const musicStore = useMusicStore();
                    musicStore.clearAll();
                    console.log('已清空音乐列表');
                } catch (e) {
                    console.error('清空音乐列表失败:', e);
                }
            } else {
                console.log('检测到现有会话，保留预览内容、配音角色、视频和音乐信息');
            }
            
            // 设置会话标志，表示会话已激活
            // 此标记将在浏览器关闭时自动清除，但在刷新时保留
            sessionStorage.setItem('browserSessionActive', 'true');
            
            // 生成并保存会话ID - 这只是为了兼容性保留
            const newSessionId = Date.now().toString();
            this.browserSessionId = newSessionId;
        },

        /**
         * 设置解析的视频信息
         * 
         * 存储从crawlVideoInfoFromUrl接口获取的视频解析结果
         * 
         * @param {Object} videoInfo - 视频信息对象，包含title, video(URL), thumbnail等
         */
        setParsedVideoInfo(videoInfo) {
            console.log('设置解析的视频信息到Pinia store:', videoInfo);
            if (!videoInfo) {
                this.parsedVideoInfo = null;
                return;
            }
            
            this.parsedVideoInfo = {
                title: videoInfo.title || '',
                video: videoInfo.video || videoInfo.videoUrl || '',
                thumbnail: videoInfo.thumbnail || videoInfo.coverUrl || '',
                description: videoInfo.description || '',
                duration: videoInfo.duration || 0,
                // 其他可能需要的视频信息字段
            };
        },

        /**
         * 获取解析的视频信息
         * 
         * @returns {Object|null} 视频信息对象或null
         */
        getParsedVideoInfo() {
            return this.parsedVideoInfo;
        },

        /**
         * 清除解析的视频信息
         */
        clearParsedVideoInfo() {
            this.parsedVideoInfo = null;
        },
    },
    
    /**
     * 配置持久化存储
     * 
     * 使用localStorage将状态持久化到浏览器本地存储
     * 确保页面刷新或重新访问网站时，预览数据不会丢失
     */
    persist: {
        // 启用持久化功能
        enabled: true,
        // 持久化策略配置
        strategies: [
            {
                // 本地存储的键名
                key: 'editor-preview-data',
                // 使用localStorage作为存储方式
                storage: localStorage,
                // 指定需要持久化的状态路径
                paths: ['title', 'content', 'lastUpdated', 'selectedRole', 'videoList', 'isTimelineContent', 'extractedContentList', 'currentVideoUrl', 'videoTimestamps', 'snapShotGenerated', 'lastSnapshotUrl', 'dubbingDuration', 'selectedVideoIds', 'projectId', 'jobResponse']
            }
        ]
    }
});

// 导出一个初始化函数，在main.js中调用
export const initPreviewStoreSession = () => {
    const previewStore = usePreviewStore();
    previewStore.initBrowserSession();
    
    // 使用sessionStorage后不再需要监听beforeunload事件
    // sessionStorage会在浏览器关闭时自动清除，而刷新页面时会保留
    console.log('初始化预览内容会话管理，使用sessionStorage标记浏览器会话');
}; 