/* 统一菜单项高度样式 */

/* 全局强制覆盖所有菜单高度 */
.el-menu .el-sub-menu__title,
.el-menu-item,
.el-sub-menu > .el-sub-menu__title,
.el-menu > .el-menu-item,
.el-menu--popup .el-menu-item,
.el-menu--horizontal > .el-menu-item,
.el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
  height: 40px !important;
  line-height: 40px !important;
  padding: 0 20px !important;
  min-height: 40px !important;
  max-height: 40px !important;
}

/* 调整子菜单弹出层 */
.el-menu--popup {
  min-width: 200px;
}

/* 调整图标和文本的垂直居中 */
.el-menu-item .el-icon,
.el-sub-menu__title .el-icon {
  vertical-align: middle !important;
  margin-right: 5px !important;
}

/* 确保菜单项文本垂直居中 */
.el-menu-item span,
.el-sub-menu__title span {
  vertical-align: middle !important;
  line-height: 40px !important;
  height: 40px !important;
  display: inline-block;
}

/* 调整箭头图标位置 */
.el-sub-menu__icon-arrow {
  margin-top: -5px !important;
}

/* 确保任何嵌套层级的菜单项都是40px高度 */
.el-menu .el-menu .el-menu-item,
.el-menu .el-menu .el-sub-menu__title {
  height: 40px !important;
  line-height: 40px !important;
} 