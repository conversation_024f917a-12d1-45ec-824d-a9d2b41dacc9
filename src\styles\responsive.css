/**
 * 响应式布局 CSS
 * 使用CSS Grid和视口单位替代JS缩放方案
 */

:root {
  /* 基础设计尺寸 - 对应原scaleHelper.js中的BASE_WIDTH和BASE_HEIGHT */
  --base-width: 1920px;
  --base-height: 1080px;
  
  /* 响应式单位转换变量 */
  --vw-factor: calc(100vw / 1920);
  --vh-factor: calc(100vh / 1080);
}

/* 基础布局设置 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* 替代transform scale的响应式字体和间距 */
body {
  font-size: calc(16px * var(--vw-factor));
  line-height: 1.5;
}

/* 按钮和控件的响应式大小 */
button, .control-item {
  padding: calc(8px * var(--vw-factor)) calc(16px * var(--vw-factor));
  font-size: calc(14px * var(--vw-factor));
}

/* 图标响应式大小 */
.icon {
  width: calc(24px * var(--vw-factor));
  height: calc(24px * var(--vw-factor));
}

/* VoiceOver页面特别处理 */
.voice-over-container {
  width: 100%;
  height: auto;
  max-height: calc(100vh - 20px);
  overflow: hidden;
  padding-bottom: 0;
  margin-bottom: 0;
}

/* 编辑器页面布局处理 - 按指定比例设置左右区域 */
.voice-over-container .main-content {
  display: flex;
  width: 100%;
}

/* 左侧区域固定38%宽度 */
/* .voice-over-container .main-content .left-section {
  width: 38% !important;
  flex: 0 0 38% !important;
} */
/* 

/* 使用CSS选择器查找第二个div子元素 */
/* 右侧区域固定60%宽度 */
/* .voice-over-container .main-content > div:nth-child(2) {  
  width: 62% !important;
  flex: 0 0 62% !important;
  margin-left: 0.1% !important;
}  */

/* 确保内容区域正确撑开 */
.voice-over-container .main-content .content-area,
.preview-section {
  width: 100%;
  height: 100%;
}

/* 修复红框区域 - 设置PreviewPanel组件中的textarea样式 */
.preview-textarea {
  width: 100% !important;
  min-height: 300px !important;

  box-sizing: border-box !important;
  border-radius: 4px !important;
}

/* 使用更具体的选择器确保样式应用 */
.voice-over-container .main-content .preview-section .preview-textarea,
.voice-over-container .main-content .preview-textarea {
  width: 100% !important;
  min-height: 500px !important;
  box-sizing: border-box !important;
  border-radius: 4px !important;
  font-size: 16px !important;
}

/* 修正preview-section的样式 */
.voice-over-container .main-content .preview-section,
.voice-over-container .preview-section {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  height: auto !important;
  min-height: 600px !important;
}

/* 修复preview-content的样式 */
.voice-over-container .preview-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  margin-top: 10px !important;
}

/* 直接针对PreviewPanel组件 */
.voice-over-container .main-content > .preview-panel {
  flex: 0 0 60% !important;
  width: 60% !important;
  margin-left: 1% !important;
  background-color: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05) !important;
}

/* 预览区域其他样式，确保其正确显示 */
.preview-panel,
.right-content {
  height: 100% !important;
  min-height: 500px !important;
  background-color: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05) !important;
}

.voice-over-tools {
  padding-bottom: 0;
}

.control-bar, .audio-player-wrapper {
  margin-bottom: 0;
}

/* 媒体查询，处理不同尺寸屏幕 */
/* 小型笔记本电脑 */
@media screen and (max-width: 1366px) {
  :root {
    --vw-factor: calc(100vw / 1366);
  }
}

/* 平板设备 */
@media screen and (max-width: 1024px) {
  :root {
    --vw-factor: calc(100vw / 1024);
  }
}

/* 确保最小尺寸限制 */
@media screen and (max-width: 768px) {
  #app {
    min-width: 768px;
    overflow-x: auto;
  }
} 