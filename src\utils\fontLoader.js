/**
 * 字体动态加载工具
 * 功能：支持CDN字体、本地字体文件的动态加载
 * 作者：AI Assistant
 */

class FontLoader {
    constructor() {
        this.loadedFonts = new Set(); // 已加载的字体缓存
        this.loadingPromises = new Map(); // 正在加载的字体Promise
    }

    /**
     * 字体CDN配置
     * 包含主流中文字体的CDN链接
     */
    // 🌟 多源CDN配置 - 每个字体都有多个备用源
    fontCDNMap = {
        // 阿里巴巴字体 - 多个备用CDN源
        'AlibabaPuHuiTi-Regular': [
            'https://at.alicdn.com/wf/webfont/jDOlwW7NjOl/JhGNtE0Y1.woff2',
            'https://webfont.res.alibaba.com/AlibabaPuHuiTi-Regular.woff2',
            'https://gw.alipayobjects.com/os/finxbff/compress_font/AlibabaPuHuiTi-Regular.woff2'
        ],
        '阿里巴巴普惠体': [
            'https://at.alicdn.com/wf/webfont/jDOlwW7NjOl/JhGNtE0Y1.woff2',
            'https://webfont.res.alibaba.com/AlibabaPuHuiTi-Regular.woff2'
        ],
        'AlimamaShuHeiTi-Bold': [
            'https://at.alicdn.com/wf/webfont/Qc0aWhWyJa/hKAOgbRq9K.woff2',
            'https://webfont.res.alibaba.com/AlimamaShuHeiTi-Bold.woff2',
            'https://gw.alipayobjects.com/os/finxbff/compress_font/AlimamaShuHeiTi-Bold.woff2'
        ],
        '阿里妈妈数黑体': [
            'https://at.alicdn.com/wf/webfont/Qc0aWhWyJa/hKAOgbRq9K.woff2',
            'https://webfont.res.alibaba.com/AlimamaShuHeiTi-Bold.woff2'
        ],
        '阿里妈妈数黑体 VF': [
            'https://at.alicdn.com/wf/webfont/Qc0aWhWyJa/hKAOgbRq9K.woff2',
            'https://webfont.res.alibaba.com/AlimamaShuHeiTi-Bold.woff2'
        ],
        'AlimamaShuHeiTi': [
            'https://at.alicdn.com/wf/webfont/Qc0aWhWyJa/hKAOgbRq9K.woff2',
            'https://webfont.res.alibaba.com/AlimamaShuHeiTi-Bold.woff2'
        ],
        
        // 思源字体 - 多个可靠的CDN源
        'Source Han Sans CN': [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400',
            'https://cdn.bootcdn.net/ajax/libs/fonts-googleapis/1.0.0/noto-sans-sc.css',
            'https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/google-fonts/1.0.0/noto-sans-sc.css'
        ],
        '思源黑体': [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400',
            'https://cdn.bootcdn.net/ajax/libs/fonts-googleapis/1.0.0/noto-sans-sc.css'
        ],
        'Source Han Serif CN': [
            'https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400',
            'https://cdn.bootcdn.net/ajax/libs/fonts-googleapis/1.0.0/noto-serif-sc.css'
        ],
        '思源宋体': [
            'https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400',
            'https://cdn.bootcdn.net/ajax/libs/fonts-googleapis/1.0.0/noto-serif-sc.css'
        ],
        'Noto Sans CJK SC': [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400',
            'https://cdn.bootcdn.net/ajax/libs/fonts-googleapis/1.0.0/noto-sans-sc.css'
        ],
        
        // 系统字体（无需CDN）
        'PingFangSC-Regular': null,
        'STHeitiSC-Light': null,
        'KaiTi': null,
        'FangSong': null,
        'SimHei': null,
        'LiSu': null,
        'YouYuan': null,
        'Microsoft YaHei': null,
        '微软雅黑': null,
        
        // 🌟 鸿蒙字体 - 使用Web Fonts服务
        'HarmonyOS Sans SC': [
            'https://cdn.jsdelivr.net/gh/HarmonyOS-Font/font@latest/HarmonyOS_Sans_SC_Regular.woff2',
            'https://unpkg.com/harmonyos-fonts@latest/dist/HarmonyOS_Sans_SC_Regular.woff2'
        ],
        '鸿蒙字体': [
            'https://cdn.jsdelivr.net/gh/HarmonyOS-Font/font@latest/HarmonyOS_Sans_SC_Regular.woff2',
            'https://unpkg.com/harmonyos-fonts@latest/dist/HarmonyOS_Sans_SC_Regular.woff2'
        ],
        'HarmonyOS Sans': [
            'https://cdn.jsdelivr.net/gh/HarmonyOS-Font/font@latest/HarmonyOS_Sans_SC_Regular.woff2'
        ],
        
        // 方正字体 - 使用备用源
        'FZLanTingHei-R-GBK': [
            'https://cdn.jsdelivr.net/npm/chinese-fonts@1.0.0/FZLanTingHei-R-GBK.woff2'
        ],
        'Yuanti SC': [
            'https://fonts.googleapis.com/css2?family=Yuanti+SC:wght@400',
            // 你可以添加其他备用CDN链接
        ],
        '圆体 SC': [
            'https://fonts.googleapis.com/css2?family=Yuanti+SC:wght@400',
        ],
    };

    /**
     * 动态加载字体
     * @param {string} fontName - 字体名称
     * @param {string} fontUrl - 字体文件URL（可选）
     * @returns {Promise} - 加载完成的Promise
     */
    async loadFont(fontName, fontUrl = null) {
        // 如果字体已加载，直接返回
        if (this.loadedFonts.has(fontName)) {
            console.log(`✅ 字体 "${fontName}" 已缓存，直接使用`);
            return Promise.resolve();
        }

        // 如果正在加载，返回现有Promise
        if (this.loadingPromises.has(fontName)) {
            console.log(`⏳ 字体 "${fontName}" 正在加载中...`);
            return this.loadingPromises.get(fontName);
        }

        // 检查是否为系统字体
        if (this.isSystemFont(fontName)) {
            this.loadedFonts.add(fontName);
            console.log(`🖥️ 系统字体 "${fontName}" 可直接使用`);
            return Promise.resolve();
        }

        // 🎨 优先使用传入的fontUrl，这是动态字体加载的核心
        let fontUrls = null;

        if (fontUrl) {
            // 如果提供了fontUrl，优先使用（支持API返回的动态URL）
            fontUrls = [fontUrl];
            console.log(`🎨 使用动态字体URL: "${fontName}" → ${fontUrl}`);
        } else {
            // 降级到预定义的CDN映射
            fontUrls = this.fontCDNMap[fontName];

            // 如果直接匹配失败，尝试智能匹配
            if (!fontUrls) {
                const matchedFontName = this.findBestFontMatch(fontName);
                if (matchedFontName) {
                    fontUrls = this.fontCDNMap[matchedFontName];
                    console.log(`🎯 智能匹配: "${fontName}" → "${matchedFontName}"`);
                }
            }
        }

        if (!fontUrls) {
            console.warn(`⚠️ 未找到字体 "${fontName}" 的URL，使用系统回退字体`);
            return Promise.resolve();
        }

        // 确保fontUrls是数组格式
        if (!Array.isArray(fontUrls)) {
            fontUrls = [fontUrls];
        }

        // 创建多源加载Promise
        const loadingPromise = this.loadFontWithFallback(fontName, fontUrls);
        this.loadingPromises.set(fontName, loadingPromise);

        try {
            await loadingPromise;
            this.loadedFonts.add(fontName);
            console.log(`✅ 字体 "${fontName}" 加载成功`);
        } catch (error) {
            console.error(`❌ 字体 "${fontName}" 加载失败:`, error);
        } finally {
            this.loadingPromises.delete(fontName);
        }

        return loadingPromise;
    }

    /**
     * 🌟 多源回退加载字体
     * @param {string} fontName - 字体名称
     * @param {Array} fontUrls - 字体URL数组，按优先级排序
     * @returns {Promise} - 加载Promise
     */
    async loadFontWithFallback(fontName, fontUrls) {
        let lastError = null;
        
        for (let i = 0; i < fontUrls.length; i++) {
            const fontUrl = fontUrls[i];
            console.log(`🔄 尝试加载字体 "${fontName}" - 源 ${i + 1}/${fontUrls.length}: ${fontUrl}`);
            
            try {
                await this.loadFontFromURL(fontName, fontUrl);
                console.log(`✅ 字体 "${fontName}" 第 ${i + 1} 个源加载成功`);
                return Promise.resolve();
            } catch (error) {
                lastError = error;
                console.warn(`❌ 字体 "${fontName}" 第 ${i + 1} 个源加载失败:`, error.message);
                
                // 如果不是最后一个源，继续尝试
                if (i < fontUrls.length - 1) {
                    console.log(`🔄 尝试下一个字体源...`);
                    continue;
                }
            }
        }
        
        // 所有源都失败了
        console.error(`💥 字体 "${fontName}" 所有 ${fontUrls.length} 个源都加载失败，最后错误:`, lastError);
        throw new Error(`所有字体源均无法访问: ${lastError?.message || '未知错误'}`);
    }

    /**
     * 从URL加载字体文件
     * @param {string} fontName - 字体名称
     * @param {string} fontUrl - 字体文件URL
     * @returns {Promise} - 加载Promise
     */
    loadFontFromURL(fontName, fontUrl) {
        return new Promise((resolve, reject) => {
            // ⏰ 设置超时机制
            const timeout = setTimeout(() => {
                reject(new Error(`字体加载超时: ${fontUrl}`));
            }, 8000); // 8秒超时

            const cleanup = () => {
                clearTimeout(timeout);
            };

            // 先检测URL是否可访问
            this.checkUrlAccessibility(fontUrl)
                .then(() => {
                    // 检查浏览器是否支持FontFace API
                    if ('FontFace' in window) {
                        // 使用FontFace API加载
                        const fontFace = new FontFace(fontName, `url(${fontUrl})`);
                        fontFace.load()
                            .then((loadedFace) => {
                                cleanup();
                                document.fonts.add(loadedFace);
                                console.log(`🎨 通过FontFace API加载字体: ${fontName}`);
                                resolve();
                            })
                            .catch((error) => {
                                console.warn(`FontFace API加载失败，尝试CSS方式: ${fontName}`, error);
                                // 降级到CSS方式
                                this.loadFontWithCSS(fontName, fontUrl, () => {
                                    cleanup();
                                    resolve();
                                }, (err) => {
                                    cleanup();
                                    reject(err);
                                });
                            });
                    } else {
                        // 降级方案：使用CSS @font-face
                        this.loadFontWithCSS(fontName, fontUrl, () => {
                            cleanup();
                            resolve();
                        }, (err) => {
                            cleanup();
                            reject(err);
                        });
                    }
                })
                .catch((error) => {
                    cleanup();
                    reject(new Error(`CDN无法访问: ${error.message}`));
                });
        });
    }

    /**
     * 🔍 快速检测URL可访问性
     * @param {string} url - 要检测的URL
     * @returns {Promise} - 检测结果
     */
    checkUrlAccessibility(url) {
        return new Promise((resolve, reject) => {
            // 简化版本：直接使用fetch进行快速检测
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                reject(new Error('连接超时'));
            }, 2000); // 2秒快速超时

            fetch(url, { 
                method: 'HEAD',
                mode: 'no-cors',  // 避免CORS问题
                cache: 'no-cache',
                signal: controller.signal
            }).then(() => {
                clearTimeout(timeoutId);
                resolve();
            }).catch((error) => {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    reject(new Error('连接超时'));
                } else {
                    // 对于no-cors模式，很多情况下会报错但实际可访问
                    // 所以我们假设网络是通的，直接resolve
                    console.log(`网络检测异常，但继续尝试加载: ${error.message}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 使用CSS @font-face加载字体
     * @param {string} fontName - 字体名称
     * @param {string} fontUrl - 字体文件URL
     * @param {Function} resolve - 成功回调
     * @param {Function} reject - 失败回调
     */
    loadFontWithCSS(fontName, fontUrl, resolve, reject) {
        // 创建style元素
        const style = document.createElement('style');
        style.textContent = `
            @font-face {
                font-family: "${fontName}";
                src: url("${fontUrl}") format("woff2"),
                     url("${fontUrl.replace('.woff2', '.woff')}") format("woff");
                font-display: swap;
                font-weight: normal;
                font-style: normal;
            }
        `;
        
        document.head.appendChild(style);
        console.log(`📝 通过CSS @font-face加载字体: ${fontName}`);

        // 使用字体加载检测
        this.waitForFontLoad(fontName, resolve, reject);
    }

    /**
     * 等待字体加载完成（优化版）
     * @param {string} fontName - 字体名称
     * @param {Function} resolve - 成功回调
     * @param {Function} reject - 失败回调
     */
    waitForFontLoad(fontName, resolve, reject) {
        if ('document' in window && document.fonts && document.fonts.ready) {
            // 🎯 改进的字体加载检测
            const maxWaitTime = 5000; // 最大等待5秒
            const startTime = Date.now();
            
            const checkFont = () => {
                if (this.checkFontAvailable(fontName)) {
                    console.log(`🎉 字体 "${fontName}" 检测到已可用`);
                    resolve();
                    return;
                }
                
                // 检查是否超时
                if (Date.now() - startTime > maxWaitTime) {
                    console.warn(`⏰ 字体 "${fontName}" 等待超时，但继续使用`);
                    resolve(); // 即使检测失败也resolve，避免阻塞
                    return;
                }
                
                // 继续检测
                setTimeout(checkFont, 100);
            };
            
            // 使用document.fonts.ready + 轮询检测
            document.fonts.ready.then(() => {
                checkFont();
            }).catch(() => {
                // 如果document.fonts.ready失败，直接开始轮询
                checkFont();
            });
        } else {
            // 降级方案：简单延时
            setTimeout(() => {
                console.log(`⏰ 字体 "${fontName}" 延时加载完成（降级方案）`);
                resolve();
            }, 1000);
        }
    }

    /**
     * 检查字体是否可用
     * @param {string} fontName - 字体名称
     * @returns {boolean} - 是否可用
     */
    checkFontAvailable(fontName) {
        // 创建测试元素
        const testElement = document.createElement('div');
        testElement.style.fontFamily = fontName;
        testElement.style.position = 'absolute';
        testElement.style.visibility = 'hidden';
        testElement.style.width = 'auto';
        testElement.style.height = 'auto';
        testElement.textContent = '测试字体加载';
        
        document.body.appendChild(testElement);
        const computedFont = window.getComputedStyle(testElement).fontFamily;
        document.body.removeChild(testElement);
        
        return computedFont.includes(fontName);
    }

    /**
     * 智能匹配字体名称
     * @param {string} fontName - 输入的字体名称
     * @returns {string|null} - 匹配到的字体名称，如果没有匹配到返回null
     */
    findBestFontMatch(fontName) {
        const normalizedInput = fontName.toLowerCase().replace(/\s+/g, '');
        let bestMatch = null;
        let bestScore = 0;

        // 遍历所有可用字体，寻找最佳匹配
        for (const availableFontName of Object.keys(this.fontCDNMap)) {
            const normalizedAvailable = availableFontName.toLowerCase().replace(/\s+/g, '');
            
            // 完全匹配
            if (normalizedInput === normalizedAvailable) {
                return availableFontName;
            }
            
            // 包含匹配
            if (normalizedInput.includes(normalizedAvailable) || normalizedAvailable.includes(normalizedInput)) {
                const score = Math.min(normalizedInput.length, normalizedAvailable.length) / 
                              Math.max(normalizedInput.length, normalizedAvailable.length);
                if (score > bestScore) {
                    bestScore = score;
                    bestMatch = availableFontName;
                }
            }
            
            // 关键词匹配 - 阿里字体特殊处理
            if (fontName.includes('阿里妈妈') && availableFontName.includes('阿里妈妈')) {
                return availableFontName;
            }
            if (fontName.includes('阿里巴巴') && availableFontName.includes('阿里巴巴')) {
                return availableFontName;
            }
            if (fontName.includes('思源') && availableFontName.includes('思源')) {
                return availableFontName;
            }
            if (fontName.includes('鸿蒙') && availableFontName.includes('HarmonyOS')) {
                return availableFontName;
            }
        }

        // 如果找到了较好的匹配（相似度超过50%），返回
        return bestScore > 0.5 ? bestMatch : null;
    }

    /**
     * 检查是否为系统字体
     * @param {string} fontName - 字体名称
     * @returns {boolean} - 是否为系统字体
     */
    isSystemFont(fontName) {
        const systemFonts = [
            'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体',
            'KaiTi', '楷体', 'FangSong', '仿宋', 
            'LiSu', '隶书', 'YouYuan', '幼圆',
            'PingFang SC', 'PingFangSC-Regular', '苹方',
            'STHeitiSC-Light', '华文黑体',
            'Arial', 'sans-serif', 'serif'
        ];
        
        return systemFonts.some(sysFont => 
            fontName.toLowerCase().includes(sysFont.toLowerCase())
        );
    }

    /**
     * 批量加载字体
     * @param {Array} fontList - 字体列表 [{name, url}]
     * @returns {Promise} - 所有字体加载完成的Promise
     */
    async loadFonts(fontList) {
        const loadPromises = fontList.map(font => 
            this.loadFont(font.name, font.url)
        );
        
        try {
            await Promise.allSettled(loadPromises);
            console.log(`📚 批量字体加载完成，成功加载 ${this.loadedFonts.size} 个字体`);
        } catch (error) {
            console.error('批量字体加载出现错误:', error);
        }
    }

    /**
     * 根据字体名称解析字体家族
     * @param {string} fontName - 字体名称
     * @returns {string} - CSS字体家族
     */
    resolveFontFamily(fontName) {
        // 确保字体已加载
        this.loadFont(fontName);
        
        // 构建字体栈
        if (fontName.includes('阿里妈妈')) {
            return `"${fontName}", "AlimamaShuHeiTi-Bold", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('阿里巴巴')) {
            return `"${fontName}", "AlibabaPuHuiTi-Regular", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('思源')) {
            return `"${fontName}", "Source Han Sans CN", "Noto Sans CJK SC", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('苹方')) {
            return `"${fontName}", "PingFang SC", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('鸿蒙')) {
            return `"${fontName}", "HarmonyOS Sans SC", "PingFang SC", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('黑体')) {
            return `"${fontName}", "SimHei", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('Yuanti') || fontName.includes('圆体')) {
            return `"${fontName}", "Yuanti SC", "Microsoft YaHei", "微软雅黑", sans-serif`;
        }else {
            return `"${fontName}", "Microsoft YaHei", "微软雅黑", "SimHei", "Arial", sans-serif`;
        }
    }

    /**
     * 获取已加载的字体列表
     * @returns {Array} - 已加载的字体名称数组
     */
    getLoadedFonts() {
        return Array.from(this.loadedFonts);
    }

    /**
     * 清除字体缓存
     */
    clearCache() {
        this.loadedFonts.clear();
        this.loadingPromises.clear();
        console.log('🗑️ 字体缓存已清除');
    }
}

// 创建全局字体加载器实例
const fontLoader = new FontLoader();

// 导出字体加载器
export default fontLoader;

// 导出便捷方法
export const loadFont = (fontName, fontUrl) => fontLoader.loadFont(fontName, fontUrl);
export const loadFonts = (fontList) => fontLoader.loadFonts(fontList);
export const resolveFontFamily = (fontName) => fontLoader.resolveFontFamily(fontName); 