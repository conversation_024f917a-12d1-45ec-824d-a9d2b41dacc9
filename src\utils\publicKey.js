import JSEncrypt from 'jsencrypt';
const PUBLIC_KEY = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApv2MsgpQxNBAzAWNQV7FZKw04cKQIShy7MOlzQcWtzUbMI0tYLd7V7QUYF7yC/oG+t/6JhLD5+CJJx3oBrqt0WZH1nRlQY+iIR8ajpIFVqREJAbOjLz9NeN8KwIasY/oO0wds116h0Czuhld1hqWMyAIDw6q4jtdYxj51pu4X4wmspxCsJq0fyJhekTNi6npz+OGAytDxOLHY11tOkX4bBy2CwJCKHWS8osQIurIlNAqfohWOJpxZw/nGbruZmsCTcZZ9BVtvapolFiNt5asM0lFK1q10YOFQSzofr0ioFPRanH5MOvdxHgJtqG0Pfjq7rfk52ug8H4wur6Fqvb5dwIDAQAB`; // 后端提供的公钥

export const encryptPassword = (password) => {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(PUBLIC_KEY);
  return encryptor.encrypt(password);

};