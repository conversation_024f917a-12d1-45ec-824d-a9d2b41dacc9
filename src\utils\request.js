// utils/request.js
import axios from 'axios'
import { globalConfig } from '@/config/index'
import { ElMessage } from "element-plus";
let CancelToken = axios.CancelToken;
let isHandling401 = false
/**
 * HTTP请求工具模块
 * 
 * 基于axios封装的HTTP请求工具，提供统一的请求处理、错误处理和拦截器。
 * 支持自动添加认证信息、参数编码、统一的错误处理和状态码响应。
 * 特别适配了后端API的返回格式，简化了API调用流程。
 */

/**
 * 错误码映射表
 * 
 * 将HTTP状态码映射为友好的中文错误消息
 * 用于在发生HTTP错误时向用户展示更易理解的提示
 */
const ERROR_MESSAGES = {
    400: '请求参数错误',
    401: '未授权或登录过期',
    403: '拒绝访问',
    404: '请求的资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    500: '服务器内部错误',
    501: '服务未实现',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时',
    505: 'HTTP版本不受支持'
}

/**
 * 统一错误处理函数
 * 
 * 处理HTTP请求过程中可能出现的各种错误情况
 * 根据错误类型返回友好的错误信息，并执行特定的错误处理逻辑
 * 
 * @param {Error} error - Axios请求返回的错误对象
 * @returns {Promise} - 返回被拒绝的Promise，便于错误传递
 */
let not_error=false;
const handleError = (error) => {
    console.log(error,'error');
    
    let message = ''

    if (error.response) {
        // 服务器返回了错误状态码
        const status = error.response.status
        // message = ERROR_MESSAGES[status] || `请求失败，状态码：${status}`

        // 特殊状态码处理
        switch (status) {
            case 401:
                // 未登录或 token 过期
                // 仅清除用户信息，但不跳转页面
                // console.error(localStorage.getItem('user')&&localStorage.getItem('user').token, '用户未登录或登录已过期')
                !not_error&&loginOut('请重新登录',not_error)
                break
            case 403:
                message='权限不足，请联系管理员'
                // 权限不足
                // console.error('权限不足，请联系管理员')
                break
            case 404:
                message='请求的资源不存在'
                // 资源不存在
                // console.error('请求的资源不存在')
                break
            case 500:
                 message='服务器错误，请稍后重试'
                // 服务器错误
                // console.error('服务器错误，请稍后重试')
                break
            case 511:
                !not_error&&loginOut('该账号已被他人登录',not_error)
                break

        }
    } else if (error.request) {
        // 请求已发出但未收到响应
        // if (error.code === 'ECONNABORTED') {
        //     message = '请求超时，请检查网络后重试'
        // } else {
        //     message = '网络错误，请检查网络连接'
        // }
    } else {
        // 请求配置出错
        message = error.message || '请求错误'
    }
    if(message!=''&&!not_error){
       
        debouncedShowTip(message)
    }
    // 显示错误信息（可以根据实际UI组件库进行调整）
    console.error(message)
    // 如果使用 Element Plus
    // ElMessage.error(message)
    // 如果使用 Ant Design Vue
    // message.error(message)

    return Promise.reject(error)
}

/**
 * 创建axios实例
 * 
 * 配置基础URL、超时时间和默认请求头
 * 通过环境变量获取API基础URL，实现开发和生产环境的灵活配置
 */
const service = axios.create({
    baseURL: globalConfig.apiBase, // 使用globalConfig中处理过的apiBase
    // timeout: import.meta.env.VITE_API_TIMEOUT ? Number(import.meta.env.VITE_API_TIMEOUT) : 5000000000000000, // 从环境变量获取超时时间，默认5秒
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    }
})

// 输出API基础URL，用于调试
console.log('当前API基础URL:', globalConfig.apiBase);
console.log('当前环境类型:', import.meta.env.VITE_ENV_TYPE);
console.log('当前域名:', window.location.hostname);


/**
 * 请求拦截器
 * 
 * 在发送请求前执行的预处理逻辑
 * 处理请求参数编码、认证信息添加等操作
 */

service.interceptors.request.use(config => {
    let need_code=false
    // 处理取消请求
    if (config.data && config.data.cancelToken) {
        config.cancelToken = config.data.cancelToken; // 直接使用传入的 cancelToken
    }
    if(config?.data?.not_error||config?.params?.not_error){
        not_error=true
        delete config?.data?.not_error;
        delete config?.params?.not_error;
    }
    // 检查请求的 URL 是否以 '/audio1' 开头（针对视频的请求）
    if (config.url && config.url.startsWith('/audio1')) {
        // 在生产环境或peiyinbangshou.com域名下使用HTTPS
        const protocol = import.meta.env.VITE_ENV_TYPE === 'pro' || window.location.hostname === 'peiyinbangshou.com' 
            ? 'https://' 
            : 'http://';
        
        // 在peiyinbangshou.com域名下，音频请求指向阿里云OSS
        // 其他环境下仍然使用配置的OSS地址
        config.baseURL = `${protocol}miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com`;
        config.url = config.url.replace('/audio1', '');
    }

    console.log( service.defaults.baseURL,config.data,555);
    // 增加一个配置选项，允许跳过编码
    const shouldEncode = config.encode !== false && !(config?.data?.no_encode||config?.params?.no_encode);

    // 处理Python后端可能需要的特殊参数格式
    console.log(config.method === 'post' && !(config.data instanceof FormData));
    
    // 对 URL 参数进行编码 - 仅当 encode !== false 时
    if (shouldEncode && config.params) {
        Object.keys(config.params).forEach(key => {
            config.params[key] = encodeURIComponent(config.params[key]);
        });
    }
    config.data?.cancelToken&& delete config.data.cancelToken
    if (config?.data?.need_code || config?.params?.need_code) {
        need_code = true;
        delete config?.data?.need_code;
        delete config?.params?.need_code;
      }
      config._need_code = need_code;
      config?.data?.no_encode&& delete config.data.no_encode
      config?.params?.no_encode&& delete config.params.no_encode
    // 对 POST 请求的 data 进行处理 - 仅当 encode !== false 时
    if (config.method === 'post' && !(config.data instanceof FormData)) {
        if (typeof config.data === 'object' && shouldEncode) {  
            // 对对象中的每个值进行编码
            const encodedData = {};
            Object.keys(config.data).forEach(key => {
                encodedData[key] = encodeURIComponent(config.data[key]);
            });
            config.data = JSON.stringify(encodedData);
        } else if (shouldEncode) {
            config.data = encodeURIComponent(config.data);
        } else {
           
            // 不编码，但确保是 JSON 字符串
            config.data = typeof config.data === 'object' ? 
                JSON.stringify(config.data) : config.data;
        }
    }
    // 添加认证token（如果存在）
    const userStr = localStorage.getItem('user')
    let token = null
    if (userStr) {
        try {
            const userObj = JSON.parse(userStr)
            token = userObj.token // 从user对象中获取token
        } catch (e) {
            console.error('解析user对象失败', e)
        }
    }
    
    if (token) {
        config.headers.Authorization = `Bearer ${token}`
    }

    return config
})

/**
 * 响应拦截器
 * 
 * 在接收到响应后执行的处理逻辑
 * 统一处理响应格式，提取有效数据，处理错误情况
 * 适配后端API的统一返回格式：{ code: 0, data: {}, message: 'success' }
 */
service.interceptors.response.use(
    response => {
        let need_code=response.config._need_code || false
        // 处理成功响应
        console.log('响应拦截器，返回数据:', response)
        const res = response.data 
        console.log('响应拦截器，返回数据:111', res)
        // 检查是否是权益检查接口（通过URL判断）
        const isUserBenefitsCheck = response.config && 
              response.config.url && 
              response.config.url.includes('/userAuth/user/benefits/check');
        if (res.code === 401) {
            return loginOut('请重新登录')
        }
        if(need_code){
            return Promise.resolve(res)
        } else if (isUserBenefitsCheck && res?.code === 1) {
            // 特殊处理：算力检查接口即使code为1也返回完整数据
            console.log('算力检查接口特殊处理，即使code为1也返回数据');
            return Promise.resolve(res)
        } else if (res?.code === 0 || res?.status_code === 200 ) { // 后端约定的成功状态码
            console.log('响应拦截器，返回数据:222', res?.data || res?.content)
            return Promise.resolve(res?.data || res?.content || res)
        } else {
            return Promise.reject(res?.message || 'Error')
        }
    },
    error => {
        return handleError(error)  // 调用统一错误处理函数
    }
)
let showTip=(message)=>{
    if(message&&message.trim()!=''){
        console.log(message,'错误1');
        
        ElMessage.error(message)
    }
}
let debounce=(fn, delay = 300)=>{
    let timer = null;
    return function(...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        fn.apply(this, args);
      }, delay);
    };
  }
//公共防抖
const debouncedShowTip = debounce(showTip, 1000);
  let loginOut = (message = '', not_error = false) => {
    console.log('退出');

    const now = Date.now();
    const lastTimeStr = localStorage.getItem('lastHandling401Time');
    const lastTime = lastTimeStr ? parseInt(lastTimeStr, 10) : 0;
    const interval = 60 * 1000; // 1分钟防抖间隔

    if (now - lastTime < interval) {
        console.log('防抖，1分钟内不重复处理');
        return;
    }

    localStorage.setItem('lastHandling401Time', now.toString());

    if (localStorage.getItem('user')) {
        try {
            localStorage.removeItem('user'); // 清除token等用户信息
            console.log('用户信息已清除');
            setTimeout(() => {
                ElMessage.error(message);
                // 这里不直接刷新页面，改为跳转登录页或显示登录弹窗
                window.location.reload();

            }, 200);
        } catch (e) {
            console.error('解析user失败', e);
        }
    }
};

// 导出封装后的请求工具
export default service


/**
 * 以下是被注释掉的旧版代码，保留作为参考
 * 目前已不再使用，但保留以便了解代码演进历史
 */

// utils/request.js
// import axios from 'axios'

// const service = axios.create({
//     baseURL: import.meta.env.VITE_API_BASEURL, // 建议通过环境变量配置
//     timeout: 15000, // 根据接口响应时间调整
//     headers: {
//         'Content-Type': 'application/json;charset=UTF-8'
//     }
// })

// // 请求拦截器（处理Python后端常见需求）
// service.interceptors.request.use(config => {
//     // 处理Python后端可能需要的特殊参数格式
//     if (config.method === 'post' && !(config.data instanceof FormData)) {
//         config.data = JSON.stringify(config.data)
//     }

//     // 添加认证token（如果需要）
//     const userStr = localStorage.getItem('user')
//     let token = null
//     if (userStr) {
//         try {
//             const userObj = JSON.parse(userStr)
//             token = userObj.token // 从user对象中获取token
//         } catch (e) {
//             console.error('解析user对象失败', e)
//         }
//     }
//     if (token) {
//         config.headers.Authorization = `Bearer ${token}`
//     }

//     return config
// })

// // 响应拦截器（适配Python后端常见响应格式）
// service.interceptors.response.use(
//     response => {
//         // 假设Python接口统一返回格式：{ code: 0, data: {}, message: 'success' }
//         const res = response.data

//         if (res.code === 0) { // 根据实际接口code值调整
//             return Promise.resolve(res.data)
//         } else {
//             return Promise.reject(res.message || 'Error')
//         }
//     },
//     error => {
//         // 处理HTTP错误状态码
//         if (error.response?.status === 401) {
//             // token过期处理
//             localStorage.removeItem('user') // 清除user（包含token）
//             // 不进行页面跳转
//         }
//         return Promise.reject(error)
//     }
// )