// 你原始代码的基础上，进行Mac特殊处理的修改

// 1. 定义策略常量（可以用原有策略，也可加上专门的策略名称）
const ScaleStrategy = {
	FIT_WIDTH: 'fit-width',
	CONTAIN: 'contain',
	COVER: 'cover',
	FIT_SCREEN: 'fit-screen', // 继续使用原有策略名
  };
  
  // 2. 代码中检测为Mac的变量
  const isMacOS = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
  // 改进的Safari检测
  const isSafari = (function() {
    const ua = navigator.userAgent;
    const isSafari = /^((?!chrome|android).)*safari/i.test(ua) && 
                  !(ua.indexOf('Chrome') > -1);
    console.log('Safari检测结果:', isSafari, 'UserAgent:', ua);
    return isSafari;
  })();

  console.log('isSafari=========', isSafari)
  
  // 添加缩放功能开关变量
  let isScalingActive = true;
  let currentConfig = {};
  
  // 检测是否处于全屏模式
  function isInFullScreen() {
    return (
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    ) !== null;
  }
  
  // 3. updateScaleFactor中添加Mac专用逻辑
  function updateScaleFactor(force = false) {
	if (!isScalingActive && !force) return;
	const targetElement = document.querySelector(currentConfig.targetSelector);
	if (!targetElement) return;
	if (shouldExcludeCurrentPage() && !force) {
	  resetScaling(targetElement);
	  return;
	}
  
	const windowWidth = window.innerWidth;
	const windowHeight = window.innerHeight;
	let scale = 1;
  
	// 具体到Mac系统
	if (isMacOS) {
	  // 目标：在不滚动、保持纵横比的前提下，使全部内容都在屏幕内
	  const baseWidth = currentConfig.baseWidth;   // 1920
	  const baseHeight = currentConfig.baseHeight; // 953
	  
	  // 宽度比例
	  const widthScale = windowWidth / baseWidth;
	  // 缩放到宽度满足
	  let scaleCandidate = widthScale;
  
	  // 计算对应的高度
	  const scaledHeight = baseHeight * scaleCandidate;
  
	  // 如果缩放后高度大于窗口高度，则用高度比例
	  if (scaledHeight > windowHeight) {
		scaleCandidate = windowHeight / baseHeight;
	  }
  
	  // 采用较小的比例，保证内容全部在屏幕内
	  scale = scaleCandidate;
  
	  // 计算内容缩放后的内容高度（可能比窗口高一点，但会在居中后完全显示）
	  const contentHeight = baseHeight * scale;
  
	  // 垂直偏移（内容居中，确保完全显示）
	  const offsetY = (windowHeight - contentHeight) / 2;
  
	  // 水平偏移（居中）
	  const offsetX = (windowWidth - baseWidth * scale) / 2;

	  // Safari使用简化处理，避免复杂DOM操作导致自动刷新
	  if (isSafari) {
		console.log('Safari使用简化缩放处理，避免自动刷新');
		
		// 使用简化的Safari处理，仅应用基本变换
		applyTransform(targetElement, scale, offsetX, offsetY);
		
		// 最小化Safari特殊处理，避免过多DOM操作
		// 已禁用复杂的Safari专用样式和DOM操作
		
		// 提前返回，不执行后面的通用 Mac 设置
		return;
	  }
  
	  // 应用变换，保持内容全部显示
	  applyTransform(targetElement, scale, offsetX, offsetY);
  
	  // 禁止垂直滚动条 - 统一使用overflow属性
	//   document.body.style.overflow = 'hidden';
  
	  // 设置body的最小内容高度，避免裁剪
	  document.body.style.minHeight = `${contentHeight + offsetY}px`;
  
	} else {
	  // 非Mac平台使用原有逻辑或其他策略
	  // 原有逻辑示例（可替换）
	  switch(currentConfig.strategy) {
		case ScaleStrategy.CONTAIN:
		  scale = Math.min(
			window.innerWidth / currentConfig.baseWidth,
			window.innerHeight / currentConfig.baseHeight);
		  break;
		case ScaleStrategy.COVER:
		  scale = Math.max(
			window.innerWidth / currentConfig.baseWidth,
			window.innerHeight / currentConfig.baseHeight);
		  break;
		case ScaleStrategy.FIT_SCREEN:
		default:
		  scale = window.innerWidth / currentConfig.baseWidth;
		  // 如果需要消除底部空白
		  if (currentConfig.removeBottomSpace) {
			const scaledHeight = currentConfig.baseHeight * scale;
			if (scaledHeight > window.innerHeight && !currentConfig.maintainAspectRatio) {
			  scale = window.innerHeight / currentConfig.baseHeight;
			}
		  }
		  break;
	  }
	  // 其他位置偏移
	  const offsetX = (window.innerWidth - currentConfig.baseWidth * scale) / 2;
	  const offsetY = (currentConfig.strategy === ScaleStrategy.FIT_SCREEN || currentConfig.strategy === ScaleStrategy.CONTAIN) ? 
					  (window.innerHeight - currentConfig.baseHeight * scale) / 2 : 0;
	  applyTransform(targetElement, scale, offsetX, offsetY);
	  // 统一使用overflow属性
	  document.body.style.overflow = currentConfig.showVerticalScrollbar ? 'auto' : 'hidden';
	  // 设置body的最小高度
	  document.body.style.minHeight = `${currentConfig.baseHeight * scale + offsetY}px`;
	}
  
	// 其他调试信息（可选）
	if (currentConfig.debug) {
	  console.debug(`ScaleHelper: window ${window.innerWidth}x${window.innerHeight}, scale ${scale}`);
	}
  }
  
  // 4. 放置一个应用变换的方法
  function applyTransform(element, scale, offsetX = 0, offsetY = 0) {
	// 使用当前实际的窗口高度而不是页面加载时的静态值
	const currentScreenHeight = window.innerHeight;
	let transform = `scale(${scale}) translateZ(0)`;
	if (currentScreenHeight < 953) {
		transform = `scale(${scale}) translateZ(0)`;
	} else {
		transform = `translateZ(0)`; // 不缩放
	}
	element.style.transform = transform
	
	element.style.transformOrigin = '0 0';
	element.style.transition = currentConfig.transition > 0 ? `transform ${currentConfig.transition}ms ease-out` : '';
	element.style.marginLeft = `${offsetX}px`;
	element.style.marginTop = `${offsetY}px`;
  
	// Safari特殊处理已简化，避免自动刷新
	// 已禁用Safari复杂DOM操作，使用通用处理
	// if (isMacOS && isSafari) {
	//   element.style.height = `${currentConfig.baseHeight}px`;
	//   void element.offsetHeight;
	//   setTimeout(() => {
	//     document.body.style.overflow = 'scroll';
	//   }, 50);
	// }
  }
  
  // 添加Safari专用样式函数
  function addSafariStyles() {
    if (!isSafari) return;
    
    // 创建专用样式标签
    const styleEl = document.createElement('style');
    styleEl.id = 'safari-specific-styles';
    styleEl.textContent = `
      html, body {
        overflow: scroll !important;
        -webkit-overflow-scrolling: touch !important;
        min-height: 100% !important;
      }
      ::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 7px;
      }
      ::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background-color: rgba(0, 0, 0, .5);
      }
    `;
    
    // 如果样式已存在则移除
    const existingStyle = document.getElementById('safari-specific-styles');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    document.head.appendChild(styleEl);
    console.log('Safari专用样式已添加');
  }
  
  // 为Safari浏览器提供刷新功能的函数
  function refreshSafari() {
    console.log('Safari自动刷新已禁用，避免自动刷新影响用户体验，时间:', new Date().toISOString());
    // 已禁用Safari自动刷新逻辑，但保留函数结构以便需要时恢复
    // 如果需要手动刷新，可以在控制台调用window.location.reload()
    
    // 自动刷新已禁用
    // try {
    //   window.location.reload(true);
    //   setTimeout(() => {
    //     window.location.href = window.location.href;
    //   }, 100);
    // } catch (e) {
    //   console.error('刷新页面失败:', e);
    //   document.location = document.location.href;
    // }
  }
  
  // 监听全屏变化事件
  function addFullScreenChangeListener() {
    // 创建统一的全屏变化处理函数
    const handleFullscreenChange = () => {
      if (isSafari) {
        // Safari自动刷新已禁用，改用updateScaleFactor
        console.log('ScaleHelper: Safari检测到全屏变化，使用缩放更新替代自动刷新');
        updateScaleFactor();
      } else {
        updateScaleFactor();
      }
    };
    
    // 存储处理函数以便清理
    if (!window._fullscreenHandlers) {
      window._fullscreenHandlers = {};
    }
    
    window._fullscreenHandlers.fullscreenchange = handleFullscreenChange;
    window._fullscreenHandlers.webkitfullscreenchange = handleFullscreenChange;
    window._fullscreenHandlers.mozfullscreenchange = handleFullscreenChange;
    window._fullscreenHandlers.MSFullscreenChange = handleFullscreenChange;
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
  }
  
  // 5. 注释掉自动初始化，改为由App.vue统一控制
  // 避免与App.vue的动态导入产生双重初始化冲突
  /*
  // 初始化加载
  (function() {
	if (typeof window !== 'undefined') {
	  document.addEventListener('DOMContentLoaded', () => {
		// 初始化配置
		setConfig();
		
		// Safari处理已简化，避免过度DOM操作导致自动刷新
		if (isSafari) {
		  console.log('Safari缩放处理已简化，避免自动刷新');
		  // 已禁用复杂的Safari专用处理
		  // addSafariStyles();
		  // document.documentElement.style.overflow = 'scroll';
		  // document.body.style.overflow = 'scroll';
		  // document.body.style.webkitOverflowScrolling = 'touch';
		}
		
		// 立即应用缩放
		updateScaleFactor();
  
		// 监听窗口变化 - 使用统一的处理函数变量，方便清理
		window._resizeHandler = () => {
		  console.log('ScaleHelper: 窗口大小改变，是否Safari:', isSafari, '窗口尺寸:', window.innerWidth, 'x', window.innerHeight);
		  
		  if (window._resizeDebounce) clearTimeout(window._resizeDebounce);
		  
		  window._resizeDebounce = setTimeout(() => {
			if (isSafari) {
			  // Safari自动刷新已禁用，改用updateScaleFactor
			  console.log('Safari检测到窗口变化，使用缩放更新替代自动刷新');
			  updateScaleFactor();
			} else {
			  updateScaleFactor();
			}
		  }, 100);
		};
		window.addEventListener('resize', window._resizeHandler);
		
		window._orientationHandler = () => {
		  console.log('ScaleHelper: 设备方向改变，是否Safari:', isSafari);
		  
		  setTimeout(() => {
			if (isSafari) {
			  // Safari自动刷新已禁用，改用updateScaleFactor
			  console.log('Safari检测到方向变化，使用缩放更新替代自动刷新');
			  updateScaleFactor();
			} else {
			  updateScaleFactor();
			}
		  }, 50);
		};
		window.addEventListener('orientationchange', window._orientationHandler);
        
        // 添加全屏变化监听
        addFullScreenChangeListener();
	  });
	}
  })();
  */
  
  // 6. 设置配置函数
  function setConfig(config = {}) {
	// 这里可以加入Mac特殊的基础高度
	currentConfig = {
	  ...currentConfig,
	  baseWidth: 1920,
	  baseHeight: isMacOS ? 953 : 953 * (window.innerHeight / window.innerWidth),
	  strategy: isMacOS ? ScaleStrategy.FIT_SCREEN : ScaleStrategy.FIT_SCREEN,
	  ...config
	};
  }
  
  // 7. 清理事件监听器函数
  function removeEventListeners() {
    console.log('ScaleHelper: 开始清理事件监听器');
    
    // 清理resize事件监听器
    if (window._resizeHandler) {
      window.removeEventListener('resize', window._resizeHandler);
      window._resizeHandler = null;
      console.log('ScaleHelper: 清理resize监听器');
    }
    
    // 清理orientationchange事件监听器
    if (window._orientationHandler) {
      window.removeEventListener('orientationchange', window._orientationHandler);
      window._orientationHandler = null;
      console.log('ScaleHelper: 清理orientationchange监听器');
    }
    
    // 清理全屏变化事件监听器
    if (window._fullscreenHandlers) {
      const events = ['fullscreenchange', 'webkitfullscreenchange', 'mozfullscreenchange', 'MSFullscreenChange'];
      events.forEach(event => {
        if (window._fullscreenHandlers[event]) {
          document.removeEventListener(event, window._fullscreenHandlers[event]);
          delete window._fullscreenHandlers[event];
        }
      });
      window._fullscreenHandlers = null;
      console.log('ScaleHelper: 清理全屏变化监听器');
    }
    
    // 清理防抖定时器
    if (window._resizeDebounce) {
      clearTimeout(window._resizeDebounce);
      window._resizeDebounce = null;
    }
    
    console.log('ScaleHelper: 事件监听器清理完成');
  }
  
  // 8. 其他辅助函数可以保持原样
  function resetScaling(element) {
	if (element) {
	  element.style.transform = '';
	  element.style.transformOrigin = '';
	  element.style.transition = '';
	  element.style.marginLeft = '';
	  element.style.marginTop = '';
	}
	// 恢复滚动 - 统一使用overflow属性
	document.body.style.overflow = '';
  }
  
  // 添加判断当前页面是否应该排除缩放的函数
  function shouldExcludeCurrentPage() {
    // 如果currentConfig中有excludePages配置，检查当前页面是否应该排除
    if (currentConfig.excludePages && Array.isArray(currentConfig.excludePages)) {
      const currentPath = window.location.pathname;
      return currentConfig.excludePages.some(page => {
        if (typeof page === 'string') {
          return currentPath === page;
        } else if (page instanceof RegExp) {
          return page.test(currentPath);
        }
        return false;
      });
    }
    return false;
  }
  
  // 如果想随时手动调用、切换等，可以暴露接口
  export {
	setConfig,
	updateScaleFactor,
	resetScaling,
	removeEventListeners,
	ScaleStrategy
  }