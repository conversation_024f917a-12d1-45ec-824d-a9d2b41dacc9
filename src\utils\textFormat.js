/**
 * 格式化文本，将【】方括号中的内容高亮显示
 * @param {string} text 需要格式化的文本
 * @param {string} color 高亮颜色 (默认为 #18AD25)
 * @returns {string} 格式化后的HTML文本
 */
export const formatBracketText = (text, color = '#18AD25') => {
  if (!text) return '';
  
  // 高亮【】、( )、（ ）内容
  let result = text.replace(/【([^】]+)】/g, `<span style="color: ${color}">【$1】</span>`);
  result = result.replace(/\(([^ -\u007F\)]+|[^\)]+)\)/g, `<span style="color: ${color}">($1)</span>`); // 英文括号
  result = result.replace(/（([^）]+)）/g, `<span style="color: ${color}">（$1）</span>`); // 中文括号
  return result;
};

/**
 * 创建一个v-html指令可以安全使用的方法
 * @param {string} text 需要格式化的文本
 * @param {string} color 高亮颜色 (默认为 #18AD25)
 * @returns {string} 格式化后的HTML文本 
 */
export const formatTextForHTML = (text, color = '#18AD25') => {
  return formatBracketText(text, color);
}; 