# 友盟埋点系统

本项目使用友盟统计来进行用户行为追踪。友盟统计是一个功能全面的数据分析平台，可以对用户行为进行深入分析。

## 使用说明

### 全局API (通过this.$umeng)

在Vue组件中可以直接通过以下方式使用：

```js
// 页面访问统计
this.$umeng.trackPageview('/page-path');

// 事件统计
this.$umeng.trackEvent('类别', '操作', '标签', '值', '节点ID');

// 设置用户变量
this.$umeng.setCustomVar('变量名', '变量值', 1);
```

### Composition API (通过useUmeng)

在Vue 3的组合式API中使用：

```js
import { useUmeng } from '@/utils/umeng/hook';

// 在setup中
const umeng = useUmeng();

// 页面访问统计
umeng.trackPageview('/page-path');

// 事件统计
umeng.trackEvent('类别', '操作', '标签');
```

### 模块化埋点

项目实现了模块化的埋点方案，例如视频解析页面：

```js
import { useUmeng } from '@/utils/umeng/hook';
import { createParsingAnalytics } from '@/utils/umeng/modules/parsing';

// 在setup中
const umeng = useUmeng();
const parsingAnalytics = createParsingAnalytics(umeng);

// 调用特定页面的埋点方法
parsingAnalytics.trackParseButtonClick();
parsingAnalytics.trackDownloadVideo();
```

#### 音视频上传组件埋点

音视频上传组件埋点可用于跟踪用户上传行为：

```js
import { useUmeng } from '@/utils/umeng/hook';
import { createAudioUploadAnalytics } from '@/utils/umeng/modules/audioUpload';

// 在setup中
const umeng = useUmeng();
const audioUploadAnalytics = createAudioUploadAnalytics(umeng);

// 调用上传组件埋点方法
audioUploadAnalytics.trackFileSelect('video', '去水印'); // 参数: 文件类型, 来源
audioUploadAnalytics.trackUploadSuccess('video', 10, '去水印'); // 参数: 文件类型, 文件大小(MB), 来源
audioUploadAnalytics.trackFileRemove('video', '去水印'); // 参数: 文件类型, 来源
```

#### 去水印功能埋点

去水印功能埋点可用于跟踪视频处理行为：

```js
import { useUmeng } from '@/utils/umeng/hook';
import { createWatermarkAnalytics } from '@/utils/umeng/modules/watermark';

// 在setup中
const umeng = useUmeng();
const watermarkAnalytics = createWatermarkAnalytics(umeng);

// 页面访问记录
watermarkAnalytics.trackWatermarkPageView();

// 调用去水印埋点方法
watermarkAnalytics.trackUploadTypeSelect('本地文件'); // 参数: 上传类型
watermarkAnalytics.trackWatermarkStart('本地'); // 参数: 处理来源
watermarkAnalytics.trackWatermarkSuccess();
watermarkAnalytics.trackCopyTitle();
watermarkAnalytics.trackDownloadResult();
```

### 路由自动埋点

本项目已集成路由自动埋点功能，每次路由切换时会自动记录PV：

```js
// 已在router/index.js中配置，无需额外代码
// 路由切换时会自动记录以下信息：
// 1. 页面URL (路由路径)
// 2. 来源页面 (上一个路由路径)
// 3. 路由名称 (作为自定义变量)
// 4. 路由参数 (作为自定义变量，JSON格式)
```

如需手动使用路由埋点模块：

```js
import { useUmeng } from '@/utils/umeng/hook';
import { createRouterAnalytics } from '@/utils/umeng/modules/router';

const umeng = useUmeng();
const routerAnalytics = createRouterAnalytics(umeng);

// 手动记录路由变化
routerAnalytics.trackRouteChange(to, from);
```

## 埋点规范

1. 页面PV：每个页面应在加载完成时记录PV
2. 关键行为：所有重要用户交互都应该进行埋点
3. 命名规范：
   - 类别名：使用产品模块名，如"视频解析"
   - 操作名：使用动作+对象，如"点击解析按钮"

## 事件分类

1. 页面事件：页面访问、页面停留时间
2. 点击事件：按钮点击、链接点击
3. 表单事件：提交、字段变更
4. 业务事件：业务流程中的关键节点

## 如何添加新的埋点点位

1. 在 `src/utils/umeng/modules/` 目录下创建对应模块的埋点文件
2. 导入使用，见以上示例

## 注意事项

1. 确保埋点操作不影响用户体验和页面性能
2. 埋点操作应当处理可能的异常，不要因埋点失败影响业务流程
3. 敏感信息不要通过埋点发送 