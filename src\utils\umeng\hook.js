import { getCurrentInstance } from 'vue';
import { useloginStore } from '@/stores/login'; // 使用项目已有的登录store
import { useUmengStore } from './pinia'; // 导入友盟Pinia Store

/**
 * 友盟统计Hook
 */
export function useUmeng() {
  // 获取当前组件实例
  const instance = getCurrentInstance();
  const umeng = instance?.appContext.config.globalProperties.$umeng || window.$umeng;
  
  // 获取友盟Store
  const umengStore = useUmengStore();
  
  /**
   * 获取最新的用户ID
   * 在每次调用埋点函数时获取，确保使用最新的登录状态
   */
  const getCurrentUserId = () => {
    const loginStore = useloginStore();
    return loginStore.userId || '';
  };
  
  /**
   * 页面浏览埋点增强版(自动附加用户信息)
   */
  const trackPageview = (pageUrl, referrer = '') => {
    // 检查是否启用埋点
    if (!umengStore.isEnabled) return;
    
    // 获取最新的用户ID
    const currentUserId = getCurrentUserId();
    
    // 如果有用户信息，自动设置用户变量
    if (currentUserId) {
      umeng.setCustomVar('用户ID', currentUserId);
    }
    
    // 调用原生API
    umeng.trackPageview(pageUrl, referrer);
    
    // 记录到Store
    umengStore.recordPageview(pageUrl);
    
    // 记录埋点事件
    umengStore.recordEvent({
      type: 'pageview',
      pageUrl,
      referrer
    });
  };
  
  /**
   * 事件埋点增强版(自动附加用户信息)
   */
  const trackEvent = (category, action, label = '', value = '', nodeId = '') => {
    // 检查是否启用埋点
    if (!umengStore.isEnabled) return;
    
    // 获取最新的用户ID
    const currentUserId = getCurrentUserId();
    
    // 如果有用户ID，自动追加到label
    const enhancedLabel = currentUserId ? `${label ? label + '_' : ''}uid_${currentUserId}` : label;
    
    // 调用原生API
    umeng.trackEvent(category, action, enhancedLabel, value, nodeId);
    
    // 记录埋点事件
    umengStore.recordEvent({
      type: 'event',
      category,
      action,
      label: enhancedLabel,
      value,
      nodeId
    });
  };
  
  /**
   * 自定义变量设置
   */
  const setCustomVar = (name, value, time = 1) => {
    // 检查是否启用埋点
    if (!umengStore.isEnabled) return;
    
    // 调用原生API
    umeng.setCustomVar(name, value, time);
    
    // 记录埋点事件
    umengStore.recordEvent({
      type: 'customVar',
      name,
      value,
      time
    });
  };
  
  return {
    trackPageview,
    trackEvent,
    setCustomVar,
    
    // 获取埋点状态和控制方法
    getUmengState: () => ({
      isEnabled: umengStore.isEnabled,
      debug: umengStore.debug,
      recentEvents: umengStore.recentEvents
    }),
    
    // 启用/禁用埋点
    enableTracking: (status = true) => umengStore.setEnabled(status),
    
    // 获取用户ID
    getUserId: () => getCurrentUserId()
  };
} 