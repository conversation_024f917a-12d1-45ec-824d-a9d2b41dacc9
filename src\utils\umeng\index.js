// 友盟埋点插件
import { useUmengStore } from './pinia';

const UmengPlugin = {
  install(app, options = {}) {
    const { siteId, debug = false } = options;
    
    if (!siteId) {
      console.error('友盟统计需要提供siteId');
      return;
    }
    
    // 全局变量初始化
    window._czc = window._czc || [];
    
    // 创建统计脚本
    const script = document.createElement('script');
    script.src = `https://s9.cnzz.com/z_stat.php?id=${siteId}&web_id=${siteId}`;
    script.async = true;
    document.head.appendChild(script);
    
    // 初始化Pinia Store
    const umengStore = useUmengStore();
    umengStore.setSiteId(siteId);
    umengStore.setDebugMode(debug);
    
    // 注册全局属性
    const umengTracker = {
      /**
       * 页面访问统计
       * @param {string} pageUrl 页面URL(相对路径，以/开头)
       * @param {string} referrer 来源页面
       */
      trackPageview(pageUrl, referrer) {
        if (!umengStore.isEnabled) return;
        
        if (typeof pageUrl !== 'string' || !pageUrl.startsWith('/')) {
          console.warn('友盟页面统计URL应以/开头');
          return;
        }
        
        window._czc.push(['_trackPageview', pageUrl, referrer]);
        if (debug) console.log('友盟页面统计', pageUrl, referrer);
        
        // 记录到Store
        umengStore.recordPageview(pageUrl);
      },
      
      /**
       * 事件统计
       * @param {string} category 事件类别
       * @param {string} action 事件操作
       * @param {string} label 事件标签
       * @param {number} value 事件值
       * @param {string} nodeId 节点ID
       */
      trackEvent(category, action, label = '', value = '', nodeId = '') {
        if (!umengStore.isEnabled) return;
        
        window._czc.push(['_trackEvent', category, action, label, value, nodeId]);
        if (debug) console.log('友盟事件统计', category, action, label, value, nodeId);
        
        // 记录到Store
        umengStore.recordEvent({
          type: 'event',
          category,
          action,
          label,
          value,
          nodeId
        });
      },
      
      /**
       * 设置自定义用户变量
       * @param {string} name 变量名
       * @param {string} value 变量值
       * @param {number} time 有效时长：1=长期,0=当前页面,2=当前会话,或自定义秒数
       */
      setCustomVar(name, value, time = 1) {
        if (!umengStore.isEnabled) return;
        
        window._czc.push(['_setCustomVar', name, value, time]);
        if (debug) console.log('友盟自定义变量', name, value, time);
        
        // 记录到Store
        umengStore.recordEvent({
          type: 'customVar',
          name,
          value,
          time
        });
      },
      
      /**
       * 启用/禁用埋点
       * @param {boolean} status 是否启用
       */
      enableTracking(status = true) {
        umengStore.setEnabled(status);
        if (debug) console.log('友盟埋点' + (status ? '已启用' : '已禁用'));
      },
      
      /**
       * 获取埋点状态
       * @returns {Object} 埋点状态
       */
      getTrackingState() {
        return {
          isEnabled: umengStore.isEnabled,
          siteId: umengStore.siteId,
          debug: umengStore.debug,
          recentEvents: umengStore.recentEvents
        };
      }
    };
    
    // 注册到Vue全局属性
    app.config.globalProperties.$umeng = umengTracker;
    
    // 挂载到window，便于在非组件环境使用
    window.$umeng = umengTracker;
  }
};

// 导出主插件和相关模块
export { useUmengStore } from './pinia';
export { useUmeng } from './hook';
export { createRouterAnalytics } from './modules/router';
export { createParsingAnalytics } from './modules/parsing';
export { createAudioUploadAnalytics } from './modules/audioUpload';
export { createWatermarkAnalytics } from './modules/watermark';
export default UmengPlugin; 