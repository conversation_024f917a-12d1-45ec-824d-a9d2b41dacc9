/**
 * 音视频上传组件埋点
 * @param {Object} tracker 埋点工具实例
 */
export function createAudioUploadAnalytics(tracker) {
  return {
    // 文件选择
    trackFileSelect(fileType, source) {
      tracker.trackEvent('文件上传', '选择文件', `类型_${fileType}_来源_${source}`);
    },
    
    // 上传成功
    trackUploadSuccess(fileType, fileSize, source) {
      tracker.trackEvent('文件上传', '上传成功', `类型_${fileType}_大小_${fileSize}_来源_${source}`);
    },
    
    // 上传失败
    trackUploadError(fileType, errorMessage, source) {
      tracker.trackEvent('文件上传', '上传失败', `类型_${fileType}_错误_${errorMessage}_来源_${source}`);
    },
    
    // 删除文件
    trackFileRemove(fileType, source) {
      tracker.trackEvent('文件上传', '删除文件', `类型_${fileType}_来源_${source}`);
    },
    
    // 重新上传
    trackReupload(fileType, source) {
      tracker.trackEvent('文件上传', '重新上传', `类型_${fileType}_来源_${source}`);
    }
  };
} 