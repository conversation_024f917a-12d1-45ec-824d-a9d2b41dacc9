/**
 * 视频解析页面埋点
 * @param {Object} tracker 埋点工具实例
 */
export function createParsingAnalytics(tracker) {
  return {
    // 页面访问
    trackParsingPageView() {
      tracker.trackPageview('/video-parsing');
    },
    
    // 解析按钮点击
    trackParseButtonClick() {
      tracker.trackEvent('视频解析', '点击解析按钮', '一键解析视频');
    },
    
    // 复制标题
    trackCopyTitle() {
      tracker.trackEvent('视频解析', '复制标题');
    },
    
    // 下载视频
    trackDownloadVideo() {
      tracker.trackEvent('视频解析', '下载视频');
    },
    
    // 下载封面
    trackDownloadCover() {
      tracker.trackEvent('视频解析', '下载封面');
    }
  };
} 