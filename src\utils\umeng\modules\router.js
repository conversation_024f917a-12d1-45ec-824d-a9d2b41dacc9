/**
 * 路由埋点模块
 * @param {Object} tracker 埋点工具实例
 */
export function createRouterAnalytics(tracker) {
  return {
    /**
     * 记录路由变化
     * @param {Object} to 目标路由
     * @param {Object} from 来源路由
     */
    trackRouteChange(to, from) {
      // 构建页面路径 - 使用路由路径作为页面URL
      const pageUrl = to.path || '/';
      
      // 构建来源页面 - 如果存在来源路由
      const referrer = from?.path || '';
      
      // 调用页面访问统计
      tracker.trackPageview(pageUrl, referrer);
      
      // 记录额外的路由信息(如路由名称)
      if (to.name) {
        tracker.setCustomVar('路由名称', to.name, 0); // 0表示仅当前页面有效
      }
      
      // 记录额外的查询参数
      if (to.query && Object.keys(to.query).length > 0) {
        tracker.setCustomVar('路由参数', JSON.stringify(to.query), 0);
      }
    }
  };
} 