/**
 * 声音克隆功能埋点
 * @param {Object} tracker 埋点工具实例
 */
export function createVoiceCloneAnalytics(tracker) {
  return {
    /**
     * 声音克隆激活成功埋点
     * @param {boolean} isPaid 是否付费激活
     * @param {string} voiceName 音色名称
     * @param {number} paymentAmount 支付金额（付费时）
     */
    trackVoiceCloneActivateSuccess(isPaid, voiceName = '默认音色', paymentAmount = 0) {
      const label = isPaid 
        ? `克隆声音_激活成功_已付款_${paymentAmount}元_${voiceName}` 
        : `克隆声音_激活成功_免费使用_${voiceName}`;
      
      tracker.trackEvent('声音克隆', '激活成功', label);
      
      // 设置自定义变量记录激活方式
      tracker.setCustomVar('激活方式', isPaid ? '付费' : '免费', 0);
      
      // 如果是付费，记录支付金额
      if (isPaid && paymentAmount > 0) {
        tracker.setCustomVar('支付金额', String(paymentAmount), 0);
      }
    }
  };
} 