/**
 * 去水印功能埋点
 * @param {Object} tracker 埋点工具实例
 */
export function createWatermarkAnalytics(tracker) {
  return {
    // 页面访问
    trackWatermarkPageView() {
      tracker.trackPageview('/watermark');
    },
    
    // 选择上传方式
    trackUploadTypeSelect(type) {
      tracker.trackEvent('去水印', '选择上传方式', type);
    },
    
    // 开始去水印处理
    trackWatermarkStart(source) {
      tracker.trackEvent('去水印', '开始去水印', `来源_${source}`);
    },
    
    // 去水印成功
    trackWatermarkSuccess() {
      tracker.trackEvent('去水印', '去水印成功');
    },
    
    // 去水印失败
    trackWatermarkFail(errorMessage) {
      tracker.trackEvent('去水印', '去水印失败', `错误_${errorMessage}`);
    },
    
    // 复制标题
    trackCopyTitle() {
      tracker.trackEvent('去水印', '复制标题');
    },
    
    // 下载去水印结果
    trackDownloadResult() {
      tracker.trackEvent('去水印', '下载结果');
    }
  };
} 