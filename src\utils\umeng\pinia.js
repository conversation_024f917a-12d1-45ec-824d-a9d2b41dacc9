import { defineStore } from 'pinia';

/**
 * 友盟埋点Pinia Store
 * 用于全局存储和管理埋点相关的状态和操作
 */
export const useUmengStore = defineStore('umeng', {
  state: () => ({
    trackedEvents: [], // 记录已经发送的埋点事件，方便调试
    isEnabled: true, // 是否启用埋点
    lastPageview: null, // 最后一次页面访问记录
    siteId: 1281415790, // 站点ID
    debug: false, // 是否开启调试模式
  }),

  getters: {
    // 获取最近的埋点事件
    recentEvents: (state) => state.trackedEvents.slice(-10),
    
    // 获取特定类型的埋点事件
    getEventsByCategory: (state) => (category) => {
      return state.trackedEvents.filter(event => event.category === category);
    },
  },

  actions: {
    /**
     * 记录埋点事件
     * @param {Object} event 埋点事件对象
     */
    recordEvent(event) {
      // 添加时间戳
      event.timestamp = new Date().toISOString();
      this.trackedEvents.push(event);
      
      // 仅保留最近100条记录
      if (this.trackedEvents.length > 100) {
        this.trackedEvents.shift();
      }
    },
    
    /**
     * 清空埋点记录
     */
    clearEvents() {
      this.trackedEvents = [];
    },
    
    /**
     * 设置站点ID
     * @param {string} siteId 友盟站点ID
     */
    setSiteId(siteId) {
      this.siteId = siteId;
    },
    
    /**
     * 启用或禁用埋点
     * @param {boolean} status 是否启用
     */
    setEnabled(status) {
      this.isEnabled = status;
    },
    
    /**
     * 设置调试模式
     * @param {boolean} status 是否开启调试
     */
    setDebugMode(status) {
      this.debug = status;
    },
    
    /**
     * 记录页面访问
     * @param {string} pageUrl 页面URL
     */
    recordPageview(pageUrl) {
      this.lastPageview = {
        url: pageUrl,
        timestamp: new Date().toISOString()
      };
    }
  }
}); 