/**
 * 版本更新检测工具
 * 通过定期检查version.json文件来检测应用是否有新版本
 * 已禁用更新检测功能
 */

const VERSION_CHECK_INTERVAL = 10 * 60 * 1000; // 10分钟检查一次
const VERSION_STORAGE_KEY = 'app_current_version';

// 判断是否为开发环境
const isDevelopment = import.meta.env.DEV;



// 仅在非开发环境下初始化版本
if (!isDevelopment) {
  // 初始版本设置为当前时间戳，确保第一次加载时能正确比较
  const initialVersion = localStorage.getItem(VERSION_STORAGE_KEY) || Date.now().toString();
  localStorage.setItem(VERSION_STORAGE_KEY, initialVersion);
}

/**
 * 创建更新提示元素 (已禁用)
 */
function createUpdateNotification() {
  // 已禁用更新通知
  return;
}

/**
 * 检查是否有新版本 (已禁用)
 */
async function checkForUpdates() {
  // 已禁用版本检查
  return;
}

/**
 * 初始化版本检测 (已禁用)
 */
export function initVersionCheck() {
  // 已禁用版本检测
  return;
}

export default {
  initVersionCheck
}; 