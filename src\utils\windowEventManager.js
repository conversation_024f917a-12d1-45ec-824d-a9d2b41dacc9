/**
 * 统一的窗口事件管理器
 * 解决多个缩放工具和组件重复监听窗口事件导致的冲突问题
 * @version 1.0.0
 */

class WindowEventManager {
  constructor() {
    if (WindowEventManager.instance) {
      return WindowEventManager.instance;
    }
    
    // 存储所有注册的事件监听器
    this.listeners = new Map();
    // 防抖定时器
    this.debounceTimers = new Map();
    // 默认防抖延迟
    this.defaultDebounceDelay = 100;
    
    WindowEventManager.instance = this;
  }

  /**
   * 注册事件监听器
   * @param {string} event - 事件类型 (resize, orientationchange, fullscreenchange等)
   * @param {string} key - 监听器唯一标识
   * @param {Function} callback - 回调函数
   * @param {number} debounceDelay - 防抖延迟，默认100ms
   */
  addEventListener(event, key, callback, debounceDelay = this.defaultDebounceDelay) {
    // 如果已经存在相同key的监听器，先移除
    this.removeEventListener(event, key);
    
    // 创建防抖包装函数
    const debouncedCallback = this.createDebouncedCallback(
      event,
      key,
      callback,
      debounceDelay
    );
    
    // 存储监听器信息
    const listenerKey = `${event}_${key}`;
    this.listeners.set(listenerKey, {
      event,
      key,
      callback: debouncedCallback,
      originalCallback: callback
    });
    
    // 添加实际的事件监听器
    window.addEventListener(event, debouncedCallback);
    
    console.log(`WindowEventManager: 注册事件监听器 ${event}:${key}`);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件类型
   * @param {string} key - 监听器唯一标识
   */
  removeEventListener(event, key) {
    const listenerKey = `${event}_${key}`;
    const listener = this.listeners.get(listenerKey);
    
    if (listener) {
      // 清除防抖定时器
      this.clearDebounceTimer(event, key);
      
      // 移除实际的事件监听器
      window.removeEventListener(event, listener.callback);
      
      // 从存储中删除
      this.listeners.delete(listenerKey);
      
      console.log(`WindowEventManager: 移除事件监听器 ${event}:${key}`);
    }
  }

  /**
   * 移除指定事件类型的所有监听器
   * @param {string} event - 事件类型
   */
  removeAllEventListeners(event) {
    const keysToRemove = [];
    
    this.listeners.forEach((listener, listenerKey) => {
      if (listener.event === event) {
        keysToRemove.push(listener.key);
      }
    });
    
    keysToRemove.forEach(key => {
      this.removeEventListener(event, key);
    });
  }

  /**
   * 清理所有事件监听器
   */
  clearAll() {
    this.listeners.forEach((listener, listenerKey) => {
      // 清除防抖定时器
      this.clearDebounceTimer(listener.event, listener.key);
      
      // 移除实际的事件监听器
      window.removeEventListener(listener.event, listener.callback);
    });
    
    this.listeners.clear();
    this.debounceTimers.clear();
    
    console.log('WindowEventManager: 清理所有事件监听器');
  }

  /**
   * 创建防抖回调函数
   * @param {string} event - 事件类型
   * @param {string} key - 监听器标识
   * @param {Function} callback - 原始回调函数
   * @param {number} delay - 防抖延迟
   * @returns {Function} 防抖后的回调函数
   */
  createDebouncedCallback(event, key, callback, delay) {
    return (...args) => {
      // 清除之前的定时器
      this.clearDebounceTimer(event, key);
      
      // 创建新的定时器
      const timerKey = `${event}_${key}`;
      const timer = setTimeout(() => {
        callback(...args);
        this.debounceTimers.delete(timerKey);
      }, delay);
      
      this.debounceTimers.set(timerKey, timer);
    };
  }

  /**
   * 清除防抖定时器
   * @param {string} event - 事件类型
   * @param {string} key - 监听器标识
   */
  clearDebounceTimer(event, key) {
    const timerKey = `${event}_${key}`;
    const timer = this.debounceTimers.get(timerKey);
    
    if (timer) {
      clearTimeout(timer);
      this.debounceTimers.delete(timerKey);
    }
  }

  /**
   * 获取当前注册的监听器信息
   * @returns {Array} 监听器列表
   */
  getListenerInfo() {
    const info = [];
    this.listeners.forEach((listener, listenerKey) => {
      info.push({
        key: listenerKey,
        event: listener.event,
        identifier: listener.key
      });
    });
    return info;
  }
}

// 创建并导出单例实例
const windowEventManager = new WindowEventManager();

export default windowEventManager;

// 导出便捷方法
export const {
  addEventListener,
  removeEventListener,
  removeAllEventListeners,
  clearAll,
  getListenerInfo
} = windowEventManager; 