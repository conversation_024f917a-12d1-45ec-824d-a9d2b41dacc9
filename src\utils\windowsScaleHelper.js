/**
 * 增强版全局等比例缩放辅助工具
 * 支持整页缩放保持UI界面不变形，并允许特定页面不受全局缩放影响
 * @version 2.2.0
 */

// 检测当前操作系统是否为Mac
const isMacOS = /Mac|iPod|iPhone|iPad/.test(navigator.platform);

// 缩放策略常量
const ScaleStrategy = {
  FIT_WIDTH: 'fit-width',   // 宽度适应，高度可能需要滚动
  CONTAIN: 'contain',       // 完整显示，可能有空白
  COVER: 'cover',           // 填满屏幕，可能截断内容
  FIT_SCREEN: 'fit-screen'  // 智能适应屏幕，消除底部空白
};
const app = document.getElementById('app');
const screenWidth = window.innerWidth;
const screenHeight = window.innerHeight;
const aspectRatio = screenHeight / screenWidth;
let rate=953/1920

// 默认配置
const defaultConfig = {
  // 基准屏幕尺寸 - 以设计稿为基准
  baseWidth: screenWidth>1920?'100%':1920,
  // Mac系统使用固定高度，Windows使用动态计算高度
  baseHeight: isMacOS ? 953 : 953 *(aspectRatio/rate),
  
  // 默认缩放策略 - Mac系统使用CONTAIN策略以确保完整显示
  strategy: isMacOS ? ScaleStrategy.CONTAIN : ScaleStrategy.FIT_SCREEN,
  
  // 动画过渡时间(ms)，0表示无动画
  transition: 200,
  
  // 排除的路由路径正则表达式列表
  excludedRoutes: [],
  
  // 排除的元素选择器列表
  excludedSelectors: [],
  
  // 缩放目标元素，默认为#app
  targetSelector: '#app',
  
  // 是否启用调试日志
  debug: false,
  
  // 在高DPI屏幕上是否应用更佳的字体平滑
  improveTextRendering: true,
  
  // 是否消除底部空白
  removeBottomSpace: !isMacOS, // Mac系统不移除底部空白，保留完整内容
  
  // 是否保持原始高度比例
  maintainAspectRatio: true,
  
  // 是否显示垂直滚动条 - Mac系统显示垂直滚动条以确保内容可见
  showVerticalScrollbar: isMacOS
};

// 当前实际运行时配置
let currentConfig = { ...defaultConfig };

// 内部状态
let isScalingActive = true;
let currentRoute = '';
let debounceTimer = null;
let scaleTransformElement = null;
let styleElement = null;

/**
 * 设置缩放配置
 * @param {Object} config - 配置对象，可以部分覆盖默认配置
 */
function setConfig(config = {}) {
  // 合并配置，保留Mac特定配置
  if (isMacOS && !config.hasOwnProperty('strategy')) {
    config.strategy = ScaleStrategy.CONTAIN;
  }
  
  if (isMacOS && !config.hasOwnProperty('showVerticalScrollbar')) {
    config.showVerticalScrollbar = true;
  }
  
  if (isMacOS && !config.hasOwnProperty('removeBottomSpace')) {
    config.removeBottomSpace = false;
  }
  
  currentConfig = { ...currentConfig, ...config };
  
  // 重新应用缩放计算
  if (typeof window !== 'undefined') {
    updateScaleFactor();
  }
  
  // 日志配置更新
  if (currentConfig.debug) {
    console.debug('ScaleHelper: Configuration updated', currentConfig);
    if (isMacOS) {
      console.debug('ScaleHelper: Running on macOS with specific optimizations');
    }
  }
  
  return currentConfig; // 返回当前配置，便于链式调用
}

/**
 * 计算当前页面是否应该排除在缩放之外
 * @returns {boolean} 如果应该排除返回true
 */
function shouldExcludeCurrentPage() {
  // 检查当前路由是否在排除列表中
  if (currentRoute && currentConfig.excludedRoutes.length > 0) {
    for (const routePattern of currentConfig.excludedRoutes) {
      if (typeof routePattern === 'string') {
        if (currentRoute === routePattern) return true;
      } else if (routePattern instanceof RegExp) {
        if (routePattern.test(currentRoute)) return true;
      }
    }
  }
  
  // 检查是否存在排除的选择器
  if (currentConfig.excludedSelectors.length > 0) {
    for (const selector of currentConfig.excludedSelectors) {
      if (document.querySelector(selector)) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * 计算并应用缩放比例
 * @param {boolean} force - 是否强制更新，忽略排除规则
 */
function updateScaleFactor(force = false) {
  // 如果缩放被禁用，则不执行
  if (!isScalingActive && !force) return;
  
  // 获取目标元素
  const targetElement = document.querySelector(currentConfig.targetSelector);
  if (!targetElement) {
    if (currentConfig.debug) {
      console.warn(`ScaleHelper: Target element "${currentConfig.targetSelector}" not found`);
    }
    return;
  }
  
  // 检查当前页面是否应该排除
  if (shouldExcludeCurrentPage() && !force) {
    // 如果应该排除，恢复正常显示
    resetScaling(targetElement);
    if (currentConfig.debug) {
      console.debug('ScaleHelper: Scaling disabled for current page/element');
    }
    return;
  }
  
  // 计算缩放比例
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  
  // 根据不同策略计算缩放比例
  let scale;
  
  switch (currentConfig.strategy) {
    case ScaleStrategy.CONTAIN:
      // 取最小值，确保内容完全可见
      scale = Math.min(
        windowWidth / currentConfig.baseWidth,
        windowHeight / currentConfig.baseHeight
      );
      break;
    
    case ScaleStrategy.COVER:
      // 取最大值，确保填满屏幕
      scale = Math.max(
        windowWidth / currentConfig.baseWidth,
        windowHeight / currentConfig.baseHeight
      );
      break;
    
    case ScaleStrategy.FIT_SCREEN:
      // 智能适应屏幕，尽量填满并避免底部空白
      // 使用宽度比例确保横向铺满
      scale = windowWidth / currentConfig.baseWidth;
      
      // 如果内容高度超出屏幕且设置了消除底部空白
      if (currentConfig.removeBottomSpace) {
        const contentHeight = currentConfig.baseHeight * scale;
        
        // 如果内容高度超出屏幕，并且没有要求保持纵横比
        if (contentHeight > windowHeight && !currentConfig.maintainAspectRatio) {
          // 直接缩放到屏幕高度，可能会在宽高比与设计不符的情况下稍微改变UI比例
          scale = windowHeight / currentConfig.baseHeight;
        }
      }
      break;
    
    case ScaleStrategy.FIT_WIDTH:
    default:
      // 使用宽度比例，可能需要垂直滚动
      scale = windowWidth / currentConfig.baseWidth;
      break;
  }
  
  // 计算居中定位偏移
  let offsetX = 0;
  let offsetY = 0;
  
  // 计算缩放后的尺寸
  const scaledWidth = currentConfig.baseWidth * scale;
  const scaledHeight = currentConfig.baseHeight * scale;
  
  if (currentConfig.strategy === ScaleStrategy.CONTAIN || 
      currentConfig.strategy === ScaleStrategy.COVER) {
    // 计算居中偏移
    offsetX = (windowWidth - scaledWidth) / 2;
    offsetY = (windowHeight - scaledHeight) / 2;
  } else if (currentConfig.strategy === ScaleStrategy.FIT_SCREEN) {
    // 仅在宽度方向居中，高度顶部对齐，避免底部空白
    offsetX = (windowWidth - scaledWidth) / 2;
    offsetY = 0; // 顶部对齐
    
    // 如果启用了底部空间调整且内容不足以填满屏幕
    if (currentConfig.removeBottomSpace && scaledHeight < windowHeight) {
      if (currentConfig.maintainAspectRatio) {
        // 在保持比例的情况下，对内容进行垂直居中
        offsetY = (windowHeight - scaledHeight) / 2;
      }
    }
  }
  
  // 应用缩放变换
  let transform = `scale(${scale}) translateZ(0)`;
  const transformOrigin = '0 0';

  // 使用当前实际的窗口高度而不是页面加载时的静态值
  const currentScreenHeight = window.innerHeight;
  if (currentScreenHeight < 953) {
    transform = `scale(${scale}) translateZ(0)`;
  } else {
    transform = `translateZ(0)`; // 不缩放
  }

  // 应用过渡效果
  let transition = '';
  if (currentConfig.transition > 0) {
    transition = `transform ${currentConfig.transition}ms ease-out`;
  }
  
  // 设置样式
  targetElement.style.transform = transform;
  targetElement.style.transformOrigin = transformOrigin;
  targetElement.style.transition = transition;
  targetElement.style.marginLeft = `${offsetX}px`;
  targetElement.style.marginTop = `${offsetY}px`;
  
  // 保存元素引用，以便必要时重置
  scaleTransformElement = targetElement;
  
  // 处理滚动条设置
  document.body.style.overflowY = currentConfig.showVerticalScrollbar ? 'auto' : 'hidden';
  document.body.style.overflowX = 'hidden';
  
  // 如果应用FIT_SCREEN策略且启用了底部空间消除
  if (currentConfig.strategy === ScaleStrategy.FIT_SCREEN && currentConfig.removeBottomSpace) {
    // 计算实际缩放后的内容高度
    const totalHeight = scaledHeight + offsetY;
    
    // 如果缩放后的内容高度小于窗口高度，则扩展body到窗口高度
    if (totalHeight < windowHeight) {
      document.body.style.minHeight = `${windowHeight}px`;
    }
  }
  
  // Mac系统特殊处理，确保内容完全可见
  if (isMacOS) {
    document.body.style.height = 'auto';
    document.body.style.minHeight = '100%';
    
    // 为Mac设置额外的底部间距以避免内容被裁剪
    if (scaledHeight > windowHeight) {
      document.body.style.paddingBottom = '20px';
    }
  } else {
    // 设置HTML和BODY高度为100%确保铺满
    document.documentElement.style.height = '100%';
    document.body.style.height = '100%';
  }
  
  // 输出调试信息
  if (currentConfig.debug) {
    console.debug(`ScaleHelper: Window size: ${windowWidth}x${windowHeight}, Scale: ${scale.toFixed(4)}, Strategy: ${currentConfig.strategy}`);
    console.debug(`ScaleHelper: Scaled size: ${scaledWidth.toFixed(0)}x${scaledHeight.toFixed(0)}, Offset: ${offsetX.toFixed(0)},${offsetY.toFixed(0)}`);
    if (isMacOS) {
      console.debug('ScaleHelper: Using macOS specific settings');
    }
  }
}

/**
 * 重置元素缩放和样式
 * @param {HTMLElement} element - 需要重置的元素
 */
function resetScaling(element) {
  if (!element) {
    element = scaleTransformElement || document.querySelector(currentConfig.targetSelector);
    if (!element) return;
  }
  
  // 清除缩放相关样式
  element.style.transform = '';
  element.style.transformOrigin = '';
  element.style.transition = '';
  element.style.marginLeft = '';
  element.style.marginTop = '';
  
  // 更新内部状态
  scaleTransformElement = null;
}

/**
 * 启用缩放功能
 */
function enableScaling() {
  isScalingActive = true;
  updateScaleFactor();
  if (currentConfig.debug) {
    console.debug('ScaleHelper: Scaling enabled');
  }
}

/**
 * 禁用缩放功能
 */
function disableScaling() {
  isScalingActive = false;
  resetScaling();
  if (currentConfig.debug) {
    console.debug('ScaleHelper: Scaling disabled');
  }
}

/**
 * 设置当前路由路径，用于排除特定页面
 * @param {string} route - 当前路由路径
 */
function setCurrentRoute(route) {
  currentRoute = route;
  updateScaleFactor(); // 更新缩放以应用可能的排除规则
}

/**
 * Windows系统的缩放处理逻辑，可从scaleHelper.js直接调用
 * 无需参数，直接使用默认配置应用缩放
 */
function applyWindowsScaling() {
  // 直接调用更新缩放函数，强制更新
  updateScaleFactor(true);
}

/**
 * 初始化缩放系统
 * @param {Object} config - 初始配置
 */
function initializeScaling(config = {}) {
  // 设置初始配置
  setConfig(config);
  
  // 添加基础样式
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.textContent = `
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
      }
      ${currentConfig.targetSelector} {
        width: ${currentConfig.baseWidth}px;
        height: ${currentConfig.baseHeight}px;
        transform-origin: 0 0;
        position: relative;
      }
      ${currentConfig.improveTextRendering ? `
      * {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }` : ''}
      
      ${isMacOS ? `
      /* Mac特定样式 */
      body {
        overflow-y: auto !important;
      }` : ''}
    `;
    document.head.appendChild(styleElement);
  }
  
  // 先执行一次计算，设置初始缩放
  updateScaleFactor();
  
  // 监听窗口大小变化，动态更新缩放 - 使用统一的处理函数变量，方便清理
  const resizeHandler = function() {
    // 高效防抖处理
    if (debounceTimer) clearTimeout(debounceTimer);
    debounceTimer = setTimeout(function() {
      updateScaleFactor();
      debounceTimer = null;
    }, 100); // 防抖延迟
  };
  window.addEventListener('resize', resizeHandler);
  
  // 存储处理函数以便清理
  window._windowsScaleResizeHandler = resizeHandler;
  
  // 适应移动设备旋转
  const orientationHandler = function() {
    // 在下一帧更新缩放以适应新方向
    setTimeout(updateScaleFactor, 50);
  };
  window.addEventListener('orientationchange', orientationHandler);
  
  // 存储处理函数以便清理
  window._windowsScaleOrientationHandler = orientationHandler;
  
  if (currentConfig.debug) {
    console.debug('ScaleHelper: Initialized with config', currentConfig);
    if (isMacOS) {
      console.debug('ScaleHelper: Running on macOS with specific optimizations');
    }
  }
}

// 注释掉自动初始化，改为由App.vue统一控制
// 避免与App.vue的动态导入产生双重初始化冲突
/*
// 自动初始化
(function() {
  // 检查是否为浏览器环境
  if (typeof window !== 'undefined') {
    // 在DOM加载完成后初始化缩放
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        initializeScaling();
      });
    } else {
      initializeScaling();
    }
  }
})();
*/

/**
 * 清理事件监听器函数
 */
function removeEventListeners() {
  console.log('WindowsScaleHelper: 开始清理事件监听器');
  
  // 清理resize事件监听器
  if (window._windowsScaleResizeHandler) {
    window.removeEventListener('resize', window._windowsScaleResizeHandler);
    window._windowsScaleResizeHandler = null;
    console.log('WindowsScaleHelper: 清理resize监听器');
  }
  
  // 清理orientationchange事件监听器
  if (window._windowsScaleOrientationHandler) {
    window.removeEventListener('orientationchange', window._windowsScaleOrientationHandler);
    window._windowsScaleOrientationHandler = null;
    console.log('WindowsScaleHelper: 清理orientationchange监听器');
  }
  
  // 清理防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
  
  console.log('WindowsScaleHelper: 事件监听器清理完成');
}

// 导出接口
export {
  initializeScaling,
  updateScaleFactor,
  setConfig,
  enableScaling,
  disableScaling,
  setCurrentRoute,
  removeEventListeners,
  ScaleStrategy,
  isMacOS, // 导出Mac检测值，方便其他模块使用
  applyWindowsScaling // 导出Windows系统缩放处理函数
}; 