.editor-container {
    display: flex;
    height: 100vh;
    background: #f5f7fa;

    // 左侧菜单栏
    .left-menu {
        width: 80px;
        background: #fff;
        border-right: 1px solid #e4e7ed;

        .menu-item {
            height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #606266;
            font-size: 12px;

            &:hover {
                background: #f5f7fa;
            }

            &.active {
                color: #409EFF;
                background: #ecf5ff;
            }

            .icon {
                font-size: 24px;
                margin-bottom: 4px;
            }
        }
    }

    // 主要内容区域
    .main-content {
        flex: 1;
        padding: 20px;
        // padding-right: 40px;
        // margin-right: 20px;
        background: #fff;
        // 顶部操作栏
        .operation-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;

            .btn {
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;

                &.new {
                    background: #fff;
                    border: 1px solid #dcdfe6;
                    color: #606266;
                }

                &.export {
                    background: #67c23a;
                    color: #fff;
                    border: none;
                }
            }
        }

        // 工具栏
        .tools-bar {
            display: flex;
            gap: 15px;
            // margin-bottom: 20px;

            .tool-item {
                display: flex;
                align-items: center;
                padding: 8px 15px;
                background: #fff;
                border-radius: 4px;
                cursor: pointer;

                &.active {
                    // background: #fdf6ec;
                    color: #e6a23c;
                }

                .icon {
                    margin-right: 5px;
                }
            }
        }

        // 编辑区域
        .edit-area {
            display: flex;
            gap: 20px;

            // 左侧输入区
            .input-section {
                flex: 1;
                background: #fff;
                border-radius: 4px;
                padding: 20px;

                .text-input {
                    width: 100%;
                    min-height: 200px;
                    border: none;
                    resize: none;
                    outline: none;
                    font-size: 14px;
                    line-height: 1.6;

                    &::placeholder {
                        color: #c0c4cc;
                    }
                }

                .bottom-tools {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 10px;
                    color: #909399;
                    font-size: 12px;
                }
            }

            // 右侧预览区
            .preview-section {
                // flex: 1;
                background: #fff;
                border-radius: 4px;
                padding: 20px;

                .material-selector {
                    display: flex;
                    gap: 10px;
                    margin-bottom: 15px;

                    .select-item {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }
                }

                .preview-content {
                    font-size: 14px;
                    line-height: 1.8;
                    color: #303133;
                }
            }
        }
    }
}

// 右侧滑块
.slider-bar {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100px;
    background: #e4e7ed;
    border-radius: 2px;

    .slider-handle {
        width: 12px;
        height: 12px;
        background: #409EFF;
        border-radius: 50%;
        position: absolute;
        left: -4px;
        cursor: pointer;
    }
}