<template>
	<div class="content-creation-container">
		<!-- 使用Headbar组件替换原来的标题栏 -->
		<Headbar />

		<!-- 使用OperationBar组件替换原来的操作栏 -->
		<OperationBar @action="handleBarAction" />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<!-- 左侧区域 -->
			<div class="left-section">
				<!-- 左侧菜单 -->
				<LeftMenu />

				<!-- 工具区域 -->
				<div class="input-section">
					<!-- 工具栏 -->
					<div class="tools-wrapper">
						<div class="tools-container">
							<div class="tools-bar">
								<div v-for="tool in tools" :key="tool.id" class="tool-item"
									:class="{ active: activeTool === tool.id }" @click="handleToolClick(tool)">
									<img :src="(activeTool === tool.id || hoveredTool === tool.id) ? tool.activeImg : tool.img"
										:alt="tool.name" class="tool-icon" @mouseenter="hoveredTool = tool.id"
										@mouseleave="hoveredTool = null">
								</div>
							</div>
						</div>
					</div>

					<!-- 动态加载工具组件 -->
					<component :is="currentToolComponent" :is-uploading="isUploading" :is-extracting="isExtracting"
						:is-button-extracting="isButtonExtracting" :upload-file="uploadFile"
						:show-extract-result="showExtractResult" :extracted-video-url="extractedVideoUrl"
						@extract="handleExtract" @upload-file="handleFileChange" @cancel-upload="cancelUpload"
						@download-video="handleDownloadVideo" @add-to-space="handleAddToSpace"
						@extract-content="handleExtractContent" ref="previewPanel" :isVideoEditingPage="false" />
				</div>
			</div>

			<!-- 右侧区域使用已封装的组件 -->
			<div class="right-content">
				<PreviewPanel ref="previewPanel" v-model:title="titleText" v-model:content="previewContent" v-model:musicList="musicList"
					:is-extracting="isExtracting" :extracted-content="extractedContent" :isVideoEditingPage="false"
					@generate-video="handleGenerateVideo" @add-role="handleAddRole"
					@add-music="handleAddMusic" @add-video="handleAddVideo" @volume-change="handleVolumeChange" />
			</div>
		</div>

		<!-- 音乐素材弹窗 -->
		<MusicDialog v-model:visible="musicDialogVisible" :material-list="musicList" :previewContenttitle-icon="yinsu2Icon"
			@close="handleMusicDialogClose" @confirm="handleMusicDialogConfirm" @remove="handleMusicRemove"
			@toggle-play="handleMusicTogglePlay" />

		<!-- 视频素材弹窗 -->
		<MaterialDialog v-model:visible="videoDialogVisible" :material-list="videoList" type="视频"
			:title-icon="yinsu3Icon" @close="handleVideoDialogClose" @confirm="handleVideoDialogConfirm"
			@remove="handleVideoRemove" @toggle-play="handleVideoTogglePlay" @upload="handleMaterialUpload" />

		<!-- 在template末尾添加SubtitleDialog组件 -->
		<SubtitleDialog v-model="spaceDialogVisible" :folders="spaceFolders" :selected-folders="selectedSpaceFolders" :sourcePage="'content-creation'"
			@confirm="handleSpaceDialogConfirm" />
	</div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, reactive, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import LeftMenu from '@/views/Editor/components/common/LeftMenu.vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import OperationBar from '@/views/Editor/components/common/OperationBar.vue'
import PreviewPanel from '@/views/Editor/components/PreviewPanel.vue'
import MusicDialog from '@/components/MusicDialog.vue'
import MaterialDialog from '@/components/MaterialDialog.vue'
import SubtitleDialog from '@/components/SubtitleDialog/index.vue'
import { dubbing, callbackOss, extractFile, oneKeyParseVideo } from '@/api/dubbing'
import { parsingVideo } from "@/api/parsingVideo"
import { useMusicStore } from '@/stores/modules/musicStore' // 导入音乐存储
import { usePreviewStore } from '@/stores/previewStore'
import { useloginStore } from '@/stores/login' // 导入用户store

// 导入工具组件
import MaterialExtraction from '@/views/Editor/components/tools/MaterialExtraction.vue'
import AICreation from '@/views/Editor/components/tools/AICreation.vue'
import TextPolishing from '@/views/Editor/components/tools/TextPolishing.vue'

// 导入图标
import xuanzhongIcon from '@/assets/img/xuanzhong.png'
import weixuanzhongIcon from '@/assets/img/weixuanzhong.png'
import weixuanzhong1Icon from '@/assets/img/weixuanzhong1.png'
import weixuanzhong2Icon from '@/assets/img/weixunazhong2.png'
import chuangzuoIcon from '@/assets/img/chuangzuo.png'
import runseIcon from '@/assets/img/runse.png'
import yinsu2Icon from '@/assets/img/yinsu2.png'
import yinsu3Icon from '@/assets/img/yinsu3.png'

// 工具栏状态
const activeTool = ref(2)
const hoveredTool = ref(null)

// 工具数据
const tools = [
	{
		id: 2,
		name: 'AI创作',
		img: weixuanzhong1Icon,
		activeImg: chuangzuoIcon,
		component: AICreation
	},
	{
		id: 1,
		name: '素材提取',
		img: weixuanzhongIcon,
		activeImg: xuanzhongIcon,
		component: MaterialExtraction
	},
	{
		id: 3,
		name: '文案润色',
		img: weixuanzhong2Icon,
		activeImg: runseIcon,
		component: TextPolishing
	}
]

// 计算显示的工具组件
const currentToolComponent = computed(() => {
	const tool = tools.find(t => t.id === activeTool.value)
	return tool ? tool.component : MaterialExtraction
})

// 处理工具点击
const handleToolClick = (tool) => {
	activeTool.value = tool.id
}

// 文本输入相关
const inputText = ref('')
const fileInputRef = ref(null)
const isUploading = ref(false)
const options = ref({
	extractText: false,
	extractVideo: false
})

// 添加上传文件状态
const uploadFile = ref({
	name: '',
	size: 0,
	loaded: 0,
	percent: 0,
	type: ''
})

// 预览内容
const previewContent = ref('')
const titleText = ref('') // 标题输入框的值

// 添加路由实例
const router = useRouter()

// 获取音乐存储
const musicStore = useMusicStore()

// 获取预览内容存储
const previewStore = usePreviewStore()

// 获取用户store
const loginStore = useloginStore()

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}

// 视频提取相关
const showExtractResult = ref(false)
const extractedVideoUrl = ref('')
const extractedVideoTitle = ref('CHAOS LAB')
const videoProgress = ref(0)
const currentTime = ref('0:00')
const isButtonExtracting = ref(false)
const isExtracting = ref(false)
const isParsing = ref(false)
const parsedVideoUrl = ref('')
const uploadRequest = ref(null)

// 添加素材相关状态
const musicDialogVisible = ref(false)
const videoDialogVisible = ref(false)
const spaceDialogVisible = ref(false)

// 使用音乐存储的音乐列表
const musicList = computed(() => musicStore.musicList)

const videoList = ref([
	
])

const spaceFolders = ref([
	{ name: "默认收藏夹", count: 12 },
	{ name: "动漫素材", count: 5 },
	{ name: "历史素材", count: 10 },
])
const selectedSpaceFolders = ref(["默认收藏夹"])

// 添加音视频播放相关的状态
const audioPlayer = ref(null)  // 音频播放器引用
const isPlaying = ref(false)   // 播放状态

// 添加提取内容的状态
const extractedContent = ref([]);

// 组件卸载时清理
onUnmounted(() => {
	if (audioPlayer.value) {
		audioPlayer.value.pause()
		audioPlayer.value = null
	}
})

// 添加一个工具函数用于移除颜文字
const removeEmojisAndSpecialSymbols = (text) => {
	if (!text) return '';
	
	// 这个正则表达式会匹配emoji和大多数特殊符号
	// 保留中文、英文、数字、常用标点符号和基本排版符号
	return text.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[\u2600-\u27FF]|[\u2300-\u23FF]|[\u2B50-\u2BFF]|[\u231A-\u231B]|[\u23E9-\u23EC]|[\u23F0-\u23F3]|[\u23F8-\u23FA]|[\u25AA-\u25AB]|[\u25FB-\u25FE]|[\u2600-\u2604]|[\u260E-\u260F]|[\u2611]|[\u2614-\u2615]|[\u261D]|[\u263A]|[\u2648-\u2653]|[\u2660-\u2667]|[\u2668]|[\u267B]|[\u267F]|[\u2693]|[\u26A0-\u26A1]|[\u26AA-\u26AB]|[\u26BD-\u26BE]|[\u26C4-\u26C5]|[\u26CE]|[\u26D4]|[\u26EA]|[\u26F2-\u26F3]|[\u26F5]|[\u26FA]|[\u26FD]|[\u2702]|[\u2705]|[\u2708-\u270D]|[\u270F]|[\u2712]|[\u2714]|[\u2716]|[\u271D]|[\u2721]|[\u2728]|[\u2733-\u2734]|[\u2744]|[\u2747]|[\u274C]|[\u274E]|[\u2753-\u2755]|[\u2757]|[\u2763-\u2764]|[\u2795-\u2797]|[\u27A1]|[\u27B0]|[\u27BF]|[\u2934-\u2935]|[\u2B05-\u2B07]|[\u2B1B-\u2B1C]|[\u2B50]|[\u2B55]|[\u3030]|[\u303D]|[\u3297]|[\u3299]|[\uFE0F]|[\u200D]/g, '');
}

// 修改 handleExtract 方法，在提取文本后移除颜文字
const handleExtract = async (params) => {
	try {
		console.log('Extract:', params)

		// 无论提取什么，都将按钮设置为提取中状态
		isButtonExtracting.value = true

		const { content, options, file, isUrl } = params

		// 判断文件类型
		const isVideoFile = file && file.type &&
			(file.type.startsWith('video/') ||
				file.name.toLowerCase().endsWith('.mp4'))

		const isAudioFile = file && file.type &&
			(file.type.startsWith('audio/') ||
				file.name.toLowerCase().endsWith('.mp3'))

		// 判断是否是URL链接提取
		const isUrlExtraction = isUrl && content.trim()

		// 修改：只要有文案要展示，就显示 loading
		// 1. URL提取且选择了提取文案
		// 2. 音频文件提取
		// 3. 视频文件且会提取文案
		// 4. 纯文本输入
		const shouldShowTextLoading = (isUrlExtraction && options.extractText) ||
			isAudioFile ||
			(isVideoFile && !options.extractVideo) ||
			(!isUrlExtraction && !file && content.trim());

		// 设置右侧预览区的提取状态
		if (shouldShowTextLoading) {
			isExtracting.value = true;
		}

		// 1. 处理URL链接提取
		if (isUrlExtraction) {
			try {
				// 根据用户选择构建type数组
				const types = [];
				if (options.extractText) types.push('1');
				if (options.extractVideo) types.push('2');

				if (types.length === 0) {
					types.push('1');
					options.extractText = true;
				}

				console.log('解析URL:', content, '类型:', types)
				const response = await oneKeyParseVideo({
					url: content.trim(),
					type: types
				});

				if (response && response.status_code === 200) {
					const result = response.content.result;

					if (types.includes('1') && result.txt) {
						isExtracting.value = true;
						// 在赋值前移除颜文字
						const filteredText = removeEmojisAndSpecialSymbols(result.txt);
						
						// 修改：获取现有内容，追加新内容而不是替换
						const currentContent = previewContent.value.trim();
						const newContent = currentContent 
							? `${currentContent}\n${filteredText}` 
							: filteredText;
							
						previewContent.value = newContent;
						previewStore.setContent(newContent);
					}

					if (types.includes('2') && result.video) {
						extractedVideoUrl.value = result.video;
						extractedVideoTitle.value = '解析视频';
						showExtractResult.value = true;
						videoProgress.value = 0;
						currentTime.value = '0:00';
					}
				} else {
					throw new Error(response.message || '提取失败');
				}
			} catch (error) {
				console.error('链接提取失败:', error);
				ElMessage.error(`链接提取失败: ${error.message || '未知错误'}`);
			} finally {
				isButtonExtracting.value = false;
			}
		}
		// 2. 处理文件提取
		else if ((isAudioFile || isVideoFile) && file.url) {
			try {
				// 音频文件一定会显示 loading
				if (isAudioFile) {
					isExtracting.value = true;
					ElMessage.info('文案提取中，请稍等...')
				}

				// 调用提取API
				const response = await extractFile({
					url: file.url,
				});

				if (response && response.status_code == 200) {
					// 对于视频文件的处理
					if (isVideoFile) {
						// 如果接口返回了视频URL，使用返回的URL
						if (response.content.result.video) {
							extractedVideoUrl.value = response.content.result.video;
						} else {
							// 否则使用上传的视频URL
							extractedVideoUrl.value = file.url;
						}

						// 设置视频标题
						extractedVideoTitle.value = file.name || 'CHAOS LAB';

						// 重置视频进度
						videoProgress.value = 0;
						currentTime.value = '0:00';

						// 确保显示视频区域
						showExtractResult.value = true;
					}
					// 对于音频文件或有文案的处理
					if (isAudioFile || response.content.result.txt) {
						isExtracting.value = true;
						// 在赋值前移除颜文字
						const filteredText = removeEmojisAndSpecialSymbols(response.content.result.txt || '');
						
						// 修改：获取现有内容，追加新内容而不是替换
						const currentContent = previewContent.value.trim();
						const newContent = currentContent 
							? `${currentContent}\n${filteredText}` 
							: filteredText;
							
						previewContent.value = newContent;
						previewStore.setContent(newContent);
						// 音频文件不显示视频区域
						if (isAudioFile) {
							showExtractResult.value = false;
						}
					}
				} else {
					throw new Error(response.message || '提取失败');
				}
			} catch (error) {
				console.error('提取失败:', error);
				ElMessage.error(`提取失败: ${error.message || '未知错误'}`);

				// 对于视频文件，即使提取失败也显示视频播放器
				if (isVideoFile) {
					extractedVideoUrl.value = file.url;
					extractedVideoTitle.value = file.name || 'CHAOS LAB';
					showExtractResult.value = true;

					// 重置视频进度
					videoProgress.value = 0;
					currentTime.value = '0:00';
				}
			} finally {
				isButtonExtracting.value = false;
			}
		}
		// 3. 处理纯文本
		else if (content && content.trim()) {
			isExtracting.value = true; // 纯文本也显示 loading
			showExtractResult.value = false;
			// 在赋值前移除颜文字
			const filteredText = removeEmojisAndSpecialSymbols(content);
			
			// 修改：获取现有内容，追加新内容而不是替换
			const currentContent = previewContent.value.trim();
			const newContent = currentContent 
				? `${currentContent}\n${filteredText}` 
				: filteredText;
				
			previewContent.value = newContent;
			previewStore.setContent(newContent);
			isButtonExtracting.value = false;
		} else {
			showExtractResult.value = false;
			ElMessage.info('未找到可提取的内容');
			isButtonExtracting.value = false;
		}
	} catch (error) {
		console.error('提取处理出错:', error);
		ElMessage.error('提取过程中发生错误');
		isButtonExtracting.value = false;
	} finally {
		// 延迟一小段时间后再关闭 loading，让用户能看到加载效果
		setTimeout(() => {
			isExtracting.value = false;
		}, 500);
		isButtonExtracting.value = false;
	}
}

// 修改取消上传方法，添加隐藏视频播放区域的逻辑
const cancelUpload = () => {
	// 重置上传文件状态
	uploadFile.value = {
		name: '',
		size: 0,
		loaded: 0,
		percent: 0,
		type: ''
	}

	// 隐藏视频播放区域
	showExtractResult.value = false

	// 清除提取的视频URL
	extractedVideoUrl.value = ''

	// 如果有正在进行的上传请求，取消它
	if (uploadRequest.value) {
		uploadRequest.value.abort()
		uploadRequest.value = null
	}

	// 重置其他相关状态
	isUploading.value = false

	// 清空文件输入框
	if (fileInputRef.value) {
		fileInputRef.value.value = ''
	}

	ElMessage.info('已取消上传')
}

// 修改文件上传处理函数，保留原有功能并支持不同的参数形式
const handleFileChange = async (eventOrFile) => {
	try {
		// 确保我们获取到正确的file对象，无论传入的是DOM事件还是文件对象
		let event, file;

		if (eventOrFile instanceof Event) {
			event = eventOrFile;
			file = event.target.files[0];
		} else if (eventOrFile && eventOrFile.target && eventOrFile.target.files) {
			event = eventOrFile;
			file = event.target.files[0];
		} else {
			console.error('无效的文件参数');
			return;
		}

		if (!file) return;

		// 检查文件类型
		const allowedTypes = ['video/', 'audio/'];
		if (!allowedTypes.some(type => file.type.startsWith(type)) &&
			!file.name.toLowerCase().endsWith('.mp3') &&
			!file.name.toLowerCase().endsWith('.mp4')) {
			ElMessage.error('请上传视频或音频文件');
			return;
		}

		// 检查文件大小（这里设置最大500MB）
		const maxSize = 500 * 1024 * 1024; // 500MB
		if (file.size > maxSize) {
			ElMessage.error('文件大小不能超过500MB');
			return;
		}

		isUploading.value = true;

		// 设置上传文件信息
		uploadFile.value = {
			name: file.name,
			size: file.size,
			loaded: 0,
			percent: 0,
			type: file.type
		};

		// 获取文件扩展名确定文件类型
		const fileExtension = file.name.split('.').pop().toLowerCase();
		const fileType = fileExtension === 'mp4' ? 'mp4' : 'mp3';

		// 调用 dubbing API 获取 OSS 上传凭证
		const response = await dubbing({ userId: getUserId(), fileType });

		// 去掉文件名的后缀
		const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');

		const formData = new FormData();
		// 添加 OSS 需要的参数
		formData.append('OSSAccessKeyId', response.accessKeyId);
		formData.append('policy', response.policy);
		formData.append('signature', response.signature);
		formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`);
		formData.append('file', file);

		// 使用 XHR 上传文件，以便能够跟踪进度
		const xhr = new XMLHttpRequest();

		// 保存上传请求引用，以便能够取消
		uploadRequest.value = xhr;

		// 设置进度监听
		xhr.upload.onprogress = (e) => {
			if (e.lengthComputable) {
				uploadFile.value.percent = Math.round(e.loaded / e.total * 100);
				uploadFile.value.loaded = e.loaded;
			}
		};

		// 上传完成后的处理
		xhr.onload = async () => {
			try {
				if (xhr.status >= 200 && xhr.status < 300) {
					// 判断文件类型
					const materialType = fileExtension === 'mp4' ? 'video' : 'audio';
					const userId = '1';

					// 调用 callbackOss 接口
					const callbackResponse = await callbackOss({
						userId: getUserId(),
						materialName: fileNameWithoutExt,
						ossPath: response.key.replace(/[^\/]+$/, '') + file.name,
						fileSize: String(file.size),
						fileExtension: fileExtension,
						tagNames: '1',
						materialType: materialType,
						isPrivate: '1',
						storage_path: `/material/${getUserId()}/${file.name}`
					});

					// 更新文件信息
					uploadFile.value = {
						...uploadFile.value,
						name: callbackResponse.filename || file.name,
						url: callbackResponse.url,
						percent: 100,
						loaded: file.size
					};

					ElMessage.success('文件上传成功');
				} else {
					throw new Error(xhr.statusText || '上传失败');
				}
			} catch (error) {
				console.error('处理错误:', error);
				ElMessage.error(error.message || '文件处理失败');
				throw error; // 继续抛出错误以便外层catch捕获
			} finally {
				// 确保在所有处理完成后重置状态
				isUploading.value = false;
				isButtonExtracting.value = false;
				isExtracting.value = false;
				uploadRequest.value = null;
			}
		};

		// 错误处理
		xhr.onerror = (error) => {
			console.error('上传错误:', error);
			ElMessage.error('文件上传失败');
			// 发生错误时重置所有状态
			isUploading.value = false;
			isButtonExtracting.value = false;
			isExtracting.value = false;
			uploadFile.value.percent = 0;
			uploadRequest.value = null;
		};

		// 发送请求
		xhr.open('POST', response.host, true);
		xhr.send(formData);

		// 重置 input，使得相同文件能够重复选择
		if (event.target) {
			event.target.value = '';
		}

	} catch (error) {
		console.error('处理错误:', error);
		ElMessage.error('上传准备失败: ' + error.message);
		// catch 块中也要重置所有状态
		isUploading.value = false;
		isButtonExtracting.value = false;
		isExtracting.value = false;
		uploadFile.value = {
			name: '',
			size: 0,
			loaded: 0,
			percent: 0,
			type: ''
		};
		uploadRequest.value = null;
	}
};

// 添加下载视频事件处理函数
const handleDownloadVideo = () => {
	if (!extractedVideoUrl.value) {
		ElMessage.warning('没有可下载的视频')
		return
	}

	try {
		// 创建一个临时的a标签来触发下载
		const link = document.createElement('a')
		link.href = extractedVideoUrl.value

		// 设置下载文件名，使用视频标题或默认名称
		const fileName = extractedVideoTitle.value || 'video_download'

		// 确保文件名有正确的后缀
		link.download = fileName.endsWith('.mp4') ? fileName : `${fileName}.mp4`

		// 添加到DOM，触发点击，然后移除
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)

	} catch (error) {
		console.error('下载视频失败:', error)
		ElMessage.error('下载视频失败，请稍后重试')

		// 如果直接下载失败，提供一个备用方法 - 打开新窗口
		window.open(extractedVideoUrl.value, '_blank')
	}
}

// 处理"加入空间"按钮点击
const handleAddToSpace = () => {
	spaceDialogVisible.value = true
}

// 处理空间对话框确认
const handleSpaceDialogConfirm = (folders) => {
	console.log('选择的文件夹:', folders)
}

// 添加音乐对话框相关方法
const handleMusicDialogClose = () => {
	musicDialogVisible.value = false
}

const handleMusicDialogConfirm = () => {
	musicDialogVisible.value = false
	router.push('/music')
}

const handleMusicRemove = (index) => {
	musicStore.removeMusic(index)
	if (musicStore.musicList.length === 0) {
		musicDialogVisible.value = false
	}
}

const handleMusicTogglePlay = (index) => {
	musicStore.togglePlay(index)
}

// 添加视频对话框相关方法
const handleVideoDialogClose = () => {
	videoDialogVisible.value = false
}

const handleVideoDialogConfirm = () => {
	videoDialogVisible.value = false
	router.push('/video')
}

const handleVideoRemove = (index) => {
	videoList.value.splice(index, 1)
	if (videoList.value.length === 0) {
		videoDialogVisible.value = false
	}
}

const handleVideoTogglePlay = (index) => {
	videoList.value.forEach((item, i) => {
		if (i !== index) {
			item.isPlaying = false
		}
	})
	videoList.value[index].isPlaying = !videoList.value[index].isPlaying
}

// 处理素材上传
const handleMaterialUpload = async (formData, file, fileUrl) => {
	try {
		// 可以使用 fileUrl 来预览视频
		console.log('Video preview URL:', fileUrl)

		// 可以在这里处理视频文件的上传
		// const res = await uploadVideo(formData)

		// 添加到素材列表
		videoList.value.push({
			id: Date.now(), // 临时ID，实际应该使用后端返回的ID
			name: file.name,
			duration: '00:00', // 这里可以通过视频元数据获取实际时长
			thumbnail: '', // 可以是视频的第一帧或者后端生成的缩略图
			url: fileUrl, // 临时URL，实际应该使用后端返回的URL
			isPlaying: false
		})

		ElMessage.success('视频上传成功')
	} catch (error) {
		console.error('Upload error:', error)
		ElMessage.error('视频上传失败')
	}
}

// 添加以下必要的事件处理方法
const handleGenerateVideo = () => {
	console.log('开始生成视频')
	// 这里添加生成视频的逻辑
	ElMessage.info('正在生成视频，请稍候...')
}

const handleAddRole = () => {
	console.log('添加角色')
	// 跳转到角色选择页面的逻辑
	router.push('/VoiceOver')
}

const handleAddMusic = (openDialog = false) => {
	if (openDialog) {
		musicDialogVisible.value = true
	} else {
		// 跳转到音乐选择页面
		router.push('/MusicAudio')
	}
}

const handleAddVideo = (openDialog = false) => {
	// 跳转到视频成片页面
	router.push('/VideoEditing');
}

// 简化处理音量变化的方法
const handleVolumeChange = (item, value) => {
	console.log(`${item.title} 音量设置为: ${value}`)
	// 这里添加需要在父组件中处理的音量变化逻辑
	// 例如：调用API更新音量设置等
}

// 添加处理操作栏动作的方法
const handleBarAction = (action) => {
	console.log('操作栏动作:', action)
	switch (action) {
		case 'new':
			ElMessage.info('新建项目')
			break
		case 'recent':
			ElMessage.info('查看最近项目')
			break
		case 'edit':
			ElMessage.info('前往剪辑页面')
			router.push('/VideoEditing')
			break
		case 'export':
			ElMessage.info('导出项目')
			handleGenerateVideo()
			break
	}
}

// 处理提取内容的事件
const handleExtractContent = (content) => {
	previewPanel.value?.handleContentExtract(content);
}

const previewPanel = ref(null);
</script>

<style lang="scss" scoped>
.content-creation-container {
	display: grid;
	grid-template-rows: auto auto minmax(0, 1fr); /* 第一行Headbar，第二行OperationBar，第三行main-content占满剩余空间 */
	row-gap: 30px; /* 添加行间距 */
	height: 100vh;
	background: #f7f7f9;
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */

	:deep(.headbar-container) {
		grid-row: 1; /* Headbar在第一行 */
		width: 100%;
	}

	:deep(.operation-bar-container) {
		grid-row: 2; /* OperationBar在第二行 */
		width: 100%;
	}

	// 主要内容区域
	.main-content {
		grid-row: 3; /* 放在第三行 */
		display: flex;
		padding: 0 20px 20px 20px;
		padding-top: 10px; /* 改用更小的padding-top替代margin-top */
		margin-top: 0; /* 移除margin-top */
		background: #f7f7f9;
		overflow: auto; /* 内容过多时允许滚动 */

		// 左侧区域
		.left-section {
			display: flex;
			background: #fff;
			border-radius: 8px;
			margin-right: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

			// 左侧输入区
			.input-section {
				flex: 0 0 505px;
				width: 505px;
				min-width: unset;
				background: #fff;
				border-radius: 0 8px 8px 0;
				padding: 20px;
				display: flex;
				flex-direction: column;
				height: 100%;
				z-index: 2;

				.tools-wrapper {
					margin-bottom: 20px;
					position: relative;
					max-width: 100%;
					width: 100%;

					.tools-container {
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 8px;
						background: transparent;
						border-radius: 4px;
						padding: 8px;
						position: relative;
						height: 52px;

						&::after {
							content: '';
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 1px;
							background-color: #0000001A;
						}
					}

					.tools-bar {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
					}

					.tool-item {
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						padding: 0;
						position: relative;
						flex: 1;
						height: 52px;

						&::after {
							content: '';
							position: absolute;
							bottom: -2px;
							left: 50%;
							width: 120px;
							height: 0;
							background-color: transparent;
							transition: all 0.3s;
							transform: translateX(-50%);
						}

						&.active {
							&::after {
								height: 4px;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								opacity: 1;
								z-index: 1;
							}
						}

						&:hover {
							&::after {
								height: 4px;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								opacity: 0.5;
								z-index: 1;
							}
						}

						.tool-icon {
							height: 52px;
							width: 120px;
							object-fit: contain;
						}
					}
				}
			}
		}

		// 右侧区域使用已封装的组件
		.right-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			min-width: 400px;
		}
	}
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
	.content-creation-container {
		.main-content {
			.left-section {
				.input-section {
					flex: 0 0 450px;
					width: 450px;
				}
			}
		}
	}
}

@media screen and (max-width: 992px) {
	.content-creation-container {
		.main-content {
			flex-direction: column;
			
			.left-section {
				margin-right: 0;
				margin-bottom: 20px;
				
				.input-section {
					width: 100%;
					flex: 1;
				}
			}
			
			.right-content {
				min-width: unset;
			}
		}
	}
}
</style>