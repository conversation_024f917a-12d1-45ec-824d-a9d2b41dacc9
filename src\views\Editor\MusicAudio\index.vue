<template>
	<div class="music-audio-container">
		<!-- 使用Headbar组件替换原来的标题栏 -->
		<Headbar />

		<!-- 使用OperationBar组件替换原来的操作栏 -->
		<OperationBar @action="handleBarAction" />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<!-- 左侧区域 -->
			<div class="left-section">
				<!-- 左侧菜单 -->
				<LeftMenu />

				<!-- 音频库区域 -->
				<div class="audio-library">
					<!-- 工具栏 -->
					<div class="tools-wrapper">
						<div class="tools-container">
							<div class="tools-bar">
								<div v-for="tool in tools" :key="tool.id" class="tool-item"
									:class="{ active: activeTool === tool.id }" @click="handleToolClick(tool)">
									<img :src="(activeTool === tool.id || hoveredTool === tool.id) ? tool.activeImg : tool.img"
										:alt="tool.name" class="tool-icon" @mouseenter="hoveredTool = tool.id"
										@mouseleave="hoveredTool = null">
								</div>
							</div>
						</div>
					</div>

					<!-- 音频列表 -->
					<div class="audio-list">
						<div class="button-bar">
							<!-- <button class="action-btn green-btn">选择专辑</button> -->
							<button v-if="showUploadMusicBtn" class="action-btn white-btn" @click="triggerFileUpload">
								<img src="@/assets/img/btn.png" alt="上传" class="btn-icon">
								上传音乐
							</button>
							<!-- 添加隐藏的文件上传输入框 -->
							<input 
								ref="fileInputRef" 
								type="file" 
								accept=".mp3" 
								style="display: none;" 
								@change="handleFileChange"
							/>
						</div>

						<!-- 上传进度条 -->
						<div v-if="isUploading" class="upload-progress">
							<div class="progress-info">
								<span class="file-name">{{ uploadFile.name }}</span>
								<span class="progress-text">{{ uploadFile.percent }}%</span>
							</div>
							<div class="progress-bar">
								<div class="progress-fill" :style="{ width: `${uploadFile.percent}%` }"></div>
							</div>
							<div class="progress-size">
								{{ formatFileSize(uploadFile.loaded) }} / {{ formatFileSize(uploadFile.size) }}
							</div>
						</div>

						<!-- 类别导航栏 -->
						<div class="category-nav" v-if="activeTool !== 1">
							<div 
								v-for="category in categories" 
								:key="category"
								class="category-item" 
								:class="{ 'active': activeCategory === category }"
								@click="handleCategoryClick(category)"
							>
								{{ category }}
							</div>
						</div>

						<!-- 音频列表内容 -->
						<div class="audio-items">
							<!-- 加载状态 -->
							<div v-if="isLoading" class="loading-state">
								<div class="loading-spinner"></div>
								<div class="loading-text">正在加载音乐...</div>
							</div>

							<!-- 错误状态 -->
							<div v-else-if="loadError" class="error-state">
								<div class="error-icon"></div>
								<div class="error-text">{{ loadError }}</div>
							</div>

							<!-- 音频列表 -->
							<div v-else-if="displayedAudioItems.length > 0" v-for="(item, index) in displayedAudioItems"
								:key="index" class="audio-item" :class="{ playing: item.isPlaying }"
								@mouseenter="item.isHovered = true" @mouseleave="item.isHovered = false">
								<div class="audio-thumbnail" :style="{ background: getGradientColor(index) }">
									<div class="play-pause-btn" @click="togglePlay(index)">
										<img :src="item.isPlaying ? caretRight1Icon : caretRightIcon" alt="播放按钮"
											class="player-icon">
									</div>
								</div>
								<div class="audio-content">
									<div class="audio-info">
										<div class="audio-name" :title="item.name">{{ formatMusicName(item.name) }}</div>
										<div class="audio-actions" v-show="item.isHovered || item.isPlaying || item.isChangingVolume">
											<span class="action-text download-text" @click="handleDownload(item)">下载</span>
											<!-- 只在"我的收藏"工具栏选项（ID为2）被选中时显示"加入空间"按钮 -->
											<span v-if="activeTool === 2" class="action-text space-text"
												@click="handleAddToSpace(item)">加入空间</span>
											<span class="action-text add-text" @click="addToPreview(item)">添加音乐</span>
										</div>
									</div>
									<!-- 水平控制条 -->
									<div class="audio-control-bar">
										<div class="audio-text-info">{{ item.duration }} | {{ formatMusicName(item.name) }}</div>
										<div class="volume-controls"
											v-show="item.isHovered || item.isPlaying || item.isChangingVolume">
											<div class="volume-icon" :class="{ 'muted': item.volume <= 0 }"
												@click="toggleMute(index)"></div>
											<div class="volume-track">
												<div class="volume-fill" :style="{ width: `${item.volume}%` }"
													:class="{ 'active': item.isChangingVolume }"></div>
											</div>
											<input type="range" min="0" max="100" v-model="item.volume"
												@input="updateVolume(index, item.volume)"
												@mousedown="item.isChangingVolume = true"
												@mouseup="item.isChangingVolume = false"
												@mouseleave="item.isChangingVolume = false">
										</div>
									</div>
								</div>
							</div>
							<div v-else class="no-results">
								<img src="@/assets/img/empty.png" alt="暂无数据" class="empty-img">
								<div class="empty-text">没有找到符合条件的结果</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 右侧区域使用已封装的组件 -->
			<div class="right-content">
				<PreviewPanel ref="previewPanelRef" v-model:title="titleText" v-model:content="previewContent"
					:isVideoEditingPage="false"
					v-model:musicList="selectedAudioItems" :is-extracting="isPreviewLoading"
					@generate-video="handleGenerateVideo" @add-role="handleAddRole" @add-music="handleAddMusic"
					@add-video="handleAddVideo" @volume-change="handlePreviewVolumeChange" />
			</div>
		</div>

		<!-- 音乐素材弹窗 -->
		<MusicDialog v-model:visible="musicDialogVisible" :material-list="selectedAudioItems"
			@close="handleMusicDialogClose" @confirm="handleMusicDialogConfirm" @remove="handleMusicRemove"
			@toggle-play="handleMusicTogglePlay" @add-music="handleAddMusic" />

		<!-- 空间对话框 -->
		<SubtitleDialog 
			v-model="spaceDialogVisible" 
			:material-info="{ userId: getUserId(), materialName: currentMusicItem?.name, materialUrl: currentMusicItem?.url, fileSize: currentMusicItem?.fileSize }"
			:folders="spaceFolders" 
			:selected-folders="selectedSpaceFolders"
			@confirm="handleSpaceDialogConfirm" />
	</div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'
import LeftMenu from '@/views/Editor/components/common/LeftMenu.vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import OperationBar from '@/views/Editor/components/common/OperationBar.vue'
import PreviewPanel from '@/views/Editor/components/PreviewPanel.vue'
import MusicDialog from '@/components/MusicDialog.vue'
import SubtitleDialog from '@/components/SubtitleDialog/index.vue'
import { useMusicStore } from '@/stores/modules/musicStore' // 导入音乐存储
import { useloginStore } from '@/stores/login' // 导入用户store
import { queryBGM, querySFX, queryUserAudio } from '@/api/music' // 导入音乐API
import { dubbing, callbackOss } from '@/api/dubbing' // 添加这一行
import { saveFullMaterial } from '@/api/myMaterial' // 导入保存素材接口

// 导入图标
import Variant4 from '@/assets/img/Variant4.png'
import Variant3 from '@/assets/img/Variant3.png'
import Variant5 from '@/assets/img/Variant5.png'
import Variant8 from '@/assets/img/Variant8.png'
import Variant6 from '@/assets/img/Variant6.png'
import Variant7 from '@/assets/img/Variant7.png'
import caretRightIcon from '@/assets/img/caret-right.png'
import caretRight1Icon from '@/assets/img/caret-right1.png'

// 路由实例
const router = useRouter()

// 获取音乐存储
const musicStore = useMusicStore()

// 获取用户store
const loginStore = useloginStore()

// 添加getCurrentInstance相关代码
const { proxy } = getCurrentInstance();

// 添加格式化音乐名称的函数，限制在四个字符以内
const formatMusicName = (name) => {
	if (!name) return '未命名';
	return name.length > 6 ? name.slice(0, 6) + '...' : name;
};

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}

// 工具栏状态
const activeTool = ref(1)
const hoveredTool = ref(null)

// 当前选中的分类
const activeCategory = ref('我的')

// 分类列表 - 改为响应式引用
const categories = ref(['我的', '热门', '宣传片', '喜庆', '抒情', '史诗', '震撼'])

// 存储完整的主题数据对象
const themeDataList = ref([])

// 1. 添加缓存机制
const durationCache = new Map()

// 判断是否展示 上传音乐按钮
const showUploadMusicBtn = ref(true)

// 获取音频时长的工具函数 - 修改后版本
const getAudioDuration = (fileOrUrl) => {
	return new Promise((resolve, reject) => {
		const audio = new Audio();
		let objectUrl = null;

		const cleanup = () => {
			if (objectUrl) {
				URL.revokeObjectURL(objectUrl);
			}
			audio.removeEventListener('loadedmetadata', onLoadedMetadata);
			audio.removeEventListener('error', onError);
			audio.src = ''; // 清理audio元素的src
		};

		const onLoadedMetadata = () => {
			if (isNaN(audio.duration) || audio.duration === Infinity) {
				console.warn('音频时长无效，可能仍在加载或格式不支持，返回默认值');
				cleanup();
				resolve({ durationInSeconds: 0, formattedDuration: '00:00' });
				return;
			}
			const durationInSeconds = Math.round(audio.duration);
			const minutes = Math.floor(durationInSeconds / 60);
			const seconds = durationInSeconds % 60;
			const formattedDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
			cleanup();
			resolve({ durationInSeconds, formattedDuration });
		};

		const onError = (err) => {
			console.error('获取音频时长失败:', err);
			cleanup();
			// 在发生错误时，也返回默认时长，避免阻塞流程
			resolve({ durationInSeconds: 0, formattedDuration: '00:00' });
		};

		audio.addEventListener('loadedmetadata', onLoadedMetadata);
		audio.addEventListener('error', onError);

		if (typeof fileOrUrl === 'string') {
			audio.src = fileOrUrl;
		} else if (fileOrUrl instanceof File) {
			objectUrl = URL.createObjectURL(fileOrUrl);
			audio.src = objectUrl;
		} else {
			cleanup();
			console.error('无效的参数类型，期望 File 对象或 URL 字符串');
			resolve({ durationInSeconds: 0, formattedDuration: '00:00' }); // 参数类型错误也返回默认值
			return;
		}

		audio.load(); // 显式调用load方法

		// 设置一个超时，以防某些情况下 loadedmetadata 不触发
		const timeoutId = setTimeout(() => {
			console.warn('获取音频时长超时，返回默认值');
			cleanup();
			resolve({ durationInSeconds: 0, formattedDuration: '00:00' });
		}, 5000); // 5秒超时

		// 在事件触发后清除超时
		audio.addEventListener('loadedmetadata', () => clearTimeout(timeoutId));
		audio.addEventListener('error', () => clearTimeout(timeoutId));
	});
};

/**
 * 根据选择的主题加载音效或背景音乐
 * @param {string} theme - 当前选择的主题分类名称
 */
const loadSFXByTheme = (theme) => {
	// 设置加载状态为true，表示正在加载数据
	isLoading.value = true
	// 清空之前的错误信息
	loadError.value = null
	
	try {
		// 从已获取的主题数据列表中找到匹配当前选中主题的数据对象
		const themeData = themeDataList.value.find(item => item.theme === theme)
		// 初始化要展示的数据列表
		let dataList = []
		// 根据当前激活的工具ID选择相应的数据列表
		if (activeTool.value === 2) {
			// 如果是音乐库(ID为2)，则获取背景音乐列表
			dataList = themeData?.bgmList || []
		} else if (activeTool.value === 3) {
			// 如果是音效(ID为3)，则获取音效列表
			dataList = themeData?.sfxList || []
		}
		
		if (dataList.length > 0) {
			// 当数据列表不为空时，先立即显示基本信息，使用预估时长提升用户体验
			const items = dataList.map(item => ({
				// 音频名称，如果后端没提供则使用默认值
				name: item.musicName || '未命名音频',
				// 使用后端返回的时长(如果有)，否则使用默认值
				duration: item.duration || '00:00', 
				// 默认状态设置
				isPlaying: false,        // 是否正在播放
				isHovered: false,        // 是否鼠标悬停
				isChangingVolume: false, // 是否正在调整音量
				volume: 100,             // 默认音量为100%
				// 音频文件路径
				url: item.ossPath || '',
				// 音频ID，用于后续操作
				id: item.id,
				fileSize: item.fileSize || 0,
			}))
			
			// 更新UI显示的音频列表
			audioItems.value = items

			// 批量异步获取真实的音频时长，每批处理5个，避免同时发起过多请求
			const batchSize = 5
			const updateDurations = async () => {
				// 按批次处理所有音频项
				for (let i = 0; i < items.length; i += batchSize) {
					// 提取当前批次的音频项
					const batch = items.slice(i, i + batchSize)
					// 并行处理当前批次中的所有音频项
					await Promise.all(
						batch.map(async (item, index) => {
							// 只处理有URL的音频项
							if (item.url) {
								// 计算当前项在整体列表中的实际索引
								const realIndex = i + index
								// 异步获取音频的实际时长
								const durationInfo = await getAudioDuration(item.url) // getAudioDuration 返回 { durationInSeconds, formattedDuration }
								// 确保索引项仍然存在，然后更新时长为格式化后的字符串
								if (audioItems.value[realIndex]) {
									audioItems.value[realIndex].duration = durationInfo.formattedDuration
								}
							}
						})
					)
				}
			}

			// 开始执行批量更新时长的操作
			updateDurations()
		} else {
			// 数据列表为空时，清空音频列表显示
			audioItems.value = []
		}
	} catch (error) {
		// 捕获并记录加载过程中的任何错误
		console.error(`加载主题 "${theme}" 的数据失败:`, error)
		// 根据当前激活的工具设置相应的错误提示
		loadError.value = `加载${activeTool.value === 2 ? '背景音乐' : '音效'}失败，请稍后重试`
		// 错误情况下清空音频列表
		audioItems.value = []
	} finally {
		// 无论成功还是失败，最终都将加载状态设置为false
		isLoading.value = false
	}
}

// 处理分类点击
const handleCategoryClick = (category) => {
	// 暂停所有正在播放的音频
	stopAllAudio()
	
	activeCategory.value = category
	
	// 根据不同的工具ID和选中的分类加载不同的数据
	if (activeTool.value !== 1) {
		// 第2个tab - 从已获取的主题数据中加载对应sfxList
		loadSFXByTheme(category)
	} else if (category === '我的') {
		// 第1个tab且分类为"我的"
		loadMyMusic()
	} 
}

// 工具数据
const tools = [
	{
		id: 1,
		name: '我的音乐',
		img: Variant4,
		activeImg: Variant3
	},
	{
		id: 2,
		name: '音乐库',
		img: Variant6,
		activeImg: Variant5
	},
	/* {
		id: 3,
		name: '音效',
		img: Variant8,
		activeImg: Variant7
	} */
]

// 处理工具点击
const handleToolClick = (tool) => {
	// 暂停所有正在播放的音频
	stopAllAudio()
	
	activeTool.value = tool.id
	
	// 根据不同的工具ID加载不同的数据
	switch (tool.id) {
		case 1: // 我的音乐
			showUploadMusicBtn.value = true
			loadMyMusic() // 加载推荐音乐
			break
		case 2: // 音乐库 - 获取主题分类音效数据
			// 获取主题分类数据
			showUploadMusicBtn.value = false
			loadBGM()
			break
		case 3: // 音效 - 调用普通音效库
			showUploadMusicBtn.value = false
			loadSFX() // 音效
			break
	}
}

// 暂停所有正在播放的音频
const stopAllAudio = () => {
	audioItems.value.forEach(item => {
		if (item.isPlaying) {
			item.isPlaying = false
			
			// 停止正在播放的音频
			if (item.audioElement) {
				item.audioElement.pause()
			}
		}
	})
}

// 音频相关状态
const searchQuery = ref('')
const isLoading = ref(false)  // 左侧音频列表加载状态
const isPreviewLoading = ref(false)  // 右侧预览加载状态
const loadError = ref(null)
const titleText = ref('') // 标题输入框的值
const previewContent = ref('') // 预览内容

// 音频数据
const audioItems = ref([])

// 已选音频列表，使用全局存储的列表替代本地状态
const selectedAudioItems = computed(() => musicStore.musicList)

// 已选音频列表
const selectedAudioItems2 = ref([
	{ name: '轻松愉快', isPlaying: false, duration: '02:15' },
	{ name: '雨声', isPlaying: false, duration: '00:30' }
])

// 显示所有音频项，不按类型过滤
const displayedAudioItems = computed(() => {
	return audioItems.value.filter(item => {
		const matchesSearch = searchQuery.value ? item.name.toLowerCase().includes(searchQuery.value.toLowerCase()) : true
		return matchesSearch
	})
})

// 音频播放器引用
const audioPlayer = ref(null)

// 组件卸载时清理
onUnmounted(() => {
	// 停止所有正在播放的音频并释放资源
	audioItems.value.forEach(item => {
		if (item.audioElement) {
			item.audioElement.pause();
			item.audioElement.src = '';
			item.audioElement = null;
		}
	});

	if (updateVolumeTimer.value) {
		clearTimeout(updateVolumeTimer.value);
	}
	
	// 移除全局点击事件监听器
	document.removeEventListener('click', handleGlobalClick);
});

// 添加全局点击事件处理函数
const handleGlobalClick = (event) => {
	// 获取点击事件的路径
	const path = event.composedPath ? event.composedPath() : event.path || [];
	
	// 检查点击是否发生在播放控制区域内
	const isClickOnPlayControl = path.some(element => {
		// 检查元素是否有类名
		if (element.classList) {
			// 检查是否点击了播放按钮或音频控制区域的相关元素
			return element.classList.contains('play-pause-btn') || 
				element.classList.contains('player-icon') || 
				element.classList.contains('volume-controls') || 
				element.classList.contains('audio-actions');
		}
		return false;
	});
	
	// 如果点击不在播放控制区域内，停止所有音频播放
	if (!isClickOnPlayControl) {
		stopAllAudio();
	}
};

// 在mounted生命周期中添加全局点击事件监听器
onMounted(() => {
	// 添加全局点击事件监听器
	document.addEventListener('click', handleGlobalClick);
	loadMyMusic()
})

// 切换播放状态
const togglePlay = (index) => {

	
	// 先暂停任何正在播放的音频
	audioItems.value.forEach((item, i) => {
		if (i !== index && item.isPlaying) {
			item.isPlaying = false
			// 停止其他正在播放的音频
			if (item.audioElement) {
				item.audioElement.pause()
			}
		}
	})

	const item = audioItems.value[index]
	console.log(item, 'item');
	
	// 切换当前音频的播放状态
	item.isPlaying = !item.isPlaying

	// 实际播放/暂停音频
	if (!item.audioElement) {
		// 检查URL是否有效
		if (!item.url || item.url.trim() === '') {
			console.error('无效的音频URL');
			ElMessage.error('无法播放：音频URL无效');
			item.isPlaying = false;
			return;
		}
		
		// 如果还没有创建音频元素，则创建一个
		try {
			const audioUrl = item.url || 'http://example.com/default-audio.mp3' // 提供一个默认URL用于测试
			console.log('准备播放音频:', audioUrl)
			
			item.audioElement = new Audio(audioUrl)
			item.audioElement.volume = item.volume / 100

			// 监听播放结束事件，自动重置状态
			item.audioElement.addEventListener('ended', () => {
				item.isPlaying = false
			})

			// 监听音频加载错误
			item.audioElement.addEventListener('error', (e) => {
				console.error('音频加载错误:', e)
				ElMessage.error(`无法加载音频 "${item.name}"，请检查音频文件是否有效`)
				item.isPlaying = false
			})
		} catch (err) {
			console.error('创建音频元素失败:', err)
			ElMessage.error(`无法播放 "${item.name}"`)
			item.isPlaying = false
			return
		}
	}

	if (item.isPlaying) {
		// 尝试播放音频
		try {
			const playPromise = item.audioElement.play()

			if (playPromise !== undefined) {
				playPromise.then(() => {
					// 播放成功
					// 移除成功消息通知
				}).catch(err => {
					console.error('播放失败:', err)
					// 提供更详细的错误信息
					let errorMsg = '网络错误或资源不可用';
					if (err.name) {
						switch(err.name) {
							case 'NotAllowedError':
								errorMsg = '浏览器阻止了自动播放，请尝试点击页面后再播放';
								break;
							case 'NotSupportedError':
								errorMsg = '音频格式不受支持';
								break;
							case 'AbortError':
								errorMsg = '播放被中断';
								break;
							default:
								errorMsg = `播放出错 (${err.name})`;
						}
					}
					ElMessage.error(`播放失败: ${errorMsg}`);
					item.isPlaying = false
				})
			}
		} catch (err) {
			console.error('播放音频时发生错误:', err)
			ElMessage.error(`无法播放 "${item.name}"：${err.message || '未知错误'}`)
			item.isPlaying = false
		}
	} else {
		item.audioElement.pause()
		// 移除已暂停消息通知
	}
}

// 添加对话框显示状态变量
const musicDialogVisible = ref(false)
// 空间对话框显示状态
const spaceDialogVisible = ref(false)
// 当前操作的音频项
const currentMusicItem = ref(null)

// 空间文件夹数据
const spaceFolders = ref([
	{ name: "默认收藏夹", count: 8 },
	{ name: "音乐收藏", count: 5 },
	{ name: "热门音乐", count: 12 },
])

// 默认选中的文件夹
const selectedSpaceFolders = ref(["默认收藏夹"])

// 将音频添加到预览面板
const addToPreview = (item) => {
	// 创建一个新对象以避免引用问题
	const musicItem = {
		name: item.name,
		duration: item.duration,
		url: item.url,
		volume: item.volume, // 确保使用当前项目的音量值
		isPlaying: false
	};

	// 检查当前音乐列表是否为空
	if (musicStore.musicList.length === 0) {
		// 如果为空，直接添加
		musicStore.addMusic(musicItem);
	} else {
		// 如果已有音乐，则清空列表后添加新的（替换操作）
		musicStore.clearAll();
		musicStore.addMusic(musicItem);
	}
}

// 添加处理对话框事件的方法
const handleMusicDialogClose = () => {
	musicDialogVisible.value = false
}

const handleMusicDialogConfirm = () => {
	musicDialogVisible.value = false
	// 移除确认提示
}

// 修改音乐移除方法，使用musicStore移除音乐
const handleMusicRemove = (index) => {
	musicStore.removeMusic(index)
	// 移除移除提示
}

// 修改音乐播放切换方法，使用musicStore切换音乐播放状态
const handleMusicTogglePlay = (index) => {
	musicStore.togglePlay(index)

	const item = musicStore.musicList[index]
	// 移除播放状态提示
}

// 以下是必要的事件处理方法
const handleGenerateVideo = () => {
	console.log('开始生成视频')
	isPreviewLoading.value = true  // 开始生成时设置预览加载状态
	ElMessage.info('正在生成视频，请稍候...')
	
	// 模拟生成过程
	setTimeout(() => {
		isPreviewLoading.value = false  // 生成完成后重置加载状态
		ElMessage.success('视频生成完成')
	}, 3000)
}

const handleAddRole = () => {
	router.push('/VoiceOver')
}

const handleAddMusic = () => {
	// 修改逻辑：判断是否已添加音乐
	if (selectedAudioItems.value.length > 0) {
		// 如果已添加音乐，则打开对话框
		musicDialogVisible.value = true
	}
}

const handleAddVideo = () => {
	router.push('/VideoEditing')
}

// 处理音量变化
const handleVolumeChange = (item, value) => {
	if (item.type === 'music' && musicStore.musicList.length > 0) {
		// 更新音乐存储中的音量
		const updatedList = musicStore.musicList.map(music => ({
			...music,
			volume: value
		}));
		musicStore.updateMusicList(updatedList);
		
		// 如果正在播放,实时更新音频音量
		if (audioPlayer.value) {
			audioPlayer.value.volume = value / 100;
		}
		// 仅当左侧选中项名称与 PreviewPanel 中展示的名称一致时，同步到右侧预览面板音量
		const selected = selectedAudioItems.value[0]
		if (selected && selected.name === item.name && previewPanelRef.value) {
			previewPanelRef.value.updateMaterialVolume('music', Number(value))
		}
	}
}

// 在 PreviewPanel 组件中监听音量变化事件
const handlePreviewVolumeChange = (item, value) => {
	handleVolumeChange(item, value);
}

// 切换静音
const toggleMute = (index) => {
	const item = audioItems.value[index]
	if (item.volume > 0) {
		item.previousVolume = item.volume  // 保存当前音量
		item.volume = 0
	} else {
		item.volume = item.previousVolume || 100
	}

	// 实际更新音频元素的音量
	if (item.audioElement) {
		item.audioElement.volume = item.volume / 100
	}

	// 移除提示消息
}

// 更新音量
const updateVolume = (index, volume) => {
	const item = audioItems.value[index]
	item.volume = volume

	// 实际更新音频元素的音量
	if (item.audioElement) {
		item.audioElement.volume = volume / 100
	}

	// 仅当左侧选中预览的音乐名称与当前项名称一致时，同步到右侧预览面板音量
	const selected = selectedAudioItems.value[0]
	if (selected && selected.name === item.name && previewPanelRef.value) {
		previewPanelRef.value.updateMaterialVolume('music', Number(volume))
	}

	// 防抖处理，避免频繁显示消息
	if (updateVolumeTimer.value) clearTimeout(updateVolumeTimer.value)
}

// 添加一个防抖动定时器变量
const updateVolumeTimer = ref(null)

// 处理OperationBar组件的action事件
const handleBarAction = (action) => {
	console.log('操作栏动作:', action)
	switch (action) {
		case 'new':
			ElMessage.info('新建音乐项目')
			break
		case 'save':
			ElMessage.info('查看最近项目')
			break
		case 'edit':
			ElMessage.info('前往剪辑页面')
			router.push('/VideoEditing')
			break
		case 'export':
			ElMessage.info('导出音乐项目')
			handleGenerateVideo()
			break
	}
}

// 处理"加入空间"按钮点击
const handleAddToSpace = async (item) => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		ElMessage.warning('请先登录后再使用此功能')
		return
	}
	
	// 暂时写死1MB文件大小，避免CORS问题
	if (!item.fileSize || item.fileSize === 0) {
		item.fileSize = 1024 * 1024; // 1MB = 1024 * 1024 bytes
	}
	
	// 保存当前操作的音频项
	currentMusicItem.value = item
	// 显示空间对话框
	spaceDialogVisible.value = true
}

// 处理空间对话框确认
const handleSpaceDialogConfirm = async (params) => {
	// params为完整参数对象，含tagId
	// 校验必选字段
	const required = ['materialName', 'userId', 'materialUrl', 'tagId'];
	for (const key of required) {
		if (!params[key]) {
			ElMessage.error(`${key}为必填项`);
			return;
		}
	}
	
	// 调用保存接口
	try {
		// 组装保存参数，添加音乐特有的字段
		const saveParams = {
			...params,
			storagePath: params.materialUrl, // 将materialUrl映射为storagePath
			materialType: 'audio', // 设置为音频类型
			// 如果需要其他字段可以在这里添加
		};
		// 删除原来的materialUrl字段，避免重复
		delete saveParams.materialUrl;
		
		const res = await saveFullMaterial(saveParams);
		ElMessage.success("保存成功");
	} catch (error) {
		ElMessage.error("保存失败");
		console.error("保存失败:", error);
	}
}

// 加载我的音乐
const loadMyMusic = async () => {
	isLoading.value = true
	loadError.value = null
	
	// 添加登录检查
	if (!checkUserLogin()) {
		loadError.value = '请先登录后查看您的音乐'
		audioItems.value = []
		isLoading.value = false
		return
	}

	try {
		const response = await queryUserAudio({ userId: getUserId() })
		console.log(response, 'response')
		if (response) {
			// 将API返回的数据映射到audioItems的格式
			const items = response.map(item => ({
				name: item.materialName || '未命名音乐',
				duration: '00:00', // 初始化为默认值，稍后会更新
				isPlaying: false,
				isHovered: false,
				isChangingVolume: false,
				volume: 100, // 默认音量100%
				url: item.storagePath || '', // 修改这里：从ossPath改为storagePath
				fileSize: item.fileSize || 0,
				// 其他可能需要的字段
			}))
			
			// 设置初始数据让界面先显示
			audioItems.value = items
			
			// 异步获取每个音频的实际时长
			Promise.all(
				items.map(async (item, index) => {
					try {
						if (item.url) {
							const durationInfo = await getAudioDuration(item.url) // getAudioDuration 返回 { durationInSeconds, formattedDuration }
							// 更新时长为格式化后的字符串
							items[index].duration = durationInfo.formattedDuration 
						}
					} catch (error) {
						console.error('获取音频时长失败:', error)
					}
				})
			).then(() => {
				// 获取完所有时长后更新列表
				audioItems.value = [...items]
			})
		} else {
			audioItems.value = []
		}
	} catch (error) {
		console.error('加载我的音乐失败:', error)
		loadError.value = '加载音乐失败，请稍后重试'
		audioItems.value = []
	} finally {
		isLoading.value = false
	}
}

// 加载音乐库
const loadBGM = async (category) => {
	isLoading.value = true
	loadError.value = null

	try {
		const response = await queryBGM()
		console.log(response, 'response');
		
		if (response && response.length > 0) {
			// 保存完整的theme数据对象数组
			themeDataList.value = response
			
			// 提取theme字段作为分类展示
			categories.value = response.map(item => item.theme)
			
			// 设置默认选中第一个分类
			if (categories.value.length > 0) {
				activeCategory.value = categories.value[0]
				
				// 加载第一个分类的音效数据
				loadSFXByTheme(activeCategory.value)
			}
		} else {
			categories.value = []
			themeDataList.value = []
			audioItems.value = []
		}
	} catch (error) {
		ElMessage.error('获取音乐库分类失败，请稍后重试')
		categories.value = []
		themeDataList.value = []
		audioItems.value = []
	} finally {
		isLoading.value = false
	}
}

// 加载音效
const loadSFX = async () => {
	isLoading.value = true
	try {
		// 调用querySFX接口获取主题分类
		const response = await querySFX()
		console.log('获取音效分类数据:', response)
		
		if (response && response.length > 0) {
			// 保存完整的theme数据对象数组
			themeDataList.value = response
			
			// 提取theme字段作为分类展示
			categories.value = response.map(item => item.theme)
			
			// 设置默认选中第一个分类
			if (categories.value.length > 0) {
				activeCategory.value = categories.value[0]
				
				// 加载第一个分类的音效数据
				loadSFXByTheme(activeCategory.value)
			}
		} else {
			categories.value = []
			themeDataList.value = []
			audioItems.value = []
		}
	} catch (error) {
		ElMessage.error('获取音效分类失败，请稍后重试')
		categories.value = []
		themeDataList.value = []
		audioItems.value = []
	} finally {
		isLoading.value = false
	}
}

// 格式化时间的工具函数
const formatTime = (seconds) => {
	if (!seconds || isNaN(seconds)) return '00:00';
	const mins = Math.floor(seconds / 60);
	const secs = Math.floor(seconds % 60);
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 处理下载音频到桌面
const handleDownload = (item) => {
	// 检查是否有URL
	if (!item.url) {
		ElMessage.error('该音频没有可用的下载链接')
		return
	}
	
	try {
		// 创建一个隐藏的a标签来下载文件
		const link = document.createElement('a')
		link.href = item.url
		
		// 获取原始文件名
		let fileName = item.name
		
		// 从URL中提取文件扩展名（如果URL中包含）
		let fileExtension = '.mp3' // 默认扩展名
		if (item.url) {
			const urlParts = item.url.split('/')
			const urlFileName = urlParts[urlParts.length - 1]
			if (urlFileName.includes('.')) {
				const urlExtension = '.' + urlFileName.split('.').pop()
				// 只有当URL扩展名看起来像音频文件时才使用它
				if (['.mp3', '.wav', '.ogg', '.flac', '.m4a', '.aac'].includes(urlExtension.toLowerCase())) {
					fileExtension = urlExtension
				}
			}
		}
		
		// 确保文件名不为空
		if (!fileName || fileName.trim() === '') {
			fileName = '未命名音频'
		}
		
		// 清理文件名中的非法字符
		fileName = fileName.replace(/[\\/:*?"<>|]/g, '_')
		
		// 设置下载文件名（保留原始名称和文件扩展名）
		link.download = `${fileName}${fileExtension}`
		
		// 将链接添加到文档
		document.body.appendChild(link)
		// 模拟点击
		link.click()
		// 移除链接
		document.body.removeChild(link)
		
		ElMessage.success(`正在下载: ${item.name}`)
	} catch (error) {
		console.error('下载失败:', error)
		ElMessage.error('下载失败，请稍后重试')
	}
}

// 文件上传相关状态
const fileInputRef = ref(null)  // 文件输入框引用
const isUploading = ref(false)  // 上传状态
const uploadFile = ref({  // 上传文件信息
	name: '',
	size: 0,
	loaded: 0,
	percent: 0,
	type: '',
	url: ''
})
const uploadRequest = ref(null)  // 上传请求引用

// 添加用户登录检查函数
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;
	
	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

// 触发文件选择对话框
const triggerFileUpload = () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	fileInputRef.value.click();
}

// 处理文件上传
const handleFileChange = async (event) => {
	console.log(event,'handleFileChange');
	
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 显示全局 loading
	const loadingInstance = ElLoading.service({
		lock: true,
		text: '权限检查中...',
		background: 'rgba(0, 0, 0, 0.7)'
	});
	
	try {
		const file = event.target.files[0];
		if (!file) {
			loadingInstance.close();
			return;
		}
		// Prevent uploading files with duplicate name
		const selectedFileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');
		if (audioItems.value.some(item => item.name && item.name.toLowerCase() === selectedFileNameWithoutExt.toLowerCase())) {
			ElMessage.error('请勿上传同名MP3文件');
			event.target.value = '';
			loadingInstance.close();
			return;
		}
		// 检查文件类型，只允许MP3
		if (!file.type.startsWith('audio/') && !file.name.toLowerCase().endsWith('.mp3')) {
			ElMessage.error('请上传MP3格式的音频文件');
			event.target.value = ''; // 清空文件输入以便重新选择
			loadingInstance.close();
			return;
		}
		
		// 检查文件大小（最大100MB）
		const maxSize = 100 * 1024 * 1024; // 100MB
		if (file.size > maxSize) {
			ElMessage.error('文件大小不能超过100MB');
			event.target.value = ''; // 清空文件输入以便重新选择
			loadingInstance.close();
			return;
		}
		
		// 验证通过，关闭权限检查loading
		loadingInstance.close();
		
		isUploading.value = true;
		
		// 设置上传文件信息
		uploadFile.value = {
			name: file.name,
			size: file.size,
			loaded: 0,
			percent: 0,
			type: file.type
		};
		
		// 调用dubbing API获取OSS上传凭证
		const response = await dubbing({ userId: getUserId(), fileType: 'mp3' });
		
		// 去掉文件名的后缀
		const callbackFileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');
		
		const formData = new FormData();
		// 添加OSS需要的参数
		formData.append('OSSAccessKeyId', response.accessKeyId);
		formData.append('policy', response.policy);
		formData.append('signature', response.signature);
		formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`);
		formData.append('file', file);
		
		// 使用XHR上传文件以跟踪进度
		const xhr = new XMLHttpRequest();
		
		// 保存上传请求引用
		uploadRequest.value = xhr;
		
		// 设置进度监听
		xhr.upload.onprogress = (e) => {
			if (e.lengthComputable) {
				uploadFile.value.percent = Math.round(e.loaded / e.total * 100);
				uploadFile.value.loaded = e.loaded;
			}
		};
		
		// 上传完成后的处理
		xhr.onload = async () => {
			try {
				if (xhr.status >= 200 && xhr.status < 300) {
					const userId = getUserId();
					
					// 获取音频时长
					let durationInSeconds = 0;
					try {
						// 从 handleFileChange 的参数 event 中获取原始 File 对象
						// 注意：这里的 'file' 实际上是 handleFileChange(event) 中的 event.target.files[0]
						// 因此，我们需要确保能访问到这个原始的 File 对象。
						// 假设 'originalFile' 变量在外部作用域已经正确引用了原始 File 对象。
						// 如果 'fileInputRef.value.files[0]' 是正确的引用方式，则使用它。
						const durationInfo = await getAudioDuration(file); // 'file' 是 handleFileChange 的参数
						durationInSeconds = durationInfo.durationInSeconds;
						console.log(`成功获取音频时长: ${durationInSeconds}秒, 格式化: ${durationInfo.formattedDuration}`);
					} catch (error) {
						console.error('获取音频时长失败，使用默认值 0:', error);
					}

					// 调用callbackOss接口
					const callbackResponse = await callbackOss({
						userId: userId,
						materialName: callbackFileNameWithoutExt, // 使用之前为回调准备的文件名
						ossPath: response.key.replace(/[^\/]+$/, '') + file.name, // ossPath 仍然使用原始文件名
						fileSize: String(file.size),
						fileExtension: 'mp3', // 文件扩展名
						tagNames: '1', // 标签名称，默认为'1'
						materialType: 'audio', // 素材类型
						isPrivate: '1', // 是否私有，默认为'1'（私有）
						storage_path: response.key.replace(/[^\/]+$/, '') + file.name, // 存储路径，与ossPath一致
						duration: durationInSeconds // 添加时长字段
					});
					
					// 更新文件信息
					uploadFile.value = {
						...uploadFile.value,
						name: callbackResponse.filename || file.name,
						url: callbackResponse.url,
						percent: 100,
						loaded: file.size
					};
					
					ElMessage.success('音乐上传成功');
					
					// 上传成功后，自动查询最新的"我的音乐"
					loadMyMusic();
				} else {
					throw new Error(xhr.statusText || '上传失败');
				}
			} catch (error) {
				console.error('处理错误:', error);
				ElMessage.error(error.message || '文件处理失败');
			} finally {
				isUploading.value = false;
				uploadRequest.value = null;
			}
		};
		
		// 错误处理
		xhr.onerror = (error) => {
			console.error('上传错误:', error);
			ElMessage.error('文件上传失败');
			isUploading.value = false;
			uploadFile.value.percent = 0;
			uploadRequest.value = null;
		};
		
		// 发送请求
		xhr.open('POST', response.host, true);
		xhr.send(formData);
		
		// 重置input，使相同文件能够重复选择
		event.target.value = '';
	} catch (error) {
		console.error('处理错误:', error);
		ElMessage.error('上传准备失败: ' + error.message);
		isUploading.value = false;
		loadingInstance && loadingInstance.close();
		uploadFile.value = {
			name: '',
			size: 0,
			loaded: 0,
			percent: 0,
			type: ''
		};
		uploadRequest.value = null;
		// 重置文件输入
		if (event.target) {
			event.target.value = '';
		}
	}
};

// 格式化文件大小的工具函数
const formatFileSize = (bytes) => {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取文件大小的工具函数
const getFileSizeFromURL = async (url) => {
	try {
		const response = await fetch(url, { method: 'HEAD' });
		const contentLength = response.headers.get('Content-Length');
		return contentLength ? parseInt(contentLength) : 0;
	} catch (error) {
		console.error('获取文件大小失败:', error);
		return 0; // 默认值
	}
};

const previewPanelRef = ref(null)

// 预定义精美的渐变色组合数组
const gradientColors = [
	'linear-gradient(135deg, #0AAF60, #A4CB55, #7ED4AD)', // 绿色主题
	'linear-gradient(135deg, #667eea, #764ba2)', // 蓝紫渐变
	'linear-gradient(135deg, #f093fb, #f5576c)', // 粉红渐变  
	'linear-gradient(135deg, #4facfe, #00f2fe)', // 蓝色渐变
	'linear-gradient(135deg, #43e97b, #38f9d7)', // 薄荷绿渐变
	'linear-gradient(135deg, #fa709a, #fee140)', // 橙粉渐变
	'linear-gradient(135deg, #a8edea, #fed6e3)', // 清新渐变
	'linear-gradient(135deg, #ffecd2, #fcb69f)', // 暖橙渐变
	'linear-gradient(135deg, #ff9a9e, #fecfef)', // 樱花渐变
	'linear-gradient(135deg, #a18cd1, #fbc2eb)', // 紫粉渐变
]

// 根据索引获取渐变色的函数
const getGradientColor = (index) => {
	return gradientColors[index % gradientColors.length]
}
</script>

<style lang="scss" scoped>
.music-audio-container {
	display: grid;
	grid-template-rows: auto auto minmax(0, 1fr); /* 第一行Headbar，第二行OperationBar，第三行main-content占满剩余空间 */
	row-gap: 35px; /* 添加行间距 */
	height: 100vh;
	background: #f7f7f9;
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */

	:deep(.headbar-container) {
		grid-row: 1; /* Headbar在第一行 */
		width: 100%;
	}

	:deep(.operation-bar-container) {
		grid-row: 2; /* OperationBar在第二行 */
		width: 100%;
	}

	// 主要内容区域
	.main-content {
		grid-row: 3; /* 放在第三行 */
		display: flex;
		padding: 0 20px 20px 20px;
		margin-top: 0; /* 移除margin-top */
		background: #f7f7f9;
		overflow: hidden; // 防止内容溢出

		// 左侧区域
		.left-section {
			display: flex;
			background: #fff;
			border-radius: 8px;
			margin-right: 20px;
			overflow: hidden; // 防止内容溢出
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

			// 音频库区域
			.audio-library {
				flex: 0 0 505px;
				width: 505px;
				min-width: unset;
				background: #fff;
				border-radius: 0 8px 8px 0;
				padding: 20px;
				display: flex;
				flex-direction: column;
				height: 100%;
				z-index: 2;
				overflow: hidden; // 防止内容溢出

				.tools-wrapper {
					margin-bottom: 20px;
					position: relative;
					max-width: 100%;
					width: 100%;

					.tools-container {
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 8px;
						background: transparent;
						border-radius: 4px;
						padding: 8px;
						position: relative;
						height: 52px;

						&::after {
							content: '';
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 1px;
							background-color: #0000001A;
						}
					}

					.tools-bar {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
					}

					.tool-item {
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						padding: 0;
						position: relative;
						flex: 1;
						height: 52px;

						&::after {
							content: '';
							position: absolute;
							bottom: -2px;
							left: 50%;
							width: 120px;
							height: 0;
							background-color: transparent;
							transition: all 0.3s;
							transform: translateX(-50%);
						}

						&.active {
							&::after {
								height: 4px;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								opacity: 1;
								z-index: 1;
							}
						}

						&:hover {
							&::after {
								height: 4px;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								opacity: 0.5;
								z-index: 1;
							}
						}

						.tool-icon {
							height: 52px;
							width: 120px;
							object-fit: contain;
						}
					}
				}

				// 音频列表
				.audio-list {
					flex: 1;
					display: flex;
					flex-direction: column;
					overflow: hidden; // 防止内容溢出

					// 按钮栏
					.button-bar {
						display: flex;
						gap: 10px;
						margin-bottom: 15px;

						.action-btn {
							width: 106px;
							height: 34px;
							border-radius: 4px;
							cursor: pointer;
							font-size: 14px;
							transition: all 0.3s;

							&.green-btn {
								background: #0AAF60;
								color: white;
								border: none;

								&:hover {
									opacity: 0.9;
								}
							}

							&.white-btn {
								background: #ffffff;
								color: #0AAF60;
								border: 1px solid #0AAF60;
								display: flex;
								align-items: center;
								justify-content: center;
								gap: 5px;

								.btn-icon {
									width: 16px;
									height: 16px;
									object-fit: contain;
									filter: invert(55%) sepia(87%) saturate(433%) hue-rotate(101deg) brightness(96%) contrast(94%);
								}

								&:hover {
									background: #f0f9eb;
								}
							}
						}
					}

					// 上传进度条样式
					.upload-progress {
						background: #fff;
						border-radius: 4px;
						padding: 12px;
						margin-bottom: 15px;
						box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

						.progress-info {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 8px;

							.file-name {
								font-size: 14px;
								color: #303133;
								flex: 1;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							}

							.progress-text {
								font-size: 14px;
								color: #0AAF60;
								margin-left: 10px;
							}
						}

						.progress-bar {
							height: 4px;
							background: #E4E7ED;
							border-radius: 2px;
							overflow: hidden;
							margin-bottom: 4px;

							.progress-fill {
								height: 100%;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								transition: width 0.3s ease;
							}
						}

						.progress-size {
							font-size: 12px;
							color: #909399;
							text-align: right;
						}
					}

					// 类别导航栏
					.category-nav {
						display: flex;
						overflow-x: auto;
						padding: 5px 0;
						margin-bottom: 15px;
						border-bottom: none;

						.category-item {
							padding: 8px 0;
							cursor: pointer;
							transition: all 0.3s;
							white-space: nowrap;
							color: #606266;
							font-size: 14px;
							position: relative;

							// 给第二个及之后的元素设置左侧间距
							&:not(:first-child) {
								margin-left: 18px;
							}

							&.active {
								color: #0AAF60;
								font-weight: 500;
							}

							&:hover {
								color: #0AAF60;
							}
						}

						&::-webkit-scrollbar {
							height: 3px;
						}

						&::-webkit-scrollbar-thumb {
							background: #E4E7ED;
							border-radius: 3px;
						}

						&::-webkit-scrollbar-track {
							background: #F5F7FA;
						}
					}

					// 音频列表内容
					.audio-items {
						flex: 1; // 占据剩余空间
						overflow-y: auto;
						padding-right: 5px;

						// 隐藏滚动条 - WebKit浏览器(Chrome/Safari)
						&::-webkit-scrollbar {
							width: 0;
							background: transparent;
						}

						// 隐藏滚动条 - Firefox
						scrollbar-width: none;

						// 隐藏滚动条 - IE/Edge
						-ms-overflow-style: none;

						.audio-item {
							display: flex;
							align-items: center;
							padding: 0;
							border-radius: 4px;
							margin-bottom: 8px;
							background: #ffffff;
							transition: all 0.3s;
							border: 1px solid transparent;
							overflow: hidden;

							&:hover {
								background: #ffffff;
								border: 1px solid #0AAF60;
								box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

								.volume-controls {
									opacity: 1;
								}
							}

							&.playing {
								background: #f0f9eb;
								border-left: 3px solid #0AAF60;

								.volume-controls {
									opacity: 1;
								}
							}

							.audio-thumbnail {
								width: 68px;
								height: 68px;
								position: relative;
								flex-shrink: 0;

								.play-pause-btn {
									position: absolute;
									top: 0;
									left: 0;
									right: 0;
									bottom: 0;
									display: flex;
									align-items: center;
									justify-content: center;
									cursor: pointer;

									.player-icon {
										width: 24px;
										height: 24px;
										object-fit: contain;
									}

									.play-icon {
										display: none;
									}

									.pause-icon {
										display: none;
									}
								}
							}

							.audio-content {
								flex: 1;
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								padding: 10px 15px;

								.audio-info {
									display: flex;
									justify-content: space-between;
									align-items: center;
									margin-bottom: 8px;
									
									.audio-name {
										font-size: 14px;
										font-weight: 500;
										color: #303133;
										flex: 1;
									}
									
									.audio-actions {
										display: flex;
										gap: 12px;
										justify-content: flex-end;
										transition: opacity 0.3s ease;
										
										.action-text {
											font-size: 14px;
											cursor: pointer;
											transition: all 0.3s;
											color: #0AAF60;
											
											&:hover {
												opacity: 0.8;
											}
										}
									}
								}

								.audio-control-bar {
									display: flex;
									align-items: center;
									justify-content: space-between;
									height: 30px;
									
									.audio-text-info {
										// min-width: 120px;
										font-size: 12px;
										color: #909399;
									}
									
									.volume-controls {
										flex: 1;
										display: flex;
										align-items: center;
										position: relative;
										// padding: 0 10px;
										
										.volume-icon {
											width: 24px;
											height: 24px;
											background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230AAF60'%3E%3Cpath d='M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z'/%3E%3C/svg%3E");
											background-repeat: no-repeat;
											background-position: center;
											background-size: contain;
											cursor: pointer;
											margin-right: 10px;
											
											&.muted {
												background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23909399'%3E%3Cpath d='M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z'/%3E%3C/svg%3E");
											}
										}
									}
								}
							}
						}

						.no-results {
							text-align: center;
							padding: 30px;
							color: #909399;
							font-size: 14px;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							height: 100%;

							.empty-img {
								width: 120px;
								height: 120px;
								object-fit: contain;
								margin-bottom: 16px;
							}

							.empty-text {
								color: #909399;
								font-size: 14px;
							}
						}

						// 加载状态样式
						.loading-state {
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							height: 200px;

							.loading-spinner {
								width: 40px;
								height: 40px;
								border: 3px solid #f3f3f3;
								border-top: 3px solid #0AAF60;
								border-radius: 50%;
								animation: spin 1s linear infinite;
								margin-bottom: 16px;
							}

							.loading-text {
								color: #0AAF60;
								font-size: 14px;
							}

							@keyframes spin {
								0% {
									transform: rotate(0deg);
								}

								100% {
									transform: rotate(360deg);
								}
							}
						}

						// 错误状态样式
						.error-state {
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							height: 200px;

							.error-icon {
								width: 40px;
								height: 40px;
								background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23F56C6C'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
								background-size: contain;
								background-repeat: no-repeat;
								background-position: center;
								margin-bottom: 16px;
							}

							.error-text {
								color: #F56C6C;
								font-size: 14px;
								margin-bottom: 16px;
							}
						}
					}
				}
			}
		}

		// 右侧区域使用已封装的组件
		.right-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			min-width: 400px;
		}
	}
}

.volume-controls {
	flex: 1;
	display: flex;
	align-items: center;
	position: relative;
	padding: 0 0 0 10px;
	// 增加宽度，提供更多空间给音量条
	width: 120%;
	
	.volume-icon {
		width: 24px;
		height: 24px;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230AAF60'%3E%3Cpath d='M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z'/%3E%3C/svg%3E");
		background-repeat: no-repeat;
		background-position: center;
		background-size: contain;
		cursor: pointer;
		margin-right: 10px;
		
		&.muted {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23909399'%3E%3Cpath d='M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z'/%3E%3C/svg%3E");
		}
	}
	
	.volume-track {
		position: relative;
		width: 100%; // 增长音量条长度
		height: 4px; // 将高度从6px减小到4px，使其变细
		background-color: #e0e0e0;
		border-radius: 2px; // 调整圆角以匹配新高度
		pointer-events: none;
		
		.volume-fill {
			height: 100%;
			background-color: #0AAF60;
			border-radius: 2px; // 调整圆角以匹配新高度
			pointer-events: none;
			transition: width 0.1s ease;
			
			&.active {
				background-color: #0AAF60;
				box-shadow: 0 0 4px rgba(10, 175, 96, 0.5);
			}
		}
	}
	
	input[type="range"] {
		position: absolute;
		width: calc(100% - 30px); // 调整与音量条宽度一致
		height: 100%;
		margin: 0;
		opacity: 0;
		cursor: pointer;
		left: 34px;
	}
}
</style>