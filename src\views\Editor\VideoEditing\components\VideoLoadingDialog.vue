<template>
	<div class="video-loading-dialog" v-if="visible">
		<div class="dialog-content" :style="progressBorderStyle">
			<!-- 关闭按钮 -->
			<div class="close-btn" @click="handleClose">
				<i class="el-icon-close">×</i>
			</div>

			<!-- 视频缩略图 - 修改为使用选中视频的缩略图 -->
			<div class="video-thumbnail" :style="thumbnailStyle"></div>

			<!-- 加载提示 -->
			<div class="loading-info">
				<div class="loading-text">视频正在生成...{{ progress }}%</div>

				<!-- 进度条 -->
				<div class="progress-bar-container">
					<div class="progress-bar" :style="{ width: `${progress}%` }"></div>
				</div>

				<!-- 取消按钮 -->
				<div class="cancel-text">您可以中途关闭该窗口，由系统自动保存结果</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	thumbnailUrl: {
		type: String,
		default: ''
	},
	progress: {
		type: Number,
		default: 20
	},
	// 添加videos属性，用于接收视频数组
	videos: {
		type: Array,
		default: () => []
	}
})

const emit = defineEmits(['update:visible', 'close'])

// 计算获取当前显示的缩略图URL
const currentThumbnailUrl = computed(() => {
	// 优先使用videos数组中第一个视频的缩略图
	if (props.videos && props.videos.length > 0 && props.videos[0].thumbnailUrl) {
		return props.videos[0].thumbnailUrl;
	}
	// 如果没有视频或缩略图，则回退到传入的thumbnailUrl属性
	return props.thumbnailUrl;
});

// 计算缩略图样式
const thumbnailStyle = computed(() => {
	const url = currentThumbnailUrl.value;
	return {
		backgroundImage: url ? `url(${url})` : 'none',
		backgroundColor: url ? 'transparent' : '#333'
	}
})

// 计算进度边框样式
const progressBorderStyle = computed(() => {
	// 根据进度设置边框渐变
	// 当进度达到100%时，使用纯色边框
	if (props.progress >= 100) {
		return {
			background: '#fff',
			boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
			border: '5px solid #0AAF60',
			borderRadius: '8px'
		}
	}

	// 否则使用渐变边框
	return {
		background: '#fff',
		boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
		// 使用 conic-gradient 创建环绕边框效果
		border: '5px solid transparent',
		backgroundClip: 'padding-box',
		position: 'relative',
		'&::before': {
			content: '""',
			position: 'absolute',
			top: 0,
			right: 0,
			bottom: 0,
			left: 0,
			margin: '-3px',
			borderRadius: 'inherit',
			background: `conic-gradient(#0AAF60 ${props.progress * 0.5}%, #75D2A6 ${props.progress * 0.8}%, #FFFFFF ${props.progress}%)`,
			zIndex: -1,
		},
		// 更简单的替代方案：只在顶部显示进度色边框
		borderImage: `linear-gradient(to right, #0AAF60 ${props.progress * 0.4}%, #75D2A6 ${props.progress * 0.7}%, #FFFFFF ${props.progress}%) 1`
	}
})

// 处理关闭
const handleClose = () => {
	emit('update:visible', false)
	emit('close')
}

// 监听进度变化
watch(() => props.progress, (newProgress) => {
	// 当进度达到100%，可以在这里添加自动关闭逻辑
	if (newProgress >= 100) {
		setTimeout(() => {
			handleClose()
		}, 1000) // 延迟1秒关闭
	}
})
</script>

<style lang="scss" scoped>
.video-loading-dialog {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;

	.dialog-content {
		width: 470px;
		background-color: #fff;
		border-radius: 8px;
		overflow: hidden;
		position: relative;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

		// 添加进度边框
		&::before {
			content: '';
			position: absolute;
			top: -3px;
			left: -3px;
			right: -3px;
			bottom: -3px;
			border-radius: 11px;
			background: v-bind('props.progress >= 100 ? "#0AAF60" : "conic-gradient(#0AAF60 calc(var(--progress) * 0.5%), #75D2A6 calc(var(--progress) * 0.8%), white calc(var(--progress) * 1%))"');
			z-index: -1;
			--progress: v-bind('progress');
		}

		.close-btn {
			position: absolute;
			top: 10px;
			right: 10px;
			width: 24px;
			height: 24px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 20px;
			cursor: pointer;
			z-index: 10;
			background-color: rgba(0, 0, 0, 0.4);
			border-radius: 50%;

			&:hover {
				background-color: rgba(0, 0, 0, 0.6);
			}
		}

		.video-thumbnail {
			width: 100%;
			height: 250px;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			position: relative;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.3);
			}
		}

		.loading-info {
			padding: 15px 20px 20px;
			display: flex;
			flex-direction: column;
			align-items: center;

			.loading-text {
				font-size: 16px;
				font-weight: 500;
				color: #333;
				margin-bottom: 15px;
				text-align: center;
			}

			.progress-bar-container {
				width: 100%;
				height: 6px;
				background-color: #EBEEF5;
				border-radius: 3px;
				overflow: hidden;
				margin-bottom: 15px;

				.progress-bar {
					height: 100%;
					background: linear-gradient(90deg, #0AAF60 0%, #A4CB55 100%);
					border-radius: 3px;
					transition: width 0.3s ease;
				}
			}

			.cancel-text {
				font-size: 14px;
				color: #909399;
				text-align: center;
			}
		}
	}
}
</style>