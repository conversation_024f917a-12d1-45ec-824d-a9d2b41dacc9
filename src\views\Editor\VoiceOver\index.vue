<template>
	<div class="voice-over-container">
		<!-- 使用Headbar组件替换原来的标题栏 -->
		<Headbar />

		<!-- 使用OperationBar组件替换原来的操作栏 -->
		<OperationBar @action="handleBarAction" />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<!-- 左侧区域 - 配音选择界面 -->
			<div class="left-section">
				<!-- 左侧菜单 -->
				<LeftMenu />
				
				<!-- 配音工具区域 -->
				<div class="voice-over-tools">
					<!-- 顶部分类导航 -->
					<div class="category-nav">
						<div class="category-container">
							<!-- 主要分类：voiceType - 注释掉第一个分类 -->
							<!-- <div class="main-categories">
								<div 
									v-for="(category, index) in mainCategories" 
									:key="'main-'+index"
									:class="['category-item', { active: currentMainCategory === category.id }]"
									@click="selectMainCategory(category.id)">
									{{ category.name }}
								</div>
							</div> -->
							
							<!-- 性别分类：genders -->
							<div class="main-categories" v-if="genderCategories.length > 1">
								<div 
									v-for="(gender, index) in genderCategories" 
									:key="'gender-'+index"
									:class="['category-item', { active: currentGender === gender.id }]"
									@click="selectGender(gender.id)">
									{{ gender.name }}
								</div>
							</div>
							
							<!-- 场景分类：sceneMetadata -->
							<div class="main-categories" v-if="sceneCategories.length > 1">
								<div 
									v-for="(scene, index) in sceneCategories" 
									:key="'scene-'+index"
									:class="['category-item', { active: currentScene === scene.id }]"
									@click="selectScene(scene.id)">
									{{ scene.name }}
								</div>
							</div>
							
							<!-- 推荐标签分类：来自场景的recommend_tags -->
							<div class="main-categories" v-if="recommendTagCategories.length > 1">
								<div 
									v-for="(tag, index) in recommendTagCategories" 
									:key="'tag-'+index"
									:class="['category-item', { active: currentRecommendTag === tag.id }]"
									@click="selectRecommendTag(tag.id)">
									{{ tag.name }}
								</div>
							</div>
							
							<!-- 子分类 -->
							<div class="main-categories" v-if="subCategories.length > 0">
								<div 
									v-for="(category, index) in subCategories" 
									:key="'sub-'+index"
									:class="['category-item', { active: currentSubCategory === category.id }]"
									@click="selectSubCategory(category.id)">
									{{ category.name }}
								</div>
							</div>
							
							<!-- 详细分类 -->
							<div class="main-categories" v-if="detailedCategories.length > 0">
								<div 
									v-for="(category, index) in detailedCategories" 
									:key="'detail-'+index"
									:class="['category-item', { active: currentDetailedCategory === category.id }]"
									@click="selectDetailedCategory(category.id)">
									{{ category.name }}
								</div>
							</div>
						</div>
					</div>

					<!-- 声音角色选择区域 -->
					<div class="voice-grid">
						<div class="voice-row">
							<div class="voice-item-wrapper" 
								 v-for="(voice, index) in voices" 
								 :key="index" 
								 @click="selectVoice(index)" 
								 :class="getVoiceItemClass(voice, index)">
								<!-- 精品和珍享图片 -->
								<div class="position_image overflow-hidden" v-if="voice.tag === '臻享' || voice.tag === '精品'">
									<img :src="getTagIcon(voice.tag)" :alt="voice.tag || '音色标签'" class="premium-badge">
								</div>
								
								<div class="voice-item">
									<!-- 头像区域 -->
									<div class="voice-avatar margin_t-14">
										<span class="avatar-circle">
											<img :src="voice.avatarUrl || voice.avatar || '@/assets/img/image111.png'" alt="音色头像">
										</span>
										<!-- 播放按钮 - 只在有音频URL且未播放时显示 -->
										<div class="play-button width-20 height-20 flex flex_a_i-center flex_j_c-center"
											 @click.stop="playAudio(voice)" 
											 v-show="!voice.isPlaying && (voice.audioUrl || voice.demoUrl)">
											<i class="iconfont icon-bofang" style="font-size: 12px; color: rgb(255, 255, 255);"></i>
										</div>
										<!-- 暂停按钮 - 只在正在播放时显示 -->
										<div class="pause-button width-20 height-20 flex flex_a_i-center flex_j_c-center"
											 @click.stop="pauseAudio(voice)"
											 v-show="voice.isPlaying">
											<i class="iconfont icon-pause-fill" style="font-size: 12px; color: rgb(255, 255, 255);"></i>
										</div>
									</div>
									<!-- 名称 -->
									<div class="voice-name margin_t-4 font-size-14">{{ voice.name }}</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 底部控制区 -->
					<div class="control-bar">
						<div class="volume-control">
							<ElPopover
								placement="top"
								:width="500"
								trigger="click"
								v-model:visible="pitchSliderVisible"
								popper-class="gradient-slider-popover"
								popper-style="background-color: #000000; opacity: 0.75; height: 38px; padding: 0 10px; display: flex; align-items: center;"
								:offset="20"
								:transition="'none'"
								:hide-after="0"
								:popper-options="{
									modifiers: [
										{
											name: 'offset',
											options: {
												offset: [0, 20]
											},
										},
										{
											name: 'preventOverflow',
											options: {
												padding:20,
											},
										}
									],
								}"
							>
								<template #reference>
									<div class="pitch-display">
										<span class="pitch-label">语调</span>
										<span class="pitch-value">{{ pitchValue > 0 ? '+' + pitchValue : pitchValue }}</span>
									</div>
								</template>
								
								<!-- 弹出的滑块控制器 -->
								<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
									<div class="el-slider flex-item_f-8 slider gradient-slider">
										<ElSlider 
											v-model="pitchValue"
											:min="pitchMin"
											:max="pitchMax"
											:step="pitchStep"
											:show-stops="showPitchMarks"
											:format-tooltip="(val) => val > 0 ? '+' + val : val"
											class="gradient-slider"
										/>
									</div>
									<span class="font-size-14 margin_l-10">{{ pitchValue }}</span>
								</div>
							</ElPopover>
						</div>
						
						<div class="action-buttons">
							<button class="listen-btn" @click="handleListenButtonClick">
								<template v-if="!isPlaying">试听</template>
								<template v-else>
									<span class="control-icons">
										<i class="pause-icon"></i>
										<i class="divider"></i>
										<i class="stop-icon"></i>
									</span>
								</template>
							</button>
							<button class="generate-btn" @click="generateVoiceOver">合成音频</button>
						</div>
					</div>

					<!-- 音频播放器控件 - 默认隐藏，只有生成音频后才显示 -->
					<div class="audio-player-wrapper" v-show="showAudioPlayer" :class="{'player-disabled': !audioPlayer || !audioPlayer.src}">
						<div class="audio-player-control">
							<div class="player-button" @click="handlePlayerButtonClick" :class="{'disabled-button': !showAudioPlayer}">
								<i v-if="!isPlayerPlaying" class="player-icon"></i>
								<i v-else class="pause-icon"></i>
							</div>
							<div class="progress-bar" 
								 @click="handleProgressBarClick"
								 @mousedown="startDragging"
								 :class="{'disabled-progress': !showAudioPlayer}">
								<div class="progress-filled" :style="{width: progress + '%'}"></div>
							</div>
							<div class="player-time" :class="{'disabled-text': !showAudioPlayer}">
                                {{ showAudioPlayer ? timeDisplay : '00:00:00/00:00:00' }}
                            </div>
						</div>
					</div>
				</div>
			</div>

			<!-- 右侧预览区域 - 使用PreviewPanel组件 -->
			<PreviewPanel 
				ref="previewPanelRef"
				v-model:title="previewTitle"
				v-model:content="previewContent"
				:musicList="musicList"
				:videoList="videoList"
				:roleList="roleList"
				:isVideoEditingPage="false"
				@generate-video="handleGenerateVideo"
				@add-role="handleAddRole"
				@add-music="handleAddMusic"
				@add-video="handleAddVideo"
				@volume-change="handleVolumeChange"
			/>
		</div>

		<!-- 在组件末尾添加音乐对话框组件 -->
		<MusicDialog
			v-model:visible="musicDialogVisible"
			:material-list="musicList"
			@close="musicDialogVisible = false"
			@confirm="handleMusicDialogConfirm"
			@remove="handleMusicDialogRemove"
			@togglePlay="handleMusicDialogTogglePlay"
			@add-music="handleMusicDialogAddMusic"
		/>
	</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, getCurrentInstance } from 'vue'
import OperationBar from '@/views/Editor/components/common/OperationBar.vue'
import PreviewPanel from '@/views/Editor/components/PreviewPanel.vue'
import { ElMessage, ElLoading, ElPopover, ElSlider, ElOption, ElSelect } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useMusicStore } from '@/stores/modules/musicStore'
import { usePreviewStore } from '@/stores/previewStore'
import { useloginStore } from '@/stores/login' // 添加这行，引入loginStore
import LeftMenu from '@/views/Editor/components/common/LeftMenu.vue'
import { getAllVoices, getAllVoiceMetadata, queryVoicesByCategory, generateAudio, submitAudioSynthesisJob } from '@/api/voiceOver'
import MusicDialog from '@/components/MusicDialog.vue'  // 添加音乐对话框组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'

const router = useRouter()
const route = useRoute()
const musicStore = useMusicStore()
const previewStore = usePreviewStore()
const loginStore = useloginStore() // 获取 loginStore 实例
const { proxy } = getCurrentInstance() // 获取组件实例，用于访问$modal

// ===== 状态管理 =====
// 预览面板相关状态
const previewTitle = ref(previewStore.title || '')
const previewContent = ref(previewStore.content || '')
const musicList = ref([])
const videoList = ref([])

// 配音相关状态
const selectedVoice = ref(-1)  // 当前选中的音色索引，-1表示未选中
const pitchValue = ref(0)    // 语调调整值，范围-12到12，默认为0
const defaultAvatar = '/path/to/default/avatar.png'

// 音色列表数据
const voices = ref([])
// 添加存储所有音色数据的状态
const allVoices = ref([])
// 添加已筛选的音色数据状态
const filteredVoices = ref([])

// 音频播放相关状态
const audioPlayer = ref(null)  // 音频播放器实例
const currentPlayingVoice = ref(null)  // 当前正在播放的音色
const audioDuration = ref(0) // 音频总时长（秒）
const currentTime = ref(0)    // 当前播放位置（秒）
const progress = ref(0)       // 播放进度（百分比）
const isDragging = ref(false) // 是否正在拖动进度条
const audioTimer = ref(null)  // 播放进度定时器
const showAudioPlayer = ref(false) // 控制播放器显示与隐藏

// 播放器按钮状态（与试听状态区分开）
const isPlayerPlaying = ref(false)

// 分类相关状态
const voiceMetadata = ref({
	voiceType: [],     // 第一个分类：音色类型
	genders: [],       // 第二个分类：性别
	sceneMetadata: [], // 第三个分类：场景元数据
	recommendTags: [], // 第四个分类：场景推荐标签（从第三项中提取）
	categories: []     // 保留原有分类数据结构
})

// 当前选中的分类值
const currentMainCategory = ref('all')    // 主分类
const currentGender = ref('all')          // 性别分类
const currentScene = ref('all')           // 场景分类
const currentRecommendTag = ref('all')    // 推荐标签
const currentSubCategory = ref('all')     // 子分类
const currentDetailedCategory = ref('all') // 详细分类

// 试听按钮状态变量
const isPlaying = ref(false)

// 语调相关状态
const pitchSliderVisible = ref(false)  // 控制语调滑块弹出层的显示
const pitchMin = ref(-12)              // 语调最小值
const pitchMax = ref(12)               // 语调最大值
const pitchStep = ref(1)               // 语调步长
const showPitchMarks = ref(false)       // 是否显示刻度标记

// 添加角色列表管理
const roleList = ref([]) // 改回原始声明，不再从 previewStore 获取初始值

// 添加音乐对话框状态变量
const musicDialogVisible = ref(false)
// 添加对 PreviewPanel 的引用
const previewPanelRef = ref(null)

// 添加 getUserId 函数，与其他组件保持一致
const getUserId = () => {
    return loginStore.userId || '11'
}

// 检查用户是否已登录
const checkUserLogin = () => {
    // 从localStorage获取用户信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;
    
    try {
        const userData = JSON.parse(userStorage);
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== '';
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
}

// 声音查询筛选参数
const filterParams = ref({
	voiceType: '',    // 主分类 
	gender: '',       // 性别
	scene: '',        // 场景
	recommendTag: '', // 推荐标签
	subCategory: '',  // 子分类
	detailedCategory: '', // 详细分类
	tts: '3'          // 添加tts参数，值为'3'
})

// ===== 计算属性 =====
// 主分类列表，包含"全部"选项和API返回的voiceType数据
const mainCategories = computed(() => {
	return [
		{ id: 'all', name: '全部' },
		...(voiceMetadata.value.voiceType || [])
	]
})

// 性别分类列表 - 根据当前筛选状态动态计算
const genderCategories = computed(() => {
	// 获取基础数据
	const baseData = currentMainCategory.value === 'all' ? allVoices.value : filteredVoices.value;
	
	// 从筛选后的数据中提取唯一的性别值
	const availableGenders = new Set();
	baseData.forEach(voice => {
		if (voice.gender) availableGenders.add(voice.gender);
	});
	
	// 只显示有数据的性别分类
	const genderOptions = voiceMetadata.value.genders?.filter(gender => 
		gender.id === 'all' || availableGenders.has(gender.id)
	) || [];
	
	return [
		{ id: 'all', name: '全部' },
		...genderOptions
	]
})

// 场景分类列表 - 根据当前性别筛选状态动态计算
const sceneCategories = computed(() => {
	// 获取基于当前筛选的数据
	let baseData = allVoices.value;
	
	// 应用主分类筛选
	if (currentMainCategory.value !== 'all') {
		baseData = baseData.filter(voice => voice.tag === currentMainCategory.value);
	}
	
	// 应用性别筛选
	if (currentGender.value !== 'all') {
		baseData = baseData.filter(voice => voice.gender === currentGender.value);
	}
	
	// 从筛选后的数据中提取唯一的场景值
	const availableScenes = new Set();
	baseData.forEach(voice => {
		if (voice.sceneCategory) availableScenes.add(voice.sceneCategory);
	});
	
	// 只显示有数据的场景分类
	const sceneOptions = voiceMetadata.value.sceneMetadata?.filter(scene => 
		scene.id === 'all' || availableScenes.has(scene.id)
	) || [];
	
	return [
		{ id: 'all', name: '全部' },
		...sceneOptions
	]
})

// 推荐标签分类列表 - 与场景分类联动，基于筛选结果
const recommendTagCategories = computed(() => {
	// 添加"全部"选项
	const allOption = [{ id: 'all', name: '全部' }];
	
	// 获取基于当前筛选的数据
	let baseData = allVoices.value;
	
	// 应用主分类筛选
	if (currentMainCategory.value !== 'all') {
		baseData = baseData.filter(voice => voice.tag === currentMainCategory.value);
	}
	
	// 应用性别筛选
	if (currentGender.value !== 'all') {
		baseData = baseData.filter(voice => voice.gender === currentGender.value);
	}
	
	// 应用场景筛选
	if (currentScene.value !== 'all') {
		baseData = baseData.filter(voice => voice.sceneCategory === currentScene.value);
	}
	
	// 从筛选后的数据中提取唯一的推荐标签
	const availableTags = new Set();
	baseData.forEach(voice => {
		if (voice.recommendTags) {
			// 推荐标签可能是逗号分隔的多个值
			const tags = voice.recommendTags.split(',').map(tag => tag.trim());
			tags.forEach(tag => {
				if (tag) availableTags.add(tag);
			});
		}
	});
	
	// 如果选择了特定场景，则只显示该场景的推荐标签
	if (currentScene.value !== 'all') {
		const selectedScene = voiceMetadata.value.sceneMetadata?.find(
			scene => scene.id === currentScene.value
		);
		
		if (selectedScene && selectedScene.recommend_tags) {
			// 处理该场景的推荐标签
			const sceneTags = selectedScene.recommend_tags
				.map(tagObj => ({
					id: tagObj.recommend_tags || '',
					name: tagObj.recommend_tags || '',
					sceneId: selectedScene.id
				}))
				.filter(tag => tag.id && availableTags.has(tag.id)); // 过滤掉无效标签和没有对应音色的标签
			
			return [...allOption, ...sceneTags];
		}
	}
	
	// 如果没有选择特定场景或没有场景推荐标签，则显示所有标签
	const allTags = voiceMetadata.value.recommendTags || [];
	const availableTagOptions = allTags.filter(tag => availableTags.has(tag.id));
	
	return [...allOption, ...availableTagOptions];
})

// 子分类列表 - 基于选择的主分类
const subCategories = computed(() => {
	if (currentMainCategory.value === 'all') {
		return []
	}
	
	const selectedCategory = voiceMetadata.value.categories?.find(
		cat => cat.id === currentMainCategory.value
	)
	
	if (!selectedCategory || !selectedCategory.subCategories) {
		return []
	}
	
	// 添加"全部"选项
	return [
		{ id: 'all', name: '全部' },
		...selectedCategory.subCategories
	]
})

// 详细分类列表 - 基于选择的子分类
const detailedCategories = computed(() => {
	if (currentMainCategory.value === 'all' || currentSubCategory.value === 'all') {
		return []
	}
	
	const selectedCategory = voiceMetadata.value.categories?.find(
		cat => cat.id === currentMainCategory.value
	)
	
	const selectedSubCategory = selectedCategory?.subCategories?.find(
		cat => cat.id === currentSubCategory.value
	)
	
	if (!selectedSubCategory || !selectedSubCategory.detailedCategories) {
		return []
	}
	
	// 添加"全部"选项
	return [
		{ id: 'all', name: '全部' },
		...selectedSubCategory.detailedCategories
	]
})

// ===== 方法定义 =====
/**
 * 选择音色
 * @param {Number} index - 选中的音色索引
 */
const selectVoice = (index) => {
	selectedVoice.value = index
	const voiceData = voices.value[index]
	
	// 记录合成所需信息
	voiceData.voiceName = voiceData.voiceName 
	
	console.log(`选择音色: ${voiceData.name}, voiceName/ID: ${voiceData.voiceName}`)
	
	// 不再立即添加到预览区，仅设置状态
	// addRoleToPreview(voiceData) - 移除这行，仅在合成音频成功后调用
	// ElMessage.success(`已选择音色"${voiceData.name}"，点击合成音频后会添加到右侧`)
}

/**
 * 将选中的声音角色添加到预览区域
 * @param {Object} voiceData - 选中的声音数据
 * @param {String} audioUrl - 已合成的音频URL
 */
const addRoleToPreview = (voiceData, audioUrl) => {
	console.log('添加角色到预览区域:', voiceData, '音频URL:', audioUrl)
	
	// 只有在有真实音频URL时才添加角色
	if (!audioUrl) {
		console.warn('没有合成音频URL，不添加角色到预览区')
		return
	}
	
	// 准备角色数据，包含完整音色信息与合成的音频
	const roleData = {
		id: voiceData.id,
		name: voiceData.name,
		voiceName: voiceData.voiceName,
		avatar: voiceData.avatarUrl || voiceData.avatar,
		audioUrl: audioUrl, // 使用已合成的音频URL
		selectedRole: voiceData.name,
		isDemo: false // 这是真实合成的音频
	}
	
	roleList.value = [roleData]
	
	// 保存到 previewStore
	previewStore.setRole({
		name: voiceData.name,
		voiceName: voiceData.voiceName || voiceData.id,
		audioUrl: audioUrl,
		isDemo: false
	})
	
	console.log(`已添加角色"${voiceData.name}"和合成音频到右侧预览区`)
}

/**
 * 在合成音频成功后调用这个函数更新角色的实际音频URL
 * @param {String} audioUrl - 合成的实际音频URL
 */
const updateRoleAudio = (audioUrl) => {
	if (roleList.value.length > 0) {
		// 更新roleList中的音频URL
		roleList.value[0].audioUrl = audioUrl;
		roleList.value[0].isDemo = false; // 不再是示例
		
		// 更新store
		previewStore.setRole({
			name: roleList.value[0].name,
			voiceName: roleList.value[0].voiceName,
			audioUrl: audioUrl,
			isDemo: false
		});
		
		console.log('更新角色实际音频URL:', audioUrl);
	}
}

/**
 * 试听按钮点击处理
 */
const handleListenButtonClick = async () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 检查是否已选择音色
	if (selectedVoice.value < 0 || !voices.value[selectedVoice.value]) {
		ElMessage.warning('请先选择一个音色')
		return
	}
	
	// 检查预览区是否有文本内容
	if (!previewContent.value || previewContent.value.trim() === '') {
		ElMessage.warning('请先在右侧填写文案内容')
		return
	}
	
	// 获取选中的音色数据
	const selectedVoiceData = voices.value[selectedVoice.value]
	
	// 如果已经在播放，则停止播放
	if (isPlaying.value) {
		// 设置状态为非播放
		isPlaying.value = false
		isPlayerPlaying.value = false
		
		// 如果有音频正在播放，停止它
		if (audioPlayer.value) {
			audioPlayer.value.pause()
			audioPlayer.value.currentTime = 0
		}
		
		// 重置播放进度
		resetProgress()
		
		// 清除定时器
		clearProgressTimer()
		
		ElMessage.info('已停止播放')
		return
	}
	
	// 准备API请求参数 - 使用submitAudioSynthesisJob接口格式
	const params = {
		type: '1',
		userId: getUserId(),
		inputConfig: {
			// 使用预览区文本，并限制长度为500字
			inputFile: (() => {
				// 这里已确保 previewContent.value 有值
				return previewContent.value.length > 500 ? 
					previewContent.value.substring(0, 500) : 
					previewContent.value;
			})()
		},
		editingConfig: {
			format: "MP3",
			pitch_rate: Math.round((pitchValue.value / 12) * 500) || 0, // 转换为-500到500范围
			sample_rate: 16000,
			speech_rate: 0,
			voice: selectedVoiceData.voiceName, // 使用选择的音色voiceName
			volume: 50
		},
		outputConfig: {
			// 输出配置为空对象
		}
	}
	
	// 显示加载状态
	const loadingInstance = ElLoading.service({
		text: '正在生成音频...',
		background: 'rgba(255, 255, 255, 0.7)'
	})
	
	try {
		// 调用新的API并等待响应
		const response = await submitAudioSynthesisJob(params)
		console.log("试听音频合成响应:", response)
		
		// 处理成功响应
		if (response) {
			// 获取合成的音频URL
			const audioUrl = response.userData
			
			// 添加角色信息到右侧预览区
			const selectedVoiceData = voices.value[selectedVoice.value]
			addRoleToPreview(selectedVoiceData, audioUrl)
			
			// 创建或重置音频播放器
			if (!audioPlayer.value) {
				audioPlayer.value = new Audio()
				audioPlayer.value.addEventListener('ended', handleAudioEnded)
				audioPlayer.value.addEventListener('timeupdate', updateProgress)
				audioPlayer.value.addEventListener('loadedmetadata', () => {
					audioDuration.value = audioPlayer.value.duration;
					
					// 保存配音时长到Pinia store，用于费用计算
					if (previewStore && typeof audioDuration.value === 'number' && audioDuration.value > 0) {
						previewStore.setDubbingDuration(Math.round(audioDuration.value));
						console.log("已保存配音时长到Pinia:", Math.round(audioDuration.value), "秒");
					}
				})
			}
			
			// 设置音频源
			audioPlayer.value.src = audioUrl
			
			// 设置一个默认时长，等待元数据加载后更新
			audioDuration.value = 0
			
			try {
				// 尝试播放音频
				await audioPlayer.value.play()
				
				// 设置播放状态
				isPlaying.value = true
				isPlayerPlaying.value = true
				
				// 重要变更：永久显示播放器
				showAudioPlayer.value = true
				
				// 启动播放进度定时器
				startProgressTimer()
				
				// ElMessage.success('试听开始')
			} catch (playError) {
				console.error('音频播放失败:', playError)
				ElMessage.error('音频播放失败，请重试')
				isPlaying.value = false
			}
			
			// 更新角色的实际音频URL
			updateRoleAudio(audioUrl);
		} else {
			ElMessage.error('生成音频失败，请重试')
		}
	} catch (error) {
		console.error('API调用失败:', error)
		ElMessage.error('生成音频失败，请重试')
	} finally {
		loadingInstance.close()
	}
}

/**
 * 合成完整音频
 * 不限制文本长度
 */
const generateVoiceOver = async () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 检查是否已选择音色
	if (selectedVoice.value < 0 || !voices.value[selectedVoice.value]) {
		ElMessage.warning('请先选择一个音色')
		return
	}
	
	// 确保使用最新的文本内容 - 直接从store获取
	const latestContent = previewStore.content || previewContent.value;
	
	// 检查预览区是否有文本内容
	if (!latestContent || latestContent.trim() === '') {
		ElMessage.warning('请先在右侧填写文案内容')
		return
	}
	
	// 获取选中的音色数据
	const selectedVoiceData = voices.value[selectedVoice.value]
	
	// 显示加载提示
	const loadingInstance = ElLoading.service({
		lock: true,
		text: '正在合成音频，请稍候...',
		background: 'rgba(0, 0, 0, 0.7)'
	})
	
	try {
		// 准备API请求参数 - 使用新的submitAudioSynthesisJob接口格式
		const params = {
			userId: getUserId(),
			type: '1',
			inputConfig: {
				inputFile: latestContent // 使用最新的内容进行配音
			},
			editingConfig: {
				format: "MP3",
				pitch_rate: Math.round((pitchValue.value / 12) * 500) || 0, // 转换为-500到500范围
				sample_rate: 16000,
				speech_rate: 0,
				voice: selectedVoiceData.voiceName, // 使用选择的音色voiceName
				volume: 50
			},
			outputConfig: {
				// 输出配置为空对象
			}
		}
		
		// 调用新的API并等待响应
		const response = await submitAudioSynthesisJob(params)
		console.log("音频合成任务提交响应:", response)
		
		// 处理成功响应
		if (response) {
			// 关闭加载提示
			loadingInstance.close()
			
			// 显示成功消息
			// ElMessage.success('音频合成成功')
			
			// 获取音频URL
			const audioUrl = response.userData
			
			// 添加角色信息到右侧预览区
			const selectedVoiceData = voices.value[selectedVoice.value]
			addRoleToPreview(selectedVoiceData, audioUrl)
			
			// 更新角色的实际音频URL
			updateRoleAudio(audioUrl)
			
			// 重要修改：永久显示播放器
			showAudioPlayer.value = true
			
			// 设置播放状态
			isPlaying.value = true
			
			// 创建或重置音频播放器
			if (!audioPlayer.value) {
				audioPlayer.value = new Audio()
				audioPlayer.value.addEventListener('ended', handleAudioEnded)
				audioPlayer.value.addEventListener('timeupdate', updateProgress)
				audioPlayer.value.addEventListener('loadedmetadata', () => {
					audioDuration.value = audioPlayer.value.duration
				})
			}
			
			// 设置音频源并播放
			audioPlayer.value.src = audioUrl
			audioPlayer.value.play().then(() => {
				isPlayerPlaying.value = true
			}).catch(error => {
				console.error('自动播放失败:', error)
				ElMessage.warning('自动播放失败，请手动点击播放按钮')
				isPlayerPlaying.value = false
			})
			
			// 开始更新进度
			startProgressUpdate()
		} else {
			// 关闭加载提示
			loadingInstance.close()
			
			// 显示错误消息
			ElMessage.error(response?.message || '音频合成失败，请重试')
		}
	} catch (error) {
		// 关闭加载提示
		loadingInstance.close()
		
		// 显示错误消息
		console.error('音频合成请求出错:', error)
		ElMessage.error('音频合成请求失败，请检查网络连接')
	}
}

/**
 * 处理操作栏动作
 * @param {String} action - 操作类型：new/recent/edit/export
 */
const handleBarAction = (action) => {
	console.log('操作栏动作:', action)
	switch (action) {
		case 'new':
			ElMessage.info('新建配音项目')
			break
		case 'save':
			ElMessage.info('查看最近配音项目')
			break
		case 'edit':
			ElMessage.info('前往剪辑页面')
			router.push('/VideoEditing')
			break
		case 'export':
			ElMessage.info('导出配音')
			generateVoiceOver()
			break
	}
}

// ===== 预览面板事件处理 =====
/**
 * 处理生成视频请求
 */
const handleGenerateVideo = () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	ElMessage.info('准备生成视频')
}

/**
 * 处理添加角色请求
 */
const handleAddRole = () => {
	if (selectedVoice.value >= 0 && voices.value[selectedVoice.value]) {
		// 如果已选中角色，提示用户需要先试听或合成
		ElMessage.info('请先试听或合成音频后再添加配音角色')
	} else {
		// 如果未选中角色，提示用户在左侧选择
		ElMessage.info('请在左侧选择配音角色')
	}
}

/**
 * 处理添加音乐请求
 * @param {Boolean} showDialog - 是否显示对话框
 */
const handleAddMusic = (showDialog = false) => {
	// 检查是否有现有音乐
	if (musicList.value && musicList.value.length > 0) {
		// 如果有音乐，直接打开对话框
		musicDialogVisible.value = true
		console.log('已有音乐，打开音乐选择对话框')
	} else if (musicStore.musicList && musicStore.musicList.length > 0) {
		// 如果musicStore中有音乐但本地没有，先同步再打开对话框
		musicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
		musicDialogVisible.value = true
		console.log('从musicStore同步音乐并打开对话框')
	} else {
		// 如果没有音乐，跳转到音乐音效页面
		console.log('无音乐，跳转到音乐音效页面')
		router.push('/MusicAudio')
	}
}

/**
 * 处理添加视频请求
 * @param {Boolean} showDialog - 是否显示对话框
 */
const handleAddVideo = (showDialog = false) => {
	if (showDialog) {
		ElMessage.info('打开视频选择对话框')
	} else {
		router.push('/VideoEditing')
	}
}

/**
 * 处理音量变更
 * @param {Object} item - 音频/视频项
 * @param {Number} volume - 新的音量值
 */
const handleVolumeChange = (item, volume) => {
	console.log(`${item.title}音量变更为: ${volume}`)
}

/**
 * 根据标签类型获取对应图标
 * @param {String} tag - 标签类型
 * @returns {String} 图标URL
 */
const getTagIcon = (tag) => {
	switch(tag) {
		case '臻享':
			return new URL('@/assets/img/zhenxiang.png', import.meta.url).href
		case '精品':
			return new URL('@/assets/img/jingpin.png', import.meta.url).href
		case '精品':
			return new URL('@/assets/img/jingpin.png', import.meta.url).href
		default:
			return ''
	}
}

/**
 * 初始化音色图标
 * 作为API调用失败的备用方案
 */
const initVoiceIcons = () => {
	voices.value = voices.value.map(voice => {
		return {
			...voice,
			tagIcon: getTagIcon(voice.tag)
		}
	})
}

/**
 * 播放音频样本
 * @param {Object} voice - 要播放的音色对象
 */
const playAudio = (voice) => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 获取准备播放的音频URL
	const audioSrc = voice.audioUrl || voice.sample;
	
	// 检查当前声音是否有音频URL
	if (!audioSrc) {
		ElMessage.warning('该音色没有可播放的音频样本')
		return
	}
	
	// 如果有正在播放的音频，先暂停它（不重置状态）
	if (currentPlayingVoice.value && currentPlayingVoice.value !== voice) {
		currentPlayingVoice.value.isPlaying = false
	}
	
	// 创建或重设音频播放器
	if (!audioPlayer.value) {
		audioPlayer.value = new Audio()
		// 添加播放结束事件监听
		audioPlayer.value.addEventListener('ended', handleAudioEnded)
		// 添加时间更新事件监听，用于更新进度条
		audioPlayer.value.addEventListener('timeupdate', updateProgress)
		// 添加元数据加载事件监听，用于获取音频时长
		audioPlayer.value.addEventListener('loadedmetadata', () => {
			audioDuration.value = audioPlayer.value.duration
		})
	}
	
	// 判断是否是同一音频源，如果是同一个音色且已设置相同URL则不重新设置，保持当前播放位置
	const isSameAudio = audioPlayer.value.src && 
		(audioPlayer.value.src === audioSrc || 
		 audioPlayer.value.src.endsWith(encodeURIComponent(audioSrc)));
	
	// 仅在音频源不同或首次播放时设置音频源
	if (!isSameAudio) {
		audioPlayer.value.src = audioSrc
		// 重置播放位置仅当音频源改变
		audioPlayer.value.currentTime = 0
	}
	
	// 显示音频播放器控件
	showAudioPlayer.value = true
	
	// 播放音频
	audioPlayer.value.play().then(() => {
		// 播放成功，更新状态
		voice.isPlaying = true
		currentPlayingVoice.value = voice
		
		// 设置播放器按钮为播放状态
		isPlayerPlaying.value = true
		
		// 启动进度更新定时器
		startProgressTimer()
	}).catch(error => {
		// 播放失败，提示错误
		console.error('音频播放失败:', error)
		ElMessage.error('音频播放失败，请重试')
		voice.isPlaying = false
		currentPlayingVoice.value = null
		isPlayerPlaying.value = false
	})
}

/**
 * 暂停音频
 * @param {Object} voice - 要暂停的音色对象
 */
const pauseAudio = (voice) => {
	if (audioPlayer.value && voice.isPlaying) {
		audioPlayer.value.pause()
		voice.isPlaying = false
		// 不再重置当前播放音色，保留音色引用
		// currentPlayingVoice.value = null
		
		// 更新播放器按钮状态
		isPlayerPlaying.value = false
		
		// 停止进度更新定时器
		clearProgressTimer()
	}
}

/**
 * 处理音频播放结束事件
 */
const handleAudioEnded = () => {
	// 更新播放状态
	isPlayerPlaying.value = false
	isPlaying.value = false
	progress.value = 0
	currentTime.value = 0
	
	// 更新音色卡片状态
	if (currentPlayingVoice.value) {
		currentPlayingVoice.value.isPlaying = false
		// 只有在音频完全结束时才重置当前播放音色
		currentPlayingVoice.value = null;
	}
	
	// 清除进度更新定时器
	if (audioTimer.value) {
		clearInterval(audioTimer.value)
		audioTimer.value = null
	}
}

/**
 * 获取音色卡片的样式类
 * @param {Object} voice - 音色对象
 * @param {Number} index - 音色索引
 * @returns {Object} 包含样式类的对象
 */
const getVoiceItemClass = (voice, index) => {
	return {
		'active': selectedVoice.value === index,
		'playing': voice.isPlaying
	}
}

/**
 * 获取所有音色数据
 */
const fetchVoices = async () => {
	const loadingInstance = ElLoading.service({
		text: '正在加载音色数据...',
		background: 'rgba(255, 255, 255, 0.7)'
	})
	
	try {
		const res = await getAllVoices({tts:'3'})
		
		// 处理API返回的音色数据，添加前端需要的属性
		const processedVoices = res.map(voice => {
			// 从API提取的基础数据
			const baseVoice = {
				id: voice.id,
				name: voice.platformNickname || '未命名音色',
				voiceName: voice.voiceName, // 保存原始的 voiceName 字段
				tag: voice.voiceType || voice.tag || '经典',
				avatar: voice.avatar || '',
				avatarUrl: voice.avatarUrl || '',
				sample: voice.sampleUrl || voice.sample || '',
				// 添加 demoText 字段
				demoText: voice.demoText || '这是一段示例文本，用于展示该音色的效果。',
				
				// 其他字段保持不变...
				gender: voice.gender,
				language: voice.language,
				description: voice.description,
				
				// 添加前端需要的控制状态
				isPlaying: false,
				isSelected: false,
				loadProgress: 0,
				
				// 添加UI相关的属性
				tagIcon: '',
				displayOrder: voice.displayOrder || 0,
				
				// 添加业务逻辑相关的属性
				compatibility: calculateCompatibility(voice, previewContent.value),
				isFavorite: checkIfFavorite(voice.id),
				
				// 可以添加计算的属性
				shortName: (voice.platformNickname || '').substring(0, 10),
				category: mapVoiceToCategory(voice),
				
				// 添加音频URL字段
				audioUrl: voice.audioUrl || voice.sampleUrl || '',
				
				// 保存原始数据中的分类字段，用于筛选
				sceneCategory: voice.sceneCategory,
				recommendTags: voice.recommendTags,
				ageGroup: voice.ageGroup,
				emotionTags: voice.emotionTags
			}
			
			return baseVoice
		})
		
		// 保存处理后的数据到allVoices和voices
		allVoices.value = processedVoices
		filteredVoices.value = processedVoices // 初始化时设置filteredVoices
		
		// 添加标签图标并设置到voices
		voices.value = processedVoices.map(voice => ({
			...voice,
			tagIcon: getTagIcon(voice.tag)
		}))
		
		// 标记推荐音色
		markRecommendedVoices()
		
		// ElMessage.success('音色数据加载成功')
	} catch (error) {
		console.error('获取音色数据失败:', error)
		ElMessage.error('获取音色数据失败，请稍后重试')
		initVoiceIcons() // 初始化默认图标作为备用
	} finally {
		loadingInstance.close()
	}
}

/**
 * 计算音色与当前文本的兼容性
 * @param {Object} voice - 音色对象
 * @param {String} text - 文本内容
 * @returns {Number} 兼容性得分(0-100)
 */
const calculateCompatibility = (voice, text) => {
	// 简单示例：根据文本长度和音色特点计算
	return Math.min(100, Math.max(0, 80 + Math.random() * 20))
}

/**
 * 检查音色是否被用户收藏
 * @param {String} voiceId - 音色ID
 * @returns {Boolean} 是否已收藏
 */
const checkIfFavorite = (voiceId) => {
	// 简单示例
	return false
}

/**
 * 将音色映射到更具体的分类
 * @param {Object} voice - 音色对象
 * @returns {String} 分类名称
 */
const mapVoiceToCategory = (voice) => {
	// 根据音色属性将其映射到更精细的类别
	if (voice.gender === 'male') {
		return voice.age < 30 ? '青年男声' : '中年男声'
	} else if (voice.gender === 'female') {
		return voice.age < 30 ? '青年女声' : '中年女声'
	}
	return '通用'
}

/**
 * 标记推荐音色
 */
const markRecommendedVoices = () => {
	// 可以实现一些逻辑来标记推荐的音色
	if (voices.value.length > 0) {
		voices.value[0].isRecommended = true
	}
}

/**
 * 选择主分类
 * @param {String} id - 分类ID
 */
const selectMainCategory = (id) => {
	currentMainCategory.value = id
	// 重置所有后续分类
	currentGender.value = 'all'
	currentScene.value = 'all'
	currentRecommendTag.value = 'all'
	currentSubCategory.value = 'all'
	currentDetailedCategory.value = 'all'
	
	filterVoices()
}

/**
 * 选择子分类
 * @param {String} id - 分类ID
 */
const selectSubCategory = (id) => {
	currentSubCategory.value = id
	currentDetailedCategory.value = 'all'
	filterVoices()
}

/**
 * 选择详细分类
 * @param {String} id - 分类ID
 */
const selectDetailedCategory = (id) => {
	currentDetailedCategory.value = id
	filterVoices()
}

/**
 * 选择推荐标签
 * @param {String} id - 标签ID
 */
const selectRecommendTag = (id) => {
	currentRecommendTag.value = id
	// 重置其他后续选项
	currentSubCategory.value = 'all'
	currentDetailedCategory.value = 'all'
	
	filterVoices()
}

/**
 * 根据分类筛选音色
 * 现在在本地对allVoices进行筛选，不再调用API
 */
const filterVoices = async () => {
	// 显示加载状态
	const loadingInstance = ElLoading.service({
		text: '筛选音色数据...',
		background: 'rgba(255, 255, 255, 0.7)'
	})
	
	try {
		// 在本地进行筛选
		const result = allVoices.value.filter(voice => {
			// 检查主分类(voiceType)
			if (currentMainCategory.value !== 'all' && voice.tag !== currentMainCategory.value) {
				return false
			}
			
			// 检查性别
			if (currentGender.value !== 'all' && voice.gender !== currentGender.value) {
				return false
			}
			
			// 检查场景分类
			if (currentScene.value !== 'all' && voice.sceneCategory !== currentScene.value) {
				return false
			}
			
			// 检查推荐标签
			if (currentRecommendTag.value !== 'all') {
				// 标签可能是逗号分隔的多个值，需要检查是否包含
				if (!voice.recommendTags || !voice.recommendTags.includes(currentRecommendTag.value)) {
					return false
				}
			}
			
			// 检查子分类(保留现有逻辑，如果需要)
			if (currentSubCategory.value !== 'all') {
				// 根据具体数据结构实现子分类筛选
				// 如果没有对应字段，可能需要通过其他方式筛选
				// 这里保留为通过逻辑，实际上可能不需要
				return true
			}
			
			// 检查详细分类(保留现有逻辑，如果需要)
			if (currentDetailedCategory.value !== 'all') {
				// 根据具体数据结构实现详细分类筛选
				// 如果没有对应字段，可能需要通过其他方式筛选
				// 这里保留为通过逻辑，实际上可能不需要
				return true
			}
			
			// 通过所有筛选条件
			return true
		})
		
		// 保存筛选结果
		filteredVoices.value = result
		
		// 处理筛选后的数据
		processVoiceData(result)
	} catch (error) {
		console.error('筛选音色失败:', error)
		ElMessage.error('筛选音色失败，请重试')
	} finally {
		loadingInstance.close()
	}
}

/**
 * 处理音色数据
 * @param {Array} data - 音色数据数组
 */
const processVoiceData = (data) => {
	// 注意：data现在是已经在本地过滤好的数据，不是API返回的
	voices.value = data.map(voice => ({
		...voice,
		tagIcon: getTagIcon(voice.tag)
	}))
	
	// 可以选择是否需要随机排序，取决于产品需求
	// voices.value = shuffleArray(voices.value)
	
	// 标记推荐音色
	markRecommendedVoices()
}

/**
 * 获取分类元数据
 * 从API获取所有分类数据
 */
const fetchVoiceMetadata = async () => {
	try {
		const response = await getAllVoiceMetadata({tts:'3'})
		console.log('API返回的元数据:', response)
		
		// 确保获取data字段内的内容
		const res = response.data || response
		
		// 重置现有数据
		voiceMetadata.value = {
			voiceType: [],
			genders: [],
			sceneMetadata: [],
			recommendTags: [],
			categories: []
		}
		
		// 处理voiceType数据
		if (res && res.voiceType && Array.isArray(res.voiceType)) {
			voiceMetadata.value.voiceType = res.voiceType.map(item => ({
				id: item.voiceType || item.id || item.value || item.type || '',
				name: item.voiceType || item.label || item.type || '未知'
			}))
		}
		
		// 处理gender数据
		if (res && res.genders && Array.isArray(res.genders)) {
			voiceMetadata.value.genders = res.genders.map(item => {
				return {
					id: item.gender || item.id || item.value || '',
					name: item.gender || '未知'
				}
			})
		}
		
		// 处理sceneMetadata数据
		if (res && res.sceneMetadata && Array.isArray(res.sceneMetadata)) {
			voiceMetadata.value.sceneMetadata = res.sceneMetadata.map(item => {
				return {
					id: item.id || item.scene_category || '',
					name: item.scene_category || '未知',
					recommend_tags: item.recommend_tags || [] 
				}
			})
			
			// 从所有场景中收集推荐标签
			const allTags = [];
			
			// 处理所有场景的recommend_tags
			voiceMetadata.value.sceneMetadata.forEach(scene => {
				if (scene.recommend_tags && Array.isArray(scene.recommend_tags)) {
					// 处理标签对象数组
					const processTags = scene.recommend_tags.map(tagObj => {
						return {
							id: tagObj.recommend_tags || '',
							name: tagObj.recommend_tags || '',
							sceneId: scene.id || ''
						};
					});
					
					allTags.push(...processTags);
				}
			});

			// 去重处理
			const uniqueTags = new Map();
			allTags.forEach(tag => {
				if (!uniqueTags.has(tag.id) && tag.id) {
					uniqueTags.set(tag.id, tag);
				}
			});

			// 转换回数组
			voiceMetadata.value.recommendTags = Array.from(uniqueTags.values());

			console.log('最终的推荐标签列表:', voiceMetadata.value.recommendTags);
		}
		
		console.log('处理后的分类元数据:', voiceMetadata.value)
	} catch (error) {
		console.error('获取分类元数据失败:', error)
		ElMessage.warning('分类数据加载失败，显示默认分类')
		// 设置默认分类数据
		voiceMetadata.value = {
			voiceType: [
				{ id: '经典', name: '经典' },
				{ id: '精品', name: '精品' },
				{ id: '臻享', name: '臻享' }
			],
			genders: [
				{ id: 'male', name: '男声' },
				{ id: 'female', name: '女声' },
				{ id: 'children', name: '童声' }
			],
			sceneMetadata: [
				{ id: 'narration', name: '解说' },
				{ id: 'advertisement', name: '广告' },
				{ id: 'dubbing', name: '配音' }
			],
			recommendTags: [
				{ id: 'emotional', name: '情感' },
				{ id: 'calm', name: '平静' },
				{ id: 'professional', name: '专业' }
			],
			categories: []
		}
	}
}

/**
 * 选择性别分类
 * @param {String} id - 性别ID
 */
const selectGender = (id) => {
	currentGender.value = id
	// 重置后续级联选择
	currentScene.value = 'all'
	currentRecommendTag.value = 'all'
	currentSubCategory.value = 'all'
	currentDetailedCategory.value = 'all'
	
	filterVoices()
}

/**
 * 选择场景分类
 * @param {String} id - 场景ID
 */
const selectScene = (id) => {
	currentScene.value = id
	// 重置推荐标签选择
	currentRecommendTag.value = 'all'
	// 重置其他后续选项
	currentSubCategory.value = 'all'
	currentDetailedCategory.value = 'all'
	
	filterVoices()
}

// 组件生命周期钩子
// 组件挂载时初始化数据
onMounted(async () => {
	fetchVoices(); // 获取音色数据
	fetchVoiceMetadata(); // 获取分类元数据
	
	// 从预览存储中恢复角色信息
	const savedRole = previewStore.selectedRole;
	if (savedRole && typeof savedRole === 'object' && savedRole.name) {
		console.log('从 previewStore 中恢复角色信息:', savedRole);
		
		// 更新 roleList
		roleList.value = [{
			name: savedRole.name,
			voiceName: savedRole.voiceName,
			audioUrl: savedRole.audioUrl,
			isDemo: false
		}];
		
		// 移除设置配音角色可见性的代码，由PreviewPanel内部根据角色信息自动处理
	} else {
		console.log('无已保存的角色信息');
		roleList.value = [];
	}
	
	// 添加全局点击事件监听，实现点击其他区域暂停播放
	document.addEventListener('click', handleGlobalClick);
	
	// 其他初始化代码...
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
	if (audioPlayer.value) {
		audioPlayer.value.pause()
		audioPlayer.value.removeEventListener('ended', handleAudioEnded)
		audioPlayer.value = null
	}
	currentPlayingVoice.value = null
	
	// 清理播放进度定时器
	clearProgressTimer()
	
	// 移除全局点击事件监听
	document.removeEventListener('click', handleGlobalClick);
})

/**
 * 处理全局点击事件，点击播放区域外部时暂停播放
 * @param {Event} event - 鼠标点击事件
 */
const handleGlobalClick = (event) => {
	// 如果没有正在播放的音频，不需要处理
	if (!isPlayerPlaying.value && !isPlaying.value) return;
	
	// 检查点击目标是否在播放控制区域内
	const isPlayButton = event.target.closest('.player-button');
	const isProgressBar = event.target.closest('.progress-bar');
	const isVoiceAvatar = event.target.closest('.voice-avatar');
	const isPlayOrPauseButton = event.target.closest('.play-button') || event.target.closest('.pause-button');
	const isControlBar = event.target.closest('.control-bar');
	const isAudioPlayer = event.target.closest('.audio-player-wrapper');
	
	// 如果点击在播放控制区域外，则暂停播放
	if (!isPlayButton && !isProgressBar && !isVoiceAvatar && !isPlayOrPauseButton && !isControlBar && !isAudioPlayer) {
		// 如果有正在播放的声音，暂停它（但不要重置currentPlayingVoice）
		if (currentPlayingVoice.value) {
			// 直接修改播放状态而不清除引用
			currentPlayingVoice.value.isPlaying = false;
			
			// 暂停音频播放
			if (audioPlayer.value) {
				audioPlayer.value.pause();
			}
		}
		
		// 暂停主播放器
		if (audioPlayer.value && isPlayerPlaying.value) {
			audioPlayer.value.pause();
			isPlayerPlaying.value = false;
			clearProgressTimer();
		}
	}
}

/**
 * 随机排序数组
 * @param {Array} array - 要排序的数组
 * @returns {Array} 随机排序后的数组
 */
const shuffleArray = (array) => {
	// 创建数组副本以避免修改原数组
	const shuffled = [...array];
	// Fisher-Yates 随机排序算法
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
}

/**
 * 开始播放进度定时器
 * 模拟音频播放进度增加
 */
const startProgressTimer = () => {
	// 清除可能存在的定时器
	clearProgressTimer()
	
	// 设置新的定时器，每100毫秒更新一次进度
	audioTimer.value = setInterval(() => {
		// 增加当前时间（100毫秒 = 0.1秒）
		currentTime.value += 0.1
		
		// 如果超过总时长，重置或停止
		if (currentTime.value >= audioDuration.value) {
			// 自动停止播放
			isPlaying.value = false
			clearProgressTimer()
			resetProgress()
			return
		}
		
	// 计算进度百分比
	progress.value = (currentTime.value / audioDuration.value) * 100
	}, 100)
}

/**
 * 清除播放进度定时器
 */
const clearProgressTimer = () => {
	if (audioTimer.value) {
		clearInterval(audioTimer.value)
		audioTimer.value = null
	}
}

/**
 * 重置播放进度
 */
const resetProgress = () => {
	currentTime.value = 0
	progress.value = 0
}

/**
 * 处理进度条点击
 * @param {Event} event - 鼠标事件
 */
const handleProgressBarClick = (event) => {
	// 检查是否有可播放的音频
	if (!showAudioPlayer.value || !audioPlayer.value || audioDuration.value <= 0) {
		return
	}
	
	// 改进的坐标计算
	const progressBar = event.currentTarget
	const rect = progressBar.getBoundingClientRect()
	
	// 使用更精确的计算方法
	const clickPosition = event.clientX
	const barStart = rect.left
	const barWidth = rect.width
	
	// 计算百分比，确保在0-1范围内
	let percentage = (clickPosition - barStart) / barWidth
	percentage = Math.max(0, Math.min(1, percentage))
	
	// 设置新的播放进度
	setProgress(percentage)
}

// 添加防抖动函数
const debounce = (func, wait) => {
	let timeout
	return function(...args) {
		const context = this
		clearTimeout(timeout)
		timeout = setTimeout(() => func.apply(context, args), wait)
	}
}

// 添加requestAnimationFrame支持
let animationFrameId = null
const updateProgressWithRAF = (percentage) => {
	if (animationFrameId) {
		cancelAnimationFrame(animationFrameId)
	}
	
	animationFrameId = requestAnimationFrame(() => {
		setProgress(percentage)
		animationFrameId = null
	})
}

/**
 * 处理拖动过程
 * @param {Event} event - 鼠标事件
 */
const handleDragging = (event) => {
	if (!isDragging.value) return
	
	const progressBar = document.querySelector('.progress-bar')
	if (!progressBar) return
	
	// 改进的坐标计算
	const rect = progressBar.getBoundingClientRect()
	const dragPosition = event.clientX
	const barStart = rect.left
	const barWidth = rect.width
	
	// 计算百分比，确保在0-1范围内
	let percentage = (dragPosition - barStart) / barWidth
	percentage = Math.max(0, Math.min(1, percentage))
	
	// 使用requestAnimationFrame更新进度
	updateProgressWithRAF(percentage)
}

// 优化的拖动处理 - 添加防抖
const debouncedHandleDragging = debounce(handleDragging, 10)

/**
 * 开始拖动进度条
 */
const startDragging = () => {
	// 检查是否有可播放的音频
	if (!showAudioPlayer.value || !audioPlayer.value || !isPlayerPlaying.value) {
		return
	}
	
	// 暂停进度更新定时器
	clearProgressTimer()
	isDragging.value = true
	
	// 添加鼠标移动和鼠标松开事件监听
	document.addEventListener('mousemove', debouncedHandleDragging)
	document.addEventListener('mouseup', stopDragging)
}

/**
 * 停止拖动进度条
 */
const stopDragging = () => {
	if (!isDragging.value) return
	
	isDragging.value = false
	
	// 取消任何待处理的动画帧
	if (animationFrameId) {
		cancelAnimationFrame(animationFrameId)
		animationFrameId = null
	}
	
	// 移除事件监听
	document.removeEventListener('mousemove', debouncedHandleDragging)
	document.removeEventListener('mouseup', stopDragging)
	
	// 确保最后一次拖动位置被应用
	const lastEvent = window.event
	if (lastEvent) {
		handleDragging(lastEvent)
	}
	
	// 继续播放进度更新
	if (isPlayerPlaying.value) {
		// 短暂延迟启动定时器，确保UI已更新
		setTimeout(() => {
			startProgressTimer()
		}, 50)
	}
}

/**
 * 设置播放进度
 * @param {Number} percentage - 进度百分比 (0-1)
 */
const setProgress = (percentage) => {
	// 限制百分比在 0-1 之间
	percentage = Math.max(0, Math.min(1, percentage))
	
	// 更新进度和当前时间
	progress.value = percentage * 100
	currentTime.value = percentage * audioDuration.value
	
	// 更新音频元素的当前时间
	if (audioPlayer.value) {
		audioPlayer.value.currentTime = percentage * audioDuration.value
	}
}

/**
 * 播放器按钮点击事件
 * 控制播放/暂停
 */
const handlePlayerButtonClick = () => {
	// 如果没有音频，直接返回
	if (!showAudioPlayer.value || !audioPlayer.value) {
		// 可以提示用户先点击试听按钮
		ElMessage.info('请先点击"试听"按钮生成音频')
		return
	}

	// 切换播放状态
	isPlayerPlaying.value = !isPlayerPlaying.value
	
	if (isPlayerPlaying.value) {
		// 继续播放
		if (audioPlayer.value) {
			audioPlayer.value.play()
				.then(() => {
					// 播放成功，启动进度定时器
					startProgressTimer()
					
					// 同步更新音色卡片状态
					if (currentPlayingVoice.value) {
						currentPlayingVoice.value.isPlaying = true
					}
				})
				.catch(error => {
					console.error('音频播放失败:', error)
					isPlayerPlaying.value = false
					isPlaying.value = false
					
					// 确保音色卡片状态同步
					if (currentPlayingVoice.value) {
						currentPlayingVoice.value.isPlaying = false
					}
					
					ElMessage.error('音频播放失败，请重试')
				})
		}
	} else {
		// 暂停播放
		if (audioPlayer.value) {
			audioPlayer.value.pause()
			// 暂停进度更新定时器
			clearProgressTimer()
			
			// 同步更新音色卡片状态
			if (currentPlayingVoice.value) {
				currentPlayingVoice.value.isPlaying = false
			}
		}
	}
}

/**
 * 格式化时间显示
 * @param {Number} seconds - 秒数
 * @return {String} 格式化后的时间字符串 (00:00:00)
 */
const formatTime = (seconds) => {
	const hours = Math.floor(seconds / 3600)
	const minutes = Math.floor((seconds % 3600) / 60)
	const secs = Math.floor(seconds % 60)
	
	return [
		hours.toString().padStart(2, '0'),
		minutes.toString().padStart(2, '0'),
		secs.toString().padStart(2, '0')
	].join(':')
}

// 计算当前播放时间显示
const timeDisplay = computed(() => {
	return `${formatTime(currentTime.value)}/${formatTime(audioDuration.value)}`
})

/**
 * 切换语调滑块的显示状态
 */
const togglePitchSlider = () => {
	pitchSliderVisible.value = !pitchSliderVisible.value
}

// 处理音乐对话框相关方法
const handleMusicDialogConfirm = () => {
	// 关闭对话框
	musicDialogVisible.value = false
	
	// 使用musicStore中的音乐列表更新本地音乐列表
	if (musicStore.musicList && musicStore.musicList.length > 0) {
		// 深拷贝音乐列表，防止直接引用
		musicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
		// ElMessage.success(`已选择${musicList.value.length}首音乐`)
	}
}

const handleMusicDialogRemove = (index) => {
	// 在MusicDialog组件中已经处理了从musicStore中移除的逻辑
	// 这里只需同步更新本地musicList
	musicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
}

const handleMusicDialogTogglePlay = (index) => {
	console.log(`切换播放音乐 ${index}`)
}

const handleMusicDialogAddMusic = () => {
	ElMessage.info('添加音乐')
}

// 更新播放进度
const updateProgress = () => {
	if (!audioPlayer.value || isDragging.value) return
	
	currentTime.value = audioPlayer.value.currentTime
	progress.value = (currentTime.value / audioDuration.value) * 100
}

// 开始更新进度
const startProgressUpdate = () => {
	// 清除可能存在的旧定时器
	if (audioTimer.value) {
		clearInterval(audioTimer.value)
	}
	
	// 设置新的定时器
	audioTimer.value = setInterval(() => {
		updateProgress()
	}, 1000)
}

/**
 * 停止播放 - 在试听按钮点击时调用
 */
const stopPlayback = () => {
  // 设置状态为非播放
  isPlaying.value = false
  isPlayerPlaying.value = false
  
  // 如果有音频正在播放，停止它
  if (audioPlayer.value) {
    audioPlayer.value.pause()
    audioPlayer.value.currentTime = 0
  }
  
  // 重置播放进度
  resetProgress()
  
  // 清除定时器
  clearProgressTimer()
  
  // 重要：不要重置showAudioPlayer状态
  // showAudioPlayer.value = false  <-- 删除这行
  
  ElMessage.info('已停止播放')
}

// 在setup脚本的顶部（ref declarations之后）添加一个watch以同步previewContent与store
watch(() => previewStore.content, (newContent) => {
  if (newContent !== previewContent.value) {
    console.log('同步previewContent：', newContent)
    previewContent.value = newContent
  }
}, { immediate: true })

</script>

<style lang="scss" scoped>
.voice-over-container {
	display: grid;
	grid-template-rows: auto auto minmax(0, 1fr); /* 第一行Headbar，第二行OperationBar，第三行main-content占满剩余空间 */
	row-gap: 35px; /* 添加行间距 */
	height: auto; /* 使用自适应高度 */
	min-height: 800px; /* 设置一个合理的最小高度 */
	max-height: 1000px; /* 限制最大高度，避免内容过长 */
	background: #f7f7f9;
	position: relative;
	overflow: visible; /* 允许内容溢出，由响应式CSS处理 */
	width: 100%;
	
	:deep(.headbar-container) {
		grid-row: 1; /* Headbar在第一行 */
		width: 100%;
	}

	:deep(.operation-bar-container) {
		grid-row: 2; /* OperationBar在第二行 */
		width: 100%;
	}
	
	// 主要内容区域
	.main-content {
		grid-row: 3; /* 放在第三行 */
		display: flex;
		flex: 1;
		// padding: 20px;
		margin: 0 0 0 20px; /* 移除top margin */
		// background-color: #F5F7FA;
		position: relative;
		height: auto; /* 使用自适应高度 */
		min-height: 700px; /* 设置一个合理的最小高度 */
		overflow: auto; /* 允许内容溢出时滚动 */
		
		// 左侧区域
		.left-section {
			display: flex;
			background: #fff;
			border-radius: 8px;
			margin-right: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
			height: auto; /* 使用自适应高度 */
			min-height: 700px; /* 设置一个合理的最小高度 */
			overflow: auto; /* 当内容过多时允许滚动 */
			position: relative;
			
			.voice-over-tools {
				flex: 1;
				width: 505px;
				padding: 20px;
				display: flex;
				flex-direction: column;
				height: 100%;
				overflow: hidden;
				
				.category-nav {
					// margin-bottom: 20px;
					
					.category-container {
						width: 100%;
						margin-left: 40px;
						
						.main-categories .category-item:first-child,
						.sub-categories .category-item:first-child,
						.detailed-categories .category-item:first-child {
							position: absolute;
							left: 0;
							margin-left: 0;
							padding-left: 0;
						}
					}
					
					.main-categories,
					.sub-categories,
					.detailed-categories {
						display: flex;
						flex-wrap: wrap;
						margin-bottom: 6px;
						padding-left: 0;
						position: relative;
						
						.category-item {
							padding: 2px 5px;  /* 修改为6px上下间距 */
							font-size: 13px;
							color: #666;
							cursor: pointer;
							margin-right: 5px;
							
							&.active {
								color: #0AAF60;
								font-weight: 500;
							}
							
							&:hover {
								color: #0AAF60;
							}
						}
					}
					
					.category-container {
						width: 100%;
						margin-left: 40px;
						
						.main-categories .category-item:first-child,
						.sub-categories .category-item:first-child,
						.detailed-categories .category-item:first-child {
							position: absolute;
							left: -36px;
							margin-left: 0;
							padding-left: 0;
							
							&.active {
								color: #0AAF60;
								font-weight: 500;
							}
						}
					}
				}
				
				// 声音角色选择区域
				.voice-grid {
					overflow-y: auto;
					// padding-left: 10px;
					// margin-top: 10px;
					// max-height: calc(100vh - 350px); /* 修改：删除基于vh的计算 */
					// height: calc(100% - 250px); /* 修改：调整高度，从200px改为250px，减小区域高度 */
					// min-height: 200px; /* 添加最小高度 */
					position: relative; /* 添加相对定位 */
					
					/* 笔记本屏幕：使用固定最大高度，节省空间 */
					max-height: 58%;
					min-height: 58%;
					/* 大屏PC：使用flex:1充分利用屏幕空间 */
					// @media (min-width: 1600px) {
					// 	flex: 1;
					// 	max-height: none;
					// }
					
					// /* 超大屏幕：确保良好的显示效果 */
					// @media (min-width: 1920px) {
					// 	flex: 1;
					// 	max-height: 600px; /* 设置一个合理的上限 */
					// }
					
					&::-webkit-scrollbar {
						display: none;
					}
					scrollbar-width: none; // Firefox
					-ms-overflow-style: none; // IE and Edge
					
					/* 添加滚动优化属性 */
					scroll-behavior: smooth;
					-webkit-overflow-scrolling: touch;
					
					.voice-row {
						display: grid; // 修改：从flex改为grid布局
						grid-template-columns: repeat(4, 1fr); // 修改：每行固定4列
						grid-row-gap: 24px; // 垂直间距保持24px
						grid-column-gap: 18px; // 水平间距调整为18px，为右侧留出更多空间
						padding: 10px 20px 10px 10px; // 增加右侧内边距到20px
					}
					
					.voice-item-wrapper {
						position: relative;
						width: 100px;
						// height: 120px;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #F7F7F9;
						border-radius: 4px 40px 4px 4px;
						cursor: pointer;
						margin-bottom: 0;
						margin-right: 0;
						justify-self: center; // 修改：在网格中居中显示
						
						// 精品珍享图片定位 - 调整到边框外
						.position_image {
							position: absolute;
							top: -1px;
							left: -1px;
							overflow: hidden;
							z-index: 3;
							
							.premium-badge {
								width: 54px;
								height: 20px;
							}
						}
						
						// 内部卡片
						.voice-item {
							width: 96px;
							height: 116px;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							background-color: #F7F7F9;
							border-radius: 3px 38px 3px 3px;
							position: relative;
							z-index: 1;
						}
						
						// 活跃状态样式 - 简化边框处理
						&.active {
							background: linear-gradient(to bottom, rgb(10, 175, 96), rgb(255, 214, 0));
							background-color: rgb(241, 251, 246);
							box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
							
							// 内部卡片在选中状态下的背景
							.voice-item {
								background-color: rgb(241, 251, 246);
							}
							
							// 移除多余的伪元素
							&::before {
								content: none;
							}
							
							&::after {
								content: none;
							}
							
							// 删除底部边框
							.voice-item::after {
								content: none;
							}
						}
					}
				}
				
				// 底部控制区
				.control-bar {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 20px 0; /* 调整padding为固定值 */
					border-top: 1px solid #EBEEF5;
					width: 100%;
					background: #fff;
					height: 80px; /* 固定高度 */
					position: relative; /* 添加相对定位 */
					flex-shrink: 0; /* 防止被压缩 */
					
					.volume-control {
						display: flex;
						align-items: center;
						margin-left: 20px;
						
						.pitch-display {
							display: flex;
							align-items: center;
							justify-items: center;
							background-color: #F4F4F4;
							border-radius: 4px;
							padding: 6px 16px;
							cursor: pointer;
							height: 40px;
							
							.pitch-label {
								color: #999999;
								margin-right: 12px;
								font-size: 14px;
								line-height: 1;
								display: flex;
								align-items: center;
							}
							
							.pitch-value {
								color: #333333;
								font-size: 15px;
								font-weight: 500;
								line-height: 1;
								display: flex;
								align-items: center;
							}
						}
					}
					
					.action-buttons {
						display: flex;
						gap: 15px;
						margin-right: 20px;
						
						button {
							height: 40px;
							width: 90px;
							border-radius: 4px;
							font-size: 14px;
							cursor: pointer;
							transition: all 0.3s;
							
							&.listen-btn {
								background: #fff;
								color: #606266;
								border: 1px solid #DCDFE6;
								
								// 控制图标样式
								.control-icons {
									display: flex;
									align-items: center;
									justify-content: center;
									
									.pause-icon {
										display: inline-block;
										width: 12px;
										height: 14px;
										position: relative;
										
										&:before, &:after {
											content: '';
											position: absolute;
											width: 4px;
											height: 14px;
											background-color: #606266;
											border-radius: 2px;
										}
										
										&:before {
											left: 0;
										}
										
										&:after {
											right: 0;
										}
									}
									
									.divider {
										width: 1px;
										height: 14px;
										background-color: #DCDFE6;
										margin: 0 8px;
									}
									
									.stop-icon {
										display: inline-block;
										width: 12px;
										height: 12px;
										background-color: #606266;
										border-radius: 2px;
									}
								}
								
								&:hover {
									border-color: #606266;
									color: #606266;
								}
							}
							
							&.generate-btn {
								background: linear-gradient(90deg, #0AAF60 0%, #A4CB55 100%);
								color: #fff;
								border: none;
								font-weight: 500;
								
								&:hover {
									opacity: 0.9;
								}
								
								&:active {
									opacity: 0.8;
								}
							}
						}
					}
				}
			}
		}
	}
}

// 辅助类
.margin_t-4 {
	margin-top: 4px;
}
.margin_t-14 {
	margin-top: 14px;
}
.font-size-14 {
	font-size: 14px;
}
.flex {
	display: flex;
}
.flex_a_i-center {
	align-items: center;
}
.flex_j_c-center {
	justify-content: center;
}
.flex_d-column {
	flex-direction: column;
}
.width-20 {
	width: 20px;
}
.height-20 {
	height: 20px;
}
.overflow-hidden {
	overflow: hidden;
}

// 头像样式更新
.voice-avatar {
	position: relative;
	width: 56px;
	height: 56px;
	margin-top: 14px;
	
	.avatar-circle {
		display: block;
		width: 56px;
		height: 56px;
		border-radius: 50%; // 这会使容器变成圆形
		background-color: #fff;
		overflow: hidden;  // 这会裁剪超出圆形的部分
		
		img {
			width: 100%;
			height: 100%;
			object-fit: cover; // 这会确保图片适当缩放并覆盖整个圆形区域
		}
	}
	
	// 播放和暂停按钮
	.play-button, .pause-button {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 20px;
		height: 20px;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0; // 默认隐藏
		transition: all 0.3s ease;
		z-index: 5;
	}
}

// 悬停效果 - 当鼠标悬停在卡片上时显示播放按钮
.voice-item-wrapper:hover {
	.voice-avatar {
		.play-button, .pause-button {
			opacity: 1; // 鼠标悬停时显示播放/暂停按钮
		}
	}
}

// 当音频正在播放时，给声音卡片添加特殊样式
.voice-item-wrapper.playing {
	.voice-avatar {
		.pause-button {
			opacity: 1; // 播放状态下始终显示暂停按钮
		}
	}
	
	.voice-item {
		// 可以添加其他表示播放状态的样式
		// 例如边框闪烁或背景色变化
	}
}

// 添加辅助类
.width-30 {
	width: 30px;
}
.height-30 {
	height: 30px;
}

// 统一所有分类的样式
.main-categories {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); 
	// gap: 8px; 
	margin-bottom: 8px;
	padding-left: 0;
	position: relative;
	margin-right: 24px; /* 从24px更新为25px */
	// width: 457px; /* 添加宽度设置 */
}

.category-item {
	text-align: center; /* 文本居中 */
	padding: 6px 5px;  /* 修改为6px上下间距 */
	font-size: 13px;
	color: #666;
	cursor: pointer;
	margin-right: 0; /* 移除右边距 */
	white-space: nowrap; /* 防止文本换行 */
	overflow: hidden;
	text-overflow: ellipsis;
	
	&.active {
		color: #0AAF60;
		font-weight: 500;
		margin-right: 24px; /* 添加选中状态的右侧间距 */
	}
	
	&:hover {
		color: #0AAF60;
	}
}

/* 移除第一个分类项的绝对定位 */
.category-container .main-categories .category-item:first-child {
	position: static;
	left: auto;
	margin-left: 0;
	padding-left: 0;
}

// 分类容器布局
.category-container {
	width: 100%;
	margin-left: 40px;
	
	.main-categories .category-item:first-child {
		position: absolute;
		left: -36px;
		margin-left: 0;
		padding-left: 0;
		
		&.active {
			color: #0AAF60;
			font-weight: 500;
		}
	}
}

// 音频播放器控件样式
.audio-player-wrapper {
	width: 100%;
	background: #F7F7F9;
	border-radius: 30px; /* 减小圆角 */
	padding: 8px; /* 减小内边距 */
	margin-top: 8px; /* 减小上边距 */
}

.audio-player-control {
	display: flex;
	align-items: center;
	width: 100%;
	
	.player-button {
		width: 26px; /* 减小尺寸 */
		height: 26px; /* 减小尺寸 */
		background: #0AAF60;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 8px; /* 减小右边距 */
		cursor: pointer;
        transition: background-color 0.3s ease;
        
        /* 禁用状态样式 */
        &.disabled-button {
            background: #CCCCCC;
            cursor: not-allowed;
        }
		
		.player-icon {
			width: 0;
			height: 0;
			border-style: solid;
			border-width: 6px 0 6px 10px; /* 减小尺寸 */
			border-color: transparent transparent transparent #fff;
		}
		
		.pause-icon {
			display: inline-block;
			width: 12px; /* 减小尺寸 */
			height: 12px; /* 减小尺寸 */
			position: relative;
			
			&:before, &:after {
				content: '';
				position: absolute;
				width: 3px; /* 减小尺寸 */
				height: 12px; /* 减小尺寸 */
				background-color: #fff;
				border-radius: 2px;
			}
			
			&:before {
				left: 2px;
			}
			
			&:after {
				right: 2px;
			}
		}
	}
	
	.progress-bar {
		flex: 1;
		height: 4px;
		background: #E6E6E6;
		border-radius: 2px;
		overflow: hidden;
		position: relative;
        transition: background-color 0.3s ease;
        cursor: pointer; /* 修改为传统小手样式 */
        
        /* 禁用状态样式 */
        &.disabled-progress {
            background: #DDDDDD;
            cursor: not-allowed;
        }
		
        /* 删除拖动时的样式 */
		
		.progress-filled {
			position: absolute;
			top: 0;
			left: 0;
			height: 100%;
			background: #0AAF60;
			border-radius: 2px;
		}
	}
	
	.player-time {
		margin-left: 12px; /* 减小左边距 */
		font-size: 12px; /* 减小字体大小 */
		color: #606266;
		white-space: nowrap;
        transition: color 0.3s ease;
        
        /* 禁用状态样式 */
        &.disabled-text {
            color: #AAAAAA;
        }
	}
}

/* 全局样式，不使用scoped */
.gradient-slider-popover {
	background-color: #000000 !important;
	opacity: 0.75 !important;
	border-radius: 8px !important;
	border: none !important;
	height: 38px !important;
	padding: 0 20px !important;
	display: flex !important;
	align-items: center !important;
}

.gradient-slider-popover .el-popover__title {
	color: #fff;
}

.gradient-slider-popover.el-popper[data-popper-placement^='top'],
.gradient-slider-popover.el-popper[data-popper-placement^='bottom'] {
	margin-bottom: 12px !important; /* 调整弹出层边距 */
	/* 移除固定的transform */
}

.gradient-slider-popover .el-popper__arrow {
	left: 50% !important; /* 将箭头居中，通过Popper的offset控制具体位置 */
}

/* 箭头伪元素样式穿透 */
.gradient-slider-popover .el-popper__arrow::before {
	background-color: #000000BF !important;
	border-color: #000000BF !important;
}

/* 箭头伪元素样式 - 更精确的选择器 */
.el-popper.gradient-slider-popover[data-popper-placement^=top] > .el-popper__arrow::before {
	background-color: #000000BF !important;
	border-color: #000000BF !important;
}

.el-popper.gradient-slider-popover[data-popper-placement^=bottom] > .el-popper__arrow::before {
	background-color: #000000BF !important;
	border-color: #000000BF !important;
}




/* 滑块容器样式 */
.speaker_content_bottom_left_speed_speech {
	width: 100%;
	padding: 0 5px;
	display: flex;
	align-items: center;
	justify-content: center;
	
	.flex-item_f-8 {
		flex: 8;
	}
	
	.margin_l-10 {
		margin-left: 10px;
		color: white;
		width: 30px;
		text-align: right;
	}
	
	.font-size-14 {
		font-size: 14px;
	}
}

/* 自定义滑块样式 */
.gradient-slider {
	:deep(.el-slider) {
		--el-slider-main-bg-color: #0AAF60;
		--el-slider-height: 4px;
		height: 20px;
		display: flex;
		align-items: center;
		position: relative;
		
		.el-slider__runway {
			background-color: rgba(255, 255, 255, 0.3);
			height: 4px;
			border-radius: 4px;
			position: relative;
			margin: 0 auto;
		}
		
		.el-slider__bar {
			background: linear-gradient(to right, #0AAF60, #A4CB55);
			height: 4px;
			border-radius: 4px;
			top: 0;
		}
		
		.el-slider__button-wrapper {
			top: -4px; /* 调整滑块按钮位置 */
			height: 12px;
			width: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.el-slider__button {
			width: 12px;
			height: 12px;
			border: 3px solid #0AAF60;
			background-color: #fff;
			box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
			margin: 0;
		}
		
		.el-slider__stop {
			width: 4px;
			height: 4px;
			border-radius: 50%;
			background-color: rgba(255, 255, 255, 0.5);
			transform: translateY(-50%);
			top: 50%;
		}
		
		/* 使用伪元素选择器，根据刻度点位置设置渐变色 */
		.el-slider__stop {
			&:nth-child(1) { background-color: #0AAF60; }
			&:nth-child(2) { background-color: #1FAF5F; }
			&:nth-child(3) { background-color: #39B25E; }
			&:nth-child(4) { background-color: #53B65D; }
			&:nth-child(5) { background-color: #6DBD5C; }
			&:nth-child(6) { background-color: #88C35A; }
			&:nth-child(7) { background-color: #A4CB55; }
		}
		
		/* 隐藏刻度标记文本 */
		.el-slider__marks-text {
			display: none;
		}
	}
}

.voice-item-wrapper {
  gap: 24px; /* 设置子元素间距，从21.67px改为24px */
}

.main-categories:first-child .category-item,
.main-categories:nth-child(2) .category-item,
.main-categories:nth-child(3) .category-item {
	padding-top: 24px;
	padding-bottom: 24px;
}
</style>

<style>
/* 隐藏原生箭头并创建自定义箭头 */
body .el-popper.gradient-slider-popover .el-popper__arrow {
	visibility: hidden !important;
}

.player-icon,
.pause-icon {
  pointer-events: none;
}

</style>