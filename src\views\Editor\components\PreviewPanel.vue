<template>
	<div class="right-content">
		<!-- 右侧预览区标题栏 -->
		<div class="preview-header">
			<div class="preview-title-bar">
				<div class="title">
					<el-input v-model="titleValue" placeholder="请输入标题" class="title-input" prefix-icon="fill-icon"
						@change="updateTitle" @blur="forceTitleSync">
						<template #prefix>
							<img src="@/assets/img/fill.png" alt="填充" class="title-icon">
						</template>
					</el-input>
				</div>
				<!-- <button class="generate-video-btn" @click="handleGenerateVideo">生成视频</button> -->
			</div>

			<!-- 素材选择区 - 移到标题栏旁边 -->
			<div class="material-selector">
				<!-- 第一部分：三个素材项 -->
				<div class="material-items-group">
					<!-- 移除音量滑块容器，将其放回原位置 -->

					<!-- 使用 v-for 循环渲染素材卡片 -->
					<div v-for="(item, index) in materialItems" :key="index"
						:class="['material-item', item.type + '-item']" v-show="(item.type === 'sound' && showVoiceRole) ||
							(item.type === 'music' && showMusicItem) ||
							(item.type === 'video' && showVideoItem)">
						<!-- 将删除图标移到这里 -->
						<img v-if="(item.type === 'sound' && item.selectedRole && !isPlaying) ||
							(item.type === 'music' && effectiveMusicList.length > 0 && !isPlaying) ||
							(item.type === 'video' && effectiveVideoList.length > 0 && !isPlaying)" src="@/assets/img/001.png" alt="删除"
							class="delete-icon" @click.stop="item.type === 'sound' ? removeRole(item, $event) :
								item.type === 'music' ? handleMusicRemove(0, $event) :
									handleVideoRemove(0, $event)">

						<div class="material-left">
							<div class="material-icon-container">
								<i class="material-icon"></i>
								<span>{{ item.title }}</span>
							</div>
							<div class="sound-control-wrapper" v-if="item.type !== 'video'">
								<i class="close-icon" @click="toggleVolumeSlider(item, $event)"
									:class="{ 'volume-on': item.volume > 0 }"></i>
								<!-- 直接在每个音标下添加音量滑块 -->
								<div class="volume-slider-popup" v-if="volumeSliderStates[item.type]">
									<div class="volume-slider">
										<div class="volume-value">{{ typeof item.volume === 'object' ? 50 : item.volume
											}}</div>
										<el-slider v-model="item.volume" :min="0" :max="100" :step="1" vertical
											height="70px" @change="(val) => handleVolumeChange(val, item)"
											style="--el-slider-main-bg-color: #0AAF60;" :show-tooltip="false" />
									</div>
								</div>
							</div>
						</div>

						<div class="right-controls">
							<div class="add-sound">
								<span v-if="item.type === 'sound'" class="add-sound-text"
									:class="{ 'selected': item.selectedRole, 'disabled': isPlaying }"
									@click="!isPlaying && $event.target.closest('.delete-icon') === null && handleAddRoleClick(item)">
									<span class="add-text">{{ item.selectedRole || item.addText }}</span>
									<!-- 移除内部删除图标 -->
								</span>
								<span v-else-if="item.type === 'music'" class="add-sound-text"
									:class="{ 'selected': effectiveMusicList.length > 0, 'disabled': isPlaying }"
									@click="!isPlaying && $event.target.closest('.delete-icon') === null && handleMusicClick()">
									<span class="add-text">{{ effectiveMusicList.length > 0 ?
										`${effectiveMusicList[0].name}...` :
										item.addText
									}}</span>
									<!-- 移除内部删除图标 -->
								</span>
								<span v-else-if="item.type === 'video'" class="add-sound-text"
									:class="{ 'selected': effectiveVideoList.length > 0, 'disabled': isPlaying }"
									@click="!isPlaying && $event.target.closest('.delete-icon') === null && handleVideoClick()">
									<span class="add-text">{{ effectiveVideoList.length > 0 ?
										(effectiveVideoList[0].name || '未命名视频') + (effectiveVideoList.length > 1 ? '...'
											: '') :
										item.addText
									}}</span>
									<!-- 移除内部删除图标 -->
								</span>
							</div>
						</div>
					</div>
				</div>

				<!-- 第二部分：播放图标 -->
				<img v-if="showPlayIcon" :src="isPlaying && !isPaused ? bfqIcon : playCircleFillIcon" alt="播放"
					class="video-play-icon" @click="handleVideoPreview">
			</div>
		</div>

		<!-- 右侧预览区 -->
		<div class="preview-section">
			<div class="preview-content">
				<!-- 添加提取中的遮罩层 -->
				<div class="extraction-overlay" v-if="extracting">
					<div class="extraction-loading">
						<div class="extraction-spinner"></div>
						<div class="extraction-text">文案提取中，请稍等...</div>
					</div>
				</div>

				<!-- 添加指南div，在编辑区域为空且未播放时显示 -->
				<div class="guide-content" v-if="showGuide" @click="handleGuideClick">
					<div class="guide-text">
						简单四步，轻松生成优质视频<br>
						Step1 文案创作<br>
						可通过AI创作，文案提取，文稿润色等多种方式快速创作文案（需使用中文标点符号）；<br>
						Step 2 选配音<br>
						为您的文案选择最合适的音色；<br>
						Step 3 选音乐<br>
						可上传音乐，或在在线音乐库中选择音乐为视频配乐；<br>
						Step 4 选视频<br>
						选择视频，点击智能匹配，即可生成带有字幕、配音和音乐的成品视频。
					</div>
				</div>

				<!-- 添加提取内容展示区域 -->
				<div v-if="showExtractedContent" class="extracted-content-list">
					<!-- 使用transition-group添加列表动画 -->
					<transition-group name="list-animation" tag="div" class="animation-container">
						<div v-for="(item, index) in effectiveExtractedContent" :key="index"
							class="extracted-content-item" @click="handleExtractedItemClick(item)"
							:class="{ 'active': selectedExtractedItem === item }"
							:style="{ animationDelay: `${index * 0.05}s` }">
							<!-- 左侧缩略图 -->
							<div class="thumbnail">
								<img :src="item.thumbnailUrl || defaultThumbnail" alt="视频缩略图"
									@error="handleThumbnailError($event, item)">
								<div class="time-label">{{ formatTime(item.time ? item.time.end : 0) }}</div>
								<div class="play-overlay">
									<i class="el-icon-video-play"></i>
								</div>
							</div>

							<!-- 中间内容区 -->
							<div class="content-area">
								<!-- 时间 -->
								<div class="content-time">
									<span class="time-text">{{ formatTime(item.time ? item.time.start : 0) }}</span>
									<el-icon class="time-separator"><arrow-right /></el-icon>
									<span class="time-text">{{ formatTime(item.time ? item.time.end : 0) }}</span>
								</div>
								<div class="content-text">{{ item.name }}</div>
							</div>

							<!-- 右侧区域 -->
							<div class="content-right">
								<div class="content-tag">
									<span v-html="effectiveRoleName"></span>
								</div>
							</div>
						</div>
					</transition-group>

					<!-- 添加无内容时的状态 -->
					<div v-if="effectiveExtractedContent.length === 0" class="no-content-placeholder">
						<i class="el-icon-video-camera"></i>
						<p>暂无提取内容</p>
					</div>
				</div>

				<!-- 在播放时禁用内容编辑 - 移除placeholder -->
				<textarea v-if="!showExtractedContent" ref="editableContent" class="preview-textarea"
					@input="handleContentInput" @compositionstart="isComposing = true"
					@compositionend="handleCompositionEnd" @paste="handlePaste" @click="handleEditorClick"
					placeholder="" :class="{ 'playing': isPlaying, 'timeline-content': hasTimelineContent }"
					style="background-image: none !important; background-clip: initial !important; -webkit-background-clip: initial !important; color: inherit !important;"
					:style="{ '--highlight-progress': textHighlightProgress + '%' }"></textarea>

				<!-- 添加右下角图片 - 在播放时禁用 -->
				<img v-if="!isPlaying" src="@/assets/img/Vectors.png" alt="vectors" class="corner-image"
					@click="clearContent">

				<!-- 将音量滑块容器放回这里 -->
				<!-- <div class="volume-slider-container" v-if="showVolumeSlider && activeVolumeItem"
					:style="volumeSliderStyle">
					<div class="volume-slider">
						<div class="volume-value">{{ typeof activeVolumeItem.volume === 'object' ? 50 :
							activeVolumeItem.volume
						}}</div>
						<el-slider v-model="activeVolumeItem.volume" :min="0" :max="100" :step="1" vertical
							height="70px" @change="(val) => handleVolumeChange(val, activeVolumeItem)"
							style="--el-slider-main-bg-color: #0AAF60;" :show-tooltip="false" />
					</div>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, nextTick, onUnmounted, watch, onBeforeUnmount, defineExpose, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useMusicStore } from '@/stores/modules/musicStore'
import { usePreviewStore } from '@/stores/previewStore'
import { generateAudio } from '@/api/voiceOver'
import { generateVideoSnapshotUrls } from '@/api/videoEditing'  // 导入视频快照生成API
import { ElLoading } from 'element-plus'

// 引入图片资源
import playCircleFillIcon from '@/assets/img/play-circle-fill.png'
import bfqIcon from '@/assets/img/bfq.png'
import yinsu2Icon from '@/assets/img/yinsu2.png'
import yinsu3Icon from '@/assets/img/yinsu3.png'
import defaultThumbnail from '@/assets/img/1.png'  // 导入默认缩略图
// 在setup中添加新的响应式变量
const selectedExtractedItem = ref(null);
const textPreviewRef = ref(null); // 添加对文本预览区域的引用
// 全局辅助函数定义，确保它们在任何地方都可以被调用
// 从提取内容中获取时间点的辅助函数
function getExtractedTimePoints(extractedContent) {
	// 获取预览区effectiveExtractedContent中的时间点
	if (extractedContent && extractedContent.length > 0) {
		// 从提取内容中收集开始时间点
		const timePoints = extractedContent.map(item => {
			if (item.time && item.time.start !== undefined) {
				// 将秒数格式化为MM:SS格式
				return formatTimeForAPI(item.time.start);
			}
			return null;
		}).filter(time => time !== null); // 过滤掉无效的时间点

		console.log('从提取内容中收集到的时间点:', timePoints);
		return timePoints;
	}
	// 如果没有有效的提取内容，返回空数组
	console.log('没有有效的提取内容，返回空时间点数组');
	return [];
}

// 将秒数格式化为API需要的格式（MM:SS）
function formatTimeForAPI(seconds) {
	if (typeof seconds !== 'number') return null;

	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = Math.floor(seconds % 60);
	return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 抽取生成快照API调用为独立函数，避免代码重复
async function callGenerateVideoSnapshotUrls(snapshotParams) {
	console.log('callGenerateVideoSnapshotUrls:', snapshotParams);
	// 确保有有效的参数
	if (!snapshotParams || !snapshotParams.storypath || !snapshotParams.times) {
		console.error('生成视频快照的参数无效:', snapshotParams);
		return;
	}

	// 确保storypath与previewStore.currentVideoUrl保持一致
	if (previewStore.currentVideoUrl && previewStore.currentVideoUrl !== snapshotParams.storypath) {
		console.log('检测到storypath与Pinia store中的URL不一致，使用最新的URL');
		console.log('原始storypath:', snapshotParams.storypath);
		console.log('Pinia store中的URL:', previewStore.currentVideoUrl);
		snapshotParams.storypath = previewStore.currentVideoUrl;
	}

	try {
		const response = await generateVideoSnapshotUrls(snapshotParams);
		console.log('视频快照生成成功:', response);

		// 处理返回的快照URL - 检查 response 是否为数组格式
		if (Array.isArray(response) && response.length > 0) {
			// 从数组中提取所有 URL
			const snapshotUrls = response.map(item => item.url).filter(url => url);
			console.log('提取的快照URLs:', snapshotUrls);

			// 先尝试使用 previewStore 中的提取内容
			let effectiveContent = previewStore.extractedContentList;

			// 如果 previewStore 中没有，则使用 props.extractedContent
			if (!effectiveContent || effectiveContent.length === 0) {
				effectiveContent = props.extractedContent || [];
			}

			// 如果 effectiveContent 有内容并且有快照URL，则更新缩略图
			if (effectiveContent && effectiveContent.length > 0 && snapshotUrls && snapshotUrls.length > 0) {
				console.log('更新提取内容的缩略图, 内容项数量:', effectiveContent.length, '快照URL数量:', snapshotUrls.length);

				effectiveContent.forEach((item, index) => {
					if (index < snapshotUrls.length) {
						console.log(`设置第${index + 1}个内容项的缩略图:`, snapshotUrls[index]);
						item.thumbnailUrl = snapshotUrls[index];
					}
				});

				// 确保数据更新到 store 中，以便其他组件能够访问
				if (previewStore.extractedContentList && previewStore.extractedContentList.length > 0) {
					previewStore.extractedContentList = [...effectiveContent];
				}
			} else {
				console.warn('没有有效的内容项或快照URL，无法更新缩略图');
			}
		} else {
			console.warn('视频快照API返回的数据结构不完整或为空:', response);
		}
	} catch (error) {
		console.error('生成视频快照失败:', error);
	}
}

// 添加一个新的函数，用于检查和等待时间点数据，然后再调用快照API
async function checkAndGenerateSnapshots(snapshotParams) {
	// 简化日志输出
	// console.log('检查快照参数:', snapshotParams);

	// 如果没有时间点或时间点为空数组，尝试从提取内容中获取
	if (!snapshotParams.times || snapshotParams.times.length === 0) {
		// console.log('时间点为空，尝试从提取内容中获取');

		// 从提取内容中获取时间点
		const extractedTimePoints = getExtractedTimePoints(effectiveExtractedContent.value);

		// 如果获取到了时间点，则更新参数并设置到store中
		if (extractedTimePoints && extractedTimePoints.length > 0) {
			// 更新时间点到store，但不等待操作完成
			previewStore.setVideoTimestamps(extractedTimePoints);

			// 使用更新后的参数
			const updatedParams = {
				...snapshotParams,
				times: extractedTimePoints
			};

			// 直接使用更新后的参数调用生成快照API
			return callGenerateVideoSnapshotUrls(updatedParams);
		} else {
			// 如果还是没有时间点，减少等待时间至200ms
			// console.log('没有找到时间点，等待200ms后再次尝试');
			return new Promise(resolve => {
				setTimeout(() => {
					// 再次获取store中的时间点
					const storeTimePoints = previewStore.videoTimestamps;

					if (storeTimePoints && storeTimePoints.length > 0) {
						// 使用store中的时间点
						const finalParams = {
							...snapshotParams,
							times: storeTimePoints
						};

						resolve(callGenerateVideoSnapshotUrls(finalParams));
					} else {
						// 简化错误日志
						// console.warn('多次尝试后仍无法获取时间点，无法生成视频快照');
						resolve(null);
					}
				}, 200); // 将等待时间从500ms减少到200ms
			});
		}
	} else {
		// 如果已经有时间点，直接调用生成快照API
		return callGenerateVideoSnapshotUrls(snapshotParams);
	}
}

// 添加角色列表管理
const roleList = ref([]) // 在计算属性之前定义 roleList

// 添加控制配音角色显示的状态变量 - 默认为false（隐藏）
const showVoiceRole = computed(() => {
	// 检查 roleList 中是否有有效角色
	const hasRole = roleList.value?.length > 0 && roleList.value[0]?.audioUrl;

	// 检查 store 中是否有有效角色
	const hasStoreRole = previewStore.selectedRole &&
		typeof previewStore.selectedRole === 'object' &&
		previewStore.selectedRole.name &&
		previewStore.selectedRole.audioUrl;

	return hasRole || hasStoreRole;
});

// 从父组件接收属性
const props = defineProps({
	title: {
		type: String,
		default: ''
	},
	content: {
		type: String,
		default: ''
	},
	isExtracting: {
		type: Boolean,
		default: false
	},
	musicList: {
		type: Array,
		default: () => []
	},
	videoList: {
		type: Array,
		default: () => []
	},
	roleList: {
		type: Array,
		default: () => []
	},
	extractedContent: {
		type: Array,
		default: () => []
	},
	// 添加标记当前是否在视频成片页面
	isVideoEditingPage: {
		type: Boolean,
		default: false
	},
	// 添加当前工具ID属性
	activeToolId: {
		type: Number,
		default: 0 // 默认值0表示未指定
	}
})

// 修改事件发射器，添加更新音乐列表的事件
const emit = defineEmits([
	'update:title',
	'update:content',
	'update:musicList',
	'update:videoList',
	'generate-video',
	'add-role',
	'add-music',
	'add-video',
	'volume-change',
	'update:roleList',
	'cancel-video-selection',
	'extractedItemClick',
	'switchToPreview'
])

// 路由实例
const router = useRouter()
const route = useRoute()

// 获取音乐存储
const musicStore = useMusicStore()
// 获取预览内容存储
const previewStore = usePreviewStore()

// 使用计算属性从 musicStore 和 props 中获取音乐列表
const effectiveMusicList = computed(() => {
	// 如果 musicStore 中有数据，优先使用 musicStore 的数据
	// 否则使用 props 传入的数据
	return musicStore.musicList.length > 0 ? musicStore.musicList : props.musicList;
})

// 本地状态变量（用于双向绑定）
const titleValue = computed({
	get: () => previewStore.title || props.title || '',
	set: (value) => {
		// 获取旧值，用于比较是否有实际变化
		const oldValue = previewStore.title || '';

		// 显式处理空值情况，确保空字符串也能正确设置
		previewStore.setTitle(value || '');
		emit('update:title', value || '');

		// 如果标题确实发生了变化，标记内容已被编辑
		if (value !== oldValue) {
			console.log('标题已修改，标记内容已编辑');
			setContentEdited(true);
		}
	}
})
const contentValue = ref(props.content || previewStore.content)
const extracting = computed(() => props.isExtracting)
const isUpdatingContent = ref(false) // 添加变量，用于标记是否由程序主动更新内容

// 添加一个变量，用于跟踪用户是否编辑过标题
// const userHasEditedTitle = ref(!!titleValue.value)

// 添加debug日志用于排查问题
console.log('组件初始化', {
	propsTitle: props.title,
	propsContent: props.content,
	storeTitle: previewStore.title,
	storeContent: previewStore.content,
	resultTitle: titleValue.value,
	resultContent: contentValue.value,
	musicStore: musicStore.musicList,
	propsMusicList: props.musicList
})

// 音频播放相关状态
const audioPlayer = ref(null)       // 用于播放音乐素材
const voicePlayer = ref(null)       // 用于播放配音角色
const isPlaying = ref(false)        // 是否正在播放
const showAudioPlayer = ref(false)   // 控制音频播放器显示
const isPlayerPlaying = ref(false)   // 播放器播放状态

// 添加是否来自timeline的标记 - 将此变量定义移到这里，所有监听器之前
const hasTimelineContent = ref(false)

// 添加音频同步相关状态
const voiceRoleDuration = ref(0)    // 配音角色的时长（秒）
const musicPlayer = ref(null)       // 音乐播放器
const musicCurrentTime = ref(0)     // 音乐当前播放时间
const musicDuration = ref(0)        // 音乐总时长
const musicLoopCount = ref(0)       // 音乐循环播放计数
const loopInterval = ref(null)      // 循环控制定时器

// 添加进度相关状态
const progress = ref(0)           // 播放进度（百分比）
const currentTime = ref(0)        // 当前播放时间
const audioDuration = ref(0)      // 音频总时长
const isDragging = ref(false)     // 是否正在拖动进度条
const audioTimer = ref(null)      // 播放进度定时器

// 添加当前播放的音乐索引变量
const currentMusicIndex = ref(0);

// 添加一个标记输入法组合状态的变量
const isComposing = ref(false);

// 在 script setup 部分添加一个变量来存储点击事件监听器引用
// 在音频播放相关状态区域附近添加
const clickOutsideListener = ref(null); // 存储点击外部事件监听器引用，用于后续销毁

// 在 script setup 中添加
const getStoredVolume = (type) => {
	const storedValue = localStorage.getItem(`${type}Volume`);
	return storedValue ? parseInt(storedValue) : 50;
}

// 修改 materialItems 的初始化，使用普通文本作为默认角色名
const materialItems = reactive([
	{
		type: 'sound',
		title: '配音角色',
		addText: '添加音色',
		volume: getStoredVolume('sound'),
		selectedRole: null, // 默认为 null，会在组件挂载时从 previewStore 获取
		audioUrl: '' // 不预设默认音频
	},
	{
		type: 'music',
		title: '音乐素材',
		addText: '添加音乐',
		volume: getStoredVolume('music'),
	},
	{
		type: 'video',
		title: '视频素材',
		addText: '添加视频',
		volume: getStoredVolume('video')
	}
])

// 音量控制相关
const showVolumeSlider = ref(false)
const activeVolumeItem = ref(null) // 保留这个变量以避免可能的其他引用，但不再用于控制显示

// 新增：为每个音量项独立管理显示状态
const volumeSliderStates = reactive({
	sound: false,
	music: false,
	video: false
})

// 计算属性：是否显示播放图标 - 当配音角色和音乐素材都没有时隐藏，其他情况显示
const showPlayIcon = computed(() => {
	// 检查是否有配音角色
	const hasVoiceRole = materialItems.some(item =>
		item.type === 'sound' && item.selectedRole && item.audioUrl
	);

	// 检查是否有音乐素材
	const hasMusic = effectiveMusicList.value &&
		effectiveMusicList.value.length > 0 &&
		effectiveMusicList.value[0].url &&
		typeof effectiveMusicList.value[0].url === 'string' &&
		effectiveMusicList.value[0].url.trim() !== '';

	// 如果配音角色和音乐素材都没有，则隐藏播放按钮，否则显示
	return hasVoiceRole || hasMusic;
})

// 可编辑内容引用
const editableContent = ref(null)

// 方法: 更新标题
const updateTitle = () => {
	// 清除手动清空标记，因为用户修改了标题
	localStorage.removeItem('content_manually_cleared');

	// 直接通过计算属性的 setter 更新标题
	titleValue.value = titleValue.value
}

// 方法: 更新内容
const updateContent = () => {
	emit('update:content', contentValue.value)
}

// 方法: 生成视频
const handleGenerateVideo = () => {
	emit('generate-video')
}

/**
 * 处理添加角色点击事件
 * 如果还没有角色，则跳转到配音页面
 */
const handleAddRoleClick = (item) => {
	// 跳转到配音页面
	emit('add-role');

	// 设置显示状态为true
	showVoiceRole.value = true;

	// 如果是从配音页面选择的角色并合成过音频后，才会有这些数据
	if (item.selectedRole && item.audioUrl && !item.isDemo) {
		previewStore.setRole({
			name: item.selectedRole,
			voiceName: item.voiceName || item.selectedRole,
			audioUrl: item.audioUrl
		});

		// 标记内容已被编辑 - 只有当实际设置了角色时才标记
		setContentEdited(true);
		console.log('设置配音角色，标记内容已编辑');
	}
}

// 方法: 音乐点击 - 修改跳转逻辑
const handleMusicClick = () => {
	// 添加播放状态检查
	if (isPlaying.value) {
		ElMessage.info('正在播放中，请停止播放后再操作')
		return
	}

	// 如果没有音乐，跳转到音乐音效页面
	if (!effectiveMusicList.value || effectiveMusicList.value.length === 0) {
		console.log('无音乐，跳转到音乐音效页面')
		router.push('/MusicAudio')
	}
}

// 方法: 视频点击 - 处理弹窗或跳转
const handleVideoClick = () => {
	// 在播放状态下不执行任何操作
	if (isPlaying.value) {
		ElMessage.info('正在播放中，请停止播放后再操作');
		return;
	}

	// 直接跳转到视频成片页面
	console.log('跳转到视频成片页面');
	router.push('/VideoEditing');
}

// 方法: 删除角色
const removeRole = (item, event) => {
	item.selectedRole = null;
	// 明确清除store中的角色
	previewStore.setRole(null);
	// 隐藏配音角色项
	showVoiceRole.value = false;
	emit('update:materials', materialItems);
	// 标记内容已被编辑
	setContentEdited(true);
	console.log('用户手动移除了角色，标记内容已编辑');
}

// 方法: 音乐删除 (修改确保同步到父组件)
const handleMusicRemove = (index, event) => {
	// 使用musicStore移除音乐
	musicStore.removeMusic(index)
	// 更新父组件传递的列表（为了保持现有的父子组件交互）
	emit('update:musicList', musicStore.musicList);
	// 标记内容已被编辑
	setContentEdited(true);
	console.log('用户移除了音乐，标记内容已编辑');
}

// 方法: 视频删除 - 确保删除后显示清晰的成功消息
const handleVideoRemove = (index, event) => {
	console.log('删除视频:', index);

	// 获取当前使用的视频列表
	const currentVideoList = effectiveVideoList.value;

	if (currentVideoList && currentVideoList.length > index) {
		// 获取视频名称用于显示成功消息
		const videoName = currentVideoList[index].name || '未命名视频';

		// 获取要删除的视频信息，用于后续取消左侧选中状态
		const removedVideo = currentVideoList[index];

		// 创建新数组以避免直接修改数据
		const newVideoList = [...currentVideoList];
		newVideoList.splice(index, 1);

		// 更新预览视频列表 - 同时更新store和通知父组件
		previewStore.setVideoList(newVideoList);
		emit('update:videoList', newVideoList);

		// 更新选中ID列表 - 如果store中有selectedVideoIds属性
		if (previewStore.selectedVideoIds) {
			// 从选中ID中移除相应的ID
			const updatedIds = previewStore.selectedVideoIds.filter(id =>
				!(removedVideo.id && id === removedVideo.id)
			);
			previewStore.setSelectedVideoIds(updatedIds);
		}

		// 发送事件通知父组件取消左侧视频的选中状态
		emit('cancel-video-selection', removedVideo);

		// 标记内容已被编辑
		setContentEdited(true);

		ElMessage.success(`已移除视频: ${videoName}`);
		console.log('删除视频后标记内容已编辑');
	}
}

// 方法: 切换音量滑块 - 修改为独立控制每个音量滑块的显示状态
const toggleVolumeSlider = (item, event) => {
	console.log('点击了音量图标', item.type)

	// 检查并修正音量值类型
	if (typeof item.volume !== 'number' || isNaN(item.volume)) {
		console.warn('检测到无效的音量值类型，已重置为默认值:', item.volume);
		item.volume = 50;
	}

	// 仍然保留这个引用，以便其他地方可能的使用
	activeVolumeItem.value = item;

	// 切换当前项的滑块显示状态
	volumeSliderStates[item.type] = !volumeSliderStates[item.type];

	// 如果正在播放并且切换的是音乐音量，确保显示当前实际音量
	if (isPlaying.value && item.type === 'music' && musicPlayer.value) {
		// 确保滑块显示的是当前实际播放音量
		item.volume = Math.round(musicPlayer.value.volume * 100);
		console.log('音乐正在播放，设置滑块显示当前音量:', item.volume);
	}
	else if (isPlaying.value && item.type === 'sound' && voicePlayer.value) {
		// 确保滑块显示的是当前实际播放音量
		item.volume = Math.round(voicePlayer.value.volume * 100);
		console.log('配音正在播放，设置滑块显示当前音量:', item.volume);
	}

	// 阻止事件冒泡，防止点击滑块时触发其他事件
	if (event) {
		event.stopPropagation();
	}

	// 点击其他区域关闭滑块的处理，修改为判断源滑块类型
	const closeVolumeSlider = (e) => {
		// 检查点击的元素是否在滑块或音量图标内
		const isInsideControls = e.target.closest('.volume-slider-popup') ||
			e.target.closest('.close-icon');

		if (!isInsideControls) {
			// 关闭所有滑块
			Object.keys(volumeSliderStates).forEach(key => {
				volumeSliderStates[key] = false;
			});
			document.removeEventListener('mousedown', closeVolumeSlider);
		}
	}

	// 使用 mousedown 而不是 click，确保在滑动滑块时不会关闭
	setTimeout(() => {
		document.addEventListener('mousedown', closeVolumeSlider);
	}, 100);
}

// 方法: 处理音量变化 - 确保音乐和配音音量完全独立
const handleVolumeChange = (value, item = activeVolumeItem.value) => {
	if (!item) return;

	// 确保音量值是有效的数字
	const newVolume = typeof value === 'object' ? 50 : parseInt(value);

	// 更新对应素材项的音量
	item.volume = newVolume;

	// 保存音量到 localStorage
	localStorage.setItem(`${item.type}Volume`, newVolume.toString());

	// 根据类型应用音量
	if (item.type === 'sound' && voicePlayer.value) {
		voicePlayer.value.volume = newVolume / 100;
		voicePlayer.value.muted = (newVolume === 0);
	} else if (item.type === 'music' && musicPlayer.value) {
		musicPlayer.value.volume = newVolume / 100;
		musicPlayer.value.muted = (newVolume === 0);

		// 如果有音乐列表，更新所有音乐的音量
		if (effectiveMusicList.value.length > 0) {
			effectiveMusicList.value.forEach((music, index) => {
				// 确保音量属性存在
				if (!music.volume) {
					music.volume = newVolume;
				} else {
					music.volume = newVolume;
				}
				musicStore.updateVolume(index, newVolume);
			});
		}
	}

	// 触发音量变化事件，将类型和音量值传给父组件
	emit('volume-change', { type: item.type, volume: newVolume });

	console.log(`已更新 ${item.type} 音量为 ${newVolume}%`);
}

// 增强forceApplyMusicVolume方法，确保音量设置始终有效
const forceApplyMusicVolume = () => {
	// 获取音乐素材项
	const musicItem = materialItems.find(item => item.type === 'music');
	if (musicItem && musicPlayer.value) {
		const musicVolume = typeof musicItem.volume === 'number' ? musicItem.volume : 50;

		// 额外确认音量类型
		if (isNaN(musicVolume)) {
			console.warn('检测到非法音量值，重置为默认值');
			musicItem.volume = 50;
		}

		// 应用音量
		musicPlayer.value.volume = musicVolume / 100;

		// 处理静音 - 明确使用严格等于0的判断
		if (musicVolume === 0) {
			musicPlayer.value.muted = true;
			// 增加双重保护，同时设置音量为0
			musicPlayer.value.volume = 0;
			console.log('音乐音量为0，强制静音成功');
		} else {
			musicPlayer.value.muted = false;
		}

		return musicVolume; // 返回应用的音量值，方便调试
	}
	return null;
}

// 创建播放前音乐检查方法，确保音量正确设置
const ensureMusicVolumeSetting = () => {
	// 获取音乐素材项
	const musicItem = materialItems.find(item => item.type === 'music');

	// 如果音量为0，记录日志
	if (musicItem && musicItem.volume === 0) {
		console.log('启动播放前检测：音乐音量为0，将确保静音');
	}

	return forceApplyMusicVolume();
};

// 添加暂停状态跟踪变量
const isPaused = ref(false); // 是否处于暂停状态（而不是停止）
const lastVoicePauseTime = ref(0); // 记录配音暂停时的播放位置
const lastMusicPauseTime = ref(0); // 记录音乐暂停时的播放位置

const handleVideoPreview = async () => {
	// 如果正在播放，则暂停播放
	if (isPlaying.value && !isPaused.value) {
		// 记录当前暂停位置
		if (voicePlayer.value) {
			lastVoicePauseTime.value = voicePlayer.value.currentTime;
		}
		if (musicPlayer.value) {
			lastMusicPauseTime.value = musicPlayer.value.currentTime;
		}

		// 设置为暂停状态
		isPaused.value = true;

		// 暂停所有音频，但不改变isPlaying状态，这样特效会保留
		stopAllAudio(true); // 传递isPause=true表示这是暂停而不是停止
		return;
	}

	// 如果是从暂停状态恢复播放
	if (isPlaying.value && isPaused.value) {
		// 尝试从暂停位置恢复播放
		try {
			// 恢复配音播放
			if (voicePlayer.value && lastVoicePauseTime.value > 0) {
				voicePlayer.value.currentTime = lastVoicePauseTime.value;
				await voicePlayer.value.play();
				console.log('恢复配音播放，从位置:', lastVoicePauseTime.value);
			}

			// 恢复音乐播放
			if (musicPlayer.value && lastMusicPauseTime.value > 0) {
				musicPlayer.value.currentTime = lastMusicPauseTime.value;
				await musicPlayer.value.play();
				console.log('恢复音乐播放，从位置:', lastMusicPauseTime.value);
			}

			// 重置暂停状态
			isPaused.value = false;

			// 重新开始更新进度
			startProgressTimer();

			return;
		} catch (error) {
			console.error('恢复播放失败:', error);
			ElMessage.error('恢复播放失败，将重新开始播放');
			// 如果恢复失败，尝试重新完整播放
			stopAllAudio(false);
		}
	}

	// 检查是否有可播放的内容
	const hasVoiceRole = materialItems.find(item =>
		item.type === 'sound' && item.selectedRole && item.audioUrl
	);

	const hasMusic = effectiveMusicList.value &&
		effectiveMusicList.value.length > 0 &&
		effectiveMusicList.value[0].url;

	if (!hasVoiceRole && !hasMusic) {
		ElMessage.warning('没有可播放的音频内容');
		return;
	}

	try {
		// 设置播放状态
		isPlaying.value = true;
		isPaused.value = false;

		// 播放配音角色（如果有）
		if (hasVoiceRole) {
			// 获取声音项
			const soundItem = materialItems.find(item => item.type === 'sound');

			// 检查是否有有效的音频URL
			if (soundItem && soundItem.audioUrl) {
				// 创建或重置配音播放器
				if (!voicePlayer.value) {
					voicePlayer.value = new Audio();
					voicePlayer.value.addEventListener('ended', handleVoiceEnded);
					voicePlayer.value.addEventListener('loadedmetadata', () => {
						voiceRoleDuration.value = voicePlayer.value.duration;
						console.log(`配音角色音频时长: ${voiceRoleDuration.value}秒`);
					});
				}

				console.log('准备播放配音URL:', soundItem.audioUrl);
				voicePlayer.value.src = soundItem.audioUrl;

				// 设置配音音量
				if (typeof soundItem.volume === 'number' && !isNaN(soundItem.volume)) {
					voicePlayer.value.volume = soundItem.volume / 100;
					voicePlayer.value.muted = (soundItem.volume === 0);
				}

				// 播放音频
				await voicePlayer.value.play();
				console.log('配音开始播放');
			}
		}

		// 播放音乐（如果有）
		if (hasMusic) {
			// 确保音乐URL是有效的
			const musicUrl = effectiveMusicList.value[0].url;
			if (musicUrl) {
				console.log('准备播放音乐URL:', musicUrl);
				await playMusic(hasVoiceRole); // true表示与配音同步播放
			}
		}

		// 重置暂停相关状态
		lastVoicePauseTime.value = 0;
		lastMusicPauseTime.value = 0;

		// 显示播放器
		showAudioPlayer.value = true;

		// 开始更新进度
		startProgressTimer();
	} catch (error) {
		console.error('播放失败:', error);
		ElMessage.error('播放失败，请检查音频源');
		stopAllAudio(false); // 完全停止，不是暂停
		isPaused.value = false; // 重置暂停状态
	}
};

// 修改音乐播放方法
const playMusic = async (syncWithVoice = false) => {
	if (!effectiveMusicList.value || effectiveMusicList.value.length === 0) {
		return false;
	}

	// 创建或重置音乐播放器
	if (!musicPlayer.value) {
		musicPlayer.value = new Audio();
		musicPlayer.value.addEventListener('ended', handleMusicEnded);
		musicPlayer.value.addEventListener('loadedmetadata', () => {
			musicDuration.value = musicPlayer.value.duration;
			console.log(`音乐素材时长: ${musicDuration.value}秒`);

			// 确保元数据加载后应用音量设置
			forceApplyMusicVolume();
		});
	}

	// 重置音乐状态
	currentMusicIndex.value = 0;
	musicCurrentTime.value = 0;
	musicLoopCount.value = 0;

	// 获取音乐URL并检查有效性
	const musicUrl = effectiveMusicList.value[currentMusicIndex.value].url;
	if (!musicUrl) {
		console.error('无效的音乐URL');
		return false;
	}

	console.log('设置音乐源:', musicUrl);
	musicPlayer.value.src = musicUrl;

	// 应用音量
	forceApplyMusicVolume();

	try {
		// 开始播放音乐
		await musicPlayer.value.play();
		console.log('音乐开始播放');

		return true;
	} catch (error) {
		console.error('音乐播放失败:', error);
		return false;
	}
};

// 修改音乐结束处理函数，支持多音乐播放情况
const handleMusicEnded = () => {
	console.log('音乐播放结束:', {
		currentIndex: currentMusicIndex.value,
		totalMusic: effectiveMusicList.value.length
	});

	// 情况1: 没有配音角色在播放(独立音乐播放模式)
	if (!voicePlayer.value && isPlaying.value) {
		// 如果没有配音角色，只播放一遍音乐列表然后停止
		if (effectiveMusicList.value.length > 1 && currentMusicIndex.value < effectiveMusicList.value.length - 1) {
			// 如果有多个音乐且未播放完整个列表，播放下一个
			currentMusicIndex.value++;
			musicPlayer.value.src = effectiveMusicList.value[currentMusicIndex.value].url;
			forceApplyMusicVolume();
			musicPlayer.value.play();
			console.log(`独立模式播放下一个音乐: 索引${currentMusicIndex.value}`);
		} else {
			// 如果只有一个音乐或已播放完整个列表，完全停止播放
			stopAllAudio(false);

			// 明确重置暂停状态
			isPaused.value = false;
			lastVoicePauseTime.value = 0;
			lastMusicPauseTime.value = 0;

			console.log('音乐播放列表已全部播放完成，停止播放');

			// 显示播放结束提示
			ElMessage.info('播放结束');
		}
	}
	// 情况2: 有配音角色在播放，循环逻辑由interval处理
	// 不需要在这里处理，由loopInterval控制
};

// 添加滚动到底部的函数
const scrollToBottom = () => {
	// 添加空值检查，确保 editableContent.value 存在
	if (editableContent.value) {
		editableContent.value.scrollTop = editableContent.value.scrollHeight
	}
}

// 修改颜文字过滤函数为更轻量级的版本
const removeEmojis = (text) => {
	if (!text) return '';

	// 简化的正则表达式，只过滤最常见的颜文字，减少对输入的影响
	// 避免过滤中文和其他语言字符
	return text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}]/gu, '');
}

// 修改组合输入结束处理函数，确保光标在文本末尾
const handleCompositionEnd = (event) => {
	isComposing.value = false;
	// 组合输入结束后处理内容
	handleContentInput(event, true);
}

// 修改内容输入处理函数，添加参数来指示是否是组合输入结束
const handleContentInput = (event, isCompositionEnd = false) => {
	// 如果正在显示提取内容，不处理普通输入
	if (showExtractedContent.value) return;

	// 如果正在组合输入中文且不是组合输入结束事件，不处理内容
	if (isComposing.value && !isCompositionEnd) return;

	// 总是标记内容已被编辑，移除inputType条件判断
	console.log('用户修改了内容，标记内容已编辑');
	setContentEdited(true);

	// 清除手动清空标记，因为用户已经开始添加新内容
	localStorage.removeItem('content_manually_cleared');

	// 对于textarea元素，直接使用value属性
	const newContent = event.target.value;

	// 过滤颜文字
	let cleanContent = newContent;
	if (/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}]/gu.test(newContent)) {
		cleanContent = removeEmojis(newContent);

		// 如果内容确实被过滤了，需要更新DOM
		if (cleanContent !== newContent) {
			// 更新textarea的值
			editableContent.value.value = cleanContent;
		}
	}

	// 更新状态
	contentValue.value = cleanContent;
	previewStore.setContent(cleanContent);
	emit('update:content', cleanContent);

	// 检查内容是否为空，控制指南显示
	showGuide.value = !cleanContent.trim();
	if (showGuide.value) {
		window.location.reload();
	}
}

// 添加一个新的辅助函数，将光标移动到末尾
const moveCursorToEnd = () => {
	if (!editableContent.value) return;

	// 创建选择范围
	const range = document.createRange();
	const selection = window.getSelection();

	// 确保有内容节点
	if (!editableContent.value.lastChild) {
		editableContent.value.appendChild(document.createTextNode(''));
	}

	// 获取最后一个文本节点
	const lastNode = editableContent.value.lastChild;
	const textLength = lastNode.textContent ? lastNode.textContent.length : 0;

	// 设置范围到末尾
	range.setStart(lastNode, textLength);
	range.setEnd(lastNode, textLength);

	// 应用选择范围
	selection.removeAllRanges();
	selection.addRange(range);
}

// 修改 handlePaste 方法，确保粘贴内容也会过滤颜文字
const handlePaste = (e) => {
	e.preventDefault();
	let text = (e.clipboardData || window.clipboardData).getData('text/plain');

	// 清除手动清空标记，因为用户粘贴了新内容
	localStorage.removeItem('content_manually_cleared');

	// 过滤粘贴内容中的表情符号
	text = removeEmojis(text);

	// 对于textarea元素，直接更新value值
	if (editableContent.value) {
		const currentValue = editableContent.value.value || '';
		const selStart = editableContent.value.selectionStart;
		const selEnd = editableContent.value.selectionEnd;

		// 将粘贴内容插入到当前光标位置
		const newValue = currentValue.substring(0, selStart) + text + currentValue.substring(selEnd);
		editableContent.value.value = newValue;

		// 更新光标位置到新内容后
		const newCursorPos = selStart + text.length;
		editableContent.value.setSelectionRange(newCursorPos, newCursorPos);
	}

	console.log('用户粘贴了内容，保持timeline样式状态');
	// 标记内容已被编辑
	setContentEdited(true);

	// 获取更新后的内容
	let updatedContent = '';
	if (editableContent.value) {
		updatedContent = editableContent.value.value || '';
	}

	// 更新所有相关状态和存储
	contentValue.value = updatedContent;
	previewStore.setContent(updatedContent);

	// 明确触发事件通知父组件内容已更新
	emit('update:content', updatedContent);

	console.log('粘贴处理完成，内容长度:', updatedContent.length);
}

// 添加函数：强制清空标题输入框
const forceClearTitle = () => {
	// 直接使用DOM操作清空输入框
	const titleInput = document.querySelector('.title-input input');
	if (titleInput) {
		titleInput.value = '';
		// 触发输入事件，确保Vue响应式更新
		titleInput.dispatchEvent(new Event('input', { bubbles: true }));
		console.log('强制清空标题输入框成功');
	}
}

// 修改 clearContent 方法，添加对标题输入框的处理
const clearContent = () => {
	// 只清空内容，不清空标题
	previewStore.setContent(''); // 只清除内容，保留标题

	// 设置localStorage标记，表示内容已被手动清空
	localStorage.setItem('content_manually_cleared', 'true');

	// 只清空内容相关状态，不清空标题
	contentValue.value = '';

	// 清除提取内容列表
	previewStore.clearExtractedContent();

	// 清除localStorage中可能存储的内容相关数据
	localStorage.removeItem('manual-extracted-content');
	localStorage.removeItem('manual-video-url');

	if (editableContent.value) {
		// 对于input元素，直接清空value
		editableContent.value.value = '';
	}

	// 只通知父组件内容已清空，不清空标题
	emit('update:content', '');

	// 清空后显示指南
	showGuide.value = true;

	console.log('清空内容但保留标题，并添加清空标记');

	// 强制刷新浏览器
	setTimeout(() => {
		window.location.reload();
	}, 100); // 短暂延时确保清空操作完成
}

// 添加初始化配音角色的方法
const initializeVoiceRole = () => {
	const storedRole = previewStore.selectedRole
	if (storedRole && typeof storedRole === 'object' && storedRole.name && storedRole.audioUrl) {
		showVoiceRole.value = true;

		// 更新 materialItems
		if (materialItems[0]) {
			materialItems[0].selectedRole = storedRole.name;
			materialItems[0].voiceName = storedRole.voiceName;
			materialItems[0].audioUrl = storedRole.audioUrl;
		}

		console.log('初始化配音角色:', storedRole);
	}
}

// 组件挂载时初始化
onMounted(() => {
	// 检查是否有手动清空标记
	const isManuallyCleared = localStorage.getItem('content_manually_cleared') === 'true';

	// 初始化配音角色 - 不管是否手动清空都执行
	initializeVoiceRole();

	// 确保标题正确加载 - 不管是否手动清空都要恢复标题
	if (previewStore.title) {
		titleValue.value = previewStore.title;
		console.log('组件挂载时从Pinia加载标题:', titleValue.value);
	}

	// 如果未手动清空，则进行完整初始化
	if (!isManuallyCleared) {
		// 检查路由参数，如果有refresh参数且值为true，则清空预览数据
		if (route.query.refresh === 'true') {
			console.log('检测到refresh参数，清空预览数据');
			clearContent();
			// 清除URL中的参数，避免刷新页面时重复清空
			router.replace({ query: {} });
		}
	} else {
		console.log('检测到内容已被手动清空，跳过内容初始化，但保留标题');
		// 在手动清空状态下，确保编辑区域保持为空
		if (editableContent.value) {
			editableContent.value.value = '';
		}
		showGuide.value = true;
	}

	// 监听路由变化
	if (router) {
		const unregister = router.beforeEach((to, from, next) => {
			// 检查是否有手动清空标记
			const isManuallyCleared = localStorage.getItem('content_manually_cleared') === 'true';

			// 不管是否手动清空，都恢复标题和角色信息
			// 对所有路由变化都检查角色信息
			const storedRole = previewStore.selectedRole;
			if (storedRole && typeof storedRole === 'object' && storedRole.name && storedRole.audioUrl) {
				// 确保 materialItems 中的角色信息正确
				if (materialItems[0]) {
					materialItems[0].selectedRole = storedRole.name;
					materialItems[0].voiceName = storedRole.voiceName;
					materialItems[0].audioUrl = storedRole.audioUrl;
				}
			}

			// 如果未手动清空，才恢复其他内容
			if (!isManuallyCleared) {
				// 保存 isTimelineContent 状态在路由变化时
				if (previewStore.isTimelineContent) {
					console.log('全局路由守卫：保留提取内容样式状态');
					hasTimelineContent.value = true;
				}

				// 如果是编辑器页面之间的跳转，执行原有逻辑
				if (from.path.includes('/Editor') && to.path.includes('/Editor') && from.path !== to.path) {
					clearTitleOnRouteChange();
				}
			} else {
				console.log('检测到内容已被手动清空，跳过内容恢复逻辑，但保留标题');
			}
			next();
		});

		onUnmounted(() => {
			unregister();
		});
	}

	// 输出完整的previewStore状态进行调试
	console.log('PreviewPanel挂载时previewStore状态:', {
		title: previewStore.title,
		content: previewStore.content,
		videoList: previewStore.videoList?.length || 0,
		currentVideoUrl: previewStore.currentVideoUrl,
		videoTimestamps: previewStore.videoTimestamps,
		hasVideoUrl: Boolean(previewStore.currentVideoUrl),
		previewStoreInstance: previewStore
	});

	// 检查Pinia store中是否有视频URL，如果有则调用生成快照API
	if (previewStore.currentVideoUrl) {
		console.log('检测到视频URL，准备生成视频快照:', previewStore.currentVideoUrl);

		// 获取视频截图参数
		const snapshotParams = previewStore.getVideoSnapshotParams();
		console.log('生成视频快照参数:', snapshotParams);

		// 调用生成视频快照API
		checkAndGenerateSnapshots(snapshotParams);
	} else {
		console.warn('PreviewPanel挂载时未检测到视频URL');
	}

	// 添加 onMounted 钩子，确保在组件挂载时正确恢复状态
	console.log('预览面板挂载，检查是否有提取内容需要恢复');
	// 如果 store 中的 isTimelineContent 为 true，确保本地状态也同步
	if (previewStore.isTimelineContent) {
		console.log('检测到store中有提取内容标记，恢复提取内容状态');
		hasTimelineContent.value = true;

		// 如果有内容，更新到可编辑区域
		if (previewStore.content && editableContent.value) {
			editableContent.value.innerText = previewStore.content;
		}
	}

	// 从 store 中恢复角色数据
	const storeRole = previewStore.selectedRole;
	if (storeRole && storeRole.name) {
		// 找到角色素材项
		const soundItem = materialItems.find(item => item.type === 'sound');
		if (soundItem) {
			soundItem.selectedRole = storeRole.name;
			soundItem.audioUrl = storeRole.audioUrl;
			soundItem.voiceName = storeRole.voiceName || storeRole.name;
		}
	}

	// 在组件挂载时检查store中是否有提取内容
	if (previewStore.extractedContentList && previewStore.extractedContentList.length > 0) {
		console.log('从store恢复提取内容:', previewStore.extractedContentList.length);
		// 不需要额外操作，因为计算属性和条件渲染会处理显示
	}

	// 检查Pinia store中是否有extractedContentList
	console.log('组件挂载时检查Pinia存储:', {
		hasExtractedContent: Boolean(previewStore.extractedContentList?.length),
		extractedContentLength: previewStore.extractedContentList?.length || 0,
		isTimelineContent: previewStore.isTimelineContent,
		firstItem: previewStore.extractedContentList && previewStore.extractedContentList.length > 0
			? JSON.stringify(previewStore.extractedContentList[0])
			: 'none'
	});

	// 如果props中有extractedContent，确保保存到Pinia
	if (props.extractedContent && props.extractedContent.length > 0 &&
		(!previewStore.extractedContentList || previewStore.extractedContentList.length === 0)) {
		console.log('从props恢复extractedContent到Pinia');
		const contentToStore = JSON.parse(JSON.stringify(props.extractedContent));
		previewStore.setExtractedContentList(contentToStore);
		previewStore.setIsTimelineContent(true);
	}

	// 首先检查Pinia中是否有视频列表和URL
	console.log('检查Pinia中的视频列表:', previewStore.videoList?.length);
	let videoUrlFound = false;

	if (previewStore.videoList && previewStore.videoList.length > 0) {
		const firstVideo = previewStore.videoList[0];
		if (firstVideo && (firstVideo.url || firstVideo.videoUrl)) {
			const videoUrl = firstVideo.url || firstVideo.videoUrl;
			console.log('从Pinia视频列表中获取到视频URL:', videoUrl);

			// 如果Pinia中已有videoList但没有currentVideoUrl，设置currentVideoUrl
			if (!previewStore.currentVideoUrl) {
				console.log('Pinia中有视频列表但没有URL，设置URL:', videoUrl);
				previewStore.setCurrentVideoUrl(videoUrl);

				// 同时设置时间点 - 使用提取内容的开始时间
				const extractedTimePoints = getExtractedTimePoints(effectiveExtractedContent.value);
				console.log('从提取内容设置时间点:', extractedTimePoints);

				// 标记已找到URL
				videoUrlFound = true;

				// 获取视频截图参数并调用API
				const snapshotParams = previewStore.getVideoSnapshotParams();
				console.log('从Pinia视频列表设置后的视频参数:', snapshotParams);

				// 调用生成视频快照API
				checkAndGenerateSnapshots(snapshotParams);
			} else {
				// 如果currentVideoUrl已存在，也标记为已找到
				videoUrlFound = true;
			}
		}
	}

	// 如果Pinia中没有找到视频URL，再检查props中的视频列表
	if (!videoUrlFound) {
		console.log('检查props中的视频列表:', props.videoList?.length);
		if (props.videoList && props.videoList.length > 0) {
			const firstVideo = props.videoList[0];
			if (firstVideo && (firstVideo.url || firstVideo.videoUrl)) {
				const videoUrl = firstVideo.url || firstVideo.videoUrl;
				console.log('从props.videoList中获取到视频URL:', videoUrl);

				// 如果Pinia中没有URL，则设置从props获取的URL
				if (!previewStore.currentVideoUrl) {
					console.log('Pinia中没有视频URL，从props设置:', videoUrl);
					previewStore.setCurrentVideoUrl(videoUrl);

					// 同时设置时间点 - 使用提取内容的开始时间
					const extractedTimePoints = getExtractedTimePoints(effectiveExtractedContent.value);
					console.log('从提取内容设置时间点:', extractedTimePoints);

					// 获取视频截图参数
					const snapshotParams = previewStore.getVideoSnapshotParams();
					console.log('从props设置后的视频参数:', snapshotParams);

					// 调用生成视频快照API
					checkAndGenerateSnapshots(snapshotParams);
				}
			}
		}
	}

	// 添加强制恢复方法
	const forceShowExtractedContent = () => {
		// 检查是否有数据需要展示
		const hasContent = (previewStore.extractedContentList && previewStore.extractedContentList.length > 0) ||
			(props.extractedContent && props.extractedContent.length > 0);

		// 检查是否在视频编辑页面和是否需要强制显示
		const isVideoEditingPage = props.isVideoEditingPage || route.path.includes('/VideoEditing');
		const forceShow = isVideoEditingPage && localStorage.getItem('showExtractedContent') === 'true';

		// 修改条件：只有在有内容时才设置
		if (hasContent) {
			console.log('有提取内容，设置isTimelineContent为true:',
				previewStore.extractedContentList?.length || props.extractedContent?.length || 0);

			// 强制设置标记为true
			previewStore.setIsTimelineContent(true);
			hasTimelineContent.value = true;

			// 如果Pinia中没有但props中有，进行恢复
			if ((!previewStore.extractedContentList || previewStore.extractedContentList.length === 0) &&
				props.extractedContent && props.extractedContent.length > 0) {
				console.log('从props恢复数据到Pinia');
				const contentToStore = JSON.parse(JSON.stringify(props.extractedContent));
				previewStore.setExtractedContentList(contentToStore);
			}
		} else if (forceShow) {
			// 如果强制显示但没有内容，检查localStorage
			console.log('强制显示标记为true，但无提取内容');

			// 清除localStorage中的标记，避免下次再显示
			localStorage.removeItem('showExtractedContent');
		}
	};

	// 立即调用
	forceShowExtractedContent();

	// 在下一个tick也调用一次，防止数据更新太慢
	nextTick(() => {
		forceShowExtractedContent();
	});

	// 检查localStorage中的showExtractedContent标记
	if (localStorage.getItem('showExtractedContent') === 'true') {
		console.log('检测到显示提取内容的标记');

		// 只有当有提取内容时才设置标记
		const hasContent = (previewStore.extractedContentList && previewStore.extractedContentList.length > 0) ||
			(props.extractedContent && props.extractedContent.length > 0);

		if (hasContent) {
			console.log('设置时间线内容为true');
			previewStore.setIsTimelineContent(true);
			hasTimelineContent.value = true;

			// 延迟再执行一次强制显示，确保在组件完全挂载后执行
			setTimeout(() => {
				console.log('延迟强制显示提取内容');
				forceShowExtractedContent();
			}, 500);
		} else {
			console.log('没有提取内容，不设置时间线内容标记');
		}

		// 清除标记，避免下次刷新页面时仍然显示
		localStorage.removeItem('showExtractedContent');
	}

	// 从Pinia store恢复视频选择状态
	console.log('组件挂载时检查Pinia中的视频列表:', {
		hasVideoList: Boolean(previewStore.videoList?.length),
		videoListLength: previewStore.videoList?.length || 0,
		videoListSample: previewStore.videoList && previewStore.videoList.length > 0
			? previewStore.videoList[0].name || '未命名视频'
			: 'none',
		selectedIds: previewStore.selectedVideoIds || []
	});

	// 如果Pinia中有视频列表但本地props中没有，恢复视频列表
	if (previewStore.videoList && previewStore.videoList.length > 0 &&
		(!props.videoList || props.videoList.length === 0)) {
		console.log('从Pinia恢复视频列表到组件');

		// 通知父组件更新videoList
		emit('update:videoList', previewStore.videoList);

		// 更新视频素材项的选中状态显示
		const videoItem = materialItems.find(item => item.type === 'video');
		if (videoItem) {
			console.log('更新视频素材显示状态');
		}
	}

	// 初始化视频素材显示
	const initVideoDisplay = () => {
		console.log('初始化视频素材显示');

		// 检查Pinia中是否有视频
		if (previewStore.videoList && previewStore.videoList.length > 0) {
			console.log('从Pinia恢复视频列表', previewStore.videoList.length);

			// 找到视频素材项
			const videoItem = materialItems.find(item => item.type === 'video');
			if (videoItem) {
				console.log('找到视频素材项，强制更新显示');

				// 触发计算属性重新计算
				nextTick(() => {
					// 强制视图更新
					if (showVideoItem.value) {
						console.log('视频素材应显示');
					} else {
						console.warn('视频素材不应显示，可能有问题');
					}
				});
			}
		}
	};

	// 立即执行初始化
	initVideoDisplay();

	// 100ms后再次执行，以确保所有状态都已更新
	setTimeout(initVideoDisplay, 100);

	// 添加全局点击事件监听器，在播放时点击外部区域自动暂停
	clickOutsideListener.value = (event) => {
		// 如果当前正在播放但不是暂停状态
		if (isPlaying.value && !isPaused.value) {
			// 检查点击的元素是否是播放控件或其子元素
			const isPlayControls = (
				// 检查是否点击了播放按钮
				event.target.closest('.video-play-icon') ||
				// 检查是否点击了进度条或播放器控件
				event.target.closest('.audio-player-control') ||
				// 检查是否点击了音量控制相关元素
				event.target.closest('.volume-slider-popup')
			);

			// 如果不是点击播放控件，则暂停播放
			if (!isPlayControls) {
				console.log('点击外部区域，暂停播放');

				// 记录当前暂停位置
				if (voicePlayer.value) {
					lastVoicePauseTime.value = voicePlayer.value.currentTime;
				}
				if (musicPlayer.value) {
					lastMusicPauseTime.value = musicPlayer.value.currentTime;
				}

				// 设置为暂停状态
				isPaused.value = true;

				// 暂停所有音频，但不改变isPlaying状态，这样特效会保留
				stopAllAudio(true); // 传递isPause=true表示这是暂停而不是停止
			}
		}
	};

	// 添加点击事件监听器
	document.addEventListener('click', clickOutsideListener.value);
})

// 监听 roleList 变化
watch(() => props.roleList, (newRoleList) => {
	if (newRoleList && newRoleList.length > 0 && newRoleList[0].audioUrl) {
		// 不需要手动设置 showVoiceRole，因为它现在是计算属性

		const roleName = newRoleList[0].selectedRole || newRoleList[0].name;

		// 更新 materialItems
		materialItems[0].selectedRole = roleName;

		// 同步到 store
		previewStore.setRole({
			name: roleName,
			voiceName: newRoleList[0].voiceName,
			audioUrl: newRoleList[0].audioUrl,
			isDemo: false
		});

		console.log('更新配音角色:', roleName, 'voiceName:', newRoleList[0].voiceName, '音频URL:', newRoleList[0].audioUrl);
	} else if (newRoleList && newRoleList.length === 0) {
		// 检查 store 中是否有角色信息，如果有则保持显示
		const storedRole = previewStore.selectedRole
		if (!(storedRole && typeof storedRole === 'object' && storedRole.name && storedRole.audioUrl)) {
			// 只有当 store 中也没有角色信息时，才清除角色
			previewStore.setRole(null);
		}
	}
}, { deep: true, immediate: true });

// 监听 store 中的角色信息变化
watch(() => previewStore.selectedRole, (newRole) => {
	if (newRole && typeof newRole === 'object' && newRole.name && newRole.audioUrl) {
		showVoiceRole.value = true;

		// 更新 materialItems
		if (materialItems[0]) {
			materialItems[0].selectedRole = newRole.name;
			materialItems[0].voiceName = newRole.voiceName;
			materialItems[0].audioUrl = newRole.audioUrl;
		}

		console.log('从 store 更新配音角色:', newRole);
	} else {
		showVoiceRole.value = false;
	}
}, { deep: true, immediate: true });

// 在组件卸载前保存角色信息
onBeforeUnmount(() => {
	stopAllAudio();

	// 移除事件监听器
	if (voicePlayer.value) {
		voicePlayer.value.removeEventListener('ended', handleVoiceEnded);
	}

	if (musicPlayer.value) {
		musicPlayer.value.removeEventListener('ended', handleMusicEnded);
	}

	const soundItem = materialItems.find(item => item.type === 'sound')
	if (soundItem && soundItem.selectedRole) {
		// 保存到store
		previewStore.setRole({
			name: soundItem.selectedRole,
			audioUrl: soundItem.audioUrl || '',
			voiceName: soundItem.voiceName
		});

		console.log('组件卸载前保存角色信息:', {
			name: soundItem.selectedRole,
			voiceName: soundItem.voiceName,
			audioUrl: soundItem.audioUrl
		});
	}
})

// 在组件挂载时初始化测试数据
onMounted(() => {
	// 打印初始状态
	console.log('PreviewPanel 挂载:', {
		editableContent: editableContent.value?.value,
		contentValue: contentValue.value,
		storeContent: previewStore.content,
		propsContent: props.content,
		musicList: musicStore.musicList,
		selectedRole: previewStore.selectedRole // 添加角色信息日志
	})

	// 确保内容同步
	nextTick(() => {
		if (editableContent.value) {
			const content = previewStore.content || props.content || ''
			// 对于input元素，设置value属性
			editableContent.value.value = content
			contentValue.value = content
			emit('update:content', content)
		}

		// 检查并修正所有素材项的音量值
		materialItems.forEach(item => {
			if (typeof item.volume === 'object' || isNaN(item.volume)) {
				console.warn(`检测到 ${item.title} 有无效的音量值，已重置为默认值`);
				item.volume = 50;
			}
		});
	})

	// 添加音量滑块实时监听功能
	document.addEventListener('mousemove', (e) => {
		// 如果音量滑块可见且正在播放
		if (showVolumeSlider.value && isPlaying.value && activeVolumeItem.value) {
			// 获取滑块元素中的当前音量值
			const volumeValue = activeVolumeItem.value.volume;

			// 根据类型应用到对应的播放器
			if (activeVolumeItem.value.type === 'music' && musicPlayer.value) {
				// 明确处理为0的情况
				if (volumeValue === 0) {
					musicPlayer.value.volume = 0;
					musicPlayer.value.muted = true;
					console.log('滑动检测：音乐音量为0，强制静音');
				} else {
					musicPlayer.value.volume = volumeValue / 100;
					musicPlayer.value.muted = false;
				}

				// 输出调试信息
				console.log(`实时拖动更新音乐音量: ${volumeValue}%`);
			}
			else if (activeVolumeItem.value.type === 'sound' && voicePlayer.value) {
				// ... 配音相关逻辑保持不变 ...
			}
		}
	});

	// 初始化每个播放器的音量
	materialItems.forEach(item => {
		if (item.type === 'sound' && voicePlayer.value) {
			voicePlayer.value.volume = item.volume / 100;
		} else if (item.type === 'music' && musicPlayer.value) {
			musicPlayer.value.volume = item.volume / 100;
		}
	});

	// 根据初始内容决定是否显示指南
	if (contentValue.value && contentValue.value.trim()) {
		showGuide.value = false;
	} else {
		showGuide.value = true;
	}
})

// 在组件更新前保存内容
const beforeRouteLeave = () => {
	// 确保在路由离开前保存内容
	if (contentValue.value) {
		console.log('路由离开前保存内容', contentValue.value)
		previewStore.setContent(contentValue.value);
	}
	if (titleValue.value) {
		previewStore.setTitle(titleValue.value);
	}

	// 不重置isTimelineContent标志，以确保文字样式保持一致
	// 特别是提取内容的展示方式
}

// 修改路由监听器，保留角色信息和标题
const clearTitleOnRouteChange = () => {
	// 注释掉以下自动清空的逻辑，完全不清空任何内容
	// titleValue.value = ''
	// emit('update:title', '')
	// previewStore.setTitle('')

	// 检查是否有标题，如果有则保持显示
	if (previewStore.title) {
		titleValue.value = previewStore.title;
	}

	// 确保保留提取内容的样式状态
	if (previewStore.isTimelineContent) {
		console.log('路由变化，保留提取内容样式状态');
		hasTimelineContent.value = true;
	}

	// 检查是否有角色信息，如果有则保持显示
	const storedRole = previewStore.selectedRole;
	if (storedRole && typeof storedRole === 'object' && storedRole.name && storedRole.audioUrl) {
		// 不需要手动设置 showVoiceRole，因为它现在是计算属性

		// 确保 materialItems 中的角色信息正确
		if (materialItems[0]) {
			materialItems[0].selectedRole = storedRole.name;
			materialItems[0].voiceName = storedRole.voiceName;
			materialItems[0].audioUrl = storedRole.audioUrl;
		}
	}

	console.log('路由变化，保留预览区标题、提取内容样式和角色信息');
}

// 组件挂载时添加路由监听
onMounted(() => {
	// 初始化配音角色
	initializeVoiceRole();

	// 监听路由变化
	router.beforeEach((to, from, next) => {
		// 检查是否有手动清空标记
		const isManuallyCleared = localStorage.getItem('content_manually_cleared') === 'true';

		// 不管是否手动清空，都恢复标题和角色信息
		// 对所有路由变化都检查角色信息
		const storedRole = previewStore.selectedRole;
		if (storedRole && typeof storedRole === 'object' && storedRole.name && storedRole.audioUrl) {
			// 确保 materialItems 中的角色信息正确
			if (materialItems[0]) {
				materialItems[0].selectedRole = storedRole.name;
				materialItems[0].voiceName = storedRole.voiceName;
				materialItems[0].audioUrl = storedRole.audioUrl;
			}
		}

		// 确保保留标题
		if (previewStore.title) {
			titleValue.value = previewStore.title;
		}

		// 如果未手动清空，才恢复其他内容
		if (!isManuallyCleared) {
			// 保存 isTimelineContent 状态在路由变化时
			if (previewStore.isTimelineContent) {
				console.log('全局路由守卫：保留提取内容样式状态');
				hasTimelineContent.value = true;
			}

			// 如果是编辑器页面之间的跳转，执行原有逻辑
			if (from.path.includes('/Editor') && to.path.includes('/Editor') && from.path !== to.path) {
				clearTitleOnRouteChange();
			}
		} else {
			console.log('检测到内容已被手动清空，跳过内容恢复逻辑，但保留标题');
		}
		next();
	});

	// 其他初始化代码...
})

// 在组件卸载前，添加调试日志
onBeforeUnmount(() => {
	stopAllAudio();

	// 移除事件监听器
	if (voicePlayer.value) {
		voicePlayer.value.removeEventListener('ended', handleVoiceEnded);
	}

	if (musicPlayer.value) {
		musicPlayer.value.removeEventListener('ended', handleMusicEnded);
	}

	const soundItem = materialItems.find(item => item.type === 'sound')
	if (soundItem && soundItem.selectedRole) {
		// 添加更多日志
		console.log('组件卸载前检查sound item状态:', {
			selectedRole: soundItem.selectedRole,
			voiceName: soundItem.voiceName,
			audioUrl: soundItem.audioUrl
		});

		// 确认voiceName存在
		if (!soundItem.voiceName) {
			console.warn('警告: 组件卸载前voiceName为空，使用roleList获取');

			// 尝试从props.roleList获取
			if (props.roleList && props.roleList.length > 0 && props.roleList[0].voiceName) {
				soundItem.voiceName = props.roleList[0].voiceName;
				console.log('从roleList获取到voiceName:', soundItem.voiceName);
			}
		}

		previewStore.setRole({
			name: soundItem.selectedRole,
			audioUrl: soundItem.audioUrl || '',
			voiceName: soundItem.voiceName
		})

		console.log('组件卸载前保存角色信息:', {
			name: soundItem.selectedRole,
			voiceName: soundItem.voiceName
		});
	}
})

// 在组件卸载前，保存当前内容到store
onBeforeUnmount(() => {
	console.log('PreviewPanel组件即将卸载');

	// 保存文本内容
	if (editableContent.value && !isPlaying.value) {
		const content = editableContent.value.innerText;
		if (content && content.trim() !== '') {
			console.log('保存文本内容到store:', content.substr(0, 20) + '...');
			previewStore.setContent(content);
		}
	}

	// 移除全局点击事件监听器
	if (clickOutsideListener.value) {
		document.removeEventListener('click', clickOutsideListener.value);
		clickOutsideListener.value = null;
	}

	// 如果不在视频编辑页面，清除可能的显示标记
	if (!route.path.includes('/VideoEditing')) {
		// 只清除显示标记，但保留内容，这样在返回时可以恢复
		localStorage.removeItem('showExtractedContent');

		// 不重置 isTimelineContent，因为这会导致提取内容丢失
		// 只在实际需要重置时才执行
	}
});

// 在PreviewPanel.vue中添加一个计算属性，确保视频列表正确处理
const videoList = computed(() => {
	// 优先使用previewStore中的数据，如果没有再使用props传入的数据
	return previewStore.videoList && previewStore.videoList.length > 0
		? previewStore.videoList
		: props.videoList || [];
});

// 停止所有音频播放
const stopAllAudio = (isPause = false) => {
	// 停止所有音频播放
	if (voicePlayer.value) {
		voicePlayer.value.pause();
		// 只有在完全停止（而不是暂停）时才重置时间
		if (!isPause) {
			voicePlayer.value.currentTime = 0;
		}
	}

	if (musicPlayer.value) {
		musicPlayer.value.pause();
		// 只有在完全停止（而不是暂停）时才重置时间
		if (!isPause) {
			musicPlayer.value.currentTime = 0;
		}
	}

	// 更新状态 - 只有在完全停止（而不是暂停）时才更新播放状态
	if (!isPause) {
		isPlaying.value = false;
		resetProgress();
		// 关闭播放器显示
		showAudioPlayer.value = false;
		// 重置暂停状态
		isPaused.value = false;
		lastVoicePauseTime.value = 0;
		lastMusicPauseTime.value = 0;
	} else {
		// 关键修改：暂停时保持isPlaying.value为true，这样特效会保留
		// isPlaying.value = false; // 这行被移除了，保持isPlaying为true

		// 为防止定时器被清除，我们需要保留进度信息但停止更新
		clearProgressTimer();
		// 在暂停时保持播放器显示
		showAudioPlayer.value = true;

		// 确保最后一次更新进度
		if (voicePlayer.value) {
			currentTime.value = voicePlayer.value.currentTime;
			progress.value = (currentTime.value / voiceRoleDuration.value) * 100;
		} else if (musicPlayer.value) {
			currentTime.value = musicPlayer.value.currentTime;
			progress.value = (currentTime.value / musicDuration.value) * 100;
		}

		// 最后一次更新高亮文本，确保暂停时高亮保持在当前位置
		updateKaraokeText(progress.value);
	}
}

// 处理配音角色播放结束
const handleVoiceEnded = () => {
	console.log('配音播放结束');

	// 完全停止播放（不是暂停）并重置所有状态
	stopAllAudio(false);

	// 明确重置暂停状态
	isPaused.value = false;
	lastVoicePauseTime.value = 0;
	lastMusicPauseTime.value = 0;

	// 如果有音乐正在播放，也停止
	if (musicPlayer.value) {
		musicPlayer.value.pause();
		musicPlayer.value.currentTime = 0;
		if (loopInterval.value) {
			clearInterval(loopInterval.value);
			loopInterval.value = null;
		}
	}

	// 确保所有定时器都被清除
	clearProgressTimer();

	// 设置进度为100%并进行最后一次高亮更新
	progress.value = 100;
	updateKaraokeText(100, true); // 添加第二个参数表示这是最终更新
};

// 修改 handleContentExtract 方法，确保提取的内容不含颜文字但保留标点符号
const handleContentExtract = (extractedText) => {
	console.log('收到提取的内容', extractedText);

	// 确保编辑区有内容 - 使用innerText而不是innerHTML，避免HTML注入
	if (editableContent.value) {
		// 直接使用原始文本，不做任何处理
		editableContent.value.innerText = extractedText;
	}

	// 更新内容变量 - 保留原始内容的所有格式
	contentValue.value = extractedText;

	// 确保设置时间线标记为true
	hasTimelineContent.value = true;

	// 确保预览Store中的标记也设置为true
	previewStore.setIsTimelineContent(true);

	// 将原始文本也保存到store中，确保一致性
	previewStore.setContent(extractedText);

	// 标记不再显示指南内容
	showGuide.value = false;

	// 显示成功提示
	ElMessage.success('成功提取文案内容');
};

// 修改预览区域内容更新时确保过滤颜文字
watch(() => previewStore.content, (newContent) => {
	// 如果有新内容且不在更新中
	if (newContent && editableContent.value && !isUpdatingContent.value) {
		// 设置标记，避免递归更新
		isUpdatingContent.value = true;

		// 对于input元素，直接设置value属性
		editableContent.value.value = newContent;
		contentValue.value = newContent;

		// 根据previewStore中的标记设置是否是来自timeline的内容
		// 这样可以让预览面板知道应该使用什么样的样式
		hasTimelineContent.value = previewStore.isTimelineContent || false;

		// 如果内容不为空，隐藏指南
		if (newContent.trim()) {
			showGuide.value = false;
		}

		// 重置标记
		setTimeout(() => {
			isUpdatingContent.value = false;
		}, 0);
	}
	// 如果当前内容为空，且存储中有内容，则使用存储中的内容
	else if (newContent && editableContent.value) {
		editableContent.value.innerText = newContent;
		// 使用store中的标记来决定是否是timeline内容
		hasTimelineContent.value = previewStore.isTimelineContent || false;
		console.log('从存储加载内容，timeline标记:', hasTimelineContent.value);
	}
}, { immediate: true })

// 添加对timeline标记的监听
watch(() => previewStore.isTimelineContent, (isTimeline) => {
	console.log('timeline内容标记更新:', isTimeline);
	// 直接根据store中的值更新本地标记
	hasTimelineContent.value = isTimeline;
}, { immediate: true })

// 添加新的状态变量控制指南显示
const showGuide = ref(true); // 默认显示指南

// 处理编辑区域点击
const handleEditorClick = () => {
	// 点击编辑区域时隐藏指南
	showGuide.value = false;
}

// 处理指南区域点击
const handleGuideClick = () => {
	// 点击指南也会隐藏指南并聚焦编辑区域
	showGuide.value = false;
	nextTick(() => {
		if (editableContent.value) {
			editableContent.value.focus();
		}
	});
}

// 监听内容变化
watch(() => contentValue.value, (newVal) => {
	// 根据内容是否为空控制指南显示
	showGuide.value = !newVal || !newVal.trim();
});

// 添加计算属性控制音乐素材显示
const showMusicItem = computed(() => {
	return effectiveMusicList.value && effectiveMusicList.value.length > 0;
});

// 添加进度更新相关方法
const startProgressTimer = () => {
	// 先清除现有定时器
	clearProgressTimer();

	// 增加检查：确保只有在播放状态下才启动定时器
	if (!isPlaying.value) {
		console.log('播放已结束，不启动定时器');
		return;
	}

	// 仅在第一次播放时准备文本行，避免暂停后恢复播放时重新处理文本造成闪烁
	if (!isPaused.value) {
		prepareTextLines();
	}

	// 恢复播放时，立即更新一次高亮状态，确保从暂停时的高亮状态恢复
	if (isPaused.value) {
		if (voicePlayer.value) {
			currentTime.value = voicePlayer.value.currentTime;
			progress.value = (currentTime.value / voiceRoleDuration.value) * 100;

			// 立即更新卡拉OK文本高亮
			updateKaraokeText(progress.value);
		} else if (musicPlayer.value) {
			currentTime.value = musicPlayer.value.currentTime;
			progress.value = (currentTime.value / musicDuration.value) * 100;

			// 立即更新卡拉OK文本高亮
			updateKaraokeText(progress.value);
		}
	}

	// 启动定时器前再次检查播放状态
	if (!isPlaying.value) {
		return;
	}

	audioTimer.value = setInterval(() => {
		// 播放过程中随时检查状态，如果已停止则清除定时器
		if (!isPlaying.value) {
			clearProgressTimer();
			return;
		}

		if (!isDragging.value) {
			if (voicePlayer.value) {
				currentTime.value = voicePlayer.value.currentTime;
				progress.value = (currentTime.value / voiceRoleDuration.value) * 100;

				// 使用卡拉OK效果更新文本
				updateKaraokeText(progress.value);
			} else if (musicPlayer.value) {
				currentTime.value = musicPlayer.value.currentTime;
				progress.value = (currentTime.value / musicDuration.value) * 100;

				// 使用卡拉OK效果更新文本
				updateKaraokeText(progress.value);
			}
		}
	}, 100);
};

const clearProgressTimer = () => {
	if (audioTimer.value) {
		clearInterval(audioTimer.value);
		audioTimer.value = null;
	}
};

const resetProgress = () => {
	currentTime.value = 0;
	progress.value = 0;
};

// 添加时间格式化方法
const formatTime = (seconds) => {
	if (seconds === undefined || seconds === null || isNaN(seconds)) return '00:00';
	const mins = Math.floor(seconds / 60);
	const secs = Math.floor(seconds % 60);
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 计算当前播放时间显示
const timeDisplay = computed(() => {
	return `${formatTime(currentTime.value)}/${formatTime(audioDuration.value)}`;
});

// 添加计算属性控制视频素材显示
const showVideoItem = computed(() => {
	// 检查是否有视频
	const hasVideos = effectiveVideoList.value.length > 0;

	// 不再考虑是否处于预览工具模式，只要有视频就显示
	return hasVideos;
});

// 添加用于控制音量滑块位置的响应式样式对象
const volumeSliderStyle = ref({
	position: 'fixed',
	zIndex: '9999999'
});

// 添加这行代码，暴露方法给父组件
defineExpose({
	handleContentExtract,
	updateMaterialVolume
});


// 创建计算属性获取预览文本
const previewContent = computed({
	get: () => {
		const content = editableContent.value?.innerText || ''
		return content
	},
	set: (value) => {
		if (editableContent.value) {
			editableContent.value.innerText = value || ''

			// 检查内容是否来自timeline
			if (value && previewStore.content === value) {
				hasTimelineContent.value = true
				console.log('检测到timeline内容更新')
			}
		}
	}
})

// 监听预览存储内容变化
watch(() => previewStore.content, (newContent) => {
	// 如果当前内容为空，且存储中有内容，则使用存储中的内容
	if ((!previewContent.value || previewContent.value.trim() === '') && newContent) {
		if (editableContent.value) {
			editableContent.value.innerText = newContent
			// 使用store中的标记来决定是否是timeline内容
			hasTimelineContent.value = previewStore.isTimelineContent || false
			console.log('从存储加载内容，timeline标记:', hasTimelineContent.value)
		}
	}
}, { immediate: true })

// 添加对timeline标记的监听
watch(() => previewStore.isTimelineContent, (isTimeline) => {
	console.log('timeline内容标记更新:', isTimeline)
	hasTimelineContent.value = isTimeline || false
}, { immediate: true })

// 添加新的状态变量控制提取内容显示
const showExtractedContent = computed(() => {
	// 检查是否有提取内容并且是否是时间线内容
	const hasExtractedContent = effectiveExtractedContent.value &&
		effectiveExtractedContent.value.length > 0;

	// 从store获取isTimelineContent标记
	const isTimelineContent = previewStore.isTimelineContent;

	// 优先使用props中的isVideoEditingPage标记，如果没有再检查路由
	// 这样可以确保在路由切换时保持一致的样式
	const isVideoEditingPage = props.isVideoEditingPage || route.path.includes('/VideoEditing');

	// 检查当前是否处于预览工具状态
	const isPreviewTool = props.activeToolId === 2;

	// 修改条件：
	// 1. 只有在视频编辑页面才显示提取内容
	// 2. 在其他页面中，即使isTimelineContent为true也不显示提取内容
	// 3. 在视频编辑页面中，需要同时满足有提取内容且标记为时间线内容
	// 4. 只有在预览工具(activeToolId === 2)激活时才显示提取内容列表
	return isVideoEditingPage && hasExtractedContent && isTimelineContent && isPreviewTool;
});

// 添加一个计算属性，合并props和store中的提取内容
const effectiveExtractedContent = computed(() => {
	// 如果props中有提取内容，优先使用props中的
	// 否则使用store中存储的提取内容
	const content = (props.extractedContent && props.extractedContent.length > 0)
		? props.extractedContent
		: previewStore.extractedContentList;

	// 在视频成片页面，如果提取内容为空，返回一个空数组，
	// 但showExtractedContent会确保显示"暂无提取内容"的提示
	return content || [];
});

// 添加监听器确保videoList变化时更新showVideoItem计算属性
watch(() => props.videoList, (newList) => {
	console.log('props.videoList变化:', newList?.length || 0);

	// 检查Pinia中是否已有视频列表，如果没有，才使用props的数据
	if ((!previewStore.videoList || previewStore.videoList.length === 0) &&
		newList && newList.length > 0) {

		const firstVideo = newList[0];
		if (firstVideo && (firstVideo.url || firstVideo.videoUrl)) {
			const videoUrl = firstVideo.url || firstVideo.videoUrl;

			// 检查是否需要更新URL
			if (!previewStore.currentVideoUrl || previewStore.currentVideoUrl !== videoUrl) {
				console.log('从props监听器检测到新的视频URL:', videoUrl);
				previewStore.setCurrentVideoUrl(videoUrl);

				// 同时设置从提取内容获取的时间点
				const extractedTimePoints = getExtractedTimePoints(effectiveExtractedContent.value);
				console.log('监听器设置的时间点:', extractedTimePoints);
				previewStore.setVideoTimestamps(extractedTimePoints);

				// 如果有时间点变化，可能需要重新生成快照
				const snapshotParams = previewStore.getVideoSnapshotParams();
				checkAndGenerateSnapshots(snapshotParams);

				// 添加视频后标记内容已编辑
				setContentEdited(true);
				console.log('添加视频，标记内容已编辑');
			}
		}
	}
}, { immediate: true });

// 添加计算属性获取有效的视频列表
const effectiveVideoList = computed(() => {
	// 优先使用 previewStore 中的数据，如果没有再使用 props 传入的
	return previewStore.videoList && previewStore.videoList.length > 0
		? previewStore.videoList
		: props.videoList || [];
});

// 添加一个新的格式化函数，处理角色名称显示和折行
const formatRoleName = (name) => {
	if (!name) return '';

	// 去除空格
	const trimmedName = name.trim();

	// 如果长度为4，在中间添加折行
	if (trimmedName.length === 4) {
		return `${trimmedName.substring(0, 2)}<br>${trimmedName.substring(2, 4)}`;
	}

	// 其他情况不添加折行
	return trimmedName;
};

// 计算有效的角色名称
const effectiveRoleName = computed(() => {
	// 优先从previewStore获取角色
	const roleName = previewStore.selectedRole?.name || '';
	return formatRoleName(roleName);
});

/**
 * 处理提取内容项点击事件
 * @param {Object} item - 被点击的内容项
 */
const handleExtractedItemClick = (item) => {
	selectedExtractedItem.value = item;
	// 获取start时间并发送给父组件
	const startTime = item.time ? item.time.start : 0;

	// 先发送时间点事件，再切换工具，确保时间点在工具切换后能保留
	emit('extractedItemClick', startTime);
	// 然后通知父组件切换到预览工具
	emit('switchToPreview');

	console.log('点击了提取内容项，start时间:', startTime);
};

// 添加监听器，当提取内容更新时重新生成视频快照
watch(() => previewStore.extractedContentList, async (newExtractedContent, oldExtractedContent) => {
	// 确认有内容变化，避免无意义的重复调用
	const isNewContent = newExtractedContent &&
		(!oldExtractedContent ||
			newExtractedContent.length !== oldExtractedContent.length ||
			JSON.stringify(newExtractedContent) !== JSON.stringify(oldExtractedContent));

	if (isNewContent && previewStore.currentVideoUrl) {
		// 从新的提取内容中获取时间点
		const extractedTimePoints = getExtractedTimePoints(newExtractedContent);

		// 更新时间点到store
		if (extractedTimePoints && extractedTimePoints.length > 0) {
			previewStore.setVideoTimestamps(extractedTimePoints);

			// 获取最新的快照参数
			const snapshotParams = previewStore.getVideoSnapshotParams();

			// 立即调用生成快照函数，不使用延时
			checkAndGenerateSnapshots(snapshotParams);
		}
	}
}, { deep: true });

// 方法: 移除视频 - 增强后会同步更新左侧选择状态
const removeVideo = (index) => {
	// 确保视频列表存在
	const currentVideoList = effectiveVideoList.value;
	if (currentVideoList && currentVideoList.length > index) {
		// 获取视频名称用于显示成功消息
		const videoName = currentVideoList[index].name || '未命名视频';

		// 获取要删除的视频信息，用于后续取消左侧选中状态
		const removedVideo = { ...currentVideoList[index] };

		// 简化日志，减少控制台输出
		// console.log('准备删除视频，原始数据:', removedVideo);

		// 高效添加所有必要的匹配字段
		// 使用一个高效的辅助对象添加所有匹配信息
		const enhancedVideo = {
			id: removedVideo.id || `vid-${Date.now()}`,
			title: removedVideo.title || removedVideo.name || '未命名视频',
			name: removedVideo.name || removedVideo.title || '未命名视频',
			url: removedVideo.url || removedVideo.videoUrl || '',
			videoUrl: removedVideo.videoUrl || removedVideo.url || ''
		};

		// 创建新数组以避免直接修改数据，使用扩展运算符实现高效复制
		const newVideoList = [...currentVideoList];
		newVideoList.splice(index, 1);

		// 批量高效更新：先更新Pinia store，然后通知父组件
		// 这样可以减少重复的数据处理和DOM更新
		previewStore.setVideoList(newVideoList);

		// 高效更新选中ID列表 - 只更新被删除视频对应的ID
		if (previewStore.selectedVideoIds && previewStore.selectedVideoIds.length > 0) {
			const videoId = enhancedVideo.id;
			// 只过滤掉当前被删除视频的ID，而不是批量处理所有视频
			previewStore.setSelectedVideoIds(
				previewStore.selectedVideoIds.filter(id => id !== videoId)
			);
		}

		// 确保在localStorage中也删除相关数据
		try {
			// 获取原有存储数据
			const storageKey = 'editor-preview-data';
			const storedData = localStorage.getItem(storageKey);

			if (storedData) {
				const parsedData = JSON.parse(storedData);

				// 如果存在videoList数据
				if (parsedData.videoList && Array.isArray(parsedData.videoList)) {
					// 从localStorage中也删除相应的视频
					const updatedVideoList = parsedData.videoList.filter(video =>
						video.id !== enhancedVideo.id
					);

					// 更新localStorage中的数据
					parsedData.videoList = updatedVideoList;
					localStorage.setItem(storageKey, JSON.stringify(parsedData));
					console.log('从localStorage中删除视频成功');
				}
			}
		} catch (e) {
			console.error('尝试从localStorage中删除视频失败:', e);
		}
		// 使用增强的视频对象通知父组件取消选中状态
		// 只发送当前被删除的单个视频信息，不是批量操作
		emit('cancel-video-selection', enhancedVideo);

		// 批量通知更新到父组件
		emit('update:videoList', newVideoList);

		ElMessage.success(`已移除视频: ${videoName}`);
	}
}

// 添加新的响应式变量跟踪文本高亮进度
const textHighlightProgress = ref(0)
const scrollProgress = ref(0)



// 更新：更新文本高亮和滚动位置的方法，实现按行高亮
const updateTextHighlightAndScroll = (progress) => {
	// 更新高亮进度
	textHighlightProgress.value = progress

	// 获取文本预览元素
	const textArea = editableContent.value
	if (!textArea) return

	// 获取文本内容
	const text = textArea.innerText || ''
	if (!text.trim()) return

	// 分割成行
	const lines = text.split('\n')
	const totalLines = lines.length

	// 计算当前应该高亮的行索引
	const currentLineIndex = Math.floor((totalLines * progress) / 100)

	// 清除之前的高亮
	clearLineHighlights(textArea)

	// 添加当前行的高亮
	if (currentLineIndex < totalLines) {
		// 找到当前行的元素并添加高亮
		highlightLine(textArea, currentLineIndex)

		// 确保当前行在视图中可见
		scrollToLine(textArea, currentLineIndex, totalLines)
	}
}

// 清除所有行的高亮
const clearLineHighlights = (element) => {
	// 如果使用span包装，可以移除所有高亮span
	const highlightedSpans = element.querySelectorAll('.highlight-line')
	highlightedSpans.forEach(span => {
		// 保留文本内容
		if (span.parentNode) {
			const textNode = document.createTextNode(span.textContent || '')
			span.parentNode.replaceChild(textNode, span)
		}
	})
}

// 高亮指定行
const highlightLine = (element, lineIndex) => {
	const text = element.innerText || ''
	const lines = text.split('\n')

	if (lineIndex >= 0 && lineIndex < lines.length) {
		// 创建行高亮的临时文档
		const tempDiv = document.createElement('div')
		tempDiv.innerHTML = element.innerHTML

		// 获取行节点并包装
		let currentNode = tempDiv.firstChild
		let currentLine = 0
		let targetNode = null

		// 简单的递归函数查找目标行
		const findLine = (node, currentLineCount) => {
			if (!node) return currentLineCount

			if (node.nodeType === 3) { // 文本节点
				const text = node.textContent || ''
				const lineBreaks = (text.match(/\n/g) || []).length

				if (currentLineCount <= lineIndex && lineIndex < currentLineCount + lineBreaks + 1) {
					// 目标行在这个文本节点中
					const lines = text.split('\n')
					const linePosition = lineIndex - currentLineCount

					if (linePosition < lines.length) {
						// 创建包装元素
						const span = document.createElement('span')
						span.textContent = lines[linePosition]
						span.className = 'highlight-line'

						// 替换原文本节点
						const beforeText = lines.slice(0, linePosition).join('\n')
						const afterText = lines.slice(linePosition + 1).join('\n')

						if (beforeText) {
							node.parentNode.insertBefore(document.createTextNode(beforeText + '\n'), node)
						}

						node.parentNode.insertBefore(span, node)

						if (afterText) {
							node.parentNode.insertBefore(document.createTextNode('\n' + afterText), node)
						}

						node.parentNode.removeChild(node)
						targetNode = span
						return currentLineCount + lineBreaks + 1
					}
				}
				return currentLineCount + lineBreaks
			} else if (node.nodeType === 1) { // 元素节点
				let newLineCount = currentLineCount
				let child = node.firstChild

				while (child && !targetNode) {
					newLineCount = findLine(child, newLineCount)
					child = child.nextSibling
				}

				return newLineCount
			}

			return currentLineCount
		}

		findLine(tempDiv, 0)

		// 应用更改
		if (targetNode) {
			element.innerHTML = tempDiv.innerHTML
		}
	}
}



const prepareTextLines = () => {
	console.log("使用input元素，卡拉OK文本处理功能已禁用");
	// input元素不支持复杂的DOM操作，此功能暂时禁用
}

// 添加：实现按行顺序高亮的卡拉OK效果 - 适配input元素
const updateKaraokeText = (progress, isFinalUpdate = false) => {
	// 仅更新文本高亮进度值，用于CSS变量
	textHighlightProgress.value = progress;
	// 减少日志输出
	if (!isFinalUpdate && progress % 10 === 0) {
		console.log("更新文本高亮进度值:", progress);
	}
}

const updateCurrentLine = (progress) => {
	const textArea = editableContent.value;
	if (!textArea) return;

	// input元素不支持此功能
	console.log("使用input元素，不支持按行高亮功能");
}



// 添加到方法部分
const forceTitleSync = () => {
	// 确保预览存储与输入框同步
	previewStore.setTitle(titleValue.value || '');
	emit('update:title', titleValue.value || '');
}

// 处理缩略图加载错误
const handleThumbnailError = (event, item) => {
	console.error('视频缩略图加载失败:', item.thumbnailUrl);
	// 设置为默认缩略图
	event.target.src = defaultThumbnail;
	// 记录到日志
	console.warn(`提取内容ID: ${item.id || '未知'} 的缩略图加载失败，使用默认图片`);

	// 更新item避免再次尝试加载失败的URL
	if (item.thumbnailUrl) {
		// 记录原始URL便于调试
		item._originalThumbnailUrl = item.thumbnailUrl;
		item.thumbnailUrl = defaultThumbnail;
	}
}

// 监听路由变化，确保在离开视频编辑页面时能正确显示文本内容
watch(() => route.path, (newPath, oldPath) => {
	console.log('路由变化:', oldPath, '->', newPath);

	// 如果离开了VideoEditing页面，确保正确切换显示内容
	if (oldPath && oldPath.includes('/VideoEditing') && newPath && !newPath.includes('/VideoEditing')) {
		// 从视频编辑页面切换到其他页面，此时应该显示文本内容
		console.log('离开视频编辑页面，准备显示文本内容');

		// 检查是否有需要恢复的文本内容
		if (previewStore && previewStore.content && previewStore.content.trim() !== '') {
			// 恢复编辑区内容，但不更改isTimelineContent标记
			// 这样在返回视频编辑页面时仍能显示提取内容
			if (editableContent && editableContent.value) {
				console.log('恢复文本内容:', previewStore.content.substr(0, 20) + '...');
				editableContent.value.innerText = previewStore.content;
			}
		}
	}
}, { immediate: false }); // 将immediate改为false，确保组件完全挂载后再执行

// 监听 showExtractedContent 的变化，确保在状态切换时正确处理
watch(() => showExtractedContent.value, (newValue, oldValue) => {
	// 如果从显示提取内容切换到不显示提取内容
	if (newValue === false && oldValue === true) {
		console.log('从提取内容视图切换到文本编辑视图');

		// 确保下一个渲染周期能正确显示文本内容
		nextTick(() => {
			if (editableContent && editableContent.value) {
				// 检查是否有存储的文本内容
				if (previewStore && previewStore.content && previewStore.content.trim() !== '') {
					console.log('显示存储的文本内容');
					editableContent.value.innerText = previewStore.content;
				} else {
					// 如果没有存储内容，可以显示默认或空内容
					console.log('没有存储内容，显示空内容');
					editableContent.value.innerText = '';
				}

				// 聚焦文本区域以便用户可以立即编辑
				try {
					editableContent.value.focus();
				} catch (e) {
					console.error('聚焦文本区域失败:', e);
				}
			}
		});
	}
}, { immediate: false }); // 将immediate改为false，确保组件完全挂载后再执行

// 添加对route.query的监听
watch(() => route.query, (newQuery) => {
	// 检查路由参数，如果有refresh参数且值为true，则清空预览数据
	if (newQuery.refresh === 'true') {
		console.log('监听到refresh参数变化，清空预览数据');
		clearContent();
		// 清除URL中的参数，避免刷新页面时重复清空
		router.replace({ query: {} });
	}
}, { immediate: true });  // 添加immediate: true，确保初始化时也执行一次

// 注入setContentEdited函数
const setContentEdited = inject('setContentEdited', () => {
	// 当找不到函数时，提供一个默认的空实现，避免显示警告
	console.log('内容已修改，但setContentEdited未注入');
});

// 在props.videoList监听后添加监听musicStore.musicList的函数
// 监听音乐列表变化
watch(() => musicStore.musicList, (newList, oldList) => {
	// 检查是否真的有变化（长度或内容）
	const hasChanged = !oldList || oldList.length !== newList.length ||
		JSON.stringify(newList) !== JSON.stringify(oldList);

	// 如果有变化，标记内容已编辑
	if (hasChanged && newList && newList.length > 0) {
		console.log('音乐列表变化，标记内容已编辑');
		setContentEdited(true);
	}
}, { deep: true });

// Add method to allow parent component to update material item volume by type
function updateMaterialVolume(type, volume) {
	const item = materialItems.find(it => it.type === type);
	if (item) {
		item.volume = volume;
	}
}



</script>

<style lang="scss" scoped>
// 右侧内容容器
.right-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-width: 400px;
	background-color: #fff;
	border-radius: 0 8px 8px 0;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

// 添加顶部头部容器，包含标题栏和素材选择器
.preview-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px 5px;
}

// 右侧预览区标题栏
.preview-title-bar {
	display: flex;
	align-items: center;
	flex: 0 0 auto;
	max-width: 35%; // 限制标题栏宽度，为素材项留出更多空间
}

.title {
	font-size: 18px;
	font-weight: 700;
	color: #303133;
	display: flex;
	align-items: flex-start;
	gap: 8px;
	padding-top: 5px;

	.title-icon {
		width: 28px;
		height: 28px;
		object-fit: contain;
	}
}

.generate-video-btn {
	width: 110px;
	height: 42px;
	padding: 0 16px;
	border-radius: 4px;
	background: linear-gradient(90deg, #0AAF60, #A4CB55);
	color: #ffffff;
	border: none;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s;
	display: flex;
	align-items: center;
	justify-content: center;

	&:hover {
		opacity: 0.9;
	}
}

// 修改顶部material-selector样式
.material-selector {
	display: flex;
	align-items: center;
	margin-left: 30px; // 增加与标题栏的间距

	// 素材项群组
	.material-items-group {
		display: flex;
		gap: 18px; // 增加素材项之间的间距（从12px增加到18px）
	}

	// 播放图标样式优化
	.video-play-icon {
		width: 32px;
		height: 32px;
		margin-left: 20px !important; // 增加播放按钮的左边距
		cursor: pointer;
		transition: all 0.2s;

		&:hover {
			opacity: 0.8;
			transform: scale(1.1);
		}
	}

	// 素材项样式
	.material-item {
		flex: 0 0 auto;
		width: 256px; // 从170px增加到234px
		height: 53px; // 从44px增加到53px
		padding: 0 15px; // 保持原有内边距
		border-radius: 4px;
		background: #F5F5F5;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		/* 确保相对定位，以便放置删除图标 */
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
		transition: all 0.2s ease;

		/* 删除图标样式 - 移到material-item下，使其位于右上角 */
		.delete-icon {
			width: 16px;
			height: 16px;
			cursor: pointer;
			position: absolute;
			/* 绝对定位 */
			top: -8px;
			/* 向上偏移 */
			right: -8px;
			/* 向右偏移 */
			z-index: 10;
			/* 提高z-index确保在最上层 */
			transition: all 0.2s;
			padding: 3px;
			/* 增加内边距扩大点击区域 */
			box-sizing: content-box;
			/* 确保padding不会改变元素实际大小 */
			margin: 0;
			/* 清除可能的外边距 */
			pointer-events: auto;
			/* 确保图标可接收点击事件 */

			/* 创建一个点击区域 */
			&::before {
				content: '';
				position: absolute;
				top: -5px;
				left: -5px;
				right: -5px;
				bottom: -5px;
				border-radius: 50%;
			}

			&:hover {
				opacity: 0.7;
				transform: scale(1.1);
			}
		}

		&:hover {
			box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
			background: #F8F8F8;
		}

		// 左侧区域样式优化
		.material-left {
			display: flex;
			align-items: center;
			gap: 12px; // 增加图标和文字间距

			.material-icon-container {
				display: flex;
				align-items: center;
				gap: 10px; // 增加图标容器内的间距

				.material-icon {
					width: 24px;
					height: 24px;
					background-color: transparent;
					border-radius: 4px;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					&::before {
						content: '';
						position: absolute;
						width: 100%;
						height: 100%;
						background-size: contain;
						background-repeat: no-repeat;
						background-position: center;
					}
				}

				span {
					font-size: 14px;
					color: #303133;
					font-weight: 500;
				}
			}

			// 喇叭图标样式优化
			.close-icon {
				width: 20px;
				height: 20px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #909399;
				cursor: pointer;
				margin-left: -5px; // 减小负边距，让图标间距更合理
				background-image: url('@/assets/img/shengyin.png');
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				font-size: 0;
				transition: all 0.2s;

				&.volume-on {
					background-image: url('@/assets/img/you.png');
				}

				&:hover {
					opacity: 0.7;
					transform: scale(1.1);
				}
			}
		}

		// 右侧控制区样式优化
		.right-controls {
			display: flex;
			align-items: center;
			margin-left: 8px; // 增加左边距

			.add-sound {
				display: flex;
				align-items: center;
				margin: 0;

				.add-sound-text {
					display: flex;
					align-items: center;
					gap: 5px;
					background: #FFFFFF;
					border: none;
					/* 去掉边框 */
					border-radius: 4px;
					padding: 0 10px;
					height: 28px;
					min-width: 85px;
					cursor: pointer;
					transition: all 0.2s;
					position: relative;
					/* 添加相对定位以便放置删除图标 */

					.add-text {
						color: #0AAF60;
						font-size: 14px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						width: 100%;
						display: inline-block;
						max-width: 100%;
					}

					&.selected {
						background: transparent;
						/* 去掉选中状态的背景色 */
						border: none;
						/* 去掉选中状态的边框 */
						padding-right: 10px;
						/* 恢复内边距 */
						max-width: 80px;

						.add-text {
							color: #606266;
						}

						/* 删除旧的删除图标样式，已移至material-item */
					}

					&:not(.selected):hover {
						transform: translateY(-1px);

						.add-text {
							color: #078f4d;
						}
					}

					&.disabled {
						opacity: 0.7;
						cursor: not-allowed;
					}
				}
			}
		}
	}
}

// 右侧预览区样式
.preview-section {
	flex: 1;
	padding: 0 20px 20px;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	height: fit-content;
	margin-left: 0;

	// 预览内容区样式优化
	.preview-content {
		flex: 1;
		background: #fff;
		border: none; // 移除灰色边框
		border-radius: 4px;
		overflow: hidden;
		line-height: 1.8;
		position: relative;
		margin-top: 5px;

		/* 提取中的遮罩层样式 */
		.extraction-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(255, 255, 255, 0.9);
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 10;
		}

		.extraction-loading {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 15px;
		}

		.extraction-spinner {
			width: 40px;
			height: 40px;
			border: 3px solid #f3f3f3;
			border-top: 3px solid #0AAF60;
			border-radius: 50%;
			animation: spin 1s linear infinite;
		}

		.extraction-text {
			color: #0AAF60;
			font-size: 16px;
			font-weight: 500;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		.preview-textarea {
			width: 100%;
			height: 700px !important;
			min-height: unset !important;
			max-height: none !important;
			border: none;
			background: transparent;
			outline: none;
			font-size: 14px !important;
			line-height: 1.8;
			color: #303133;
			padding: 15px;
			overflow-y: auto;
			white-space: pre-wrap;
			word-break: break-word;
			resize: none;
			transition: all 0.2s;

			/* 当为input元素时，确保样式一致 */
			display: block;
			box-sizing: border-box;

			/* 滚动条样式优化 */
			&::-webkit-scrollbar {
				width: 6px;
			}

			&::-webkit-scrollbar-track {
				background: transparent;
			}

			&::-webkit-scrollbar-thumb {
				background: #E0E0E0;
				border-radius: 3px;
			}

			&:empty:before {
				content: attr(placeholder);
				color: #909399;
				pointer-events: none;
				white-space: pre-wrap;
				display: block;
				line-height: 1.8;
			}

			// 编辑时样式优化
			&:focus {
				box-shadow: none; // 移除边线效果
			}

			// 播放时样式优化
			&.playing {
				/* 移除这些阻止编辑的属性 */
				/*
				pointer-events: none;
				user-select: none;
				*/

				/* 移除背景色，只保留左边框作为视觉提示 */
				border-left: none;
			}

			// timeline内容显示样式
			&.timeline-content {
				// font-size: 16px;
				line-height: 2;
				color: #333;

				// 段落间距
				p,
				div {
					margin-bottom: 12px;
				}

				// 强调显示
				strong,
				b {
					font-weight: 600;
					/* color: #0AAF60; */
					color: inherit;
				}
			}
		}

		// 添加右下角图片的样式优化
		.corner-image {
			position: absolute;
			bottom: 20px;
			right: 20px;
			width: 26px;
			height: 26px;
			pointer-events: auto;
			z-index: 1;
			cursor: pointer;
			transition: all 0.2s;

			&:hover {
				opacity: 0.7;
				transform: scale(1.1);
			}
		}

		// 指南样式优化
		.guide-content {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: #fff;
			z-index: 1; // 低于提取遮罩,高于编辑器
			display: flex;
			align-items: flex-start;
			justify-content: flex-start;
			cursor: text;
			padding: 15px;

			.guide-text {
				color: #909399;
				font-size: 14px;
				line-height: 1.8;
				white-space: pre-line;
				text-align: left;
			}
		}

		// 提取内容展示区域样式
		.extracted-content-list {
			width: 100%;
			height: 700px;
			overflow-y: auto;
			padding: 12px 10px;
			color: #303133;
			position: relative;

			/* 滚动条样式 */
			&::-webkit-scrollbar {
				width: 6px;
			}

			&::-webkit-scrollbar-track {
				background: transparent;
			}

			&::-webkit-scrollbar-thumb {
				background: #E0E0E0;
				border-radius: 3px;

				&:hover {
					background: #C0C0C0;
				}
			}

			/* 动画容器 */
			.animation-container {
				width: 100%;
			}

			/* 无内容占位符 */
			.no-content-placeholder {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: #909399;

				i {
					font-size: 48px;
					margin-bottom: 16px;
					opacity: 0.5;
				}

				p {
					font-size: 16px;
				}
			}
		}

		/* 提取内容项样式 */
		.extracted-content-item {
			display: flex;
			margin-bottom: 15px;
			padding: 8px 10px;
			border-radius: 8px;
			cursor: pointer;
			position: relative;
			background-color: #fff;
			box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
			transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
			animation: fadeInUp 0.5s forwards;
			transform-origin: center;
			overflow: hidden;

			/* 选中状态 */
			&.active {
				border-left: 3px solid #0AAF60;
				background-color: rgba(10, 175, 96, 0.05);
				transform: translateX(5px);
				box-shadow: 0 4px 12px rgba(10, 175, 96, 0.15);

				.content-text {
					color: #0AAF60;
					font-weight: 500;
				}
			}

			&:hover {
				box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
				transform: translateY(-2px);
				background-color: #FAFAFA;

				.play-overlay {
					opacity: 1;
				}

				.content-tag {
					transform: translateY(0);
					opacity: 1;
				}
			}

			&:active {
				transform: scale(0.98);
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
			}

			/* 左侧缩略图 */
			.thumbnail {
				width: 64px;
				height: 48px;
				position: relative;
				margin-right: 10px;
				flex-shrink: 0;
				border-radius: 6px;
				overflow: hidden;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				transition: all 0.3s;

				&:hover {
					transform: scale(1.05);
				}

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					transition: all 0.5s;
				}

				.time-label {
					position: absolute;
					left: 0;
					bottom: 0;
					background-color: rgba(0, 0, 0, 0.7);
					color: white;
					font-size: 9px;
					padding: 2px 5px;
					font-family: 'PingFang SC', sans-serif;
					border-top-right-radius: 4px;
					transition: all 0.3s;
				}

				.play-overlay {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.3);
					display: flex;
					align-items: center;
					justify-content: center;
					opacity: 0;
					transition: opacity 0.3s;

					i {
						color: white;
						font-size: 24px;
						filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
						transform: scale(0.8);
						transition: transform 0.3s;
					}
				}

				&:hover {
					.play-overlay i {
						transform: scale(1);
					}
				}
			}

			/* 中间内容区 */
			.content-area {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				transition: all 0.3s;
				max-width: calc(100% - 150px) !important;
				/* 限制最大宽度，给右侧标签留出空间 */

				.content-time {
					display: flex;
					align-items: center;
					margin-bottom: 4px;
					font-size: 12px;
					color: #909399;
					transition: color 0.3s;

					.time-text {
						transition: color 0.3s;
					}

					.time-separator {
						margin: 0 5px;
						color: #DCDFE6;
						transition: color 0.3s, transform 0.3s;
					}
				}

				.content-text {
					display: block !important;
					/* 改为block */
					border-radius: 4px;
					padding: 4px;
					line-height: 1.2;
					white-space: nowrap !important;
					overflow: hidden !important;
					text-overflow: ellipsis !important;
					width: 100% !important;
					/* 确保宽度 */
					max-width: 100% !important;
				}
			}

			/* 右侧区域 */
			.content-right {
				display: flex;
				align-items: center;
				margin-left: 10px;
				min-width: 70px !important;
				/* 添加最小宽度 */
				width: 70px !important;
				/* 固定宽度 */
				flex-shrink: 0 !important;
				/* 防止收缩 */

				.content-tag {
					width: 54px !important;
					height: 52px !important;
					background: #fff !important;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 4px;
					flex-shrink: 0 !important;
					/* 防止收缩 */

					span {
						text-align: center;
						line-height: 1.2;
						max-width: 50px !important;
						/* 限制最大宽度 */
						word-break: break-word !important;
						/* 允许在单词内换行 */
					}
				}
			}
		}
	}
}

// 标题输入框样式优化
.title-input {
	width: 100% !important;
	max-width: 320px !important; // 减小标题输入框宽度，为素材项留出更多空间
	margin-top: -5px;

	:deep(.el-input__wrapper) {
		height: 52px !important;
		padding: 0 15px;
		box-shadow: none !important; // 移除边框阴影
		background-color: transparent;
		border-radius: 4px;
		transition: all 0.3s;

		&:hover {
			box-shadow: none !important; // 移除鼠标悬停时的边框阴影
		}

		&:focus-within {
			box-shadow: none !important; // 移除聚焦时的边框阴影
		}
	}

	:deep(.el-input__inner) {
		height: 52px !important;
		font-size: 18px;
		font-weight: 700;
		color: #303133;

		&::placeholder {
			color: #909399;
			font-weight: 500;
		}
	}

	:deep(.el-input__prefix) {
		display: flex;
		align-items: center;
		margin-right: -22px;
	}

	.title-icon {
		width: 28px;
		height: 28px;
		object-fit: contain;
	}
}

// 音量滑块样式
.volume-slider-container {
	width: 25px; // 减小宽度
	background: #333;
	border-radius: 6px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
	padding: 8px 4px; // 恢复原来的内边距
	position: absolute; // 使用绝对定位
	z-index: 1000; // 确保在其他元素之上
}

.volume-slider {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100px; // 减少高度

	.volume-value {
		color: #fff;
		font-size: 12px;
		margin-bottom: 4px;
	}

	:deep(.el-slider) {
		margin: 0;
		height: 100%;
		display: flex;
		justify-content: center;

		// 禁用Element Plus的默认hover效果
		&:hover,
		&:active,
		&:focus {
			.el-slider__button-wrapper {
				transform: translateX(-50%) !important;

				.el-slider__button {
					transform: none !important;
					box-shadow: none !important;
				}
			}
		}

		.el-slider__runway {
			width: 2px !important; // 更细的轨道
			background-color: rgba(255, 255, 255, 0.2) !important;
			margin: 0 auto !important;

			// 禁用轨道hover效果
			&:hover {
				background-color: rgba(255, 255, 255, 0.2) !important;
			}
		}

		.el-slider__bar {
			width: 2px !important; // 更细的轨道
			background-color: #0AAF60 !important;
			left: 50% !important;
			transform: translateX(-50%) !important;
		}

		.el-slider__button-wrapper,
		.el-slider__button-wrapper:hover,
		.el-slider__button-wrapper:active,
		.el-slider__button-wrapper:focus {
			width: 12px !important;
			height: 12px !important;
			z-index: 2 !important;
			left: 50% !important;
			transform: translateX(-50%) !important;
			transition: none !important;

			.el-slider__button,
			.el-slider__button:hover,
			.el-slider__button:active,
			.el-slider__button:focus {
				width: 8px !important;
				height: 8px !important;
				border: 1px solid #fff !important;
				background-color: #0AAF60 !important;
				transform: none !important;
				transition: none !important;
				box-shadow: none !important;
			}
		}
	}
}

// 素材项特定样式
.material-item {
	&.sound-item {
		.material-icon {
			&::before {
				background-image: url('@/assets/img/yinsu1.png');
			}
		}
	}

	&.music-item {
		.material-icon {
			&::before {
				background-image: url('@/assets/img/yinsu2.png');
			}
		}
	}

	&.video-item {
		.material-icon {
			&::before {
				background-image: url('@/assets/img/yinsu3.png');
			}
		}
	}
}

/* 提取内容列表样式 */
.extracted-content-list {
	width: 100%;
	height: 432px;
	overflow-y: auto;
	padding: 12px 10px;
	color: #303133;
	position: relative;

	/* 滚动条样式 */
	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: transparent;
	}

	&::-webkit-scrollbar-thumb {
		background: #E0E0E0;
		border-radius: 3px;
	}
}

/* 提取内容项样式 */
.extracted-content-item {
	display: flex;
	margin-bottom: 15px;
	padding: 8px 10px;
	border-radius: 8px;
	cursor: pointer;
	position: relative;
	background-color: #fff;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
	animation: fadeInUp 0.5s forwards;
	transform-origin: center;
	overflow: hidden;

	/* 选中状态 */
	&.active {
		border-left: 3px solid #0AAF60;
		background-color: rgba(10, 175, 96, 0.05);
		transform: translateX(5px);
		box-shadow: 0 4px 12px rgba(10, 175, 96, 0.15);

		.content-text {
			color: #0AAF60;
			font-weight: 500;
		}
	}

	&:hover {
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		transform: translateY(-2px);
		background-color: #FAFAFA;

		.play-overlay {
			opacity: 1;
		}

		.content-tag {
			transform: translateY(0);
			opacity: 1;
		}
	}

	&:active {
		transform: scale(0.98);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	/* 左侧缩略图 */
	.thumbnail {
		width: 64px;
		height: 42px;
		position: relative;
		margin-right: 10px;
		flex-shrink: 0;
		border-radius: 6px;
		overflow: hidden;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		transition: all 0.3s;

		&:hover {
			transform: scale(1.05);
		}

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			transition: all 0.5s;
		}

		.time-label {
			position: absolute;
			left: 0;
			bottom: 0;
			background-color: rgba(0, 0, 0, 0.7);
			color: white;
			font-size: 9px;
			padding: 2px 5px;
			font-family: 'PingFang SC', sans-serif;
			border-top-right-radius: 4px;
			transition: all 0.3s;
		}

		.play-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.3);
			display: flex;
			align-items: center;
			justify-content: center;
			opacity: 0;
			transition: opacity 0.3s;

			i {
				color: white;
				font-size: 24px;
				filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
				transform: scale(0.8);
				transition: transform 0.3s;
			}
		}

		&:hover {
			.play-overlay i {
				transform: scale(1);
			}
		}
	}

	/* 中间内容区 */
	.content-area {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		transition: all 0.3s;
		max-width: calc(100% - 150px) !important;
		/* 限制最大宽度，给右侧标签留出空间 */

		.content-time {
			display: flex;
			align-items: center;
			margin-bottom: 4px;
			font-size: 12px;
			color: #909399;

			.time-text {
				transition: color 0.3s;
			}

			.time-separator {
				margin: 0 5px;
				color: #DCDFE6;

				.el-icon svg {
					margin-top: -1px;
				}
			}
		}

		.content-text {
			display: block !important;
			/* 改为block */
			border-radius: 4px;
			padding: 4px;
			line-height: 1.2;
			white-space: nowrap !important;
			overflow: hidden !important;
			text-overflow: ellipsis !important;
			width: 100% !important;
			/* 确保宽度 */
			max-width: 100% !important;
		}
	}

	/* 右侧区域 */
	.content-right {
		display: flex;
		align-items: center;
		margin-left: 10px;

		.content-tag {
			width: 54px !important;
			height: 52px !important;
			background: #fff !important;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 4px;

			span {
				text-align: center;
				line-height: 1.2;
			}
		}
	}
}

/* 淡入上移动画关键帧 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 列表动画 */
.list-animation-enter-active,
.list-animation-leave-active {
	transition: all 0.5s ease;
}

.list-animation-enter-from {
	opacity: 0;
	transform: translateY(30px);
}

.list-animation-leave-to {
	opacity: 0;
	transform: translateY(-30px);
}

// 添加分隔符图标样式
.time-separator {
	margin: 0 4px;
	color: #909399;
	font-size: 12px;
	display: inline-flex;
	align-items: center;
	vertical-align: middle;
}

// 修改时间显示区域的样式
.content-time {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #606266;
	margin-bottom: 6px;

	.time-text {
		display: inline-block;
	}

	.el-icon svg {
		margin-top: -1px;
	}

	.time-separator {
		margin: 0 4px;
		color: #909399;
		font-size: 12px;
	}
}

/* 使用全局样式规则允许文本折行 */
.extracted-content-list .extracted-content-item .content-area .content-text {
	white-space: normal !important;
	/* 改为normal允许折行 */
	overflow: visible !important;
	/* 改为visible让内容可见 */
	text-overflow: clip !important;
	/* 不再需要省略号 */
	max-width: 100% !important;
	display: block !important;
	width: 100% !important;
	word-wrap: break-word !important;
	/* 长单词可以折行 */
	word-break: break-all !important;
	/* 允许在任何字符间断行 */
}

/* 添加一个全局样式确保路由切换时样式不丢失 */
.right-content .preview-section .preview-content .extracted-content-list .content-text {
	white-space: normal !important;
	/* 改为normal允许折行 */
	overflow: visible !important;
	/* 改为visible让内容可见 */
	text-overflow: clip !important;
	/* 不再需要省略号 */
	display: block !important;
	width: 100% !important;
	word-wrap: break-word !important;
	/* 长单词可以折行 */
	word-break: break-all !important;
	/* 允许在任何字符间断行 */
}

/* 添加最高优先级的全局样式 */
div[class*="content-text"] {
	white-space: normal !important;
	/* 改为normal允许折行 */
	overflow: visible !important;
	/* 改为visible让内容可见 */
	text-overflow: clip !important;
	/* 不再需要省略号 */
	display: block !important;
	word-wrap: break-word !important;
	/* 长单词可以折行 */
	word-break: break-all !important;
	/* 允许在任何字符间断行 */
}

/* 修复提取内容项布局 */
.extracted-content-item {
	display: flex !important;
	align-items: center !important;
	width: 100% !important;
}

.preview-textarea {
	width: 100%;
	height: 700px !important;
	min-height: unset !important;
	max-height: none !important;
	border: none;
	background: transparent;
	outline: none;
	font-size: 14px;
	line-height: 1.8;
	color: #303133;
	padding: 15px;
	overflow-y: auto;
	white-space: pre-wrap;
	word-break: break-word;
	resize: none;
	transition: all 0.2s;

	/* 当为input元素时，确保样式一致 */
	display: block;
	box-sizing: border-box;

	/* 滚动条样式优化 */
	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: transparent;
	}

	&::-webkit-scrollbar-thumb {
		background: #E0E0E0;
		border-radius: 3px;
	}
}

// 添加新的CSS样式
.sound-control-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

// 新的内联音量滑块样式
.volume-slider-popup {
	position: absolute;
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
	width: 25px;
	background: #333;
	border-radius: 6px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
	padding: 8px 4px;
	z-index: 9999999;
	margin-top: 5px;
}

// 保持音量滑块的内部样式不变
.volume-slider {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100px;

	.volume-value {
		color: #fff;
		font-size: 12px;
		margin-bottom: 4px;
	}
}
</style>