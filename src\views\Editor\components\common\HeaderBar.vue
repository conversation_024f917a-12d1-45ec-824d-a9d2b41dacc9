<template>
	<div class="title-bar">
		<div class="title">{{ title }}</div>
		<div class="right-section">
			<div class="operation-buttons">
				<template v-for="(button, index) in visibleButtons" :key="index">
					<!-- 使用自定义提示框替代el-tooltip -->
					<div class="tooltip-container" v-if="button.action === 'edit'">
						<button 
							class="btn" 
							:class="{ 'disabled': !canEdit && button.action === 'edit' }"
							@click="handleClick(button.action)"
							@mouseenter="showCustomTooltip = true"
							@mouseleave="showCustomTooltip = false"
							:disabled="!canEdit && button.action === 'edit'"
						>
							<i :class="['btn-icon', button.icon]" v-if="button.icon"></i>
							{{ button.text }}
						</button>
						<!-- 自定义提示框 -->
						<div class="custom-tooltip" v-if="showCustomTooltip">
							{{ canEdit ? '可将视频导入到云剪辑中进行精准剪辑' : '请先生成视频' }}
						</div>
					</div>
					
					<button 
						v-else 
						class="btn" 
						@click="handleClick(button.action)"
					>
						<i :class="['btn-icon', button.icon]" v-if="button.icon"></i>
						{{ button.text }}
					</button>
				</template>
			</div>
			<div class="user-info">
				<!-- 已登录状态显示头像 -->
				<el-tooltip
					v-if="checkUserLogin()"
					popper-class="information-tooltip"
					effect="light"
					placement="bottom"
					:popper-options="popperOptions"
				>
					<template #content>
						<information />
					</template>
					<el-avatar class="cursor-pointer" :size="34">
						<div class="avatar_img">
							<img class="avatar_img_avatar" :src="loginStore.userInfo && loginStore.userInfo.avatar ? loginStore.userInfo.avatar : user.avatar" alt="用户头像" @error="handleAvatarError">
							<div class="avatar_img_sign">
								<img :src="getSignImg()" alt="">
							</div>
						</div>
					</el-avatar>
				</el-tooltip>
				
				<!-- 未登录状态显示登录按钮 -->
				<div v-else class="login-text" @click="handleLogin">
					登录
				</div>
			</div>
		</div>
	</div>
	<!-- 退出登录弹窗 -->
	<loginOut ref="login_out_ref"></loginOut>
	<!-- 创作者社群弹窗 -->
	<creatorCommunity ref="creator_community_ref"></creatorCommunity>
	<!-- 账号信息弹窗 -->
	<accountInfoDialog ref="account_info_dialog_ref"></accountInfoDialog>
</template>

<script setup>
import { defineProps, defineEmits, inject, computed, ref, getCurrentInstance, reactive, provide, onMounted } from 'vue'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { addVideoMaterial } from '@/api/material'
import { usePreviewStore } from '@/stores/previewStore'
import { useMusicStore } from '@/stores/modules/musicStore'
import { useRouter, useRoute } from 'vue-router'
import { useloginStore } from '@/stores/login'
// 导入information组件代替单独导入vipTop和accountRow
import information from '@/views/account/information.vue'
// 保留必要的引入
import avatar from '@/assets/images/account/avatar.png'
import avatarSign from '@/assets/images/account/avatar_sign.png'
import avatarSvipSign from '@/assets/images/account/avatar_svip_sign.svg'
// 导入退出登录弹窗组件
import loginOut from '@/views/account/login_out.vue'
// 导入创作者社群弹窗组件
import creatorCommunity from '@/views/account/creator_community_dialog.vue'
// 导入账号信息弹窗组件
import accountInfoDialog from '@/views/account/account_info_dialog.vue'

// 引入路由器
const router = useRouter()
// 引入当前路由
const route = useRoute()

// 使用预览存储
const previewStore = usePreviewStore()

// 使用音乐存储
const musicStore = useMusicStore()

// 导入用户store
const loginStore = useloginStore()

// 获取组件实例
const { proxy } = getCurrentInstance();

// 头像相关数据
const user = reactive({
	avatar: avatar,
	avatarSign: avatarSign,
	avatarSvipSign: avatarSvipSign
})

// 弹窗位置配置
const popperOptions = ref({
	modifiers: [
		{
			name: 'offset',
			options: {
				offset: [-130, 27], // [x, y] 偏移量，向右调整以显示完整内容
			},
		},
	],
});

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}

// 加载状态标志
const isLoading = ref(false)

// 退出登录弹窗引用
const login_out_ref = ref(null)
// 创作者社群弹窗引用
const creator_community_ref = ref(null)
// 账号信息弹窗引用
const account_info_dialog_ref = ref(null)

// 添加一个变量跟踪是否编辑过内容
const hasBeenEdited = ref(false)

// 定义props
const props = defineProps({
	title: {
		type: String,
		required: true
	},
	buttons: {
		type: Array,
		default: () => [
			// { text: '新建', action: 'new' }, // 注释掉新建按钮
			{ text: '返回首页', action: 'home' }, // 添加返回首页按钮
			{ text: '保存', action: 'save' },
			{ text: '去精剪', action: 'edit', icon: 'el-icon-scissors' }, // 添加图标
			// { text: '导出', action: 'export' } // 注释掉导出按钮
		]
	},
	// 获取父组件中的数据
	previewTitle: {
		type: String,
		default: ''
	},
	previewContent: {
		type: String,
		default: ''
	},
	videoList: {
		type: Array,
		default: () => []
	},
	musicList: {
		type: Array,
		default: () => []
	},
	roleList: {
		type: Array,
		default: () => []
	},
	// 添加视频是否已生成标志
	videoGenerated: {
		type: Boolean,
		default: false
	}
})

// 计算属性，显示返回首页、保存和去剪辑按钮
const visibleButtons = computed(() => {
	return props.buttons.filter(button => 
		button.action === 'home' || button.action === 'save' || button.action === 'edit'
	);
})

// 判断是否可以进行精剪的计算属性
const canEdit = computed(() => {
	return !!previewStore.getProjectId() || props.videoGenerated
})

// 定义emit
const emit = defineEmits(['action', 'save-success', 'save-error'])

// 判断用户是否已登录
const checkUserLogin = () => {
	// 检查 Pinia store 中是否有 token
	if (loginStore.token) {
		return true;
	}
	
	// 如果 store 中没有，再从本地存储获取 user 信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;
	
	try {
		const userData = JSON.parse(userStorage);
		// 检查 token 是否存在且不为空
	// 检查 token 是否存在且不为空
	const isLoggedIn = userData && userData.token && userData.token.trim() !== '';
		
		// 如果本地存储有 token 但 store 没有，同步到 store
		if (isLoggedIn && !loginStore.token) {
			loginStore.setToken(userData.token);
			if (userData.userId) loginStore.setUserId(userData.userId);
			if (userData) loginStore.setLoginData(userData);
		}
		
		return isLoggedIn;
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

// 处理登录按钮点击事件
const handleLogin = () => {
	// 打开登录弹窗
	proxy.$modal.open('组合式标题');
};

/**
 * 处理按钮点击事件
 * @param {string} action - 按钮动作
 */
const handleClick = async (action) => {
	// 如果已经在加载状态，阻止多次点击
	if (isLoading.value) {
		return;
	}
	
	// 如果是去精剪操作但是不能编辑，显示提示并返回
	if (action === 'edit' && !canEdit.value) {
		ElMessage.warning('请先生成视频，然后再使用精剪功能');
		return;
	}
	
	// 处理返回首页动作
	if (action === 'home') {
		// 检查用户是否已登录
		if (checkUnsavedChanges() && !checkUserLogin()) {
			// 未登录但有未保存内容，弹出登录弹窗
			proxy.$modal.open('组合式标题');
			return;
		}
		
		// 尝试保存内容然后返回首页
		if (checkUnsavedChanges()) {
			isLoading.value = true;
			
			try {
				const result = await handleSave();
				console.log('返回首页前保存成功:', result);
				ElMessage.success('保存成功，正在返回首页...');
				emit('save-success', result);
			} catch (error) {
				console.error('返回首页前保存失败:', error);
				const errorMsg = getErrorMessage(error);
				ElMessage.error(`保存失败: ${errorMsg}`);
				emit('save-error', error);
				// 保存失败也继续返回首页
			} finally {
				isLoading.value = false;
			}
		}
		
		// 重置保存状态
		previewStore.isSaved = false;
		localStorage.removeItem('content_saved_state');
		
		// 触发action事件并返回首页
		emit('action', action);
		router.push({ name: 'home' });
		return;
	}
	
	// 如果是保存操作，调用保存API
	if (action === 'save') {
		// 检查用户是否已登录
		if (!checkUserLogin()) {
			// 未登录，弹出登录弹窗
			proxy.$modal.open('组合式标题');
			return;
		}
		
		isLoading.value = true
		emit('action', action)
		
		try {
			const result = await handleSave()
			// 移除成功提示，保留日志
			ElMessage.success('保存成功')
			emit('save-success', result)
		} catch (error) {
			console.error('保存失败:', error)
			const errorMsg = getErrorMessage(error)
			ElMessage.error(`保存失败: ${errorMsg}`)
			emit('save-error', error)
		} finally {
			isLoading.value = false
		}
		return
	}
	
	// 如果是去精剪操作，先调用保存API再继续
	if (action === 'edit') {
		// 检查用户是否已登录
		if (!checkUserLogin()) {
			// 未登录，弹出登录弹窗
			proxy.$modal.open('组合式标题');
			return;
		}
		
		console.log('====== 开始去精剪操作 ======');
		console.log('canEdit值:', canEdit.value);
		
		isLoading.value = true
		
		// 使用Element Plus的加载服务
		const loadingInstance = ElLoading.service({
			text: '正在准备数据，请稍候...',
			background: 'rgba(0, 0, 0, 0.7)'
		})
		
		try {
			// 等待保存完成
			const result = await handleSave()
			console.log('保存成功，结果:', result);
			
			// 关闭loading
			loadingInstance.close()
			isLoading.value = false
			
			// 移除成功提示，保留日志
			ElMessage.success('数据已保存，正在跳转...')
			
			// 只有保存成功后才触发事件通知父组件
			emit('action', action)
			
			// 从Pinia中获取projectId，不再从路由获取
			const projectId = previewStore.getProjectId()
			console.log('去精剪 - 从Pinia获取到的projectId:', projectId);
			console.log('Pinia中完整项目数据:', previewStore.$state);
			
			// 安全地获取用户token
			let token = "";
			try {
				const userStorage = localStorage.getItem('user');
				if (userStorage) {
					const userData = JSON.parse(userStorage);
					token = userData?.token || '';
					console.log('获取到用户token:', token ? '成功(长度:' + token.length + ')' : '为空');
				} else {
					console.log('localStorage中未找到用户信息');
				}
			} catch (e) {
				console.error('获取用户token失败:', e);
			}
			
			// 跳转到云剪辑系统，只有当projectId有效时才添加到URL
			const redirectUrl = projectId && !isNaN(projectId)
				? `https://yunjian.peiyinbangshou.com/App?projectId=${projectId}&token=${token}`
				: `https://yunjian.peiyinbangshou.com/App?token=${token}`;
			
			console.log('即将在新窗口打开云剪辑:', `https://yunjian.peiyinbangshou.com/App?projectId=${projectId}&token=${token}`);
			
			// 在新标签页打开URL
			setTimeout(() => {
				console.log('====== 尝试在新窗口打开云剪辑 ======');
				const newWindow = window.open(redirectUrl, '_blank');
				
				// 检查弹出窗口是否被阻止
				if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
					console.warn('弹出窗口可能被浏览器阻止');
					ElMessage.warning('云剪辑窗口可能被浏览器阻止，请检查浏览器设置并允许弹出窗口');
					
					// 如果弹出窗口被阻止，显示提示并提供手动点击链接的选项
					ElMessageBox.confirm(
						'<div class="custom-message-content">' +
							'<div class="message-text">' +
								'<h3>无法自动打开云剪辑</h3>' +
								'<p>您的浏览器阻止了弹出窗口。请点击下方按钮手动打开云剪辑，或修改浏览器设置允许本站点的弹出窗口。</p>' +
							'</div>' +
						'</div>',
						{
							title: '打开云剪辑',
							confirmButtonText: '手动打开云剪辑',
							cancelButtonText: '取消',
							dangerouslyUseHTMLString: true,
							customClass: 'custom-message-box',
							closeOnClickModal: true
						}
					).then(() => {
						// 用户点击"手动打开云剪辑"按钮
						window.open(redirectUrl, '_blank');
					}).catch(() => {
						// 用户取消操作
						console.log('用户取消了手动打开操作');
					});
				} else {
					console.log('云剪辑已在新窗口打开');
				}
			}, 100);
		} catch (error) {
			console.error('去精剪前保存失败:', error)
			
			// 关闭loading
			loadingInstance.close()
			isLoading.value = false
			
			// 获取友好的错误信息
			const errorMsg = getErrorMessage(error)
			
			// 显示更具体的错误信息
			ElMessage.error(`保存失败: ${errorMsg}`)
			
			// 通知父组件保存失败
			emit('save-error', error)
		}
		return
	}
	
	// 处理其他类型的按钮操作
	emit('action', action)
}

// 修改 checkUnsavedChanges 函数
const checkUnsavedChanges = () => {
	// 如果已明确标记为编辑过，直接返回true
	if (hasBeenEdited.value) {
		console.log('检测到内容已被编辑，需要提示保存');
		return true;
	}
	
	// 如果刚保存过，直接返回false
	if (previewStore.isSaved === true) {
		console.log('内容已保存，不需要提示保存');
		return false;
	}
	
	// 检查localStorage中的保存状态
	const savedState = localStorage.getItem('content_saved_state');
	if (savedState === 'true') {
		console.log('localStorage中标记为已保存状态');
		// 清除保存状态，避免影响下次检查
		localStorage.removeItem('content_saved_state');
		return false;
	}
	
	// 获取当前页面所有可能的内容
	const currentTitle = previewStore.title || '';
	const currentContent = previewStore.content || '';
	
	// 1. 检查文字内容是否为空
	const hasTextContent = !!(currentContent.trim() || currentTitle.trim());
	
	// 2. 检查配音角色是否有值
	const hasRole = !!(
		(props.roleList?.length > 0 && props.roleList[0]?.audioUrl) || 
		(previewStore.selectedRole?.name && previewStore.selectedRole?.audioUrl)
	);
	
	// 3. 检查视频素材是否有值
	const hasVideoMaterial = !!(
		(props.videoList?.length > 0 && props.videoList[0]?.url) || 
		(previewStore.videoList?.length > 0 && previewStore.videoList[0]?.url)
	);
	
	// 4. 检查音乐素材是否有值
	const hasMusicMaterial = !!(
		(props.musicList?.length > 0 && props.musicList[0]?.url) || 
		(musicStore.musicList?.length > 0 && musicStore.musicList[0]?.url)
	);
	
	// 5. 检查内容是否有变化
	const lastSavedTitle = previewStore.lastSavedTitle || ''; 
	const lastSavedContent = previewStore.lastSavedContent || '';
	const hasContentChanged = (currentTitle !== lastSavedTitle) || (currentContent !== lastSavedContent);
	
	// 如果有内容但是是刚清空过的，也不需要提示
	const isFreshlyCleared = localStorage.getItem('freshly_cleared_flag') === 'true';
	if (isFreshlyCleared) {
		localStorage.removeItem('freshly_cleared_flag');
		return false;
	}
	
	// 记录详细日志，帮助调试
	console.log('检查未保存内容:', {
		hasTextContent,
		hasRole,
		hasVideoMaterial,
		hasMusicMaterial,
		hasContentChanged,
		isSaved: previewStore.isSaved,
		hasBeenEdited: hasBeenEdited.value
	});
	
	// 只要有内容且内容有变化，就需要提示保存
	if (hasContentChanged && (hasTextContent || hasRole || hasVideoMaterial || hasMusicMaterial)) {
		console.log('内容有变化，需要提示保存');
		// 设置已编辑标记
		hasBeenEdited.value = true;
		return true;
	}
	
	// 如果没有任何内容，直接返回false
	if (!hasTextContent && !hasRole && !hasVideoMaterial && !hasMusicMaterial) {
		console.log('没有任何内容，不需要提示保存');
		return false;
	}
	
	// 如果从未保存过，但有内容，也提示保存
	if (!previewStore.lastUpdated && (hasTextContent || hasRole || hasVideoMaterial || hasMusicMaterial)) {
		console.log('有内容但从未保存过，需要提示保存');
		// 设置已编辑标记
		hasBeenEdited.value = true;
		return true;
	}
	
	// 默认不提示
	return false;
}

/**
 * 从错误对象中提取错误信息
 * @param {Error|string|object} error - 错误对象
 * @returns {string} - 友好的错误消息
 */
const getErrorMessage = (error) => {
	if (typeof error === 'string') {
		return error
	}
	
	if (error.message) {
		return error.message
	}
	
	if (error.response && error.response.data && error.response.data.message) {
		return error.response.data.message
	}
	
	// 默认错误信息
	return '发生未知错误，请重试'
}

/**
 * 设置内容已被编辑状态
 * @param {boolean} [value=true] - 是否已编辑，默认为true
 */
const setContentEdited = (value = true) => {
	console.log('设置内容已编辑状态:', value);
	hasBeenEdited.value = value;
	// 总是将保存状态设置为false，确保能触发保存提示
	if (value) {
		previewStore.isSaved = false;
		// 确保清除本地存储中的保存标记
		localStorage.removeItem('content_saved_state');
		console.log('已重置保存状态 previewStore.isSaved =', previewStore.isSaved);
	}
}

/**
 * 处理保存操作
 * @returns {Promise<object>} - 返回保存结果
 * @throws {Error} - 当保存失败时抛出错误
 */
const handleSave = async () => {
	// 优先从props获取数据，如果不存在则从预览存储中获取
	const title = props.previewTitle || previewStore.title || ''
	
	// 修改内容获取逻辑
	const content = props.previewContent || previewStore.content || '';
	
	// 获取视频素材
	let ossPath = ''

	// 首先从previewStore.currentVideoUrl获取，这是getVideos接口返回的ossPath
	if (previewStore.currentVideoUrl) {
		console.log('从Pinia的currentVideoUrl获取视频URL:', previewStore.currentVideoUrl);
		ossPath = previewStore.currentVideoUrl;
	}
	// 其次从pinia的videoList获取
	else if (previewStore.videoList && previewStore.videoList.length > 0 && previewStore.videoList[0].url) {
		console.log('从pinia的videoList获取视频URL:', previewStore.videoList[0].url);
		ossPath = previewStore.videoList[0].url;
	}
	// 最后从props获取
	else if (props.videoList && props.videoList.length > 0 && props.videoList[0].url) {
		console.log('从props获取视频URL:', props.videoList[0].url);
		ossPath = props.videoList[0].url;
	}
	
	// 获取音乐素材
	let musicMaterialUrl = ''
	
	// 优先从musicStore中获取音乐数据
	const musicStoreData = musicStore.musicList || []
	
	// 添加详细日志记录，查看音乐列表结构
	console.log('音乐列表数据:', {
		musicStoreExists: !!musicStoreData,
		musicStoreLength: musicStoreData.length,
		musicStoreData: musicStoreData,
		musicListExists: !!props.musicList,
		musicListLength: props.musicList ? props.musicList.length : 0,
		musicList: props.musicList
	})
	
	// 首先尝试从musicStore获取数据
	if (musicStoreData.length > 0) {
		const musicItem = musicStoreData[0]
		console.log('音乐存储中第一项:', musicItem)
		
		// 检查musicItem的各种属性
		if (musicItem.url) {
			musicMaterialUrl = musicItem.url
		} else if (musicItem.src) {
			musicMaterialUrl = musicItem.src
		} else if (musicItem.path) {
			musicMaterialUrl = musicItem.path
		} else if (musicItem.musicUrl) {
			musicMaterialUrl = musicItem.musicUrl
		} else if (typeof musicItem === 'string') {
			// 如果音乐项直接是字符串URL
			musicMaterialUrl = musicItem
		}
		
		console.log('从音乐存储获取的URL:', musicMaterialUrl)
	}
	
	// 如果musicStore没有数据，再尝试从props获取
	if (!musicMaterialUrl && props.musicList && props.musicList.length > 0) {
		const musicItem = props.musicList[0]
		
		// 检查多种可能的URL属性名
		if (musicItem.url) {
			musicMaterialUrl = musicItem.url
		} else if (musicItem.musicUrl) {
			musicMaterialUrl = musicItem.musicUrl
		} else if (musicItem.audioUrl) {
			musicMaterialUrl = musicItem.audioUrl
		} else if (musicItem.src) {
			musicMaterialUrl = musicItem.src
		} else if (musicItem.path) {
			musicMaterialUrl = musicItem.path
		} else if (typeof musicItem === 'string') {
			// 如果音乐项直接是字符串URL
			musicMaterialUrl = musicItem
		}
		// 如果有嵌套属性，也进行检查
		else if (musicItem.music && musicItem.music.url) {
			musicMaterialUrl = musicItem.music.url
		}
		
		console.log('从props获取的音乐URL:', musicMaterialUrl)
	}
	
	// 最终日志输出
	console.log('最终获取的音乐URL:', musicMaterialUrl)
	
	// 获取配音角色URL
	let audioUrl = ''
	
	// 添加日志记录，查看角色列表数据
	console.log('角色列表数据:', {
		roleListExists: !!props.roleList,
		roleListLength: props.roleList ? props.roleList.length : 0,
		roleList: props.roleList,
		previewStoreRole: previewStore.selectedRole
	})
	
	// 首先尝试从props获取配音角色URL
	if (props.roleList && props.roleList.length > 0) {
		const roleItem = props.roleList[0]
		
		// 检查各种可能的URL属性
		if (roleItem.audioUrl) {
			audioUrl = roleItem.audioUrl
		} else if (roleItem.url) {
			audioUrl = roleItem.url
		} else if (roleItem.src) {
			audioUrl = roleItem.src
		} else if (roleItem.voiceUrl) {
			audioUrl = roleItem.voiceUrl
		}
		
		console.log('从props获取的配音URL:', audioUrl)
	}
	
	// 如果props中没有找到，尝试从预览存储中获取
	if (!audioUrl && previewStore.selectedRole) {
		const storeRole = previewStore.selectedRole
		
		// 检查各种可能的URL属性
		if (storeRole.audioUrl) {
			audioUrl = storeRole.audioUrl
		} else if (storeRole.url) {
			audioUrl = storeRole.url
		}
		
		console.log('从预览存储获取的配音URL:', audioUrl)
	}
	
	// 最终日志输出
	console.log('最终获取的配音URL:', audioUrl)
	
	// 构建API参数
	const params = {
		userId: getUserId(),  // 使用getUserId函数获取userId
		copywrite: content, // 直接使用 content 作为 copywrite
		status: "1",
		isDeleted: "0"
	}

	// 添加其他可选参数
	if (title) {
		params.title = title;
		params.name = title;
	}
	if (ossPath) params.ossPath = ossPath;
	if (musicMaterialUrl) params.musicMaterialUrl = musicMaterialUrl;
	if (audioUrl) params.audioUrl = audioUrl;
	

	// 优先使用URL中的albumId作为id参数，如果不存在则使用projectId
	const albumId = route.query.albumId;

	
	if (albumId) {
		console.log('使用URL中的albumId作为id参数:', albumId);
		params.id = albumId;
	}  else {
		console.log('未找到albumId和projectId，这将是新建保存');
	}
	
	// 打印每个参数的情况，便于调试
	console.log('保存参数详情:', {
		id: params.id,
		title: params.title,
		ossPath: params.ossPath,
		musicMaterialUrl: params.musicMaterialUrl,
		audioUrl: params.audioUrl,
		copywrite: params.copywrite,
		content: content, // 打印原始content变量
		contentLength: content ? content.length : 0,
		allParams: params
	})
	
	
	try {
		const result = await addVideoMaterial(params)
		console.log('保存成功:', result)

		// 保存返回的id到previewStore
		if (result.id) {
			console.log('从保存接口获取到项目ID:', result.id);
			previewStore.setProjectId(result.id);
		}

		// 更新预览区文字
		if (result.copywrite) {
			previewStore.setContent(result.copywrite)
		}

		// 更新音乐素材URL
		if (result.musicMaterialUrl) {
			musicStore.updateMusicUrl(result.musicMaterialUrl)
		}

		// 更新配音角色URL
		if (result.audioUrl) {
			previewStore.updateSelectedRoleUrl(result.audioUrl)
		}

		// 在保存成功后更新lastSavedTitle和lastSavedContent
		previewStore.lastSavedTitle = title;
		previewStore.lastSavedContent = content;
		
		// 更新最后保存时间
		previewStore.lastUpdated = new Date().getTime();
		
		// 添加保存成功标志
		previewStore.isSaved = true;
		
		// 重置编辑状态
		hasBeenEdited.value = false;
		
		// 记录本次保存状态到localStorage
		localStorage.setItem('content_saved_state', 'true');

		return result
	} catch (error) {
		console.error('保存API请求失败:', error)
		throw error
	}
}

// 提供退出登录方法
provide('loginout', () => {
	// 显示退出登录弹窗
	login_out_ref.value.dialogVisible = true
})

// 提供内容编辑状态设置方法
provide('setContentEdited', setContentEdited)

// 提供账号信息点击方法
provide('accountInfoClick', () => {
	// 显示账号信息弹窗而不是跳转
	account_info_dialog_ref.value.dialogVisable = true
})

// 提供创作者社群方法
provide('creator_community', () => {
	// 显示创作者社群二维码弹窗
	creator_community_ref.value.dialogVisable = true
})

// 组件挂载时检查登录状态
onMounted(() => {
	// 检查登录状态
	const isLoggedIn = checkUserLogin();
	console.log('当前登录状态:', isLoggedIn ? '已登录' : '未登录');
	
	// 初始化编辑状态为false
	hasBeenEdited.value = false;
})

// 在script部分添加获取用户角标图片的函数
const getSignImg = () => {
	let result = '';
	if (loginStore.userInfo && loginStore.userInfo.benefitLevel) {
		switch (loginStore.userInfo.benefitLevel) {
			case 0:
				result = '';
				break;
			case 1:
				result = user.avatarSign;
				break;
			case 2:
				result = user.avatarSvipSign;
				break;
			default:
				break;
		}
	}
	
	return result;
}

// 添加处理头像加载错误的方法
const handleAvatarError = (e) => {
	// 当头像加载失败时，使用默认头像
	e.target.src = user.avatar;
}

// 在script部分添加控制tooltip显示的变量
// 自定义tooltip显示状态
const showCustomTooltip = ref(false);
</script>

<style lang="scss" scoped>
.title-bar {
	// height: 64px !important;
	padding: 0 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: #fff;
	border-bottom: 1px solid #e4e7ed;
	margin-bottom: 16px;

	.title {
		font-size: 20px;
		font-weight: 700;
		color: #303133;
	}

	.right-section {
		display: flex;
		align-items: center;
		gap: 20px;
	}

	.operation-buttons {
		display: flex;
		padding: 10px;
		gap: 12px;

		.btn {
			height: 32px;
			padding: 0 16px;
			border-radius: 4px;
			border: 1px solid #0AAF60;
			display: flex;
			align-items: center;
			gap: 8px;
			cursor: pointer;
			background: #fff;
			color: #0AAF60;
			font-size: 14px;
			transition: all 0.3s;

			&:hover {
				color: #0AAF60;
				border-color: #0AAF60;
				background-color: #f0f9eb;
			}

			// 精剪按钮使用绿色背景
			&:nth-child(3), &:last-child {
				background: #0AAF60;
				color: #ffffff;
				border: none;

				&:hover {
					opacity: 0.9;
					background: #0AAF60;
					color: #ffffff;
				}
			}
			
			// 禁用状态
			&:disabled, &.disabled {
				opacity: 0.6;
				cursor: not-allowed;
				background-color: #c0c4cc;
				border-color: #c0c4cc;
				color: #ffffff;
				
				&:hover {
					opacity: 0.6;
					background-color: #c0c4cc;
					border-color: #c0c4cc;
					color: #ffffff;
				}
			}
		}
	}

	.user-info {
		display: flex;
		align-items: center;
		gap: 8px;
		
		.avatar {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			object-fit: cover;
			border: 1px solid #DCDFE6;
			cursor: pointer;
		}
		
		.el-avatar {
			background-color: transparent;
			
			.avatar_img {
				width: 28px;
				height: 28px;
				border-radius: 50%;
				position: relative;
				
				.avatar_img_avatar {
					width: 100%;
					height: 100%;
					border-radius: 50%;
					object-fit: cover;
				}
				
				.avatar_img_sign {
					position: absolute;
					right: 0;
					bottom: -2px;
					z-index: 1;
					box-sizing: border-box;
					width: 10px;
					height: 10px;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					
					img {
						width: 10px;
						height: 9px;
					}
					
					&.ordinary {
						border: 2px solid #FFFFFF;
						background: #68CD5F;
					}
				}
			}
		}
		
		.login-text {
			font-size: 14px;
			font-weight: bold;
			color: #303133;
			cursor: pointer;
			transition: all 0.3s;
			
			&:hover {
				color: #0AAF60;
			}
		}
		
		.login-button {
			height: 32px;
			padding: 0 16px;
			border-radius: 4px;
			border: 1px solid #0AAF60;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			background: #0AAF60;
			color: #FFFFFF;
			font-size: 14px;
			transition: all 0.3s;
			
			&:hover {
				opacity: 0.9;
				box-shadow: 0 2px 8px rgba(10, 175, 96, 0.3);
			}
		}
	}
}

// 添加响应式样式
@media screen and (max-width: 768px) {
	.title-bar {
		flex-direction: column;
		height: auto !important;
		padding: 10px;
		
		.title {
			margin-bottom: 10px;
		}
		
		.right-section {
			width: 100%;
			justify-content: space-between;
		}
		
		.operation-buttons {
			flex-wrap: wrap;
			justify-content: center;
		}
	}
}
</style>

<style lang="scss">
// 自定义消息框样式
.custom-message-box {
	border-radius: 8px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	animation: message-fade-in 0.3s ease-in-out; /* 添加入场动画 */
	
	@keyframes message-fade-in {
		from {
			opacity: 0;
			transform: translateY(-20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.el-message-box__header {
		background-color: #f8f9fa;
		padding: 0 20px;
		height: 56px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #ebeef5;
		
		.el-message-box__title {
			font-size: 18px;
			font-weight: 600;
			color: #303133;
			margin: 0;
			line-height: 1.5;
			flex: 1;
		}
		
		.el-message-box__headerbtn {
			position: static;
			width: 24px;
			height: 24px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.el-message-box__close {
				font-size: 18px;
				color: #909399;
				transition: all 0.3s;
				
				&:hover {
					color: #0AAF60;
					transform: rotate(90deg);
				}
			}
		}
	}
	
	.el-message-box__content {
		padding: 24px 20px;
		
		.custom-message-content {
			display: flex;
			align-items: flex-start;
			
			.warning-icon {
				font-size: 28px;
				color: #e6a23c;
				margin-right: 16px;
			}
			
			.message-text {
				h3 {
					margin: 0 0 12px 0;
					font-size: 16px;
					font-weight: 600;
					color: #303133;
				}
				
				p {
					margin: 0;
					color: #606266;
					line-height: 1.6;
				}
			}
		}
	}
	
	.el-message-box__btns {
		padding: 16px 20px;
		display: flex;
		justify-content: flex-end;
		border-top: 1px solid #ebeef5;
		
		.el-button {
			height: 40px;
			padding: 0 20px;
			border-radius: 4px;
			font-size: 14px;
			transition: all 0.3s;
			font-weight: 500;
		}
		
		.confirm-danger-button {
			background-color: #f56c6c;
			border-color: #f56c6c;
			color: white;
			
			&:hover, &:focus {
				background-color: #f78989;
				border-color: #f78989;
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
			}
		}
		
		.save-button {
			margin-right: 12px;
			background-color: #0AAF60 !important;
			border-color: #0AAF60 !important;
			color: #ffffff !important;
			
			&:hover, &:focus {
				opacity: 0.9;
				background-color: #0AAF60 !important;
				color: #ffffff !important;
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(10, 175, 96, 0.3);
			}
		}
	}
}

// 自定义消息提示样式
.el-message {
	min-width: 280px;
	padding: 12px 16px;
	border-radius: 6px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	
	.el-message__icon {
		font-size: 18px;
		margin-right: 10px;
	}
	
	.el-message__content {
		font-size: 14px;
		line-height: 1.5;
	}
	
	&.el-message--success {
		background-color: #f0f9eb;
		border-color: #0AAF60;
		
		.el-message__icon {
			color: #0AAF60;
		}
	}
	
	&.el-message--error {
		background-color: #fef0f0;
		border-color: #f56c6c;
	}
	
	&.el-message--warning {
		background-color: #fdf6ec;
		border-color: #e6a23c;
	}
	
	&.el-message--info {
		background-color: #f4f4f5;
		border-color: #909399;
	}
}

// 自定义加载动画样式
.el-loading-mask {
	background-color: rgba(0, 0, 0, 0.7);
	
	.el-loading-spinner {
		.circular {
			width: 48px;
			height: 48px;
		}
		
		.el-loading-text {
			color: white;
			font-size: 16px;
			margin-top: 16px;
		}
	}
}

// 用户信息弹窗样式
.user-popover {
	padding: 0 !important;
	border-radius: 12px !important;
	overflow: hidden !important;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
	border: none !important;
	z-index: 9999 !important;
	
	.user-popover-content {
		display: flex;
		flex-direction: column;
		/* 确保内容不被裁剪 */
		overflow: visible;
		
		// 上下部分样式保持一致
		:deep(.vipTop) {
			border-radius: 0 !important;
			background: linear-gradient(to right, #e2c48d, #d1a15f) !important;
		}
		
		:deep(.account_row) {
			display: flex;
			flex-direction: column;
			padding: 24px 12px 0;
			width: 100%;
			box-sizing: border-box;
		
			.account_row_charact_space {
				border-bottom: 1px solid rgba(0,0,0,0.1);
				padding-bottom: 12px;
				display: flex;
				flex-direction: column;
				
				.account_row_charact_space_item {
					display: flex;
					margin-bottom: 16px;
					line-height: 21px;
					align-items: center;
					cursor: pointer;
					padding: 8px 12px;
					transition: all 0.2s;
					
					&:hover {
						background-color: #f5f7fa;
					}
					
					.account_row_charact_space_item_label {
						display: flex;
						align-items: center;
						
						.account_row_charact_space_item_icon {
							width: 15px;
							height: 13px;
							margin-right: 8px;
							display: flex;
							align-items: center;
							justify-content: center;
						}
						
						.account_row_charact_space_item_title {
							font-size: 14px;
							color: #353D49;
						}
					}
					
					.account_row_charact_space_item_value {
						margin-left: auto;
						
						span {
							font-size: 17px;
							font-weight: 500;
							color: #1D2129;
						}
						
						.account_row_charact_space_item_value_more {
							width: 5px !important;
							height: 8px !important;
						}
					}
					
					&.charact_item {
						cursor: default;
						padding: 12px;
						
						.account_row_charact_space_item_label {
							.account_row_charact_space_item_title {
								font-weight: 500;
							}
						}
						
						.account_row_charact_space_item_value {
							span {
								font-size: 17px;
								font-weight: 600;
								color: #1D2129;
							}
						}
					}
					
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
			
			.account_row_info {
				padding-top: 16px;
				padding-bottom: 12px;
			}
			
			.account_row_loginout {
				padding: 16px 0 20px 0;
				border-bottom: none;
				margin: 0 12px;
				
				.account_row_charact_space_item {
					padding: 8px 12px;
					border: 1px solid #F56C6C;
					border-radius: 4px;
					
					.account_row_charact_space_item_label {
						.account_row_charact_space_item_title {
							color: #F56C6C;
						}
					}
					
					&:hover {
						background-color: #FEF0F0;
					}
				}
			}
		}
	}
}

// 自定义首页顶部导航栏中的用户菜单样式
.headerbar-user-menu {
	.diamond-indicator {
		display: flex;
		align-items: center;
		
		.diamond-icon {
			font-size: 16px;
			color: gold;
			margin-right: 4px;
		}
		
		.diamond-count {
			font-size: 14px;
			color: #333;
		}
	}
}

// 首页样式的弹窗
.information-tooltip {
	padding: 0 !important;
	border-radius: 12px !important;
	overflow: hidden !important;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
	border: none !important;
	z-index: 9999 !important;
	width: 370px !important;  /* 增加宽度以确保内容完全显示 */
	
	.el-popper__arrow {
		display: none; /* 确保箭头被隐藏 */
	}
	
	.user-popover-content {
		display: flex;
		flex-direction: column;
		/* 确保内容不被裁剪 */
		overflow: visible;
		
		// 上下部分样式保持一致
		:deep(.vipTop) {
			border-radius: 0 !important;
			background: linear-gradient(to right, #e2c48d, #d1a15f) !important;
		}
		
		:deep(.account_row) {
			display: flex;
			flex-direction: column;
			padding: 24px 12px 0;
			width: 100%;
			box-sizing: border-box;
		
			.account_row_charact_space {
				border-bottom: 1px solid rgba(0,0,0,0.1);
				padding-bottom: 12px;
				display: flex;
				flex-direction: column;
				
				.account_row_charact_space_item {
					display: flex;
					margin-bottom: 16px;
					line-height: 21px;
					align-items: center;
					cursor: pointer;
					padding: 8px 12px;
					transition: all 0.2s;
					
					&:hover {
						background-color: #f5f7fa;
					}
					
					.account_row_charact_space_item_label {
						display: flex;
						align-items: center;
						
						.account_row_charact_space_item_icon {
							width: 15px;
							height: 13px;
							margin-right: 8px;
							display: flex;
							align-items: center;
							justify-content: center;
						}
						
						.account_row_charact_space_item_title {
							font-size: 14px;
							color: #353D49;
						}
					}
					
					.account_row_charact_space_item_value {
						margin-left: auto;
						
						span {
							font-size: 17px;
							font-weight: 500;
							color: #1D2129;
						}
						
						.account_row_charact_space_item_value_more {
							width: 5px !important;
							height: 8px !important;
						}
					}
					
					&.charact_item {
						cursor: default;
						padding: 12px;
						
						.account_row_charact_space_item_label {
							.account_row_charact_space_item_title {
								font-weight: 500;
							}
						}
						
						.account_row_charact_space_item_value {
							span {
								font-size: 17px;
								font-weight: 600;
								color: #1D2129;
							}
						}
					}
					
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
			
			.account_row_info {
				padding-top: 16px;
				padding-bottom: 12px;
			}
			
			.account_row_loginout {
				padding: 16px 0 20px 0;
				border-bottom: none;
				margin: 0 12px;
				
				.account_row_charact_space_item {
					padding: 8px 12px;
					border: 1px solid #F56C6C;
					border-radius: 4px;
					
					.account_row_charact_space_item_label {
						.account_row_charact_space_item_title {
							color: #F56C6C;
						}
					}
					
					&:hover {
						background-color: #FEF0F0;
					}
				}
			}
		}
	}
}

// 添加tooltip样式
.tooltip-container {
	position: relative;
	display: inline-block;
	
	// 为tooltip-container内的按钮添加特殊样式
	.btn {
		&:hover {
			// 确保hover状态不会改变背景色
			background: #0AAF60 !important;
			color: #ffffff !important;
			opacity: 1 !important;
			// 添加阴影效果作为视觉反馈
			box-shadow: 0 2px 8px rgba(10, 175, 96, 0.2);
		}
	}
}

.custom-tooltip {
	position: absolute;
	top: 40px; // 改为按钮下方
	left: 50%;
	transform: translateX(-50%);
	background-color: #303133;
	color: white;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	white-space: nowrap;
	z-index: 9999;
	
	&::after {
		content: '';
		position: absolute;
		top: -5px; // 箭头位置改到顶部
		left: 50%;
		transform: translateX(-50%);
		border-width: 0 5px 5px; // 反转箭头方向
		border-style: solid;
		border-color: transparent transparent #303133; // 改变边框颜色
	}
}
</style>