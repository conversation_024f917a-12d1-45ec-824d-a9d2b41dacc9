<template>
	<div class="left-menu">
		<div v-for="(item, index) in menuItems" :key="index" class="menu-item"
			:class="{ active: activeMenuItem === index }" @click="handleMenuClick(index)">
			<!-- 菜单图标 -->
			<div class="icon-container">
				<img v-if="activeMenuItem === index && index === 0" src="@/assets/img/down1.png" :alt="item.name" class="menu-text selected-icon">
				<img v-else-if="activeMenuItem === index && index === 1" src="@/assets/img/down2.png" :alt="item.name" class="menu-text selected-icon">
				<img v-else-if="activeMenuItem === index && index === 2" src="@/assets/img/down3.png" :alt="item.name" class="menu-text selected-icon">
				<img v-else-if="activeMenuItem === index && index === 3" src="@/assets/img/down4.png" :alt="item.name" class="menu-text selected-icon">
				<img v-else :src="item.icon" :alt="item.name" class="menu-text">
			</div>
		</div>
	</div>
</template>

<script>
import { ref, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePreviewStore } from '@/stores/previewStore' // 导入预览存储
import { useMusicStore } from '@/stores/modules/musicStore' // 添加音乐存储引用

// 导入图标
import wenanIcon from '@/assets/img/wenanIcon.png'
import peiyinIcon from '@/assets/img/peiyinIcon.png'
import yinleIcon from '@/assets/img/yinleIcon.png'
import shipinIcon from '@/assets/img/shipinIcon.png'

export default {
	name: 'LeftMenu',
	setup() {
		const router = useRouter()
		const route = useRoute()
		const activeMenuItem = ref(0)
		const previewStore = usePreviewStore() // 使用预览存储
		const musicStore = useMusicStore() // 添加音乐存储

		// 菜单项数据
		const menuItems = [
			{ name: '文案创作', icon: wenanIcon, path: '/ContentCreation' },
			{ name: '配音', icon: peiyinIcon, path: '/VoiceOver' },
			{ name: '音乐音效', icon: yinleIcon, path: '/MusicAudio' },
			{ name: '视频剪片', icon: shipinIcon, path: '/VideoEditing' }
		]

		// 根据当前路由设置活动菜单项
		const setActiveMenuItem = () => {
			const currentPath = route.path
			console.log('当前路径:', currentPath)
			const index = menuItems.findIndex(item => item.path === currentPath)
			if (index !== -1) {
				activeMenuItem.value = index
				console.log('设置活动菜单项:', index, menuItems[index].name)
			}
		}

		// 初始化时设置活动菜单项
		onMounted(() => {
			setActiveMenuItem()
		})

		// 监听路由变化，确保路由变化时更新活动菜单项
		watch(() => route.path, (newPath) => {
			console.log('路由变化:', newPath)
			setActiveMenuItem()
		}, { immediate: true })

		// 处理菜单点击事件 - 在路由跳转前保存预览内容，但清空标题
		const handleMenuClick = (index) => {
			// 如果点击的是当前激活的菜单项，不需要执行任何操作
			if (activeMenuItem.value === index) {
				return
			}
			
			// 确保在路由跳转前保存当前的预览内容
			console.log('菜单点击，保存当前预览内容，保留标题')

			// 记录内容状态到持久化存储，确保切换页面后内容保持
			const currentContent = document.querySelector('.preview-textarea')?.innerText
			if (currentContent) {
				previewStore.setContent(currentContent)
			}
			
			// 不再清空标题，保留当前标题
			// previewStore.setTitle('')
			
			// 不再修改标题输入框的值
			// const titleInput = document.querySelector('.title-input input')
			// if (titleInput) {
			// 	titleInput.value = ''
			// }
			
			// 注意：音乐存储是通过musicStore自动处理的，不需要额外保存
			// 因为useMusicStore本身就是持久化的，所以页面切换时不会丢失数据

			// 切换活动菜单项
			activeMenuItem.value = index
			console.log('点击设置活动菜单项:', index, menuItems[index].name)

			// 执行路由跳转
			router.push(menuItems[index].path)
		}

		return {
			activeMenuItem,
			menuItems,
			handleMenuClick
		}
	}
}
</script>

<style lang="scss" scoped>
.left-menu {
	width: 98px;
	background: #fff;
	border-radius: 8px 0 0 8px;
	height: auto;
	border-right: 1px solid rgba(0, 0, 0, 0.2);
	padding: 20px 0;

	.menu-item {
		width: 100%;
		height: 76px; /* 调整高度 */
		display: flex;
		align-items: center;
		justify-content: flex-start; /* 改为左对齐 */
		cursor: pointer;
		position: relative;
		background: #fff;
		margin: 10px 0;
		padding-left: 4px; /* 左侧增加内边距 */

		.icon-container {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			width: 100px; /* 与图片宽度匹配 */
		}

		.menu-text {
			height: 93px; /* 调整高度 */
			width: 83px; /* 调整宽度 */
			object-fit: contain; /* 改为contain确保图片完整显示 */
			transform: scale(1);
			max-width: none;
			max-height: none;
			position: relative;
			z-index: 2;
		}

		.selected-icon {
			height: 83px; /* 与普通图标保持一致 */
			width: 93px; /* 与普通图标保持一致 */
			/* 移除其他不必要的样式 */
		}

		// 修改右侧线条，只在选中状态显示
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: 0;
			transform: translateY(-50%);
			width: 3px;
			height: 0; /* 默认不显示 */
			background-color: transparent; /* 默认透明 */
			z-index: 1;
		}

		// 修改悬停效果
		&:hover {
			// background-color: #F5F7FA;
			
			/* 悬停时也不显示线条
			&::after {
				background-color: #C0C4CC; 
			}
			*/
		}

		// 修改选中效果
		&.active {
			// background-color: #F5F7FA;
			
			&::after {
				
				height: 80%; /* 选中时边框显示 */
			}
		}
	}
}
</style>