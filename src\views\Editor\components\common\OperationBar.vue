<template>
	<!-- 此组件的功能已移至 HeaderBar.vue -->
	<div class="operation-bar-container" v-if="showLegacyBar">
		<div class="operation-bar">
			<button class="btn" v-for="(button, index) in buttons" :key="index" @click="handleClick(button.action)">
				<i :class="['btn-icon', button.icon]" v-if="button.icon"></i>
				{{ button.text }}
			</button>
		</div>
	</div>
</template>

<script>
export default {
	name: 'OperationBar',
	props: {
		buttons: {
			type: Array,
			default: () => [
				{ text: '新建', action: 'new' },
				{ text: '保存', action: 'save' },
				{ text: '去剪辑', action: 'edit' },
				{ text: '导出', action: 'export' }
			]
		},
		showLegacyBar: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		handleClick(action) {
			this.$emit('action', action);
		}
	}
}
</script>

<style lang="scss" scoped>
.operation-bar-container {
	background: #EFEFF1;
	display: flex;
	flex-direction: column;
}

// 固定标题样式
.fixed-title {
	font-size: 20px;
	font-weight: bold;
	color: #303133;
	padding: 15px 20px 0 20px;
}

.operation-bar {
	padding: 15px 20px 20px 20px;
	display: flex;
	gap: 12px;

	.btn {
		height: 32px;
		padding: 0 16px;
		border-radius: 4px;
		border: 1px solid #0AAF60;
		display: flex;
		align-items: center;
		gap: 8px;
		cursor: pointer;
		background: #fff;
		color: #0AAF60;
		font-size: 14px;
		transition: all 0.3s;

		&:hover {
			color: #0AAF60;
			border-color: #0AAF60;
			background-color: #f0f9eb;
		}

		// 最后一个按钮（导出）使用白色文字
		&:last-child {
			background: #0AAF60;
			color: #ffffff;
			border: none;

			&:hover {
				opacity: 0.9;
				background: #0AAF60;
				color: #ffffff;
			}
		}
	}
}
</style>