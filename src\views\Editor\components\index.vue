<template>
	<div class="voice-over-container">
		<!-- 使用HeaderBar组件但不显示标题 -->
		<HeaderBar title="一键成片" />

		<!-- 使用OperationBar组件替换原来的操作栏 -->
		<OperationBar @action="handleBarAction" />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<!-- 左侧区域 - 配音选择界面 -->
			<div class="left-section">
				<!-- 左侧菜单 -->
				<LeftMenu />
				
				<!-- 配音工具区域 -->
				<div class="voice-over-tools">
					<!-- 顶部分类导航 -->
					<div class="category-nav">
						<div class="category-container">
							<!-- 主要分类：voiceType - 注释掉第一个分类 -->
							<!-- <div class="main-categories">
								<div 
									v-for="(category, index) in mainCategories" 
									:key="'main-'+index"
									:class="['category-item', { active: currentMainCategory === category.id }]"
									@click="selectMainCategory(category.id)">
									{{ category.name }}
								</div>
							</div> -->
							
							<!-- 性别分类：genders -->
							<div class="main-categories" v-if="genderCategories.length > 1">
								<div 
									v-for="(gender, index) in genderCategories" 
									:key="'gender-'+index"
									:class="['category-item', { active: currentGender === gender.id }]"
									@click="selectGender(gender.id)">
									{{ gender.name }}
								</div>
							</div>
							
							<!-- 场景分类：sceneMetadata -->
							<div class="main-categories" v-if="sceneCategories.length > 1">
								<div 
									v-for="(scene, index) in sceneCategories" 
									:key="'scene-'+index"
									:class="['category-item', { active: currentScene === scene.id }]"
									@click="selectScene(scene.id)">
									{{ scene.name }}
								</div>
							</div>
							
							<!-- 推荐标签分类：来自场景的recommend_tags -->
							<div class="main-categories" v-if="recommendTagCategories.length > 1">
								<div 
									v-for="(tag, index) in recommendTagCategories" 
									:key="'tag-'+index"
									:class="['category-item', { active: currentRecommendTag === tag.id }]"
									@click="selectRecommendTag(tag.id)">
									{{ tag.name }}
								</div>
							</div>
							
							<!-- 子分类 -->
							<div class="main-categories" v-if="subCategories.length > 0">
								<div 
									v-for="(category, index) in subCategories" 
									:key="'sub-'+index"
									:class="['category-item', { active: currentSubCategory === category.id }]"
									@click="selectSubCategory(category.id)">
									{{ category.name }}
								</div>
							</div>
							
							<!-- 详细分类 -->
							<div class="main-categories" v-if="detailedCategories.length > 0">
								<div 
									v-for="(category, index) in detailedCategories" 
									:key="'detail-'+index"
									:class="['category-item', { active: currentDetailedCategory === category.id }]"
									@click="selectDetailedCategory(category.id)">
									{{ category.name }}
								</div>
							</div>
						</div>
					</div>

					<!-- 声音角色选择区域 -->
					<div class="voice-grid">
						<div class="voice-row">
							<div class="voice-item-wrapper" 
								 v-for="(voice, index) in voices" 
								 :key="index" 
								 @click="selectVoice(index)" 
								 :class="getVoiceItemClass(voice, index)">
								<!-- 精品和珍享图片 -->
								<div class="position_image overflow-hidden" v-if="voice.tag === '臻享' || voice.tag === '精品'">
									<img :src="getTagIcon(voice.tag)" :alt="voice.tag || '音色标签'" class="premium-badge">
								</div>
								
								<div class="voice-item">
									<!-- 头像区域 -->
									<div class="voice-avatar margin_t-14">
										<span class="avatar-circle">
											<img :src="voice.avatarUrl || voice.avatar || '@/assets/img/image111.png'" alt="音色头像">
										</span>
										<!-- 播放按钮 - 只在有音频URL且未播放时显示 -->
										<div class="play-button width-20 height-20 flex flex_a_i-center flex_j_c-center"
											 @click.stop="playAudio(voice)" 
											 v-show="!voice.isPlaying && (voice.audioUrl || voice.demoUrl)">
											<i class="iconfont icon-bofang" style="font-size: 12px; color: rgb(255, 255, 255);"></i>
										</div>
										<!-- 暂停按钮 - 只在正在播放时显示 -->
										<div class="pause-button width-20 height-20 flex flex_a_i-center flex_j_c-center"
											 @click.stop="pauseAudio(voice)"
											 v-show="voice.isPlaying">
											<i class="iconfont icon-pause-fill" style="font-size: 12px; color: rgb(255, 255, 255);"></i>
										</div>
									</div>
									<!-- 名称 -->
									<div class="voice-name margin_t-4 font-size-14">{{ voice.name }}</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 底部控制区 -->
					<div class="control-bar">
						<div class="volume-control">
							<ElPopover
								placement="top"
								:width="463"
								trigger="click"
								v-model:visible="pitchSliderVisible"
								popper-class="gradient-slider-popover"
								popper-style="background-color: #000000; opacity: 0.75; height: 38px; padding: 0 10px; display: flex; align-items: center; margin-left: 120px;"
								:offset="10"
								:transition="'none'"
								:hide-after="0"
							>
								<template #reference>
									<div class="pitch-display">
										<span class="pitch-label">语调</span>
										<span class="pitch-value">{{ pitchValue > 0 ? '+' + pitchValue : pitchValue }}%</span>
									</div>
								</template>
								
								<!-- 弹出的滑块控制器 -->
								<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
									<div class="el-slider flex-item_f-8 slider gradient-slider">
										<ElSlider 
											v-model="pitchValue"
											:min="pitchMin"
											:max="pitchMax"
											:step="pitchStep"
											:show-stops="showPitchMarks"
											:format-tooltip="(val) => val > 0 ? '+' + val : val"
											class="gradient-slider"
										/>
									</div>
									<span class="font-size-14 margin_l-10">{{ pitchValue }}</span>
								</div>
							</ElPopover>
						</div>
						
						<div class="action-buttons">
							<button class="listen-btn" @click="handleListenButtonClick">
								<template v-if="!isPlaying">试听</template>
								<template v-else>
									<span class="control-icons">
										<i class="pause-icon"></i>
										<i class="divider"></i>
										<i class="stop-icon"></i>
									</span>
								</template>
							</button>
							<button class="generate-btn" @click="generateVoiceOver">合成音频</button>
						</div>
					</div>

					<!-- 音频播放器控件 - 仅在试听时显示 -->
					<div class="audio-player-wrapper" v-if="isPlaying">
						<div class="audio-player-control">
							<div class="player-button" @click="handlePlayerButtonClick">
								<i v-if="!isPlayerPlaying" class="player-icon"></i>
								<i v-else class="pause-icon"></i>
							</div>
							<div class="progress-bar" 
								 @click="handleProgressBarClick"
								 @mousedown="startDragging">
								<div class="progress-filled" :style="{width: progress + '%'}"></div>
							</div>
							<div class="player-time">{{ timeDisplay }}</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 右侧预览区域 - 使用PreviewPanel组件 -->
			<PreviewPanel 
				v-model:title="previewTitle"
				v-model:content="previewContent"
				:musicList="musicList"
				:videoList="videoList"
				:roleList="roleList"
				:isVideoEditingPage="false"
				@generate-video="handleGenerateVideo"
				@add-role="handleAddRole"
				@add-music="handleAddMusic"
				@add-video="handleAddVideo"
				@volume-change="handleVolumeChange"
			/>
		</div>

		<!-- 在组件末尾添加音乐对话框组件 -->
		<MusicDialog
			v-model:visible="musicDialogVisible"
			:material-list="musicList"
			@close="musicDialogVisible = false"
			@confirm="handleMusicDialogConfirm"
			@remove="handleMusicDialogRemove"
			@togglePlay="handleMusicDialogTogglePlay"
			@add-music="handleMusicDialogAddMusic"
		/>
	</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import HeaderBar from '@/views/Editor/components/common/HeaderBar.vue'
import OperationBar from '@/views/Editor/components/common/OperationBar.vue'
import PreviewPanel from '@/views/Editor/components/PreviewPanel.vue'
import { ElMessage, ElLoading, ElPopover, ElSlider, ElOption, ElSelect } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useMusicStore } from '@/stores/modules/musicStore'
import { usePreviewStore } from '@/stores/previewStore'
import LeftMenu from '@/views/Editor/components/common/LeftMenu.vue'
import { getAllVoices, getAllVoiceMetadata, queryVoicesByCategory, generateAudio } from '@/api/voiceOver'
import MusicDialog from '@/components/MusicDialog.vue'  // 添加音乐对话框组件

const router = useRouter()
const route = useRoute()
const musicStore = useMusicStore()
const previewStore = usePreviewStore()

// ===== 状态管理 =====
// 预览面板相关状态
const previewTitle = ref(previewStore.title || '')
const previewContent = ref(previewStore.content || '')
const musicList = ref([])
const videoList = ref([])

// 配音相关状态
const selectedVoice = ref(-1)  // 当前选中的音色索引，-1表示未选中
const pitchValue = ref(0)    // 语调调整值，范围-12到12，默认为0
const defaultAvatar = '/path/to/default/avatar.png'

// 音色列表数据
const voices = ref([])

// 音频播放相关状态
const audioPlayer = ref(null)  // 音频播放器实例
const currentPlayingVoice = ref(null)  // 当前正在播放的音色
const audioDuration = ref(0) // 音频总时长（秒）
const currentTime = ref(0)    // 当前播放位置（秒）
const progress = ref(0)       // 播放进度（百分比）
const isDragging = ref(false) // 是否正在拖动进度条
const audioTimer = ref(null)  // 播放进度定时器

// 播放器按钮状态（与试听状态区分开）
const isPlayerPlaying = ref(false)

// 分类相关状态
const voiceMetadata = ref({
	voiceType: [],     // 第一个分类：音色类型
	genders: [],       // 第二个分类：性别
	sceneMetadata: [], // 第三个分类：场景元数据
	recommendTags: [], // 第四个分类：场景推荐标签（从第三项中提取）
	categories: []     // 保留原有分类数据结构
})

// 当前选中的分类值
const currentMainCategory = ref('all')    // 主分类
const currentGender = ref('all')          // 性别分类
const currentScene = ref('all')           // 场景分类
const currentRecommendTag = ref('all')    // 推荐标签
const currentSubCategory = ref('all')     // 子分类
const currentDetailedCategory = ref('all') // 详细分类

// 试听按钮状态变量
const isPlaying = ref(false)

// 语调相关状态
const pitchSliderVisible = ref(false)  // 控制语调滑块弹出层的显示
const pitchMin = ref(-12)              // 语调最小值
const pitchMax = ref(12)               // 语调最大值
const pitchStep = ref(2)               // 语调步长
const showPitchMarks = ref(true)       // 是否显示刻度标记

// 添加角色列表管理
const roleList = ref([]) // 改回原始声明，不再从 previewStore 获取初始值

// 添加音乐对话框状态变量
const musicDialogVisible = ref(false)

// 声音查询筛选参数
const filterParams = ref({
	voiceType: '',    // 主分类 
	gender: '',       // 性别
	scene: '',        // 场景
	recommendTag: '', // 推荐标签
	subCategory: '',  // 子分类
	detailedCategory: '', // 详细分类
	tts: '3'          // 添加tts参数，值为'3'
})

// ===== 计算属性 =====
// 主分类列表，包含"全部"选项和API返回的voiceType数据
const mainCategories = computed(() => {
	return [
		{ id: 'all', name: '全部', visible: true, level: 1 },
		...(voiceMetadata.value.voiceType || []).map(item => ({
			...item,
			visible: true,
			level: 1
		}))
	]
})

// 性别分类列表
const genderCategories = computed(() => {
	return [
		{ id: 'all', name: '全部', visible: true, level: 2 },
		...(voiceMetadata.value.genders || []).map(item => ({
			...item,
			visible: true,
			level: 2
		}))
	]
})

// 场景分类列表
const sceneCategories = computed(() => {
	return [
		{ id: 'all', name: '全部', visible: true, level: 3 },
		...(voiceMetadata.value.sceneMetadata || []).map(item => ({
			...item,
			visible: true,
			level: 3
		}))
	]
})

// 推荐标签分类列表 - 与场景分类联动
const recommendTagCategories = computed(() => {
	// 添加"全部"选项
	const allOption = [{ id: 'all', name: '全部', visible: true, level: 4 }]
	
	// 如果选择了"全部"场景，则显示所有推荐标签
	if (currentScene.value === 'all') {
		return [
			...allOption,
			...(voiceMetadata.value.recommendTags || []).map(item => ({
				...item,
				visible: true,
				level: 4
			}))
		]
	}
	
	// 如果选择了特定场景，则只显示该场景的推荐标签
	const selectedScene = voiceMetadata.value.sceneMetadata?.find(
		scene => scene.id === currentScene.value
	)
	
	if (!selectedScene || !selectedScene.recommend_tags) {
		return allOption
	}
	
	// 处理该场景的推荐标签
	const sceneTags = selectedScene.recommend_tags.map(tagObj => ({
		id: tagObj.recommend_tags || '',
		name: tagObj.recommend_tags || '',
		sceneId: selectedScene.id,
		visible: true,
		level: 4
	})).filter(tag => tag.id) // 过滤掉无效标签
	
	return [...allOption, ...sceneTags]
})

// 子分类列表 - 基于选择的主分类
const subCategories = computed(() => {
	if (currentMainCategory.value === 'all') {
		return []
	}
	
	const selectedCategory = voiceMetadata.value.categories?.find(
		cat => cat.id === currentMainCategory.value
	)
	
	if (!selectedCategory || !selectedCategory.subCategories) {
		return []
	}
	
	// 添加"全部"选项
	return [
		{ id: 'all', name: '全部', visible: true, level: 5 },
		...selectedCategory.subCategories.map(item => ({
			...item,
			visible: true,
			level: 5
		}))
	]
})

// 详细分类列表 - 基于选择的子分类
const detailedCategories = computed(() => {
	if (currentMainCategory.value === 'all' || currentSubCategory.value === 'all') {
		return []
	}
	
	const selectedCategory = voiceMetadata.value.categories?.find(
		cat => cat.id === currentMainCategory.value
	)
	
	const selectedSubCategory = selectedCategory?.subCategories?.find(
		cat => cat.id === currentSubCategory.value
	)
	
	if (!selectedSubCategory || !selectedSubCategory.detailedCategories) {
		return []
	}
	
	// 添加"全部"选项
	return [
		{ id: 'all', name: '全部', visible: true, level: 6 },
		...selectedSubCategory.detailedCategories.map(item => ({
			...item,
			visible: true,
			level: 6
		}))
	]
})

// ===== 方法定义 =====
/**
 * 选择音色
 * @param {Number} index - 选中的音色索引
 */
const selectVoice = (index) => {
	selectedVoice.value = index
	const voiceData = voices.value[index]
	
	// 记录合成所需信息
	voiceData.voiceName = voiceData.voiceName 
	
	console.log(`选择音色: ${voiceData.name}, voiceName/ID: ${voiceData.voiceName}`)
	
	// 不再立即添加到预览区，仅设置状态
	// addRoleToPreview(voiceData) - 移除这行，仅在合成音频成功后调用
	// ElMessage.success(`已选择音色"${voiceData.name}"，点击合成音频后会添加到右侧`)
}

/**
 * 将选中的声音角色添加到预览区域
 * @param {Object} voiceData - 选中的声音数据
 * @param {String} audioUrl - 已合成的音频URL
 */
const addRoleToPreview = (voiceData, audioUrl) => {
	console.log('添加角色到预览区域:', voiceData, '音频URL:', audioUrl)
	
	// 只有在有真实音频URL时才添加角色
	if (!audioUrl) {
		console.warn('没有合成音频URL，不添加角色到预览区')
		return
	}
	
	// 准备角色数据，包含完整音色信息与合成的音频
	const roleData = {
		id: voiceData.id,
		name: voiceData.name,
		voiceName: voiceData.voiceName,
		avatar: voiceData.avatarUrl || voiceData.avatar,
		audioUrl: audioUrl, // 使用已合成的音频URL
		selectedRole: voiceData.name,
		isDemo: false // 这是真实合成的音频
	}
	
	roleList.value = [roleData]
	
	// 保存到 previewStore
	previewStore.setRole({
		name: voiceData.name,
		voiceName: voiceData.voiceName || voiceData.id,
		audioUrl: audioUrl,
		isDemo: false
	})
	
	console.log(`已添加角色"${voiceData.name}"和合成音频到右侧预览区`)
}

/**
 * 在合成音频成功后调用这个函数更新角色的实际音频URL
 * @param {String} audioUrl - 合成的实际音频URL
 */
const updateRoleAudio = (audioUrl) => {
	if (roleList.value.length > 0) {
		// 更新roleList中的音频URL
		roleList.value[0].audioUrl = audioUrl;
		roleList.value[0].isDemo = false; // 不再是示例
		
		// 更新store
		previewStore.setRole({
			name: roleList.value[0].name,
			voiceName: roleList.value[0].voiceName,
			audioUrl: audioUrl,
			isDemo: false
		});
		
		console.log('更新角色实际音频URL:', audioUrl);
	}
}

/**
 * 试听按钮点击处理
 */
const handleListenButtonClick = async () => {
	// 检查是否已选择音色
	if (selectedVoice.value < 0 || !voices.value[selectedVoice.value]) {
		ElMessage.warning('请先选择一个音色')
		return
	}
	
	// 检查预览区是否有文本内容
	if (!previewContent.value || previewContent.value.trim() === '') {
		ElMessage.warning('请先在右侧填写文案内容')
		return
	}
	
	// 获取选中的音色数据
	const selectedVoiceData = voices.value[selectedVoice.value]
	
	// 如果已经在播放，则停止播放
	if (isPlaying.value) {
		// 设置状态为非播放
		isPlaying.value = false
		isPlayerPlaying.value = false
		
		// 如果有音频正在播放，停止它
		if (audioPlayer.value) {
			audioPlayer.value.pause()
			audioPlayer.value.currentTime = 0
		}
		
		// 重置播放进度
		resetProgress()
		
		// 清除定时器
		clearProgressTimer()
		
		ElMessage.info('已停止播放')
		return
	}
	
	// 准备API请求参数
	const params = {
		user_id: "11",
		// 使用预览区文本，并限制长度为500字
		text: (() => {
			// 这里已确保 previewContent.value 有值
			return previewContent.value.length > 500 ? 
				previewContent.value.substring(0, 500) : 
				previewContent.value;
		})(),
		voice_id: selectedVoiceData.voiceName,
		audio_format: "mp3",
		pitch: pitchValue.value || 0.0,
	}
	
	// 显示加载状态
	const loadingInstance = ElLoading.service({
		text: '正在生成音频...',
		background: 'rgba(255, 255, 255, 0.7)'
	})
	
	try {
		// 调用API并等待响应
		const response = await generateAudio(params)
		console.log("生成音频响应:", response)
		
		// 处理成功响应
		if (response && response.status_code == 200) {
			// 获取合成的音频URL
			const audioUrl = response.content.result.audio_file
			
			// 添加角色信息到右侧预览区
			const selectedVoiceData = voices.value[selectedVoice.value]
			addRoleToPreview(selectedVoiceData, audioUrl)
			
			// 创建或重置音频播放器
			if (!audioPlayer.value) {
				audioPlayer.value = new Audio()
				audioPlayer.value.addEventListener('ended', handleAudioEnded)
				
				// 添加元数据加载事件
				audioPlayer.value.addEventListener('loadedmetadata', () => {
					// 获取音频的实际时长
					audioDuration.value = audioPlayer.value.duration
					console.log("音频时长:", audioDuration.value, "秒")
				})
			}
			
			// 设置音频源
			audioPlayer.value.src = response.content.result.audio_file
			
			// 设置一个默认时长，等待元数据加载后更新
			audioDuration.value = 0
			
			try {
				// 尝试播放音频
				await audioPlayer.value.play()
				
				// 设置播放状态
				isPlaying.value = true
				isPlayerPlaying.value = true
				
				// 启动播放进度定时器
				startProgressTimer()
				
				// ElMessage.success('试听开始')
			} catch (playError) {
				console.error('音频播放失败:', playError)
				ElMessage.error('音频播放失败，请重试')
				isPlaying.value = false
			}
			
			// 更新角色的实际音频URL
			updateRoleAudio(response.content.result.audio_file);
		} else {
			ElMessage.error('生成音频失败，请重试')
		}
	} catch (error) {
		console.error('API调用失败:', error)
		ElMessage.error('生成音频失败，请重试')
	} finally {
		loadingInstance.close()
	}
}

/**
 * 合成完整音频
 * 不限制文本长度
 */
const generateVoiceOver = async () => {
	// 检查是否已选择音色
	if (selectedVoice.value < 0 || !voices.value[selectedVoice.value]) {
		ElMessage.warning('请先选择一个音色')
		return
	}
	
	// 检查预览区是否有文本内容
	if (!previewContent.value || previewContent.value.trim() === '') {
		ElMessage.warning('请先在右侧填写文案内容')
		return
	}
	
	// 获取选中的音色数据
	const selectedVoiceData = voices.value[selectedVoice.value]
	
	// 显示加载提示
	const loadingInstance = ElLoading.service({
		lock: true,
		text: '正在合成音频，请稍候...',
		background: 'rgba(0, 0, 0, 0.7)'
	})
	
	try {
		// 准备API请求参数 - 使用完整文本
		const params = {
			user_id: "11",
			text: previewContent.value, // 使用完整文本，不限制长度
			voice_id: selectedVoiceData.voiceName, // 使用 voice_id 作为参数名
			audio_format: "mp3",
			pitch: pitchValue.value || 0.0,
		}
		
		// 调用API并等待响应
		const response = await generateAudio(params)
		console.log("生成音频响应:", response)
		
		// 处理成功响应
		if (response && response.status_code == 200) {
			// 关闭加载提示
			loadingInstance.close()
			
			// 显示成功消息
			// ElMessage.success('音频合成成功')
			
			// 获取音频URL
			const audioUrl = response.content.result.audio_file
			
			// 添加角色信息到右侧预览区
			const selectedVoiceData = voices.value[selectedVoice.value]
			addRoleToPreview(selectedVoiceData, audioUrl)
			
			// 创建或重置音频播放器
			if (!audioPlayer.value) {
				audioPlayer.value = new Audio()
				audioPlayer.value.addEventListener('ended', handleAudioEnded)
				
				// 添加元数据加载事件
				audioPlayer.value.addEventListener('loadedmetadata', () => {
					// 获取音频的实际时长
					audioDuration.value = audioPlayer.value.duration
					console.log("音频时长:", audioDuration.value, "秒")
				})
			}
			
			// 设置音频源
			audioPlayer.value.src = response.content.result.audio_file
			
			// 设置一个默认时长，等待元数据加载后更新
			audioDuration.value = 0
			
			// 设置当前播放的音色
			currentPlayingVoice.value = selectedVoiceData
			
			// 更新为播放状态
			isPlaying.value = true
			isPlayerPlaying.value = true
			
			// 开始播放
			try {
				await audioPlayer.value.play()
				// 开始进度更新
				startProgressTimer()
			} catch (error) {
				// 处理播放错误
				console.error('播放合成的音频失败:', error)
				ElMessage.warning('播放音频失败，可能需要用户交互后才能播放')
				
				// 重置状态
				isPlaying.value = false
				isPlayerPlaying.value = false
			}
			
			// 更新角色的实际音频URL
			updateRoleAudio(response.content.result.audio_file);
		} else {
			// 关闭加载提示
			loadingInstance.close()
			
			// 显示错误消息
			ElMessage.error(response?.message || '音频合成失败，请重试')
		}
	} catch (error) {
		// 关闭加载提示
		loadingInstance.close()
		
		// 显示错误消息
		console.error('音频合成请求出错:', error)
		ElMessage.error('音频合成请求失败，请检查网络连接')
	}
}

/**
 * 处理操作栏动作
 * @param {String} action - 操作类型：new/recent/edit/export
 */
const handleBarAction = (action) => {
	console.log('操作栏动作:', action)
	switch (action) {
		case 'new':
			ElMessage.info('新建配音项目')
			break
		case 'save':
			ElMessage.info('查看最近配音项目')
			break
		case 'edit':
			ElMessage.info('前往剪辑页面')
			router.push('/VideoEditing')
			break
		case 'export':
			ElMessage.info('导出配音')
			generateVoiceOver()
			break
	}
}

// ===== 预览面板事件处理 =====
/**
 * 处理生成视频请求
 */
const handleGenerateVideo = () => {
	ElMessage.info('准备生成视频')
}

/**
 * 处理添加角色请求
 */
const handleAddRole = () => {
	if (selectedVoice.value >= 0 && voices.value[selectedVoice.value]) {
		// 如果已选中角色，则将其添加到预览区
		const selectedVoiceData = voices.value[selectedVoice.value]
		addRoleToPreview(selectedVoiceData)
	} else {
		// 如果未选中角色，提示用户在左侧选择
		ElMessage.info('请在左侧选择配音角色')
	}
}

/**
 * 处理添加音乐请求
 * @param {Boolean} showDialog - 是否显示对话框
 */
const handleAddMusic = (showDialog = false) => {
	// 检查是否有现有音乐
	if (musicList.value && musicList.value.length > 0) {
		// 如果有音乐，直接打开对话框
		musicDialogVisible.value = true
		console.log('已有音乐，打开音乐选择对话框')
	} else if (musicStore.musicList && musicStore.musicList.length > 0) {
		// 如果musicStore中有音乐但本地没有，先同步再打开对话框
		musicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
		musicDialogVisible.value = true
		console.log('从musicStore同步音乐并打开对话框')
	} else {
		// 如果没有音乐，跳转到音乐音效页面
		console.log('无音乐，跳转到音乐音效页面')
		router.push('/MusicAudio')
	}
}

/**
 * 处理添加视频请求
 * @param {Boolean} showDialog - 是否显示对话框
 */
const handleAddVideo = (showDialog = false) => {
	if (showDialog) {
		ElMessage.info('打开视频选择对话框')
	} else {
		router.push('/VideoEditing')
	}
}

/**
 * 处理音量变更
 * @param {Object} item - 音频/视频项
 * @param {Number} volume - 新的音量值
 */
const handleVolumeChange = (item, volume) => {
	console.log(`${item.title}音量变更为: ${volume}`)
}

/**
 * 根据标签类型获取对应图标
 * @param {String} tag - 标签类型
 * @returns {String} 图标URL
 */
const getTagIcon = (tag) => {
	switch(tag) {
		case '臻享':
			return new URL('@/assets/img/zhenxiang.png', import.meta.url).href
		case '精品':
			return new URL('@/assets/img/jingpin.png', import.meta.url).href
		case '精品':
			return new URL('@/assets/img/jingpin.png', import.meta.url).href
		default:
			return ''
	}
}

/**
 * 初始化音色图标
 * 作为API调用失败的备用方案
 */
const initVoiceIcons = () => {
	voices.value = voices.value.map(voice => {
		return {
			...voice,
			tagIcon: getTagIcon(voice.tag)
		}
	})
}

/**
 * 播放音频样本
 * @param {Object} voice - 要播放的音色对象
 */
const playAudio = (voice) => {
	// 如果有正在播放的音频，先停止它
	if (currentPlayingVoice.value && currentPlayingVoice.value !== voice) {
		currentPlayingVoice.value.isPlaying = false
	}
	
	// 检查当前声音是否有音频URL
	if (!voice.audioUrl && !voice.sample) {
		ElMessage.warning('该音色没有可播放的音频样本')
		return
	}
	
	// 创建或重设音频播放器
	if (!audioPlayer.value) {
		audioPlayer.value = new Audio()
		// 添加播放结束事件监听
		audioPlayer.value.addEventListener('ended', handleAudioEnded)
	}
	
	// 设置音频源
	audioPlayer.value.src = voice.audioUrl || voice.sample
	
	// 播放音频
	audioPlayer.value.play().then(() => {
		// 播放成功，更新状态
		voice.isPlaying = true
		currentPlayingVoice.value = voice
	}).catch(error => {
		// 播放失败，提示错误
		console.error('音频播放失败:', error)
		ElMessage.error('音频播放失败，请重试')
		voice.isPlaying = false
		currentPlayingVoice.value = null
	})
}

/**
 * 暂停音频
 * @param {Object} voice - 要暂停的音色对象
 */
const pauseAudio = (voice) => {
	if (audioPlayer.value && voice.isPlaying) {
		audioPlayer.value.pause()
		voice.isPlaying = false
		currentPlayingVoice.value = null
	}
}

/**
 * 处理音频播放结束事件
 */
const handleAudioEnded = () => {
	if (currentPlayingVoice.value) {
		currentPlayingVoice.value.isPlaying = false
		currentPlayingVoice.value = null
		isPlaying.value = false
	}
}

/**
 * 获取音色卡片的样式类
 * @param {Object} voice - 音色对象
 * @param {Number} index - 音色索引
 * @returns {Object} 包含样式类的对象
 */
const getVoiceItemClass = (voice, index) => {
	return {
		'active': selectedVoice.value === index,
		'playing': voice.isPlaying
	}
}

/**
 * 获取所有音色数据
 */
const fetchVoices = async () => {
	const loadingInstance = ElLoading.service({
		text: '正在加载音色数据...',
		background: 'rgba(255, 255, 255, 0.7)'
	})
	
	try {
		const res = await getAllVoices({tts:'3'})
		
		// 处理API返回的音色数据，添加前端需要的属性
		voices.value = res.map(voice => {
			// 从API提取的基础数据
			const baseVoice = {
				id: voice.id,
				name: voice.platformNickname || '未命名音色',
				voiceName: voice.voiceName, // 保存原始的 voiceName 字段
				tag: voice.voiceType || voice.tag || '经典',
				avatar: voice.avatar || '',
				avatarUrl: voice.avatarUrl || '',
				sample: voice.sampleUrl || voice.sample || '',
				// 添加 demoText 字段
				demoText: voice.demoText || '这是一段示例文本，用于展示该音色的效果。',
				
				// 其他字段保持不变...
				gender: voice.gender,
				language: voice.language,
				description: voice.description,
				
				// 添加前端需要的控制状态
				isPlaying: false,
				isSelected: false,
				loadProgress: 0,
				
				// 添加UI相关的属性
				tagIcon: '',
				displayOrder: voice.displayOrder || 0,
				
				// 添加业务逻辑相关的属性
				compatibility: calculateCompatibility(voice, previewContent.value),
				isFavorite: checkIfFavorite(voice.id),
				
				// 可以添加计算的属性
				shortName: (voice.platformNickname || '').substring(0, 10),
				category: mapVoiceToCategory(voice),
				
				// 添加音频URL字段
				audioUrl: voice.audioUrl || voice.sampleUrl || '',
			}
			
			return baseVoice
		})
		
		// 添加标签图标
		voices.value = voices.value.map(voice => ({
			...voice,
			tagIcon: getTagIcon(voice.tag)
		}))
		
		// 标记推荐音色
		markRecommendedVoices()
		
		// ElMessage.success('音色数据加载成功')
	} catch (error) {
		console.error('获取音色数据失败:', error)
		ElMessage.error('获取音色数据失败，请稍后重试')
		initVoiceIcons() // 初始化默认图标作为备用
	} finally {
		loadingInstance.close()
	}
}

/**
 * 计算音色与当前文本的兼容性
 * @param {Object} voice - 音色对象
 * @param {String} text - 文本内容
 * @returns {Number} 兼容性得分(0-100)
 */
const calculateCompatibility = (voice, text) => {
	// 简单示例：根据文本长度和音色特点计算
	return Math.min(100, Math.max(0, 80 + Math.random() * 20))
}

/**
 * 检查音色是否被用户收藏
 * @param {String} voiceId - 音色ID
 * @returns {Boolean} 是否已收藏
 */
const checkIfFavorite = (voiceId) => {
	// 简单示例
	return false
}

/**
 * 将音色映射到更具体的分类
 * @param {Object} voice - 音色对象
 * @returns {String} 分类名称
 */
const mapVoiceToCategory = (voice) => {
	// 根据音色属性将其映射到更精细的类别
	if (voice.gender === 'male') {
		return voice.age < 30 ? '青年男声' : '中年男声'
	} else if (voice.gender === 'female') {
		return voice.age < 30 ? '青年女声' : '中年女声'
	}
	return '通用'
}

/**
 * 标记推荐音色
 */
const markRecommendedVoices = () => {
	// 可以实现一些逻辑来标记推荐的音色
	if (voices.value.length > 0) {
		voices.value[0].isRecommended = true
	}
}

/**
 * 选择主分类
 * @param {String} id - 分类ID
 */
const selectMainCategory = (id) => {
	currentMainCategory.value = id
	currentSubCategory.value = 'all'
	currentDetailedCategory.value = 'all'
	
	// 实现层级过滤：先进行本地筛选
	const filteredList = voices.value.filter(voice => {
		return id === 'all' || voice.voiceType === id || voice.tag === id;
	});
	
	// 更新所有较低级别的标签
	updateLowerLevelTags(filteredList, 1);
	
	// 然后调用API获取完整数据
	filterVoices()
}

/**
 * 选择子分类
 * @param {String} id - 分类ID
 */
const selectSubCategory = (id) => {
	currentSubCategory.value = id
	currentDetailedCategory.value = 'all'
	
	// 实现层级过滤：先进行本地筛选
	const filteredList = voices.value.filter(voice => {
		return id === 'all' || voice.subCategory === id;
	});
	
	// 更新所有较低级别的标签
	updateLowerLevelTags(filteredList, 5);
	
	// 然后调用API获取完整数据
	filterVoices()
}

/**
 * 选择详细分类
 * @param {String} id - 分类ID
 */
const selectDetailedCategory = (id) => {
	currentDetailedCategory.value = id
	
	// 实现层级过滤：先进行本地筛选
	const filteredList = voices.value.filter(voice => {
		return id === 'all' || voice.detailedCategory === id;
	});
	
	// 更新所有较低级别的标签
	updateLowerLevelTags(filteredList, 6);
	
	// 然后调用API获取完整数据
	filterVoices()
}

/**
 * 选择推荐标签
 * @param {String} id - 标签ID
 */
const selectRecommendTag = (id) => {
	currentRecommendTag.value = id
	
	// 实现层级过滤：先进行本地筛选
	const filteredList = voices.value.filter(voice => {
		if (id === 'all') return true;
		
		// 处理推荐标签数组或字符串
		if (voice.recommendTags) {
			const tags = typeof voice.recommendTags === 'string'
				? voice.recommendTags.split(',')
				: Array.isArray(voice.recommendTags)
					? voice.recommendTags
					: [voice.recommendTags];
					
			return tags.some(tag => 
				tag && typeof tag === 'string' && tag.trim() === id
			);
		}
		return false;
	});
	
	// 更新所有较低级别的标签
	updateLowerLevelTags(filteredList, 4);
	
	// 然后调用API获取完整数据
	filterVoices()
}

/**
 * 根据分类筛选音色
 * 构建筛选参数并调用API
 */
const filterVoices = async () => {
	// 构建筛选参数
	const filterParams = {}
	
	if (currentMainCategory.value !== 'all') {
		filterParams.voiceType = currentMainCategory.value
	}
	
	if (currentGender.value !== 'all') {
		filterParams.gender = currentGender.value
	}
	
	if (currentScene.value !== 'all') {
		filterParams.sceneCategory = currentScene.value
	}
	
	if (currentSubCategory.value !== 'all') {
		filterParams.subCategory = currentSubCategory.value
	}
	
	if (currentDetailedCategory.value !== 'all') {
		filterParams.detailedCategory = currentDetailedCategory.value
	}
	
	if (currentRecommendTag.value !== 'all') {
		filterParams.recommendTags = currentRecommendTag.value
	}
	filterParams.tts = '3'
	// 调用API获取筛选后的音色数据
	const loadingInstance = ElLoading.service({
		text: '筛选音色数据...',
		background: 'rgba(255, 255, 255, 0.7)'
	})
	
	try {
		const res = await queryVoicesByCategory(filterParams)
		// 处理响应数据
		processVoiceData(res)
	} catch (error) {
		console.error('筛选音色失败:', error)
		ElMessage.error('筛选音色失败，请重试')
	} finally {
		loadingInstance.close()
	}
}

/**
 * 处理音色数据
 * @param {Array} data - 音色数据数组
 */
const processVoiceData = (data) => {
	voices.value = data.map(voice => {
		// 数据处理逻辑与fetchVoices方法相同
		const baseVoice = {
			id: voice.id,
			name: voice.platformNickname || '未命名音色',
			voiceName: voice.voiceName, // 保存原始的 voiceName 字段
			tag: voice.voiceType || voice.tag || '经典',
			avatar: voice.avatar || '',
			avatarUrl: voice.avatarUrl || '',
			sample: voice.sampleUrl || voice.sample || '',
			audioUrl: voice.audioUrl || voice.sampleUrl || '',
			demoText: voice.demoText || '这是一段示例文本，用于展示该音色的效果。',
			
			// 其他字段保持不变
			gender: voice.gender,
			language: voice.language,
			description: voice.description,
			
			// 前端状态
			isPlaying: false,
			isSelected: false,
			loadProgress: 0,
			
			// UI相关属性
			tagIcon: '',
			displayOrder: voice.displayOrder || 0,
			
			// 业务逻辑相关属性
			compatibility: calculateCompatibility(voice, previewContent.value),
			isFavorite: checkIfFavorite(voice.id),
			
			// 计算属性
			shortName: (voice.platformNickname || '').substring(0, 10),
			category: mapVoiceToCategory(voice)
		}
		
		return baseVoice
	})
	
	// 处理标签图标
	voices.value = voices.value.map(voice => ({
		...voice,
		tagIcon: getTagIcon(voice.tag)
	}))
	
	// 随机排序音色数据
	voices.value = shuffleArray(voices.value)
	
	// 标记推荐音色
	markRecommendedVoices()
}

/**
 * 获取分类元数据
 * 从API获取所有分类数据
 */
const fetchVoiceMetadata = async () => {
	try {
		const response = await getAllVoiceMetadata({tts:'3'})
		console.log('API返回的元数据:', response)
		
		// 确保获取data字段内的内容
		const res = response.data || response
		
		// 重置现有数据
		voiceMetadata.value = {
			voiceType: [],
			genders: [],
			sceneMetadata: [],
			recommendTags: [],
			categories: []
		}
		
		// 处理voiceType数据
		if (res && res.voiceType && Array.isArray(res.voiceType)) {
			voiceMetadata.value.voiceType = res.voiceType.map(item => ({
				id: item.voiceType || item.id || item.value || item.type || '',
				name: item.voiceType || item.label || item.type || '未知'
			}))
		}
		
		// 处理gender数据
		if (res && res.genders && Array.isArray(res.genders)) {
			voiceMetadata.value.genders = res.genders.map(item => {
				return {
					id: item.gender || item.id || item.value || '',
					name: item.gender || '未知'
				}
			})
		}
		
		// 处理sceneMetadata数据
		if (res && res.sceneMetadata && Array.isArray(res.sceneMetadata)) {
			voiceMetadata.value.sceneMetadata = res.sceneMetadata.map(item => {
				return {
					id: item.id || item.scene_category || '',
					name: item.scene_category || '未知',
					recommend_tags: item.recommend_tags || [] 
				}
			})
			
			// 从所有场景中收集推荐标签
			const allTags = [];
			
			// 处理所有场景的recommend_tags
			voiceMetadata.value.sceneMetadata.forEach(scene => {
				if (scene.recommend_tags && Array.isArray(scene.recommend_tags)) {
					// 处理标签对象数组
					const processTags = scene.recommend_tags.map(tagObj => {
						return {
							id: tagObj.recommend_tags || '',
							name: tagObj.recommend_tags || '',
							sceneId: scene.id || ''
						};
					});
					
					allTags.push(...processTags);
				}
			});

			// 去重处理
			const uniqueTags = new Map();
			allTags.forEach(tag => {
				if (!uniqueTags.has(tag.id) && tag.id) {
					uniqueTags.set(tag.id, tag);
				}
			});

			// 转换回数组
			voiceMetadata.value.recommendTags = Array.from(uniqueTags.values());

			console.log('最终的推荐标签列表:', voiceMetadata.value.recommendTags);
		}
		
		console.log('处理后的分类元数据:', voiceMetadata.value)
	} catch (error) {
		console.error('获取分类元数据失败:', error)
		ElMessage.warning('分类数据加载失败，显示默认分类')
		// 设置默认分类数据
		voiceMetadata.value = {
			voiceType: [
				{ id: '经典', name: '经典' },
				{ id: '精品', name: '精品' },
				{ id: '臻享', name: '臻享' }
			],
			genders: [
				{ id: 'male', name: '男声' },
				{ id: 'female', name: '女声' },
				{ id: 'children', name: '童声' }
			],
			sceneMetadata: [
				{ id: 'narration', name: '解说' },
				{ id: 'advertisement', name: '广告' },
				{ id: 'dubbing', name: '配音' }
			],
			recommendTags: [
				{ id: 'emotional', name: '情感' },
				{ id: 'calm', name: '平静' },
				{ id: 'professional', name: '专业' }
			],
			categories: []
		}
	}
}

/**
 * 选择性别分类
 * @param {String} id - 性别ID
 */
const selectGender = (id) => {
	currentGender.value = id
	
	// 实现层级过滤：先进行本地筛选
	const filteredList = voices.value.filter(voice => {
		return id === 'all' || voice.gender === id;
	});
	
	// 更新所有较低级别的标签
	updateLowerLevelTags(filteredList, 2);
	
	// 然后调用API获取完整数据
	filterVoices()
}

/**
 * 选择场景分类
 * @param {String} id - 场景ID
 */
const selectScene = (id) => {
	currentScene.value = id
	// 重置推荐标签选择
	currentRecommendTag.value = 'all'
	
	// 实现层级过滤：先进行本地筛选
	const filteredList = voices.value.filter(voice => {
		return id === 'all' || voice.sceneCategory === id;
	});
	
	// 更新所有较低级别的标签
	updateLowerLevelTags(filteredList, 3);
	
	// 然后调用API获取完整数据
	filterVoices()
}

// 组件生命周期钩子
// 组件挂载时初始化数据
onMounted(() => {
	fetchVoices() // 获取音色数据
	fetchVoiceMetadata() // 获取分类元数据
	
	// 清空角色列表，确保不会显示任何未合成的角色
	roleList.value = [];
	
	// 使用setTimeout确保弹出层已经被创建
	setTimeout(() => {
		const arrows = document.querySelectorAll('.gradient-slider-popover .el-popper__arrow::before');
		arrows.forEach(arrow => {
			arrow.style.backgroundColor = '#000000BF';
			arrow.style.borderColor = '#000000BF';
		});
	}, 500);
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
	if (audioPlayer.value) {
		audioPlayer.value.pause()
		audioPlayer.value.removeEventListener('ended', handleAudioEnded)
		audioPlayer.value = null
	}
	currentPlayingVoice.value = null
	
	// 清理播放进度定时器
	clearProgressTimer()
})

/**
 * 随机排序数组
 * @param {Array} array - 要排序的数组
 * @returns {Array} 随机排序后的数组
 */
const shuffleArray = (array) => {
	// 创建数组副本以避免修改原数组
	const shuffled = [...array];
	// Fisher-Yates 随机排序算法
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
}

/**
 * 开始播放进度定时器
 * 模拟音频播放进度增加
 */
const startProgressTimer = () => {
	// 清除可能存在的定时器
	clearProgressTimer()
	
	// 设置新的定时器，每100毫秒更新一次进度
	audioTimer.value = setInterval(() => {
		// 增加当前时间（100毫秒 = 0.1秒）
		currentTime.value += 0.1
		
		// 如果超过总时长，重置或停止
		if (currentTime.value >= audioDuration.value) {
			// 自动停止播放
			isPlaying.value = false
			clearProgressTimer()
			resetProgress()
			return
		}
		
	// 计算进度百分比
	progress.value = (currentTime.value / audioDuration.value) * 100
	}, 100)
}

/**
 * 清除播放进度定时器
 */
const clearProgressTimer = () => {
	if (audioTimer.value) {
		clearInterval(audioTimer.value)
		audioTimer.value = null
	}
}

/**
 * 重置播放进度
 */
const resetProgress = () => {
	currentTime.value = 0
	progress.value = 0
}

/**
 * 处理进度条点击
 * @param {Event} event - 鼠标事件
 */
const handleProgressBarClick = (event) => {
	if (!isPlayerPlaying.value || audioDuration.value <= 0) return
	
	const progressBar = event.currentTarget
	const rect = progressBar.getBoundingClientRect()
	const offsetX = event.clientX - rect.left
	const percentage = offsetX / progressBar.offsetWidth
	
	// 设置新的播放进度
	setProgress(percentage)
}

/**
 * 开始拖动进度条
 */
const startDragging = () => {
	if (!isPlayerPlaying.value) return
	
	// 暂停进度更新定时器
	clearProgressTimer()
	isDragging.value = true
	
	// 添加鼠标移动和鼠标松开事件监听
	document.addEventListener('mousemove', handleDragging)
	document.addEventListener('mouseup', stopDragging)
}

/**
 * 处理拖动过程
 * @param {Event} event - 鼠标事件
 */
const handleDragging = (event) => {
	if (!isDragging.value) return
	
	const progressBar = document.querySelector('.progress-bar')
	if (!progressBar) return
	
	const rect = progressBar.getBoundingClientRect()
	let offsetX = event.clientX - rect.left
	
	// 限制在进度条范围内
	if (offsetX < 0) offsetX = 0
	if (offsetX > progressBar.offsetWidth) offsetX = progressBar.offsetWidth
	
	const percentage = offsetX / progressBar.offsetWidth
	
	// 设置新的播放进度
	setProgress(percentage)
}

/**
 * 停止拖动进度条
 */
const stopDragging = () => {
	if (!isDragging.value) return
	
	isDragging.value = false
	
	// 移除事件监听
	document.removeEventListener('mousemove', handleDragging)
	document.removeEventListener('mouseup', stopDragging)
	
	// 继续播放进度更新
	if (isPlayerPlaying.value) {
		startProgressTimer()
	}
}

/**
 * 设置播放进度
 * @param {Number} percentage - 进度百分比 (0-1)
 */
const setProgress = (percentage) => {
	// 限制百分比在 0-1 之间
	percentage = Math.max(0, Math.min(1, percentage))
	
	// 更新进度和当前时间
	progress.value = percentage * 100
	currentTime.value = percentage * audioDuration.value
	
	// 更新音频元素的当前时间
	if (audioPlayer.value) {
		audioPlayer.value.currentTime = percentage * audioDuration.value
	}
}

/**
 * 播放器按钮点击事件
 * 控制播放/暂停
 */
const handlePlayerButtonClick = () => {
	// 切换播放状态
	isPlayerPlaying.value = !isPlayerPlaying.value
	
	if (isPlayerPlaying.value) {
		// 继续播放
		if (audioPlayer.value) {
			audioPlayer.value.play()
				.then(() => {
					// 播放成功，启动进度定时器
					startProgressTimer()
				})
				.catch(error => {
					console.error('音频播放失败:', error)
					isPlayerPlaying.value = false
					isPlaying.value = false
					ElMessage.error('音频播放失败，请重试')
				})
		}
	} else {
		// 暂停播放
		if (audioPlayer.value) {
			audioPlayer.value.pause()
			// 暂停进度更新定时器
			clearProgressTimer()
		}
	}
}

/**
 * 格式化时间显示
 * @param {Number} seconds - 秒数
 * @return {String} 格式化后的时间字符串 (00:00:00)
 */
const formatTime = (seconds) => {
	const hours = Math.floor(seconds / 3600)
	const minutes = Math.floor((seconds % 3600) / 60)
	const secs = Math.floor(seconds % 60)
	
	return [
		hours.toString().padStart(2, '0'),
		minutes.toString().padStart(2, '0'),
		secs.toString().padStart(2, '0')
	].join(':')
}

// 计算当前播放时间显示
const timeDisplay = computed(() => {
	return `${formatTime(currentTime.value)}/${formatTime(audioDuration.value)}`
})

/**
 * 切换语调滑块的显示状态
 */
const togglePitchSlider = () => {
	pitchSliderVisible.value = !pitchSliderVisible.value
}

// 处理音乐对话框相关方法
const handleMusicDialogConfirm = () => {
	// 关闭对话框
	musicDialogVisible.value = false
	
	// 使用musicStore中的音乐列表更新本地音乐列表
	if (musicStore.musicList && musicStore.musicList.length > 0) {
		// 深拷贝音乐列表，防止直接引用
		musicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
		// ElMessage.success(`已选择${musicList.value.length}首音乐`)
	}
}

const handleMusicDialogRemove = (index) => {
	// 在MusicDialog组件中已经处理了从musicStore中移除的逻辑
	// 这里只需同步更新本地musicList
	musicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
}

const handleMusicDialogTogglePlay = (index) => {
	console.log(`切换播放音乐 ${index}`)
}

const handleMusicDialogAddMusic = () => {
	ElMessage.info('添加音乐')
}

/**
 * 根据筛选级别更新所有下级标签的显示状态
 * @param {Array} filteredResults - 筛选后的音色数据
 * @param {Number} currentLevel - 当前操作的级别
 */
const updateLowerLevelTags = (filteredResults, currentLevel) => {
	// 如果没有筛选结果，重置所有下级标签可见性
	if (!filteredResults || filteredResults.length === 0) {
		// 重置从当前级别+1到最后一级的所有标签
		for (let level = currentLevel + 1; level <= 6; level++) {
			resetTagsVisibilityByLevel(level)
		}
		return
	}

	// 根据当前级别处理不同的下级标签
	switch (currentLevel) {
		case 1: // 第一级是音色类型，更新2、3、4级
			// 处理第2级（性别/年龄）
			updateLevelTags(filteredResults, 2)
			// 同时处理第3级（场景分类）
			updateLevelTags(filteredResults, 3)
			// 同时处理第4级（推荐标签）
			updateLevelTags(filteredResults, 4)
			break

		case 2: // 第二级是性别，更新3、4级
			// 处理第3级（场景分类）
			updateLevelTags(filteredResults, 3)
			// 同时处理第4级（推荐标签）
			updateLevelTags(filteredResults, 4)
			break

		case 3: // 第三级是场景分类，更新第4级
			// 处理第4级（推荐标签）
			updateLevelTags(filteredResults, 4)
			break
	}
}

/**
 * 辅助函数：更新特定级别的标签
 * @param {Array} filteredResults - 筛选后的音色数据
 * @param {Number} level - 要更新的标签级别
 */
const updateLevelTags = (filteredResults, level) => {
	switch (level) {
		case 2: // 性别/年龄级别
			const existingGenders = new Set(['全部'])
			
			// 收集筛选结果中存在的性别
			filteredResults.forEach(voice => {
				if (voice.gender) {
					existingGenders.add(voice.gender)
				}
			})

			// 更新性别标签的可见性
			const genders = genderCategories.value
			genders.forEach(gender => {
				gender.visible = existingGenders.has(gender.name) || gender.id === 'all'
			})
			break

		case 3: // 场景分类级别
			const existingScenes = new Set(['全部'])
			
			// 收集筛选结果中存在的场景分类
			filteredResults.forEach(voice => {
				if (voice.sceneCategory) {
					existingScenes.add(voice.sceneCategory)
				}
			})

			// 更新场景标签的可见性
			const scenes = sceneCategories.value
			scenes.forEach(scene => {
				scene.visible = existingScenes.has(scene.name) || scene.id === 'all'
			})
			break

		case 4: // 推荐标签级别
			const existingTags = new Set(['全部'])
			
			// 收集筛选结果中存在的推荐标签
			filteredResults.forEach(voice => {
				if (voice.recommendTags) {
					const tags = typeof voice.recommendTags === 'string'
						? voice.recommendTags.split(',')
						: Array.isArray(voice.recommendTags)
							? voice.recommendTags
							: [voice.recommendTags]

					tags.forEach(tag => {
						if (tag && typeof tag === 'string') {
							existingTags.add(tag.trim())
						}
					})
				}
			})

			// 更新推荐标签的可见性
			const tags = recommendTagCategories.value
			tags.forEach(tag => {
				tag.visible = existingTags.has(tag.name) || tag.id === 'all'
			})
			break
	}
}

/**
 * 重置指定级别的标签为可见状态
 * @param {Number} level - 标签级别
 */
const resetTagsVisibilityByLevel = (level) => {
	switch (level) {
		case 1: // 音色类型级别
			mainCategories.value.forEach(item => item.visible = true)
			break
		case 2: // 性别级别
			genderCategories.value.forEach(item => item.visible = true)
			break
		case 3: // 场景分类级别
			sceneCategories.value.forEach(item => item.visible = true)
			break
		case 4: // 推荐标签级别
			recommendTagCategories.value.forEach(item => item.visible = true)
			break
		case 5: // 子分类级别
			subCategories.value.forEach(item => item.visible = true)
			break
		case 6: // 详细分类级别
			detailedCategories.value.forEach(item => item.visible = true)
			break
		default:
			// 重置所有级别
			resetAllTagsVisibility()
			break
	}
}

/**
 * 重置所有标签为可见状态
 */
const resetAllTagsVisibility = () => {
	for (let level = 1; level <= 6; level++) {
		resetTagsVisibilityByLevel(level)
	}
}
</script>

<style lang="scss" scoped>
.voice-over-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #EFEFF1;
	
	// 主要内容区域
	.main-content {
		flex: 1;
		display: flex;
		padding: 0 20px 20px 20px;
		background: #EFEFF1;

		// 左侧区域
		.left-section {
			display: flex;
			background: #fff;
			border-radius: 8px;
			margin-right: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
			height: calc(100vh - 120px); /* 修改：从auto改为固定高度 */
			max-height: calc(100vh - 120px);
			overflow: hidden;
			
			.voice-over-tools {
				flex: 1;
				width: 694px;
				padding: 20px;
				display: flex;
				flex-direction: column;
				height: 100%;
				overflow: hidden;
				
				.category-nav {
					// margin-bottom: 20px;
					
					.category-container {
						width: 100%;
						margin-left: 35px;
						
						.main-categories .category-item:first-child,
						.sub-categories .category-item:first-child,
						.detailed-categories .category-item:first-child {
							position: absolute;
							left: 0;
							margin-left: 0;
							padding-left: 0;
						}
					}
					
					.main-categories,
					.sub-categories,
					.detailed-categories {
						display: flex;
						flex-wrap: wrap;
						margin-bottom: 8px;
						padding-left: 0;
						position: relative;
						
						.category-item {
							padding: 4px 10px;
							font-size: 13px;
							color: #666;
							cursor: pointer;
							margin-right: 5px;
							
							&.active {
								color: #fb6d30;
								font-weight: 500;
							}
							
							&:hover {
								color: #fb6d30;
							}
						}
					}
					
					.category-container {
						width: 100%;
						margin-left: 35px;
						
						.main-categories .category-item:first-child,
						.sub-categories .category-item:first-child,
						.detailed-categories .category-item:first-child {
							position: absolute;
							left: -36px;
							margin-left: 0;
							padding-left: 0;
							
							&.active {
								color: #fb6d30;
								font-weight: 500;
							}
						}
					}
				}
				
				// 声音角色选择区域
				.voice-grid {
					flex: 1;
					overflow-y: auto;
					// padding-left: 10px;
					// margin-top: 10px;
					max-height: calc(100vh - 350px); /* 修改：调整最大高度，为分类和底部控制栏留出空间 */
					
					// 隐藏滚动条但保留滚动功能
					&::-webkit-scrollbar {
						display: none;
					}
					scrollbar-width: none; // Firefox
					-ms-overflow-style: none; // IE and Edge
					
					.voice-row {
						display: flex;
						flex-wrap: wrap;
						gap: 24px 36px;
					}
					
					.voice-item-wrapper {
						position: relative;
						width: 100px;
						height: 120px;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #F7F7F9;
						border-radius: 4px 40px 4px 4px;
						cursor: pointer;
						margin-bottom: 0;
						margin-right: 0;
						
						// 精品珍享图片定位 - 调整到边框外
						.position_image {
							position: absolute;
							top: -1px;
							left: -1px;
							overflow: hidden;
							z-index: 3;
							
							.premium-badge {
								width: 54px;
								height: 20px;
							}
						}
						
						// 内部卡片
						.voice-item {
							width: 96px;
							height: 116px;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							background-color: #F7F7F9;
							border-radius: 3px 38px 3px 3px;
							position: relative;
							z-index: 1;
						}
						
						// 活跃状态样式 - 简化边框处理
						&.active {
							background-image: linear-gradient(134deg, rgb(10, 175, 96), rgb(255, 214, 0));
							background-color: rgb(241, 251, 246);
							box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
							
							// 内部卡片在选中状态下的背景
							.voice-item {
								background-color: rgb(241, 251, 246);
							}
							
							// 移除多余的伪元素
							&::before {
								content: none;
							}
							
							&::after {
								content: none;
							}
							
							// 删除底部边框
							.voice-item::after {
								content: none;
							}
						}
					}
				}
				
				// 底部控制区
				.control-bar {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 30px 0;
					border-top: 1px solid #EBEEF5;
					width: 100%;
					background: #fff;
					
					.volume-control {
						display: flex;
						align-items: center;
						margin-left: 20px;
						
						.pitch-display {
							display: flex;
							align-items: center;
							justify-items: center;
							background-color: #F4F4F4;
							border-radius: 4px;
							padding: 6px 16px;
							cursor: pointer;
							height: 40px;
							
							.pitch-label {
								color: #999999;
								margin-right: 12px;
								font-size: 14px;
								line-height: 1;
								display: flex;
								align-items: center;
							}
							
							.pitch-value {
								color: #333333;
								font-size: 15px;
								font-weight: 500;
								line-height: 1;
								display: flex;
								align-items: center;
							}
						}
					}
					
					.action-buttons {
						display: flex;
						gap: 15px;
						margin-right: 20px;
						
						button {
							height: 40px;
							width: 90px;
							border-radius: 4px;
							font-size: 14px;
							cursor: pointer;
							transition: all 0.3s;
							
							&.listen-btn {
								background: #fff;
								color: #606266;
								border: 1px solid #DCDFE6;
								
								// 控制图标样式
								.control-icons {
									display: flex;
									align-items: center;
									justify-content: center;
									
									.pause-icon {
										display: inline-block;
										width: 12px;
										height: 14px;
										position: relative;
										
										&:before, &:after {
											content: '';
											position: absolute;
											width: 4px;
											height: 14px;
											background-color: #606266;
											border-radius: 2px;
										}
										
										&:before {
											left: 0;
										}
										
										&:after {
											right: 0;
										}
									}
									
									.divider {
										width: 1px;
										height: 14px;
										background-color: #DCDFE6;
										margin: 0 8px;
									}
									
									.stop-icon {
										display: inline-block;
										width: 12px;
										height: 12px;
										background-color: #606266;
										border-radius: 2px;
									}
								}
								
								&:hover {
									border-color: #606266;
									color: #606266;
								}
							}
							
							&.generate-btn {
								background: linear-gradient(90deg, #0AAF60 0%, #A4CB55 100%);
								color: #fff;
								border: none;
								font-weight: 500;
								
								&:hover {
									opacity: 0.9;
								}
								
								&:active {
									opacity: 0.8;
								}
							}
						}
					}
				}
			}
		}
	}
}

// 辅助类
.margin_t-4 {
	margin-top: 4px;
}
.margin_t-14 {
	margin-top: 14px;
}
.font-size-14 {
	font-size: 14px;
}
.flex {
	display: flex;
}
.flex_a_i-center {
	align-items: center;
}
.flex_j_c-center {
	justify-content: center;
}
.flex_d-column {
	flex-direction: column;
}
.width-20 {
	width: 20px;
}
.height-20 {
	height: 20px;
}
.overflow-hidden {
	overflow: hidden;
}

// 头像样式更新
.voice-avatar {
	position: relative;
	width: 56px;
	height: 56px;
	margin-top: 14px;
	
	.avatar-circle {
		display: block;
		width: 56px;
		height: 56px;
		border-radius: 50%; // 这会使容器变成圆形
		background-color: #fff;
		overflow: hidden;  // 这会裁剪超出圆形的部分
		
		img {
			width: 100%;
			height: 100%;
			object-fit: cover; // 这会确保图片适当缩放并覆盖整个圆形区域
		}
	}
	
	// 播放和暂停按钮
	.play-button, .pause-button {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 20px;
		height: 20px;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0; // 默认隐藏
		transition: all 0.3s ease;
		z-index: 5;
	}
}

// 悬停效果 - 当鼠标悬停在卡片上时显示播放按钮
.voice-item-wrapper:hover {
	.voice-avatar {
		.play-button, .pause-button {
			opacity: 1; // 鼠标悬停时显示播放/暂停按钮
		}
	}
}

// 当音频正在播放时，给声音卡片添加特殊样式
.voice-item-wrapper.playing {
	.voice-avatar {
		.pause-button {
			opacity: 1; // 播放状态下始终显示暂停按钮
		}
	}
	
	.voice-item {
		// 可以添加其他表示播放状态的样式
		// 例如边框闪烁或背景色变化
	}
}

// 添加辅助类
.width-30 {
	width: 30px;
}
.height-30 {
	height: 30px;
}

// 统一所有分类的样式
.main-categories {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 8px;
	padding-left: 0;
	position: relative;
	
	.category-item {
		padding: 4px 10px;
		font-size: 13px;
		color: #666;
		cursor: pointer;
		margin-right: 5px;
		
		&.active {
			color: #fb6d30;
			font-weight: 500;
		}
		
		&:hover {
			color: #fb6d30;
		}
	}
}

// 分类容器布局
.category-container {
	width: 100%;
	margin-left: 35px;
	
	.main-categories .category-item:first-child {
		position: absolute;
		left: -36px;
		margin-left: 0;
		padding-left: 0;
		
		&.active {
			color: #fb6d30;
			font-weight: 500;
		}
	}
}

// 音频播放器控件样式
.audio-player-wrapper {
	width: 100%;
	background: #F7F7F9;
	border-radius: 40px;
	padding: 10px;
}

.audio-player-control {
	display: flex;
	align-items: center;
	width: 100%;
	
	.player-button {
		width: 30px;
		height: 30px;
		background: #0AAF60;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10px;
		cursor: pointer;
		
		.player-icon {
			width: 0;
			height: 0;
			border-style: solid;
			border-width: 7px 0 7px 12px;
			border-color: transparent transparent transparent #fff;
		}
		
		.pause-icon {
			display: inline-block;
			width: 14px;
			height: 14px;
			position: relative;
			
			&:before, &:after {
				content: '';
				position: absolute;
				width: 4px;
				height: 14px;
				background-color: #fff;
				border-radius: 2px;
			}
			
			&:before {
				left: 2px;
			}
			
			&:after {
				right: 2px;
			}
		}
	}
	
	.progress-bar {
		flex: 1;
		height: 4px;
		background: #E6E6E6;
		border-radius: 2px;
		overflow: hidden;
		position: relative;
		
		.progress-filled {
			position: absolute;
			top: 0;
			left: 0;
			height: 100%;
			background: #0AAF60;
			border-radius: 2px;
		}
	}
	
	.player-time {
		margin-left: 15px;
		font-size: 14px;
		color: #606266;
		white-space: nowrap;
	}
}

/* 全局样式，不使用scoped */
.gradient-slider-popover {
	background-color: #000000 !important;
	opacity: 0.75 !important;
	border-radius: 8px !important;
	border: none !important;
	height: 38px !important;
	padding: 0 20px !important;
	display: flex !important;
	align-items: center !important;
}

.gradient-slider-popover .el-popover__title {
	color: #fff;
}

.gradient-slider-popover.el-popper[data-popper-placement^='top'],
.gradient-slider-popover.el-popper[data-popper-placement^='bottom'] {
	margin-bottom: 12px !important;
	transform: translateX(70px) !important; /* 向右移动弹出层，避免超出左边界 */
}

.gradient-slider-popover .el-popper__arrow {
	left: 70px !important; /* 调整箭头位置，与transform: translateX(70px)保持一致 */
}

/* 箭头伪元素样式穿透 */
.gradient-slider-popover .el-popper__arrow::before {
	background-color: #000000BF !important;
	border-color: #000000BF !important;
}

/* 箭头伪元素样式 - 更精确的选择器 */
.el-popper.gradient-slider-popover[data-popper-placement^=top] > .el-popper__arrow::before {
	background-color: #000000BF !important;
	border-color: #000000BF !important;
}

.el-popper.gradient-slider-popover[data-popper-placement^=bottom] > .el-popper__arrow::before {
	background-color: #000000BF !important;
	border-color: #000000BF !important;
}

/* 滑块容器样式 */
.speaker_content_bottom_left_speed_speech {
	width: 100%;
	padding: 0 5px;
	display: flex;
	align-items: center;
	justify-content: center;
	
	.flex-item_f-8 {
		flex: 8;
	}
	
	.margin_l-10 {
		margin-left: 10px;
		color: white;
		width: 30px;
		text-align: right;
	}
	
	.font-size-14 {
		font-size: 14px;
	}
}

/* 自定义滑块样式 */
.gradient-slider {
	:deep(.el-slider) {
		--el-slider-main-bg-color: #0AAF60;
		--el-slider-height: 4px;
		height: 20px;
		display: flex;
		align-items: center;
		position: relative;
		
		.el-slider__runway {
			background-color: rgba(255, 255, 255, 0.3);
			height: 4px;
			border-radius: 4px;
			position: relative;
			margin: 0 auto;
		}
		
		.el-slider__bar {
			background: linear-gradient(to right, #0AAF60, #A4CB55);
			height: 4px;
			border-radius: 4px;
			top: 0;
		}
		
		.el-slider__button-wrapper {
			top: -4px; /* 调整滑块按钮位置 */
			height: 12px;
			width: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.el-slider__button {
			width: 12px;
			height: 12px;
			border: 3px solid #0AAF60;
			background-color: #fff;
			box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
			margin: 0;
		}
		
		.el-slider__stop {
			width: 4px;
			height: 4px;
			border-radius: 50%;
			background-color: rgba(255, 255, 255, 0.5);
			transform: translateY(-50%);
			top: 50%;
		}
		
		/* 使用伪元素选择器，根据刻度点位置设置渐变色 */
		.el-slider__stop {
			&:nth-child(1) { background-color: #0AAF60; }
			&:nth-child(2) { background-color: #1FAF5F; }
			&:nth-child(3) { background-color: #39B25E; }
			&:nth-child(4) { background-color: #53B65D; }
			&:nth-child(5) { background-color: #6DBD5C; }
			&:nth-child(6) { background-color: #88C35A; }
			&:nth-child(7) { background-color: #A4CB55; }
		}
		
		/* 隐藏刻度标记文本 */
		.el-slider__marks-text {
			display: none;
		}
	}
}
</style>

<style>
/* 隐藏原生箭头并创建自定义箭头 */
body .el-popper.gradient-slider-popover .el-popper__arrow {
	visibility: hidden !important;
}

body .el-popper.gradient-slider-popover::after {
	content: '';
	position: absolute;
	width: 10px;
	height: 10px;
	background-color: #000000;
	opacity: 0.75;
	bottom: -5px;
	left: 85px !important; /* 使用固定位置并增加优先级 */
	transform: rotate(45deg);
	z-index: -1;
	transition: none !important; /* 禁用任何过渡效果 */
}
</style>