<template>
	<div class="ai-creation-container">
		<!-- AI创作类型选择区域 -->
		<div class="ai-types-grid" v-loading="loading" v-if="showTypeSelection">
			<div v-for="(item, index) in images" :key="index"
				:class="['ai-type-item', { active: selectedType === index }]"
				@click="selectType(index, item)">
				<div class="type-icon">
					<img :src="item.ossUrl" alt="icon">
				</div>
			</div>
		</div>

		<!-- 聊天消息展示区域 -->
		<div class="message-list" ref="messageList" v-if="!showTypeSelection && messages.length > 0">
			<div v-for="(msg, index) in messages" :key="index" class="message-item" :class="{
                'user-message': msg.isUser,
                'ai-message': !msg.isUser,
            }" @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = -1">
				<!-- 刷新加载状态 -->
				<div v-if="msg.isRefreshing" class="bubble thinking-bubble">
					<div class="thinking-text">
						<span>💭</span>
						<div class="thinking-wave">重新生成中...</div>
					</div>
					<span class="timestamp">🕒 {{ msg.refreshTime }}</span>
				</div>
				<div class="bubble" v-else>
					<div class="message-content">
						<span v-if="msg.isUser" class="user-text">{{ msg.content }}</span>
						<div v-else class="ai-text markdown-body" v-html="renderMarkdown(msg.isTyping ? msg.displayContent : msg.content)" @contextmenu="(e) => copyWithContext(index, e)"></div>
					</div>
					<!-- 只在AI消息显示操作按钮 -->
					<div v-if="!msg.isUser" class="action-buttons" :class="{
                        'always-visible':
                            msg.isNew ||
                            hoverIndex === index ||
                            isLatestAIMessage(index),
                    }">
						<div class="action-button-wrapper">
							<el-button link @click="copyText(msg.content)" @contextmenu.prevent="(e) => copyWithContext(index, e)" aria-label="复制">
								<el-icon>
									<DocumentCopy />
								</el-icon>
								<span class="button-text">复制</span>
							</el-button>
						</div>

						<div class="action-button-wrapper">
							<el-button link @click="refreshText(index, msg)" aria-label="刷新">
								<el-icon>
									<Refresh />
								</el-icon>
								<span class="button-text">刷新</span>
							</el-button>
						</div>

						<div class="action-button-wrapper">
							<el-button link @click="saveText(index)" aria-label="收藏">
								<el-icon>
									<Star />
								</el-icon>
								<span class="button-text">收藏</span>
							</el-button>
						</div>

						<div class="action-button-wrapper">
							<el-button link @click="deleteText(index)" aria-label="删除">
								<el-icon>
									<Delete />
								</el-icon>
								<span class="button-text">删除</span>
							</el-button>
						</div>

						<div class="action-button-wrapper">
							<el-button link @click="extractText(index)" aria-label="提取使用">
								<el-icon>
									<FolderChecked />
								</el-icon>
								<span class="button-text">提取使用</span>
							</el-button>
						</div>
					</div>
					<span class="timestamp">🕒 {{ msg.time }}</span>
				</div>
			</div>

			<div v-if="isThinking" class="message-item">
				<div class="bubble thinking-bubble">
					<div class="thinking-text">
						<span>💭</span>
						<div class="thinking-wave">正在思考中</div>
					</div>
					<span class="timestamp">🕒 {{ getCurrentTime() }}</span>
				</div>
			</div>
		</div>

		<!-- 空聊天提示 -->
		<div class="empty-chat" v-if="!showTypeSelection && messages.length === 0">
			<div class="empty-message">
				<p>请输入提示词开始创作</p>
			</div>
		</div>

		<!-- 输入区域 -->
		<div class="input-footer">
			<!-- 将切换按钮移到这里，成为input-footer的第一个子元素 -->
			<div class="mode-toggle-btn-container">
				<el-button type="primary" plain size="small" @click="toggleMode">
					<img class="toggle-icon" src="@/assets/img/expand-diagonal-fill.png" alt="切换" />
					{{ showTypeSelection ? '进入聊天页面' : '切换创作模板' }}
				</el-button>
			</div>

			<div class="custom-input-container">
				<!-- 高亮输入框，contenteditable 替换 el-input -->
				<div
					class="custom-input"
					contenteditable="true"
					ref="inputRef"
					:placeholder="`请输入${currentTypeName}创作提示...`"
					@input="handleInput"
					@keydown.enter="handleKeydown"
					@paste="handlePaste"
					@focus="handleInputFocus"
					@blur="handleInputBlur"
					@compositionstart="handleCompositionStart"
					@compositionend="handleCompositionEnd"
					:style="{minHeight: '70px'}"
				></div>
				<div class="action-buttons">
					<div class="button-group">
						<el-button @click="sendMessage" link class="icon-btn" circle>
							<img class="icon-img" src="@/assets/img/send-plane-fill.png" alt="发送" />
						</el-button>
					</div>
				</div>
			</div>
		</div>

		<!-- 保存到我的空间 -->
		<SubtitleDialog v-model="showSaveDialog" :material-info="materialInfo" :sourcePage="'ai-tool'" @confirm="saveToFavorites" />
		
		<!-- 添加会员限制弹窗 -->
		<AlertDialog 
			v-model:visible="showLimitDialog"
			type="warning"
			title="会员功能"
			message="非会员每日只能使用15次，请开通会员使用"
			confirm-button-text="开通会员"
			cancel-button-text="我知道了"
			:show-cancel-button="true"
			:custom-confirm-class="true"
			:custom-cancel-class="true"
			:show-fee-explanation="false"
			@confirm="handleOpenMember"
			@cancel="handleCloseLimitDialog"
		/>
	</div>
	<div class="ai-disclaimer">内容由AI生成，请仔细甄别</div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, computed, onUnmounted, getCurrentInstance } from "vue";
import { marked } from 'marked';
import { ElMessage, ElLoading } from "element-plus";
import { questionAPl, getChatRecordApi, creationList } from "@/api/creation";
import { saveFullMaterial } from "@/api/myMaterial";
import { DocumentCopy, Refresh, Star, Delete, FolderChecked} from "@element-plus/icons-vue";
import SubtitleDialog from "@/components/SubtitleDialog/index.vue"; // 请确保路径正确
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; // 代码高亮样式
import { useMessageStore } from '@/stores/message'
import { useloginStore } from '@/stores/login' // 导入用户store
import { useRouter } from 'vue-router'
import { formatTextForHTML } from '@/utils/textFormat'; // 导入格式化函数

// 定义Props
const props = defineProps({
	// 声明组件接收的属性
	isUploading: Boolean,
	isExtracting: Boolean,
	isButtonExtracting: Boolean,
	uploadFile: [Function, Object],
	showExtractResult: Boolean,
	extractedVideoUrl: String,
	isVideoEditingPage: Boolean
})

// 修改emit声明，包含所有需要的事件
const emit = defineEmits([
	"back",
	"extract-content", 
	"extract", 
	"upload-file", 
	"cancel-upload", 
	"download-video", 
	"add-to-space"
]);

// 状态变量
const selectedType = ref(0);
const inputText = ref("");
const messages = ref([]);
const isThinking = ref(false);
const hoverIndex = ref(-1);
const showSaveDialog = ref(false);
const materialInfo = ref({});
const messageList = ref(null);
const images = ref([]);
const loading = ref(true);
const currentTemplate = ref(null);
const showTypeSelection = ref(true); // 新增：控制显示模式的状态
const showLimitDialog = ref(false); // 会员限制弹窗显示状态
const inputRef = ref(null); // 输入框引用
const formattedText = ref(""); // 格式化后的文本
const isComposing = ref(false);

// 在 script setup 中添加
const messageStore = useMessageStore()
// 获取用户store
const loginStore = useloginStore()
// 获取组件实例，用于访问$modal
const { proxy } = getCurrentInstance();

// 获取路由实例
const router = useRouter()

// 添加打字效果定时器引用
const typingTimer = ref(null);

// 从模板获取当前类型名称
const currentTypeName = computed(() => {
	if (selectedType.value === -1) {
		return '内容'; // 未选中任何类型时的默认文本
	}
	if (images.value.length > 0 && selectedType.value < images.value.length) {
		return images.value[selectedType.value].templateName || '内容';
	}
	return '内容';
});

// 从HomePage.vue移植的获取列表数据方法
const fetchCreationList = async () => {
	try {
		loading.value = true;
		const res = await Promise.race([
			creationList({in_use:'2'}),
			new Promise((_, reject) =>
				setTimeout(() => reject(new Error('请求超时')), 10000)
			)
		]);

		if (res.length > 0) {
			images.value = res;
			// 不再默认选中第一项
			// 不再默认填充输入框
		}
	} catch (error) {
		console.error('获取列表失败:', error);
		ElMessage.error('获取列表失败，请稍后重试');
	} finally {
		loading.value = false;
	}
};

// 从ChatPage移植的方法
// 配置 marked
marked.setOptions({
	highlight: function (code, lang) {
		if (lang && hljs.getLanguage(lang)) {
			return hljs.highlight(code, { language: lang }).value;
		}
		return hljs.highlightAuto(code).value;
	},
	breaks: true,
	gfm: true,
});

// Markdown 渲染函数
const renderMarkdown = (content) => {
	try {
		return marked(content);
	} catch (e) {
		console.error('Markdown 渲染错误:', e);
		return content;
	}
};

// 判断是否是最新的AI消息
const isLatestAIMessage = (index) => {
	return (
		index === messages.value.length - 1 &&
		!messages.value[index].isUser &&
		messages.value.some((msg) => msg.isUser)
	);
};

// 获取当前时间
const getCurrentTime = () =>
	new Date().toLocaleTimeString("zh-CN", {
		hour: "2-digit",
		minute: "2-digit",
	});

// 添加一个工具函数用于移除颜文字
const removeEmojisAndSpecialSymbols = (text) => {
	if (!text) return '';
	
	// 这个正则表达式会匹配emoji和大多数特殊符号
	// 保留中文、英文、数字、常用标点符号和基本排版符号
	return text.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[\u2600-\u27FF]|[\u2300-\u23FF]|[\u2B50-\u2BFF]|[\u231A-\u231B]|[\u23E9-\u23EC]|[\u23F0-\u23F3]|[\u23F8-\u23FA]|[\u25AA-\u25AB]|[\u25FB-\u25FE]|[\u2600-\u2604]|[\u260E-\u260F]|[\u2611]|[\u2614-\u2615]|[\u261D]|[\u263A]|[\u2648-\u2653]|[\u2660-\u2667]|[\u2668]|[\u267B]|[\u267F]|[\u2693]|[\u26A0-\u26A1]|[\u26AA-\u26AB]|[\u26BD-\u26BE]|[\u26C4-\u26C5]|[\u26CE]|[\u26D4]|[\u26EA]|[\u26F2-\u26F3]|[\u26F5]|[\u26FA]|[\u26FD]|[\u2702]|[\u2705]|[\u2708-\u270D]|[\u270F]|[\u2712]|[\u2714]|[\u2716]|[\u271D]|[\u2721]|[\u2728]|[\u2733-\u2734]|[\u2744]|[\u2747]|[\u274C]|[\u274E]|[\u2753-\u2755]|[\u2757]|[\u2763-\u2764]|[\u2795-\u2797]|[\u27A1]|[\u27B0]|[\u27BF]|[\u2934-\u2935]|[\u2B05-\u2B07]|[\u2B1B-\u2B1C]|[\u2B50]|[\u2B55]|[\u3030]|[\u303D]|[\u3297]|[\u3299]|[\uFE0F]|[\u200D]|[\u1F000-\u1FFFF]|[\u1F300-\u1F5FF]|[\u1F600-\u1F64F]|[\u1F680-\u1F6FF]|[\u1F700-\u1F77F]|[\u1F780-\u1F7FF]|[\u1F800-\u1F8FF]|[\u1F900-\u1F9FF]|[\u1FA00-\u1FA6F]|[\u1FA70-\u1FAFF]|[\u2708\uFE0F]|[\u1F9F3]|[\u2728]|[\u2B50]|[\u2600-\u26FF]|\u2728|\u2B50/g, '');
}

// 复制文本
const copyText = (text) => {
	// 先将Markdown转换为纯文本并过滤颜文字
	const plainText = removeEmojisAndSpecialSymbols(convertMarkdownToPlainText(text));

	// 尝试使用现代 Clipboard API
	if (navigator.clipboard && window.isSecureContext) {
		navigator.clipboard
			.writeText(plainText)
			.then(() => {
				// ElMessage.success("复制成功");
			})
			.catch(() => {
				fallbackCopyTextToClipboard(plainText);
			});
	} else {
		fallbackCopyTextToClipboard(plainText);
	}
};

// 添加复制菜单函数
const copyWithContext = (index, e) => {
	e.preventDefault();
	e.stopPropagation();
	const msg = messages.value[index];
	
	// 创建上下文菜单
	const menu = document.createElement('div');
	menu.className = 'copy-context-menu';
	menu.style.position = 'fixed';
	menu.style.top = e.clientY + 'px';
	menu.style.left = e.clientX + 'px';
	menu.style.zIndex = '10000';
	menu.style.background = 'white';
	menu.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
	menu.style.borderRadius = '4px';
	menu.style.padding = '8px 0';
	
	// 菜单选项1：复制为纯文本
	const plainTextOption = document.createElement('div');
	plainTextOption.innerText = '复制为纯文本';
	plainTextOption.style.padding = '8px 16px';
	plainTextOption.style.cursor = 'pointer';
	plainTextOption.style.fontSize = '14px';
	plainTextOption.onmouseover = () => {
		plainTextOption.style.backgroundColor = '#f5f7fa';
	};
	plainTextOption.onmouseout = () => {
		plainTextOption.style.backgroundColor = 'transparent';
	};
	plainTextOption.onclick = () => {
		// convertMarkdownToPlainText已经包含了颜文字过滤
		const plainText = convertMarkdownToPlainText(msg.content);
		copyToClipboard(plainText);
		document.body.removeChild(menu);
	};
	
	// 菜单选项2：复制原始格式
	const originalOption = document.createElement('div');
	originalOption.innerText = '复制原始内容';
	originalOption.style.padding = '8px 16px';
	originalOption.style.cursor = 'pointer';
	originalOption.style.fontSize = '14px';
	originalOption.onmouseover = () => {
		originalOption.style.backgroundColor = '#f5f7fa';
	};
	originalOption.onmouseout = () => {
		originalOption.style.backgroundColor = 'transparent';
	};
	originalOption.onclick = () => {
		// 原始内容也需要过滤颜文字
		copyToClipboard(removeEmojisAndSpecialSymbols(msg.content));
		document.body.removeChild(menu);
	};
	
	// 添加选项到菜单
	menu.appendChild(plainTextOption);
	menu.appendChild(originalOption);
	document.body.appendChild(menu);
	
	// 点击页面其他区域关闭菜单
	const closeMenu = (e) => {
		if (!menu.contains(e.target)) {
			document.body.removeChild(menu);
			document.removeEventListener('mousedown', closeMenu);
		}
	};
	setTimeout(() => {
		document.addEventListener('mousedown', closeMenu);
	}, 100);
};

// 通用复制到剪贴板函数
const copyToClipboard = (text) => {
	// 确保复制前过滤颜文字
	const filteredText = removeEmojisAndSpecialSymbols(text);
	
	if (navigator.clipboard && window.isSecureContext) {
		navigator.clipboard
			.writeText(filteredText)
			.then(() => {
				// ElMessage.success("复制成功");
			})
			.catch(() => {
				fallbackCopyTextToClipboard(filteredText);
			});
	} else {
		fallbackCopyTextToClipboard(filteredText);
	}
};

// 添加Markdown转换为纯文本的函数
const convertMarkdownToPlainText = (markdown) => {
	try {
		// 先使用marked将Markdown转换为HTML
		const html = marked(markdown);
		
		// 然后使用DOM API从HTML提取纯文本
		const temp = document.createElement('div');
		temp.innerHTML = html;
		// 提取纯文本后，过滤掉所有颜文字
		return removeEmojisAndSpecialSymbols(temp.textContent || temp.innerText || markdown);
	} catch (error) {
		console.error('转换Markdown失败:', error);
		// 出错时也应用过滤
		return removeEmojisAndSpecialSymbols(markdown);
	}
};

// 传统复制方法
const fallbackCopyTextToClipboard = (text) => {
	try {
		// 确保复制的文本已过滤颜文字
		const filteredText = removeEmojisAndSpecialSymbols(text);
		
		const textArea = document.createElement("textarea");
		textArea.value = filteredText;
		textArea.style.position = "fixed";
		textArea.style.top = "0";
		textArea.style.left = "0";
		textArea.style.width = "2em";
		textArea.style.height = "2em";
		textArea.style.padding = "0";
		textArea.style.border = "none";
		textArea.style.outline = "none";
		textArea.style.boxShadow = "none";
		textArea.style.background = "transparent";

		document.body.appendChild(textArea);
		textArea.focus();
		textArea.select();

		const successful = document.execCommand("copy");
		document.body.removeChild(textArea);

		if (successful) {
			// ElMessage.success("复制成功");
		} else {
			ElMessage.error("复制失败");
		}
	} catch (err) {
		ElMessage.error("复制失败");
		console.error("复制失败:", err);
	}
};

// 删除消息
const deleteText = (index) => {
	messages.value.splice(index, 1);
};

// 修改提取文本的方法
const extractText = (index) => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}

	const msg = messages.value[index];
	if (!msg || msg.isUser) return;
	
	// 移除颜文字后的内容
	const filteredContent = removeEmojisAndSpecialSymbols(msg.content);
	
	// 使用 messageStore 添加内容
	messageStore.addBotMessage({
		content: {
			result: {
				ai: filteredContent
			}
		}
	});
	
	// 同时通过事件发送内容给父组件
	emit('extract-content', filteredContent);
	
	// 显示成功提示
	// ElMessage.success("内容已添加到编辑器");
};

// 点击收藏按钮
const saveText = (index) => {
	const msg = messages.value[index];
	// 组装参数对象
	const info = {
		textContent: msg.content, // 必选
		materialName: msg.content.slice(0, 10), // 必选，前10字
		userId: getUserId(), // 必选
		materialType: 'text', // 必选，文案写死为'text'
		duration: msg.content.length, // 字数
		fileSize: 1024 * 1024, // 1MB = 1024 * 1024 bytes
		// 其余可选字段可按需补充
	};
	materialInfo.value = info;
	showSaveDialog.value = true;
};

// 保存到收藏夹
const saveToFavorites = async (params) => {
	// params为完整参数对象，含tagId
	// 校验必选字段
	const required = ['textContent', 'materialName', 'userId', 'materialType', 'tagId'];
	for (const key of required) {
		if (!params[key]) {
			ElMessage.error(`${key}为必填项`);
			return;
		}
	}
	// 调用保存接口
	try {
		const res = await saveFullMaterial(params);
		ElMessage.success("收藏成功");
	} catch (error) {
		ElMessage.error("收藏失败");
		console.error("收藏失败:", error);
	}
};

// 刷新文本
const refreshText = async (index, msg) => {
	const originalContent = msg.content;
	const originalTime = msg.time;

	// 立即清空内容并进入加载状态
	messages.value[index] = {
		...msg,
		content: "",
		isRefreshing: true,
		refreshTime: getCurrentTime(),
		originalContent,
		originalTime,
	};

	try {
		// 调用API获取新内容
		const res = await questionAPl({
			userId: getUserId(),
			question: messages.value.find(m => m.isUser)?.content || "请创建内容"
		});

		// 获取新内容
		const newContent = res.content.result.ai;
		
		// 更新消息对象，准备打字效果
		messages.value[index] = {
			...msg,
			content: newContent, // 存储完整内容
			displayContent: "", // 用于显示的内容初始为空
			isRefreshing: false,
			isTyping: true, // 标记为打字中
			time: getCurrentTime(),
		};
		
		// 启动打字效果
		startTypingEffect(index, newContent);
	} catch (error) {
		// 恢复原始内容
		messages.value[index] = {
			...msg,
			content: originalContent,
			time: originalTime,
			isRefreshing: false,
		};
		ElMessage.error("刷新失败");
	}
};

// 跳转到历史记录
const goToHistory = () => {
	// 实现跳转到历史记录的逻辑
	ElMessage.info("跳转到历史记录");
};

// 切换显示模式
const toggleMode = () => {
	showTypeSelection.value = !showTypeSelection.value;
	showLimitDialog.value = false; // 重置会员限制弹窗状态
};

// 修改选择AI类型的方法
const selectType = async (index, item) => {
	selectedType.value = index;
	currentTemplate.value = item;
	// 自动填充主题到输入框，并同步高亮
	if (item && item.theme) {
		inputText.value = item.theme;
		handleInputChange();
		inputRef.value && inputRef.value.focus();
	}
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		proxy.$modal.open('组合式标题');
		return;
	}
	try {
		const res = await questionAPl({
			userId: getUserId(),
			question: "测试会员限制状态",
		});
		// if (res.status_code === 310) {
		//	showLimitDialog.value = true;
		//	return;
		// }
		// 不再自动切换到聊天界面
	} catch (error) {
		// if (error && error.response && error.response.status_code === 310) {
		//	showLimitDialog.value = true;
		//	return;
		// }
		ElMessage.error("检查会员状态失败，但仍将继续");
		// 不再自动切换到聊天界面
	}
};

// 处理回车键
const handleKeydown = (event) => {
	if (!event.shiftKey) {
		event.preventDefault();
		sendMessage();
	}
};

// 发送消息
const sendMessage = async () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}

	if (!inputText.value || !inputText.value.trim()) {
		ElMessage.error("请输入创作提示");
		return;
	}

	// 先不切换到聊天界面，等待API响应后再决定
	// 添加用户消息
	const userMsg = {
		content: inputText.value,
		time: getCurrentTime(),
		isUser: true,
		isNew: true,
		isThinking: true,
	};

	// 添加思考状态
	isThinking.value = true;
	messages.value.push(userMsg);
	
	// 保存当前输入内容
	const currentInput = inputText.value;
	
	// 立即清空输入区域
	inputText.value = "";

	// 显示全局loading
	const loadingInstance = ElLoading.service({
		lock: true,
		text: '文案创作中...',
		background: 'rgba(255, 255, 255, 0.7)'
	});

	try {
		// 准备历史记录
		let questionWithHistory = currentInput;
		if (messages.value && messages.value.length > 1) {
			const historyText = messages.value.slice(0, -1).map(msg => {
				if (!msg) return '';
				return `${msg.isUser ? 'user：' : 'ai：'}${msg.content || ''}`;
			}).join('\n');
			questionWithHistory = `${historyText}\nuser：${currentInput}`;
		}
		const userId = getUserId() || '';
		const res = await questionAPl({
			userId: userId,
			question: questionWithHistory,
		});
		isThinking.value = false;
		// if (res.status_code === 310) {
		//	showLimitDialog.value = true;
		//	loadingInstance.close();
		//	return;
		// }
		// 只有在状态码不是310时，才确保切换到聊天界面
		if (res.status_code === 200) {
			showTypeSelection.value = false;
		}
		// 添加AI响应，增加打字效果相关字段
		const aiContent = res.content.result.ai;
		const aiResponse = {
			content: aiContent, // 存储完整内容，用于复制和其他操作
			displayContent: "", // 用于显示的逐步增加的内容
			time: getCurrentTime(),
			isUser: false,
			isNew: true,
			isThinking: false,
			isTyping: true, // 标记正在打字中
		};
		messages.value.push(aiResponse);
		startTypingEffect(messages.value.length - 1, aiContent);
		setTimeout(() => {
			messages.value.forEach((msg) => (msg.isNew = false));
			scrollToBottom();
		}, 1000);
	} catch (error) {
		isThinking.value = false;
		// if (error && error.response && error.response.status_code === 310) {
		//	showLimitDialog.value = true;
		//	loadingInstance.close();
		//	return;
		// }
		ElMessage.error("生成失败，请稍后重试");
		console.error("生成失败:", error);
	} finally {
		loadingInstance.close();
	}
};

// 添加打字效果函数
const startTypingEffect = (messageIndex, fullText) => {
	// 确保不超出数组范围
	if (messageIndex < 0 || messageIndex >= messages.value.length) {
		return;
	}
	
	// 初始化显示内容为空
	messages.value[messageIndex].displayContent = "";
	
	// 字符计数器
	let charIndex = 0;
	// 字符总数
	const totalChars = fullText.length;
	// 随机打字速度 - 每个字符25-40毫秒
	
	// 清除可能存在的旧定时器
	if (typingTimer.value) {
		clearInterval(typingTimer.value);
	}
	
	// 设置定时器逐字显示
	typingTimer.value = setInterval(() => {
		// 每次显示一个新字符
		charIndex++;
		
		// 更新显示内容
		messages.value[messageIndex].displayContent = fullText.substring(0, charIndex);
		
		// 自动滚动到底部，保持最新消息可见
		scrollToBottom();
		
		// 所有字符显示完成后清除定时器
		if (charIndex >= totalChars) {
			clearInterval(typingTimer.value);
			messages.value[messageIndex].isTyping = false; // 打字效果结束
			typingTimer.value = null;
		}
	}, 80 + Math.random() * 40); // 调整为更慢的速度：80-120毫秒
};

// 滚动到底部
const scrollToBottom = () => {
	if (messageList.value) {
		messageList.value.scrollTo({
			top: messageList.value.scrollHeight,
			behavior: "smooth",
		});
	}
};

// 监听消息变化自动滚动
watch(
	() => messages.value.length,
	async () => {
		await nextTick();
		scrollToBottom();
	},
	{ deep: true }
);

// 组件挂载时
onMounted(async () => {
	await fetchCreationList(); // 获取AI创作类型列表
	// 重置组件状态
	resetCreationState();
	// 同步输入框滚动到格式化层
	nextTick(() => {
		const inputDom = document.querySelector('.custom-input textarea');
		const formattedDom = document.querySelector('.formatted-text');
		if (inputDom && formattedDom) {
			inputDom.addEventListener('scroll', () => {
				formattedDom.scrollTop = inputDom.scrollTop;
			});
		}
	});
});

// 修改重置状态的函数
const resetCreationState = () => {
	// 清空选中类型
	selectedType.value = -1; // 使用-1表示未选择任何类型
	// 清空输入框
	inputText.value = "";
	// 清空消息记录
	messages.value = [];
	// 重置思考状态
	isThinking.value = false;
	// 重置当前模板
	currentTemplate.value = null;
	// 默认显示类型选择
	showTypeSelection.value = true;
	// 重置会员限制弹窗状态
	showLimitDialog.value = false;
};

// 组件卸载时清理定时器
onUnmounted(() => {
	if (typingTimer.value) {
		clearInterval(typingTimer.value);
		typingTimer.value = null;
	}
});

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}

// 判断用户是否已登录
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;
	
	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

// 添加会员限制弹窗相关方法
const handleOpenMember = () => {
	// 关闭弹窗
	showLimitDialog.value = false;
	
	// 导航到会员页面
	try {
		// 判断是否在layout布局内
		if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
			// 如果在layout布局内，使用内部路由导航
			router.push({ name: 'membership-nav' });
		} else {
			// 否则通过URL跳转
			window.location.href = '/membership';
		}
		ElMessage.success("正在跳转到会员页面");
	} catch (error) {
		console.error("导航到会员页面失败:", error);
		ElMessage.error("导航到会员页面失败，请手动前往会员中心");
	}
};

const handleCloseLimitDialog = () => {
	// 关闭会员限制弹窗
	showLimitDialog.value = false;
};

// 处理输入变化
const handleInputChange = () => {
    if (inputText.value) {
        formattedText.value = formatTextForHTML(inputText.value);
    } else {
        formattedText.value = "";
    }
};

// 输入框获得焦点时处理
const handleInputFocus = () => {
    // 可扩展逻辑
};

// 点击格式化文本区域时聚焦输入框
const focusInput = () => {
    inputRef.value && inputRef.value.focus();
};

// 新增事件处理和同步逻辑
const handleInput = (e) => {
    if (isComposing.value) return; // 输入法组合期间不做高亮渲染
    const editableDiv = e.target;
    // 获取当前光标位置（纯文本）
    const caretPos = getCaretPosition(editableDiv);
    // 获取纯文本内容
    const text = editableDiv.innerText;
    inputText.value = text;
    // 用高亮HTML渲染
    const html = formatTextForHTML(text);
    // 只有内容变化时才重渲染，避免死循环
    if (editableDiv.innerHTML !== html) {
        editableDiv.innerHTML = html;
        // 恢复光标
        setCaretPosition(editableDiv, caretPos);
    }
};

const handlePaste = (e) => {
    e.preventDefault();
    const text = (e.clipboardData || window.clipboardData).getData('text');
    document.execCommand('insertText', false, text);
};

// 保证外部赋值时也能高亮
watch(inputText, (val) => {
    const editableDiv = inputRef.value;
    if (editableDiv && editableDiv.innerText !== val) {
        const html = formatTextForHTML(val);
        editableDiv.innerHTML = html;
    }
});

// 工具函数：获取光标位置
function getCaretPosition(editableDiv) {
    let caretOffset = 0;
    const doc = editableDiv.ownerDocument || editableDiv.document;
    const win = doc.defaultView || doc.parentWindow;
    let sel;
    if (typeof win.getSelection != "undefined") {
        sel = win.getSelection();
        if (sel.rangeCount > 0) {
            const range = win.getSelection().getRangeAt(0);
            const preCaretRange = range.cloneRange();
            preCaretRange.selectNodeContents(editableDiv);
            preCaretRange.setEnd(range.endContainer, range.endOffset);
            caretOffset = preCaretRange.toString().length;
        }
    }
    return caretOffset;
}

// 工具函数：设置光标位置
function setCaretPosition(editableDiv, chars) {
    if (chars >= 0) {
        const selection = window.getSelection();
        let nodeStack = [editableDiv], node, foundStart = false, charCount = 0, stop = false;
        while (!stop && (node = nodeStack.pop())) {
            if (node.nodeType === 3) { // text node
                let nextCharCount = charCount + node.length;
                if (!foundStart && chars <= nextCharCount) {
                    const range = document.createRange();
                    range.setStart(node, chars - charCount);
                    range.collapse(true);
                    selection.removeAllRanges();
                    selection.addRange(range);
                    stop = true;
                }
                charCount = nextCharCount;
            } else {
                let i = node.childNodes.length;
                while (i--) {
                    nodeStack.push(node.childNodes[i]);
                }
            }
        }
    }
}

const handleInputBlur = (e) => {
    // 可扩展逻辑
};

const handleCompositionStart = () => {
    isComposing.value = true;
};
const handleCompositionEnd = (e) => {
    isComposing.value = false;
    // 组合输入结束后，手动触发一次高亮渲染
    handleInput(e);
};
</script>

<style lang="scss" scoped>
.ai-creation-container {
	padding: 15px 0;
	display: flex;
	flex-direction: column;
	height: 100%;
	position: relative;
	overflow: hidden;
}

.message-list {
	flex: 1;
	overflow-y: auto;
	padding: 0 10px;
	position: absolute;
	top: 15px;
	left: 0;
	right: 0;
	bottom: 195px; // 调整为195px，适应输入框的上移
	
	/* 隐藏滚动条 */
	&::-webkit-scrollbar {
		width: 0 !important;
		display: none !important;
	}
	
	/* Firefox */
	scrollbar-width: none !important;
	
	/* IE, Edge */
	-ms-overflow-style: none !important;
}

.empty-chat {
	position: absolute;
	top: 15px;
	left: 0;
	right: 0;
	bottom: 195px; // 同样调整为195px
	display: flex;
	justify-content: center;
	align-items: center;
	color: #909399;
}

.input-footer {
	position: absolute;
	bottom: 15px; // 从0改为15px，让输入框向上移动
	left: 0;
	right: 0;
	background: white;
	padding: 15px 25px 0;
	z-index: 10;
	border-top: 1px solid #eee;
	height: 180px; // 固定输入区域高度
}

// AI类型网格样式
.ai-types-grid {
	display: grid;
	grid-template-columns: repeat(3, 140px);
	justify-content: center;
	gap: 15px;
	margin-bottom: 15px;

	.ai-type-item {
		width: 140px;
		height: 64px;
		padding: 0;
		background: #FFFFFF;
		border: 1px solid #EBEEF5;
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.3s;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;

		&:hover {
			border-color: #8A70F0;
			transform: translateY(-2px);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		}

		&.active {
			border-color: #8A70F0;
			background: rgba(138, 112, 240, 0.05);
			box-shadow: 0 2px 10px rgba(138, 112, 240, 0.15);
		}

		.type-icon {
			width: 140px;
			height: 64px;
			margin-right: 0;
			overflow: hidden;
			
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}
}

// 聊天消息列表样式
.message-item {
	display: flex;
	flex-direction: column;
	margin-bottom: 20px;
	animation: fadeIn 0.3s ease;

	.bubble {
		position: relative;
		padding: 12px 15px;
		border-radius: 12px;
		background-color: #fff;
		border: 1px solid #e4e7ed;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
		max-width: 80%;
		transition: all 0.3s;

		.message-content {
			position: relative;
			z-index: 1;
			word-break: break-word;
		}

		.thinking-bubble {
			background: #f5f7fa !important;
			border-color: #e4e7ed !important;
			position: relative;
			overflow: hidden;
			
			&::before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg,
						rgba(255, 255, 255, 0) 0%,
						rgba(255, 255, 255, 0.6) 50%,
						rgba(255, 255, 255, 0) 100%);
				animation: wave 1.5s infinite;
			}
		}

		.thinking-text {
			display: flex;
			align-items: center;
			gap: 8px;
			position: relative;
			z-index: 1;

			span {
				font-size: 1.2em;
				animation: float 2s ease-in-out infinite;
			}
		}

		.thinking-wave {
			position: relative;
			display: inline-block;
			color: #606266;

			&::after {
				content: "...";
				animation: dots 1.5s infinite;
			}
		}
	}

	&.user-message .bubble {
		font-size: 14px;
		margin-left: auto;
		background: #1890FF33;
		color: #333;
		border: none;
		border-radius: 18px 0 18px 18px;

		.timestamp {
			color: #666;
		}

		.user-text {
			margin: 10px 0;
			display: inline-block;
			line-height: 1.8;
		}
	}

	&.ai-message .bubble {
		font-size: 16px;
		padding: 10px;
		border-radius: 12px;
		position: relative;
		max-width: 100%;
		width: 100%;
		align-self: flex-start;

		.ai-text {
			margin: 10px 0;
			display: inline-block;
			line-height: 1.6;
			width: 100%;
		}

		.markdown-body {
			font-size: 14px;
			line-height: 1.6;
			
			:deep(p) {
				margin: 8px 0;
			}
			
			:deep(pre) {
				background-color: #f6f8fa;
				border-radius: 6px;
				padding: 16px;
				overflow: auto;
				font-size: 85%;
				line-height: 1.45;
				margin: 12px 0;
			}
			
			:deep(code) {
				background-color: rgba(175, 184, 193, 0.2);
				border-radius: 6px;
				padding: 0.2em 0.4em;
				font-size: 85%;
				font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
			}
			
			:deep(ul), :deep(ol) {
				padding-left: 2em;
				margin: 8px 0;
			}
			
			:deep(blockquote) {
				padding: 0 1em;
				color: #656d76;
				border-left: 0.25em solid #d0d7de;
				margin: 12px 0;
			}
			
			:deep(table) {
				border-collapse: collapse;
				width: 100%;
				margin: 12px 0;
				
				th, td {
					border: 1px solid #d0d7de;
					padding: 6px 13px;
				}
				
				tr:nth-child(2n) {
					background-color: #f6f8fa;
				}
			}
		}

		.action-buttons {
			position: absolute;
			left: 10px;
			bottom: -28px;
			display: flex;
			gap: 8px;
			opacity: 0;
			transition: opacity 0.3s;
			padding: 4px 8px;
			border-radius: 24px;

			&.always-visible,
			.message-item:hover & {
				opacity: 1;
				visibility: visible;
			}

			.action-button-wrapper {
				position: relative;
				
				.el-button {
					padding: 2px;
					color: #666 !important;

					&:hover {
						color: #333 !important;
						background: #f5f5f5;
					}

					&:hover + .button-text {
						opacity: 1;
						visibility: visible;
						transform: translateY(0);
					}
				}

				.button-text {
					position: absolute;
					left: 50%;
					bottom: -25px;
					transform: translateX(-50%) translateY(5px);
					font-size: 12px;
					color: #666;
					white-space: nowrap;
					opacity: 0;
					visibility: hidden;
					transition: all 0.2s ease;
					background: rgba(255, 255, 255, 0.95);
					padding: 4px 8px;
					border-radius: 4px;
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
					z-index: 9999;
					pointer-events: none;
				}

				&:hover .button-text {
					opacity: 1;
					visibility: visible;
					transform: translateX(-50%) translateY(0);
				}
			}
		}
	}

	.timestamp {
		font-size: 12px;
		color: #999;
		margin-top: 6px;
		display: block;
		text-align: right;
	}
}

// 输入框区域
.input-footer {
	position: sticky; // 改为sticky定位
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 5px 25px 0;
	z-index: 10;
	border-top: 1px solid #eee;
	margin-top: auto; // 确保在底部

	// 将切换按钮移到这里，成为input-footer的第一个子元素
	.mode-toggle-btn-container {
		display: flex;
		justify-content: flex-start;
		margin-bottom: 10px;
		
		.el-button {
			border-radius: 4px;
			font-size: 12px;
			padding: 0 12px;
			background-color: #fff;
			border: 1px solid #4cd964;
			color: #4cd964;
			width: 96px;
			height: 28px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;
			
			.toggle-icon {
				width: 14px;
				height: 14px;
			}
		}
	}

	.custom-input-container {
		width: 100%;
		margin-top: 15px;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
		position: relative;
		border: 1px solid #dcdfe6;
		border-radius: 8px;
		padding: 10px 10px 8px;
		display: flex;
		flex-direction: column;
		min-height: 80px;
		.formatted-text {
			position: absolute;
			top: 10px;
			left: 12px;
			right: 12px;
			padding: 0 0 20px 0 !important;
			font-size: 14px;
			line-height: 1.5;
			font-family: inherit;
			box-sizing: border-box;
			z-index: 1;
			pointer-events: none;
			overflow: hidden;
			max-height: 200px;
			user-select: none;
			:deep(span) {
				display: inline;
			}
		}
		&:hover {
			border-color: #c0c4cc;
			box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
		}
		&:focus-within {
			border-color: #409eff;
			box-shadow: 0 2px 12px rgba(32, 160, 255, 0.2);
		}
		.custom-input {
			flex: 1;
			position: relative;
			z-index: 2;
			min-height: 70px;
			font-size: 14px;
			font-family: inherit;
			line-height: 1.5;
			box-sizing: border-box;
			/* border: 1px solid #dcdfe6; */
			// border-radius: 20px;
			background: #fff;
			outline: none;
			padding: 0 0 20px 0 !important;
			color: #333;
			caret-color: #000;
			resize: none;
			overflow-y: auto;
			// 兼容placeholder
			&:empty:before {
				content: attr(placeholder);
				color: #bbb;
			}
		}
		.action-buttons {
			align-self: flex-end;
			.button-group {
				display: flex;
				align-items: center;
				gap: 15px;
				.icon-btn {
					padding: 6px;
					width: 30px;
					height: 30px;
					border: none;
					:hover {
						background-color: #fff;
					}
					.icon-img {
						width: 30px;
						height: 30px;
					}
				}
			}
		}
	}
}

@keyframes wave {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(100%);
	}
}

@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}

	50% {
		transform: translateY(-4px);
	}
}

@keyframes dots {
	0%, 20% {
		content: ".";
	}

	40% {
		content: "..";
	}

	60%, 100% {
		content: "...";
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

// 可以添加光标闪烁效果
.ai-text.typing::after {
  content: '|';
  animation: blink 0.7s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

// 为模式切换按钮创建新的容器样式
.mode-toggle-btn-container {
	display: flex;
	justify-content: flex-start;
	margin-bottom: 10px;
	
	.el-button {
		border-radius: 4px;
		font-size: 12px;
		padding: 0 12px;
		background-color: #fff;
		border: 1px solid #4cd964;
		color: #4cd964;
		width: 96px;
		height: 28px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 4px;
		
		.toggle-icon {
			width: 14px;
			height: 14px;
		}
	}
}

.ai-disclaimer {
    font-size: 12px;
    color: #999;
    text-align: center;
    margin-top: 5px;
}
</style>