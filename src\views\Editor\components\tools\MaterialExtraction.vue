<template>
	<div>
		<!-- 上传文件进度显示 -->
		<div class="input-area">
			<!-- 上传文件进度显示 -->
			<div class="upload-progress" v-if="uploadFile.name">
				<div class="upload-file-info">
					<div class="file-icon">
						<img src="@/assets/img/folder-video-fill.png" alt="视频文件"
							v-if="uploadFile.type.startsWith('video/')">
						<img src="@/assets/img/folder-video-fill.png" alt="音频文件" v-else>
					</div>
					<div class="file-details">
						<div class="file-name">{{ uploadFile.name }}</div>
						<div class="file-size">{{ formatFileSize(uploadFile.loaded) }}/{{
							formatFileSize(uploadFile.size) }}</div>
					</div>
					<div class="upload-percent">{{ uploadFile.percent }}%</div>
					<div class="close-upload" @click="handleCancelUpload">
						<img src="@/assets/img/001.png" alt="取消上传">
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="progress-bar" :style="{ width: uploadFile.percent + '%' }"></div>
				</div>
			</div>

			<!-- 原来的文本区域 -->
			<div class="text-area" v-else>
				<textarea v-model="inputText" placeholder="输入短视频链接或上传本地音视频（MP3格式）及文档文件，快速提取视频或文案"
					@input="handleInput"></textarea>
				<div class="word-count">{{ wordCount }}/1500</div>
			</div>

			<!-- 上传按钮 -->
			<el-button class="upload-btn" type="primary" @click="handleUploadClick" :disabled="isUploading">
				<img src="@/assets/img/btn.png" alt="上传" class="btn-icon">
				{{ isUploading ? '上传中...' : '上传文件' }}
			</el-button>

			<!-- 隐藏的文件输入框 -->
			<input type="file" ref="fileInputRef" @change="handleFileChanged" style="display: none"
				accept="video/*,audio/*">
		</div>

		<!-- 底部选项 -->
		<div class="bottom-options" v-if="showOptions">
			<label class="option-item">
				<input type="checkbox" v-model="options.extractText">
				提取文案
			</label>
			<label class="option-item">
				<input type="checkbox" v-model="options.extractVideo">
				提取视频
			</label>
		</div>

		<!-- 提取按钮 -->
		<button class="extract-btn" :disabled="isExtractButtonDisabled" @click="handleExtractClick">
			{{ extractButtonText }}
		</button>

		<!-- 提取结果视频区域 -->
		<div class="extract-result" v-if="showExtractResult">
			<div class="video-player-container">
				<div class="video-wrapper">
					<video ref="videoRef" class="video-element" controls>
						<source :src="extractedVideoUrl" type="video/mp4">
					</video>
				</div>
			</div>
		</div>

		<!-- 操作按钮区域 -->
		<div class="extract-actions" v-if="showExtractResult">
			<div class="left-buttons">
				<button class="action-btn download-btn" @click="handleDownloadVideoClick">
					<img src="@/assets/img/download.png" alt="下载视频">
					下载视频
				</button>
				<button class="action-btn space-btn" @click="handleAddToSpaceClick">
					<img src="@/assets/img/space.png" alt="加入空间">
					加入空间
				</button>
			</div>
			<button class="action-btn select-btn">
				<img src="@/assets/img/select.png" alt="选择使用">
				选择使用
			</button>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { formatFileSize } from '../../utils/tools.js'

// 修改导入路径 - 尝试常见的store路径
// 方案一：尝试store而不是stores
import { usePreviewStore } from '@/stores/previewStore'
import { useloginStore } from '@/stores/login' // 导入用户store

// 获取组件实例
const { proxy } = getCurrentInstance();

// 获取用户store
const loginStore = useloginStore();

// 判断用户是否已登录
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;
	
	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

const props = defineProps({
	// 来自父组件的状态
	isUploading: Boolean,
	isButtonExtracting: Boolean,
	uploadFile: {
		type: Object,
		default: () => ({
			name: '',
			size: 0,
			loaded: 0,
			percent: 0,
			type: ''
		})
	},
	showExtractResult: Boolean,
	extractedVideoUrl: String
})

const emit = defineEmits([
	'extract',
	'upload-file',
	'cancel-upload',
	'download-video',
	'add-to-space',
])

// 本地状态
const inputText = ref('')
const fileInputRef = ref(null)
const videoRef = ref(null)
const options = ref({
	extractText: false,
	extractVideo: false
})
// 添加本地提取状态变量
const localIsExtracting = ref(false)

// 初始化store
const previewStore = usePreviewStore()

// 计算属性
const wordCount = computed(() => inputText.value.length)

// 计算是否可以提取
const canExtract = computed(() => {
	// 检查是否有文件上传
	if (props.uploadFile && props.uploadFile.name) {
		return true;
	}

	// 检查是否有输入文本
	if (inputText.value.trim()) {
		// 如果是URL，必须选择了至少一个提取选项
		if (isUrl.value) {
			return options.value.extractText || options.value.extractVideo;
		}

		// 如果是普通文本，必须选择了提取文案选项
		return options.value.extractText;
	}

	return false;
})

// 计算是否显示选项
const showOptions = computed(() => {
	// 只有在文本输入框有内容且没有上传文件时显示选项（URL情况）
	return inputText.value.trim().length > 0 && (!props.uploadFile || !props.uploadFile.name);
})

// 处理文本输入
const handleInput = () => {
	// 如果超出字数限制，截断文本
	if (inputText.value.length > 1500) {
		inputText.value = inputText.value.slice(0, 1500);
	}
}

// 点击上传按钮
const handleUploadClick = () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	fileInputRef.value.click()
}

// 文件选择改变
const handleFileChanged = (event) => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 确保传递的是原始DOM事件对象
	if (event && event.target && event.target.files && event.target.files[0]) {
		emit('upload-file', event);
	} else {
		console.error('无效的文件选择事件');
	}
}

// 取消上传
const handleCancelUpload = () => {
	emit('cancel-upload')
}

// 提取按钮点击
const handleExtractClick = () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 准备参数
	const selectedTypes = [];
	if (options.value.extractText) {
		selectedTypes.push('1');
	}
	if (options.value.extractVideo) {
		selectedTypes.push('2');
	}
	
	const params = {
		content: inputText.value,
		type: selectedTypes.join(','),
		options: options.value, // 保持向后兼容
		file: props.uploadFile,
		isUrl: isUrl.value,
		isTextExtraction: options.value.extractText // 添加标识，表明是否是文案提取
	}
	
	// 立即将本地按钮状态设置为提取中
	localIsExtracting.value = true;
	console.log('提取参数:', params);
	
	// 重要: 当点击提取按钮时，更新Pinia store中的标志，临时关闭提取内容显示
	// 设置isTimelineContent为false，这样PreviewPanel的showExtractedContent会返回false
	previewStore.setIsTimelineContent(false);
	
	// 发送提取事件到父组件
	emit('extract', params)
	
	// 在短暂延迟后恢复按钮状态
	setTimeout(() => {
		// 直接重置本地状态，不需要检查父组件状态
		localIsExtracting.value = false;
	}, 200);
}

// 下载视频
const handleDownloadVideoClick = () => {
	emit('download-video')
}

// 添加到空间
const handleAddToSpaceClick = () => {
	emit('add-to-space')
}

// 添加URL检测逻辑
const isUrl = computed(() => {
	const urlPattern = /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/;
	return urlPattern.test(inputText.value.trim());
});

// 计算提取按钮是否应该禁用
const isExtractButtonDisabled = computed(() => {
	// 分别打印每个条件的值
	console.log('按钮状态条件:', {
		isUploading: props.isUploading,
		notCanExtract: !canExtract.value,
		isButtonExtracting: props.isButtonExtracting,
		localIsExtracting: localIsExtracting.value
	});
	
	return props.isUploading || !canExtract.value || props.isButtonExtracting || localIsExtracting.value;
});

// 计算提取按钮显示的文本
const extractButtonText = computed(() => {
	return (props.isButtonExtracting || localIsExtracting.value) ? '提取中...' : '提 取';
});
</script>

<style lang="scss" scoped>
@use './toolCommon.scss' as *;

.input-area {
	flex: 1;
	background: #EFEFF1;
	border-radius: 4px;
	padding: 16px;
	margin-bottom: 12px;
	position: relative;
	border: 1px solid #e4e7ed;
	width: 473px;
	height: 185px !important;
	max-height: 185px !important;
	min-height: 185px !important;
	overflow: hidden;

	// 上传进度样式
	.upload-progress {
		width: 450px;
		height: 82px;
		display: flex;
		flex-direction: column;
		padding: 15px 20px;
		border-radius: 4px;
		background: #fff;
		position: absolute;
		left: 50%;
		top: 31%;
		transform: translate(-50%, -50%);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.upload-file-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 12px;

			.file-icon {
				width: 32px;
				height: 32px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 10px;

				img {
					width: 22px;
					height: 22px;
					object-fit: contain;
				}
			}

			.file-details {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;

				.file-name {
					font-size: 14px;
					font-weight: 500;
					color: #333;
					margin-bottom: 4px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					max-width: 400px;
				}

				.file-size {
					font-size: 12px;
					color: #666;
				}
			}

			.upload-percent {
				font-size: 14px;
				color: #0AAF60;
				font-weight: 500;
				margin: 0 15px;
			}

			.close-upload {
				width: 20px;
				height: 20px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				img {
					width: 16px;
					height: 16px;
					object-fit: contain;
				}

				&:hover {
					opacity: 0.8;
				}
			}
		}

		.progress-bar-container {
			height: 6px;
			background: #E0E0E0;
			border-radius: 3px;
			overflow: hidden;

			.progress-bar {
				height: 100%;
				background: linear-gradient(90deg, #0AAF60, #A4CB55);
				border-radius: 3px;
				transition: width 0.3s ease;
			}
		}
	}

	.text-area {
		height: 100%;
		position: relative;

		textarea {
			width: 100%;
			height: 100% !important;
			min-height: unset !important;
			max-height: none !important;
			border: none;
			background: #EFEFF1;
			resize: none;
			outline: none;
			font-size: 14px;
			line-height: 1.5;
			padding: 12px;
			padding-top: 2px;
			padding-bottom: 40px;
			padding-left: 4px;
			padding-right: 12px;
			color: #303133;

			&::placeholder {
				color: #909399;
				line-height: 1.5;
			}
		}

		.word-count {
			position: absolute;
			right: 12px;
			bottom: 8px;
			color: #909399;
			font-size: 12px;
			background: transparent;
			padding: 0 4px;
			z-index: 1;
		}
	}

	.upload-btn {
		position: absolute;
		left: 12px;
		bottom: 10px;
		width: 106px;
		height: 30px;
		padding: 0 12px;
		background: #EFEFF1;
		color: #0AAF60;
		border: 1px solid #0AAF60;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px;
		cursor: pointer;
		transition: all 0.3s;
		font-size: 13px;

		.btn-icon {
			width: 17px;
			height: 17px;
			margin: -1px 8px 0 0;
			filter: brightness(0) saturate(100%) invert(56%) sepia(93%) saturate(385%) hue-rotate(93deg) brightness(92%) contrast(93%);
		}

		&:hover {
			opacity: 0.9;
		}
	}
}

.bottom-options {
	margin: 12px 0;
	display: flex;
	gap: 24px;

	.option-item {
		display: flex;
		align-items: center;
		gap: 6px;
		color: #606266;
		cursor: pointer;
		user-select: none;

		input[type="checkbox"] {
			margin: 0;
			background: #fff;
			border: 1px solid #0AAF60;
			appearance: none;
			width: 16px;
			height: 16px;
			border-radius: 3px;
			position: relative;
			cursor: pointer;

			&:checked {
				background: #0AAF60;

				&::after {
					content: '';
					position: absolute;
					left: 4px;
					top: 1px;
					width: 4px;
					height: 8px;
					border: solid #fff;
					border-width: 0 2px 2px 0;
					transform: rotate(45deg);
				}
			}
		}

		&:hover {
			color: #0AAF60;
		}
	}
}

.extract-btn {
	width: 473px;
	height: 36px;
	border: none;
	border-radius: 4px;
	background: linear-gradient(90deg, #67c23a, #85ce61);
	color: #fff;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s;

	&:hover {
		opacity: 0.9;
	}

	&:disabled {
		background: #e4e7ed;
		opacity: 1;
		cursor: not-allowed;
	}
}

// 提取结果视频区域
.extract-result {
	margin-top: 15px;
	border-radius: 8px;
	overflow: hidden;
	background: #fff;
	width: 100%;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

	.video-player-container {
		position: relative;
		width: 100%;

		.video-wrapper {
			position: relative;
			width: 100%;
			min-height: 364px;
			background: #000;

			.video-element {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}
}

// 操作按钮区域
.extract-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 15px;

	.left-buttons {
		display: flex;
		gap: 10px;
	}

	.action-btn {
		width: 106px;
		height: 34px;
		border: none;
		border-radius: 4px;
		color: #fff;
		font-size: 14px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
		transition: all 0.3s;
		background: #0AAF60;

		img {
			width: 16px;
			height: 16px;
			object-fit: contain;
			filter: brightness(0) invert(1); // 将图标颜色改为白色
		}

		&:hover {
			opacity: 0.9;
		}
	}
}
</style>