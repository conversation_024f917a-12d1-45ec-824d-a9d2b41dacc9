<template>
	<div class="text-polishing-wrapper">
		<!-- 控制区域 - 包含下拉框和输入区 -->
		<div class="control-container">
			<!-- 上半部分 - 三个下拉框 -->
			<div class="dropdown-section">
				<!-- 第一行下拉框 -->
				<div class="dropdown-row">
					<div class="dropdown-item">
						<span class="dropdown-label">类型</span>
						<div class="dropdown-wrapper">
							<el-select v-model="typeValue" class="dropdown-select" placeholder="选择类型">
								<el-option value="humor" label="改写" />
								<el-option value="serious" label="扩写" />
								<el-option value="casual" label="缩写" />
								<el-option value="decorate" label="装写" />
								<el-option value="imitate" label="仿写" />
							</el-select>
						</div>
					</div>
					
					<div class="dropdown-item">
						<span class="dropdown-label">风格</span>
						<div class="dropdown-wrapper">
							<el-select v-model="styleValue" class="dropdown-select" placeholder="选择风格">
								<el-option value="humor" label="谦逊谨慎" />
								<el-option value="elegant" label="优雅精致" />
								<el-option value="simple" label="简洁明了" />
								<el-option value="oral" label="口语对话" />
								<el-option value="spoken" label="口头语" />
								<el-option value="written" label="书面语" />
								<el-option value="official" label="公文用语" />
							</el-select>
						</div>
					</div>
				</div>

				
				
				<!-- 第二行下拉框 -->
				<div class="dropdown-row">
					<div class="dropdown-item">
						<span class="dropdown-label">字数</span>
						<div class="dropdown-wrapper">
							<el-input v-model="wordCount" class="input-field" placeholder="请输入" />
						</div>
					</div>
					
					<div class="dropdown-item align-with-above">
						<span class="dropdown-label">修辞手法</span>
						<div class="dropdown-wrapper">
							<el-select v-model="methodValue" class="dropdown-select" placeholder="请选择">
								<el-option v-for="item in methodOptions" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</div>
					</div>
				</div>
			</div>
			
			<!-- 下半部分 - 按钮 -->
			<div class="input-button-section">
				<div class="buttons-wrapper">
					<el-button class="clear-btn" @click="handleClear">清除</el-button>
					<el-button class="confirm-btn" type="primary" @click="handleConfirm">确定</el-button>
				</div>
			</div>
		</div>

		<!-- 聊天框区域 - 独立的容器 -->
		<div class="chat-container">
			<!-- 消息显示区域 -->
			<div ref="messageListRef" class="message-list" v-if="messages.length > 0">
				<div v-for="group in messages" :key="group.id" class="message-group">
					<!-- 用户请求消息 -->
					<div class="message-item user-message" @mouseenter="hoverIndex = group.id" @mouseleave="hoverIndex = -1">
						<div class="bubble user-bubble">
							<div class="message-content">
								<div class="user-request-format">
									请{{ getTypeName(group.userMessage.type) }}文案内容
									<template v-if="group.userMessage.style">，以{{ getStyleName(group.userMessage.style) }}的风格</template>
									<template v-if="group.userMessage.method">，{{ getMethodName(group.userMessage.method) }}的修辞手法</template>
									<template v-if="group.userMessage.count">，字数{{ group.userMessage.count }}字</template>
								</div>
							</div>
							<span class="timestamp">🕒 {{ group.userMessage.time }}</span>
						</div>
					</div>
					
					<!-- AI响应消息 -->
					<div v-if="group.aiMessage" class="message-item ai-message" @mouseenter="hoverIndex = group.id" @mouseleave="hoverIndex = -1">
						<!-- 刷新加载状态 -->
						<div v-if="group.aiMessage.isRefreshing" class="bubble thinking-bubble">
							<div class="thinking-text">
								<span>💭</span>
								<div class="thinking-wave">重新生成中...</div>
							</div>
							<span class="timestamp">🕒 {{ group.aiMessage.refreshTime || group.aiMessage.time }}</span>
						</div>
						
						<!-- AI消息内容 -->
						<div v-else class="bubble ai-bubble">
							<div class="message-content">
								<div class="ai-text" @contextmenu="(e) => copyWithContext(group.id, e)">{{ group.aiMessage.aiResponse }}</div>
							</div>
							
							<!-- 操作按钮 -->
							<div class="action-buttons" :class="{
								'always-visible': group.aiMessage.isNew || hoverIndex === group.id || isLatestAIMessage(messages.indexOf(group))
							}">
								<div class="action-button-wrapper">
									<el-button link @click="copyText(group.aiMessage.aiResponse)" @contextmenu.prevent="(e) => copyWithContext(group.id, e)" aria-label="复制">
										<el-icon><DocumentCopy /></el-icon>
										<span class="button-text">复制</span>
									</el-button>
								</div>

								<div class="action-button-wrapper">
									<el-button link @click="refreshText(group.id, group.aiMessage)" aria-label="刷新">
										<el-icon><Refresh /></el-icon>
										<span class="button-text">刷新</span>
									</el-button>
								</div>

								<div class="action-button-wrapper">
									<el-button link @click="saveText(group.id)" aria-label="收藏">
										<el-icon><Star /></el-icon>
										<span class="button-text">收藏</span>
									</el-button>
								</div>

								<div class="action-button-wrapper">
									<el-button link @click="deleteText(group.id)" aria-label="删除">
										<el-icon><Delete /></el-icon>
										<span class="button-text">删除</span>
									</el-button>
								</div>

								<div class="action-button-wrapper">
									<el-button link @click="useContent(group.aiMessage.aiResponse)" aria-label="提取使用">
										<el-icon><FolderChecked /></el-icon>
										<span class="button-text">提取使用</span>
									</el-button>
								</div>
							</div>
							
							<span class="timestamp">🕒 {{ group.aiMessage.time }}</span>
						</div>
					</div>
					
					<!-- 处理中状态 - 修改为包含AI流体步骤 -->
					<div v-if="group.userMessage.status === 'pending'" class="message-item ai-message">
						<!-- AI思考流程步骤 -->
						<div v-if="showThinkingProcess && group.flowStep" class="bubble thinking-bubble ai-flow-step">
							<div class="thinking-text flow-step-content">
								<span>💭</span>
								<div class="flow-step-title">{{ getFlowStepTitle(group.flowStep) }}</div>
							</div>
							<div class="flow-step-description">{{ getFlowStepDescription(group.flowStep) }}</div>
							<div class="thinking-wave">{{ getFlowStepAction(group.flowStep) }}</div>
							<span class="timestamp">🕒 {{ group.userMessage.time }}</span>
						</div>
						
						<!-- 常规处理中状态 -->
						<div v-else class="bubble thinking-bubble">
							<div class="thinking-text">
								<span>💭</span>
								<div class="thinking-wave">正在思考中...</div>
							</div>
							<span class="timestamp">🕒 {{ group.userMessage.time }}</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 保存到我的空间 -->
		<SubtitleDialog v-model="showSaveDialog" :message-content="selectedMessage" @confirm="saveToFavorites" />

		<!-- 添加会员限制弹窗 -->
		<AlertDialog 
			v-model:visible="showLimitDialog"
			type="warning"
			title="会员功能"
			message="非会员每日只能使用15次，请开通会员使用"
			confirm-button-text="开通会员"
			cancel-button-text="我知道了"
			:show-cancel-button="true"
			:custom-confirm-class="true"
			:custom-cancel-class="true"
			:show-fee-explanation="false"
			@confirm="handleOpenMember"
			@cancel="handleCloseLimitDialog"
		/>
	</div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Refresh, Star, Delete, FolderChecked } from '@element-plus/icons-vue'
import { questionAPl, getChatRecordApi } from "@/api/creation" // 引入与AICreation相同的API
import { useMessageStore } from '@/stores/message' // 引入消息存储
import SubtitleDialog from "@/components/SubtitleDialog/index.vue" // 收藏对话框
import { usePreviewStore } from '@/stores/previewStore' // 引入预览内容存储
import { useloginStore } from '@/stores/login' // 导入用户store
import AlertDialog from "@/views/components/AlertDialog.vue" // 引入AlertDialog组件
import { useRouter } from 'vue-router' // 引入路由
// import { getCurrentTime } from '../../utils/time'

// 获取组件实例
const { proxy } = getCurrentInstance();

// 获取消息存储
const messageStore = useMessageStore()
// 获取预览内容存储
const previewStore = usePreviewStore()
// 获取用户store
const loginStore = useloginStore()

// 获取路由实例
const router = useRouter();

// 基础功能逻辑
const typeValue = ref('')
const styleValue = ref('')
const methodValue = ref('')
const wordCount = ref('')
const messages = ref([])
const isLoading = ref(false)
const aiResponse = ref('')
const hoverIndex = ref(-1)
const showSaveDialog = ref(false)
const selectedMessage = ref("")
const messageListRef = ref(null) // 消息列表DOM引用

// 新增：AI流程相关变量
const showThinkingProcess = ref(true) // 控制是否显示思考流程
const currentFlowStep = ref(0)
const flowSteps = [
	{
		title: '理解需求',
		description: '分析用户所需的文本类型、风格和修辞手法',
		action: '正在分析需求...'
	},
	{
		title: '构思内容',
		description: '根据要求构思适合的内容框架和主题',
		action: '正在构思内容...'
	},
	{
		title: '润色表达',
		description: '应用所选风格和修辞手法优化表达',
		action: '正在润色文本...'
	},
	{
		title: '调整长度',
		description: '按照字数要求调整文本长度',
		action: '正在调整长度...'
	},
	{
		title: '最终检查',
		description: '检查文本是否符合所有要求',
		action: '正在进行最终检查...'
	}
]

// 修辞手法选项
const methodOptions = ref([
	{ value: 'metaphor', label: '比喻' },
	{ value: 'personification', label: '拟人' },
	{ value: 'exaggeration', label: '夸张' },
	{ value: 'impersonation', label: '拟代' },
	{ value: 'antithesis', label: '对偶' },
	{ value: 'rhetorical', label: '反问' },
	{ value: 'question', label: '设问' }
])

// 类型选项对应的名称
const typeNames = {
	'humor': '改写',
	'serious': '扩写',
	'casual': '缩写',
	'decorate': '装写',
	'imitate': '仿写'
}

// 风格选项对应的名称
const styleNames = {
	'humor': '谦逊谨慎',
	'elegant': '优雅精致',
	'simple': '简洁明了',
	'oral': '口语对话',
	'spoken': '口头语',
	'written': '书面语',
	'official': '公文用语'
}

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}

// 滚动到最新消息位置
const scrollToBottom = () => {
	nextTick(() => {
		if (messageListRef.value) {
			setTimeout(() => {
				messageListRef.value.scrollTop = messageListRef.value.scrollHeight;
			}, 100); // 添加小延迟确保DOM已更新
		}
	});
};

// 判断是否是最新的AI消息
const isLatestAIMessage = (index) => {
	// 如果消息列表只有一条消息，则显示按钮
	if (messages.value.length === 1 && index === 0) {
		return true;
	}
	
	// 有多条消息时，只对最后一条AI消息返回true
	if (messages.value.length > 1) {
		// 找出所有有AI回复的消息
		const aiMessageGroups = messages.value.filter(msg => msg.aiMessage && !msg.aiMessage.isRefreshing);
		
		// 如果没有AI消息或者处于刷新状态，直接返回false
		if (aiMessageGroups.length === 0) {
			return false;
		}
		
		// 找到最后一条AI消息的索引
		const lastAiMessageIndex = messages.value.findIndex(msg => 
			msg.id === aiMessageGroups[aiMessageGroups.length - 1].id
		);
		
		// 只有最后一条AI消息返回true
		return index === lastAiMessageIndex;
	}
	
	return false;
};

// 获取类型名称
const getTypeName = (type) => {
	return typeNames[type] || type
}

// 获取风格名称
const getStyleName = (style) => {
	return styleNames[style] || style
}

// 获取修辞手法名称
const getMethodName = (method) => {
	const methodOption = methodOptions.value.find(item => item.value === method)
	return methodOption ? methodOption.label : method
}

// 获取当前时间
const getCurrentTime = () => {
	const now = new Date()
	return now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' })
}

// 清除方法
const handleClear = () => {
	typeValue.value = ''
	styleValue.value = ''
	methodValue.value = ''
	wordCount.value = ''
}

// 构建提示文本
const buildPrompt = () => {
	let prompt = '请'
	
	// 添加类型信息
	if (typeValue.value) {
		prompt += `${getTypeName(typeValue.value)}`
	}
	
	prompt += '文案内容'
	
	// 添加风格信息
	if (styleValue.value) {
		prompt += `，以${getStyleName(styleValue.value)}的风格`
	}
	
	// 添加修辞手法信息
	if (methodValue.value) {
		prompt += `，${getMethodName(methodValue.value)}的修辞手法`
	}
	
	// 添加字数限制
	if (wordCount.value) {
		prompt += `，字数${wordCount.value}字`
	}
	
	prompt += '。'
	
	return prompt
}

// 获取流程步骤标题
const getFlowStepTitle = (step) => {
	return flowSteps[step].title;
}

// 获取流程步骤描述
const getFlowStepDescription = (step) => {
	return flowSteps[step].description;
}

// 获取流程步骤动作
const getFlowStepAction = (step) => {
	return flowSteps[step].action;
}

// 判断用户是否已登录
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;
	
	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

// 新增：会员限制相关变量
const showLimitDialog = ref(false);

// 处理会员限制逻辑
const handleOpenMember = () => {
	// 关闭弹窗
	showLimitDialog.value = false;
	
	// 导航到会员页面
	try {
		// 判断是否在layout布局内
		if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
			// 如果在layout布局内，使用内部路由导航
			router.push({ name: 'membership-nav' });
		} else {
			// 否则通过URL跳转
			window.location.href = '/membership';
		}
		ElMessage.success("正在跳转到会员页面");
	} catch (error) {
		console.error("导航到会员页面失败:", error);
		ElMessage.error("导航到会员页面失败，请手动前往会员中心");
	}
};

const handleCloseLimitDialog = () => {
	// 关闭会员限制弹窗
	showLimitDialog.value = false;
};

// 修改确认提交方法
const handleConfirm = async () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}
	
	if (!typeValue.value || !styleValue.value) {
		ElMessage.warning('请至少填写类型和风格')
		return
	}
	
	// 检查右侧预览区是否有文本内容
	const previewContent = previewStore.content
	if (!previewContent || previewContent.trim() === '') {
		ElMessage.warning('请先输入文案')
		return
	}
	
	// 获取提示文本
	const prompt = buildPrompt()
	
	// 创建消息组
	const messageGroup = {
		id: Date.now(),
		userMessage: {
			type: typeValue.value,
			style: styleValue.value,
			method: methodValue.value,
			count: wordCount.value,
			prompt: prompt,
			time: getCurrentTime(),
			status: 'pending'
		},
		flowStep: 0, // 初始化为第一个步骤
		aiMessage: null
	}
	
	// 添加到消息列表
	messages.value.push(messageGroup)
	
	// 设置加载状态
	isLoading.value = true
	
	// 滚动到最新消息
	scrollToBottom()
	
	try {
		// 模拟AI思考流程 - 逐步展示思考过程
		for (let i = 0; i < flowSteps.length; i++) {
			messageGroup.flowStep = i;
			// 等待一段时间，模拟思考过程
			await new Promise(resolve => setTimeout(resolve, 800 + i * 200));
			// 每次更新后滚动到底部
			scrollToBottom();
		}
		
		// 构建带有预览内容的API请求
		// 注意: 将预览内容拼接到请求中，但不在对话中显示
		const apiQuestion = `${prompt} 文案内容: ${previewContent}`
		
		// 调用API
		const res = await questionAPl({
			userId: getUserId(),
			question: apiQuestion
		})
		
		// 检查状态码 - 已注释，跳过会员限制检查
		// if (res.status_code === 310 || res.status_code === 301) {
		//	// 非会员用量已达上限
		//	showLimitDialog.value = true;
		//	// 更新状态
		//	messageGroup.userMessage.status = 'failed';
		//	isLoading.value = false;
		//	return;
		// }
		
		// 更新状态
		messageGroup.userMessage.status = 'success'
		delete messageGroup.flowStep; // 移除流程步骤，不再显示思考过程
		
		// 处理响应
		if (res && res.content && res.content.result && res.content.result.ai) {
			messageGroup.aiMessage = {
				aiResponse: res.content.result.ai,
				time: getCurrentTime(),
				isNew: true
			}
			
			// 滚动到最新消息
			scrollToBottom()
			
			// 1秒后取消新消息状态
			setTimeout(() => {
				messages.value.forEach(group => {
					if (group.aiMessage) group.aiMessage.isNew = false
				})
			}, 1000)
		}
		
		// ElMessage.success('内容生成成功')
	} catch (error) {
		messageGroup.userMessage.status = 'failed'
		// 检查错误是否包含状态码301 - 已注释，跳过会员限制检查
		// if (error && error.response && error.response.status_code === 301) {
		//	// 显示会员限制弹窗
		//	showLimitDialog.value = true;
		// } else {
			ElMessage.error('生成失败，请稍后重试')
			console.error('生成失败:', error)
		// }
	} finally {
		isLoading.value = false
	}
	
	// 清空输入
	handleClear()
}

// 复制菜单函数
const copyWithContext = (groupId, e) => {
	e.preventDefault();
	e.stopPropagation();
	const group = messages.value.find(g => g.id === groupId);
	if (!group || !group.aiMessage) return;
	
	// 创建上下文菜单
	const menu = document.createElement('div');
	menu.className = 'copy-context-menu';
	menu.style.position = 'fixed';
	menu.style.top = e.clientY + 'px';
	menu.style.left = e.clientX + 'px';
	menu.style.zIndex = '10000';
	menu.style.background = 'white';
	menu.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
	menu.style.borderRadius = '4px';
	menu.style.padding = '8px 0';
	
	// 菜单选项1：复制为纯文本
	const plainTextOption = document.createElement('div');
	plainTextOption.innerText = '复制为纯文本';
	plainTextOption.style.padding = '8px 16px';
	plainTextOption.style.cursor = 'pointer';
	plainTextOption.style.fontSize = '14px';
	plainTextOption.onmouseover = () => {
		plainTextOption.style.backgroundColor = '#f5f7fa';
	};
	plainTextOption.onmouseout = () => {
		plainTextOption.style.backgroundColor = 'transparent';
	};
	plainTextOption.onclick = () => {
		copyText(group.aiMessage.aiResponse);
		document.body.removeChild(menu);
	};
	
	// 菜单选项2：复制原始格式
	const originalOption = document.createElement('div');
	originalOption.innerText = '复制原始内容';
	originalOption.style.padding = '8px 16px';
	originalOption.style.cursor = 'pointer';
	originalOption.style.fontSize = '14px';
	originalOption.onmouseover = () => {
		originalOption.style.backgroundColor = '#f5f7fa';
	};
	originalOption.onmouseout = () => {
		originalOption.style.backgroundColor = 'transparent';
	};
	originalOption.onclick = () => {
		copyText(group.aiMessage.aiResponse);
		document.body.removeChild(menu);
	};
	
	// 添加选项到菜单
	menu.appendChild(plainTextOption);
	menu.appendChild(originalOption);
	document.body.appendChild(menu);
	
	// 点击页面其他区域关闭菜单
	const closeMenu = (e) => {
		if (!menu.contains(e.target)) {
			document.body.removeChild(menu);
			document.removeEventListener('mousedown', closeMenu);
		}
	};
	setTimeout(() => {
		document.addEventListener('mousedown', closeMenu);
	}, 100);
};

// 复制文本方法
const copyText = (text) => {
	if (!text) return

	// 尝试使用现代 Clipboard API
	if (navigator.clipboard && window.isSecureContext) {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				// ElMessage.success("复制成功")
			})
			.catch(() => {
				fallbackCopyTextToClipboard(text)
			})
	} else {
		fallbackCopyTextToClipboard(text)
	}
}

// 传统复制方法（备用方案）
const fallbackCopyTextToClipboard = (text) => {
	try {
		const textArea = document.createElement("textarea")
		textArea.value = text
		textArea.style.position = "fixed"
		textArea.style.top = "0"
		textArea.style.left = "0"
		textArea.style.width = "2em"
		textArea.style.height = "2em"
		textArea.style.padding = "0"
		textArea.style.border = "none"
		textArea.style.outline = "none"
		textArea.style.boxShadow = "none"
		textArea.style.background = "transparent"

		document.body.appendChild(textArea)
		textArea.focus()
		textArea.select()

		const successful = document.execCommand("copy")
		document.body.removeChild(textArea)

		if (successful) {
			// ElMessage.success("复制成功")
		} else {
			ElMessage.error("复制失败")
		}
	} catch (err) {
		ElMessage.error("复制失败")
		console.error("复制失败:", err)
	}
}

// 使用内容方法
const useContent = (text) => {
	if (!text) return;
	
	// 使用Pinia store将内容发送到编辑器
	messageStore.addBotMessage({
		content: {
			result: {
				ai: text
			}
		}
	});
	
	// 向父组件发送提取内容事件，与AICreation组件保持一致
	emit('extract-content', text);
	
	// ElMessage.success("内容已添加到编辑器");
};

// 刷新文本
const refreshText = async (groupId, aiMsg) => {
	if (!aiMsg) return;
	
	const groupIndex = messages.value.findIndex(group => group.id === groupId);
	if (groupIndex === -1) return;
	
	const group = messages.value[groupIndex];
	const originalContent = group.aiMessage.aiResponse;
	const originalTime = group.aiMessage.time;

	// 立即设置为刷新状态
	group.aiMessage = {
		...group.aiMessage,
		isRefreshing: true,
		refreshTime: getCurrentTime(),
		originalContent,
		originalTime,
	};

	// 滚动到当前正在刷新的消息
	scrollToBottom();

	try {
		// 重新调用API获取新内容
		const prompt = group.userMessage.prompt;
		const previewContent = previewStore.content; // 获取当前编辑器内容
		const apiQuestion = `${prompt} 文案内容: ${previewContent}`; // 构建完整请求
		
		const res = await questionAPl({
			userId: getUserId(),
			question: apiQuestion
		});
		
		// 检查状态码判断会员限制 - 已注释，跳过会员限制检查
		// if (res.status_code === 310 || res.status_code === 301) {
		//	// 非会员用量已达上限
		//	showLimitDialog.value = true;
		//	// 恢复原始内容
		//	group.aiMessage = {
		//		...group.aiMessage,
		//		aiResponse: originalContent,
		//		time: originalTime,
		//		isRefreshing: false,
		//	};
		//	return;
		// }

		// 更新为新内容
		group.aiMessage = {
			...group.aiMessage,
			aiResponse: res.content.result.ai,
			isRefreshing: false,
			time: getCurrentTime(),
		};
		
		// 滚动到刷新后的消息
		scrollToBottom();
		
		// ElMessage.success("内容已刷新");
	} catch (error) {
		// 检查错误是否包含状态码301 - 已注释，跳过会员限制检查
		// if (error && error.response && error.response.status_code === 301) {
		//	// 显示会员限制弹窗
		//	showLimitDialog.value = true;
		// }
		
		// 恢复原始内容
		group.aiMessage = {
			...group.aiMessage,
			aiResponse: originalContent,
			time: originalTime,
			isRefreshing: false,
		};
		
		// if (!(error && error.response && error.response.status_code === 301)) {
			ElMessage.error("刷新失败");
		// }
	}
};

// 删除消息
const deleteText = (groupId) => {
	const index = messages.value.findIndex(group => group.id === groupId);
	if (index > -1) {
		messages.value.splice(index, 1);
		// ElMessage.success("已删除");
	}
};

// 点击收藏按钮
const saveText = (groupId) => {
	const group = messages.value.find(g => g.id === groupId);
	if (!group || !group.aiMessage) return;
	
	selectedMessage.value = group.aiMessage.aiResponse;
	showSaveDialog.value = true;
};

// 保存到收藏夹
const saveToFavorites = async (data) => {
	// 处理 tagIds：如果是数组则用逗号连接，如果是单个值则直接使用
	const tagIds = Array.isArray(data) ? data.join(',') : data;

	// 处理显示用的标题文字
	let displayTitle = selectedMessage.value;
	if (displayTitle.length > 15) {
		displayTitle = displayTitle.substring(0, 15) + "...";
	}

	// 调用保存接口
	try {
		const res = await getChatRecordApi({
			userId: getUserId(),
			materialName: displayTitle,
			mType: 'text',
			sourceType: "UPLOAD",
			isPrivate: true,
			isFavorite: true,
			fileExtension: "txt",
			isDeleted: false,
			categoryId: "4",
			tagIds: tagIds,
			text_content: selectedMessage.value
		});

		// ElMessage.success("收藏成功");
		showSaveDialog.value = false;
	} catch (error) {
		ElMessage.error("收藏失败");
		console.error("收藏失败:", error);
	}
};

// 确保包含 extract-content 事件
const emit = defineEmits(['extract-content']);
</script>

<style lang="scss" scoped>
.text-polishing-wrapper {
	width: 473px;
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.control-container {
	width: 100%;
	background-color: #f9f9f9;
	padding: 10px;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
}

.chat-container {
	width: 100%;
	background-color: white;
	border-radius: 8px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* 上半部分样式 */
.dropdown-section {
	display: flex;
	flex-direction: column;
	gap: 10px;
	margin-bottom: 10px;
	width: 100%;
}

.dropdown-row {
	display: flex;
	justify-content: space-between;
	width: 100%;
}

.dropdown-item {
	display: flex;
	align-items: center;
	flex: 1;
	margin-right: 10px;
	
	&:last-child {
		margin-right: 0;
	}
}

.dropdown-label {
	margin-right: 10px;
	font-size: 14px;
	color: #333;
	white-space: nowrap;
}

.dropdown-wrapper {
	position: relative;
	flex: 1;
}

.dropdown-select {
	width: 160px;
	height: 32px;
}

.input-field {
	width: 160px;
	height: 32px;
}

/* 下半部分样式 */
.input-button-section {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 15px;
}

.buttons-wrapper {
	display: flex;
	gap: 10px;
}

.clear-btn {
	width: 60px;
	height: 32px;
	background-color: #f9f9f9;
	color: #606266;
	border: none;
	padding: 0;
}

.confirm-btn {
	width: 60px;
	height: 32px;
	background-color: #0AAF60;
	color: white;
	border: none;
	padding: 0;
}

/* 聊天消息区域样式 */
.message-list {
	padding: 15px;
	overflow-y: auto;
	max-height: 500px;
	min-height: 500px;
	/* 隐藏滚动条但保留滚动功能 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE and Edge */
}

/* 为WebKit浏览器（Chrome、Safari等）隐藏滚动条 */
.message-list::-webkit-scrollbar {
	display: none;
}

.message-group {
	display: flex;
	flex-direction: column;
	margin-bottom: 20px;
}

.message-item {
	display: flex;
	margin-bottom: 10px;
	animation: fadeIn 0.3s ease;
}

.user-message {
	justify-content: flex-end;
}

.ai-message {
	justify-content: flex-start;
}

.bubble {
	position: relative;
	padding: 12px 15px;
	border-radius: 12px;
	background-color: #fff;
	border: 1px solid #e4e7ed;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	max-width: 80%;
	transition: all 0.3s;
}

.user-bubble {
	font-size: 14px;
	margin-left: auto;
	background: #e9f5ff; /* 浅蓝色背景 */
	color: #333;
	border: none;
	border-radius: 18px 0 18px 18px;
	max-width: 80%;
}

.user-text {
	margin: 10px 0;
	display: inline-block;
	line-height: 1.8;
}

.ai-bubble {
	font-size: 14px;
	padding: 15px;
	border-radius: 0 18px 18px 18px;
	align-self: flex-start;
	position: relative;
	max-width: 88%;
}

.user-params {
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.param-item {
	font-size: 14px;
	color: #606266;
}

.ai-text {
	margin: 10px 0;
	display: inline-block;
	line-height: 1.6;
	color: #333;
	white-space: pre-wrap;
}

.timestamp {
	font-size: 12px;
	color: #999;
	margin-top: 6px;
	display: block;
	text-align: right;
}

.thinking-bubble {
	background: #f5f7fa !important;
	border-color: #e4e7ed !important;
	position: relative;
	overflow: hidden;
	align-self: flex-start;
	
	&::before {
		content: "";
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.6) 50%,
				rgba(255, 255, 255, 0) 100%);
		animation: wave 1.5s infinite;
	}
}

.thinking-text {
	display: flex;
	align-items: center;
	gap: 8px;
	position: relative;
	z-index: 1;

	span {
		font-size: 1.2em;
		animation: float 2s ease-in-out infinite;
	}
}

.thinking-wave {
	position: relative;
	display: inline-block;
	color: #606266;

	&::after {
		content: "...";
		animation: dots 1.5s infinite;
	}
}

.action-buttons {
	position: absolute;
	left: 10px;
	bottom: -31px;
	display: flex;
	gap: 8px;
	opacity: 0;
	transition: opacity 0.3s;
	padding: 4px 8px;
	border-radius: 24px;
	z-index: 10;

	&.always-visible,
	.bubble:hover & {
		opacity: 1;
		visibility: visible;
	}

	.action-button-wrapper {
		position: relative;
		
		.el-button {
			padding: 2px;
			color: #666 !important;

			&:hover {
				color: #333 !important;
				background: #f5f5f5;
			}

			&:hover + .button-text {
				opacity: 1;
				visibility: visible;
				transform: translateY(0);
			}
		}

		.button-text {
			position: absolute;
			left: 50%;
			bottom: -25px;
			transform: translateX(-50%) translateY(5px);
			font-size: 12px;
			color: #666;
			white-space: nowrap;
			opacity: 0;
			visibility: hidden;
			transition: all 0.2s ease;
			background: rgba(255, 255, 255, 0.95);
			padding: 4px 8px;
			border-radius: 4px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			z-index: 9999;
			pointer-events: none;
		}

		&:hover .button-text {
			opacity: 1;
			visibility: visible;
			transform: translateX(-50%) translateY(0);
		}
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes wave {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-4px);
	}
}

@keyframes dots {
	0%, 20% {
		content: ".";
	}
	40% {
		content: "..";
	}
	60%, 100% {
		content: "...";
	}
}

/* 下拉框和输入框内部元素样式调整 */
:deep(.el-input__wrapper) {
	background-color: white;
	height: 32px;
	line-height: 32px;
}

:deep(.el-select .el-input) {
	width: 160px;
	height: 32px;
}

:deep(.el-input__inner) {
	height: 32px;
	line-height: 32px;
}

/* 应用于输入区按钮的样式，而不是消息操作按钮 */
.buttons-wrapper :deep(.el-button) {
	height: 32px;
	padding: 0 12px;
	font-size: 14px;
	border-radius: 4px;
}

/* 消息操作按钮的样式 */
.action-button-wrapper :deep(.el-button) {
	padding: 2px;
	height: auto;
	border-radius: 4px;
	font-size: 14px;
}

/* 新增：AI思考流程步骤样式 */
.ai-flow-step {
	width: 100%;
	
	.flow-step-content {
		display: flex;
		align-items: center;
	}
	
	.flow-step-title {
		font-weight: 500;
		color: #333;
		margin-left: 8px;
	}
	
	.flow-step-description {
		font-size: 13px;
		color: #666;
		margin: 5px 0 5px 28px;
	}
	
	.thinking-wave {
		margin-left: 28px;
		font-size: 13px;
		color: #1890ff;
		animation: pulse 1.5s infinite;
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.6;
	}
	100% {
		opacity: 1;
	}
}

.user-request-format {
	display: inline-block;
	line-height: 1.6;
	color: #333;
	white-space: normal;
	word-break: break-word;
	font-size: 14px;
	width: 100%;
	text-align: left;
	padding: 5px 0;
}

/* 确保第二行的修辞手法标签与上面的风格标签对齐 */
.align-with-above {
	padding-left: 10px; /* 添加一些左侧填充以对齐上面的标签 */
}

.dropdown-row:nth-child(1) .dropdown-item:nth-child(2) .dropdown-label,
.dropdown-row:nth-child(2) .dropdown-item:nth-child(2) .dropdown-label {
	width: 65px; /* 固定标签宽度确保对齐 */
	text-align: right;
	box-sizing: border-box;
	padding-right: 10px;
}

/* 调整用户消息气泡样式 */
.message-item.user-message {
	justify-content: flex-end;
	
	.user-bubble {
		background: #e9f5ff;
		border-radius: 18px 0 18px 18px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
		border: none;
	}
}

/* 强化AI回复样式 */
.message-item.ai-message {
	.ai-bubble {
		border-radius: 0 18px 18px 18px;
		background-color: #fff;
		border: 1px solid #e4e7ed;
	}
}

/* 消息内容样式 */
.message-content {
	margin: 0;
	padding: 5px 0;
	word-break: break-word;
}

/* 确保消息空间充足 */
.message-list {
	padding: 15px;
	max-height: 500px; /* 增加高度 */
}
</style>