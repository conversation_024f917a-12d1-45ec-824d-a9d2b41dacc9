// 常用颜色变量
$primary-color: #0AAF60;
$primary-gradient: linear-gradient(90deg, #0AAF60, #A4CB55);
$light-bg: #F8F8F8;
$border-color: #DCDFE6;
$text-color: #303133;
$text-secondary: #606266;
$text-light: #909399;

// 按钮样式
.tool-btn {
	width: 100%;
	height: 40px;
	background: $primary-gradient;
	border: none;
	border-radius: 4px;
	color: #FFFFFF;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;

	&:hover {
		opacity: 0.9;
	}

	&:disabled {
		background: #DCDFE6;
		cursor: not-allowed;
		opacity: 1;
	}
}

// 输入区域样式
.text-input-area {
	position: relative;
	margin-bottom: 16px;

	.area-title {
		font-size: 14px;
		font-weight: 500;
		color: $text-color;
		margin-bottom: 10px;
	}

	textarea {
		width: 100%;
		padding: 10px;
		border: 1px solid $border-color;
		border-radius: 4px;
		font-size: 14px;
		line-height: 1.5;
		color: $text-secondary;
		resize: none;

		&:focus {
			outline: none;
			border-color: $primary-color;
		}
	}

	.word-count {
		position: absolute;
		right: 10px;
		bottom: 10px;
		font-size: 12px;
		color: $text-light;
	}
}

// 加载动画
.spinner {
	width: 40px;
	height: 40px;
	border: 4px solid rgba(10, 175, 96, 0.1);
	border-radius: 50%;
	border-top-color: $primary-color;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

// 选项按钮组
.option-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	margin-bottom: 16px;

	.option-item {
		padding: 6px 12px;
		background: #FFFFFF;
		border: 1px solid $border-color;
		border-radius: 4px;
		font-size: 14px;
		color: $text-secondary;
		cursor: pointer;
		transition: all 0.3s;

		&:hover {
			border-color: $primary-color;
			color: $primary-color;
		}

		&.active {
			background: $primary-color;
			color: #FFFFFF;
			border-color: $primary-color;
		}
	}
}