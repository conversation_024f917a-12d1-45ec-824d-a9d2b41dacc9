/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本 
 * @returns {Promise<boolean>} 是否复制成功
 */
export function copyTextToClipboard(text) {
  return navigator.clipboard.writeText(text)
    .then(() => true)
    .catch(() => false);
} 