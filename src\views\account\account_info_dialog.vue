<template>
    <el-dialog v-model="dialogVisable" class="account_info_dialog" width="464px" :append-to-body="true" :style="{transformOrigin: 'top right',transform: `scale(${rate})`}">
        
        <template #default>
            <div  v-loading="loading">
            <!-- <img src="@/assets/images/account/avant_img_close.png" class="account_info_close" @click="close" alt=""> -->
            <div class="account_avant_box">
                <span class="account_avant_img">
                    <img :src="user.avant&&user.avant!=''?user.avant:avatar" class="avatar_img" />
                    <el-upload
                        ref="uploadRef"
                        class="upload-demo"
                        :on-change="handleFileChange"
                        :before-upload="beforeUpload"
                        :show-file-list="false"
                        accept="image/*"
                         :auto-upload="false"
                        >
                       
                        <span class="account_avant_img_edit">
                            <img src="@/assets/images/account/avant_img_edit.svg" alt="">
                        </span>
                    </el-upload>
                </span>
                <div class="account_avant_name">
                    {{user.name}}
                </div>
            </div>
            <el-form
                :rules="rules"
                :model="ruleForm"
                ref="ruleFormRef"
                label-width="0px"
                class="demo-ruleForm"
            >
                <el-form-item prop="nickname">
                    <el-input v-model="ruleForm.nickname" type="text" autocomplete="off" placeholder="请输入昵称" style="width:357px" @focus="name_focus" >
                        <template #prepend>昵称</template>
                        <template  #append v-if="nickname_edit">
                            <span @click="nicknameEdit">保存</span>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <!-- " -->
                    <el-input v-model="ruleForm.password" type="password" :disabled='true' autocomplete="off" placeholder="请输入昵称" style="width:357px">
                        <template #prepend>密码</template>
                        <template #append>
                            <span @click="open_edit_password_dialog">修改</span>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="phone">
                    <el-input v-model="ruleForm.phone" type="phone" :disabled='true' autocomplete="off" placeholder="请输入手机号" style="width:357px">
                        <template #prepend>手机号</template>
                        <template #append>
                            <span @click="open_edit_phone_dialog">修改</span>
                        </template>
                    </el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <el-input v-model="ruleForm.id" type="id" autocomplete="off" :disabled='true' style="width:357px">
                        <template #prepend>ID</template>
                    </el-input>
                </el-form-item> -->
            </el-form>
            <div class="account_info_btns">
                <button @click="save_infor" v-loading="save_loading">保存资料</button>
            </div>
        </div>
        </template>
       
    </el-dialog>
    <editPasswordDialog ref="edit_password_dialog_ref" @change_password="change_password"></editPasswordDialog>
    <editPhoneDialog  ref="edit_phone_dialog_ref" @update_phone="update_phone"></editPhoneDialog>
</template>
<script setup>
import {reactive,ref,defineExpose,watch,provide, onMounted,getCurrentInstance } from "vue"
import editPasswordDialog from './edit_password_dialog.vue'
import editPhoneDialog from './edit_phone_dialog.vue'
import { ElMessage } from 'element-plus'
import { useloginStore } from '@/stores/login'
import {userUpdate,userInfo} from '@/api/account.js'
import { useFileUpload } from './hook/upload.js';
import avatar from '@/assets/images/account/avatar.png'
const {  fileChange } = useFileUpload();
const { proxy } = getCurrentInstance();
let loginStore = useloginStore()
let dialogVisable=ref(false)
let bind_phone_dialog_ref=ref(null)
let edit_password_dialog_ref=ref(null)
let edit_phone_dialog_ref=ref(null)
let uploadRef = ref(null)
let close=()=>{
    dialogVisable.value=false
}
let rate=ref(window.innerWidth/1920)
let action=import.meta.env.VITE_API_BASE_URL+'/material/upload/signature'
let ruleFormRef=ref(null)
let ruleForm = reactive({
    // nickname: '王大明白',  //昵称
    // password:'666',
    // phone:'************',
    // id:123456
    nickname: '',  //昵称
    password:'********',   // 不直接访问 loginData.mobile
    phone:'',
    id:''

})
let nickname_edit=ref(false)
let rules = reactive({
    nickname:[
        { required: true, message: '请输入昵称', trigger: 'blur' },
    ],
    password:[
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, max: 20, message: '密码:8-20位，数字+字母',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/}
    ],
    // password:[
    //     { required: true, message: '请输入密码', trigger: 'blur' },
    //     { min: 8, max: 20, message: '密码:8-20位，数字+大小写字母+符号',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,20}$/ }
    // ],
    phone:[
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
    ],
})
let user=reactive({
    name:'',
    avant:'',
})
let getShowUserBenefits=()=>{
    return new Promise(async(resolve,reject)=>{
        let res=await showUserBenefits({userId:loginStore.userId?loginStore.userId:''})
        if(res.code!=0){
           ElMessage.error(res.msg) 
            return 
        }
        let data=res?.data?.content
        loginStore.setMemberInfo(data)
        resolve(true)
    })
   
}
// 初始化用户信息函数
const initUserInfo = async() => {
    if(loginStore && loginStore.userInfo) {
        user.name = loginStore.userInfo.nickName || ''
        user.avant = loginStore.userInfo.avatar || ''
        // 同步表单数据
        ruleForm.nickname = loginStore.userInfo.nickName || ''
        ruleForm.phone = loginStore.userInfo.mobile || ''
    }
    // await getShowUserBenefits()
}

let change_password=(data)=>{
    // ruleForm.password=data
}
let update_phone=(data)=>{
    ruleForm.phone=data
}
let save_loading=ref(false)
let save_infor=async(loading)=>{
    try {
        if(loading!='no_loading'){
            save_loading.value=true
        }
        console.log(loading,'loading');
        
        let data = await userUpdate({ nickName:ruleForm.nickname, mobile:ruleForm.phone,avatar:user.avant,userId:loginStore.userId})
        if(loading!='no_loading'){
            save_loading.value=false
            console.log('保存用户信息成功:', data);
            ElMessage.success('保存资料成功');
            await getUserInfo()
            close()
        }else{            
            nickname_edit.value=false
            ElMessage.success('保存成功');
        }
    } catch (error) {
        if(loading!='no_loading'){
            console.error('保存用户信息失败:', error);
            ElMessage.error('保存失败，请稍后再试');
        }else{            
            ElMessage.success('保存失败');
        }    
    }
}
let getUserInfo=async()=>{
    return new Promise(async(resolve,reject)=>{
        let user_data=await userInfo({userId:loginStore.userId})
        loginStore.setUserInfo(user_data)
        resolve(true)
    })
    
}
let open_edit_password_dialog=()=>{
    edit_password_dialog_ref.value.dialogVisable=true
}
let open_edit_phone_dialog=()=>{
    edit_phone_dialog_ref.value.dialogVisable=true
}
let name_focus=()=>{
    nickname_edit.value=true
}
let nicknameEdit=()=>{
    // nickname_edit.value=true
    save_infor('no_loading')
}
let beforeUpload = (file) => {
    console.log('beforeUpload');
    
    let isImage = file.type.startsWith('image/');
    if (!isImage) {
        ElMessage.error('上传文件必须是图片！');
    }
    return isImage;
};
let loading=ref(false)
let handleFileChange=async (file, fileList) => {
    loading.value=true
    console.log(file, fileList,'handleFileChange');
    const uploadRequest = await fileChange(file, fileList);
    user.avant=uploadRequest.url
    loading.value=false
}
// let isUploading=ref(false)
// let uploadFile= ref({  // 上传文件信息
// 	name: '',
// 	size: 0,
// 	loaded: 0,
// 	percent: 0,
// 	type: '',
// 	url: ''
// })
// let uploadRequest=ref(null)
// let getUserId = () => {
// 	return loginStore.userInfo.userId || '0'
// }
// let isFileChanging =ref(false)
//  // 防抖函数
//  let debounce=(func, delay)=>{
//     let timeout;
//     return function(...args) {
//         const context = this;
//         clearTimeout(timeout);
//         timeout = setTimeout(() => func.apply(context, args), delay);
//     };
// }
// // 处理文件上传
// let handleFileChange =debounce(async (file, fileList) => { 
//     console.log(file, 'handleFileChange');
//     if (isFileChanging.value) return; // 防止重复触发
//     isFileChanging.value = true;

//     try {
//         // 检查文件类型，只允许图片格式
//         const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
//         if (!validImageTypes.includes(file.raw.type)) {
//             ElMessage.error('请上传有效的图片文件（JPEG, PNG, GIF, BMP, WEBP）');
//             return;
//         }

//         // 检查文件大小（最大5MB）
//         let maxSize = 5 * 1024 * 1024; // 5MB
//         if (file.raw.size > maxSize) {
//             ElMessage.error('文件大小不能超过5MB');
//             return;
//         }

//         isUploading.value = true;

//         // 设置上传文件信息
//         uploadFile.value = {
//             name: file.raw.name,
//             size: file.raw.size,
//             loaded: 0,
//             percent: 0,
//             type: file.raw.type
//         };

//         // 调用dubbing API获取OSS上传凭证
//         let response = await dubbing({ userId: getUserId(), fileType: file.raw.type });

//         // 去掉文件名的后缀
//         let fileNameWithoutExt = file.raw.name.split('.').slice(0, -1).join('.');

//         let formData = new FormData();
//         // 添加OSS需要的参数
//         formData.append('OSSAccessKeyId', response.accessKeyId);
//         formData.append('policy', response.policy);
//         formData.append('signature', response.signature);
//         formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.raw.name}`);
//         formData.append('file', file.raw); // 使用file.raw获取原始文件

//         // 使用XHR上传文件以跟踪进度
//         let xhr = new XMLHttpRequest();
//         uploadRequest.value = xhr;

//         // 设置进度监听
//         xhr.upload.onprogress = (e) => {
//             if (e.lengthComputable) {
//                 uploadFile.value.percent = Math.round(e.loaded / e.total * 100);
//                 uploadFile.value.loaded = e.loaded;
//             }
//         };

//         // 上传完成后的处理
//         xhr.onload = async () => {
//             try {
//                 if (xhr.status >= 200 && xhr.status < 300) {
//                     const userId = getUserId();

//                     // 调用callbackOss接口
//                     const callbackResponse = await callbackOss({
//                         userId: userId,
//                         materialName: fileNameWithoutExt,
//                         ossPath: response.key.replace(/[^\/]+$/, '') + file.raw.name,
//                         fileSize: String(file.raw.size),
//                         fileExtension: file.raw.name.split('.').pop(), // 获取文件扩展名
//                         tagNames: '1',
//                         materialType: 'image',
//                         isPrivate: '1',
//                         storage_path: `/material/${userId}/${file.raw.name}`
//                     });

//                     // 更新文件信息
//                     uploadFile.value = {
//                         ...uploadFile.value,
//                         name: callbackResponse.filename || file.raw.name,
//                         url: callbackResponse.url,
//                         percent: 100,
//                         loaded: file.raw.size
//                     };
//                     user.avant=uploadFile.url
//                     ElMessage.success('图片上传成功');

//                 } else {
//                     throw new Error(xhr.statusText || '上传失败');
//                 }
//             } catch (error) {
//                 console.error('处理错误:', error);
//                 ElMessage.error(error.message || '文件处理失败');
//             } finally {
//                 isFileChanging.value = false
//                 isUploading.value = false;
//                 uploadRequest.value = null;
//             }
//         };

//         // 错误处理
//         xhr.onerror = (error) => {
//             console.error('上传错误:', error);
//             ElMessage.error('文件上传失败');
//             isUploading.value = false;
//             uploadFile.value.percent = 0;
//             uploadRequest.value = null;
//         };

//         // 发送请求
//         xhr.open('POST', response.host, true);
//         xhr.send(formData);
//     } catch (error) {
//         console.error('处理错误:', error);
//         ElMessage.error('上传准备失败: ' + error.message);
//         isUploading.value = false;
//         uploadFile.value = {
//             name: '',
//             size: 0,
//             loaded: 0,
//             percent: 0,
//             type: ''
//         };
//         uploadRequest.value = null;
//     }
// },300)

provide('account_info', ruleForm);
watch(dialogVisable, async(newValue, oldValue) => {
   if(!newValue){
        ruleFormRef.value && ruleFormRef.value.resetFields()
        nickname_edit.value = false
   } else {
       await getUserInfo()
        // 初始化用户信息
        initUserInfo();
   }
});
// 组件挂载时初始化
onMounted(() => {
    initUserInfo();
});
defineExpose({
    dialogVisable
})
</script>
<style lang="scss">
.account_info_dialog{
    position: relative;
    box-sizing: border-box;
    padding: 0;
    .el-dialog__header{
        display: none;
    }
    .account_info_close{
        width: 13px;
        height: 13px;
        position: absolute;
        top: 11px;
        right: 11px;
        cursor: pointer;
    }
    .el-dialog__body{
        padding: 58px 55px 97px 48px;
        width: 100%;
        box-sizing: border-box;
        .account_avant_box{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 42px;
            .account_avant_img{
                width: 80px;
                height: 80px;
                margin-bottom: 12px;
                position: relative;
                background-color: #f5f5f5;
                border-radius: 50%;
                img{
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                }
                .account_avant_img_edit{
                    position: absolute;
                    width: 19px;
                    height: 19px;
                    background: #D4D4D4;
                    box-shadow: 0px 0 50px 1px rgba(0,0,0,0.15);
                    border-radius: 12px 12px 12px 12px;
                    top: 1px;
                    right: 0;
                    z-index: 2;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    img{
                        width: 9px;
                        height: 10px;
                    }
                }
            }
            .account_avant_name{
                font-weight: bold;
                font-size: 20px;
                color: #252525;
                line-height: 26px;
            }
        }
        .el-form{
            margin-bottom: 70px;
            .el-form-item{
                margin-bottom: 16px;
               .el-input{
                    background: #FFFFFF;
                    border-radius: 4px;
                    border: 1px solid var(--el-border-color);
                 
                    padding: 5px 8px;
                    .el-input-group__prepend{
                        background-color: transparent;
                        border-right: none;
                        box-shadow: none;
                        line-height: 20px;
                        font-size: 14px;
                        color: rgba(0,0,0,0.45);
                        width:3em;
                        height: 20px;
                        padding: 0;
                        margin-right: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                    }
                    .el-input__wrapper{
                        border-left: none;
                        box-shadow: none;
                        line-height: 20px;
                        height: 20px;
                        padding-left: 0;
                        .el-input__inner{
                            font-size: 14px;
                            color: #353D49;
                            line-height: 20px;
                            height: 20px;
                        }
                    }
                    .el-input-group__append{
                        background-color: transparent;
                        cursor: pointer;
                        border-right: none;
                        box-shadow: none;
                        padding: 0;
                        font-size: 14px;
                        color:#0AAF60;
                        line-height: 20px;
                        height: 20px;
                    }
                    &.is-disabled{
                        .el-input__wrapper{
                            background-color: transparent;
                            .el-input__inner{
                                color: #D3D3D2;
                            }
                        }
                    }
               }
               &:hover{
                .el-input{
                    border-color:#0AAF60; ;
                }
               
               }
            }
           
        }
        .account_info_btns{
            button{
                width: 100%;
                height: 44px;
                background: #0AAF60;
                box-shadow: 0px 0 50px 1px rgba(0,0,0,0.05);
                border-radius: 4px;
                box-sizing: border-box;
                font-size: 14px;
                border: none;
                color: #FFFFFF;
                cursor: pointer;
            }
        }
    }
}
</style>