<template>
    <div class="account_img_box">
            <div class="account_img_imgs">
                <img :src="user.avatar!=''?user.avatar:avatar" alt="">
            </div>
            <div class="account_img_imgs_text">
                <div class="account_img_imgs_name">
                    {{user?.name}}
                </div>
                <div class="account_img_imgs_info">
                    <div class="account_img_imgs_info_item">
                        <span class="account_img_imgs_info_item_label">ID:</span>
                        <span class="account_img_imgs_info_item_value"> {{user.id}}</span>
                    </div>
                    <!-- <div class="account_img_imgs_info_item" v-if="user.sign&&user.sign!=''">
                        <span class="account_img_imgs_info_item_label">{{user.sign}}</span>
                        <span class="account_img_imgs_info_item_imgs">
                            <img :src="getSignImg(user.sign)" alt="" v-if="user.sign">
                        </span>
                    </div> -->
                </div>
            </div>
        </div>
</template>
<script setup>
import {reactive,ref,defineExpose,watch} from 'vue'
import medal from '@/assets/images/account/medal.svg'
import avatarSign  from '@/assets/images/account/avatar_sign.png'
import avatarSvipSign  from '@/assets/images/account/avatar_svip_sign.svg'
import expireImage from '@/assets/images/account/expire.svg'
import avatar from '@/assets/images/account/avatar.png'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
let user=reactive({
    avatar:'',
    name:'用户名称',
    id:'123456',
    sign:'',
    medal:medal
})

let getSignImg=(sign)=>{
    console.log(sign,'getSignImg');
    
  let result=''
  switch (sign) {
    case 0:
      result=''
      break;
    case 'VIP':
      result=avatarSign
      break;
    case 'SVIP':
      result=avatarSvipSign
      break;
    case '已过期':
        result=expireImage
        break;
    default:
      break;
  }
  return result
}
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
let expire=ref(false)
defineExpose({
    user
})
watch(loginStore, (newValue, oldValue) => {
    if(loginStore&&loginStore?.loginData){
        
        user.name= loginStore?.userInfo?.nickName ||loginStore?.userInfo?.mobile||''
        user.id=loginStore?.userInfo?.userId
        if(loginStore?.memberInfo?.level?.level){
            switch (loginStore?.memberInfo?.level?.level) {
                case 0:
                    user.sign=''
                    break;
                case 1:
                    user.sign='VIP'
                    break;
                case 2:
                    user.sign='SVIP'
                    break;
                default:
                    break;
            }
        }
        user.avatar=loginStore?.userInfo?.avatar||''
        
    }
    if(loginStore&&loginStore?.userInfo){
        if(loginStore?.memberInfo&&loginStore?.memberInfo?.level&&loginStore?.memberInfo?.level?.end_time){
            expire.value=isExpired(loginStore?.memberInfo?.level?.end_time ||'')
            if(expire.value){
                 user.sign='已过期'
            }
        }
    }
    
}, { deep: true,immediate:true });
</script>
<style lang="scss" scoped>
.account_img_box{
        display: flex;
        .account_img_imgs{
            width: 56px;
            height: 56px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
            // background-color: rgba(127, 81, 43, 1);
            img{
                width: 100%;
                border: none;
            }
        }
        .account_img_imgs_text{
            padding-top: 6px;
            .account_img_imgs_name{
                font-size: 16px;
                color: #353D49;
                line-height: 22px;
                margin-bottom: 4px;
                font-weight: 600;
           } 
           .account_img_imgs_info{
                display: flex;
                align-items: center;
                .account_img_imgs_info_item{
                    display: flex;
                    align-items: center;
                    margin-right: 12px;
                    line-height: 17px;
                    font-size: 12px;
                    .account_img_imgs_info_item_label{
                        color: #353D49;
                    }
                    .account_img_imgs_info_item_value{
                        color: #353D49;
                    }
                    .account_img_imgs_info_item_imgs{
                        width: 17px;
                        height: 13px;
                        margin-left: 4px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
           }
        }
    }
</style>