<template>
    <div class="account_row">
        <div class="account_row_charact_space">
            <div class="account_row_charact_space_item charact_item">
                <div class="account_row_charact_space_item_label ">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/surplus_charact.png" width="12px" height="12px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        剩余字符
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <span>{{getCharacter()}}</span>
                </div>
            </div>
            <div class="account_row_charact_space_item" @click="go_my_space">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/my_space.png" width="13px" height="13px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        我的空间
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
        </div>
        <div class="account_row_charact_space account_row_info">
            <div class="account_row_charact_space_item">
                <div class="account_row_charact_space_item_label" @click="handle_account_info_click">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/account_info.png" width="13px" height="13px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        账号信息
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
            <div class="account_row_charact_space_item" @click="go_usage_statistics">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/usage_statistics.png" width="13px" height="12px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        使用统计
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
            <!-- <div class="account_row_charact_space_item" @click="go_creator_community">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/association.png" width="14px" height="13px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        创作者社群
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div> -->
            <div class="account_row_charact_space_item" @click="go_agreement">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/policy.png" width="12px" height="13px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        平台政策
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
            <div class="account_row_charact_space_item" @click="open_redemption">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/redemption.svg" width="16px" height="16px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        CDK兑换码
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
            <div class="account_row_charact_space_item" @click="help_feedback">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/help.png" width="15px" height="12px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        帮助与反馈
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
            <div class="account_row_charact_space_item" @click="delete_account">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/delete_account.svg" width="16px" height="16px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        注销账号
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="@/assets/images/account/more.png" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
        </div>
        <div class="account_row_charact_space account_row_loginout" @click="loginout">
            <div class="account_row_charact_space_item">
                <div class="account_row_charact_space_item_label">
                    <div class="account_row_charact_space_item_icon">
                        <img src="@/assets/images/account/loginout.png" width="13px" height="13px" alt="">
                    </div>
                    <div class="account_row_charact_space_item_title">
                        退出登录
                    </div>
                </div>
                <div class="account_row_charact_space_item_value">
                    <img src="" class="account_row_charact_space_item_value_more" alt="">
                </div>
            </div>
        </div>
      
    </div>
</template>
<script setup>
import {reactive,ref,defineExpose,defineEmits,inject} from 'vue'
import { useRouter } from 'vue-router'
import { useloginStore } from '@/stores/login'
import {accAdd,accSub,accMul,accDiv} from '@/utils/accuracy'
import { useUmeng } from '@/utils/umeng/hook'

const loginStore = useloginStore()
const router = useRouter()
const umeng = useUmeng()

let sumSurplus = (a = 0, b = 0) => {
	return accSub(a, b)
}
let getCharacter=()=>{
    let current = sumSurplus(((loginStore.memberInfo && loginStore.memberInfo.total && loginStore.memberInfo.total['Premium&Deluxe']) || 0), ((loginStore.memberInfo && loginStore.memberInfo.current && loginStore.memberInfo.current['Premium&Deluxe']) || 0))
    console.log(loginStore.memberInfo,'getCharacter');
    return current
}
let accountInfoClick = inject('accountInfoClick'); 
let creator_community = inject('creator_community'); 
let login_out=inject('loginout'); 
let redemption_code=inject('redemption_code'); 
let remove_account=inject('remove_account'); 
let handle_account_info_click=()=>{
    accountInfoClick()
}
let go_usage_statistics=()=>{
    router.push('/usageStatistics')
    
}
let go_my_space=()=>{
    router.push('/mySpace/myWorks')
    
}
let help_feedback=()=>{
    window.open('https://xcntpmhkr2sp.feishu.cn/share/base/form/shrcnXloO6NKhcAFPcht1n7SjEP', '_blank')
    
}
let go_agreement=()=>{
    router.push({ path: '/agreement' })
}

let go_creator_community=()=>{
    creator_community()
    
}
let open_redemption=()=>{
    redemption_code()
}
//注销账号
let delete_account=()=>{
    remove_account()
}
let loginout=()=>{
    umeng.trackEvent(
      '用户操作', 
      '点击退出登录', 
      `用户ID: ${loginStore.userId || '未知'}`, 
      ''
    )
    
    login_out()
}
</script>
<style lang="scss" scoped>
.account_row{
    display: flex;
    flex-direction: column;
    padding:24px 12px 0;
    width: 100%;
    box-sizing: border-box;
    .account_row_charact_space{
        border-bottom: 1px solid #000;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        .account_row_charact_space_item{
            display: flex;
            margin-bottom: 16px;
            line-height: 21px;
            align-items: center;
            cursor: pointer;
            .account_row_charact_space_item_label{
                display: flex;
                align-items: center;
                .account_row_charact_space_item_icon{
                    width: 15px;
                    height: 13px;
                    margin-right: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .account_row_charact_space_item_title{
                    font-size: 14px;
                    color: #353D49;
                }
            }
            .account_row_charact_space_item_value{
                margin-left: auto;
                span{
                    font-size: 17px;
                    color: #1D2129;
                }
                .account_row_charact_space_item_value_more{
                    width: 5px;
                    height: 8px;
                }
            }
            &.charact_item{
                cursor: default;
            }
            &:last-child{
                margin-bottom: 0;
            }
            
        }
    }
    .account_row_info{
        padding-top: 16px;
        padding-bottom: 12px;
    }
    .account_row_loginout{
        padding-top: 16px;
        border-bottom: none;
        padding-bottom: 0;
    }
}
</style>
