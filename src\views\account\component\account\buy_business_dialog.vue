<template>
    <el-dialog v-model="dialogVisible" class="buy_business_dialog" width="888px" :show-close="false"   :close-on-click-modal="false" v-loading="loading">
        <template #header>
            <payCloseDialog :top="-96" @close="close_dialog" ref="pay_close_dialog_ref"></payCloseDialog>
            <div class="buy_business_dialog_header">
                <div class="buy_business_dialog_close" @click="close">
                    <img src="@/assets/images/account/buy_digital_dialog_close.svg" alt="">
                </div>
                <div class="buy_business_dialog_title">
                    <div class="buy_business_dialog_title_avator">
                        <div class="buy_business_dialog_title_avator_img">
                            <img :src="loginStore?.userInfo?.avatar&&loginStore?.userInfo?.avatar!=''?loginStore?.userInfo?.avatar:avator" alt="">
                        </div>
                        <div class="buy_business_dialog_title_avator_sign">
                            <img :src="get_sign(member_status)" alt="">
                        </div>
                    </div>
                    <span class="buy_business_dialog_title_name">商配音色包</span>
                    <div class="buy_business_dialog_title_label">
                        <img src="@/assets/images/account/buy_business_dialog_title_label.svg" alt="">
                    </div>
                </div>
            </div>
        </template>
        <template #default>
            <div class="buy_business_profile">
                <div class="buy_business_profile_tabs">
                    <span class="buy_business_profile_tabs_item" v-for="(item,index) in buy_business_profile_tabs" :key="index" :class="current_tab.value==item.value?'current':''" @click="tab_change(item)">{{ item.label }}</span>
                </div>
                <div class="buy_business_profile_content" >
                    <div class="buy_business_profile_content_item" v-for="(item,index) in current_tab.priceList" :key="index" @click="choose_thali(item)" :class="[current_tab.value, item.id === current_thali ? 'current' : '']">
                       <img :src="get_img(current_tab.value,index+1)" class="buy_business_profile_content_item_symbel" alt="" />
                        <div class="buy_business_profile_content_item_content">
                            <span class="buy_business_profile_content_item_info">{{item.info}} <b>{{ item.info_label }}</b></span>
            
                            <!-- {{item.volume}} -->
                            <div class="buy_business_profile_content_item_price_discount">
                                <span class="buy_business_profile_content_item_price">{{parseFloat(((item.discountPrice&&item.discountPrice>0)?item.discountPrice:item.price))}}</span>
                                <div class="buy_business_profile_content_item_discount" v-if="item.discountPrice&&item.discountPrice<item.price">
                                    <span>￥{{parseFloat(item.price)}}</span>
                                    <img :src="getDiscountImg(current_tab.value)" alt="">
                                </div>
                            </div>
                        </div>
                        <span class="buy_business_profile_content_item_character">{{item.volume}}</span>
                    </div>
                </div>
                 <div class="buy_business_describe">
                    <div class="buy_describe">
                        <h5> 购买说明</h5>
                        <p> 
                            1.【账号绑定】不限平台会员身份使用，可单独开通；<br/>
                            2.【有效期】自购买日起生效，到期自动终止；<br/>
                            3.【字数限制】含定额合成字符，实时扣减，余额不足时暂停服务；<br/>
                            4.【叠加规则】重复购买则有效期与字数累计叠加（有效期从末次购买计算）；<br/>
                            5.【协议确认】支付即视为同意 <span @click="go_user_agreement">《 用户协议》</span>和<span @click="go_privacy_agreement">《隐私协议》</span> ；<br/>
                            6.【退款政策】即时生效数字商品，依据法规不支持无理由退款。
                        </p>
                    </div>
                    <div class="buy_describe_paymethod">
                        <ul>
                            <li :class="current_paymethod=='alipay'?'current':''" @click="paymethod('alipay')">支付宝支付</li>
                            <li :class="current_paymethod=='weixin'?'current':''" @click="paymethod('weixin')">微信支付</li>
                        </ul>
                         <div class="buy_describe_paymethod_img">
                            <QRCode :value="pack_info.qrcode" :size="140" />
                         </div>
                        <span class="buy_describe_paymethod_tip">
                            <template v-if="current_paymethod == 'weixin'">微信</template><template v-else>支付宝</template>扫码支付<i>¥</i><span class="buy_describe_paymethod_tip_price">
                                <!-- {{user.price}} -->
                            {{  pack_info.price }}
                            </span>
                        </span>
                        <button class="buy_describe_finish_pay" @click="finish_pay" v-if="pack_info.finish_pay=='success'">已完成付款</button>
                    </div>
                </div>
            </div>
        </template>
    </el-dialog>
    <!-- 支付状态弹窗 -->
    <payStatusDialog ref="pay_status_dialog_ref" @status="order_status"></payStatusDialog>
</template>

<script setup>
import { ref, defineExpose,nextTick ,watch} from 'vue';
import svip from '@/assets/images/account/avatar_svip_sign.svg'
import vip from '@/assets/images/account/avatar_sign.png'
import expireImage from '@/assets/images/account/expire.svg'
import avator from "@/assets/images/soundStore/package_buy_header_avator.png"
import { useloginStore } from '@/stores/login'
import {queryOrder,packageByCode,productQuery,queryVoiceWithPackage}  from '@/api/soundStore.js'
import payStatusDialog from "@/components/payDialog/pay_status_dialog.vue"
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
import { useRouter } from 'vue-router'
import QRCode from 'qrcode.vue';
import payCloseDialog from "@/components/payDialog/pay_close_dialog.vue"
import { useSoundStore } from '@/stores/modules/soundStore.js'
import packageBuyProfileDiscount from '@/assets/images/soundStore/buy_business_dialog_item_content_discount.svg' 
import packageBuyProfileDiscountAttain from '@/assets/images/soundStore/buy_business_dialog_item_content_discount_attain.svg'
import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
import expirImage from "@/assets/images/account/member_pay_user_info_expire.png"
const { fetchUserBenefits } = useUserBenefits()
let router = useRouter()
import axios from 'axios';
let loginStore = useloginStore()
let soundStore = useSoundStore()
let dialogVisible=ref(false)
let expire=ref(false)
let loading=ref(false)
let current_paymethod=ref('alipay')
let member_status=ref('')
let member=ref([
])
let pack_info = ref({
   
})
let pay_status_dialog_ref=ref(null)
let pay_close_dialog_ref=ref(null)
let order_params=ref({})
let finish_pay=()=>{
    close_dialog()
}
let buy_business_profile_tabs=ref([
    {
        label: '基础版套餐包',
        value: 'base',
        list:[]
    },
    {
        label: '进阶版套餐包',
        value: 'advanced',
        list:[]
    }
])
let current_tab=ref({})
let discountImages={
    base:packageBuyProfileDiscount,
    advanced:packageBuyProfileDiscountAttain
}
let close=()=>{
    pay_close_dialog_ref.value.pay_close_show=true
}
let close_dialog=()=>{
    dialogVisible.value=false
}
let get_sign=(status)=>{
    let result=''
    switch (status) {
        case 0:
            result='' 
            break;
        case 1:
            result=vip
            break;
        case 2:
            result=svip
            break;
        case 'expire':
            result=expirImage
        default:
            break;
    }
   
    if(expire.value){
        result=expireImage
    }
    return result
}
let current_thali=ref(0)
let current_thali_obj=ref({})
//选择套餐
let choose_thali=(data)=>{
    current_thali.value=data.id
    current_thali_obj.value=data
    update_code()
    startPolling()
}
let startPolling = () => {
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value); // 清除已有的定时器
    }
    pollingInterval.value = setInterval(checkPaymentStatus, 3000);
    // pollingStartTime.value = Date.now()
};

let update_code=async()=>{
    return new Promise((resolve, reject) => {
        
    packageByCode({ paymentType: 'PURCHASE', planId: current_tab.value.id,userId:loginStore.userId,priceId:current_thali.value,quantity: 1 })
      .then(data1 => {
        pack_info.value.qrcode = data1.resp_data.counter_url;
        pack_info.value.price =get_price(data1.resp_data.total_amount,100)
        order_params.value = data1;
        resolve(true);
      })
      .catch(err => {
        reject(err);
      });
  });
}
let get_price=(a,b)=>{
    return accDiv(a,b)
}
let checkPaymentStatus = async () => {
    try {
     
        // 调用后端接口查询支付状态
        let data = await queryOrder({outOrderNo:order_params.value.resp_data.out_order_no});
       
        console.log(data.resp_data.order_status, 5555666);
        // 假设返回的状态字段为 status
        // pack_info.finish_pay = data.code; 
        // 
        // 如果支付成功或者失败或者过期，停止轮询
        if (data.resp_data.order_status == 2||data.resp_data.order_status == 3||data.resp_data.order_status == 4) {
            clearInterval(pollingInterval.value);
            if(data.resp_data.order_status == 4){
                update_code()
                console.log("已过期，重新获取验证码");
            } else {
                if(data.resp_data.order_status == 2){
                    pack_info.finish_pay='success'
                    // await notify(order_params.value) 
                }else{
                    pack_info.finish_pay='fail' 
                }
                pay_status_dialog_ref.value.status = pack_info.finish_pay; // 更新支付状态
                pay_status_dialog_ref.value.dialogVisible = true
            }
            return; // 退出函数
        } 
    } catch (error) {
        console.error('查询支付状态失败:', error);
    } 
};
let order_status = async (status) => {
    console.log(status,'order_status');

    pack_info.value.finish_pay=status

    // 如果支付成功，刷新用户权益信息
    if (status === 'success') {
        await fetchUserBenefits()
          
    }
}
let pollingInterval = ref(null); // 轮询定时器
defineExpose({
    dialogVisible,
});
let dateValue=(date)=>{
    return  date.trim().split(/\s+/).join("-")
}
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
let getList = async () => {
  loading.value = true;
  try {
    let data = await queryVoiceWithPackage({ voiceType: "SFT", inUse: "5", userId: loginStore.userId });
    let all_data = setPackage(data);
    let targets = [
      { type: '基础版套餐包', label: '基础版套餐包' },
      { type: '进阶版套餐包', label: '进阶版套餐包' }
    ];

    for (const { type, label } of targets) {
      let dataIndex = all_data.findIndex(item => item.type === type);
      let tabIndex = buy_business_profile_tabs.value.findIndex(item => item.label === label);
      if (dataIndex !== -1 && tabIndex !== -1) {
        Object.assign(buy_business_profile_tabs.value[tabIndex], all_data[dataIndex]);
        buy_business_profile_tabs.value[tabIndex].list = buy_business_profile_tabs.value[tabIndex].data;
        buy_business_profile_tabs.value[tabIndex].priceList= await getProduct(buy_business_profile_tabs.value[tabIndex].list.length, type);
      }
    }

  
  } finally {
    loading.value = false;
    tab_change(buy_business_profile_tabs.value[0]);
  }
};
let getProduct=async(length,type)=>{
    return new Promise(async(resolve, reject) => {
        let data=await productQuery({ type})
        data[0].priceList.map((item)=>{
            if(type!='进阶版套餐包'){
                item.info=`${length}款至臻音色`
                item.info_label=`（专业质）`
            }else{
                item.info=`${length}款至臻音色`
                item.info_label=` (专业级+央视级)）`
            }
        })
        resolve(data[0].priceList)
    })
    
}
let images = import.meta.glob('@/assets/images/soundStore/*.svg', { eager: true });

let get_img = (type, index) => {
  let fileName = `/src/assets/images/soundStore/package_buy_profile_${type}_${index}.svg`;
   console.log(images,fileName,'images');
  return images[fileName].default;
}
let getDiscountImg=(type)=>{
    return discountImages[type];
}
let tab_change=async(data)=>{
    adjustDialogPosition()
    current_tab.value=data
    choose_thali(current_tab.value.priceList[0])  
   
}

let setPackage=(data)=>{
    let list_pack_data=[]
        list_pack_data=data.reduce((acc, item) => {
            let packageInfo= item.packageInfo
            const type = packageInfo.type; 
    
            
            let group = acc.find(g => g.platformNickname === type);
            if (!group) {
                group = {...packageInfo,packageType:item.packageType, platformNickname: type, data: [],  package: true, };
                acc.push(group);
            }
            let item_data= { ...item }
            delete item_data.packageInfo; 
            group.data.push(item_data);
            group.area=item.recommendTags+item.sceneCategory
            let keys=['gender','ageGroup','sceneCategory','recommendTags','membershipGrade']
            keys.map((item1)=>{
                if(item[item1]){
                    group[item1]=item[item1]
                }else{
                    group[item1]=''
                }
                
            })
            console.log(acc,group,type,999);
            return acc;
        }, []);
        list_pack_data.map(group => {
            // 判断 group.data 中所有项目的 isBuy 是否都是 1
            const allBought = group.data.every(item => item.isBuy === 1);
            group.is_buy = allBought ? 1 : 0;
            group.data.sort((a, b) => {
                //添加的 price 排序逻辑
                if (a.price !== b.price) {
                    return b.price - a.price; // price 倒序
                }
                // 最后按 recommendDegree 排序，值小的在前
                return a.recommendDegree - b.recommendDegree;
            })
        });
        console.log(list_pack_data,'list_pack_data');
        
        // 找到“进阶版套餐包”组
        const advanceIndex = list_pack_data.findIndex(item => item.type === '进阶版套餐包');
        if (advanceIndex !== -1) {
        // 收集所有非“进阶版套餐包”的数据，扁平化合并成一个数组
        const otherData = list_pack_data
            .filter(item => item.type !== '进阶版套餐包')
            .flatMap(item => item.data);

        // 深拷贝“进阶版套餐包”的数据和其他数据，合并后赋值
        list_pack_data[advanceIndex].data = [
            ...JSON.parse(JSON.stringify(list_pack_data[advanceIndex].data)),
            ...JSON.parse(JSON.stringify(otherData))
        ];
        }
        let order = ['基础套餐包', '进阶版套餐包'];
        list_pack_data.sort((a, b) => order.indexOf(a.type) - order.indexOf(b.type));
        soundStore.setAllPackage(list_pack_data)
        return list_pack_data
}
let go_user_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'user' } })
}
let go_privacy_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'privacy' } })
}
let paymethod=(method)=>{
    current_paymethod.value=method
}
let rate=1
let adjustDialogPosition=()=>{
  nextTick(() => {
    const dialogEl = document.querySelector('.buy_business_dialog');
    if (!dialogEl) return;

    const dialogHeight = dialogEl.offsetHeight;
    const windowHeight = window.innerHeight;
    let appHeight = document.getElementById('app').clientHeight;
    let  scale = windowHeight / 953;
    let top=0
    // 计算居中 top，取整避免子像素
    top = Math.round((windowHeight - dialogHeight) / 2);
    if(windowHeight>=953){
        top = Math.round((windowHeight - dialogHeight) / 2);
    }else{
        top = Math.round((windowHeight/(windowHeight/953) - dialogHeight) / 2);
    }
    if(top<0){
        top=0
}
console.log('top',windowHeight/(windowHeight/953),dialogHeight,top)
    // 设置 top，取消 transform
    dialogEl.style.top = `${top*scale*(appHeight/953)}px`;
    dialogEl.style.transform = 'none';
    dialogEl.style.transform = `scale(${appHeight/953})`;
    dialogEl.style.margin = '0 auto'; // 保持水平居中
    let marginTop=40*rate
    dialogEl.style.marginTop= `${marginTop}px`;
  });
}
let getBodyScale=()=>{
  const el = document.querySelector('#app');
  if (!el) return 1;
  const transform = getComputedStyle(el).transform;
  if (!transform || transform === 'none') return 1;
  const values = transform.match(/matrix\((.+)\)/)[1].split(', ');
  return parseFloat(values[0]); // scale
}
watch(()=>dialogVisible.value, async(newValue, oldValue) => {
   if(!newValue){

    
      current_paymethod=ref('alipay')
      clearInterval(pollingInterval.value)
   }else{
    if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.level){
        member_status.value=loginStore.memberInfo.level.level
    }
    if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.end_time){
        let effective_date=dateValue(loginStore.memberInfo.level.end_time ||'');
        expire.value=isExpired(effective_date+' 23:59:59')
        if(expire.value){
            member_status.value='expire'
        }
    }
    rate=getBodyScale()
    await nextTick();
    getList();
    // startPolling()
    // expire.value=isExpired(loginStore.userInfo?.expireTime ||'')
   }
},{immediate:true,deep:true});
</script>

<style lang="scss">
.buy_business_dialog {
    padding: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: visible;
    // overflow: hidden;
    background: linear-gradient(180deg, #DFFFDF 0%, #FFFFFF 30.57%);
    border-radius: 4px;
    .el-dialog__header{
        padding:36px 32px 0;
        height: 84px;
        position: relative;
        margin-bottom: 32px;
        .buy_business_dialog_close{
            width: 20px;
            height: 20px;
            position: absolute;
            right: 32px;
            top: 16px;
            cursor: pointer;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .buy_business_dialog_title{
            display: flex;
            align-items: center;
            .buy_business_dialog_title_avator{
                width: 48px;
                height: 48px;
                position: relative;
                margin-right: 16px;
                .buy_business_dialog_title_avator_img{
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .buy_business_dialog_title_avator_sign{
                    position: absolute;
                    right: -2px;
                    bottom: 1px;
                    z-index: 1;
                    width: 14px;
                    height: 14px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: #EEEEEE;
                    border-radius: 7px;
                    img{
                        width: 10px;
                        height: 10px;
                    }
                }
          
            }
            .buy_business_dialog_title_name{
                font-size: 24px;
                line-height: 22px;
                color: rgba(0, 0, 0, 0.85);
                margin-right: 9px;
            }
            .buy_business_dialog_title_label{
                width: 86px;
                height: 20px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
    .el-dialog__body{
        padding:0 32px;
        .buy_business_profile{
            position: relative;
            width: 100%;
            margin-bottom: 29px;
            color: #5D1A64;
            .buy_business_profile_tabs{
                display: flex;
                align-items: center;
                padding-left: 8px;
                margin-bottom: 32px;
                .buy_business_profile_tabs_item{
                    margin-right: 32px;
                    cursor: pointer;
                    font-size: 17px;
                    line-height: 24px;
                    letter-spacing: -1px;
                    color: rgba(0, 0, 0, 0.45);
                   
                    &.current{
                        color: #232528;
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
            .buy_business_profile_content{
                width: 100%;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                margin-bottom: 42px;
                .buy_business_profile_content_item{
                    width: 264px;
                    height: 178px;
                    padding: 0;
                    margin-right: 15px;
                    box-sizing: border-box;
                    cursor: pointer;
                    display: flex;
                    flex-direction: column;
                    position: relative;
                    
                    border-radius: 12px;
                    .buy_business_profile_content_item_symbel{
                        width: 70px;
                        height: 32px;
                        position: absolute;
                        top: -12px;
                        left: -1px;
                        z-index: 1;
                    }
                    
                    .buy_business_profile_content_item_content{
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;
                        background-color: #fff;
                        padding: 33px 24px 19px 28px;
                        border-top-left-radius: 12px;
                        border-top-right-radius: 12px;
                        .buy_business_profile_content_item_info{
                            margin-bottom: 9px;
                            font-style: italic;
                            font-size: 19px;
                            line-height: 24px;
                            letter-spacing: -1px;
                            
                            b{
                                font-weight: normal;
                            }
                        }
                        .buy_business_profile_content_item_price_discount{
                            display: flex;
                            align-items: baseline;
                            .buy_business_profile_content_item_price{
                                font-family: 'Outfit';
                                font-size: 42px;
                                line-height: 54px;
                                letter-spacing: -1px;
                                color: #FB2A01;
                            }
                            .buy_business_profile_content_item_discount{
                                margin-left: 5px;
                                display: flex;
                                align-items: center;
                                
                                span{
                                    margin-right: 8px;
                                    font-size: 22px;
                                    line-height: 31px;
                                    text-decoration-line: line-through;
                                    color: #B2B2B3;
                                }
                                button{
                                    width: 64px;
                                    height: 23px;
                                }
                            }
                        }
                    }
                    .buy_business_profile_content_item_character{
                        padding-left: 29px;
                        height: 39px;
                        display: flex;
                        align-items: center;
                        font-style: italic;
                        font-size: 18px;
                        line-height: 22px;
                        text-align: center;
                        font-weight: 500;
                        color: #54300A;
                        border-bottom-right-radius: 12px;
                        border-bottom-left-radius: 12px;
                    }       
                    &.base{
                        background-color: #FBE8A5;
                        border: 1px solid #FFE5A1;
                        .buy_business_profile_content_item_content{
                            .buy_business_profile_content_item_info{
                                color: #54300A;
                            }
                        }
                        &.current{
                           border: 2px solid #D8B14E;
                           .buy_business_profile_content_item_symbel{
                                left: -2px;
                           }
                        }
                    }
                    &.advanced{
                        border: 1px solid #DFB1F3;
                        background-color: #F2D3FF;
                        .buy_business_profile_content_item_content{
                            padding:31px 15px 22px 20px;
                            .buy_business_profile_content_item_info{
                                color: #490974;
                                b{
                                    font-style: italic;
                                    font-size: 14px;
                                    line-height: 17px;
                                    letter-spacing: -1px;
                                }
                            }
                            .buy_business_profile_content_item_price_discount{
                                .buy_business_profile_content_item_price {
                                    color:#AD18F2;
                                }
                            } 
                        }
                        .buy_business_profile_content_item_character{
                            color: #490974;

                        }
                        &.current{
                            border: 2px solid #B7B7FF;
                            .buy_business_profile_content_item_symbel{
                                left: -2px;
                           }
                        }
                    }
                   
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
            .buy_business_describe{
                display: flex;
                width: 100%;
                padding-top: 2px;
                .buy_describe{
                    display: flex;
                    flex-direction: column;
                    h5{
                        margin: 0;
                        font-size: 16px;
                        line-height: 22px;
                        letter-spacing: -0.02em;
                        margin-bottom: 13px;
                        color: #353D49;
                    }
                    p{
                        margin: 0;
                        font-size: 14px;
                        line-height: 30px;
                        line-height: 32px;
                        color: #000000;
                        color: #353D49;
                        span{
                            color: rgb(24,144,255);
                            cursor: pointer;
                        }

                    }
                }
                .buy_describe_paymethod{
                    margin-top: 2px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: 216px;
                    margin-left: auto;
                    padding-right: 27px;
                    ul{
                        display: flex;
                        align-items: center;
                        margin-bottom: 12px;
                        li{
                            box-sizing: border-box;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 75px;
                            height: 26px;
                            border: 1px solid #D3D3D2;
                            border-radius: 2px;
                            color: #000000;
                            font-size: 12px;
                            margin-right: 4px;
                            position: relative;
                            cursor: pointer;
                            &:last-child{
                                margin-right: 0;
                            }
                            
                            &.current{
                                border: 1px solid #0AAF60;
                                &::before{
                                    content: '';
                                    position: absolute; 
                                    top: 0; 
                                    right: 0; 
                                    width: 0; 
                                    height: 0; 
                                    border-left: 17px solid transparent; 
                                    border-right: 17px solid #0AAF60; 
                                    border-bottom: 17px solid transparent; 
                                  
                                }
                                &::after{
                                    content: '';
                                    position: absolute; 
                                    top: 0px; 
                                    right: 0px; 
                                    width: 17px;
                                    height: 17px;
                                    background-image: url('@/assets/images/account/buy_describe_paymethod_current.png');
                                    background-color: transparent;
                                    background-repeat: no-repeat;
                                    background-size:9px 6px ;
                                    background-position: 8px 2px;
                                    z-index: 100;
                                }
                                
                            }
                        }
                    }
                    .buy_describe_paymethod_img{
                        width: 140px;
                        height: 140px;
                        margin-bottom: 19px;
                    }
                    .buy_describe_paymethod_tip{
                        display: flex;
                        line-height: 22px;
                        align-items: baseline;
                        font-size: 12px;
                        margin-bottom: 6px;
                        color: #000000;
                        i{
                            margin-left: 16px;
                            margin-right: 2px;
                        }
                        .buy_describe_paymethod_tip_price{
                            font-size: 24px;
                            color: #FF3B30;
                            font-style: italic;
                        }

                    }
                    .buy_describe_finish_pay{
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        width: 165px;
                        height: 32px;
                        background: #0AAF60;
                        border-radius: 100px;
                        border: none;
                        font-size: 12px;
                        color: #FFFFFF;
                        cursor: pointer;
                    }
                }
            }
        }
    }
    

}
</style>
