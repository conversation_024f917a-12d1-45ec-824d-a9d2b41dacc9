<template>
    <el-dialog v-model="dialogVisible" class="buy_digital_dialog" width="888px" :show-close="false"   :close-on-click-modal="false" v-loading="loading">
        <template #header>
            <payCloseDialog :top="-96" @close="close_dialog" ref="pay_close_dialog_ref"></payCloseDialog>
            <div class="buy_digital_dialog_header">
                <div class="buy_digital_dialog_close" @click="close">
                    <img src="@/assets/images/account/buy_digital_dialog_close.svg" alt="">
                </div>
                <div class="buy_digital_dialog_title">
                    <div class="buy_digital_dialog_title_avator">
                        <div class="buy_digital_dialog_title_avator_img">
                            <img :src="loginStore?.userInfo?.avatar&&loginStore?.userInfo?.avatar!=''?loginStore?.userInfo?.avatar:avator" alt="">
                        </div>
                        <div class="buy_digital_dialog_title_avator_sign">
                            <img :src="get_sign(member_status)" alt="">
                        </div>
                    </div>
                    <span class="buy_digital_dialog_title_name">数字人会员包</span>
                </div>
            </div>
        </template>
        <template #default>
            <div class="buy_digital_profile">
                <div class="buy_digital_profile_content">
                    <div class="buy_digital_profile_content_item" v-for="(item,index) in member" :key="index" @click="choose_thali(item)" :class="item.id==current_thali?'current':''" >
                        <div class="buy_digital_profile_content_item_selected" v-if="item.id==current_thali">
                            <img  src="@/assets/images/account/buy_digital_dialog_selected.svg"  alt="">
                        </div>
                        
                        <span class="buy_digital_profile_content_item_info">
                            <template v-if="item.planName=='Digital-Monthly'">月卡</template>
                            <template v-else-if="item.planName=='Digital-Biannual'">半年卡</template>
                            <template v-else>年卡</template>
                        </span>
                        <div class="buy_digital_profile_content_item_info_price_discount">
                            <span class="buy_digital_profile_content_item_info_price">
                              {{ (item.discountPrice>0&&item.discountPrice<item.price)?item.discountPrice:item.price }}
                            </span>
                            <div class="buy_digital_profile_content_item_info_discount" v-if="(item.discountPrice>0&&item.discountPrice<item.price)">
                                <i>{{ item.price }}</i>元/月
                            </div>
                        </div>
                        <span class="buy_digital_profile_content_item_info_benefit">{{item.resourceJsonCN['视频合成']}}</span>
                    </div>
                </div>
                 <div class="buy_digital_describe">
                    <div class="buy_describe">
                        <h5> 购买说明</h5>
                        <p> 
                            1.本数字人套餐仅限规定时间内使用，到期后剩余视频合成时间将自动清零<br/>
                            2.数字人套餐可叠加购买，购买后有效时间、视频合成时间都将自动叠加<br/>
                            3.本套餐仅支持数字人工作台使用<br/>
                            4.支付即代表您已阅读并同意 <span @click="go_user_agreement">《 用户协议》</span>和 <span @click="go_privacy_agreement">《隐私协议》</span> <br/>
                            5.购买后立即生效，不支持无理由
                        </p>
                    </div>
                    <div class="buy_describe_paymethod">
                        <ul>
                            <li :class="current_paymethod=='alipay'?'current':''" @click="paymethod('alipay')">支付宝支付</li>
                            <li :class="current_paymethod=='weixin'?'current':''" @click="paymethod('weixin')">微信支付</li>
                        </ul>
                         <div class="buy_describe_paymethod_img">
                            <QRCode :value="pack_info.qrcode" :size="140" />
                         </div>
                        <span class="buy_describe_paymethod_tip">
                            <template v-if="current_paymethod == 'weixin'">微信</template><template v-else>支付宝</template>扫码支付<i>¥</i><span class="buy_describe_paymethod_tip_price">
                                <!-- {{user.price}} -->
                         {{  pack_info.price }}
                            </span>
                        </span>
                        <button class="buy_describe_finish_pay" @click="finish_pay" v-if="pack_info.finish_pay=='success'">已完成付款</button>
                    </div>
                </div>
            </div>
        </template>
    </el-dialog>
    <!-- 支付状态弹窗 -->
    <payStatusDialog ref="pay_status_dialog_ref" @status="order_status"></payStatusDialog>
</template>

<script setup>
import { ref, defineExpose,nextTick ,watch} from 'vue';
import svip from '@/assets/images/account/avatar_svip_sign.svg'
import vip from '@/assets/images/account/avatar_sign.png'
import expireImage from '@/assets/images/account/expire.svg'
import avator from "@/assets/images/soundStore/package_buy_header_avator.png"
import { useloginStore } from '@/stores/login'
import {digitalPlanList} from '@/api/account.js'
import {queryOrder,packageByCode}  from '@/api/soundStore.js'
import payStatusDialog from "@/components/payDialog/pay_status_dialog.vue"
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
import { useRouter } from 'vue-router'
import QRCode from 'qrcode.vue';
import payCloseDialog from "@/components/payDialog/pay_close_dialog.vue"
import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
import expirImage from "@/assets/images/account/member_pay_user_info_expire.png"
const { fetchUserBenefits } = useUserBenefits()
let router = useRouter()
import axios from 'axios';
let loginStore = useloginStore()
let dialogVisible=ref(false)
let expire=ref(false)
let loading=ref(false)
let current_paymethod=ref('alipay')
let member_status=ref('')
let member=ref([
])
let pack_info = ref({
   
})
let pay_status_dialog_ref=ref(null)
let pay_close_dialog_ref=ref(null)
let order_params=ref({})
let finish_pay=()=>{
    close_dialog()
}
let close=()=>{
    pay_close_dialog_ref.value.pay_close_show=true
}
let close_dialog=()=>{
    dialogVisible.value=false
}
let get_sign=(status)=>{
    let result=''
    switch (status) {
        case 0:
            result='' 
            break;
        case 1:
            result=vip
            break;
        case 2:
            result=svip
            break;
        case 'expire':
            result=expirImage
        default:
            break;
    }
   
    if(expire.value){
        result=expireImage
    }
    return result
}
let current_thali=ref(0)
let current_thali_obj=ref({})
//选择套餐
let choose_thali=(data)=>{
    adjustDialogPosition() 
    current_thali.value=data.id
    current_thali_obj.value=data
    update_code()
    startPolling()
}
let startPolling = () => {
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value); // 清除已有的定时器
    }
    pollingInterval.value = setInterval(checkPaymentStatus, 3000);
    // pollingStartTime.value = Date.now()
};

let update_code=async()=>{
    return new Promise((resolve, reject) => {
    packageByCode({ paymentType: 'DIGITAL', planId: current_thali.value,userId:loginStore.userId,quantity: 1 })
      .then(data1 => {
        pack_info.value.qrcode = data1.resp_data.counter_url;
        pack_info.value.price=get_price(data1.resp_data.total_amount,100)  
        // package_buy_dialog_ref.value.pack_info.totalPrice = get_price(data1.resp_data.total_amount, 100);
        order_params.value = data1;
        resolve(true);
      })
      .catch(err => {
        reject(err);
      });
  });
}
let get_price=(a,b)=>{
    return accDiv(a,b)
}
let rate=1
let adjustDialogPosition=()=>{
  nextTick(() => {
    const dialogEl = document.querySelector('.buy_digital_dialog');
    if (!dialogEl) return;

    const dialogHeight = dialogEl.offsetHeight;
    const windowHeight = window.innerHeight;
    let appHeight = document.getElementById('app').clientHeight;
    let  scale = windowHeight / 953;
    let top=0
    // 计算居中 top，取整避免子像素
    top = Math.round((windowHeight - dialogHeight) / 2);
    if(windowHeight>=953){
        top = Math.round((windowHeight - dialogHeight) / 2);
    }else{
        top = Math.round((windowHeight/(windowHeight/953) - dialogHeight) / 2);
    }
    if(top<0){
        top=0
}
console.log('top',windowHeight/(windowHeight/953),dialogHeight,top)
    // 设置 top，取消 transform
    dialogEl.style.top = `${top*scale*(appHeight/953)}px`;
    dialogEl.style.transform = 'none';
    dialogEl.style.transform = `scale(${appHeight/953})`;
    dialogEl.style.margin = '0 auto'; // 保持水平居中
    let marginTop=40*rate
    dialogEl.style.marginTop= `${marginTop}px`;
  });
}
let checkPaymentStatus = async () => {
    try {
     
        // 调用后端接口查询支付状态
        let data = await queryOrder({outOrderNo:order_params.value.resp_data.out_order_no});
       
        console.log(data.resp_data.order_status, 5555666);
        // 假设返回的状态字段为 status
        // pack_info.finish_pay = data.code; 
        // 
        // 如果支付成功或者失败或者过期，停止轮询
        if (data.resp_data.order_status == 2||data.resp_data.order_status == 3||data.resp_data.order_status == 4) {
            clearInterval(pollingInterval.value);
            if(data.resp_data.order_status == 4){
                update_code()
                console.log("已过期，重新获取验证码");
            } else {
                if(data.resp_data.order_status == 2){
                    pack_info.finish_pay='success'
                    // await notify(order_params.value) 
                }else{
                    pack_info.finish_pay='fail' 
                }
                pay_status_dialog_ref.value.status = pack_info.finish_pay; // 更新支付状态
                pay_status_dialog_ref.value.dialogVisible = true
            }
            return; // 退出函数
        } 
    } catch (error) {
        console.error('查询支付状态失败:', error);
    } 
};
let order_status = async (status) => {
    console.log(status,'order_status');

    pack_info.value.finish_pay=status

    // 如果支付成功，刷新用户权益信息
    if (status === 'success') {
        await fetchUserBenefits()
          
    }
}
let pollingInterval = ref(null); // 轮询定时器
defineExpose({
    dialogVisible,
});
let dateValue=(date)=>{
    return  date.trim().split(/\s+/).join("-")
}
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
let getList=async()=>{
    let data=await digitalPlanList({userId:loginStore.userId||''})
    member.value=data.data
    choose_thali(member.value[0])
    console.log(777);
    

}
let go_user_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'user' } })
}
let go_privacy_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'privacy' } })
}
let paymethod=(method)=>{
    current_paymethod.value=method
}
let getBodyScale=()=>{
  const el = document.querySelector('#app');
  if (!el) return 1;
  const transform = getComputedStyle(el).transform;
  if (!transform || transform === 'none') return 1;
  const values = transform.match(/matrix\((.+)\)/)[1].split(', ');
  return parseFloat(values[0]); // scale
}
watch(()=>dialogVisible.value, async(newValue, oldValue) => {
   if(!newValue){

    
      current_paymethod=ref('alipay')
      clearInterval(pollingInterval.value)
   }else{
    if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.level){
        member_status.value=loginStore.memberInfo.level.level
    }
    if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.end_time){
        let effective_date=dateValue(loginStore.memberInfo.level.end_time ||'');
        expire.value=isExpired(effective_date+' 23:59:59')
        if(expire.value){
            member_status.value='expire'
        }
        
    }
    rate=getBodyScale()
    await nextTick();
    getList();
    startPolling()
   }
},{immediate:true,deep:true});
</script>

<style lang="scss">
.buy_digital_dialog {
    padding: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: visible;
    // overflow: hidden;
    background: linear-gradient(180deg, #DFFFDF 0%, #FFFFFF 30.57%);
    border-radius: 4px;
    .el-dialog__header{
        padding:36px 29px 0;
        height: 84px;
        position: relative;
        margin-bottom: 32px;
        .buy_digital_dialog_close{
            width: 20px;
            height: 20px;
            position: absolute;
            right: 32px;
            top: 16px;
            cursor: pointer;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .buy_digital_dialog_title{
            display: flex;
            align-items: center;
            .buy_digital_dialog_title_avator{
                width: 48px;
                height: 48px;
                position: relative;
                margin-right: 16px;
                .buy_digital_dialog_title_avator_img{
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .buy_digital_dialog_title_avator_sign{
                    position: absolute;
                    right: -2px;
                    bottom: 1px;
                    z-index: 1;
                    width: 14px;
                    height: 14px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: #EEEEEE;
                    border-radius: 7px;
                    img{
                        width: 10px;
                        height: 10px;
                    }
                }
          
            }
            .buy_digital_dialog_title_name{
                font-size: 24px;
                line-height: 22px;
                color: rgba(0, 0, 0, 0.85);
            }
        }
    }
    .el-dialog__body{
        padding:0 29px;
        .buy_digital_profile{
            position: relative;
            width: 100%;
            margin-bottom: 27px;
            color: #5D1A64;
            .buy_digital_profile_content{
                width: 100%;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                margin-bottom: 42px;
                .buy_digital_profile_content_item{
                    width: 268px;
                    height: 175px;
                    padding: 16px 40px 0;
                    background:#fff;
                    border-radius: 12px;
                    margin-right: 13px;
                    box-sizing: border-box;
                    position: relative;
                    cursor: pointer;
                    border: 1px solid #E5E5E5;
                    box-shadow: 0px 0px 27px rgba(0, 0, 0, 0.01);
                    display: flex;
                    flex-direction: column;
                    // overflow: hidden;
                    .buy_digital_profile_content_item_info{
                        font-size: 20px;
                        line-height: 24px;
                        letter-spacing: -1px;
                        color: #232528;
                        margin-bottom: 24px;
                    }
                    .buy_digital_profile_content_item_info_price_discount{
                        display: flex;
                        align-items: baseline;
                        margin-bottom: 9px;
                        .buy_digital_profile_content_item_info_price{
                            margin-right: 8px;
                            font-size: 50px;
                            line-height: 63px;
                            letter-spacing: -1px;
                            color: #232528;
                        }
                        .buy_digital_profile_content_item_info_discount{
                            font-size: 14px;
                            line-height: 18px;
                            text-decoration-line: line-through;
                            color: #3C3C3C;
                            i{
                                text-decoration: line-through;
                                font-style: normal;
                            }
                        }
                    }
                    .buy_digital_profile_content_item_info_benefit{
                        line-height: 22px;
                        font-size: 16px;
                        color: #353D49;
                    }
                    &.current{
                        box-shadow: 0px 26px 40px rgba(189, 255, 188, 0.13);
                        border: 2px solid #A1E57F;
                        background-image: url('@/assets/images/account/buy_digital_profile_content_item_selected_bg.svg');
                        background-repeat: no-repeat;
                        background-color: #A9FB75;
                        background-position: 0 0;
                        background-size: 100% 100%;
                        .buy_digital_profile_content_item_selected{
                            position: absolute;
                            top: -1px;
                            right: -1px;
                            z-index: -2px;
                        }
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
            .buy_digital_describe{
                display: flex;
                width: 100%;
                padding-top: 4px;
                .buy_describe{
                    padding-top: 19px;
                    display: flex;
                    flex-direction: column;
                    h5{
                        margin: 0;
                        font-size: 16px;
                        line-height: 22px;
                        letter-spacing: -0.02em;
                        margin-bottom: 13px;
                        color: #353D49;
                    }
                    p{
                        margin: 0;
                        font-size: 14px;
                        line-height: 30px;
                        line-height: 32px;
                        color: #000000;
                        color: #353D49;
                        span{
                            color: rgb(24,144,255);
                            cursor: pointer;
                        }

                    }
                }
                .buy_describe_paymethod{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: 216px;
                    margin-left: auto;
                    padding-right: 27px;
                    ul{
                        display: flex;
                        align-items: center;
                        margin-bottom: 12px;
                        li{
                            box-sizing: border-box;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 75px;
                            height: 26px;
                            border: 1px solid #D3D3D2;
                            border-radius: 2px;
                            color: #000000;
                            font-size: 12px;
                            margin-right: 4px;
                            position: relative;
                            cursor: pointer;
                            &:last-child{
                                margin-right: 0;
                            }
                            
                            &.current{
                                border: 1px solid #0AAF60;
                                &::before{
                                    content: '';
                                    position: absolute; 
                                    top: 0; 
                                    right: 0; 
                                    width: 0; 
                                    height: 0; 
                                    border-left: 17px solid transparent; 
                                    border-right: 17px solid #0AAF60; 
                                    border-bottom: 17px solid transparent; 
                                  
                                }
                                &::after{
                                    content: '';
                                    position: absolute; 
                                    top: 0px; 
                                    right: 0px; 
                                    width: 17px;
                                    height: 17px;
                                    background-image: url('@/assets/images/account/buy_describe_paymethod_current.png');
                                    background-color: transparent;
                                    background-repeat: no-repeat;
                                    background-size:9px 6px ;
                                    background-position: 8px 2px;
                                    z-index: 100;
                                }
                                
                            }
                        }
                    }
                    .buy_describe_paymethod_img{
                        width: 140px;
                        height: 140px;
                        margin-bottom: 19px;
                    }
                    .buy_describe_paymethod_tip{
                        display: flex;
                        line-height: 22px;
                        align-items: baseline;
                        font-size: 12px;
                        margin-bottom: 6px;
                        color: #000000;
                        i{
                            margin-left: 16px;
                            margin-right: 2px;
                        }
                        .buy_describe_paymethod_tip_price{
                            font-size: 24px;
                            color: #FF3B30;
                            font-style: italic;
                        }

                    }
                    .buy_describe_finish_pay{
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        width: 165px;
                        height: 32px;
                        background: #0AAF60;
                        border-radius: 100px;
                        border: none;
                        font-size: 12px;
                        color: #FFFFFF;
                        cursor: pointer;
                    }
                }
            }
        }
    }
    

}
</style>
