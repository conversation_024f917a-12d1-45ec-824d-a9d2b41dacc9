<template>
    <div class="expireTop">
        <div class="expireTop_account_img">
            <accountImg ref="account_img_ref"></accountImg>
        </div>
        <div class="expireTop_vip_box">
            <div class="expireTop_vip_text">
                <span class="expireTop_vip_text_label">会员已过期</span>
            </div>
            <div class="expireTop_vip_btn">
                <span @click="membership">立即续费</span>
            </div>
        </div>
    </div>
</template>
<script setup>
import {reactive,ref,onActivated} from 'vue'
import { useRouter } from 'vue-router'
import accountImg from './account_img.vue'
import { useloginStore } from '@/stores/login'
const router = useRouter()
let account_img_ref=ref(null)
let loginStore = useloginStore()
let membership=()=>{
    router.push('/membership')
}
let dateHandle=(dateStr)=>{
    let parts = dateStr.trim().split(/\s+/);
    return parts.join('-')
}
let expire=ref(false)
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
onActivated(()=>{
    if(loginStore&&loginStore.memberInfo){
        if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.end_time){
            expire.value=isExpired(loginStore.memberInfo.level.end_time+' 23:59:59' ||'')
        }
    }
})
</script>
<style lang="scss">
.expireTop{
    display: flex;
    flex-direction: column;
    width: 357px;
    border-radius: 12px;
    box-sizing: border-box;
    padding: 14px 12px 28px 14px;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: cover;
    background: linear-gradient( 145deg, #FDF6EB 0%, #E2B48E 100%);
    .expireTop_account_img{
        padding-bottom: 16px;
    }
    .expireTop_vip_box{
        width: 100%;
        display: flex;
        align-items: center;
        padding-top: 7px 0 0 3px;
        box-sizing: border-box;
        .expireTop_vip_text{
            display: flex;
            align-items: center;
            .expireTop_vip_text_label{
                margin-right: 6px;
                font-size: 12px;
                color: #C37F49;
            }
            .expireTop_vip_text_date{
                font-size: 18px;
                color: #3E1D04;
            }
        }
        .expireTop_vip_btn{
            margin-left: auto;
            width: 90px;
            height: 30px;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: 0,0;
            background-image: url('@/assets/images/account/vipTop_btn.png');
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            span{
                font-size: 14px;
                color:#fff;
            }
        }
    }
    
    /* 新增钻石信息样式 */
    .expireTop_diamond_box {
        display: flex;
        align-items: center;
        margin-top: 8px;
        padding-left: 3px;
        
        .diamond-icon {
            font-size: 16px;
            margin-right: 6px;
            color: #C37F49;
        }
        
        .diamond-count {
            font-size: 14px;
            color: #3E1D04;
            
            strong {
                font-weight: 600;
                margin-left: 4px;
            }
        }
    }
}
</style>