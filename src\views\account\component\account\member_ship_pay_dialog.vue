<template>
    <el-dialog v-model="dialogVisible" class="member_pay_dialog" width="888px"   :close-on-click-modal="false">
        <template #header>
            <payCloseDialog :top="-95" @close="close_dialog" ref="pay_close_dialog_ref"></payCloseDialog>
            <img src="@/assets/images/account/member_pay_close.png" class="member_pay_close" @click="member_pay_close">
        </template>
        <template #default>
            
            <div class="member_pay_content">
                <div class="member_pay_user_info">
                    <div class="member_pay_user_info_avator">
                        <img :src="user.avator&&user.avator!=''?user.avator:avatar1" alt="">
                        <div class="member_pay_user_info_avator_sign">
                            <img :src="get_sign(user.status)" v-if="user.status!=0" alt="">
                        </div>
                        
                    </div>
                    <div class="member_pay_user_info_text">
                        <div class="member_pay_user_info_account">
                            <span>账号 {{ user.account }}</span>
                            <button v-if="user.status=='expire'">已过期</button>
                        </div>
                        <div class="member_pay_user_info_date" v-if="user.effective_date!=''">
                            有效期至： {{ user.effective_date }}
                        </div>
                    </div>
                </div>
                <div class="buy_describe_content">
                    <div class="buy_describe">
                        <h5> 购买说明</h5>
                        <p> 
                        <template v-if="current_page=='membership_program'">
                            1. 多次购买套餐，有效期、合成字数、算粒叠加<br/>
                            2. 套餐权益内不含真人配音，需要单独付费<br/>
                            3. 支付即代表您已阅读并同意 <span @click="go_user_agreement">《用户协议》</span> 和 <span @click="go_privacy_agreement">《隐私协议》</span><br/>
                            4. 购买后立即生效，不支持无理由退款
                        </template>
                        <template v-else-if="current_page=='membership_digital'">
                            1. 本数字人套餐仅限规定时间内使用，到期后剩余视频合成时间将自动清零<br/>
                            2. 数字人套餐可叠加购买，购买后有效时间、视频合成时间都将自动叠加<br/>
                            3.本套餐仅支持数字人工作台使用<br/>
                            3. 支付即代表您已阅读并同意 <span @click="go_user_agreement">《用户协议》</span> 和 <span @click="go_privacy_agreement">《隐私协议》</span><br/>
                            4. 购买后立即生效，不支持无理由退款
                        </template>
                        <template v-else-if="current_page=='discount_digital'">
                            1. 包含3分钟数字人视频合成权益<br/>
                            2. 数字人体验卡开通后仅限3天内使用，过期则合成时间清零<br/>
                            3. 本套餐仅支持数字人工作台使用<br/>
                            4.支付即代表您已阅读并同意<span @click="go_user_agreement">《用户协议》</span>和 <span @click="go_privacy_agreement">《隐私协议》</span><br/>
                            5. 购买后立即生效，不支持无理由退款
                        </template>
                          <template v-else-if="current_page=='discount_svip'">
                            1. 本限时优惠包含3天AI配音SVIP会员权益，购买成功3天那可享受所有AI配音SVIP会员权益<br/>
                            2. 开通后仅限3天内使用，过期则会员权益失效<br/>
                            3.支付即代表您已阅读并同意 <span @click="go_user_agreement">《用户协议》</span>和<span @click="go_privacy_agreement">《隐私协议》</span><br/>
                            4. 购买后立即生效，不支持无理由退款
                        </template>
                        <template v-else>
                            1.多次购买加油包，合成字数、算粒叠加<br/>
                            2.支付即代表您已阅读并同意 <span @click="go_user_agreement">《用户协议》</span>和 <span @click="go_privacy_agreement">《隐私协议》</span><br/>
                            3.购买后立即生效，不支持无理由退款
                        </template>
                        </p>
                    </div>
                    <div class="buy_describe_paymethod">
                        <ul>
                            <li :class="current_paymethod=='alipay'?'current':''" @click="paymethod('alipay')">支付宝支付</li>
                            <li :class="current_paymethod=='weixin'?'current':''" @click="paymethod('weixin')">微信支付</li>
                        </ul>
                        <!-- <img :src="user.qrcode" class="buy_describe_paymethod_img" alt=""> -->
                         <div class="buy_describe_paymethod_img">
                            <QRCode :value="user.qrcode" :size="140" />
                         </div>
                        <span class="buy_describe_paymethod_tip">
                            <template v-if="current_paymethod == 'weixin'">微信</template><template v-else>支付宝</template>扫码支付<i>¥</i><span class="buy_describe_paymethod_tip_price">{{user.price}}</span>
                        </span>
                        <button class="buy_describe_finish_pay" @click="finish_pay" v-if="user.finish_pay=='success'">已完成付款</button>
                    </div>
                </div>
            </div>
        </template>
    </el-dialog>
    <payStatusDialog ref="pay_status_dialog_ref" @status="order_status"></payStatusDialog>
</template>

<script setup>
import { ref, defineExpose, reactive, onMounted, onBeforeUnmount,watch,defineEmits} from 'vue';
import avator from "@/assets/images/account/member_pay_user_info_avator.png"
import avatar1 from '@/assets/images/account/avatar.png'
import expirImage from "@/assets/images/account/member_pay_user_info_expire.png"
import svip from '@/assets/images/account/avatar_svip_sign.svg'
import vip from '@/assets/images/account/avatar_sign.png'
import qrCode from "@/assets/images/account/member_qrcode.png"
import payStatusDialog from "@/components/payDialog/pay_status_dialog.vue"
import QRCode from 'qrcode.vue';
import { useRouter } from 'vue-router'
import {queryOrder} from '@/api/account.js'
import { useloginStore } from '@/stores/login'
import payCloseDialog from "@/components/payDialog/pay_close_dialog.vue"
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
let loginStore = useloginStore()
const { fetchUserBenefits } = useUserBenefits()
let emit=defineEmits('update_code')
let pollingInterval = ref(null); // 轮询定时器
let router = useRouter()
let dialogVisible=ref(false)
let current_page=ref('')
let user=reactive({
    avator,
    account:'',
    status:'',
    effective_date:'',
    qrcode:qrCode,
    price:0,
    finish_pay:'fail'
})
let sign_image=reactive({
    svip,
    vip
})
let order_params=ref({})
let current_paymethod=ref('alipay')
let maxPollingTime=ref(5 * 60 * 1000)
let pollingStartTime=ref(0)
let pay_close_dialog_ref=ref(null)
let paymethod=(method)=>{
    current_paymethod.value=method
}
let get_sign=(status)=>{
    console.log(status,'get_sign');
    
    let result=''
    switch (status) {
        case 0:
            result='' 
            break;
        case 1:
            result=vip
            break;
        case 2:
            result=svip
            break;
        case 'expire':
            result=expirImage
        default:
            break;
    }
    return result
}
let finish_pay=()=>{
    dialogVisible.value=false
}
let go_user_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'user' } })
}
let go_privacy_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'privacy' } })
}
let member_pay_close=()=>{
    pay_close_dialog_ref.value.pay_close_show=true
}
let close_dialog=()=>{
    dialogVisible.value=false
}
let pay_status_dialog_ref=ref(null)
let order_status = async (status) => {
    console.log(status,'order_status');

    user.finish_pay=status

    // 如果支付成功，刷新用户权益信息并调用全局支付成功弹窗方法
    if (status === 'success') {
        console.log('会员支付成功，开始刷新用户权益信息')

        // 刷新用户权益信息
        try {
            await fetchUserBenefits()
            console.log('✅ 会员支付成功，用户权益信息刷新完成')
        } catch (error) {
            console.error('❌ 会员支付成功，但权益信息刷新失败:', error)
        }

        // 调用全局支付成功弹窗方法
        setTimeout(() => {
            if (typeof window !== 'undefined' && window.showVoiceClonePaymentSuccess) {
                window.showVoiceClonePaymentSuccess()
            } else {
                console.log('全局方法不存在，会员支付成功但无法显示弹窗')
            }
        }, 1000) // 延迟1秒，让支付成功消息先显示
    }
}
// let checkPaymentStatus = async () => {
//     try {
//         // 调用后端接口查询支付状态
//         let data = await ordersStatus({outOrderNo:order_params.value.resp_data.out_order_no});
//         console.log(data,33333);
        
        
//         // 假设返回的状态字段为 status
//         // 
//         // 
//         // 如果支付成功，停止轮询
//         if (data.voice||data.purchase||) {
//             clearInterval(pollingInterval.value);
//             status='success'
//             console.log("支付成功，停止轮询。");
//             user.finish_pay = 'success'; 
//             return; // 退出函数
//         }
        
//         // 检查是否超过最大轮询时间
//         if (Date.now() - pollingStartTime >= maxPollingTime) {
//             clearInterval(pollingInterval.value); // 清除轮询
//             emit('update_code');
//             console.log("轮询结束，已达到最大时间限制。");
//             return; // 退出函数
//         }
        
//     } catch (error) {
//         console.error('查询支付状态失败:', error);
//     } finally {
//         // pay_status_dialog_ref.value.dialogVisible = true
//         // pay_status_dialog_ref.value.status = user.finish_pay; // 更新支付状态
//     }
// };
let checkPaymentStatus = async () => {
    try {
     
        // 调用后端接口查询支付状态
        let data = await queryOrder({outOrderNo:order_params.value.resp_data.out_order_no});
       
        console.log(data.resp_data.order_status, 5555666);
        // 假设返回的状态字段为 status
        // user.finish_pay = data.code; 
        // 
        // 如果支付成功或者失败或者过期，停止轮询
        if (data.resp_data.order_status == 2||data.resp_data.order_status == 3||data.resp_data.order_status == 4) {
            clearInterval(pollingInterval.value);
            if(data.resp_data.order_status == 4){
                emit('update_code');
                console.log("已过期，重新获取验证码");
            } else {
                if(data.resp_data.order_status == 2){
                    user.finish_pay='success'
                    // await notify(order_params.value) 
                }else{
                    user.finish_pay='fail' 
                }
                pay_status_dialog_ref.value.status = user.finish_pay; // 更新支付状态
                pay_status_dialog_ref.value.dialogVisible = true
            }
            return; // 退出函数
        } 
    } catch (error) {
        console.error('查询支付状态失败:', error);
    } 
};
let startPolling = () => {
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value); // 清除已有的定时器
    }
    pollingInterval.value = setInterval(checkPaymentStatus, 3000);
    // pollingStartTime.value = Date.now()
};
let expire=ref(false)
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
onBeforeUnmount(()=>{
    clearInterval(pollingInterval.value);
})
let dateValue=(date)=>{
    return  date.trim().split(/\s+/).join("-")
}
watch(dialogVisible, (newValue, oldValue) => {
    if(newValue){
        user.finish_pay='fail'
        user.account= loginStore.userInfo.nickName ||loginStore.userInfo.mobile ||''
        user.avator = loginStore.userInfo.avatar || ''
        if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.level){
            user.status=loginStore.memberInfo.level.level
        }

        if(loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.end_time){
            console.log(loginStore.memberInfo.level.end_time,'过期时间');
            
            user.effective_date=loginStore.memberInfo.level.end_time ||''
            user.effective_date=dateValue(user.effective_date);
            expire.value=isExpired(user.effective_date+' 23:59:59')
            if(expire.value){
                user.status='expire'
            }
           
        } 
        startPolling()
    }else{
        clearInterval(pollingInterval.value); 
        current_paymethod.value='alipay'
    }
}, { deep: true,immediate:true });
defineExpose({
    dialogVisible,
    current_page,
    user,
    order_params,
    startPolling
});
</script>

<style lang="scss">
.member_pay_dialog {
    padding:16px 32px 29px 34px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #DFFFF6 0%, #FFFFFF 30.57%);
    border-radius: 4px;
    overflow: visible;
    .el-dialog__header{
        padding: 0;
        display: flex;
        justify-content: flex-end;
        .el-dialog__headerbtn{
            display: none;
        }
        .member_pay_close{
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
    }
    .el-dialog__body{
        .member_pay_content{
            .member_pay_user_info{
                display: flex;
                align-items: center;
                padding-bottom: 27px;
                .member_pay_user_info_avator{
                    width: 48px;
                    height: 48px;
                   
                    margin-right: 16px;
                    position: relative;
                    img{
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                    .member_pay_user_info_avator_sign{
                        position: absolute;
                        width: 14px;
                        height: 14px;
                        background-color: #EEEEEE;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        right: -2px;
                        bottom: 1px;
                        border-radius: 50%;
                        overflow: hidden;
                        img{
                            width: 10px;
                            height: 10px;
                        }
                    }
                }
              .member_pay_user_info_text{
                display: flex;
                flex-direction: column;
                .member_pay_user_info_account{
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    span{
                        color: rgba(0,0,0,0.85);
                        font-size: 16px;
                        line-height: 22px;
                        margin-right: 12px;
                    }
                    button{
                        box-sizing: border-box;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        padding: 4px 6px;
                        height: 17px;
                        background: #EFEFF1;
                        border: 0.5px solid #D3D3D2;
                        border-radius: 2px;
                        font-style: normal;
                        font-weight: 400;
                        font-size: 9px;
                        color: rgba(0, 0, 0, 0.45);

                    }
                }
              }
            }
            .buy_describe_content{
                display: flex;
                padding-top: 43px ;
                width: 100%;
            
                .buy_describe{
                    display: flex;
                    flex-direction: column;
                    height: 193px;
                    h5{
                        margin: 0;
                        font-size: 16px;
                        line-height: 22px;
                        letter-spacing: -0.02em;
                        margin-bottom: 18px;
                        color: #353D49;
                    }
                    p{
                        margin: 0;
                        font-size: 14px;
                        line-height: 32px;
                        color: #353D49;
                        span{
                            color: rgb(24,144,255);
                            cursor: pointer;
                        }

                    }
                }
                .buy_describe_paymethod{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: 216px;
                    margin-left: auto;
                    height: 280px;
                    ul{
                        display: flex;
                        align-items: center;
                        margin-bottom: 11px;
                        li{
                            box-sizing: border-box;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 75px;
                            height: 26px;
                            border: 1px solid #D3D3D2;
                            border-radius: 2px;
                            color: #000000;
                            font-size: 12px;
                            margin-right: 4px;
                            position: relative;
                            cursor: pointer;
                            &:last-child{
                                margin-right: 0;
                            }
                            
                            &.current{
                                border: 1px solid #0AAF60;
                                &::before{
                                    content: '';
                                    position: absolute; 
                                    top: 0; 
                                    right: 0; 
                                    width: 0; 
                                    height: 0; 
                                    border-left: 17px solid transparent; 
                                    border-right: 17px solid #0AAF60; 
                                    border-bottom: 17px solid transparent; 
                                  
                                }
                                &::after{
                                    content: '';
                                    position: absolute; 
                                    top: 0px; 
                                    right: 0px; 
                                    width: 17px;
                                    height: 17px;
                                    background-image: url('@/assets/images/account/buy_describe_paymethod_current.png');
                                    background-color: transparent;
                                    background-repeat: no-repeat;
                                    background-size:9px 6px ;
                                    background-position: 8px 2px;
                                    z-index: 100;
                                }
                                
                            }
                        }
                    }
                    .buy_describe_paymethod_img{
                        width: 140px;
                        height: 140px;
                        margin-bottom: 11px;
                    }
                    .buy_describe_paymethod_tip{
                        display: flex;
                        line-height: 22px;
                        align-items: baseline;
                        font-size: 12px;
                        margin-bottom: 18px;
                        color: #000000;
                        i{
                            margin-left: 16px;
                            margin-right: 2px;
                        }
                        .buy_describe_paymethod_tip_price{
                            font-size: 24px;
                            color: #FF3B30;
                            font-style: italic;
                        }

                    }
                    .buy_describe_finish_pay{
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        width: 165px;
                        height: 32px;
                        background: #0AAF60;
                        border-radius: 100px;
                        border: none;
                        font-size: 12px;
                        color: #FFFFFF;
                        cursor: pointer;
                    }
                }
            }
        }
    }
    

}
</style>
