<template>
    <div class="member_top">
        <div class="member_top_info">
            <div class="member_top_avant_box">
                <div class="member_avant_box">
                    <span class="member_avant_img" >
                        <img :src="user.avant&&user.avant!=''?user.avant:avatar" alt="">
                        <!-- <span class="member_avant_img_edit">
                            <el-upload
                                ref="uploadRef"
                                class="upload-demo"
                                :on-change="handleFileChange"
                                :before-upload="beforeUpload"
                                :show-file-list="false"
                                accept="image/*"
                                :auto-upload="false"
                                :disabled="upload_disable"
                                 :class="{ 'disabled-input': upload_disable }"
                                >
                                <img src="@/assets/images/account/avant_img_edit.svg" class="upload-image" @click="handleUpload" alt="">
                            </el-upload>
                        </span> -->
                    </span>
                </div>
                <div class="member_avant_text">
                    <div class="member_avant_name">
                        {{user.name}}
                    </div>
                    <div class="member_avant_date" v-if="user.date!=''">
                       <span class="member_avant_date_label">会员到期：</span>{{user.date}}
                    </div>
                </div>
            </div>
            <div class="member_top_nav" >
                <span v-for="(item,index) in filteredNavList" :key="index" @click="top_nav_click(item)" :class="top_nav_current==item.value?'current':''">{{item.lable}}</span>
            </div>
        </div>
        <div class="member_top_subnav" v-if="getCurrentNav(top_nav_current).length>0">
            <span v-for="(item,index) in getCurrentNav(top_nav_current)" :key="index" @click="choose_nav(item)" :class="sub_nav_current==item.value?'current':''">{{item.lable}}</span>
        </div>
    </div>
</template>
<script setup>
 import {ref,reactive,defineExpose,defineEmits,watch,onMounted,getCurrentInstance,inject,computed  } from 'vue'
 import { ElMessage } from 'element-plus'
 import { useRoute } from 'vue-router';
 import {userInfo} from '@/api/account.js'
import { useloginStore } from '@/stores/login'
import { useFileUpload } from '@/views/account/hook/upload.js';
import avatar from '@/assets/images/account/avatar.png'
const { proxy } = getCurrentInstance();
const { fileChange } = useFileUpload();
 let loginStore = useloginStore()
 let route = useRoute(); // 获取当前路由对象
 let emits=defineEmits(['chooseNav'])
 let user=reactive({
    name:'',
    date:'',
})
let currentNav=inject('currentNav')
let upload_disable=ref(false)
let uploadRef = ref(null)
let action=import.meta.env.VITE_API_BASE_URL+'/material/upload/signature'
let navList=reactive([
    {
        lable:"AI配音",
        value:'plan',
        children:[
            {
                lable:"月度",
                value:'month',
                params:1
            },{
                lable:"季度",
                value:'half_year',
                params:3
            },{
                lable:"年度",
                value:'year',
                params:12
            },
        ]
    },{
        lable:"AI商配",
        value:'business',
        children:[
           
        ]
    },{
        lable:"数字人",
        value:'digital',
        children:[
           
        ]
    },
    {
        lable:"加油包",
        value:'package',
        children:[
            {
                lable:"字符包",
                value:'character',
                params:'加油包'
            },
            {
                lable:"算粒",
                value:'calculate',
                params:'算力'
            },{
                lable:"空间",
                value:'space',
                params:'云空间'
            },
        ]
    }
])
let getCurrentNav=(current)=>{
    let result= navList.filter((item)=> item.value==current)
    return result[0]?.children||result
}
let top_nav_current=ref('plan')
let sub_nav_current=ref('month')
let top_nav_click=(data)=>{
    console.log(data,666);
    
    top_nav_current.value=data.value
    if(getCurrentNav(top_nav_current.value).length>0){
        sub_nav_current.value=getCurrentNav(top_nav_current.value)[0].value
    }
   
    emits('chooseNav',{
        top_nav_current:top_nav_current.value,
        sub_nav_current:sub_nav_current.value,
        params:data.children.length>0?data.children[0].params:'',
    })
}
let filteredNavList = computed(() => {
    // if (loginStore.memberInfo.level.level === 0) {
    // // 过滤掉 value === 'package' 的项
    // return navList.filter(item => item.value !== 'package')
    // }
    return navList
})
let choose_nav=(data)=>{
    console.log(data,777);
    sub_nav_current.value=data.value
    emits('chooseNav',{
        top_nav_current:top_nav_current.value,
        sub_nav_current:sub_nav_current.value,
        params:data.params,
    })
}
let handleUpload=(event)=>{
    console.log(loginStore.token,!loginStore.token,loginStore.token=='',777);
    
    if (!loginStore.token || loginStore.token.trim() === '') {
        proxy.$modal.open('组合式标题')
        return
    }
}
let beforeUpload = (file) => {
    console.log('beforeUpload');
    let isImage = file.type.startsWith('image/');
    if (!isImage) {
        ElMessage.error('上传文件必须是图片！');
    }
    return isImage;
};
let handleChange = (file, fileList) => {
      if (file.status === 'success') {
        user.avant = file.response.url; // 根据实际返回的结构修改
      } else if (file.status === 'fail') {
        ElMessage.error('上传失败！');
      }
};
let handleExceed = (files, fileList) => {
    ElMessage.warning(`只能上传一张图片！`);
};
// let  findParentAndChildren=(valueToFind, list)=>{
//     for (const item of list) {
//         if (item.children) {
//             console.log(valueToFind, list,'findParentAndChildren');
            
//             if (item.children.length>0) {
//                 // 检查当前 children 中是否有匹配的 value
//                 const foundChild = item.children.find(child => child.value === valueToFind);
//                 if (foundChild) {
//                     return {
//                         parent: item, // 找到的上级对象
//                         child: foundChild // 找到的当前 children 对象
//                     };
//                 }
//                 // 递归查找
//                 const result = findParentAndChildren(valueToFind, item.children);
//                 if (result) {
//                     return result; // 如果在子级中找到，返回结果
//                 }
//             }
//         }
//     }
//     return null; // 如果没有找到
// }
let findParentAndChildren = (valueToFind, list) => {
    for (const item of list) {
        // 先判断当前节点的 children 是否存在且非空
        if (item.children && item.children.length > 0) {
            // 在 children 中查找匹配的 value
            const foundChild = item.children.find(child => child.value === valueToFind);
            if (foundChild) {
                // 找到匹配的子节点，返回父节点和子节点
                return {
                    parent: item,
                    child: foundChild
                };
            }
            // 递归查找子节点的子节点
            const result = findParentAndChildren(valueToFind, item.children);
            if (result) {
                return result;
            }
        }
        // 如果当前节点的 value 就是目标值，且没有匹配到子节点，返回当前节点作为 parent
        if (item.value === valueToFind) {
            return {
                parent: item,
                nochilren:true
            };
        }
    }
    // 没有找到匹配的节点
    return null;
};

let getUserInfo=async()=>{
    console.log('getUserInfo');
    return new Promise(async(resolve,reject)=>{
        let user_data=await userInfo({userId:loginStore.userId})
        loginStore.setUserInfo(user_data)
        initUserInfo()
        resolve(true)
    })
    
}
let dateValue=(date)=>{
    return  date.trim().split(/\s+/).join("-")
}
const initUserInfo = async() => {
    console.log('initUserInfo');
    
    if(loginStore && loginStore.userInfo) {
        user.name = loginStore.userInfo.nickName ||loginStore.userInfo.mobile||''
        user.date = loginStore.memberInfo.level.end_time||''
        user.date =dateValue(user.date)
        user.avant = loginStore.userInfo.avatar || ''
        console.log(loginStore.userInfo,777777);
    }
}
let handleFileChange=async (file, fileList) => {
    // if(loginStore.token&&loginStore.token!=''){
    //     proxy.$modal.open('组合式标题')
    //     return 
    // }
    console.log(file, fileList,'handleFileChange');
    const uploadRequest = await fileChange(file, fileList);
    user.avant=uploadRequest.url
}
onMounted(async()=>{
    if(loginStore.token&&loginStore.token!=''){
        await getUserInfo()
    }
   
})
watch(
  () => loginStore.token, // 监听整个 state
  (newValue, oldValue) => {

    if(!newValue||newValue==''){
        upload_disable.value=true
        user.name = ''
        user.date = ''
        user.avant =  ''
    }else{
        getUserInfo()
        upload_disable.value=false
    }
  },
  { deep: true, immediate: true } // 深度监听和立即执行
);
watch(
      () => route.query,
      (newQuery, oldQuery) => {
        if(newQuery.nav){
            console.log('执行的啥');
            
           let result = findParentAndChildren(newQuery.nav, navList)
           console.log(result,'result');
           
           if (result) {
                console.log('找到的上级对象:', result.parent.value);
                top_nav_current.value= result.parent.value
                let emit_obj={
                    top_nav_current:top_nav_current.value, 
                }
                if(!result.nochilren){
                    sub_nav_current.value=result.child.value
                    
                    emit_obj={
                        ...emit_obj,
                        sub_nav_current:sub_nav_current.value,
                        params:result.child.params,
                    }
                }
                 emits('chooseNav',emit_obj)
                
            }
            // top_nav_current.value='package'
            // sub_nav_current.value='space'
            // emits('chooseNav',{
            //     top_nav_current:top_nav_current.value,
            //     sub_nav_current:sub_nav_current.value,
            //     params:'云空间',
            // })
        }else{
            
            top_nav_current.value='plan'
            sub_nav_current.value='month'
            choose_nav({
                params:1,
                sub_nav_current:'month',
                top_nav_current:'plan',
                value:'month'
            })
        }
        // else if(newQuery.nav=='character'){
        //     top_nav_current.value='package'
        //     sub_nav_current.value='character'
        //     emits('chooseNav',{
        //         top_nav_current:top_nav_current.value,
        //         sub_nav_current:sub_nav_current.value,
        //         params:'加油包',
        //     })
        // }
        // 在这里可以执行其他逻辑，比如重新请求数据等
      },
      { immediate: true } // 立即执行一次
    );
defineExpose({
    user,
    navList,
    top_nav_current,
    sub_nav_current
})
</script>
<style lang="scss" scoped>
.member_top{
    width: 100%;
    // height: 200px;
    margin-top: 64px;
    background-image: url('@/assets/images/account/member_top_bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 49px 0 45px 137px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 294px;
    .member_top_info{
        display: flex;
        margin-bottom: 78px;
        
        .member_top_avant_box{
            display: flex;
            align-items: center;
            margin-right: 100px;
            .member_avant_box{
                display: flex;
                flex-direction: column;
                align-items: center;
                .member_avant_img{
                    width: 78px;
                    height: 80px;
                    margin-right: 14px;
                    position: relative;
                    display: inline-block;
                    // background-color: #f5f5f5;
                    img{
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                    .member_avant_img_edit{
                        position: absolute;
                        width: 19px;
                        height: 19px;
                        background: #D4D4D4;
                        box-shadow: 0px 0 50px 1px rgba(0,0,0,0.15);
                        border-radius: 12px 12px 12px 12px;
                        top: 2px;
                        right: 2px;
                        z-index: 2;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        img{
                            width: 9px;
                            height: 10px;
                        }
                    }
                }
                
                
        }
        .member_avant_text{
                    .member_avant_name{
                        font-weight: bold;
                        font-size: 20px;
                        color: #252525;
                        line-height: 26px;
                        margin-bottom: 4px;
                    }
                    .member_avant_date{
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        color: rgba(0,0,0,0.45);
                        line-height: 21px;
                        .member_avant_date_label{
                            margin-right: 4px;
                        }
                    }
                }
        }
        .member_top_nav{
            display: flex;
            align-items: center;
            span{
                margin-right: 80px;
                display: inline-block;
                font-size: 20px;
                color: rgba(0,0,0,0.45);
                line-height: 28px;
                cursor: pointer;
                &.current{
                    color: #353D49;
                    border-bottom: 1px solid #353D49;
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
    }
    .member_top_subnav{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        span{
            display: inline-block;
            font-size: 20px;
            color: #6D6E6F;
            margin-right: 124px;
            position: relative;
            cursor: pointer;
            &.current{
                padding: 0 4px;
                &::after{
                    content:'';
                    height: 10px;
                    position: absolute;
                    bottom: -4px;
                    left: -4px;
                    width: 100%;
                    background: rgba(10,175,96,0.2);
                    z-index: 1;
                }
            }
        }
    }
    .disabled-input {
        pointer-events: auto; /* 禁止点击 */
        opacity: 0.5; /* 设置透明度，表示禁用状态 */
    }
    .upload-image {
        cursor: pointer; /* 设置鼠标指针为手型，表示可点击 */
    }
    /* 去掉禁选标志 */
    .upload-demo.is-disabled {
        // pointer-events: auto; /* 允许鼠标事件 */
        cursor: default; /* 设置鼠标指针为默认状态 */
    }
}
</style>