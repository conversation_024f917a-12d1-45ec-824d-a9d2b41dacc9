<template>
    <div class="membership_business" v-loading="loading">
        <div class="membership_business_item" v-for="(item,index) in list" :key="index" @click="handleClick(item)" :class="{
            advanced: item.type === '进阶版套餐包',
            base: item.type !== '进阶版套餐包',
            current: current_id === item.id
        }">
            <div class="membership_business_item_selected" v-if="current_id==item.id">
                <img :src="getSelectImage(item.type)" alt="">
            </div>
            <div class="membership_business_item_img">
                
                <img :src="getImage(item.type)" alt="">
            </div>
            <div class="membership_business_item_name">{{ item.type=='进阶版套餐包'?'进阶版': '基础版'}} . 套餐包</div>
            <div class="membership_business_item_info">
                <div class="membership_business_item_info_item">
                    <b>{{list[0].data.length}}</b>
                    <span>款专业至臻音色</span>
                </div>
                <div class="membership_business_item_info_item" v-if="item.type=='进阶版套餐包'">
                    <b>{{accSubCalc(item.data.length,list[0].data.length)}}</b>
                    <span>款专业至臻音色</span>
                </div>
            </div>
            <el-button class="membership_business_item_btn" @click="go_package(item)">查看详情</el-button>
        </div>
    </div>
</template>
<script setup>
import { ref,defineExpose} from 'vue'
import {accSub} from "@/utils/accuracy.js"
import membershipBusinessAdvanced from '@/assets/images/account/membership_business_item_advanced.svg'
import membershipBusinessBase from '@/assets/images/account/membership_business_item_base.svg'
import membershipBusinessAdvancedSelect from '@/assets/images/account/membership_business_item_advanced_select.svg'
import membershipBusinessBaseSelect from '@/assets/images/account/membership_business_item_base_select.svg'
import { useSoundStore } from '@/stores/modules/soundStore.js' 
let soundStore = useSoundStore()
let package_type=ref(['基础版','进阶版'])
let list=ref([])
let loading=ref(false)
let current_id=ref('')
let getImage=(type)=>{
    if(type=='进阶版套餐包'){
        return membershipBusinessAdvanced
    }else{
        return membershipBusinessBase
    }
}
let getSelectImage=(type)=>{
    if(type=='进阶版套餐包'){
        return membershipBusinessAdvancedSelect
    }else{
        return membershipBusinessBaseSelect
    }
}
let accSubCalc=(num,num1)=>{
    return accSub(num,num1)
}
let handleClick=(item)=>{
    current_id.value=item.id
}
let go_package=(item)=>{
    // const url = `${window.location.origin}/soundStore?buy=true&type=package`;
    const url = `${window.location.origin}/soundStore?package=true&show_detail=true&type=single`;
    soundStore.setShowPackage(item)
    window.open(url, '_blank');
}
defineExpose({
    list,
    loading
})
</script>
<style lang="scss" scoped>
.membership_business{
    display: flex; 
    justify-content:center; 
    width: 100%;
    padding: 0 10%;
    margin-bottom: 296px;
    position: relative;
    top: -20px;
    .membership_business_item{
        margin-right: 42px;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 529px;
        background: #FFFFFF;
        box-shadow: 0px 0px 27px rgba(0, 0, 0, 0.01);
        border-radius: 12px;
        padding: 40px;
        box-sizing: border-box;
        height: 490px;
        position: relative;
        overflow: hidden;
        border: 1px solid #f5f5f5;
        .membership_business_item_selected{
            position: absolute;
            top: 0;
            right: 0;
            z-index: 1;
            width: 92px;
            height: 57px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .membership_business_item_img{
            width: 84px;
            height: 84px;
            margin-bottom: 36px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .membership_business_item_name{
            font-size: 32px;
            line-height: 45px;
            letter-spacing: -1px;
            color: #232528;
            margin-bottom: 36px;
        }
        .membership_business_item_info{
            display: flex;
            flex-direction: column;
            .membership_business_item_info_item{
                margin-bottom: 8px;
                b{
                    margin-right: 8px;
                    font-size: 32px;
                    line-height: 40px;
                    letter-spacing: -1px;
                    color: #DB9C00;
                }
                span{
                    font-size: 24px;
                    line-height: 34px;
                    color: #858587;
                }
                &:last-child{
                    margin-bottom: 0;
                }
            }

        }
        .membership_business_item_btn{
            position: absolute;
            left: 50%;
            bottom: 40px;
            transform: translateX(-50%);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            width: 369px;
            height: 61px;
            background-color: #4C4E5B;
            border-radius: 12px;
            ::v-deep(span){
                font-size: 24px;
                leading-trim: both;
                text-edge: cap;
                color: #FFFFFF;
            }
        }
        &:last-child{
            margin-right: 0;
        }
        &.advanced{
            .membership_business_item_info{
                .membership_business_item_info_item{
                    b{
                        color: #EE70FF;
                    }
                }
            }
        }
        &:hover,&.current{
            &.base{
                border: 2px solid #F7D281;
                .membership_business_item_btn{
                    background: linear-gradient(90deg, #FCCD66 0%, #FFE389 100%);
                    ::v-deep(span){
                        color: #6B461C;
                    }
                }
            }
            &.advanced{ 
                border: 2px solid #EE70FF;
                .membership_business_item_btn{
                    background: linear-gradient(90deg, #EE70FF 0%, #FF91DE 100%);
                }
            }
       }
    }
}
</style>