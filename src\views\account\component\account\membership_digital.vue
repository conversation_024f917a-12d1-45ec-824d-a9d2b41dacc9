<template>
    <div class="membership_digital" v-loading="loading">
        <div class="membership_digital_item" v-for="(item,index) in member" :class="item.id==current_obj.id?'current':''" :key="index" @click="handleClick(item)">
            <div class="membership_digital_item_top">
                <h4 class="membership_digital_item_top_type">
                    <template v-if="item.planName=='Digital-Monthly'">月卡</template>
                    <template v-else-if="item.planName=='Digital-Biannual'">半年卡</template>
                    <template v-else>年卡</template>
                </h4>
                <div class="membership_digital_item_top_price">
                    <b>{{ (item.discountPrice>0&&item.discountPrice<item.price)?item.discountPrice:item.price }}</b>
                    <span v-if="(item.discountPrice>0&&item.discountPrice<item.price)"><i>{{ item.price }}</i>元/月</span>
                </div>
            </div>
            <div class="membership_digital_item_info">
                <div class="membership_digital_item_info_item" v-for="(item1,index1) in membership_digital_item_keys" :key="index1">
                    <div class="membership_digital_item_info_item_icon">
                        <img src="@/assets/images/account/membership_digital_item_current.svg" alt="">
                        
                    </div>
                        <span class="membership_digital_item_info_item_label">{{item.resourceJsonCN[item1]}}</span>
                </div> 
            </div>
            <el-button class="membership_digital_item_btn" @click="pay(item)">
                购买
            </el-button>
        </div>
    </div>
    <!-- 会员计划支付弹窗 -->
    <memberShipPayDialog ref="member_ship_pay_dialog_ref" @update_code="update_code"></memberShipPayDialog>
</template>
<script setup>
 import {ref, reactive,defineExpose,getCurrentInstance } from 'vue'
 import memberShipPayDialog from "./member_ship_pay_dialog.vue"
 import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
import {ordersCreate} from '@/api/account.js'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
const { proxy } = getCurrentInstance();
let membership_digital_type=ref(['Digital-Monthly','Digital-Biannual','Digital-Annual'])
let membership_digital_item_keys=ref(['视频合成','云剪辑空间','照片合成','透明底合成','文字合成','音频合成','全音色','克隆音色'])
let member=ref([
   
])
let current_obj=ref({})
let loading=ref(false)
let pay_data=ref({})
let member_ship_pay_dialog_ref=ref(null)
let handleClick=(item)=>{
    current_obj.value=item
}
let pay=async(item)=>{
    pay_data.value=item
    await update_code()
    member_ship_pay_dialog_ref.value.current_page='membership_digital'
    member_ship_pay_dialog_ref.value.dialogVisible=true
}
//更新付款码
let update_code=async()=>{
    return new Promise(async(resolve,reject)=>{
        let item=pay_data.value
        if(!item.id)return
        console.log(item,'ordersCreate');
        let data=await ordersCreate({
            paymentType:'DIGITAL',
            planId:item.id,
            quantity:1,
            userId:loginStore.userId
        })
        member_ship_pay_dialog_ref.value.user.price=get_price(data.resp_data.total_amount,100)   
        member_ship_pay_dialog_ref.value.user.qrcode=data.resp_data.counter_url
        member_ship_pay_dialog_ref.value.order_params=data
        resolve(true)
    })
}
let get_price=(a,b)=>{
    return accDiv(a,b)
}   
defineExpose({
    member,
    loading,
})
</script>
<style lang="scss" scoped>
.membership_digital{
    display: flex; 
    justify-content:center; 
    width: 100%;
    padding: 0 10%;
    margin-bottom: 82px;
    position: relative;
    top: -119px;
    padding-top: 42px;
    .membership_digital_item{
        margin-right: 37px;
        padding: 24px 40px;
        background: #FFFFFF;
        border: 1px solid #E5E5E5;
        box-shadow: 0px 0px 27px rgba(0, 0, 0, 0.01);
        border-radius: 8px;
        overflow: hidden;
        width: 446px;
        border-radius: 12px;
        background-size: 100% 100%;
        background-position: 0 0;
        background-repeat: no-repeat;
        font-family: 'DingTalk JinBuTi';
        // font-style: italic;
        .membership_digital_item_top{
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            padding-bottom: 22px;
            border-bottom: 1px solid #D5EDD8;
            .membership_digital_item_top_type{
                font-size: 28px;
                line-height: 34px;
                letter-spacing: -1px;
                color: #232528;
                margin: 0;
                margin-bottom: 12px;
                font-weight: normal;
            }
            .membership_digital_item_top_price{
                display: flex;
                align-items: baseline;
                b{
                    margin-right: 16px;
                    font-size: 57px;
                    line-height: 72px;
                    letter-spacing: -1px;
                    color: #232528;
                }
                span{
                    font-size: 24px;
                    line-height: 30px;
                    color: #3C3C3C;
                    i{
                        text-decoration-line: line-through;
                       font-style: normal;
                    }
                }
            }
           
        }
        .membership_digital_item_info{
            padding:22px 0 44px;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            .membership_digital_item_info_item{
                display: flex;
                align-items: center;
                margin-bottom: 5px;
                .membership_digital_item_info_item_icon{
                    width: 14px;
                    height: 10px;
                    margin-right: 15px;
                    display: flex;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .membership_digital_item_info_item_label{
                    font-size: 16px;
                    line-height: 40px;
                    color: #232528;
                    
                }
               &:first-child{
                    .membership_digital_item_info_item_label{
                        color: #0AAF60;
                    }  
               }
               &:last-child{
                    margin-bottom: 0;
               }
            }
        }
        .membership_digital_item_btn{
            box-sizing: border-box;
            width: 100%;
            height:50px;
            border: 1px solid #1B223C;
            border-radius: 12px;
            background-color: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            ::v-deep(span){
                font-size: 18px;
                color: #1B223C;
            }
        }
        &.current,&:hover{
            background-image: url('@/assets/images/account/membership_digital_current.png');
            border: none;
            .membership_digital_item_top{
                 border-bottom-color: #fff;
            }
            .membership_digital_item_btn{
                background: #232528;
                box-shadow: 0px 8px 36px rgba(27, 34, 60, 0.16);
                ::v-deep(span){
                    color: #fff;
                }
                
            }
        }
        &:last-child{
            margin-right: 0;
        }
    }
}
</style>