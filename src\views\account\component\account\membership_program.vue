<template>

    <div class="membership_program" v-loading="loading">
        <div class="membership_program_item" v-for="(item,index) in member" :key="index">
            <div class="membership_program_item_top"  :style="getBackgroundStyle(item.img_type)" >
                <img src="@/assets/images/account/member_limited.svg" v-if="index==2&&(item.discountPrice>0&&item.discountPrice<item.price)" class="membership_program_item_top_limit" alt="">
                <div class="membership_program_item_identity" >
                    <img :src="identity[item.img_type]" alt="">
                </div>
                <!-- <img class="membership_program_item_charge" :src="charge[item.type]" alt=""> -->
                  <div class="membership_program_item_charge">
                    <b>{{ (item.discountPrice>0&&item.discountPrice<item.price)?item.discountPrice:item.price }}</b>
                    <span class="line" v-if="(item.discountPrice>0&&item.discountPrice<item.price)">{{item.price}}</span><span>元
                        <template v-if="index!=0">
                        /<template v-if="cycle==1">月</template><template v-if="cycle==3">3<template v-if="item.discountCycle>0">+{{item.discountCycle}}</template>个月</template><template v-if="cycle==12">12<template v-if="item.discountCycle>0">+{{item.discountCycle}}</template>个月</template>
                        </template>
                    </span>
                    <button class="membership_program_item_charge_btn" v-if="index==0" :class="!loginStore.token||loginStore.token==''?'register_send':((getStatus(item)=='已过期')?'alexpire':'alget')" @click="register_send">
                        <template v-if="!loginStore.token||loginStore.token==''">注册赠送</template>
                        <template v-else>
                           {{ getStatus(item) }}
                        </template>
                    </button>
                 </div>
                <span v-if="item.img_type=='ordinary'" class="membership_program_item_ordinary_label">
                    新注册账号享24小时免费SVIP体验，到期后需购VIP会员（含精品音色）或SVIP会员（含精品及臻享全部音色）继续使用权益。
                </span>
                <button class="membership_program_item_button" @click="pay(item)" v-else>
                    <template v-if="item.img_type=='ordinary'">
                        永久使用
                    </template>
                    <template v-else>
                        特惠预定
                    </template>
                </button>
            </div>
            <div class="membership_program_item_info">
                <template v-if="index==0">
                    <!-- {{ item.list }}--- -->
                    <div class="membership_program_item_info_item" v-if="item.list['SVIP音色']">
                         <span>{{item.list['SVIP音色']&&item.list['SVIP音色'].split('，')[0]}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['SVIP音色']">
                          <span>{{textReplace(item.list['SVIP音色']&&item.list['SVIP音色'].split('，')[1],'至臻','臻享')}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['精品音色']">
                        <span>{{item.list['精品音色']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['一键成片']">
                        <span>{{item.list['一键成片']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['云剪辑空间']">
                        <span>{{item.list['云剪辑空间']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['商配SFT']">
                        <span>{{item.list['商配SFT']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['赠送数字人']">
                        <span>{{item.list['赠送数字人']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    
                    <div class="membership_program_item_info_item">
                        <span>所有功能可体验</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                </template>
                <template v-if="index==1">
                    <div class="membership_program_item_info_item" v-if="item.list['VIP音色']">
                        <span>{{ item.list['VIP音色'] }}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['精品音色']">
                        <span>{{ item.list['精品音色'] }}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['一键成片']">
                        <span>{{item.list['一键成片']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['云剪辑空间']">
                        <span>{{item.list['云剪辑空间']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['商配SFT']">
                        <span>{{item.list['商配SFT']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item">
                        <span>所有功能可体验</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    
                </template>
                <template v-if="index==2">
                    <div class="membership_program_item_info_item" v-if="item.list['SVIP音色']">
                        <span>{{item.list['SVIP音色']&&item.list['SVIP音色'].split('，')[0]}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['SVIP音色']">
                        <span>{{textReplace(item.list['SVIP音色']&&item.list['SVIP音色'].split('，')[1],'至臻','臻享')}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['精品音色']">
                        <span>{{item.list['精品音色']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['一键成片']">
                        <span>{{item.list['一键成片']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['云剪辑空间']">
                        <span>{{item.list['云剪辑空间']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['商配SFT']">
                        <span>{{item.list['商配SFT']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item">
                        <span>所有功能可体验</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item" v-if="item.list['赠送SVIP']">
                        <span>{{item.list['赠送SVIP']}}</span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                    <div class="membership_program_item_info_item"  v-if="item.list['赠送克隆']">
                        <span>
                            <div class="clone">
                                {{item.list['赠送克隆']}}
                            </div>
                        </span>
                        <img src="@/assets/images/account/membership_program_item.png" alt="">
                    </div>
                </template>
            </div>
        </div>
    </div>
    <!-- 会员计划支付弹窗 -->
    <memberShipPayDialog ref="member_ship_pay_dialog_ref" @update_code="update_code"></memberShipPayDialog>
</template>
<script setup>
 import {ref, reactive,defineExpose,getCurrentInstance,watch, nextTick } from 'vue'
 import membership_program_ordinary_identity from "@/assets/images/account/membership_program_ordinary_identity.svg"
 import membership_program_vip_identity from "@/assets/images/account/membership_program_vip_identity.png"
 import membership_program_svip_identity from "@/assets/images/account/membership_program_svip_identity.png"
 import membership_program_ordinary_charge from "@/assets/images/account/membership_program_ordinary_charge.png"
 import membership_program_vip_charge from "@/assets/images/account/membership_program_vip_charge.png"
 import membership_program_svip_charge from "@/assets/images/account/membership_program_svip_charge.png"
 import memberShipPayDialog from "./member_ship_pay_dialog.vue"
 import {ordersCreate} from '@/api/account.js'
 import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
 import { useloginStore } from '@/stores/login'
 import { useUmeng } from '@/utils/umeng/hook'
 import { usePayStore } from '@/stores/modules/payStore.js' 
 import member_ordinary_bg from "@/assets/images/account/member_ordinary_bg.png"
 import member_vip_bg from "@/assets/images/account/member_vip_bg.png"
 import member_svip_bg from "@/assets/images/account/member_svip_bg.png"
let payStore= usePayStore()
let loginStore = useloginStore()
const { proxy } = getCurrentInstance();
const umeng = useUmeng()
let member=ref([
    {
        img_type:'ordinary',
        price:0,
        list:[
            
        ] 
    },
    {
        img_type:'vip',
        price:0,
        list:[
           
        ] 
    },
    {
        img_type:'svip',
        price:0,
        list:[
           
        ] 
    }
])
let cycle=ref()
let loading=ref(false)
let member_ship_pay_dialog_ref=ref(null)
let identity=reactive({
    ordinary:membership_program_ordinary_identity,
    vip:membership_program_vip_identity,
    svip:membership_program_svip_identity
})
let charge=reactive({
    ordinary:membership_program_ordinary_charge,
    vip:membership_program_vip_charge,
    svip:membership_program_svip_charge
})
let getBgImage = reactive({
  ordinary: member_ordinary_bg,
  vip: member_vip_bg,
  svip: member_svip_bg
})
let pay_data=ref()
let getBackgroundStyle=(type)=>{
  const img = getBgImage[type] || ''
  return {
    backgroundImage: img ? `url(${img})` : 'none'
  }
}
let textReplace=(txt,origin,place)=>{
    if(txt){
        return txt.replace(origin, place);
    }
   
}
 let getStatus=(item)=>{
    if (!item.newEndTime || item.newEndTime === '') {
      return '已过期';
    }
    const endTime = new Date(item.newEndTime).getTime();
    const now = Date.now();
    return now > endTime ? '已过期' : '已获得';
  }
//更新付款码
let update_code = async (data) => {
    console.log(data,'update_code');
    
    // 设置支付类型为订阅
    let type = data?.type ?? 'SUBSCRIBE'
    return new Promise(async (resolve, reject) => {
        let item=pay_data.value
        if(!item.id)return
        console.log(item,'ordersCreate');
        
        let data=await ordersCreate({
            paymentType:type,
            planId:item.id,
            quantity:1,
            userId:loginStore.userId
        })
        member_ship_pay_dialog_ref.value.user.price=get_price(data.resp_data.total_amount,100)   
        member_ship_pay_dialog_ref.value.user.qrcode=data.resp_data.counter_url
        member_ship_pay_dialog_ref.value.order_params=data
        resolve(true)
    })
}
let pay=async(item)=>{
    // if(item.type=='ordinary'){
    //     return
    // }
    console.log(loginStore.token,'pay');
    
    if(!loginStore.token||loginStore.token==''){
        proxy.$modal.open('组合式标题')
        return 
    }
    if(item.img_type!='ordinary'){
        // 添加埋点代码
        umeng.trackEvent(
        '会员服务', 
        '点击购买按钮', 
        `${item.img_type === 'vip' ? 'VIP' : item.img_type === 'svip' ? 'SVIP' : item.img_type}-会员-${cycle.value}期-用户ID:${loginStore.userId || ''}`, 
        item.price.toString()
        )
        
        pay_data.value=item
        await update_code()
        member_ship_pay_dialog_ref.value.current_page='membership_program'
        member_ship_pay_dialog_ref.value.dialogVisible=true
    }
}
let get_price=(a,b)=>{
    return accDiv(a,b)
}
let limit_time_pay=async(data)=>{
      pay_data.value=data.request_data
      let params_current=''
      let params={}
      if(data.type!='type'){
        if(data.benefit_type=='discount'){
            params.type='DIGITAL'
            params_current='discount_digital'
        }
      }else{
         params_current='discount_svip'
      }
      await update_code(params)
      await nextTick()
      member_ship_pay_dialog_ref.value.current_page=params_current
      member_ship_pay_dialog_ref.value.dialogVisible=true
      payStore.setLimitedTime(null)
}
let register_send=()=>{
    if(!loginStore.token||loginStore.token==''){
        proxy.$modal.open('组合式标题')
        return 
    }
}
watch(
  () => payStore.limited_time_obj,
  (newVal, oldVal) => {
    if (newVal) {
      // limited_time_obj 不是 null 时触发
      // 在这里调用你想执行的方法
      limit_time_pay(payStore.limited_time_obj);
    }
  },
  {
    immediate: true, // 是否立即执行一次回调，按需设置
    deep: true       // 是否深度监听，按需设置
  }
);   
defineExpose({
    member,
    loading,
    cycle
})
</script>
<style lang="scss" scoped>
.membership_program{
    display: flex; 
    justify-content:center; 
    width: 100%;
    padding: 0 10%;
    margin-bottom: 133px;
    .membership_program_item{
        margin-right: 37px;
        height: 657px;
        padding: 0  ;
        box-shadow: 0px 0px 35px 0px rgba(0,0,0,0.05);
        border-radius: 8px;
        overflow: hidden;
        width: 446px;
        box-sizing: border-box;
        .membership_program_item_top{
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 32px 24px 32px;
            position: relative;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-position: 0 0;
            height: 252px;
            border-radius: 8px;
            overflow: hidden;
            .membership_program_item_top_limit{
                position: absolute;
                width: 92px;
                height: 32px;
                top:0;
                left: 0;
                z-index: 1;
            }
            .membership_program_item_identity{
                width: 393px;
                height: 42px;
                margin-bottom: 11px;
                display: flex;
                align-items: center;
                justify-content: center;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .membership_program_item_charge{
                display: flex;
                align-items: baseline;
                color: #fff;
                height: 66px;
                margin-bottom: 19px;
                width: 100%;
                box-sizing: border-box;
                padding-left: 67px;
                b{
                    font-size: 57px;
                    margin-right: 12px;
                    line-height: 66px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    font-style: italic;
                }
                span{
                    font-size: 20px;
                    line-height: 43px;
                    &.line{
                        text-decoration: line-through;
                    }
                }
               
            }
         
            .membership_program_item_button{
                cursor: pointer;
                width: 100%;
                height: 50px;
                background: #FFFFFF;
                box-shadow: 0px 4px 21px 0px rgba(44,86,1,0.1);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                border: none;
            }
        }
        .membership_program_item_info{
            padding: 43px 24px 0;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            .membership_program_item_info_item{
                display: flex;
                align-items: center;
                margin-bottom: 16px;
                span{
                    font-size: 16px;
                    color: #353D49;
                    line-height: 22px;
                    flex: 1;
                    max-width: 346px;
                    word-break: break-all;
                    .clone{
                        display: inline-block;
                        background: linear-gradient(95.32deg, #BB46FF 9.13%, #1774ED 94.85%), linear-gradient(95.32deg, #F37600 9.13%, #FF0900 94.85%), #FF0F0F;
                        border-radius: 6px 6px 6px 0px;
                        font-size: 16px;
                        line-height: 22px;
                        color: #FFFFFF;
                        padding: 3px 6px;
                    }
                }
                img{
                    margin-left: auto;
                    width: 19px;
                    height: 14px;
                    
                }
                &:last-child{
                    margin-bottom: 0;
                }
            }
        }
        &:first-child{
            .membership_program_item_top{
                .membership_program_item_identity{
                    img{
                        width: 195px;
                        height: 100%;
                    }
                   
                   
                   
                }
                .membership_program_item_charge{
                        color: #000;
                        margin-bottom: 11px;
                        .membership_program_item_charge_btn{
                            padding: 8px 20px;
                            font-size: 18px;
                            line-height: 22px;
                            text-align: center;
                            color: #FFFFFF;
                            border-radius: 100px;
                            border: none;
                            margin-left: 15px;
                         
                            &.register_send{
                                background-color: #0AAF60;
                                cursor: pointer;
                            }
                            &.alexpire{
                                background-color: #454652;
                            }
                            &.alget{
                                background-color: #0AAF60;
                            }
                        }
                    }
                background: linear-gradient( 180deg, #EEEEEE 0%, #FFFFFF 100%);
                .membership_program_item_button{
                    color: #fff;
                    background: #353D49;
                }
                .membership_program_item_ordinary_label{
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 26px;
                    color: #595959;
                }
            } 
        }
        &:nth-child(2){
            .membership_program_item_top{
                background: linear-gradient( 180deg, #37D329 0%, #BCEF56 100%);
                .membership_program_item_button{
                    color: rgba(10, 175, 96, 1)
                }
            } 
        }
        &:last-child{
            margin-right: 0;
            .membership_program_item_top{
                background: linear-gradient( 180deg, #4275FC 0%, #01CDF3 100%);
                .membership_program_item_button{
                    color:rgba(65, 118, 252, 1)
                }
            }
           
        }
    }
}
</style>