<template>
    <div class="ordinaryTop">
        <accountImg ref="account_img_ref"></accountImg>
        <div class="ordinaryTop_vip_box">
            <div class="ordinaryTop_vip_text">
                <img src="@/assets/images/account/information_vip.png" alt="">
                <span>开通VIP，立享会员权益！</span>
            </div>
            <div class="ordinaryTop_vip_btn">
                <span @click="membership">注册会员</span>
            </div>
        </div>
    </div>
</template>
<script setup>
import {reactive,ref} from 'vue'
import { useRouter } from 'vue-router'
import accountImg from './account_img.vue'
const router = useRouter()
let account_img_ref=ref(null)
let user=reactive({
    avatar:'',
    name:'用户名称',
    id:'123456',
    medal:''
})
let membership=()=>{
    router.push('/membership')
}
</script>
<style lang="scss">
.ordinaryTop{
    padding: 0 8px;
    padding-top: 8px;
    display: flex;
    flex-direction: column;
    width: 357px;
    box-sizing: border-box;
    .ordinaryTop_vip_box{
        width: 100%;
        height: 61px;
        background: linear-gradient( 92deg, #EFD7B6 0%, #F2E8D4 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 6px 14px;
        box-sizing: border-box;
        .ordinaryTop_vip_text{
            display: flex;
            flex-direction: column;
            line-height: 24px;
            font-size: 12px;
            color: #7F512B;
            img{
                width: 60px;
                height: 16px;
            }
        }
        .ordinaryTop_vip_btn{
            margin-left: auto;
            width: 90px;
            height: 30px;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: 0,0;
            background-image: url('@/assets/images/account/ordinaryTop_vip_btn.png');
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            span{
                font-size: 14px;
                color:#fff;
            }
        }
    }
}
</style>