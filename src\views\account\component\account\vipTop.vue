<template>
  <div class="vipTop">
    <div class="vipTop_account_img">
      <accountImg ref="account_img_ref"></accountImg>
    </div>
    <div class="vipTop_vip_box">
      <div
        class="vipTop_vip_box_card"
        :class="cards.length === 1 ? 'vipTop_vip_box_card_alone' : ''"
      >
        <Swiper
          ref="swiperRef"
          :modules="[Autoplay, Pagination, Navigation]"
          :slides-per-view="'auto'"
          :space-between="8"
          :centeredSlides="cards.length === 1?true:false"
          @slideChange="onSlideChange"
          class="vipTop_vip_box_card_swiper"
        >
        <!-- vipTop_vip_box_card_vip_bg -->
         <!-- vipTop_vip_box_card_digital_bg -->
          <SwiperSlide
            v-for="(card, index) in cards"
            :key="index"
            class="vipTop_vip_box_card_swiper_item"
            :class="[
              card.type === 'vip' ? 'vipTop_vip_box_card_vip_bg' : 'vipTop_vip_box_card_digital_bg',
              { active: currentIndex === index }
            ]"
            @click="goTo(index)"
          >
            <div class="vipTop_vip_box_card_swiper_item_top">
              <div class="vipTop_vip_box_card_swiper_item_top_label" v-if="card.label">
                <span >{{card.label}}</span>
              </div>
              <div class="vipTop_vip_box_card_swiper_item_top_date" :style="{'margin-left': card.label?'0':'17px'}">
                <span>
                  {{`有效期至 ${card.date} 结束` }}
                </span>
              </div>
            </div>
            <div class="vipTop_vip_box_card_swiper_item_content">
              <div class="vipTop_vip_box_card_swiper_item_content_info">
                <div class="vipTop_vip_box_card_swiper_item_content_info_label">
                  <img :src="getLabelImg(card.type)" alt="">
                </div>
                <div class="vipTop_vip_box_card_swiper_item_content_info_describe">
                  <span> 剩余{{card.type === 'vip'?'字符':'分钟'}}数：{{card.surplus}}{{card.type === 'vip'?'字符':''}}</span>
                </div>
               
              </div>
              <div class="vipTop_vip_box_card_swiper_item_content_btns">
                <el-button @click="renewal(card)">立即续费</el-button>
              </div>
            </div>
          
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
  </div>
</template>

<script setup>
import {reactive,ref,onMounted,nextTick} from 'vue'
import { useRouter } from 'vue-router'
import accountImg from './account_img.vue'
import { useloginStore } from '@/stores/login'
import vipTop_vip_box_card_swiper_vip_label from '@/assets/images/account/vipTop_vip_box_card_swiper_vip_label.svg'
import vipTop_vip_box_card_swiper_digital_label from '@/assets/images/account/vipTop_vip_box_card_swiper_digital_label.svg'
// 引入 Swiper 组件和样式
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination, Navigation } from 'swiper/modules'
import {accAdd,accSub,accMul,accDiv} from '@/utils/accuracy'
import 'swiper/swiper-bundle.css'
const router = useRouter()
const account_img_ref = ref(null)
const loginStore = useloginStore()

const cards = ref([{
  type:'vip',
  label:'VIP会员',
  date:'2025.03.03',
  surplus:1234,
  has:true
}
,{
  type:'digital',
  date:'2025.03.03',
  surplus:90,
  has:true
}
])
const currentIndex = ref(0)
const swiperRef = ref(null)

const goTo = (index) => {
  if (swiperRef.value && swiperRef.value.swiper) {
    swiperRef.value.swiper.slideTo(index)
  }
}

const onSlideChange = () => {
  if (swiperRef.value && swiperRef.value.swiper) {
    currentIndex.value = swiperRef.value.swiper.activeIndex
    adjustWrapperMargin()
  }
}

const adjustWrapperMargin = () => {
  nextTick(() => {
    const wrapper = swiperRef.value.$el.querySelector('.swiper-wrapper')
    if (!wrapper) return

    if (currentIndex.value === 0) {
      // 第一张卡片，左边距8px，右侧露出第二张卡片17px
      // 由于slidesPerView auto + spaceBetween 8px，默认间距是8px
      // 需要额外负margin-left 9px (17 - 8) 让第二张卡片露出17px
      wrapper.style.marginLeft = '0px'
    } else if (currentIndex.value === 1) {
      // 第二张卡片，左侧露出第一张卡片17px，左侧紧贴容器8px边距
      // 需要负margin-left 17px - 8px = 9px，向左移动9px
      wrapper.style.marginLeft = '-9px'
    }
  })
}

let getLabelImg=(type)=>{
  if(type=='vip'){
    return vipTop_vip_box_card_swiper_vip_label
  }else{
    return vipTop_vip_box_card_swiper_digital_label
  }
}
//续费
let renewal=(data)=>{
  let url = `${window.location.origin}/membership`
  if(data.type=='digital'){
    url = `${url}?nav=digital`;
  }
  window.open(url, '_blank');
}
let findArr=(type)=>{
 return cards.value.find(item => item.type === type)
}

let init = () => {
  const { memberInfo } = loginStore
  const vipCard = findArr('vip')
  const digitalCard = findArr('digital')
  let isExpired = (dateStr) => {
    if (!dateStr) return false
    const formattedDateStr = dateStr.replace(/\./g, '-')
    const cardDate = new Date(formattedDateStr)
    const now = new Date()
    return cardDate < now
  }

  if (memberInfo?.level?.end_time) {
    vipCard.label = memberInfo.level.level === 2 ? 'SVIP会员' : memberInfo.level.level === 1 ? 'VIP会员' : ''
    vipCard.date = memberInfo.level.end_time.replace(/\s+/g, '.')
    vipCard.surplus = sumSurplus(memberInfo?.total?.['Premium&Deluxe'] || 0, memberInfo?.current?.['Premium&Deluxe'] || 0)
    if (!cards.value.includes(vipCard)) cards.value.push(vipCard)
  } else {
    cards.value = cards.value.filter(item => item !== vipCard)
  }
  if (memberInfo?.digital_human?.end_time) {
    let digitalEndTime = memberInfo.digital_human.end_time.replace(/\s+/g, '.')
    if (!isExpired(digitalEndTime)) {
      digitalCard.date = digitalEndTime
      const totalSeconds = memberInfo.digital_human.digital_time || 0

      const minutes = Math.floor(totalSeconds / 60)
      const seconds = totalSeconds % 60
      digitalCard.surplus = minutes > 0 
        ? `${minutes}分钟${seconds.toString().padStart(2, '0')}秒` 
        : `${seconds}秒`
      if (!cards.value.includes(digitalCard)) cards.value.push(digitalCard)
    } else {
      cards.value = cards.value.filter(item => item !== digitalCard)
    }
  } else {
    cards.value = cards.value.filter(item => item !== digitalCard)
  }
}
let sumSurplus=(a=0,b=0)=>{
    return accSub(a,b)
}

onMounted(()=>{
  init()
  // 初始化调用
  onSlideChange()
})

</script>

<style lang="scss" scoped>
.vipTop {
  display: flex;
  flex-direction: column;
  width: 364px; // 323 + 8 + 8 + 17 + 8
  border-radius: 12px;
  box-sizing: border-box;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: visible;

  .vipTop_account_img {
    padding: 8px 12px 12px;
  }

  .vipTop_vip_box {
    width: 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    overflow-x: visible;

    .vipTop_vip_box_card {
      display: flex;
      width: 100%;
      overflow: visible;
      position: relative;
      user-select: none;
      cursor: pointer;

      .vipTop_vip_box_card_swiper {
        width: 100%;
        // padding-left: 8px; // 左边距8px
        // padding-right: 8px; // 右边距8px
        overflow: visible;

        .swiper-wrapper {
          display: flex;
          align-items: center;
          transition: margin-left 0.3s ease;
          will-change: margin-left;
        }

        .vipTop_vip_box_card_swiper_item {
          flex-shrink: 0;
          width: 323px !important; // 固定宽度
          height: 118px;
          transition: box-shadow 0.3s ease;
          user-select: none;
          font-size: 22px;
          line-height: 20px;
          color: #050b0d;
          border-radius: 12px;
          cursor: pointer;
          background-position: 0 0;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          position: relative;
          display: flex;
          flex-direction: column;
          .vipTop_vip_box_card_swiper_item_top{
            display: flex;
            .vipTop_vip_box_card_swiper_item_top_label{
              width: 81px;
              text-align: center;
              padding-top: 4px;
              line-height: 0;
              span{
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                // color: #42408D;
              }
            }
            .vipTop_vip_box_card_swiper_item_top_date{
              height: 22px;
              display: flex;
              align-items: center;
              span{
                font-weight: 400;
                font-size: 10px;
                line-height: 21px;
              }
            }
          }
          .vipTop_vip_box_card_swiper_item_content{
            width: 100%;
            padding: 11px 12px 0 16px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .vipTop_vip_box_card_swiper_item_content_info{
              display: flex;
              flex-direction: column;
              .vipTop_vip_box_card_swiper_item_content_info_label{
                width: 125px;
                height: 35px;
                img{
                  width: 100%;
                  height: 100%;
                }
              }
              .vipTop_vip_box_card_swiper_item_content_info_describe{
                span{
                  font-weight: 400;
                  font-size: 9px;
                  line-height: 21px;
                  // color: #35337C;
                }
              }
            }
            .vipTop_vip_box_card_swiper_item_content_btns{
              margin-left: auto;
              .el-button{
                width: 90px;
                height: 30px;
                // background: linear-gradient(90deg, #CEAD6D -48.89%, #7C4A16 100%);
                border-radius: 118px;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                border: none;
                ::v-deep(span){
                  font-size: 14px;
                  line-height: 14px;
                  color: #FFFFFF;
                }
              }
            }
          }
          &.vipTop_vip_box_card_vip_bg{
            background-image: url('@/assets/images/account/vipTop_vip_box_card_vip_bg.svg');
            .vipTop_vip_box_card_swiper_item_top{
              .vipTop_vip_box_card_swiper_item_top_label{
                span{
                  color: #7E4C18;
                }
              }
              .vipTop_vip_box_card_swiper_item_top_date{
                span{
                  color: #C59F53;
                }
              }
            }
            .vipTop_vip_box_card_swiper_item_content{
              .vipTop_vip_box_card_swiper_item_content_info{
                .vipTop_vip_box_card_swiper_item_content_info_describe{
                  span{
                    color: #98641F;
                  }
                }
              }
              .vipTop_vip_box_card_swiper_item_content_btns{
                .el-button{
                  background: linear-gradient(90deg, #CEAD6D -48.89%, #7C4A16 100%);
                }
              }
            }
          }
          &.vipTop_vip_box_card_digital_bg{
            background-image: url('@/assets/images/account/vipTop_vip_box_card_digital_bg.svg');
            .vipTop_vip_box_card_swiper_item_top{
              .vipTop_vip_box_card_swiper_item_top_label{
                span{
                  color: #42408D;

                }
              }
              .vipTop_vip_box_card_swiper_item_top_date{
                span{
                  color: #8280DB;

                }
              }
            }
            .vipTop_vip_box_card_swiper_item_content{
              .vipTop_vip_box_card_swiper_item_content_info{
                .vipTop_vip_box_card_swiper_item_content_info_describe{
                  span{
                    color:#35337C;
                  }
                }
              }
              .vipTop_vip_box_card_swiper_item_content_btns{
                .el-button{
                  background: linear-gradient(100.74deg, #8280DB 14.3%, #312F78 93.89%), linear-gradient(90deg, #CEAD6D -48.89%, #7C4A16 100%);
                }
              }
            }
          }
        }
      }

      &.vipTop_vip_box_card_alone {
        justify-content: center;
      }
    }
  }
}
</style>
