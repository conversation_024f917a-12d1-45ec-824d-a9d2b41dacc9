<template>
    <div class="edit_password_contanier">
       <span class="edit_password_tip">修改后可用新密码登录！</span>
       <el-form
       :rules="rules"
       :model="ruleForm"
       ref="ruleFormRef"
       label-width="0px"
       class="demo-ruleForm"
       >
           <el-form-item prop="password" :class="{ 'focus-border': isFocused.password }">
               <el-input v-model="ruleForm.password" type="text" autocomplete="off" placeholder="请输入密码" style="width:416px"  @focus="setFocus('password')" @blur="removeFocus('password')">
                   <template #prepend>输入密码</template>
               </el-input>
           </el-form-item>
           <el-form-item prop="againPassword" :class="{ 'focus-border': isFocused.againPassword }">
               <el-input v-model="ruleForm.againPassword"  type="text" autocomplete="off" placeholder="请再次输入密码" style="width:416px" @focus="setFocus('againPassword')" @blur="removeFocus('againPassword')">
                   <template #prepend>再次输入</template>
               </el-input>
           </el-form-item>
       </el-form>
   </div>
</template>
<script setup>
import {reactive,ref,defineExpose,defineEmits,defineProps} from 'vue'
let ruleFormRef=ref(null)
let emits=defineEmits(['getCode'])
let ruleForm = reactive({
    password:'',
    againPassword:'',
})
let validateAgainPassword= (rule, value, callback) => {
  if (value !== ruleForm.password) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
};

let isFocused = reactive({
  password: false,
  againPassword: false
});

let setFocus = (field) => {
  isFocused[field] = true;
};

let removeFocus = (field) => {
  isFocused[field] = false;
};
let rules = reactive({
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, max: 20, message: '密码:8-20位，数字+字母',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/}
    ],
    againPassword:[
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, max: 20, message: '密码:8-20位，数字+字母', trigger: 'blur', pattern: /^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/ },
        { validator: validateAgainPassword, trigger: 'blur' }
    ],
})
let reset=()=>{
    ruleFormRef.value&&ruleFormRef.value.resetFields()
}
defineExpose({
   ruleForm,
   reset,
   ruleFormRef
})

</script>
<style lang="scss" scoped>
  .edit_password_contanier{
           display: flex;
           flex-direction: column;
           .edit_password_tip{
               font-size: 16px;
               color: rgba(0,0,0,0.85);
               line-height: 24px;
               margin-bottom: 16px;
           }
           .el-form{
               .el-form-item{
                margin-bottom: 12px;
               .el-input{
                       background: #FFFFFF;
                       border-radius: 4px;
                       border: 1px solid #E7E7E7;
                       padding: 7px 8px;
                       display: flex;
                       align-items: center;
                       box-sizing: border-box;
                       :deep(.el-input-group__prepend){
                           background-color: transparent;
                           box-shadow: none;
                           line-height: 16px;
                           font-size: 14px;
                           color: rgba(0,0,0,0.45);
                           // width: 26px;
                           height: 16px;
                           padding: 0;
                           padding-right: 10px;
                           border-right: 1px solid #D3D3D2;;
                           display: flex;
                       }
                       :deep(.el-input__wrapper){
                           border-left: none;
                           box-shadow: none;
                           line-height: 20px;
                           height: 20px;
                           padding-left: 0;
                           .el-input__inner{
                               font-size: 14px;
                               color: #353D49;
                               line-height: 20px;
                               height: 20px;
                               padding-left: 10px;
                           }
                       }
                       .el-input-group__append{
                           background-color: transparent;
                           cursor: pointer;
                           border-right: none;
                           box-shadow: none;
                           padding: 0;
                           font-size: 14px;
                           color: #0AAF60;
                           line-height: 20px;
                           height: 20px;
                       }
                       &.is-disabled{
                           .el-input__wrapper{
                               background-color: transparent;
                               .el-input__inner{
                                   color: #D3D3D2;
                                  
                               }
                           }
                       }
                       
               }
               :deep(.el-form-item__error){
                position: static;
               }
               &.edit_password_verif_code{
                   button{
                       width: 126px;
                       height: 36px;
                       background: #FFFFFF;
                       border-radius: 4px;
                       border: 1px solid #E7E7E7;
                       font-size: 14px;
                       color: #353D49;
                       display: flex;
                       align-items: center;
                       justify-content: center;
                       margin-left: auto;
                   }
               }
               &:last-child{
                   margin-bottom: 0;
               }
               &.focus-border {
                .el-input{
                    border: 1px solid #0AAF60;
                }
                   
               }
               }
           }
       }
    
</style>