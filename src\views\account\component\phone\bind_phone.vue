<template>
    <div class="bind_phone_contanier">
       <span class="bind_phone_tip">解绑后将无法使用该手机号登陆此账号！</span>
       <span class="bind_phone_tip">当前手机号： {{ loginStore.userInfo.mobile }}</span>
       <el-form
       :rules="rules"
       :model="ruleForm"
       ref="ruleFormRef"
       label-width="0px"
       class="demo-ruleForm"
       >
           <el-form-item prop="verifCode" class="bind_phone_verif_code" style="width:416px" :class="{ 'focus-border': isFocused.verifCode }">
            <el-input v-model="ruleForm.verifCode" type="text" autocomplete="off"  placeholder="请输入验证码" style="width:282px" @focus="setFocus('verifCode')" @blur="removeFocus('verifCode')">
            </el-input>
            <el-button size="mini" type="primary" @click="getCode" v-if="is_get_code">获取验证码</el-button>
               <el-countdown :value="countDown"  ref="countdownRef"  @finish="handleFinish" format="ss" v-else >
                <template #suffix v-if="countdownRef&&countdownRef.displayValue">
                    <span>重新发送（{{ countdownRef.displayValue }}s）</span>
                </template>
               </el-countdown>
           </el-form-item>
       </el-form>
   </div>
</template>
<script setup>
import {reactive,ref,defineExpose,defineEmits,defineProps,watch,inject} from 'vue'
import { useloginStore } from '@/stores/login'
import { ElMessage } from "element-plus";
let loginStore = useloginStore()
let account_info = inject('account_info');
let countDown = ref(Date.now())
let ruleFormRef=ref(null)

let ruleForm = reactive({
   verifCode:'',
})
let countdownRef = ref(null);
let rules = reactive({
   verifCode:[
       { required: true, message: '请输入验证码', trigger: 'blur' },
   ],
})
let isFocused = reactive({
    verifCode: false,
});
let handleFinish = ()=>{
    console.log('handleFinish');
    
//   countDown.value = Date.now() + 60000
//   isCounting.value = true
    is_get_code.value=true
}
let setFocus = (field) => {
  isFocused[field] = true;
};

let removeFocus = (field) => {
  isFocused[field] = false;
};

let validateField = async (field) => {
  try {
    await ruleFormRef.value.validateField(field);
    return true;
  } catch (error) {
    return false;
  }
};
let is_get_code=ref(true)
let getCode=async()=>{
    let is_validate_phone=await validateField('phone')
    if(ruleForm.new_phone==''){
        ElMessage.error('请先输入手机号！');
        return
    }
    if(!is_validate_phone){
        ElMessage.error('手机号格式不正确！');
        return
  }
  is_get_code.value=false
  countDown.value = Date.now() + 60000;
}
let reset=()=>{
    ruleFormRef.value&&ruleFormRef.value.resetFields()
}
let init=()=>{
    ruleForm.verifCode=''
    is_get_code.value=true
}
defineExpose({
    ruleForm,
    reset,
    ruleFormRef,
    init
})
</script>
<style lang="scss" scoped>
  .bind_phone_contanier{
           display: flex;
           flex-direction: column;
           .bind_phone_tip{
               font-size: 16px;
               color: rgba(0,0,0,0.85);
               line-height: 24px;
               margin-bottom:4px;
           }
           .el-form{
                margin-top: 12px;
               .el-form-item{
                margin-bottom: 12px;
                &.bind_phone_verif_code{
                    button{
                            width: 126px;
                            height: 36px;
                            background: #FFFFFF;
                            border-radius: 4px;
                            border: 1px solid #E7E7E7;
                            font-size: 14px;
                            color: #353D49;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-left: auto;
                        }
               }
               :deep(.el-form-item__content){
                display: flex;
                align-items: center;
              
               .el-input{
                       background: #FFFFFF;
                       border-radius: 4px;
                       border: 1px solid #E7E7E7;
                       padding: 7px 8px;
                       display: flex;
                       align-items: center;
                       box-sizing: border-box;
                  
                      
                       .el-input__wrapper{
                           border-left: none;
                           box-shadow: none;
                           line-height: 20px;
                           height: 20px;
                           padding-left: 0;
                           .el-input__inner{
                               font-size: 14px;
                               color: #353D49;
                               line-height: 20px;
                               height: 20px;
                               padding-left: 10px;
                           }
                       }
               }
               :deep(.el-form-item__error){
                    position: static;
                }
              
            .el-statistic{
                margin-left: auto;
                width: 126px;
                box-sizing: border-box;
                cursor: pointer;
                .el-statistic__content{
                    width: 100%;
                    box-sizing: border-box;
                    height: 36px;
                    border-radius: 4px;
                    border: 1px solid #E7E7E7;
                    font-size: 14px;
                    color: #353D49;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: auto;
                    background-color: #E7E7E7;
                    color: rgba(0,0,0,0.45);
                    .el-statistic__number{
                        display: none;
                    }
                }
                
            }
              
            }
            &:last-child{
                   margin-bottom: 0;
               }
               &.focus-border {
                .el-input{
                    border: 1px solid #0AAF60;
                }
                   
               }
               }
           }
       }
</style>