<template>
     <div class="edit_phone_contanier">
        <span class="edit_phone_tip">绑定后可用该手机号登陆！</span>
        <el-form
        :rules="rules"
        :model="ruleForm"
        ref="ruleFormRef"
        label-width="0px"
        class="demo-ruleForm"
        >
            <el-form-item prop="new_phone" :class="{ 'focus-border': isFocused.new_phone }">
                <el-input v-model="ruleForm.new_phone" type="text" autocomplete="off" placeholder="输入新的手机号" style="width:416px"  @focus="setFocus('new_phone')" @blur="removeFocus('new_phone')">
                    <template #prepend>+86&nbsp;</template>
                </el-input>
            </el-form-item>
            <el-form-item prop="verifCode" class="edit_phone_verif_code" style="width:416px" :class="{ 'focus-border': isFocused.verifCode }">
                <el-input v-model="ruleForm.verifCode" type="text" autocomplete="off"  placeholder="请输入验证码" style="width:282px" @focus="setFocus('verifCode')" @blur="removeFocus('verifCode')">
                </el-input>
                <el-button size="mini" type="primary" @click="getCode" v-if="is_get_code">获取验证码</el-button>
                <el-countdown :value="countDown"  ref="countdownRef"  @finish="handleFinish" format="ss"  v-else>
                    <template #suffix v-if="countdownRef&&countdownRef.displayValue">
                        <span>重新发送（{{ countdownRef.displayValue }}s）</span>
                    </template>
                </el-countdown>
                <!-- <el-countdown :value="countDown" @finish="handleFinish" format="ss"  v-else>
                    <template #suffix>
                    <span>s后重新发送</span>
                    </template>
                </el-countdown> -->
            </el-form-item>
        </el-form>
    </div>
</template>
<script setup>
import {reactive,ref,defineExpose,defineEmits,defineProps,inject} from 'vue'
let ruleFormRef=ref(null)
import { ElMessage } from 'element-plus'
let account_info = inject('account_info');


let ruleForm = reactive({
    new_phone:'',
    verifCode:'',
})

let countDown = ref(Date.now())
let countdownRef = ref(null);
// 倒计时结束重置
let handleFinish = ()=>{
  countDown.value = Date.now() + 60000
  is_get_code.value = true
//   isCounting.value = true
}
let rules = reactive({
    verifCode:[
        { required: true, message: '请输入验证码', trigger: 'blur' },
    ],
    new_phone:[
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
    ],
})
let isFocused = reactive({
    new_phone: false,
    verifCode: false
});
let is_get_code=ref(true)
let setFocus = (field) => {
  isFocused[field] = true;
};

let removeFocus = (field) => {
  isFocused[field] = false;
};
let reset=()=>{
    ruleFormRef.value&&ruleFormRef.value.resetFields()
}
let validateField = async (field) => {
  try {
    await ruleFormRef.value.validateField(field);
    return true;
  } catch (error) {
    return false;
  }
};
let getCode=async()=>{
    let is_validate_phone=await validateField('phone')
    if(ruleForm.new_phone==''){
        ElMessage.error('请先输入手机号！');
        return
    }
    if(!is_validate_phone){
        ElMessage.error('手机号格式不正确！');
        return
  }
  is_get_code.value=false
  countDown.value = Date.now() + 60000;
}
let init=()=>{
    ruleForm.verifCode=''
    is_get_code.value=true
}
defineExpose({
    ruleForm,
    reset,
    ruleFormRef,
    init
    
})
</script>
<style lang="scss" scoped>
   .edit_phone_contanier{
            display: flex;
            flex-direction: column;
            .edit_phone_tip{
                font-size: 16px;
                color: rgba(0,0,0,0.85);
                line-height: 24px;
                margin-bottom: 16px;
            }
            .el-form{
                .el-form-item{
                 margin-bottom: 12px;
                .el-input{
                        background: #FFFFFF;
                        border-radius: 4px;
                        border: 1px solid #E7E7E7;
                        padding: 7px 8px;
                        display: flex;
                        align-items: center;
                        box-sizing: border-box;
                        :deep(.el-input-group__prepend){
                            background-color: transparent;
                            box-shadow: none;
                            line-height: 16px;
                            font-size: 14px;
                            color: #353D49;
                            // width: 26px;
                            height: 16px;
                            padding: 0;
                            padding-right: 10px;
                            border-right: 1px solid #D3D3D2;;
                            display: flex;
                        }
                        :deep(.el-input__wrapper){
                            border-left: none;
                            box-shadow: none;
                            line-height: 20px;
                            height: 20px;
                            padding-left: 0;
                            .el-input__inner{
                                font-size: 14px;
                                color: #353D49;
                                line-height: 20px;
                                height: 20px;
                                padding-left: 10px;
                            }
                        }
                        .el-input-group__append{
                            background-color: transparent;
                            cursor: pointer;
                            border-right: none;
                            box-shadow: none;
                            padding: 0;
                            font-size: 14px;
                            color: #0AAF60;
                            line-height: 20px;
                            height: 20px;
                        }
                        &.is-disabled{
                            .el-input__wrapper{
                                background-color: transparent;
                                .el-input__inner{
                                    color: #D3D3D2;
                                }
                            }
                        }
                }
                :deep(.el-form-item__error){
                    position: static;
                }
                &.edit_phone_verif_code{
                    button{
                        width: 126px;
                        height: 36px;
                        background: #FFFFFF;
                        border-radius: 4px;
                        border: 1px solid #E7E7E7;
                        font-size: 14px;
                        color: #353D49;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-left: auto;
                    }
                    .el-statistic{
                margin-left: auto;
                width: 126px;
                box-sizing: border-box;
                cursor: pointer;
                :deep(.el-statistic__content){
                    width: 100%;
                    box-sizing: border-box;
                    height: 36px;
                    border-radius: 4px;
                    border: 1px solid #E7E7E7;
                    font-size: 14px;
                    color: #353D49;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: auto;
                    background-color: #E7E7E7;
                    color: rgba(0,0,0,0.45);
                    .el-statistic__number{
                        display: none;
                    }
                }
                
            }
                }
                &:last-child{
                    margin-bottom: 0;
                }
                &.focus-border {
                .el-input{
                    border: 1px solid #0AAF60;
                }
                   
               }
                }
            }
        }
</style>