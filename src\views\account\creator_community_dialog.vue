<template>
    <el-dialog v-model="dialogVisable" class="creator_community_dialog" width="188px" :append-to-body="true" :style="{transformOrigin: 'top right',transform: `scale(${rate})`}">
        
        <template #default>
            <img src="@/assets/images/account/avant_img_close.png" class="creator_community_close" @click="close" alt="">
            <span class="creator_community_title">
                扫码加入创作者社群
            </span>
            <div class="creator_community_qrcode">
                <img :src="qrcode" alt="">
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import {reactive,ref,defineExpose,onMounted} from "vue"
import creatorCommunity from "@/assets/images/account/creator_community.png"
let dialogVisable=ref(false)
let qrcode=ref('')
onMounted(()=>{
    qrcode.value=creatorCommunity
})
let rate=ref(window.innerWidth/1920)
let close=()=>{
    dialogVisable.value=false
}
defineExpose({
    dialogVisable,
    qrcode
})
</script>
<style lang="scss">
.creator_community_dialog{
    position: relative;
    box-sizing: border-box;
    padding: 0;
    border-radius: 8px;
    .el-dialog__header{
        display: none;
    }
    .creator_community_close{
        width: 13px;
        height: 13px;
        position: absolute;
        top: 11px;
        right: 11px;
        cursor: pointer;
    }
    .el-dialog__body{
        padding: 43px 21px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        .creator_community_title{
            line-height: 20px;
            font-size: 14px;
            color: #000;
            margin-bottom: 11px;
            display: inline-block;
            font-weight: bold;
        }
        .creator_community_qrcode{
            width: 118px;
            height: 118px;
            img{
                width: 100%;
                height: 100%;
            }
        }
       
    }
}
</style>