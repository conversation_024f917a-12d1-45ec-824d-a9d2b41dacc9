<template>
	<div class="sign-calendar">
		<!-- 动态生成周签到 -->
		<div v-for="(week, index) in weeks" :key="index" class="calendar-section">
			<div class="section-header" :class="{ 'valid': week.isValid, 'expired': !week.isValid }">
				<img v-if="week.isValid" src="@/assets/img/Calendar.png" alt="日历" class="calendar-icon" />
				<img v-else src="@/assets/img/Calendar1.png" alt="日历" class="calendar-icon" />
				{{ week.range }}
			</div>
			<div class="calendar-grid">
				<div v-for="day in week.days" :key="day.date" class="calendar-day" :class="[day.status.toLowerCase(), { today: day.isToday }]">
					<!-- 第七天的提示图片 - 只在未开始状态显示 -->
					<img v-if="day.label === '周日' && day.status === 'FUTURE' && day.reward > 100" src="@/assets/img/tishi.png" alt="提示" class="tip-icon" />
					
					<div class="date-info">
						<div class="day-date">{{ day.date }}</div>
						<div class="day-label">{{ day.label }}</div>
					</div>
					<div class="reward">
						+{{ day.reward }}
						<img v-if="day.status === 'MISSED'" src="@/assets/img/Component1.png" alt="未签到" class="missed-reward-icon" />
						<img v-else-if="day.status === 'SIGNED'" src="@/assets/img/Component 2.png" alt="已签到" class="signed-reward-icon" />
						<img v-else-if="day.isToday" src="@/assets/img/jinbi.png" alt="金币" class="today-reward-icon" />
						<img v-else src="@/assets/img/jinbi.png" alt="金币" class="future-reward-icon" />
					</div>
					<button v-if="day.status === 'CAN_SIGN'" @click="handleSignIn(day)" class="sign-btn">签到</button>
					<button v-else-if="day.status === 'SIGNED'" class="signed-btn">✓ 签到+{{ day.consecutiveDays > 0 ? day.consecutiveDays : 1 }}</button>
					<button v-else-if="day.status === 'MISSED'" class="missed-btn">未签到</button>
					<button v-else-if="day.status === 'FUTURE'" class="future-btn">未开始</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from 'vue'
import { getDailyWelfareList, signIn } from '@/api/dailyWelfare'
import { useloginStore } from '@/stores/login'

// 定义 emit 事件
const emit = defineEmits(['update-total-points'])

const weeks = ref([])
const totalPoints = ref(0) // 存储当前可用积分
const loginStore = useloginStore() // 获取登录状态管理
const loading = ref(false) // 加载状态

/**
 * 获取从上个月第一个星期一到当前日期所在周周日的范围
 * @returns {Object} - 包含startDate和endDate的对象
 */
const getMonthWeekRanges = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = now.getMonth()
	
	// 获取上个月第一天，使用UTC避免时区问题
	const lastMonth = month === 0 ? 11 : month - 1
	const lastMonthYear = month === 0 ? year - 1 : year
	const lastMonthFirstDay = new Date(lastMonthYear, lastMonth, 1, 12, 0, 0) // 设置为中午避免时区问题
	
	console.log('上个月第一天:', lastMonthFirstDay.toISOString().split('T')[0], '星期', lastMonthFirstDay.getDay())
	
	// 找到上个月内的第一个星期一作为开始日期
	const firstMondayOfLastMonth = new Date(lastMonthFirstDay.getTime()) // 复制时间戳
	console.log('开始查找星期一，初始日期:', firstMondayOfLastMonth.toISOString().split('T')[0], '星期', firstMondayOfLastMonth.getDay())
	
	// 如果不是星期一，继续向后查找
	let searchCount = 0
	while (firstMondayOfLastMonth.getDay() !== 1 && searchCount < 7) { // 1 = 星期一，最多查找7天
		console.log('当前不是星期一（星期' + firstMondayOfLastMonth.getDay() + '），日期+1天')
		firstMondayOfLastMonth.setDate(firstMondayOfLastMonth.getDate() + 1)
		searchCount++
		console.log('新日期:', firstMondayOfLastMonth.toISOString().split('T')[0], '星期', firstMondayOfLastMonth.getDay())
	}
	
	console.log('找到星期一，退出循环')
	console.log('上个月第一个星期一:', firstMondayOfLastMonth.toISOString().split('T')[0])
	
	// 找到当前日期所在周的周日作为结束日期
	const currentSunday = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 12, 0, 0) // 同样使用中午时间
	const currentDayOfWeek = currentSunday.getDay() // 0=周日, 1=周一, ..., 6=周六
	
	// 计算到本周周日需要加的天数
	let daysToAdd
	if (currentDayOfWeek === 0) {
		// 如果今天就是周日，不需要调整
		daysToAdd = 0
	} else {
		// 其他情况：周一加6天，周二加5天，...，周六加1天
		daysToAdd = 7 - currentDayOfWeek
	}
	currentSunday.setDate(currentSunday.getDate() + daysToAdd)
	
	console.log('当前日期所在周的周日:', currentSunday.toISOString().split('T')[0])
	
	const result = {
		startDate: firstMondayOfLastMonth.toISOString().split('T')[0], // YYYY-MM-DD格式
		endDate: currentSunday.toISOString().split('T')[0] // YYYY-MM-DD格式
	}
	
	console.log('最终日期范围:', result)
	return result
}

/**
 * 获取每日福利数据
 */
const fetchDailyWelfareData = async () => {
	if (!loginStore.userId) {
		console.warn('用户未登录，无法获取每日福利数据')
		return
	}
	
	try {
		loading.value = true
		const dateRange = getMonthWeekRanges()
		
		const params = {
			userId: loginStore.userId,
			startDate: dateRange.startDate,
			endDate: dateRange.endDate
		}
		
		const response = await getDailyWelfareList(params)
		
		// 在处理 API 响应的 `totalPoints` 字段之后，但在处理 `weekStatsList` 之前，添加一个 `currentConsecutiveSignInDays` 变量并初始化为0。
		let currentConsecutiveSignInDays = 0;
		
		// 处理API返回数据，转换为组件需要的格式
		if (response && response.weekStatsList && Array.isArray(response.weekStatsList)) {
			// 提取并处理 totalPoints 字段
			if (response.totalPoints !== undefined) {       
				totalPoints.value = response.totalPoints
				// 通过 emit 事件将积分数据传递给父组件
				emit('update-total-points', response.totalPoints)
			}
			// 转换API数据为组件需要的格式，先处理每周数据
			const processedWeeks = response.weekStatsList.map(weekData => {
				const startDate = new Date(weekData.weekStart)
				const endDate = new Date(weekData.weekEnd)
				
				// 在处理每周数据之前，重置连续签到天数
				let consecutiveSignInDays = 0;
				
				// 格式化日期范围显示
				const formatDateForDisplay = (date) => {
					const month = date.getMonth() + 1
					const day = date.getDate()
					return `${month}月${day}日`
				}
				
				const range = `${formatDateForDisplay(startDate)}-${formatDateForDisplay(endDate)}`
				
				// 转换每日数据
				const days = weekData.days.map(dayData => {
					const dayDate = new Date(dayData.date)
					const today = new Date()
					const todayStr = today.toDateString()
					const dayStr = dayDate.toDateString()
					
					// 格式化日期为MM/DD格式
					const month = (dayDate.getMonth() + 1).toString().padStart(2, '0')
					const day = dayDate.getDate().toString().padStart(2, '0')
					const formattedDate = `${month}/${day}`
					
					// 状态映射：API中文状态 -> 组件英文状态
					let status = 'FUTURE'
					switch (dayData.status) {
						case '已签到':
							status = 'SIGNED'
							break
						case '未签到':
							status = 'MISSED'
							break
						case '未开始':
							status = 'FUTURE'
							break
						case '可签到':
							status = 'CAN_SIGN'
							break
						default:
							// 根据日期和当前时间判断状态
							if (dayStr === todayStr) {
								status = 'CAN_SIGN'
							} else if (dayDate < today) {
								status = 'MISSED'
							} else {
								status = 'FUTURE'
							}
					}
					
					// 如果是今天且状态不是已签到，则设为可签到
					if (dayStr === todayStr && status !== 'SIGNED') {
						status = 'CAN_SIGN'
					}
					
					// 获取星期标签（周一开头）
					const weekDayLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
					// JavaScript getDay() 返回 0(周日) 到 6(周六)，需要转换为周一开头的索引
					const dayOfWeek = dayDate.getDay()
					const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1 // 周日(0)转换为6，其他减1
					const dayLabel = weekDayLabels[dayIndex]
					
					// 根据签到状态更新连续签到天数
					if (status === 'SIGNED') {
						consecutiveSignInDays++;
					} else {
						consecutiveSignInDays = 0;
					}
					
					return {
						date: formattedDate,
						label: dayLabel,
						reward: dayData.points, // 使用API返回的积分
						status: status,
						isToday: dayStr === todayStr,
						apiDate: dayData.date, // 保留原始API日期格式，用于签到时调用API
						consecutiveDays: consecutiveSignInDays, // 添加连续签到天数
						dayOfWeek: dayOfWeek // 保存原始dayOfWeek用于排序
					}
				}).sort((a, b) => {
					// 按照周一开始的顺序排序：周一=1, 周二=2, ..., 周日=7
					const getWeekOrder = (dayOfWeek) => {
						return dayOfWeek === 0 ? 7 : dayOfWeek // 周日(0)转换为7，其他保持不变
					}
					return getWeekOrder(a.dayOfWeek) - getWeekOrder(b.dayOfWeek)
				})
				
				// 判断当前周是否有效（是否为当前周）
				const now = new Date()
				const isValid = startDate <= now && now <= endDate
				
				return {
					range,
					days,
					isValid,
					weekStart: weekData.weekStart,
					weekEnd: weekData.weekEnd
				}
			})
			
			// 自定义排序：当前周在第一位，其他周按时间倒序排列
			weeks.value = processedWeeks.sort((a, b) => {
				// 当前周优先显示在第一位
				if (a.isValid && !b.isValid) return -1
				if (!a.isValid && b.isValid) return 1
				
				// 如果都不是当前周，按时间倒序排列（最近的在前）
				if (!a.isValid && !b.isValid) {
					return new Date(b.weekStart) - new Date(a.weekStart)
				}
				
				// 如果都是当前周（理论上不会出现），保持原顺序
				return 0
			})
		} else {
			// API返回数据格式不正确时，使用默认逻辑
			console.warn('API返回数据格式不正确，使用默认逻辑')
			refreshCalendar()
		}
		
	} catch (error) {
		console.error('获取每日福利数据失败:', error)
		// 发生错误时使用默认数据，保证页面正常显示
		refreshCalendar()
	} finally {
		loading.value = false
	}
}

/**
 * 签到方法 - 集成真实API调用
 * @param {object} day - The day object to sign in.
 */
const handleSignIn = async (day) => {
	if (day.status !== 'CAN_SIGN' || !loginStore.userId) {
		return
	}
	
	try {
		// 调用签到API
		const signInParams = {
			userId: loginStore.userId,
			date: day.apiDate // 使用原始API日期格式 YYYY-MM-DD
		}
		
		const response = await signIn(signInParams)
		
		// API调用成功，更新本地状态
		day.status = 'SIGNED'
		
		// 可以添加成功提示
		console.log('签到成功:', day.date, response)
		
		// 重新获取最新数据以确保状态同步
		await fetchDailyWelfareData()
		
	} catch (error) {
		console.error('签到失败:', error)
		// 可以添加错误提示，比如使用 ElMessage 或其他提示组件
		// ElMessage.error('签到失败，请稍后重试')
	}
}

/**
 * 刷新日历数据 - 备用方法，当API调用失败时使用
 */
const refreshCalendar = () => {
	// 备用数据生成逻辑，生成基本的周数据结构
	const today = new Date()
	const currentWeek = generateBasicWeekData(0)
	const lastWeek = generateBasicWeekData(-1)
	const twoWeeksAgo = generateBasicWeekData(-2)
	const threeWeeksAgo = generateBasicWeekData(-3)
	
	weeks.value = [currentWeek, lastWeek, twoWeeksAgo, threeWeeksAgo]
	console.warn('使用备用数据生成逻辑')
}

/**
 * 生成基本的周数据（备用方法）
 * @param {number} weekOffset - 周偏移量
 * @returns {object} - 周数据对象
 */
const generateBasicWeekData = (weekOffset) => {
	const today = new Date()
	const currentDayOfWeek = today.getDay() === 0 ? 7 : today.getDay()
	const startOfWeek = new Date(today)
	startOfWeek.setDate(today.getDate() - (currentDayOfWeek - 1) + weekOffset * 7)

	const days = []
	const weekRange = {
		start: new Date(startOfWeek),
		end: new Date(startOfWeek)
	}
	weekRange.end.setDate(startOfWeek.getDate() + 6)

	const formatDate = (date, format) => {
		const month = (date.getMonth() + 1).toString()
		const dayNum = date.getDate().toString()
		if (format === 'header') {
			return `${month}月${dayNum}日`
		}
		return `${month.padStart(2, '0')}/${dayNum.padStart(2, '0')}`
	}

	const range = `${formatDate(weekRange.start, 'header')}-${formatDate(weekRange.end, 'header')}`
	const todayDateStr = formatDate(new Date(), 'short')

	for (let i = 0; i < 7; i++) {
		const currentDay = new Date(startOfWeek)
		currentDay.setDate(startOfWeek.getDate() + i)
		const currentDayStr = formatDate(currentDay, 'short')
		let status = 'FUTURE'
		const isToday = currentDayStr === todayDateStr

		if (currentDayStr < todayDateStr) {
			status = 'MISSED' // 默认过去的日期为未签到
		} else if (isToday) {
			status = 'CAN_SIGN' // 今天可以签到
		}

		const weekDayLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
		
		days.push({
			date: currentDayStr,
			label: weekDayLabels[i],
			reward: [10, 20, 30, 40, 50, 60, 150][i], // 默认积分
			status: status,
			isToday: isToday,
			apiDate: currentDay.toISOString().split('T')[0] // 提供API格式的日期
		})
	}

	return { 
		range, 
		days, 
		isValid: weekOffset === 0,
		weekStart: weekRange.start.toISOString().split('T')[0],
		weekEnd: weekRange.end.toISOString().split('T')[0]
	}
}

onMounted(() => {
	// 初始化时获取API数据
	fetchDailyWelfareData()
})

// 暴露给父组件的属性和方法
defineExpose({ fetchDailyWelfareData });
</script>

<style lang="scss" scoped>
.sign-calendar {
	.calendar-section {
		margin-bottom: 20px;
		width: 695px; /* 设置宽度为695px */

		&:last-child {
			margin-bottom: 0;
		}

		.section-header {
			display: flex;
			align-items: center;
			gap: 8px;
			margin-bottom: 12px;
			font-size: 14px;
			font-weight: 500;
			min-width: 144px;
			max-width: 160px;
			height: 32px;
			background-color: #F3F5F5;
			border-radius: 4px;
			padding: 0 8px;

			.calendar-icon {
				width: 16px;
				height: 16px;
				margin-top: -1px;
			}

			&.valid {
				color: #000000;
			}

			&.expired {
				color: #9CA3AF;
			}
		}

		.calendar-grid {
			display: grid;
			grid-template-columns: repeat(5, 127px);
			gap: 15px;

			.calendar-day {
				width: 127px;
				height: 163px;
				box-sizing: border-box;
				border: 1px solid #e2e8f5;
				border-radius: 16px;
				background: #F3F5F5;
				margin-bottom: 14px;
				padding: 20px 0;
				transition: all 0.2s;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				position: relative;
				overflow: visible;

				&.missed {
					background: #f8f9fa;
					opacity: 0.7;
				}

				&.future {
					background: linear-gradient(90deg, #0AAF60, #5CCADE);
					padding: 1px; /* 边框厚度 */
					
					/* 重新设置flex布局，因为padding会影响原有布局 */
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					align-items: center;
					box-sizing: border-box;
					
					/* 内层背景 */
					&::before {
						content: '';
						position: absolute;
						top: 1px;
						left: 1px;
						right: 1px;
						bottom: 1px;
						background: #F3F5F5;
						border-radius: 15px; /* 比外层小1px */
						z-index: 0;
					}
					
					/* 确保内容在最上层并调整内边距 */
					.date-info,
					.reward,
					button {
						position: relative;
						z-index: 1;
					}
					
					/* 调整内容区域的padding，因为外层已经有1px的padding了 */
					padding: 21px 1px 21px 1px; /* 上下20px + 1px边框 = 21px，左右保持1px边框 */
				}

				&.today {
					border-color: transparent;
					box-shadow: none;
					background: #0AAF60;

					.day-date,
					.day-label {
						color: #FFFFFF;
					}

					&.signed {
						.day-date,
						.day-label {
							color: #181B1A; /* 当天且已签到时的文字颜色 */
						}
					}
				}
				&.signed {
					border-color: #e2e8f5;
					background: #CEEFDF;
					.day-date,
					.day-label {
						color: #181B1A; /* 签到后的文字颜色 */
					}
				}

				.date-info {
					display: flex;
					align-items: center;
					gap: 5px; /* 可以根据需要调整日期和标签之间的间距 */
				}

				.day-date {
					font-size: 16px;
					color: #181B1A;
					font-weight: 500;
					font-family: 'PingFang SC', sans-serif;
				}

				.day-label {
					font-size: 16px;
					color: #181B1A;
					font-weight: 500;
					font-family: 'PingFang SC', sans-serif;
				}

				.reward {
					font-size: 20px;
					color: #FF4311;
					font-weight: 700;
					text-align: center;
				}

				.missed-reward-icon {
					width: 35px; /* 调整图片宽度 */
					height: 35px; /* 调整图片高度 */
					vertical-align: middle; /* 使图片与文本垂直对齐 */
				}

				.signed-reward-icon {
					width: 30px; /* 调整图片宽度 */
					height: 30px; /* 调整图片高度 */
					vertical-align: middle; /* 使图片与文本垂直对齐 */
					transform: translateY(-2px);
				}

				.today-reward-icon {
					width: 35px; /* 调整图片宽度 */
					height: 35px; /* 调整图片高度 */
					vertical-align: middle; /* 使图片与文本垂直对齐 */
				}

				.future-reward-icon {
					width: 35px; /* 调整图片宽度 */
					height: 35px; /* 调整图片高度 */
					vertical-align: middle; /* 使图片与文本垂直对齐 */
					margin-left: -2px; /* 向左移动2px */
				}

				.tip-icon {
					width: 71px;
					position: absolute;
					top: -25px;
					right: -15px;
					animation: float-glow 2s ease-in-out infinite, fadeIn 0.5s ease-in;
					filter: drop-shadow(0 0 8px rgba(255, 193, 7, 0.6));
				}

				@keyframes float-glow {
					0%, 100% {
						transform: translateY(0px);
						filter: drop-shadow(0 0 8px rgba(255, 193, 7, 0.6));
					}
					50% {
						transform: translateY(-6px);
						filter: drop-shadow(0 0 12px rgba(255, 193, 7, 0.8));
					}
				}

				@keyframes fadeIn {
					from {
						opacity: 0;
						transform: scale(0.8);
					}
					to {
						opacity: 1;
						transform: scale(1);
					}
				}

				button {
					width: 58px;
					height: 20px;
					padding: 0;
					line-height: 20px;
					display: block;
					border: none;
					border-radius: 4px;
					font-size: 12px;
					text-align: center;
					cursor: pointer;
					transition: all 0.2s;

					&.sign-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 100px;
						font-family: HarmonyOS_Sans_SC;
						background: linear-gradient(90deg, #FDFDCC, #FDCC54);
						color: #12321B;
						width: 78px;
						height: 28px;
						font-size: 14px;

						&:hover {
							filter: brightness(0.95);
						}
					}
					
					&.signed-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 78px;
						height: 28px;
						background: #0AAF6066;
						opacity: 0.4;
						border-radius: 100px;
						font-family: HarmonyOS_Sans_SC;
						color: #12321B;
						font-size: 14px;
					}

					&.future-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 78px;
						height: 28px;
						border-radius: 100px;
						font-family: HarmonyOS_Sans_SC;
						background: transparent;
						border: 1px solid #B2C8BE;
						color: #B2C8BE;
						font-size: 14px;
						cursor: not-allowed;
					}

					&.missed-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 78px;
						height: 28px;
						border-radius: 100px;
						font-family: HarmonyOS_Sans_SC;
						background: #B2C9BE1A;
						color: #B2C8BE;
						font-size: 14px;
					}
				}
			}
		}
	}
}

// 暗色主题适配
:global(.dark) {
	.sign-calendar {
		.calendar-day {
			background: #2d3748;
			border-color: #4a5568;
			color: #e2e8f0;

			&.signed {
				background: #2d3748;
				border-color: #4a5568;
			}
			&.today {
				border-color: #6abb8c;
			}
		}
	}
}
</style> 