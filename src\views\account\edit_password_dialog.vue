<template>
    <el-dialog v-model="dialogVisable" class="edit_password_dialog" width="464px" :append-to-body="true" :style="{transformOrigin: 'top right',transform: `scale(${rate})`}">
        <template #header>
            <div class="edit_password_header">
                <span>
                  修改密码
                </span>
                <img src="@/assets/images/account/avant_img_close.png" @click="close" alt="">
            </div>
        </template>
        <template #default>
            <sendCode ref="send_code_ref" v-if="!send_code"></sendCode>
            <editPassword ref="edit_password_ref" v-else></editPassword>
        </template>
        <template #footer>
            <div class="edit_password_btns">
                <button class="edit_password_cancel" @click="edit_password_cancel">取消</button>
                <button class="edit_password_submit" @click="edit_password_submit" v-loading="save_loading">确定</button>
            </div>
        </template>
   </el-dialog>
</template>
<script setup>
import {reactive,ref,defineExpose,watch,defineEmits,nextTick,inject} from 'vue'
import sendCode from "./component/password/bind_phone.vue"
import editPassword from "./component/password/edit_password.vue"
import { ElMessage } from 'element-plus'
import {resetPassword} from '@/api/account.js'
import { useloginStore } from '@/stores/login'
import { encryptPassword } from '@/utils/publicKey.js';
let loginStore = useloginStore()
let rate=ref(window.innerWidth/1920)
let emit=defineEmits(['change_password'])
let edit_password_ref=ref(null)
let dialogVisable=ref(false)
let close=()=>{
    send_code.value=false
    dialogVisable.value=false
}
let isCounting = ref(true)
let send_code=ref(false)
let countDown = ref(Date.now())
let getCode = async ()=>{
  countDown.value = Date.now() + 60000;
  isCounting.value = false
  
}
// 倒计时结束重置
let handleFinish = ()=>{
  countDown.value = Date.now() + 60000
  isCounting.value = true
}
let send_code_ref=ref(null)
let edit_password_cancel=()=>{
    close()
}
let password_param=ref({})
let save_loading=ref(false)
let edit_password_submit=()=>{
    if(!send_code.value){
        send_code_ref.value.ruleFormRef.validate((valid) => {
            if (valid) {
                console.log(password_param,555);
                
                password_param.value.mobile=loginStore.userInfo.mobile
                password_param.value.code=send_code_ref.value.ruleForm.verifCode
                send_code_ref.value.init()
                send_code.value = true
               
               
            } else {
                ElMessage.error('提交失败，请检查');
                return false;
            }
        })
    }else{
        edit_password_ref.value.ruleFormRef.validate(async(valid) => {
            if (valid) {
                password_param.value.password=encryptPassword(edit_password_ref.value.ruleForm.password)
                password_param.value.confirmPassword=encryptPassword(edit_password_ref.value.ruleForm.password)
                save_loading.value=true
                let data=await resetPassword(password_param.value)
                ElMessage({ message:'修改密码成功', type: 'success' });
                save_loading.value=false
                emit('change_password',edit_password_ref.value.ruleForm.password)
                edit_password_ref.value.reset()
                close()
                send_code.value = false
            } else {
                ElMessage.error('提交失败，请检查');
                return false;
            }
        })
    }
   
  
 
}
defineExpose({
    dialogVisable
})
watch(dialogVisable, (newValue, oldValue) => {
   if(!newValue){
    send_code_ref.value&&send_code_ref.value.init()
    edit_password_ref.value&&edit_password_ref.value.reset()
   }
    // 在这里可以执行其他逻辑
});
</script>
<style lang="scss">
.edit_password_dialog{
    padding: 0;
    .el-dialog__header{
        padding: 0;
        .el-dialog__headerbtn{
            display: none;
        }
    }
    
    .edit_password_header{
        padding: 8px 21px 8px 16px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-bottom:1px  solid rgba(240, 240, 240, 1) ;
        span{
            font-size: 16px;
            color: rgba(0,0,0,0.85);
            line-height: 24px;
        }
        img{
            width: 13px;
            height: 13px;
            margin-left: auto;
            cursor: pointer;
        }
    }
    .el-dialog__body{
        padding: 32px 24px;
        width: 100%;
        box-sizing: border-box;
        padding-bottom: 0;
   
    }
    .el-dialog__footer{
        padding: 32px 24px;
        width: 100%;
        box-sizing: border-box;
        .edit_password_btns{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            button{
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 8px;
                border: none;
                border-radius: 2px;
                box-sizing: border-box;
                cursor: pointer;
                font-size: 14px;
                &.edit_password_cancel{
                    padding: 0 16px;
                    background-color: #F2F3F5;
                    color: #4E5969;
                }
                &.edit_password_submit{
                    padding: 0 28px;
                    background-color: #0AAF60;
                    color: #fff;
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
    }
}
</style>