<template>
    <el-dialog v-model="dialogVisable" class="edit_phone_dialog" width="464px" :append-to-body="true" :style="{transformOrigin: 'top right',transform: `scale(${rate})`}">
        <template #header>
            <div class="edit_phone_header">
                <span>
                    <template v-if="isCounting">修改手机号</template>
                    <template v-else>绑定新手机</template>
                </span>
                <img src="@/assets/images/account/avant_img_close.png" @click="close" alt="">
            </div>
        </template>
    
        <template #default>
            <editPhone ref="edit_phone_ref"  v-if="isCounting"></editPhone>
            <bindPhone ref="bind_phone_ref"  v-else></bindPhone>
        </template>
        <template #footer>
            <div class="edit_phone_btns">
                <button class="edit_phone_cancel" @click="edit_phone_cancel">取消</button>
                <button class="edit_phone_submit" @click="edit_phone_submit">确定</button>
            </div>
        </template>
   </el-dialog>
</template>
<script setup>
import {reactive,ref,defineExpose,watch,defineEmits,inject, nextTick} from 'vue'
import editPhone from "./component/phone/edit_phone.vue"
import bindPhone from "./component/phone/bind_phone.vue"
import { ElMessage } from 'element-plus'
let account_info = inject('account_info');
let emit=defineEmits(['update_phone'])
let rate=ref(window.innerWidth/1920)
let dialogVisable=ref(false)
let close=()=>{
    edit_phone_ref.value&&edit_phone_ref.value.init()
    bind_phone_ref.value&&bind_phone_ref.value.init()
    dialogVisable.value=false
   
}
let user=ref({
    new_phone:""
})
let edit_phone_ref=ref(null)
let bind_phone_ref=ref(null)
let isCounting = ref(true)
let countDown = ref(Date.now())

let edit_phone_cancel=()=>{
    close()
}
let edit_phone_submit=()=>{
    if(isCounting.value){
            edit_phone_ref.value.ruleFormRef.validate((valid) => {
            if (valid) {
                user.new_phone=edit_phone_ref.value.ruleForm.new_phone 
                isCounting.value = false
                nextTick(()=>{
                   
                    bind_phone_ref.value.init()
                    
                })
               
            } else {
                ElMessage.error('提交失败，请检查');
                return false;
            }
        })
    }else{
        bind_phone_ref.value.ruleFormRef.validate((valid) => {
        if (valid) {
            emit('update_phone',user.new_phone)
            close()
        } else {
            ElMessage.error('提交失败，请检查');
            return false;
        }
    })
    }
}
defineExpose({
    dialogVisable
})
watch(dialogVisable, (newValue, oldValue) => {
   if(!newValue){
     if(isCounting.value){
        edit_phone_ref.value.reset()
     }else{
        bind_phone_ref.value&&bind_phone_ref.value.reset()
     }

   }else{
    isCounting.value=true
    nextTick(()=>{
        edit_phone_ref.value.ruleForm.new_phone=account_info.phone;
    
    })

   }
});
</script>
<style lang="scss">
.edit_phone_dialog{
    padding: 0;
    .el-dialog__header{
        padding: 0;
        .el-dialog__headerbtn{
            display: none;
        }
    }
    
    .edit_phone_header{
        padding: 8px 21px 8px 16px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-bottom:1px  solid rgba(240, 240, 240, 1) ;
        span{
            font-size: 16px;
            color: rgba(0,0,0,0.85);
            line-height: 24px;
        }
        img{
            width: 13px;
            height: 13px;
            margin-left: auto;
            cursor: pointer;
        }
    }
    .el-dialog__body{
        padding: 32px 24px;
        width: 100%;
        box-sizing: border-box;
        padding-bottom: 0;
   
    }
    .el-dialog__footer{
        padding: 32px 24px;
        width: 100%;
        box-sizing: border-box;
        .edit_phone_btns{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            button{
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 8px;
                border: none;
                border-radius: 2px;
                box-sizing: border-box;
                cursor: pointer;
                font-size: 14px;
                &.edit_phone_cancel{
                    padding: 0 16px;
                    background-color: #F2F3F5;
                    color: #4E5969;
                }
                &.edit_phone_submit{
                    padding: 0 28px;
                    background-color: #0AAF60;
                    color: #fff;
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
    }
}
</style>