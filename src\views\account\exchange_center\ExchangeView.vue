<template>
	<div class="exchange-view">
		<!-- 加载状态 -->
		<div v-if="loading" class="loading-container">
			<div class="loading-text">正在加载兑换商品...</div>
		</div>
		
		<!-- 内容区域 -->
		<div v-else>
			<!-- VIP会员区域 -->
			<div class="exchange-section" v-if="vipItems.length > 0">
				<div class="section-row">
					<div 
						v-for="item in vipItems" 
						:key="item.id"
						class="exchange-card vip-card"
						@click="handleExchange(item)"
					>
						<div class="vip-label">
							<span class="label-text">VIP会员</span>
						</div>
						<div class="card-content">
							<div class="duration">{{ item.duration }}</div>
							<div class="cost">{{ item.cost }}个积分兑换</div>
						</div>
					</div>
				</div>
			</div>

			<!-- SVIP会员区域 -->
			<div class="exchange-section" v-if="svipItems.length > 0">
				<div class="section-row">
					<div 
						v-for="item in svipItems" 
						:key="item.id"
						class="exchange-card svip-card"
						@click="handleExchange(item)"
					>
						<div class="svip-label">
							<span class="label-text">SVIP会员</span>
						</div>
						<div class="card-content">
							<div class="duration">{{ item.duration }}</div>
							<div class="cost">{{ item.cost }}个积分兑换</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 加油包区域 -->
			<div class="exchange-section" v-if="boostItems.length > 0">
				<div class="section-row">
					<div 
						v-for="item in boostItems" 
						:key="item.id"
						class="exchange-card boost-card"
						@click="handleExchange(item)"
					>
						<div class="boost-label">
							<span class="label-text">加油包-字符</span>
						</div>
						<div class="card-content">
							<div class="boost-amount">{{ item.amount }}</div>
							<div class="cost">{{ item.cost }}个积分兑换</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 算力区域 -->
			<div class="exchange-section" v-if="computeItems.length > 0">
				<div class="section-row">
					<div 
						v-for="item in computeItems" 
						:key="item.id"
						class="exchange-card compute-card"
						@click="handleExchange(item)"
					>
						<div class="compute-label">
							<span class="label-text">加油包-算粒</span>
						</div>
						<div class="card-content">
							<div class="compute-amount">{{ item.amount }}</div>
							<div class="cost">{{ item.cost }}个积分兑换</div>
						</div>
					</div>
					<!-- 填充第三个位置保持布局 -->
					<div class="exchange-card placeholder-card"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getExchangePlanList, exchangeItem } from '@/api/dailyWelfare';
import { useloginStore } from '@/stores/login';
import { ElMessage } from 'element-plus';

// 定义 props
const props = defineProps({
	totalPoints: {
		type: Number,
		default: 0
	}
});

// 定义 emit 事件
const emit = defineEmits(['exchange-success']);

// 加载状态
const loading = ref(true);

// Pinia store
const loginStore = useloginStore();

// VIP会员数据
const vipItems = ref([]);

// SVIP会员数据  
const svipItems = ref([]);

// 加油包数据
const boostItems = ref([]);

// 算力数据
const computeItems = ref([]);

// 获取兑换列表数据
const fetchExchangeList = async () => {
	try {
		loading.value = true;
		
		// 从Pinia store中获取真实的用户ID
		const userId = loginStore.userId;
		console.log('当前用户ID:', userId);
		
		if (!userId) {
			console.warn('用户ID未获取到，无法请求兑换列表');
			setDefaultData();
			return;
		}
		
		const response = await getExchangePlanList({ userId });
		console.log('API完整响应:', response);
		// 根据返回的数据格式处理数据
		if (response  && response.plans) {
			const plans = response.plans;
			console.log('所有商品数据:', plans);
			
			// 打印每个商品的level，用于调试
			plans.forEach(item => {
				console.log(`商品: ${item.planName}, level: ${item.level}, 类型分析:`, item);
			});
			
			// 根据level字段分类商品
			// level: 1=VIP, 2=SVIP, 4=加油包
			vipItems.value = plans.filter(item => item.level === 1)
				.sort((a, b) => a.cycle - b.cycle) // 按天数升序排序
				.map(item => ({
					id: item.id,
					type: 'vip',
					duration: `${item.cycle}天`,
					cost: item.needNum,
					planName: item.planNameCN || item.planName,
					originalData: item
				}));
			
			svipItems.value = plans.filter(item => item.level === 2)
				.sort((a, b) => a.cycle - b.cycle) // 按天数升序排序
				.map(item => ({
					id: item.id,
					type: 'svip', 
					duration: `${item.cycle}天`,
					cost: item.needNum,
					planName: item.planNameCN || item.planName,
					originalData: item
				}));
			
			boostItems.value = plans.filter(item => item.level === 4).map(item => ({
				id: item.id,
				type: 'boost',
				amount: item.planName.includes('万字符') ? item.planName : `${item.planName}`,
				cost: item.needNum,
				planName: item.planNameCN || item.planName,
				originalData: item
			}));
			
			// 处理算力数据 - 根据实际返回的level值处理
			// 先尝试常见的level值：3, 5, 6等，或者包含"算粒"关键词的商品
			const computeItems_temp = plans.filter(item => {
				// 方式1：通过level值判断（需要根据实际API确定）
				const isComputeByLevel = item.level === 3 || item.level === 5 || item.level === 6;
				// 方式2：通过商品名称判断
				const isComputeByName = (item.planName && item.planName.includes('算粒')) || 
									  (item.planNameCN && item.planNameCN.includes('算粒'));
				return isComputeByLevel || isComputeByName;
			});
			
			computeItems.value = computeItems_temp.map(item => ({
				id: item.id,
				type: 'compute',
				amount: item.planName.includes('算粒') ? item.planName : `${item.planName}`,
				cost: item.needNum,
				planName: item.planNameCN || item.planName,
				originalData: item
			}));
			
			console.log('分类后的数据:');
			console.log('VIP:', vipItems.value);
			console.log('SVIP:', svipItems.value);
			console.log('加油包:', boostItems.value);
			console.log('算粒:', computeItems.value);
		}
	} catch (error) {
		console.error('获取兑换列表失败:', error);
		// 如果接口调用失败，可以使用默认数据
		setDefaultData();
	} finally {
		loading.value = false;
	}
};

// 设置默认数据（作为兜底方案）
const setDefaultData = () => {
	vipItems.value = [
		{ id: 'vip-1', type: 'vip', duration: '1天', cost: 300 },
		{ id: 'vip-7', type: 'vip', duration: '7天', cost: 2000 },
		{ id: 'vip-30', type: 'vip', duration: '30天', cost: 9000 }
	];

	svipItems.value = [
		{ id: 'svip-1', type: 'svip', duration: '1天', cost: 1000 },
		{ id: 'svip-7', type: 'svip', duration: '7天', cost: 7000 },
		{ id: 'svip-30', type: 'svip', duration: '30天', cost: 30000 }
	];

	boostItems.value = [
		{ id: 'boost-20k', type: 'boost', amount: '2万字符', cost: 500 },
		{ id: 'boost-100k', type: 'boost', amount: '10万字符', cost: 2500 },
		{ id: 'boost-200k', type: 'boost', amount: '20万字符', cost: 5000 }
	];

	computeItems.value = [
		{ id: 'compute-100', type: 'compute', amount: '100算粒', cost: 2500 },
		{ id: 'compute-500', type: 'compute', amount: '500算粒', cost: 12000 }
	];
};

// 更新用户信息的函数
const updateUserInfo = async () => {
	try {
		const { userInfo } = await import('@/api/account');
		const userData = await userInfo({ userId: loginStore.userId });
		loginStore.setUserInfo(userData);
		console.log('用户信息更新成功');
	} catch (error) {
		console.error('更新用户信息失败:', error);
	}
};

// 兑换处理函数
const handleExchange = async (item) => {
	console.log('兑换项目:', item);

	// 从Pinia store中获取真实的用户ID
	const userId = loginStore.userId;
	
	if (!userId) {
		console.warn('用户ID未获取到，无法进行兑换');
		ElMessage.error('用户ID未获取到，请重新登录！');
		return;
	}

	// 积分校验
	const userCredits = props.totalPoints;
	const requiredCredits = item.cost;
	
	console.log('当前积分:', userCredits, '需要积分:', requiredCredits);
	
	if (userCredits <= 0 || userCredits < requiredCredits) {
		console.warn('积分不足，无法兑换');
		ElMessage.error('积分不足，无法兑换');
		return;
	}

	// 移除confirm弹窗，直接进行兑换操作
	try {
		// 从原始数据中获取level字段
		const level = item.originalData?.level;
		
		const response = await exchangeItem({
			userId: userId,
			planId: item.id,
			level: level // 新增level字段
		});
		console.log('兑换接口返回:', response);

		if (response) {
			ElMessage.success(response);
			// 更新用户信息，包括积分
			await updateUserInfo();
			// 刷新兑换列表
			fetchExchangeList(); 
			emit('exchange-success'); // 发出兑换成功事件
		} else {
			ElMessage.error(response);
		}
	} catch (error) {
		console.error('调用兑换接口失败:', error);
		ElMessage.error('兑换请求失败，请稍后再试！');
	}
};

// 组件挂载时获取数据
onMounted(() => {
	fetchExchangeList();
});
</script>

<style lang="scss" scoped>
.exchange-view {
	// padding: 20px;
	// background: #f5f7fa;
	// min-height: 100vh;
}

// 加载状态样式
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
	
	.loading-text {
		font-size: 16px;
		color: #666;
		font-family: 'PingFang SC';
	}
}

.exchange-section {
	margin-bottom: 15px;
}

.section-row {
	display: flex;
	gap: 20px;
	justify-content: flex-start;
}

.exchange-card {
	width: 217px;
	height: 110px;
	border-radius: 12px;
	position: relative;
	cursor: pointer;
	transition: all 0.3s ease;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	overflow: hidden;
	
	&:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	}
	
	&.placeholder-card {
		visibility: hidden;
		cursor: default;
		
		&:hover {
			transform: none;
			box-shadow: none;
		}
	}
}

// VIP卡片样式
.vip-card {
	background-image: url('@/assets/img/vip.png');
	
	.vip-label {
		position: absolute;
		top: 2px;
		left: 55%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.label-text {
			font-size: 14px;
			font-weight: 600;
			color: #29263A;
			font-family: PingFang SC;
		}
	}
	
	.card-content {
		padding: 12px 16px;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
	}
	
	.duration {
		font-size: 21px;
		font-weight: 700;
		font-family: 'Alibaba PuHuiTi 2.0';
		text-align: center;
		color: #FF4311;
		margin-top: 28px;
		margin-bottom: 18px;
	}
	
	.cost {
		font-size: 14px;
		font-family: 'PingFang SC';
		text-align: center;
		color: #FFFFFF;
	}
}

// SVIP卡片样式
.svip-card {
	background-image: url('@/assets/img/svip.png');
	
	.svip-label {
		position: absolute;
		top: 2px;
		left: 55%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.label-text {
			font-size: 14px;
			font-weight: 600;
			color: #281787;
			font-family: PingFang SC;
		}
	}
	
	.card-content {
		padding: 12px 16px;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
	}
	
	.duration {
		font-size: 21px;
		font-weight: 700;
		font-family: 'Alibaba PuHuiTi 2.0';
		text-align: center;
		color: #FF4311;
		margin-top: 28px;
		margin-bottom: 18px;
	}
	
	.cost {
		font-size: 14px;
		font-family: 'PingFang SC';
		text-align: center;
		color: #FFFFFF;
	}
}

// 加油包卡片样式
.boost-card {
	background-image: url('@/assets/img/jiayoubao.png');
	
	.boost-label {
		position: absolute;
		top: 2px;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.label-text {
			font-size: 14px;
			font-weight: 600;
			color: #29263A;
			font-family: PingFang SC;
			margin-top: 6px;
		}
	}
	
	.card-content {
		padding: 12px 16px;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #333;
	}
	
	.boost-amount {
		font-size: 21px;
		font-weight: 700;
		font-family: 'Alibaba PuHuiTi 2.0';
		text-align: center;
		color: #FF4311;
		margin-top: 28px;
		margin-bottom: 18px;
	}
	
	.cost {
		font-size: 14px;
		font-weight: 500;
		font-family: 'PingFang SC';
		text-align: center;
		color: #92730C;
	}
}

// 算力卡片样式
.compute-card {
	background-image: url('@/assets/img/suanli.png');
	
	.compute-label {
		position: absolute;
		top: 6px;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.label-text {
			font-size: 14px;
			font-weight: 600;
			color: #29263A;
			font-family: PingFang SC;
		}
	}
	
	.card-content {
		padding: 12px 16px;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #333;
	}
	
	.compute-amount {
		font-size: 21px;
		font-weight: 700;
		font-family: 'Alibaba PuHuiTi 2.0';
		text-align: center;
		color: #FF4311;
		margin-top: 28px;
		margin-bottom: 18px;
	}
	
	.cost {
		font-size: 14px;
		font-weight: 500;
		font-family: 'PingFang SC';
		text-align: center;
		color: #9C580A;
	}
}

// 响应式设计
@media (max-width: 768px) {
	.exchange-view {
		padding: 16px;
	}
	
	.section-row {
		flex-direction: column;
		align-items: center;
		gap: 12px;
	}
	
	.exchange-card {
		width: 100%;
		max-width: 300px;
	}
}

@media (max-width: 480px) {
	.exchange-card {
		width: 100%;
		max-width: 280px;
		height: 100px;
		
		.card-content {
			padding: 10px 14px;
		}
		
		.duration, .boost-amount, .compute-amount {
			font-size: 18px;
		}
		
		.cost {
			font-size: 11px;
		}
	}
}
</style> 