<template>
    <div class="account_information" >
        <!-- 普通用户头部 -->
        <!-- <ordinary ref="ordinary_ref" v-if="user.sign == ''"></ordinary> -->
         
        <!-- 会员过期头部 -->
         <!--  -->
        <expireTop ref="expire_top_ref" v-if="expire && digitalExpire ||(loginStore.memberInfo?.level?.end_time === '' && loginStore.memberInfo?.digital_human?.end_time === '')" ></expireTop>
        <!-- vip用户头部 -->
        <vipTop  ref="vip_top_ref" v-else></vipTop>
        <!-- 账号信息栏目 -->
        <accountRow ref="account_row_ref"></accountRow>
    </div>
</template>

<script setup>
import { ref, defineExpose, reactive,onActivated,onErrorCaptured } from 'vue';
import vipTop from './component/account/vipTop.vue'
import ordinary from './component/account/ordinaryTop.vue'
import expireTop from './component/account/expireTop.vue'
import accountRow from "./component/account/account_row.vue";
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
let dialogVisible = ref(false);
let ordinary_ref=ref(null)
let vip_top_ref=ref(null)
let account_row_ref=ref(null)
let expire_top_ref=ref(null)
let user=reactive({
    sign:'vip'
})
onErrorCaptured((err, instance, info) => {
  console.error('捕获子组件错误:', err, info)
  // 返回 true 阻止错误继续冒泡，防止父组件崩溃
  return true
})
let rate=ref(window.innerWidth/1920)
let expire=ref(false)
let digitalExpire=ref(false)
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
onActivated(()=>{
    if(loginStore&&loginStore.memberInfo){
        if(loginStore?.memberInfo&&loginStore?.memberInfo?.level&&loginStore?.memberInfo?.level?.end_time){
            expire.value=isExpired(loginStore?.memberInfo?.level?.end_time+' 23:59:59' ||'')
        }
        if(loginStore?.memberInfo&&loginStore?.memberInfo?.digital_human&&loginStore?.memberInfo?.digital_human?.end_time){
            digitalExpire.value=isExpired(loginStore?.memberInfo?.digital_human?.end_time+' 23:59:59' ||'')
        }
    }
})
defineExpose({
    dialogVisible,
});
</script>

<style lang="scss">
.account_information{
    padding: 8px;
    padding-bottom: 16px;
    box-sizing: border-box;

}
</style>
