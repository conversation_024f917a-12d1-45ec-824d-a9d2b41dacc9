<template>
    <el-dialog
    v-model="dialogVisible"
    title="提示"
    width="400"
    class="login_out_dialog"
  >
    <span>确定退出当前配音帮手账号吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref,defineExpose ,watch} from 'vue'
import { useloginStore } from '@/stores/login'
import {userExit} from '@/api/account.js'
let  loginStore = useloginStore()     
let dialogVisible = ref(false)
import { useRouter } from 'vue-router'
import { ElMessage } from "element-plus";
let rate=ref(window.innerWidth/1920)
const router = useRouter()
let cancel=()=>{
    dialogVisible.value=false
}
let submit=async()=>{
    let data=await userExit({userId:loginStore.userId})
    // if(data.code!=0){
    //     ElMessage({ message:data.msg, type: 'error' });
    //     return
    // }
    // 调用 store 的登出方法
    loginStore.loginOut()
    
    // 直接清除 localStorage 中的用户信息，确保完全登出
    localStorage.removeItem('user')

    
    // 关闭对话框
    dialogVisible.value=false
    // router.push('/H4Home')
    // 强制刷新页面，使界面状态更新
    // setTimeout(() => {
    //     window.location.reload()
    //     // router.push('/H4Home')
    // }, 100)
}
defineExpose({
    dialogVisible
})
watch(dialogVisible, (newVal, oldVal) => {
    if(newVal){
        setTimeout(()=>{
            const el = document.querySelector('.login_out_dialog');
            console.log(el,'退出');
            
            if (el && el.parentElement) {
                el.parentElement.style.transform = 'scale(1) translateZ(0px)';
            }
        },200)
        
    }
});
</script>
<style lang="scss" >
.login_out_dialog{
    .el-dialog__body{
        padding: 20px 0;
    }
    .el-button--primary{
        background-color: var(--main-page-color);
        border: none;
    }
    .el-dialog__headerbtn{
        &:hover{
            .el-dialog__close{
                color: var(--main-page-color);
            }
        }
    }
}


</style>