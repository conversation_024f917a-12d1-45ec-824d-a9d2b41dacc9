<template>
    <div class="membership">
        <div class="headbar-container height-64">
            <Headbar />
        </div>

        <memberTop ref="member_top_ref" @chooseNav="choose_nav"></memberTop>
        <membershipProgram ref="membership_program_ref"  v-if="currentNav=='plan'"></membershipProgram>
        <membershipBusiness ref="membership_business_ref"  v-else-if="currentNav=='business'"></membershipBusiness>
        <membershipDigital ref="membership_digital_ref"  v-else-if="currentNav=='digital'"></membershipDigital>
        <membershipFuelPack ref="membership_fuel_pack_ref" v-else></membershipFuelPack>
       
    </div>
      
</template>
<script setup>
 import {ref, reactive,onMounted,nextTick,onDeactivated,onActivated,provide  } from 'vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import memberTop from './component/account/member_top.vue'
import membershipBusiness from './component/account/membership_business.vue'
import membershipDigital from './component/account/membership_digital.vue'
import membershipFuelPack from './component/account/membership_fuel_pack.vue'
import membershipProgram from './component/account/membership_program.vue'
import {planList,productList,digitalPlanList} from '@/api/account.js'
import { queryVoiceWithPackage} from '@/api/soundStore.js'
import { useRoute } from 'vue-router';
import { useloginStore } from '@/stores/login'
import { useSoundStore } from '@/stores/modules/soundStore.js' 
import axios from 'axios';
let loginStore = useloginStore()
let route = useRoute();
let soundStore = useSoundStore()
let member_top_ref=ref(null)
let membership_fuel_pack_ref=ref(null)
let membership_program_ref=ref(null)
let membership_business_ref=ref(null)
let membership_digital_ref=ref(null)
let currentNav=ref('plan')
let list_request=ref({})
 provide('currentNav',currentNav)
let params=reactive({
    plan:{
        cycle:1
    },
    package:{
        cycle:'加油包'
    }
})
let receive=reactive({
    plan:['Free','VIP','SVIP'],

})
let choose_nav=(data)=>{
    console.log(data,params,'choose_nav');
    
    currentNav.value=data.top_nav_current
    Object.keys(params).map((item)=>{
        if(currentNav.value==item){
            params[item].cycle=data.params
        }
    })
    getData()
}
let getData=()=>{
    if (list_request.value?.cancelToken) {
        list_request.value.cancelToken.cancel(' ');
    }
    // 创建新的 CancelToken 源并存储在 list_request 中
    list_request.value.cancelToken = axios.CancelToken.source();
    nextTick(async()=>{

 
    if(currentNav.value=='plan'){
        membership_program_ref.value.loading=true
        let data=await planList({...params.plan,userId:loginStore.userId, cancelToken: list_request.value.cancelToken.token})
        data.map((item)=>{
            receive.plan.map((item1,index1)=>{
                if(item.planName.startsWith(item1)){
                    membership_program_ref.value.member[index1]=Object.assign(membership_program_ref.value.member[index1],item)
                    membership_program_ref.value.member[index1].list=item.resourceJsonCN
                    
                    membership_program_ref.value.cycle=params.plan.cycle
                }
            })
        })
        membership_program_ref.value.loading=false
    }else if(currentNav.value=='business'){
        membership_business_ref.value.loading=true
        let data=await queryVoiceWithPackage({voiceType:"SFT",inUse:"5", cancelToken: list_request.value.cancelToken.token,userId:loginStore.userId})
        membership_business_ref.value.list=setPackage(data)
        membership_business_ref.value.loading=false
    }else if(currentNav.value=='digital'){
        membership_digital_ref.value.loading=true
        let data=await digitalPlanList({userId:loginStore.userId||'', cancelToken: list_request.value.cancelToken.token})
        membership_digital_ref.value.member=data.data
        membership_digital_ref.value.loading=false
    }else{
        membership_fuel_pack_ref.value.loading=true
        let data=await productList({categoryName:params.package.cycle, cancelToken: list_request.value.cancelToken.token,level:loginStore?.memberInfo?.level?.level||0})
       
        membership_fuel_pack_ref.value.member=data
        membership_fuel_pack_ref.value.loading=false
        membership_fuel_pack_ref.value.current_nav=params.package.cycle
        
  
    }
}) 
}
let setPackage=(data)=>{
    let list_pack_data=[]
        list_pack_data=data.reduce((acc, item) => {
            let packageInfo= item.packageInfo
            const type = packageInfo.type; 
    
            
            let group = acc.find(g => g.platformNickname === type);
            if (!group) {
                group = {...packageInfo,packageType:item.packageType, platformNickname: type, data: [],  package: true, };
                acc.push(group);
            }
            let item_data= { ...item }
            delete item_data.packageInfo; 
            group.data.push(item_data);
            group.area=item.recommendTags+item.sceneCategory
            let keys=['gender','ageGroup','sceneCategory','recommendTags','membershipGrade']
            keys.map((item1)=>{
                if(item[item1]){
                    group[item1]=item[item1]
                }else{
                    group[item1]=''
                }
                
            })
            console.log(acc,group,type,999);
            return acc;
        }, []);
        list_pack_data.map(group => {
            // 判断 group.data 中所有项目的 isBuy 是否都是 1
            const allBought = group.data.every(item => item.isBuy === 1);
            group.is_buy = allBought ? 1 : 0;
            group.data.sort((a, b) => {
                //添加的 price 排序逻辑
                if (a.price !== b.price) {
                    return b.price - a.price; // price 倒序
                }
                // 最后按 recommendDegree 排序，值小的在前
                return a.recommendDegree - b.recommendDegree;
            })
        });
        console.log(list_pack_data,'list_pack_data');
        
        // 找到“进阶版套餐包”组
        const advanceIndex = list_pack_data.findIndex(item => item.type === '进阶版套餐包');
        if (advanceIndex !== -1) {
        // 收集所有非“进阶版套餐包”的数据，扁平化合并成一个数组
        const otherData = list_pack_data
            .filter(item => item.type !== '进阶版套餐包')
            .flatMap(item => item.data);

        // 深拷贝“进阶版套餐包”的数据和其他数据，合并后赋值
        list_pack_data[advanceIndex].data = [
            ...JSON.parse(JSON.stringify(list_pack_data[advanceIndex].data)),
            ...JSON.parse(JSON.stringify(otherData))
        ];
        }
        let order = ['基础套餐包', '进阶版套餐包'];
        list_pack_data.sort((a, b) => order.indexOf(a.type) - order.indexOf(b.type));
        soundStore.setAllPackage(list_pack_data)
        return list_pack_data
}
onMounted(()=>{
    
    getData()
})
onDeactivated (() => {
    member_top_ref.value.top_nav_current='plan'
    member_top_ref.value.sub_nav_current='month'
})
</script>
<style lang="scss" scoped>
.membership{
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-bottom: 0;
    overflow-y: scroll;
    overflow-x: clip; /* 使用clip代替auto，防止下拉显示空白区域 */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    min-height: 100vh; /* 确保至少填满视口高度 */
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    .headbar-container{
        z-index: 10;
        // min-height: var(--gl-headbar-height);
        //background-color: var(--gl-headbar-background-color);
        background-color: #ffffff;
        box-shadow: var(--el-box-shadow-light);
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        // height: var(--gl-headbar-height);
       
    }
}
</style>