<template>
	<div class="recharge-welfare-view">
		<div class="welfare-list">
			<div class="welfare-item">
				<div class="item-description">
					<span class="desc-text">累计消费满38元，赠送SVIP任选1款3天使用权</span>
				</div>
				<div class="item-actions">
					<button class="recharge-btn">去充值</button>
					<span class="item-value">价值9元</span>
				</div>
			</div>
			<div class="welfare-item">
				<div class="item-description">
					<span class="desc-text">累计消费满58元，赠送SVIP任选3款3天使用权</span>
				</div>
				<div class="item-actions">
					<button class="recharge-btn">去充值</button>
					<span class="item-value">价值27元</span>
				</div>
			</div>
			<div class="welfare-item">
				<div class="item-description">
					<span class="desc-text">累计消费满98元，赠送VIP7天+SVIP任选4款7天使用权</span>
				</div>
				<div class="item-actions">
					<button class="recharge-btn">去充值</button>
					<span class="item-value">价值95元</span>
				</div>
			</div>
			<div class="welfare-item">
				<div class="item-description">
					<span class="desc-text">累计消费满198元，赠送VIP15天+SVIP任选3款15天使用权</span>
				</div>
				<div class="item-actions">
					<button class="recharge-btn">去充值</button>
					<span class="item-value">价值159元</span>
				</div>
			</div>
			<div class="welfare-item">
				<div class="item-description">
					<span class="desc-text">累计消费满398元，赠送VIP30天+SVIP任选5款30天使用权</span>
				</div>
				<div class="item-actions">
					<button class="recharge-btn">去充值</button>
					<span class="item-value">价值288元</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';

// 假设累积消费数据
const totalConsumption = ref(0.00);

// 累充福利列表数据
const welfareItems = ref([
    {
        id: 1,
        description: '累计消费满38元，赠送SVIP任选1款3天使用权',
        value: 9,
        threshold: 38
    },
    {
        id: 2,
        description: '累计消费满58元，赠送SVIP任选3款3天使用权',
        value: 27,
        threshold: 58
    },
    {
        id: 3,
        description: '累计消费满98元，赠送VIP7天+SVIP任选4款7天使用权',
        value: 95,
        threshold: 98
    },
    {
        id: 4,
        description: '累计消费满198元，赠送VIP15天+SVIP任选3款15天使用权',
        value: 159,
        threshold: 198
    },
    {
        id: 5,
        description: '累计消费满398元，赠送VIP30天+SVIP任选5款30天使用权',
        value: 288,
        threshold: 398
    },
]);

// 处理去充值按钮点击事件
const goToRecharge = (item) => {
    console.log('去充值', item);
    // 这里可以添加跳转到充值页面的逻辑
};

// 暴露给父组件的属性和方法
defineExpose({
    totalConsumption
});
</script>

<style lang="scss" scoped>
.recharge-welfare-view {
	padding-left: 0;
	padding-right: 24px;

	.welfare-list {
		display: flex;
		flex-direction: column;
		gap: 12px;

		.welfare-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #f5f6fa;
			border-radius: 8px;
			padding: 15px 20px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

			.item-description {
				flex: 1;
				font-size: 15px;
				color: #333333;
				font-family: HarmonyOS_Sans_SC;

				.desc-text {
					line-height: 1.5;
				}
			}

			.item-actions {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;

				.item-value {
					font-size: 13px;
					color: #999999;
					font-family: HarmonyOS_Sans_SC;
					white-space: nowrap;
				}

				.recharge-btn {
					background: linear-gradient(90deg, #ff9a44, #ffb75e);
					color: #ffffff;
					border: none;
					border-radius: 20px;
					padding: 8px 18px;
					font-size: 14px;
					cursor: pointer;
					transition: all 0.3s ease;
					box-shadow: 0 4px 8px rgba(255, 154, 68, 0.2);
					font-family: HarmonyOS_Sans_SC_Medium;

					&:hover {
						box-shadow: 0 6px 12px rgba(255, 154, 68, 0.3);
						transform: translateY(-2px);
					}

					&:active {
						transform: translateY(0);
						box-shadow: 0 2px 4px rgba(255, 154, 68, 0.1);
					}
				}
			}
		}
	}
}
// 暗色主题适配
:global(.dark) {
	.recharge-welfare-view {
		.welfare-list {
			.welfare-item {
				background: #2d3748;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

				.item-description {
					color: #e2e8f0;
				}

				.item-value {
					color: #a0aec0;
				}

				.recharge-btn {
					background: linear-gradient(90deg, #d35400, #e67e22);
					box-shadow: 0 4px 8px rgba(211, 84, 0, 0.2);

					&:hover {
						box-shadow: 0 6px 12px rgba(211, 84, 0, 0.3);
					}

					&:active {
						transform: translateY(0);
						box-shadow: 0 2px 4px rgba(211, 84, 0, 0.1);
					}
				}
			}
		}
	}
}

</style> 