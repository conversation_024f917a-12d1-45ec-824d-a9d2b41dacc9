<template>
	<div class="record-view">
		<!-- 按周循环展示兑换记录 -->
		<div class="record-section" v-for="weekData in recordList" :key="weekData?.weekStart || Math.random()" >
			<div class="section-header">
				<img :src="calendarIcon" alt="Calendar Icon" class="calendar-icon">
				{{ formatWeekDate(weekData?.weekStart) }}
			</div>
			<!-- 每周内的记录循环 -->
			<div class="record-item" v-for="(dayRecord, index) in (weekData?.days || [])" :key="index">
				<div class="record-description">{{ getRecordDescription(dayRecord?.type) }}</div>
				<div class="record-reward">
					<span class="reward-value">{{ dayRecord?.type === 1 ? '+' : '-' }}{{ dayRecord?.points || 0 }}</span>
					<img :src="jinbiIcon" alt="金币" class="reward-icon">
				</div>
			</div>
		</div>
		
		<!-- 暂无数据状态 -->
		<div class="record-section" v-if="recordList.length === 0 && !loading">
			<div class="section-header">
				<img :src="calendarIcon" alt="Calendar Icon" class="calendar-icon">
				暂无兑换记录
			</div>
		</div>
		
		<!-- 加载状态 -->
		<div class="record-section" v-if="loading">
			<div class="section-header">
				<img :src="calendarIcon" alt="Calendar Icon" class="calendar-icon">
				加载中...
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue' // 导入Vue响应式API
import { getClaimedWelfareList } from '@/api/dailyWelfare' // 导入兑换记录接口
import { useloginStore } from '@/stores/login' // 导入用户状态管理
import calendarIcon from '@/assets/img/Calendar5.png'; // 导入日历图标
import jinbiIcon from '@/assets/img/jinbi.png'; // 导入金币图标

// 获取用户状态管理
const loginStore = useloginStore()

// 响应式数据定义
const recordList = ref([]) // 兑换记录列表（按周分组）
const loading = ref(false) // 加载状态

// 获取用户ID的辅助函数
const getUserId = () => {
	return loginStore.userId || '11'
}

// 格式化周开始日期显示
const formatWeekDate = (weekStart) => {
	if (!weekStart) return '2025年06月12日' // 默认日期
	// 处理 "2025-06-16" 格式
	const date = new Date(weekStart)
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	return `${year}年${month}月${day}日`
}

// 根据类型获取记录描述
const getRecordDescription = (type) => {
	switch (type) {
		case 1:
			return '每日福利-签到'
		case 2:
			return '每日福利-兑换'
		default:
			return '每日福利-其他'
	}
}

// 获取兑换记录列表
const fetchRecordList = async () => {
	try {
		loading.value = true
		
		const params = {
			userId: getUserId(),
		}
		const response = await getClaimedWelfareList(params)
		console.log('API完整响应:', response)
		
		// 适配新的数据结构：response.data 是周数据数组
		let weeklyData = []
		
		// 尝试多种数据结构获取方式
		if (response && response.data && Array.isArray(response.data)) {
			weeklyData = response.data
			console.log('使用 response.data:', weeklyData)
		} else if (Array.isArray(response)) {
			weeklyData = response
			console.log('使用 response:', weeklyData)
		} else {
			console.log('未识别的数据结构:', response)
			weeklyData = []
		}
		
		console.log('处理后的周数据:', weeklyData)
		
		// 过滤掉无效数据，确保每个周数据都有days数组
		const filteredData = weeklyData.filter(week => {
			const isValid = week && week.days && Array.isArray(week.days) && week.days.length > 0
			console.log('周数据验证:', week, '是否有效:', isValid)
			return isValid
		})
		
		console.log('过滤后的数据:', filteredData)
		recordList.value = filteredData
		
		// 如果没有有效数据，显示提示信息
		if (filteredData.length === 0) {
			console.log('没有有效数据，原始数据结构:', weeklyData)
		}
		
	} catch (error) {
		console.error('获取兑换记录失败:', error)
		// 如果接口失败，使用默认数据进行测试
		recordList.value = [{
			weekStart: '2025-06-12',
			days: [{
				points: 10,
				type: 1
			}]
		}]
		console.log('使用默认测试数据')
	} finally {
		loading.value = false
	}
}

// 组件挂载时获取数据
onMounted(() => {
	fetchRecordList()
})
</script>

<style lang="scss" scoped>
.record-view {
	padding-right: 24px;

	.record-section {
		margin-top: 15px;
		margin-bottom: 20px;

		.section-header {
			display: flex;
			align-items: center;
			gap: 8px;
			margin-bottom: 12px;
			color: #00000073;
			font-size: 14px;
			font-weight: 500;
			margin-left: 14px;

			.calendar-icon { /* 为图片添加样式 */
				width: 16px;
				height: 16px;
			}
		}

		.record-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 47px;
			background-color: #F3F5F5;
			border-radius: 8px;
			margin-top: 10px;

			.record-description {
				font-size: 16px;
				color: #181B1A;
				font-weight: 500;
				margin-left: 14px;
			}

			.record-reward {
				font-size: 16px;
				color: #00000073;
				font-weight: 600;
				display: flex;
				align-items: center;
				.reward-value { /* 设置文字和表情符号之间的间距 */
					margin-right: 7px;
					color: #FF4311; /* 将文字颜色改为#FF4311 */
					font-size: 20px; /* 将文字大小改为20px */
					font-weight: 700; /* 将字体粗细改为700 */
					font-family: '.ThonburiUI'; /* 设置字体为.ThonburiUI */
				}

				.reward-icon { /* 为图片添加样式 */
					width: 35px; /* 设置宽度为35px */
					height: 35px; /* 设置高度为35px */
					margin-top: 5px;
					// vertical-align: middle; /* 垂直居中对齐 */
				}
			}
		}
	}
}

// 暗色主题适配
:global(.dark) {
	.record-view {
		.record-section {
			.record-item {
				background: transparent;
				border-color: transparent;
				.record-description {
					color: #e2e8f0;
				}
			}
		}
	}
}
</style> 