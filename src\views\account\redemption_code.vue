<template>
<el-dialog v-model="dialogVisible" class="redemption_code_dialog" width="464px" append-to-body :show-close="false">
  <template #header>
    <div class="redemption_code_dialog_title">
        CDK兑换码
    </div>
    
    <img src="@/assets/images/account/redemption_code_close.svg" class="redemption_code_dialog_close" alt="" @click="close">
    </template>
    <template  #default>
      <div class="redemption_code_contaier">
         <el-form :inline="true" :model="redemption_code_form" class="redemption_code_form" ref="ruleFormRef" :rules="rules" @click="formItemClick">
            <el-form-item   prop="code">
                <el-input v-model="redemption_code_form.code" placeholder="请输入CDK优惠码"   @focus="codeFocus" >
                    <template #append v-if="showAppend">
                        <div class="show_appende" >
                            <span @click="copyable" class="copyable" v-if="redemption_code_form.code==''">粘贴</span>
                            <span @click="clearable" class="clearable" v-else >清空</span>
                        </div>
                        
                    </template>
                </el-input>
            </el-form-item>
        </el-form>
        <button class="redemption_code_submit" @click="redemption_code_submit">激活领取</button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref,defineExpose ,watch, reactive,inject} from 'vue'
import { useloginStore } from '@/stores/login'
import {userExit} from '@/api/account.js'
import { useRouter } from 'vue-router'
import { ElMessage } from "element-plus";
import {bindCommit} from '@/api/account.js'
let redemption_success = inject('redemption_success'); 
let  loginStore = useloginStore()     
let dialogVisible = ref(false)
let ruleFormRef = ref(null)
let rate=ref(window.innerWidth/1920)
let router = useRouter()
let showAppend=ref(false)
let close=()=>{
    dialogVisible.value=false
}
let redemption_code_form = reactive({
  code: '',
})
let rules=reactive({
     code: [
        { required: true, message: '请输入CDK优惠码', trigger: 'blur' },
        // { min: 6, max: 6, message: '请输入至少六位的优惠码', trigger: 'blur' },
    ],
})
let codeFocus=()=>{
    showAppend.value = true
}
let formItemClick =()=>{
     // 判断点击目标是否是输入框或其子元素
  const inputEl = event.currentTarget.querySelector('input');
  if (inputEl && (inputEl === event.target || inputEl.contains(event.target))) {
    // 点击的是输入框，不隐藏
    return;
  }
  // 点击的是其他区域，隐藏附加内容
  showAppend.value = false;
}
let copyable=()=>{
    // 粘贴功能
    navigator.clipboard.readText().then((text) => {
        redemption_code_form.code = text; // 将剪贴板内容赋值给输入框
    }).catch(err => {
        console.error('Failed to read clipboard contents: ', err);
    });
}
let clearable=()=>{
    // 清空功能
    redemption_code_form.code = ''; // 清空输入框
}
let initClear=()=>{
  ruleFormRef.value&&ruleFormRef.value.resetFields()
  ruleFormRef.value&&ruleFormRef.value.clearValidate()
}
let redemption_code_submit=async()=>{
    if (!ruleFormRef.value) return
    await ruleFormRef.value.validate(async(valid, fields) => {
        if (valid) {
            console.log(redemption_code_form);


             try {
                let data = await bindCommit({ userId:loginStore.userId, cdkCode:redemption_code_form.code})
                console.log(data,'返回信息');
                
                if(data.code==0){
                    ElMessage.success('兑换成功');
                    setTimeout(()=>{
                        close()
                        redemption_success(data.data)
                    },500)
                   
                }else{
                    ElMessage.error(data.msg);
                }
                
            } catch (error) { 
                console.log(error,'返回错误');
                ElMessage.error('兑换失败');
            }
           
        }
    })
}
defineExpose({
    dialogVisible
})
watch(dialogVisible, (newVal, oldVal) => {
    if(!newVal){
        initClear()
    }
});
</script>
<style lang="scss" >
.redemption_code_dialog{
    padding: 0;
    .el-dialog__header{
        height: 54px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        padding: 0 22px;
        margin-bottom: 32px;
        width: 100%;
        box-sizing: border-box;
        .redemption_code_dialog_title{
            font-size: 18px;
            color: #1D2129;
        }
        .redemption_code_dialog_close{
            position: absolute;
            right: 16px;
            top: 17px;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
    }
    .el-dialog__body{
        padding: 0 16px;
        padding-bottom: 40px;
        width: 100%;
        box-sizing: border-box;
        .redemption_code_contaier{
            .redemption_code_form{
                  margin-bottom: 32px;
                    .el-form-item{
                         width:100%;
                         box-sizing: border-box;
                         margin: 0;
                       
                        .el-input{
                            width:100%;
                            height: 42px;
                            border: 1px solid #DCDFE6;
                            border-radius: 4px;
                            box-sizing: border-box;
                            box-shadow: none;
                            .el-input__wrapper{
                                border: none;
                                box-shadow: none;
                            }
                            .el-input-group__append{
                                background-color: transparent;
                                border: none;
                                box-shadow: none;
                                padding: 0 12px;
                                .show_appende{
                                    span{
                                        font-size: 14px;
                                        line-height: 22px;
                                        cursor: pointer;
                                        &.copyable{
                                            color: #1890FF;
                                        }
                                        &.clearable{
                                            color: #FF3B30;
                                        }
                                    }
                                }
                            }
                           
                       }
                       &:last-child{
                        margin-bottom: 0;
                       }
                }
            }
            .redemption_code_submit{
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 42px;
                background: #0AAF60;
                border-radius: 3px;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                border: none;
                cursor: pointer;
            }
        }
        
    }
   
}


</style>