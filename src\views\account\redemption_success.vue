<template>
<el-dialog v-model="dialogVisible" class="redemption_success_dialog" width="464px" append-to-body :show-close="false">
  <template #header>
    <img src="@/assets/images/account/redemption_success.png" class="redemption_success_img" alt="" >
    <img src="@/assets/images/account/redemption_success_close.png" class="redemption_success_close" alt="" @click="redemption_success_close">
    </template>
    <template  #default>
      <div class="redemption_success_contaier">
        <img src="@/assets/images/account/redemption_success_text.png" class="redemption_success_text" alt="">
        <div class="redemption_success_info">
            <span class="thali">套餐信息：SVIP会员{{info.cycle}} <template v-if="info.type==1">天</template><template v-else>个月</template></span>
            <span>交易账号：{{info.mobile}}</span>
            <span>交易日期：{{info.bindTime}}</span>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref,defineExpose ,watch, reactive} from 'vue'
import { useloginStore } from '@/stores/login'
import {userExit} from '@/api/account.js'
import { useRouter } from 'vue-router'
import { ElMessage } from "element-plus";
let  loginStore = useloginStore()     
let dialogVisible = ref(false)
let ruleFormRef = ref(null)
let rate=ref(window.innerWidth/1920)
let router = useRouter()
let showAppend=ref(false)
let close=()=>{
    dialogVisible.value=false
}
let redemption_code_form = reactive({
  code: '',
})
let rules=reactive({
     code: [
        { required: true, message: '请输入CDK优惠码', trigger: 'blur' },
        { min: 3, max: 5, message: '请输入至少六位的优惠码', trigger: 'blur' },
    ],
})
let codeFocus=()=>{
    showAppend.value = true
}
let codeBlur=()=>{
    showAppend.value = false
}
let redemption_success_close=()=>{
    dialogVisible.value=false
}


let initClear=()=>{
  ruleFormRef.value&&ruleFormRef.value.resetFields()
  ruleFormRef.value&&ruleFormRef.value.clearValidate()
}
let info=ref({
    
})
defineExpose({
    dialogVisible,
    info
})
watch(dialogVisible, (newVal, oldVal) => {
    if(!newVal){
        initClear()
    }
});
</script>
<style lang="scss" >
.redemption_success_dialog{
    padding: 0;
    background-color: transparent;
    position: relative;
    box-shadow: none;
    padding-top: 85px;
    height: 320px;
    .el-dialog__header{
        position: absolute;
        top: 0px;
        height: 148px;
        z-index: 2;
        width: 100%;

        .redemption_success_img{
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 209px;
            height: 148px;
            cursor: pointer;
        }
        .redemption_success_close{
            position: absolute;
            right: 0;
            top: 39px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            z-index: 2;
        }
        }
            .el-dialog__body{
        width: 100%;
        height: 235px;
        box-sizing: border-box;
        background: #DCF5FC;
        border-radius: 8px;
        background-image:  url('@/assets/images/account/redemption_success_bg.png');
        background-position: 0 0;
        background-size: cover;
        background-repeat: no-repeat;
        padding: 50px 36px 24px 37px;
        width: 100%;
        box-sizing: border-box;
        position: absolute;
        overflow: hidden;
        .redemption_success_contaier{
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .redemption_success_text{
                width: 205px;
                height: 38px;
                margin-bottom: 23px;
            }
            .redemption_success_info{
                width:100%;
                padding: 13px 22px;
                background: linear-gradient(89.95deg, rgba(255, 255, 255, 0.221) -42.94%, rgba(255, 255, 255, 0.559) 122.33%);
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                span{
                    margin-bottom: 10px;
                    font-size: 11px;
                    line-height: 18px;
                    color: #754E51;
                    &.thali{
                        font-weight: 600;
                        font-size: 15px;
                        line-height: 20px;
                        letter-spacing: 0.05em;
                        color: #263983;
                    }
                    &:last-child{
                        margin-bottom: 0;
                    }
                }
            }
        }
        

   
}
    }



</style>