<template>
<el-dialog v-model="dialogVisible" class="remove_account_dialog" width="464px" append-to-body :show-close="false">
    <template #header>
    <img src="@/assets/images/account/redemption_code_close.svg" class="redemption_code_dialog_close" alt="" @click="close">
    </template>
    <template  #default>
        <template v-if="show_status=='remove'">
            <div class="remove_account_contaier">
                <div class="remove_account_contaier_title">
                <img src="@/assets/images/account/redemption_code_warn.svg" alt=""> <span>注销账号将删除账号下的一切数据，是否继续？</span>
                </div>
                <div class="remove_account_contaier_btns">
                <el-button class="remove_account_contaier_btns_cancel" @click="close">取消</el-button>
                <el-button class="remove_account_contaier_btns_confirm" @click="removeAccount">继续</el-button>
                </div>
            </div>
        </template>
        <template v-else>
             <div class="remove_success_contaier">
                <img src="@/assets/images/account/remove_account_success.svg" alt="">
                <span>注销成功</span>
            </div>
        </template>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref,defineExpose ,watch} from 'vue'
import { useloginStore } from '@/stores/login'
import { ElMessage } from "element-plus";
import {cancelByUserId} from '@/api/account.js'
let  loginStore = useloginStore()     
let dialogVisible = ref(false)
let close=()=>{
    dialogVisible.value=false
}
let removeAccount=async()=>{
      try {
        let data = await cancelByUserId({ userId:loginStore.userId})
        console.log(data,'返回信息');
        
        if(data.code==0){
            ElMessage.success('注销成功');
            show_status.value='success'
            close()
            setTimeout(()=>{
                login_out() 
            },2000)
        }else{
            ElMessage.error(data.msg);
        }
        
    } catch (error) { 
        console.log(error,'返回错误');
        ElMessage.error('注销失败');
    }
}
let login_out=()=>{

  if (localStorage.getItem('user')) {
      try {
        const userObj = JSON.parse(localStorage.getItem('user'));
        localStorage.removeItem('user')
      } catch (e) {
          console.error('解析user失败', e);
      }
  }
    setTimeout(() => {
            window.location.reload();
           
        }, 200);
}
let show_status=ref('remove')
let initClear=()=>{
    show_status.value='remove'
}
watch(dialogVisible, (newVal, oldVal) => {
    if(newVal){
        initClear()
    }
});
defineExpose({
    dialogVisible
})
</script>
<style lang="scss" >
.remove_account_dialog{
    padding: 0;
    .el-dialog__header{
        padding: 0;
        position: relative;
        .redemption_code_dialog_close{
            width: 24px;
            height: 24px;
            position: absolute;
            top: 6px;
            right: 12px;
            z-index: 1;
            cursor: pointer;
        }
    }
    .el-dialog__body{
        padding: 0;
        width: 100%;
        box-sizing: border-box;
        .remove_account_contaier{
            padding:32px;
            padding-top: 24px;
            display: flex;
            flex-direction: column;
            .remove_account_contaier_title{
                display: flex;
                align-items: center;
                img{
                    width: 20px;
                    height: 20px;
                    margin-right: 8px;
                }
                span{
                    font-size: 16px;
                    line-height: 24px;
                    color: #1D2129;
                }
            }
            .remove_account_contaier_btns{
                align-self: flex-end;
                margin-top: 62px;
                display: flex;
                align-items: center;
                .el-button{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 5px 16px;
                    margin-right: 8px;
                    box-sizing: border-box;
                    border-radius: 2px;
                    font-size: 14px;
                    line-height: 22px;
                    margin: 0;
                    border: none;
                    &.remove_account_contaier_btns_cancel{
                        background: #F2F3F5;
                        color: #4E5969;
                        margin-right: 8px;
                    }
                    &.remove_account_contaier_btns_confirm{
                        background-color: #FF4D4F;
                        color:#fff;
                    }
                }
                
            }
        }
        .remove_success_contaier{
            padding: 23px 0 29px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            img{
                width: 92px;
                height: 92px;
                margin-bottom: 8px;
            }
            span{
                font-size: 16px;
                line-height: 22px;
                color: #0AAF60;
            }
        }
        
    }
   
}


</style>