<template>
    <div class="usage_statistics">
        <div class="headbar-container height-64">
            <Headbar />
        </div>
        <div class="usage_statistics_container">
            <div class="usage_statistics_container_title">
                <h4>会员使用情况</h4>
                <span>查看您的各项额度使用情况</span>
            </div>
            <div class="usage_statistics_container_content" v-if="Object.keys(user_data).length>0 ">
                <div class="usage_statistics_container_content_item">
                    <div class="usage_statistics_container_content_item_title">
                        <div class="usage_statistics_container_content_item_title_imgs">
                            <img src="@/assets/images/account/synthesis_times.svg" width="18px" height="20px" alt="">
                        </div>
                        <span>当月合成字符</span>
                    </div>
                    <div class="usage_statistics_container_content_item_barbox">
                        <div class="usage_statistics_container_content_item_use_text">
                            <span>剩余字符</span>
                            <span class="usage_statistics_container_content_item_use_number">{{ user_data.times.surplus}}字符</span>
                        </div>
                        <el-progress color="#F97316" :percentage="user_data.times.percentage" stroke-width="8"   class="usage_statistics_container_content_item_progress"></el-progress>
                        <div class="usage_statistics_container_content_item_used_text">
                            <span>已用：{{user_data.times.used}}字符</span>
                            <span class="usage_statistics_container_content_item_total_text">总额：{{user_data.times.total}}字符</span>
                        </div>
                    </div>
                </div>
                <div class="usage_statistics_container_content_item">
                    <div class="usage_statistics_container_content_item_title">
                        <div class="usage_statistics_container_content_item_title_imgs">
                            <img src="@/assets/images/account/usage_statistics1.png" width="20px" height="18px" alt="">
                        </div>
                        <span>当日试听额度</span>
                    </div>
                    <div class="usage_statistics_container_content_item_barbox">
                        <div class="usage_statistics_container_content_item_use_text">
                            <span>可用额度</span>
                            <span class="usage_statistics_container_content_item_use_number">{{ user_data.audition.surplus}}字符</span>
                        </div>
                        <el-progress color="#18AD25" :percentage="user_data.audition.percentage" stroke-width="8"   class="usage_statistics_container_content_item_progress"></el-progress>
                        <div class="usage_statistics_container_content_item_used_text">
                            <span>已用：{{user_data.audition.used}} 字符</span>
                            <span class="usage_statistics_container_content_item_total_text">总额：{{user_data.audition.total}} 字符</span>
                        </div>
                    </div>
                </div>
                <!-- <div class="usage_statistics_container_content_item">
                    <div class="usage_statistics_container_content_item_title">
                        <div class="usage_statistics_container_content_item_title_imgs">
                            <img src="@/assets/images/account/usage_statistics2.png" width="23px" height="18px" alt="">
                        </div>
                        <span>会员套餐字符额度</span>
                    </div>
                    <div class="usage_statistics_container_content_item_barbox">
                        <div class="usage_statistics_container_content_item_use_text">
                            <span>可用额度</span>
                            <span class="usage_statistics_container_content_item_use_number">{{ user_data.thali.surplus}} 字符</span>
                        </div>
                        <el-progress color="#4A90E2" :percentage="user_data.thali.percentage" stroke-width="8"   class="usage_statistics_container_content_item_progress"></el-progress>
                        <div class="usage_statistics_container_content_item_used_text">
                            <span>已用：{{user_data.thali.used}}字符</span>
                            <span class="usage_statistics_container_content_item_total_text">总额：{{user_data.thali.total}} 字符</span>
                        </div>
                    </div>
                </div> -->
                <div class="usage_statistics_container_content_item">
                    <div class="usage_statistics_container_content_item_title">
                        <div class="usage_statistics_container_content_item_title_imgs">
                            <img src="@/assets/images/account/usage_statistics3.png" width="21px" height="20px" alt="">
                        </div>
                        <span>至臻字符额度</span>
                    </div>
                    <div class="usage_statistics_container_content_item_barbox">
                        <div class="usage_statistics_container_content_item_use_text">
                            <span>可用额度</span>
                            <span class="usage_statistics_container_content_item_use_number">{{ user_data.perfection.surplus}} 字符</span>
                        </div>
                        <el-progress color="#EAB308" :percentage="user_data.perfection.percentage" stroke-width="8"   class="usage_statistics_container_content_item_progress"></el-progress>
                        <div class="usage_statistics_container_content_item_used_text">
                            <span>已用：{{user_data.perfection.used}}字符</span>
                            <span class="usage_statistics_container_content_item_total_text">总额：{{user_data.perfection.total}}字符</span>
                        </div>
                    </div>
                </div>
                <div class="usage_statistics_container_content_item">
                    <div class="usage_statistics_container_content_item_title">
                        <div class="usage_statistics_container_content_item_title_imgs">
                            <img src="@/assets/images/account/usage_statistics4.png" width="20px" height="20px" alt="">
                        </div>
                        <span>算粒额度</span>
                    </div>
                    <div class="usage_statistics_container_content_item_barbox">
                        <div class="usage_statistics_container_content_item_use_text">
                            <span>可用额度</span>
                            <span class="usage_statistics_container_content_item_use_number">{{ user_data.computing.surplus}}  算粒</span>
                        </div>
                        <el-progress color="#A855F7" :percentage="user_data.computing.percentage" stroke-width="8"   class="usage_statistics_container_content_item_progress"></el-progress>
                        <div class="usage_statistics_container_content_item_used_text">
                            <span>已用：{{user_data.computing.used}} 算粒</span>
                            <span class="usage_statistics_container_content_item_total_text">总额：{{user_data.computing.total}} 算粒</span>
                        </div>
                    </div>
                </div>
                <div class="usage_statistics_container_content_item">
                    <div class="usage_statistics_container_content_item_title">
                        <div class="usage_statistics_container_content_item_title_imgs">
                            <img src="@/assets/images/account/usage_statistics5.png" width="20px" height="18px" alt="">
                        </div>
                        <span>个人空间</span>
                    </div>
                    <div class="usage_statistics_container_content_item_barbox">
                        <div class="usage_statistics_container_content_item_use_text">
                            <span>剩余空间</span>
                            <span class="usage_statistics_container_content_item_use_number">{{ user_data.space.surplus}} GB</span>
                        </div>
                        <el-progress  color="#6366F1" :percentage="user_data.space.percentage" stroke-width="8"   class="usage_statistics_container_content_item_progress"></el-progress>
                        <div class="usage_statistics_container_content_item_used_text">
                            <span>已用：{{user_data.space.used}} GB</span>
                            <span class="usage_statistics_container_content_item_total_text">总额：{{user_data.space.total}} GB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
       
    </div>
      
</template>
<script setup>
import {ref, reactive,defineExpose,onMounted,onActivated,watch } from 'vue'
import {showUserBenefits} from '@/api/account.js'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import { useloginStore } from '@/stores/login'
import {accAdd,accSub,accMul,accDiv} from '@/utils/accuracy'
import { ElMessage } from 'element-plus'
let loginStore = useloginStore()
let user_data=reactive({
    times:{
        surplus:0,
        used:0,
        total:0,
        percentage:0
    },
    audition:{
        surplus:0,
        used:0,
        total:0,
        percentage:0
    },
    // thali:{
    //     surplus:0,
    //     used:0,
    //     total:0,
    //     percentage:0
    // },
    perfection:{
        surplus:0,
        used:0,
        total:0,
        percentage:0,
    },
    computing:{
        surplus:0,
        used:0,
        total:0,
        percentage:0,
    },
    space:{
        surplus:0,
        used:0,
        total:0,
        percentage:0,
    },
})
let sumSurplus=(a=0,b=0)=>{
   
    
    return accSub(a,b)
}
let sumAdd=(a=0,b=0)=>{
   
    return accAdd(a,b)
}
let getPercentage=(used=0,total=0)=>{
    return (accDiv(used,total)* 100).toFixed(2)
}
let getData=async()=>{
  
    
  
   
    try {
        let res=await showUserBenefits({userId:loginStore.userId?loginStore.userId:''})
        console.log(res,'res');
        if(res.code!=0){
           ElMessage.error(res.msg) 
            return 
        }
        let data=res?.data?.content
    if(data){
        user_data.times.total=data.result.total['Premium&Deluxe']||0
        user_data.times.used=data.result.current['Premium&Deluxe']||0
        user_data.times.surplus=sumSurplus(user_data.times.total||0,user_data.times.used||0)
        user_data.times.percentage=getPercentage(user_data.times.used,user_data.times.total)
        // let data=await showUserBenefits({user_id:11})
        user_data.audition.total=data.result.total.free_resource||0
        user_data.audition.used=data.result.current.free_resource||0
        user_data.audition.surplus=sumSurplus(user_data.audition.total||0,user_data.audition.used||0)
        user_data.audition.percentage=getPercentage(user_data.audition.used,user_data.audition.total)
        




        user_data.perfection.total=data.result.total.SFT|| 0
        user_data.perfection.used=data.result.current.SFT|| 0
        user_data.perfection.surplus=sumSurplus(user_data.perfection.total||0,user_data.perfection.used||0)
        user_data.perfection.percentage=getPercentage(user_data.perfection.used,user_data.perfection.total)
    
        user_data.computing.total=data.result.total['One-Click Video']|| 0
        user_data.computing.used=data.result.current['One-Click Video']|| 0
        user_data.computing.surplus=sumSurplus(user_data.computing.total||0,user_data.computing.used||0)
        user_data.computing.percentage=getPercentage(user_data.computing.used,user_data.computing.total)


        user_data.space.total=accDiv((data.result.total['Cloud Editing Space']|| 0),1024)
        user_data.space.used=accDiv((data.result.current['Cloud Editing Space']|| 0),1024)
        user_data.space.surplus=sumSurplus((user_data.space.total||0),(user_data.space.used||0))
        user_data.space.surplus=user_data.space.surplus<0?0:user_data.space.surplus
        user_data.space.percentage=getPercentage(user_data.space.used,user_data.space.total)
    }
} catch (error) {
  console.error('获取用户权益失败:', error);
  resetUserData();
}

}
let resetUserData=()=>{
    user_data.times.surplus=0
    user_data.times.used=0
    user_data.times.total=0
    user_data.times.percentage=0
    user_data.audition.surplus=0
    user_data.audition.used=0
    user_data.audition.total=0
    user_data.audition.percentage=0
    user_data.perfection.surplus=0
    user_data.perfection.used=0
    user_data.perfection.total=0
    user_data.perfection.percentage=0
    user_data.computing.surplus=0
    user_data.computing.used=0
    user_data.computing.total=0
    user_data.computing.percentage=0
    user_data.space.surplus=0
    user_data.space.used=0
    user_data.space.total=0
    user_data.space.percentage=0
}
defineExpose({
    
})
watch(()=>loginStore.userId,(newVal)=>{
    getData()
},{immediate:true,deep:true})

onMounted(()=>{
    getData()
})
</script>
<style lang="scss" scoped>
.usage_statistics{
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    background-color: #f9f9f9;
    .headbar-container{
        z-index: 10;
        width: 100%;
        // min-height: var(--gl-headbar-height);
        //background-color: var(--gl-headbar-background-color);
        background-color: var(--gl-headbar-background-color);
        box-shadow: var(--el-box-shadow-light);
    }
    .usage_statistics_container{
        width: 1376px;
        padding-top:100px;
        box-sizing: border-box;
        .usage_statistics_container_title{
            display: flex;
            flex-direction: column;
            margin-bottom: 32px;
            h4{
                font-size: 30px;
                color: #111827;
                font-weight: bold;
                line-height: 36px;
                margin-bottom: 8px;
                margin-top: 0;
            }
            span{
                font-size: 16px;
                line-height: 24px;
                color: #6B7280;
            }
        }
        .usage_statistics_container_content{
                display: flex;
                width: 100%;
                box-sizing: border-box;
                flex-wrap: wrap;
                .usage_statistics_container_content_item{
                    background-color: #fff;
                    width: 672px;
                    box-sizing: border-box;
                    padding: 24px;
                    border-radius: 12px;
                    margin-right: 32px;
                    margin-bottom: 32px;
                    .usage_statistics_container_content_item_title{
                        display: flex;
                        align-items: center;
                        margin-bottom: 16px;
                        .usage_statistics_container_content_item_title_imgs{
                            width: 32px;
                            height: 32px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 12px;
                        }
                        span{
                            font-size: 20px;
                            color: #111827;
                        }
                    }
                    .usage_statistics_container_content_item_barbox{
                        .usage_statistics_container_content_item_use_text{
                            display: flex;
                            line-height: 24px;
                            font-size: 16px;
                            margin-bottom: 8px;
                            align-items: center;
                            color: #4B5563;
                            .usage_statistics_container_content_item_use_number{
                                margin-left: auto;
                            }
                        }
                        .usage_statistics_container_content_item_progress{
                            margin-bottom: 16px;
                           :deep(.el-progress__text) {
                                display: none; /* 隐藏进度条文本 */
                            }
                        }
                        .usage_statistics_container_content_item_used_text{
                            font-size: 14px;
                            line-height: 20px;
                            color: #6B7280;
                            display: flex;
                            align-items: center;
                            .usage_statistics_container_content_item_total_text{
                                margin-left: auto;
                            }
                        }
                    }
                    &:nth-child(2n){
                        margin-right: 0;
                    }
                    &:first-child{
                        .usage_statistics_container_content_item_barbox{
                            .usage_statistics_container_content_item_use_text{
                                .usage_statistics_container_content_item_use_number{
                                    color: #F97316;
                                }
                            }
                        }  
                        
                    }
                    &:nth-child(2){
                        .usage_statistics_container_content_item_barbox{
                            .usage_statistics_container_content_item_use_text{
                                .usage_statistics_container_content_item_use_number{
                                    color: #18AD25;
                                }
                            }
                        }  
                        
                    }
                    &:nth-child(3){
                        .usage_statistics_container_content_item_barbox{
                            .usage_statistics_container_content_item_use_text{
                                .usage_statistics_container_content_item_use_number{
                                    color: #EAB308;
                                }
                            }
                        }  
                    }
                    &:nth-child(4){
                        .usage_statistics_container_content_item_barbox{
                            .usage_statistics_container_content_item_use_text{
                                .usage_statistics_container_content_item_use_number{
                                    color: #A855F7;
                                }
                            }
                        }  
                    }
                    &:last-child{
                        width: 100%;
                        margin-right: 0;
                        .usage_statistics_container_content_item_barbox{
                            .usage_statistics_container_content_item_use_text{
                                .usage_statistics_container_content_item_use_number{
                                    color: #6366F1;
                                }
                            }
                        } 
                    }
                }
            }
        }
   
}
</style>