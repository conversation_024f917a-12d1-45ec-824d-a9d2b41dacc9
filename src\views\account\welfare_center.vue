<template>
	<div v-if="dialogVisible" class="welfare-center-overlay" @click="closeDialog">
		<div class="welfare-center-dialog" @click.stop>
			<!-- 弹窗头部 -->
			<div class="dialog-header">
				<button class="close-btn" @click="closeDialog">
					<img src="@/assets/img/shanchu.png" alt="关闭" class="close-icon" />
				</button>
			</div>

			<!-- 弹窗内容 -->
			<div class="dialog-content">
				<!-- 左侧导航栏 -->
				<div class="sidebar">
					<div class="nav-item" :class="{ active: activeTab === 'daily' }" @click="activeTab = 'daily'; showRecord = false">
						<img src="@/assets/img/Bank-card-two.png" alt="每日福利" class="nav-icon" />
						每日福利
					</div>

					<div class="nav-item" :class="{ active: activeTab === 'exchange' }" @click="activeTab = 'exchange'; showRecord = false">
						<img src="@/assets/img/Funds.png" alt="积分兑换" class="nav-icon" />
						积分兑换
					</div>

					<div class="points-record" :class="{ active: showRecord }"
						@click="showRecord = true">获得/兑换记录</div>
				</div>

				<!-- 右侧内容区 -->
				<div class="main-content">
					<!-- 顶部统计 - 完全固定不滚动 -->
					<div class="top-stats-fixed">
						<div class="stats-item">
							<span class="label">{{ currentTitle }}</span>
							<span v-if="activeTab !== 'record'" class="badge">
								<span>当前可用 </span><span class="number">{{ totalPoints }}</span><span> 个积分</span><img src="@/assets/img/jinbi.png" alt="积分" class="coin-icon" />
							</span>
						</div>
					</div>

					<!-- 可滚动的内容区域 -->
					<div class="scrollable-content">
						<!-- 记录页面 -->
						<RecordView v-if="showRecord" />
						
						<!-- 签到日历 -->
						<DailyWelfareView v-else-if="activeTab === 'daily'" @update-total-points="handleTotalPointsUpdate" ref="dailyWelfareRef" />

						<!-- 其他标签页内容 -->
						<ExchangeView v-else-if="activeTab === 'exchange'" :total-points="totalPoints" @exchange-success="handleExchangeSuccess" />

						<div v-else class="other-content">
							<div class="placeholder">{{ getTabContent() }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import helpDark from '@/assets/images/account/help_dark.svg'
import ExchangeView from './exchange_center/ExchangeView.vue'
import DailyWelfareView from './daily_welfare/DailyWelfareView.vue'

import RecordView from './record_center/RecordView.vue'

// 控制弹窗显示隐藏
const dialogVisible = ref(false)

// 当前活跃的标签页
const activeTab = ref('daily')

// 控制记录页面显示
const showRecord = ref(false)

// 存储当前可用积分
const totalPoints = ref(0)

// DailyWelfareView 组件的引用
const dailyWelfareRef = ref(null);

// 定义标题映射
const titleMap = {
	daily: '福利列表',
	exchange: '兑换列表',
	record: '获得/兑换记录'
}

// 根据当前活跃标签页计算标题
const currentTitle = computed(() => {
	if (showRecord.value) {
		return '获得/兑换记录'
	}
	return titleMap[activeTab.value] || '福利列表' // 默认值为"福利列表"
})

// 获取标签页内容
const getTabContent = () => {
	const contents = {
		// recharge: '累充福利功能正在开发中...'
	}
	return contents[activeTab.value] || '内容加载中...'
}

// 关闭弹窗
const closeDialog = () => {
	dialogVisible.value = false
}

// 处理积分更新事件
const handleTotalPointsUpdate = (points) => {
	totalPoints.value = points
}

// 独立获取用户积分的方法
const fetchUserPoints = async () => {
	try {
		const { getDailyWelfareList } = await import('@/api/dailyWelfare');
		const { useloginStore } = await import('@/stores/login');
		const loginStore = useloginStore();
		
		if (!loginStore.userId) {
			console.warn('用户未登录，无法获取积分信息');
			return;
		}
		
		// 获取当前日期范围（简化版本，只获取积分）
		const now = new Date();
		const today = now.toISOString().split('T')[0];
		
		const params = {
			userId: loginStore.userId,
			startDate: today,
			endDate: today
		};
		
		const response = await getDailyWelfareList(params);
		
		if (response && response.totalPoints !== undefined) {
			totalPoints.value = response.totalPoints;
			console.log('积分更新成功:', response.totalPoints);
		}
	} catch (error) {
		console.error('获取用户积分失败:', error);
	}
}

// 处理兑换成功事件，刷新积分
const handleExchangeSuccess = async () => {
	// 如果 DailyWelfareView 组件已渲染，直接调用其方法
	if (dailyWelfareRef.value && dailyWelfareRef.value.fetchDailyWelfareData) {
		await dailyWelfareRef.value.fetchDailyWelfareData();
	} else {
		// 如果 DailyWelfareView 组件未渲染，直接调用 API 更新积分
		await fetchUserPoints();
	}
};

// 暴露给父组件的属性和方法
defineExpose({
	dialogVisible
})
</script>

<style lang="scss" scoped>
.welfare-center-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.welfare-center-dialog {
	background: url('@/assets/img/huodong.png') no-repeat center center;
	background-size: 896px 750px;
	border-radius: 20px;
	width: 896px;
	height: 688px;
	position: relative;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
	from {
		opacity: 0;
		transform: scale(0.9) translateY(-20px);
	}

	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.dialog-header {
	position: absolute;
	top: 20px;
	right: 1px;
	z-index: 10;

	.close-btn {
		background: rgba(0, 0, 0, 0.3);
		border: none;
		padding: 10px;
		cursor: pointer;
		color: #ffffff;
		border-radius: 50%;
		transition: all 0.2s;
		width: 36px;
		height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(8px);

		&:hover {
			background: rgba(0, 0, 0, 0.5);
			transform: scale(1.1);
		}
	}
}

.dialog-content {
	display: flex;
	width: 846px;
	height: 539px;
	position: absolute;
	top: 135px;
	left: 32px;
	gap: 24px;
}

.sidebar {
	width: 127px;
	height: 539px;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	border-radius: 8px;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(151deg, #0AAF60, #5CCADE);
		border-radius: 8px;
		padding: 1px;
		mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
		mask-composite: xor;
		-webkit-mask-composite: xor;
		pointer-events: none;
	}

	.nav-item {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 6px;
		width: 127px;
		height: 48px;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 0;
		font-family: 'PingFang SC', sans-serif;
		color: #4a5568;
		cursor: pointer;
		font-size: 14px;
		font-weight: 500;
		transition: all 0.3s ease;
		border: none;

		.nav-icon {
			width: 16px;
			height: 16px;
			color: #6b7280;
			flex-shrink: 0;
			display: flex;
			align-items: center;
		}

		&:hover {
			background: rgba(74, 222, 128, 0.1);
			color: #22c55e;
		}

		&.active {
			background: linear-gradient(to right, #B6FEDB, #CBF6FF);
			color: #001126;

			.nav-icon {
				color: #001126;
			}
		}

		&:first-child {
			border-top-left-radius: 8px;
			border-top-right-radius: 8px;
		}
	}

	.points-record {
		margin-top: auto;
		margin-bottom: 16px;
		text-align: center;
		background: rgba(255, 255, 255, 0.9);
		border-radius: 8px;
		border: none;
		color: #6b7280;
		font-size: 14px;
		cursor: pointer;
		font-family: 'PingFang SC', sans-serif;
		transition: all 0.2s;
		backdrop-filter: blur(10px);

		&:hover {
			color: #4a5568;
			background: rgba(255, 255, 255, 1);
		}
	}
}

.main-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	
}

.top-stats-fixed {
	// padding: 0 0 16px 0;

	.stats-item {
		display: flex;
		justify-content: space-between;
		// align-items: center;

		.label {
			font-size: 14px;
			font-weight: 500;
			font-family: 'PingFang SC', sans-serif;
			color: #000000;
		}

		.badge {
			display: flex;
			align-items: center;
			background: transparent;
			color: #666666;
			padding: 0;
			border-radius: 0;
			font-size: 14px;
			font-weight: 500;
			border: none;

			.number {
				color: #FF4311;
				font-weight: 700;
			}

			.coin-icon {
				width: 23px;
				height: 23px;
				margin-left: 1px;
			}
		}
	}
}

.scrollable-content {
	flex: 1;
	overflow-y: auto;
	padding: 11px 0 0 0;
}

.other-content {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 300px;

	.placeholder {
		color: #9ca3af;
		font-size: 16px;
		font-family: 'PingFang SC', sans-serif;
	}
}

// 隐藏滚动条但保持滚动功能
.scrollable-content {
	/* 隐藏滚动条 */
	&::-webkit-scrollbar {
		width: 0;
		background: transparent;
	}

	/* 对于Firefox */
	scrollbar-width: none;
	
	/* 对于IE/Edge */
	-ms-overflow-style: none;
}

:global(.dark) {
	.welfare-center-dialog {
		.dialog-header .header-content .dialog-title {
			color: #f7fafc;
		}

		.sidebar .nav-item {
			background: rgba(45, 55, 72, 0.8);
			color: #e2e8f0;

			&:hover {
				background: rgba(74, 222, 128, 0.2);
				color: #4ade80;
			}

			&.active {
				background: linear-gradient(135deg, #4ade80, #22c55e);
				color: #ffffff;
			}
		}

		.sidebar .points-record {
			background: rgba(45, 55, 72, 0.8);
			color: #a0aec0;
		}

		.main-content {
			background: rgba(45, 55, 72, 0.95);
		}

		.top-stats-fixed .stats-item .label {
			color: #f7fafc;
		}
	}
}
</style>