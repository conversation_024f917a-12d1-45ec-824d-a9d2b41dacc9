<template>
	<el-dialog :title="title" v-model="dialogVisible" :width="width" :show-close="showClose"
		:close-on-click-modal="closeOnClickModal" class="alert-dialog" :class="dialogType">
		<div class="alert-content">
			<!-- 左侧图标区域 -->
			<div class="alert-icon" v-if="showIcon">
				<el-icon v-if="dialogType === 'dialog-warning'">
					<WarningFilled />
				</el-icon>
				<el-icon v-else-if="dialogType === 'dialog-error'">
					<CircleCloseFilled />
				</el-icon>
				<el-icon v-else-if="dialogType === 'dialog-success'">
					<CircleCheckFilled />
				</el-icon>
				<el-icon v-else>
					<InfoFilled />
				</el-icon>
			</div>

			<!-- 右侧文本区域 -->
			<div class="alert-message">
				<div class="alert-title" v-if="subTitle">{{ subTitle }}</div>
				<div class="alert-text" v-html="message"></div>
			</div>
		</div>

		<!-- 底部按钮区域 -->
		<template #footer>
			<div class="dialog-footer" :class="{'center-buttons': !showFeeExplanation}">
				<!-- 添加扣费说明按钮 -->
				<div v-if="showFeeExplanation" class="fee-explanation" @click="handleFeeExplanation">
					<el-icon><InfoFilled /></el-icon>
					<span>扣费说明</span>
				</div>
				
				<div class="dialog-buttons">
					<el-button v-if="showCancelButton" @click="handleCancel"
						:class="{ 'custom-cancel-btn': customCancelClass }">
						{{ cancelButtonText }}
					</el-button>
					<el-button type="primary" @click="handleConfirm" :class="{ 'custom-confirm-btn': customConfirmClass }">
						{{ confirmButtonText }}
					</el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { WarningFilled, CircleCloseFilled, CircleCheckFilled, InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
	// 弹窗是否可见
	visible: {
		type: Boolean,
		default: false
	},
	// 弹窗类型：info, success, warning, error
	type: {
		type: String,
		default: 'info'
	},
	// 弹窗标题
	title: {
		type: String,
		default: '提示'
	},
	// 弹窗子标题
	subTitle: {
		type: String,
		default: ''
	},
	// 弹窗内容
	message: {
		type: String,
		default: ''
	},
	// 确认按钮文本
	confirmButtonText: {
		type: String,
		default: '确定'
	},
	// 取消按钮文本
	cancelButtonText: {
		type: String,
		default: '取消'
	},
	// 是否显示取消按钮
	showCancelButton: {
		type: Boolean,
		default: false
	},
	// 弹窗宽度
	width: {
		type: String,
		default: '420px'
	},
	// 是否显示关闭图标
	showClose: {
		type: Boolean,
		default: false
	},
	// 点击遮罩层是否关闭弹窗
	closeOnClickModal: {
		type: Boolean,
		default: false
	},
	// 自定义确认按钮样式
	customConfirmClass: {
		type: Boolean,
		default: false
	},
	// 自定义取消按钮样式
	customCancelClass: {
		type: Boolean,
		default: false
	},
	// 错误代码，用于特定处理
	errorCode: {
		type: [String, Number],
		default: ''
	},
	// 是否显示扣费说明按钮
	showFeeExplanation: {
		type: Boolean,
		default: true
	},
	// 是否显示图标
	showIcon: {
		type: Boolean,
		default: true
	}
})

const emits = defineEmits(['confirm', 'cancel', 'update:visible', 'feeExplanation'])

// 弹窗可见状态
const dialogVisible = ref(props.visible)

// 监听visible属性变化，更新内部状态
watch(() => props.visible, (val) => {
	dialogVisible.value = val
})

// 监听内部状态变化，通知父组件
watch(dialogVisible, (val) => {
	emits('update:visible', val)
})

// 计算弹窗类型对应的CSS类
const dialogType = computed(() => {
	return `dialog-${props.type}`
})

// 确认按钮处理
const handleConfirm = () => {
	dialogVisible.value = false
	emits('confirm', props.errorCode)
}

// 取消按钮处理
const handleCancel = () => {
	dialogVisible.value = false
	emits('cancel')
}

// 扣费说明点击处理
const handleFeeExplanation = () => {
	emits('feeExplanation')
}

// 导出方法，便于其他地方使用
defineExpose({
	handleConfirm,
	handleCancel,
	handleFeeExplanation
})
</script>

<style lang="scss" scoped>
.alert-dialog {
	:deep(.el-dialog__header) {
		padding: 15px 20px;
		border-bottom: 1px solid #ebeef5;
	}

	:deep(.el-dialog__body) {
		padding: 20px;
	}

	:deep(.el-dialog__footer) {
		padding: 10px 20px 20px;
		text-align: center;
	}

	// 根据类型设置不同的样式
	&.dialog-warning {
		.alert-icon {
			color: #E6A23C;
		}
	}

	&.dialog-error {
		.alert-icon {
			color: #F56C6C;
		}
	}

	&.dialog-success {
		.alert-icon {
			color: #67C23A;
		}
	}

	&.dialog-info {
		.alert-icon {
			color: #909399;
		}
	}

	.alert-content {
		display: flex;
		align-items: flex-start;

		.alert-icon {
			font-size: 24px;
			margin-right: 15px;

			i {
				display: inline-flex;
			}
		}

		.alert-message {
			flex: 1;

			.alert-title {
				font-size: 16px;
				font-weight: bold;
				margin-bottom: 8px;
				color: #303133;
			}

			.alert-text {
				font-size: 14px;
				color: #606266;
				line-height: 1.6;
			}
		}
	}

	.dialog-footer {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		&.center-buttons {
			justify-content: center;
			
			.dialog-buttons {
				margin: 0 auto;
			}
		}
		
		// 添加扣费说明按钮样式
		.fee-explanation {
			display: flex;
			align-items: center;
			cursor: pointer;
			color: #909399;
			font-size: 14px;
			
			&:hover {
				color: #0AAF60;
			}
			
			.el-icon {
				margin-right: 5px;
				font-size: 14px;
			}
		}
		
		.dialog-buttons {
			display: flex;
			gap: 20px;
			
			.el-button {
				min-width: 88px;
				height: 32px;
				padding: 0;
				display: flex;
				align-items: center;
				padding: 0 16px;
				justify-content: center;
			}
		}

		.custom-confirm-btn {
			background-color: #09AF5E;
			border-color: #09AF5E;

			&:hover,
			&:focus {
				background-color: #0bbe6a;
				border-color: #0bbe6a;
			}

			&:active {
				background-color: #08a055;
				border-color: #08a055;
			}
		}

		.custom-cancel-btn {
			color: #606266;
			border-color: #dcdfe6;

			&:hover,
			&:focus {
				color: #09AF5E;
				border-color: #09AF5E;
			}
		}
	}
}
</style>