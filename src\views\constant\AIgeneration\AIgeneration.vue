<script setup>
import {inject, onMounted, onUnmounted, reactive, ref} from 'vue'
//弹窗
const importDialogVisible = ref(false)
const eventBus = inject('eventBusAI');
const open = () => {
  console.log('Opening modal with title:');
  // 打开模态框的逻辑
  importDialogVisible.value = true;
};

const closeLetter = () => {
  console.log('Closing modal');
  // 关闭模态框的逻辑
};
onMounted(() => {
  eventBus.on('openAIModal', open);
  eventBus.on('closeAIModal', closeLetter);
});
onUnmounted(() => {
  eventBus.off('openAIModal', open);
  eventBus.off('closeAIModal', closeLetter);
});
// 输入的短视频链接
const input_link = ref('')



</script>

<template>
  <Teleport to="body">
    <el-dialog v-model="importDialogVisible" width="600" center>
      <div class="dialog_content flex flex_d-column flex_a_i-center">
        <span class="text-align-center height-25 font-size-14">AI文案生成</span>
<!--        <div class="dialog_content_tab height-40 width-440">-->
<!--          <ul class="height-full flex flex_a_i-center flex_j_c-space-around">-->
<!--&lt;!&ndash;            <li class="cursor-pointer width-120 height-32 flex flex_a_i-center flex_j_c-center"&ndash;&gt;-->
<!--&lt;!&ndash;                :class="{is_active: tab_active_num===item.id}" v-for="item in tab_list" :key="item.id" @click="click_tab(item.id)"&ndash;&gt;-->
<!--&lt;!&ndash;            >&ndash;&gt;-->
<!--&lt;!&ndash;              {{ item.name }}</li>&ndash;&gt;-->
<!--          </ul>-->
<!--        </div>-->
        <el-scrollbar class="width-576 margin_b-10 list_tab_ul" height="110">
          <ul class=" width-full flex">
            <li class="width-80 height-30 flex flex_j_c-center flex_a_i-center font-size-12 margin_r-16 margin_b-10 cursor-pointer"
                v-for="item in 20" :style="`background-image:linear-gradient(134deg, rgba(10, 175, 96, 1), rgba(255, 214, 0, 1));color: #0AAF60;`"
            >
              <div class="li_item width-78 height-28 flex flex_j_c-center flex_a_i-center">企业宣传</div>
            </li>
          </ul>
        </el-scrollbar>





        <div class="dialog_content_middle1 width-full height-40 flex flex_a_i-center font-size-12  margin-10-n padding_l-10 " >
<!--          复制短视频链接（基本覆盖所有短视频平台）一键解析，若源文件过大有可能导致提取失败，可用本地视频方式-->
          写一篇字数<span class="highlight-text">【 * 】</span>的，适合<span class="highlight-text">【 美食 】</span>行业的，<span class="highlight-text">【 小红书 类型】</span>文稿。
        </div>
        <div class="dialog_content_middle2 width-576 height-270 margin_b-10 overflow-hidden">
          <el-scrollbar height="270">
<!--            <el-input  v-model="input_link" resize="none" autosize  type="textarea" disabled placeholder="请输入短视频链接" />-->
            <div class="width-full height-270 padding-10"></div>
          </el-scrollbar>
        </div>


        <div class="dialog_content_button flex flex_a_i-center flex_j_c-space-around font-size-14">
          <div class="dialog_content_button_left width-140 height-40 flex flex_a_i-center flex_j_c-center margin_r-20 cursor-pointer">复制使用</div>
          <div class="dialog_content_button_right width-140 height-40 flex flex_a_i-center flex_j_c-center cursor-pointer">立即生成</div>
        </div>
      </div>
    </el-dialog>
  </Teleport>
</template>

<style scoped lang="scss">
.el-upload-dragger .el-upload__text {
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}
.dialog_content{
  //background-color: #006eff;
  span{
    font-weight: 500;
    color: #121212;
  }
  //&_tab{
  //  border-radius: 24px;
  //  background-color: #fff;
  //  ul{
  //    li{
  //      //background: rgba(10,175,96,0.06);
  //      //background-color: #5b5b5b;
  //      border-radius: 20px;
  //    }
  //    .is_active{
  //      background: rgba(10,175,96,0.06);
  //      color: #0AAF60;
  //    }
  //  }
  //}
  &_middle1{
    background-color: #f7f7f9;
    //font-weight: 400;
    //color: #7E838D;
    border-radius: 8px;
    .highlight-text{
      color:#0AAF60;
    }
  }
  &_middle2{
    //background-color: #67c23a;
    border-radius:8px;
    background-color: #f7f7f9;
    ::v-deep(.el-textarea__inner){
      border: none;
      outline: none !important;
      box-shadow: none;
      background-color: rgba(255,255,255,0);
    }
  }

  .list_tab_ul{
    ul{
      flex-wrap: wrap;
      li{
        background: #f7f7f9;
        border-radius: 16px;
        color: #7E838D;
        font-weight: 500;
        .li_item{
          border-radius: 16px;
          background-color: #fff;
        }
      }
    }

  }

  &_button{
    &_left{
      background: #FFFFFF;
      border-radius: 8px;
      border: 1px solid #0AAF60;
      color:#0AAF60;
    }
    &_right{
      background: #0BAF60;
      border-radius: 8px;
      color:#fff;
    }
  }


}
.login_main_content_bottom{
  height:50px;
  line-height: 50px;
  //background-color: #00ffff;
  font-weight: 400;
  font-size: 12px;
  color: #333;
  span{
    color:var(--main-page-color);
    margin: 0 4px;
  }
}
</style>