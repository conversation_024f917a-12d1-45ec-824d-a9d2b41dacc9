<script setup>
import { inject, onMounted, onUnmounted, reactive, ref } from 'vue'
import { crawlByVideoPage, crawlTextByVideoPage,extractTextByWorkTxt, get_signature, upload_callback } from "@/api_my/AlDubb/index.js";
import { ElMessage } from 'element-plus'
import axios from "axios";
import { UploadFilled } from '@element-plus/icons-vue'
const emit = defineEmits(['call-parent']);
//弹窗
const importDialogVisible = ref(false)
// const eventBus = inject('eventBusOpen');
// const open = () => {
//   console.log('Opening modal with title:');
//   // 打开模态框的逻辑
//   importDialogVisible.value = true;
// };
//
// const closeLetter = () => {
//   console.log('Closing modal');
//   // 关闭模态框的逻辑
// };
// onMounted(() => {
//   eventBus.on('openModal11', open);
//   eventBus.on('closeImportLetter11', closeLetter);
// });
// onUnmounted(() => {
//   eventBus.off('openModal11', open);
//   eventBus.off('closeImportLetter11', closeLetter);
// });
// 中间tab按钮

const tab_list = reactive([
	{
		id: 0,
		name: '短视频链接',
		is_active: true
	},
	{
		id: 1,
		name: '本地音视频',
		is_active: false
	},
	{
	  id: 2,
	  name: '文档导入',
	  is_active: false
	}
])
const tab_active_num = ref(0)
const click_tab = (id) => {
	isUploading.value=false
	tab_active_num.value = id
	input_link.value=''
	currentFile.value.name=''
	link_Date.value =''
	uploadedFile.value=null
	uploadRef.value&&uploadRef.value.clearFiles()
	console.log(uploadRequest.value,'我要取消');
	
	uploadRequest.value&&uploadRequest.value.cancel('请求已被取消'); 
}
// 输入的短视频链接
const input_link = ref('')

// 按钮是否加载状态
const loading = ref(false)
// emit('call-parent', item);  点击提取文案按钮
const extract_button = () => {
	console.log(tab_active_num.value);
	
	if (tab_active_num.value == 0) {
		extract_link()
	} else{
		extract_file(tab_active_num.value)
	}
}
// 文件提取
const extract_file = (type) => {
	if ((!link_Date.value || !link_Date.value?.url) && (!uploadedFile.value || !uploadedFile.value?.url)) {
		ElMessage({
			message: '请先上传文件',
			type: 'warning',
		})
	} else {
		loading.value = true
		// 优先使用AudioUpload组件上传的文件URL
		const fileUrl = uploadedFile.value?.url || link_Date.value?.url;
		uploadRequest.value = axios.CancelToken.source();
		let requestFunc =''
		let params={
			request_timeout:1800000,//超时设置半小时
			userId: getUserId(),
			cancelToken: uploadRequest.value.token
		}
		//1是本地音视频2是文档导入
		if(type==1){
			requestFunc=crawlByVideoPage
			params.url=fileUrl
		}else{
			requestFunc=extractTextByWorkTxt 
			params.txtUrl=fileUrl
		}
		requestFunc(params).then(res => {
			// console.log('pppp',res)
			const {
				code,
				data: {
					content: { result = null } = {},
					status_code
				} = {}
			} = res || {};
			if (code === 0 && status_code === 200) {
				if(type==1&&result?.txt){
					emit('call-parent', result.txt);
				}else{
					emit('call-parent', result);
				}
				
				// emit('call-parent', 'dksjfhaksdjhfksjahfkjsdhf的咖啡机萨拉丁看撒旦看风景阿斯蒂芬螺丝扣搭街坊');
				ElMessage({ message: '提取文案成功', type: 'success' });
				importDialogVisible.value = false
			} else {
				// 细化错误类型提示
				const errorMsg = !result ? '数据不存在' : '链接提取失败';
				ElMessage({ message: '链接提取失败', type: 'warning' });
			}
			loading.value = false
		}).catch(err => {
			loading.value = false
			console.log('7879', err)
		})
	}
}



// 链接提取
const extract_link = () => {
	if (!input_link.value) {
		ElMessage({
			message: '链接不能为空',
			type: 'warning',
		})
		return
	}
	loading.value = true
	uploadRequest.value = axios.CancelToken.source();
	crawlTextByVideoPage({
		url: input_link.value,
		request_timeout:1800000,//超时设置半小时
		userId: getUserId(),
		cancelToken: uploadRequest.value.token
	}).then(res => {
		const {
			code,
			data: {
				content: { result = null } = {},
				status_code
			} = {}
		} = res || {};
		if (code === 0 && status_code === 200 && result?.txt) {
			emit('call-parent', result.txt);
			// emit('call-parent', 'dksjfhaksdjhfksjahfkjsdhf的咖啡机萨拉丁看撒旦看风景阿斯蒂芬螺丝扣搭街坊');
			ElMessage({ message: '提取文案成功', type: 'success' });
			importDialogVisible.value = false
		} else {
			// 细化错误类型提示
			const errorMsg = !result ? '数据不存在' : '链接提取失败';
			ElMessage({ message: '链接提取失败', type: 'warning' });
		}
		loading.value = false
	}).catch(err => {
		loading.value = false
		console.log(err)
	})
}



// 上传文件
// 以下函数已由AudioUpload组件替代，保留以兼容旧代码
// 上传文件前校验
const beforeUpload = async (file) => {
	// console.log('544545',file)
	await getSignatureFun(file)
	console.log(tab_active_num.value);
	const fileExtension = file.name.split('.').pop().toLowerCase();
	let allowedExtensions =[]
	// 根据当前tab选择不同的文件类型验证
	if (tab_active_num.value === 1) {
		// 视频文件格式
		allowedExtensions = ['mp3', 'wav', 'wmv', 'mp4', 'mov', 'rmvb', '3gp'];
		const fileExtension = file.name.split('.').pop().toLowerCase();
		const isValidExtension = allowedExtensions.includes(fileExtension);
		
		if (!isValidExtension) {
			
			ElMessage.error('请上传支持的视频或音频格式！');
			isUploading.value = false;
			return false;
		}
	} else if (tab_active_num.value === 2) {
		// // 图片文件格式
		// const allowedExtensions = ['jpg', 'jpeg', 'png', 'bmp'];
		// const fileExtension = file.name.split('.').pop().toLowerCase();
		// const isValidExtension = allowedExtensions.includes(fileExtension);
		
		// if (!isValidExtension) {
		// 	ElMessage.error('请上传支持的图片格式！');
		// 	return false;
		// }
		 // 文本和Word文档格式
		allowedExtensions = ['txt', 'doc', 'docx'];
		if (!allowedExtensions.includes(fileExtension)) {
			ElMessage.error('请上传txt或word文档格式！');
			isUploading.value = false;
			return false;
		}
	}
	
	// 文件大小限制可以在这里添加
	// const isValidSize = file.size <= 100 * 1024 * 1024; // 100MB限制
	// if (!isValidSize) {
	//   ElMessage.error('文件大小不能超过 100MB！');
	//   return false;
	// }
	
	return true;
}
// 获取的文件上传路径
const fileUrl = ref('')
const signatureData = ref({})
let isUploading=ref(false)
let uploadProgress=ref(0)
let currentFile = ref({
	name:''
});
// 上传文件之前获取oss签名
const getSignatureFun = async (file) => {
	try {
		isUploading.value = true;
        uploadProgress.value = 0;
		const response = await get_signature({ userId: getUserId(), fileType: file.type })
		let { code, data } = response
		if (code !== 0) {
			// console.log('获取签名失败')
			return
		}
		signatureData.value = data
		// const fileUrl = `${response.host}/${response.key}?Expires=${response.expire}&OSSAccessKeyId=${response.accessid}&Signature=${encodeURIComponent(response.signature)}`;
		fileUrl.value = `${data.host}/${data.key}?Expires=${data.expire}&OSSAccessKeyId=${data.accessid}&Signature=${data.signature}`
		// console.log('pppopop',fileUrl)
		// console.log('response',response)
	} catch (err) {
		isUploading.value = false;
		console.log(err)
	} finally {

	}
}
const uploadFile = async (file) => {
	const formData = new FormData();
	formData.append('OSSAccessKeyId', signatureData.value.accessKeyId);
	formData.append('policy', signatureData.value.policy);
	formData.append('signature', signatureData.value.signature);
	// formData.append('key', `${signatureData.value.dir}/${file.name}`);
	formData.append('key', `${signatureData.value.key.replace(/[^\/]+$/, '')}${file.name}`);

	formData.append('file', file);
 	 uploadRequest.value = axios.CancelToken.source();
	// 3. 发送 POST 请求
	await axios.post(signatureData.value.host, formData, {
		headers: { 'Content-Type': 'multipart/form-data' },
		cancelToken: uploadRequest.value.token, 
		onUploadProgress: (progressEvent) => {
			if (progressEvent.lengthComputable) {
                uploadProgress.value = Math.round(progressEvent.loaded / progressEvent.total * 100);
            }
			// 计算上传进度
			const total = progressEvent.total;
			const current = progressEvent.loaded;
			const percentage = Math.round((current / total) * 100);
			console.log(`上传进度: ${percentage}%`);
		}
	});
	isUploading.value = false;
	uploadProgress.value = 100;
};
/*提取音视频时获取的data*/
const link_Date = ref('');
let uploadRequest=ref(null)
const handleOssUpload = async (options) => {
	// console.log('pppppppppppppppppppp')
	try {
		const { file } = options;
		const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.')
		// console.log('ppp',file)
		const data = await uploadFile(file)
	
		
	    uploadRequest.value = axios.CancelToken.source();
		// console.log('data',data)
		upload_callback({
			userId: getUserId(),
			materialName: fileNameWithoutExt,
			ossPath: signatureData.value.key.replace(/[^\/]+$/, '') + file.name,
			fileSize: String(file.size),
			fileExtension: file.name.split('.').pop(),
			tagNames: '1',
			materialType: tab_active_num.value === 1 ? 'video' : 'audio',
			isPrivate: '1',
			storage_path: `/material/${getUserId()}/${file.name}`,
			music_classification: 'bgm',
			temporary: '1',
			cancelToken: uploadRequest.value.token
		}).then(res => {
			if (res.code == 0) {
				console.log(res,file,'看看是啥啊');
				currentFile.value = {
					name: `${file.name}`,
				};
				ElMessage({
					message: '上传成功',
					type: 'success',
				})
				// 保存返回的链接以及name
				link_Date.value = res.data
				// 同时更新uploadedFile变量，保持功能一致性
				uploadedFile.value = {
					url: res.data.url,
					name: res.data.name || file.name
				}
				
				// 重置上传组件状态，确保可以再次上传
				if (uploadRef.value) {
					uploadRef.value.clearFiles();
				}
			
		
				
				
			} else {
				ElMessage({
					message: '上传失败',
					type: 'error',
				})
			}
		}).catch(err => {
			// console.log('46546',err)
		})



	} catch (error) {
		console.error('上传失败：', error);
	}
};

// 添加存储上传文件信息的响应式变量
const uploadedFile = ref(null);

// 获取用户ID的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

// 处理上传成功
const handleUploadSuccess = (fileInfo) => {
    uploadedFile.value = fileInfo;
    link_Date.value = {
        url: fileInfo.url,
        name: fileInfo.name
    };
    ElMessage.success('文件上传成功');
};

// 处理上传错误
const handleUploadError = (error) => {
    uploadedFile.value = null;
    link_Date.value = null;
    ElMessage.error('文件上传失败');
};
let uploadRef = ref(null)
//关闭清空
let close=()=>{
	importDialogVisible.value=false
	tab_active_num.value=0
	input_link.value=''
	currentFile.value.name=''
	link_Date.value =''
	uploadedFile.value=null
	uploadRef.value&&uploadRef.value.clearFiles()
	
}
defineExpose({ importDialogVisible })
</script>

<template>
	<Teleport to="body">
		<el-dialog v-model="importDialogVisible" width="720" class="letter_dialog" center :show-close='false'>
			<template #header>
				<span class="text-align-center">导入文案</span>
				<img src="@/assets/images/aiImages/letter_dialog_close.svg" @click="close" class="letter_dialog_close" alt="">
			</template>
			<div class="dialog_content flex flex_d-column flex_a_i-center">
		
				<div class="dialog_content_tab">
					<ul class="height-full flex flex_a_i-center flex_j_c-space-around">
						<li class="cursor-pointer  flex flex_a_i-center flex_j_c-center"
							:class="{ is_active: tab_active_num === item.id }" v-for="item in tab_list" :key="item.id"
							@click="click_tab(item.id)">
							{{ item.name }}</li>
					</ul>
				</div>
				<template v-if="tab_active_num === 0">
					<div class="dialog_content_middle1 text-align-center font-size-12 padding-n-70 margin-10-n">
						<!--            复制短视频链接（基本覆盖所有短视频平台）一键解析，若源文件过大有可能导致提取失败，可用本地视频方式-->
					</div>
					<div class="dialog_content_middle2  height-161 margin_b-28" v-loading="loading">
						<el-scrollbar height="161">
							<el-input v-model="input_link" resize="none" autosize type="textarea"
								placeholder="请输入短视频链接(若源文件过大有可能导致提取失败，可用本地视频方式)" />
						</el-scrollbar>
					</div>
				</template>
				<!-- v-if="tab_active_num === 1 || tab_active_num === 2" -->
				<template v-if="tab_active_num === 1">
					<div class="width-full margin_t-20 margin_b-28" >
						<el-upload drag ref="uploadRef" action="#" class="upload-demo width-full" :auto-upload="true"
							:http-request="handleOssUpload" :before-upload="beforeUpload" :show-file-list="false"
							 accept=".mp3, .mp4, video/*, audio/*" v-loading="loading">
							<div class="file-container" @click.stop="handleUploadClick" v-if="currentFile.name!=''">
								<!-- 左侧图标+文字 -->
								<div class="left-group">
									<!-- <el-icon class="file-icon"><VideoCamera /></el-icon> -->
									<img class="file-icon" src="@/assets//img/1.png" alt="" />
									<span class="file-name">{{ currentFile.name }}</span>
								</div>
							</div>
							<template v-else>
								<el-icon class="el-icon--upload"><upload-filled /></el-icon>
								<div class="el-upload__text padding-n-75" v-if="tab_active_num === 1">
									支持 mp3/wav/wmv/mp4/mov/rmvb/3gp 格式可直接拖拽文件到此处，或点击上传
								</div>
								<!-- <div class="el-upload__text padding-n-75" v-if="tab_active_num === 2">
									支持 jpg/jpeg/png/bmp 格式可直接拖拽文件到此处，或点击上传
								</div> -->
							</template>
							<template v-if="isUploading">
								
								<div v-if="isUploading" class="upload-mask">
									<div class="upload-progress">
										<el-progress :percentage="uploadProgress" :stroke-width="20" :show-text="false" />
										<div class="progress-text">正在上传中 {{ uploadProgress }}%</div>
									</div>
								</div>
							</template>
							
							
						</el-upload>
					</div>
				</template>
				<template v-if="tab_active_num === 2">
					<div class="dialog_content_middle3  height-161 margin_b-28 margin_t-20" v-loading="loading">
						<el-upload drag ref="uploadRef" action="#" class="upload-demo width-full" :auto-upload="true"
							:http-request="handleOssUpload" :before-upload="beforeUpload" :show-file-list="false"
							 accept=".txt, .doc, .docx" v-loading="loading">
							<div class="file-container" @click.stop="handleUploadClick" v-if="currentFile.name!=''">
								<!-- 左侧图标+文字 -->
								<div class="left-group">
									<!-- <el-icon class="file-icon"><VideoCamera /></el-icon> -->
									<img class="file-icon" src="@/assets//img/1.png" alt="" />
									<span class="file-name">{{ currentFile.name }}</span>
								</div>
							</div>
							<template v-else>
								<img src="@/assets/images/aiImages/document_import.svg" alt="">
								<span>上传本地txt和word文档格式</span>
								<!-- <div class="el-upload__text padding-n-75" v-if="tab_active_num === 2">
									支持 jpg/jpeg/png/bmp 格式可直接拖拽文件到此处，或点击上传
								</div> -->
							</template>
							<template v-if="isUploading">
								
								<div v-if="isUploading" class="upload-mask">
									<div class="upload-progress">
										<el-progress :percentage="uploadProgress" :stroke-width="20" :show-text="false" />
										<div class="progress-text">正在上传中 {{ uploadProgress }}%</div>
									</div>
								</div>
							</template>
							
							
						</el-upload>
					
					</div>
				</template>
				<!--        <div class="dialog_content_button cursor-pointer width-130 height-38  flex flex_a_i-center flex_j_c-center font-size-14">提取文案</div>-->
				<el-button style="background: #0BAF60;color:#fff;" class="font-size-14 letter_dialog_submit" @click="extract_button"
					:loading="loading">提取文案</el-button>
			</div>
		</el-dialog>
	</Teleport>
</template>

<style scoped lang="scss">

.el-upload-dragger .el-upload__text {
	color: var(--el-text-color-regular);
	font-size: 14px;
	text-align: center;
	line-height: 20px;
}

.dialog_content {

	//background-color: #006eff;
	span {
		font-weight: 500;
		color: #121212;
	}

	&_tab {
		border-radius: 24px;
		background-color: #fff;
	
		padding: 0 3px;
		width: 100%;
		box-sizing: border-box;
		ul {
			border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			display: flex;
			height: 36px;
			align-items: center;
			li {
				font-size: 15px;
				height: 100%;
				text-align: center;
				color: rgba(29, 33, 41, 0.85);
				padding: 0 27px;
				box-sizing: border-box;
				font-family: 'PingFang SC';
				&.is_active {
					color: #0AAF60;
					border-bottom: 3px solid #0AAF60;
				}
			}

			
		}
	}

	&_middle1 {
		font-weight: 400;
		color: #7E838D;
	}

	&_middle2 {
		//background-color: #67c23a;
		border-radius: 4px;
		background-color: #f7f7f9;
		width: 100%;
		::v-deep(.el-textarea__inner) {
			border: none;
			outline: none !important;
			box-shadow: none;
			background-color: rgba(255, 255, 255, 0);
		}
	}
	
	&_button {
		background: #0BAF60;
		border-radius: 8px;
		font-weight: 500;
		color: #FFFFFF;
	}

	// 添加拖拽上传样式增强
	:deep(.el-upload-dragger) {
		width: 100%;
		height: 161px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 2px dashed #d9d9d9;
		border-radius: 8px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		transition: all 0.3s;

		&:hover {
			border-color: #0BAF60;
			background-color: rgba(10, 175, 96, 0.06);
		}

		&.is-dragover {
			border-color: #0BAF60;
			background-color: rgba(10, 175, 96, 0.1);
		}

		.el-icon--upload {
			font-size: 28px;
			color: #8c939d;
			margin-bottom: 10px;
		}

		.el-upload__text {
			color: #606266;
			font-size: 14px;
			text-align: center;
			margin: 10px 0;
		}
	}
	&_middle3 {
		//background-color: #67c23a;
		border-radius: 4px;
		background-color: #f7f7f9;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		:deep(.el-upload-dragger){
			border:none ;
			background-color: transparent;
		}
		img{
			width: 40px;
			height: 40px;
			margin-bottom: 12px;
			border: 1px solid #E9EAEB;
			filter: drop-shadow(0px 2px 4px rgba(27, 28, 29, 0.04));
		}
		span{
			font-size: 12px;
			line-height: 15px;
			text-align: center;
			color: #9EA2AD;
		}
	}
    .upload-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #fbf9f9; // 使用与上传按钮相同的背景色
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100;
        border-radius: 8px;
        border: 2px dashed #fbf9f9; // 添加与上传按钮相同的边框

        .upload-progress {
            width: 80%;
            text-align: center;
            padding: 26px; // 添加与上传按钮相同的内边距

            :deep(.el-progress-bar__outer) {
                background-color: #f0f0f0;
                border-radius: 4px;
                height: 20px !important; // 确保进度条高度一致
            }

            :deep(.el-progress-bar__inner) {
                border-radius: 4px;
                background-color: #bfe8bb; // 使用 Element Plus 主题色
            }

            .progress-text {
                margin-top: 12px;
                color: #303133; // 使用与上传按钮文字相同的颜色
                font-size: 14px;
                text-align: center;
            }
        }
    }
	 // 上传后状态（非触发区域）
	 .file-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 26px;
            border: 1px solid #fbf9f9;
            border-radius: 6px;
            // background: #fbf9f9;
			pointer-events: none; /* 使该元素不响应鼠标事件 */
            .left-group {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;
                min-width: 0;

                .file-icon {
                    width: 28px;
                    height: 28px;
                    font-size: 24px;
                    color: #606266;
                }

                .file-name {
                    font-size: 14px;
                    color: #303133;
                    // @include text-ellipsis;
                }
            }

            .right-group {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-left: 16px;

                :deep(.el-divider--vertical) {
                    margin: 0 4px;
                    height: 1em;
                }

                .action-btn {
                    width: 98px;
                    height: 36px;
                    border: 1px solid #DCDCDC;
                    padding: 0;
                    font-size: 13px;
                    color: #000;
                }
            }
        }
	.letter_dialog_submit{
		display: flex;
		justify-content: center;
		align-items: center;
		width: 648px;
		height: 42px;
		/* 主色 */
		background: #0AAF60;
		border-radius: 3px;
		padding: 0;
		box-sizing: border-box;
	}
}
</style>
<style lang="scss">
.el-dialog{
	&.letter_dialog{
		background: #FFFFFF;
		border-radius: 8px;
		padding: 0 35px;
		padding-bottom: 16px;
		box-sizing: border-box;
		.el-dialog__header{
			padding: 0;
			position: relative;
			span{
				display: block;
				line-height: 54px;
				font-size: 16px;
				color: #1D2129;
				font-family: 'PingFang SC';

			}
			.letter_dialog_close{
			    cursor: pointer;
				position: absolute;
				right: -19px;
				top: 20px;
			}
		}
	}
}

</style>