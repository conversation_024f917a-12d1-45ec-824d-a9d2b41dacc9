<!-- src/components/GlobalModal.vue -->
<template>
  <Teleport to="html">
<!--    style="background-color: #f0f0f0;"-->
    <el-dialog class="login_dialog" v-model="isVisible" width="461" center  style="background-size: cover"  :style="{
  backgroundImage: `url(${loginBg})`,
  transformOrigin: 'top center',
  transform: `scale(${rate})`,
   borderRadius:'8px'
}">
      <div class="login_content">
         <p class="login_title">登录配音帮手</p>
          <ul class="subtitle">
            <li v-for="item in iconsArr" :key="item.name">
              <!-- <Iconfont
                  class="margin_r-5 cursor-pointer"
                  :size="`${item.size}px`"
                  :name="`${item.name}`"
              /> -->
              <img :src="item.name" alt="">
              <span>{{ item.title }}</span>
            </li>
          </ul>
          <div class="login_main_content">
            <!--            登录时显示          -->
            <div class="login_main_content_top flex padding-n-14" v-show="loginBool">
              <div class="login_main_content_top_item flex flex_a_i-center flex_j_c-center" :class="tabHeaderNum==index?'active':''" v-for="(item,index) in tabBarHeaderArr" :key="index" @click="tabHeaderClick(index)">
                <span>{{item.title}}</span>
                <!-- :class="tabHeaderNum==index&&tabHeaderNum!=1 ?'login_main_content_top_item_bottom_active':'login_main_content_top_item_bottom_unactive'" -->
                <div class="login_main_content_top_item_bottom"
                     :class="tabHeaderNum==index ?'login_main_content_top_item_bottom_active':'login_main_content_top_item_bottom_unactive'"
                ></div>
              </div>
            </div>
            <!--     注册时显示信息       -->
            <div class="login_main_content_top_zhuce flex padding-n-14" v-show="registerBool||backPasswordBool">
              <el-icon class="position-icon cursor-pointer" v-show="backPasswordBool" @click="backLoginPage(ruleFormRef)">
                <ArrowLeftBold />
              </el-icon>
              <div class="login_main_content_top_zhuce_tab login-tab-active">{{registerBool?'注册':'找回密码'}}</div>
            </div>

            <!--      中间显示的整的外层框      -->
            <div class="login_main_content_middle">

              <!--   登录时显示信息   -->
              <div class="wx_content" >
                <!--      微信登录时显示时        -->
              <!--  <div class="wx_content_main" v-show="tabHeaderNum==0">
                 <div class="qr_code">
                  <iframe src="https://open.weixin.qq.com/connect/qrconnect?appid=wx3a4eb68d21646666&redirect_uri=http%3A%2F%2F8.130.65.126%3A8080%2FuserAuth%2Fauth%2Flogin%2Fcallback&response_type=code&scope=snsapi_login&state=07790207095#wechat_redirect"
                  style="
                    position: absolute;
                    top: -30px; 
                    left: -68px;
                    width: 500px; 
                    height: 500px; 
                    border: none;
                    transform: scale(0.61); 
                    transform-origin: 0 0;
                  "
                   frameborder="0"></iframe>
                 </div>
               
                 <span>使用微信扫一扫注册/登录</span>
               </div> -->
                <div class="phone_content_main" v-show="tabHeaderNum==0||tabHeaderNum==1">
                 
                  <el-form
                      :rules="rules"
                      :model="ruleForm"
                      ref="ruleFormRef"
                      label-width="0px"
                      class="demo-ruleForm"
                      @keyup.enter.native="submit_enter(ruleFormRef)"
                  >
                    <el-form-item prop="phone">
                      <el-input v-model="ruleForm.phone" type="text" autocomplete="one-time-code" placeholder="请输入手机号"  size="large" style="width:306px" />
                    </el-form-item>
                    <!-- 隐藏验证码发送 -->
                    <el-form-item prop="phoneCode" v-show="!verificationCodeBool|| registerBool||backPasswordBool" >
                      <el-input v-model="ruleForm.phoneCode" type="text"  autocomplete="one-time-code"  placeholder="请输入验证码"  size="large" style="width:306px" >
                        <template #append>
                          <el-button size="mini" type="primary"  @click="getCode" v-if="isCounting">获取验证码</el-button>
                            <el-countdown :value="countDown" @finish="handleFinish" style="color:#fff;" format="ss"  value-style="font-size:14px;color:#fff;" v-else>
                              <template #suffix>
                                <span class="font-size-14" style="color:#fff;">s后重新发送</span>
                              </template>
                            </el-countdown>
                        </template>
                      </el-input>
                    </el-form-item>
                    <el-form-item prop="pass" v-show="verificationCodeBool || registerBool">
                      <el-input v-model="ruleForm.pass" :type="passwardStateTow?'text':'password'" autocomplete="off"
                      
                                :placeholder="registerBool?'请输入密码:8-20位，数字+字母':'请输入密码'"  size="large" style="width:306px"
                      >
                        <template #suffix>
                          <el-icon class="el-input__icon cursor-pointer" @click="showPassword(1)">
                            <hide  v-if="!passwardStateTow"/>
                            <View  v-else/>
                          </el-icon>

                        </template>
                      </el-input>
                    </el-form-item>
                    <!--         注册时重新输入密码输入框           -->
                    <el-form-item prop="ReenterPassword" v-show="registerBool||backPasswordBool">
                      <el-input v-model="ruleForm.ReenterPassword" :type="passwardState?'text':'password'" autocomplete="off"
                                placeholder="请输入密码:8-20位，数字+字母"  size="large" style="width:306px"
                      >
                        <template #suffix>
                          <el-icon class="el-input__icon cursor-pointer" @click="showPassword(2)">
                            <hide  v-if="!passwardState"/>
                            <View  v-else/>
                          </el-icon>
                        </template>
                      </el-input>
                    </el-form-item>
                    <el-form-item prop="pass" v-show="backPasswordBool">
                      <el-input v-model="ruleForm.pass" :type="passwardStateThree?'text':'password'" autocomplete="off" placeholder="请输入密码:8-20位，数字+字母"  size="large" style="width:306px">
                        <template #suffix>
                          <el-icon class="el-input__icon cursor-pointer" @click="showPassword(3)">
                            <hide  v-if="!passwardStateThree"/>
                            <View  v-else/>
                          </el-icon>
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-form>
                 
                  <div class="phone_content_main_bottom">
                    <!-- <div class="phone_content_main_bottom_top" v-show="loginBool">
                      <div class="cursor-pointer font-color" @click="verificationCodeFun(ruleFormRef)">{{verificationCodeBool==false?'密码':'验证码'}}登录</div>
                      <div class="cursor-pointer font-color" v-show="verificationCodeBool" @click="forgotPassButton(ruleFormRef)">忘记密码?</div>
                    </div> -->
                    <div class="phone_content_main_bottom_middle">
                      <div class="cursor-pointer" v-loading="login_loading" @click="login_button('ruleFormRef')" v-show="loginBool">登录</div>
                      <!-- <div class="cursor-pointer" v-loading="register_loading"  v-show="registerBool" @click="clickRegisterButton(ruleFormRef)">注册</div>
                      <div class="cursor-pointer" v-loading="password_loading" v-show="backPasswordBool" @click="findPasswordHandle">确定</div> -->
                    </div>
                    <!-- <div class="phone_content_main_bottom_bottom">
                      <div v-show="loginBool">还没有账号? <span class="cursor-pointer"  @click="registerButton(true,ruleFormRef)">立即注册</span></div>
                      <div v-show="registerBool">已有账号? <span class="cursor-pointer"  @click="registerButton(false,ruleFormRef)">直接登录</span></div>
                    </div> -->
                  </div>
                </div>
                <div class="register_login_tip">
                  未注册用户将自动注册
                </div>
              </div>
            </div>
            <div class="login_main_content_bottom flex flex_a_i-center flex_j_c-center">
              登录即表示已阅读并同意 <span class="cursor-pointer text-blue" @click="go_user_agreement">用户协议</span> 和 <span class="cursor-pointer text-blue" @click="go_privacy_agreement">隐私协议</span>
            </div>
          </div>
      </div>
    </el-dialog>
  </Teleport>
</template>
<script setup>
import { ref,inject,onMounted,onUnmounted,reactive,watch,nextTick,defineExpose  } from 'vue'
import { useAuthStore } from '@/stores/modules/auth.js'
import {Connection} from "@element-plus/icons-vue";
import loginBg from '@/assets/images/login_bg.png'
import { View,Hide } from '@element-plus/icons-vue'
import { getRegisterCode,getRegister,getLoginCode,LoginCode,loginByCode,getFindPassword,FindPassword,LoginPassword,userInfo,showUserBenefits,qrcode,wxStatus,sendData } from '@/api/login.js'
import { ElMessage } from "element-plus";
import { useRouter,useRoute } from 'vue-router'
import { useloginStore } from '@/stores/login'
import bcrypt from 'bcryptjs';
import { encryptPassword } from '@/utils/publicKey.js';
import loginEdit from '@/assets/images/login/login_edit.svg'
import loginDub from '@/assets/images/login/login_dub.svg'
import loginTool from '@/assets/images/login/login_tool.svg'
import loginCloud from '@/assets/images/login/login_cloud.svg'
import QRCode from 'qrcode.vue';
import { useUmeng } from '@/utils/umeng/hook';
let saltRounds = 10; // 盐值复杂度（建议10-12）
const authStore = useAuthStore()
const loginStore = useloginStore()
// 初始化埋点
const umeng = useUmeng()
// 路由实例
const router = useRouter()
let route = useRoute
const isVisible = ref(false)
const title = ref('默认标题')
let rate=ref(window.innerWidth/1920)
const eventBus = inject('eventBus11');
const open = (title) => {
  console.log('Opening modal with title:', title);
 
  // 打开模态框的逻辑
  isVisible.value = true;

  

};
let validateField = async (field) => {
  try {
    await ruleFormRef.value.validateField(field);
    return true;
  } catch (error) {
    return false;
  }
};;
let register_loading=ref(false)
let login_loading=ref(false)
let password_loading=ref(false)
let wx_login=ref({})
const close = () => {
  console.log('Closing modal');
  // 关闭模态框的逻辑
};
let handleWechatCallback=()=>{
  
}

onMounted(() => {
  eventBus.on('openModal', open);
  eventBus.on('closeModal', close);
  window,addEventListener('message' ,handleWechatCallback)



});
let submit_enter=(ruleFormRef)=>{
  if(loginBool.value){
    login_button('ruleFormRef')
  }else if(registerBool.value){
    clickRegisterButton(ruleFormRef)
  }else if(backPasswordBool.value){
    findPasswordHandle()
  }
}
onUnmounted(() => {
  eventBus.off('openModal', open);
  eventBus.off('closeModal', close);
});

const iconsArr = reactive([
  {
    name:loginEdit,
    size:20,
    title:'AI剪辑'
  },
  {
    name:loginDub,
    size:20,
    title:'品质配音'
  },
  {
    name:loginTool,
    size:20,
    title:'效率工具'
  },
  {
    name:loginCloud,
    size:20,
    title:'云端创作'
  },

])

// 判断是验证码登录还是密码登录  false默认验证码登录 true是密码登录
const verificationCodeBool = ref(false)
const verificationCodeFun = (ruleFormRef)=>{
  isWeixLogin.value=false
  ruleFormRef.value&&ruleFormRef.value.clearValidate()
  verificationCodeBool.value = !verificationCodeBool.value
  if(verificationCodeBool.value){
    tabHeaderNum.value=1
  }else{
    tabHeaderNum.value=0
  }
  resetForm(ruleFormRef)
  handleFinish()
}
//
const tabBarHeaderArr = reactive([
  // {title:'微信登录'},
  {title:'验证码登录'},
  // {title:'微信登录'},
  // {title:'账号密码登录'},
])
const tabHeaderNum = ref(0)
let isWeixLogin = ref(false)
// 登录标签点击事件
const tabHeaderClick = (i)=>{
  console.log(i);
  ruleForm.phone=''
  isWeixLogin.value = false
  if(i==1){
    ruleForm.phoneCode=''
    verificationCodeBool.value=true
  }
  // else if(i==1){
  //   wxLogin()
  // }
  else{
    ruleForm.pass=''
    verificationCodeBool.value=false
  }
  // if(i!=1){
    tabHeaderNum.value = i
  // }
  updateRules()
}
// 登录表单
const ruleForm = reactive({
  pass: '',  //密码
  phone: '', //手机号
  ReenterPassword:'',  //重新输入密码
  phoneCode:'',  //手机验证码
})


// 点击icon查看密码
const passwardState = ref(false)
const passwardStateTow = ref(false)
const passwardStateThree = ref(false)

const showPassword=(type)=>{
  if(type==1){
    passwardStateTow.value = !passwardStateTow.value
  }else if(type==2){
    passwardState.value = !passwardState.value
  }else if(type==3){
    passwardStateThree.value = !passwardStateThree.value
  }
}
// 登录表单验证
const rules = reactive({
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  pass: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max: 20, message: '密码:8-20位，数字+字母',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/},
    {
      validator: (rule, value, callback) => {
        if (backPasswordBool.value&&value !== ruleForm.ReenterPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
   }
  ],
  ReenterPassword: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
    { min: 8, max: 20, message: '密码:8-20位，数字+字母',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/ },
    {
      validator: (rule, value, callback) => {
        if (registerBool.value&&value !== ruleForm.pass) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
   }
  ],
  // pass: [
  //   { required: true, message: '请输入密码', trigger: 'blur' },
  //   { min: 8, max: 20, message: '密码:8-20位，数字+大小写字母+符号',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,20}$/ }
  // ],
  // ReenterPassword: [
  //   { required: true, message: '请输入确认密码', trigger: 'blur' },
  //   { min: 8, max: 20, message: '密码:8-20位，数字+大小写字母+符号',trigger: 'blur',pattern:/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,20}$/ }
  // ],
  phoneCode: [
    { required: true, message: '请输入手机号验证码', trigger: 'blur' },
  ],
})

// 登录按钮操作
const loginBool = ref(true)  //登录显示项据此判断
const ruleFormRef = ref(null)

// 定义百度埋点函数
const trackBaiduConversion = (isLogin) => {
  // 基础URL
  let baseUrl = "https://peiyinbangshou.com/H4Home?utm_source=baidu&utm_medium=ocpc_baidu&utm_id=20250527";
  
  // 从当前浏览器URL中获取bd_vid参数
  const currentUrl = window.location.href;
  const bdVidMatch = currentUrl.match(/[?&]bd_vid=([^&#]*)/);
  const bd_vid = bdVidMatch ? bdVidMatch[1] : '';
  
  // 如果存在bd_vid参数，则添加到logidUrl中
  const logidUrl = bd_vid ? `${baseUrl}&bd_vid=${bd_vid}` : baseUrl;

  const params = {
    conversionTypes: [
      {
        logidUrl: logidUrl,
        newType: 25 // 登录和注册统一使用3
      },
      
    ]
  };
  
  // 调用sendData接口
  sendData(params).catch(err => {
    console.error('百度埋点上报失败:', err);
  });
};

const login_button = async () => {
  console.log(rules,loginBool.value,verificationCodeBool.value,'rules11111');
  isWeixLogin.value = false
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate(async(valid, fields) => {
    if (valid) {
      // 登录尝试埋点（点击登录按钮后，发起请求前）
    //   umeng.trackEvent(
    //     '用户操作',
    //     '点击登录',
    //     `登录方式: ${verificationCodeBool.value ? '密码登录' : '验证码登录'}`,
    //     ''
    //   )
      login_loading.value=true
      try {
        let loginData=''
       
        if(!verificationCodeBool.value){
          loginData=await loginByCode({mobile:ruleForm.phone,code:ruleForm.phoneCode})
        }else{
          console.log(ruleForm.pass,'pass');
         
       
          let hashedPassword =  encryptPassword(ruleForm.pass)
          loginData=await LoginPassword({mobile:ruleForm.phone,password:hashedPassword})
          console.log(loginData,'loginData');
          
        
        
        }
        if(loginData.code!=0){
            ElMessage({ message:loginData.msg, type: 'error' });
            login_loading.value=false
            // 登录失败埋点
            // umeng.trackEvent(
            //   '用户操作',
            //   '登录失败',
            //   `登录方式: ${verificationCodeBool.value ? '密码登录' : '验证码登录'}`,
            //   ''
            // )
            return
          }
        // 先写入userId
        await userData(loginData)
        login_loading.value=false
        ElMessage({ message:'登录成功', type: 'success' });

        // 根据loginByCode接口返回的register参数决定友盟埋点的操作类型
        let operationType = '';
        let actionDetail = '';
        
        if(!verificationCodeBool.value){
          // 验证码登录，根据接口返回的register参数判断是注册还是登录
          // register: 0=注册，1=登录
          if(Number(loginData.data.register) === 0){
            operationType = '注册成功';
            actionDetail = '注册方式: 验证码注册';
          } else {
            operationType = '登录成功';
            actionDetail = '登录方式: 验证码登录';
          }
        } else {
          // 密码登录
          operationType = '登录成功';
          actionDetail = '登录方式: 密码登录';
        }
        
        // 登录成功后添加友盟埋点（此时userId已写入store）
        umeng.trackEvent(
          '用户操作', 
          operationType, 
          actionDetail,
          loginData.data.userId || ''  // 添加用户ID参数
        )
        
        // 登录成功后添加百度埋点
        trackBaiduConversion(true);
       
      } catch (error) {
        login_loading.value=false
        // 登录异常埋点
        // umeng.trackEvent(
        //   '用户操作',
        //   '登录异常',
        //   `登录方式: ${verificationCodeBool.value ? '密码登录' : '验证码登录'}`,
        //   ''
        // )
        console.log(error,99);
        
        if(error.response.data){
          ElMessage({ message:error.response.data , type: 'error' });
        }else{
          ElMessage({ message:'用户登录失败请重试！', type: 'error' });
        }
      }
      
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 清除登录表单数据
const resetForm = (formEl) => {
  if (!formEl) return
  setTimeout(()=>{
    formEl.resetFields()
    formEl.clearValidate()
  },100)
  
}
// 立即注册按钮
const clickRegisterButton = async (formEl) => {
  console.log('ppppp')
  isWeixLogin.value=false
  if (!formEl) return
  await formEl.validate(async(valid, fields) => {
    if (valid) {
      register_loading.value=true
      try {
        let obj={
          mobile: ruleForm.phone,
          code: ruleForm.phoneCode,
          password:ruleForm.pass,
          confirmPassword:ruleForm.ReenterPassword   
        }
       
        let register=await getRegister(obj)
        console.log(register,'register');
        
        register_loading.value=false
        if(register.code!=0){
          ElMessage({ message:register.msg, type: 'error' });
          register_loading.value=false
          return
        }
        ElMessage({ message: '注册成功', type: 'success' });
        
        // 注册成功后立即添加友盟埋点
        // umeng.trackEvent(
        //   '用户操作', 
        //   '注册成功', 
        //   `用户ID: ${register.data.userId}`,
        //   register.data.userId || '' // 如果注册接口返回userId则使用，否则传空
        // )
        
        isCounting.value=true
        // 注册成功后添加百度埋点
        trackBaiduConversion(false);
        
        //注册后登录
        let hashedPassword =  encryptPassword(ruleForm.pass)
        let loginData=await LoginPassword({mobile:ruleForm.phone,password:hashedPassword})
        console.log(loginData,'loginData');
        if(loginData.code!=0){
            ElMessage({ message:loginData.msg, type: 'error' });
            login_loading.value=false
            return
        }
		console.log( '用户操作', 
          '注册用户成功，登录成功', 
		  `用户ID: ${loginData.data.userId}`)
        userData(loginData)
        // 登录成功后添加友盟埋点（包含登录成功信息）
        umeng.trackEvent(
          '用户操作', 
          '注册用户成功，登录成功', 
		  `用户ID: ${loginData.data.userId}`,
        )
      } catch (error) {
        register_loading.value=false
        if(error.response.data){
          ElMessage({ message:error.response.data , type: 'error' });
        }else{
          ElMessage({ message:'用户注册失败请重试！', type: 'error' });
        }
       
      }
      
     
    } else {
      console.log('error submit!', fields)
    }
  })
}
//登录后获取用户信息
let userData=async(loginData)=>{
  isVisible.value=false
  loginData.data.token&&loginStore.setToken(loginData.data.token)
  loginData.data.userId&&loginStore.setUserId(loginData.data.userId)
  loginData&&loginStore.setLoginData(loginData)
  let user_data=await userInfo({userId:loginStore.userId})
  user_data&&loginStore.setUserInfo(user_data)
  let res=await showUserBenefits({userId:loginData.data.userId})
  if(res.code!=0){
      ElMessage.error(res.msg) 
      return 
  }
  let member_data=res?.data?.content
  member_data.result&&loginStore.setMemberInfo(member_data.result)
  // setTimeout(()=>{
  //   window.location.reload()
  // },200)
}
let  generate11DigitNumber=()=>{
  let result = '';
  for (let i = 0; i < 11; i++) {
    result += Math.floor(Math.random() * 10); // 生成0-9的随机数字
  }
  return result;
}
let state=ref(0)
//微信扫码登录
let wxLogin=async()=>{
  state.value=generate11DigitNumber()
  let result=await qrcode({state:state.value})
  console.log(result,'wxLogin');
  wx_login.value=result
  isWeixLogin.value = true
  // window.open(wx_login.value.qrUrl, '_blank', );
  // window.close();
  window.location.href = wx_login.value.qrUrl;
}
let initClear=()=>{
  ruleFormRef.value&&ruleFormRef.value.resetFields()
  tabHeaderNum.value=0
  verificationCodeBool.value=false
  registerBool.value=false
  loginBool.value=true
  backPasswordBool.value=false
}
// 获取验证码
// 倒计时
const isCounting = ref(true)
const countDown = ref(Date.now())
// 倒计时结束重置
const handleFinish = ()=>{
  countDown.value = Date.now() + 60000
  isCounting.value = true
}

const getCode = async ()=>{
 
  let is_validate_phone=await validateField('phone')
  if(ruleForm.phone==''){
    ElMessage.error('请先输入手机号！');
    return
  }
  if(!is_validate_phone){
    ElMessage.error('手机号格式不正确！');
    return
  }
  countDown.value = Date.now() + 60000;
  // console.log('获取验证码')
  isCounting.value = false
  // countDown.value = 52
  // let res = await register({
  //   phone:ruleForm.phone,
  //   phoneCode:ruleForm.phoneCode
  // })
  let registerSendCode= ''
  try {
      if(registerBool.value){
        //注册
        registerSendCode=await getRegisterCode(ruleForm.phone)
      }else if(backPasswordBool.value){
        //找回密码
        registerSendCode=await getFindPassword(ruleForm.phone)
      }else if(!verificationCodeBool.value){
        //登录
        registerSendCode= await getLoginCode({mobile:ruleForm.phone})
      }
      if(registerSendCode.code!=0){
          ElMessage({ message:registerSendCode.msg, type: 'error' });       
      }else{
          ElMessage({ message: registerSendCode.data, type: 'success' });
      }
    } catch (error) {
      if(error.response.data){
        ElMessage({ message:error.response.data , type: 'error' });
      }else{
        ElMessage({ message:'获取验证码失败请重试！', type: 'error' });
      }
      
    }



}




// 立即注册按钮
const registerBool = ref(false)
const registerButton = (bool,ruleFormRef)=>{

  console.log('立即注册按钮')
 

  handleFinish()
  registerBool.value = bool
  loginBool.value = !bool

    resetForm(ruleFormRef)

 
 
}

// 找回密码
const backPasswordBool = ref(false)
const forgotPassButton = (ruleFormRef)=>{
  isWeixLogin.value = false
  resetForm(ruleFormRef)
  handleFinish()
  backPasswordBool.value = true
  loginBool.value = false
  verificationCodeBool.value = false
}
// 找回密码中返回icon
const backLoginPage = (ruleFormRef)=>{
  resetForm(ruleFormRef)
  loginBool.value = true
  backPasswordBool.value = false
  verificationCodeBool.value = true
}

// 更新规则
const updateRules = async() => {
  await nextTick()
  setTimeout(()=>{
        if (loginBool.value) {
        if(!verificationCodeBool.value){
          rules.pass[0].required=false
          rules.phoneCode[0].required=true
        }else{
          rules.pass[0].required=true
          rules.phoneCode[0].required=false
        }
        rules.ReenterPassword[0].required=false
        
      } else {
        rules.pass[0].required=true
        rules.ReenterPassword[0].required=true
        rules.phoneCode[0].required=true
      }
      setTimeout(()=>{
        ruleFormRef.value&&ruleFormRef.value.clearValidate()
      },0)
  },200)
  
 
};


const findPasswordHandle=async()=>{
  
  if (!ruleFormRef.value) return
  
  await ruleFormRef.value.validate(async(valid, fields) => {
    if (valid) {
      let findPasswordSub= ''
      password_loading.value=true
      try {
        let obj={
          mobile: ruleForm.phone,
          code: ruleForm.phoneCode,
          newPassword:ruleForm.pass,
          confirmPassword:ruleForm.ReenterPassword   
        }
        findPasswordSub= await FindPassword(obj)
        console.log(findPasswordSub,'findPasswordSub');
        if(findPasswordSub.code==0){
       
          ElMessage({ message: findPasswordSub.data, type: 'success' });
          initClear()
          setTimeout(()=>{
            ruleFormRef.value.resetFields()
            ruleFormRef.value.clearValidate()
            isCounting.value=true
          },200)
        }else{
           ElMessage({ message:findPasswordSub.msg, type: 'error' });
        }
         password_loading.value=false
      } catch (error) {
        console.log(error,'error');
        
        password_loading.value=false
        if(error.response.data){
          ElMessage({ message:error.response.data , type: 'error' });
        }else{
          ElMessage({ message:'找回密码失败请重试！', type: 'error' });
        }
        
        }
      }
  })
}
let go_user_agreement=()=>{
  isVisible.value=false
  router.push({ path: '/agreement', query: { type: 'user' } })
}
let go_privacy_agreement=()=>{
  isVisible.value=false
  router.push({ path: '/agreement', query: { type: 'privacy' } })
}

watch([loginBool, verificationCodeBool], ([newValue1, newValue2], [oldValue1, oldValue2]) => {
  updateRules(); 
},{ immediate: true }) 
watch(isVisible, (newValue, oldValue) => {
   if(!newValue){
    isWeixLogin.value = false
    initClear()
     isCounting.value=true
   }
    // 在这里可以执行其他逻辑
});

// watch(tabHeaderNum, (newValue, oldValue) => {
//    if(newValue==1){

//    }
//     // 在这里可以执行其他逻辑
// },{ immediate: true,deep: true });



























// 暴露方法给父组件
defineExpose({ open, close})
</script>
<style lang="scss">
.el-overlay{
    .el-overlay-dialog{
        overflow: hidden;
    }
}
</style>
<style scoped lang="scss">
.font-color{
  color:var(--main-page-color);
}
.text-blue {
  color: #1890FF;
}
.el-statistic__suffix {
  margin-left: 0px;
  display: inline-block;
}


//.modal-mask {
//  position: fixed;
//  top: 0;
//  left: 0;
//  width: 100%;
//  height: 100%;
//  background: rgba(0, 0, 0, 0.5);
//  display: flex;
//  justify-content: center;
//  align-items: center;
//  z-index: 999;
//}
//
//.modal-container {
//  background: white;
//  padding: 20px;
//  border-radius: 8px;
//  min-width: 300px;
//}


.login_content{
  width:100%;
  padding-top: 19px;
  padding-bottom: 11px;
  //height:400px;
  //background-color: #67c23a;
}
.login_title{
  //background-color: #67c23a;
  font-size: 24px;
  //font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333;
  line-height: 18px;
  text-align: center;
  margin: 0;
  font-size: 25px;
  line-height: 20px;
  font-feature-settings: 'tnum' on, 'lnum' on;
  color: #353D49;
}
.subtitle{
  margin-top: 31px;
  display: flex;
  padding: 0 24px;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;
  margin-bottom: 19px;
  li{
    display: flex  ;
    align-items: center;
    font-size: 14px;
    line-height: 20px;
    color: #353D49;
    img{
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
}

.login_main_content{
  width: 409px;
  //height:200px;
  border-radius: 8px;
  background-color: #fff;
  margin:0 auto;
  padding-top:11px ;
  // 注册样式
  .login_main_content_top_zhuce{
    display: flex;
    justify-content: center;
    //border-bottom: .5px solid #ececec;
    height: 40px;
    //margin-bottom: 20px;
    position: relative;
    .position-icon{
      position: absolute;
      top:50%;
      left:20px;
      transform: translateY(-50%);
    }
    .login-tab-active::after{
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: var(--main-page-color);
    }
    .login_main_content_top_zhuce_tab{
      width: 88px;
      padding: 0;
      line-height: 40px;
      text-align: center;
      font-size: 18px;
      //font-family: HarmonyOS_Sans;
      color: #333;
      cursor: pointer;
      position: relative;
    }
  }




  .login_main_content_top{
    height: 58px;
    justify-content: center;
    //background-color: #5b5b5b;
    &_item{
      height:100%;
      color:#333;
      position: relative;
      margin-right: 54px;
      &_bottom{
        width:100%;
        height:3px;
        position: absolute;
        bottom:0;
      }
      &_bottom_active{
        background-color:#0AAF60
      }
      &_bottom_unactive{
        background-color: #fff;
      }
      &:last-child{
        margin-right: 0;
      }
      span{
        font-size: 17px;
        line-height: 20px;
        color: #353D49;
      }
       &.active{
          span{
            font-weight: 500;
          } 
        }
    }
  }
  .login_main_content_middle{
    //height:320px;
    border-top:1px solid #EFEFF1;
    border-bottom:1px solid #EFEFF1;
    .wx_content{
      width: 100%;
      // min-height: 300px;
      padding-top: 27px;
      padding-bottom: 56px;
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      // min-height: 312px;
      &_main{
        .qr_code{
          width: 170px;
          height:170px;
          background-color: #67c23a;
          margin: auto;
          border: 1px solid #E7E7E7;
          border-radius: 6px;
          overflow: hidden;
          position: relative;
        }
        span{
          display: block;
          font-size: 12px;
          color: #353D49;
          line-height: 20px;
          text-align: center;
          margin-top: 17px;
          font-weight: 400;
          
        }
       
      }
      .phone_content_main{
        .el-form{
          .el-form-item{
            ::v-deep(.el-form-item__content){
              .el-input{
                 border-radius: 6px;
                 border: 1px solid #E7E7E7;
                 position: relative;
                .el-input__wrapper{
                  box-shadow: none;
                  border: none;
                  padding: 0 12px;
                  .el-input__inner{
                    height: 42px;
                    line-height: 42px;
                  }
                }
                .el-input-group__append{
                  box-shadow: none;
                  border: none;
                  position: absolute;
                  right: 0;
                  top: -1px;
                  height: 44px;
                  background: #0AAF60;
                  border-radius: 0px 6px 6px 0px;
                  color: #fff;
                  padding: 0 13px;
                  .el-button{
                    padding: 0;
                    display: contents;
                  }
                  .el-statistic__content{
                    display: flex;
                    align-items: center;
                  }
                  .el-statistic__suffix{
                     display: flex;
                     align-items: center;
                  }
                }
              }
            }
          }
        }
        

        .phone_content_main_bottom{
          margin-top: 43px;
          &_top{
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #006eff;
          }
          &_middle{
            div{
              width:100%;
              height:40px;
              line-height: 40px;
              color:#fff;
              background-color:#0AAF60;
              text-align: center;
              margin-top: 10px;
              border-radius: 6px;
              font-size: 16px;
            }
          }
          &_bottom{
            div{
              width:100%;
              margin-top: 16px;
              text-align: center;
              font{
                color: var(--main-page-color);
              }

            }
          }
        }


      }
      .register_login_tip{
        padding-top: 20px;
        font-size: 14px;
        line-height: 20px;
        color: #353D49;
      }


    }


  }
  .login_main_content_bottom{
    height:58px;
    line-height: 58px;
    //background-color: #00ffff;
    font-weight: 400;
    font-size: 12px;
    color: #353D49;
    font{
      color:var(--main-page-color);
      margin: 0 4px;
    }


  }


}




</style>