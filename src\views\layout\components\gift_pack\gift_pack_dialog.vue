<template>
    <el-dialog v-model="dialogVisible" class="gift_pack_dialog" width="888px">
        <template #header>
            <img class="gift_pack_close" @click="close" src="@/assets/images/index_images/gift_pack_close.png" alt="">
        </template>
        <template #default>
            <div class="gift_pack_dialog_top">
                <div class="gift_pack_dialog_top_title">
                    <div class="gift_pack_dialog_top_title_label">
                        <img src="@/assets/images/index_images/gift_pack_dialog_top_title_label.svg" alt="">
                    </div>
                    <div class="gift_pack_dialog_top_title_describe">
                        <span>登录即可领取专属礼包！</span>
                        <span>注册送SVIP会员！</span>
                    </div>
                </div>
               <div class="gift_pack_dialog_top_tip">
                    <span>领取后24小时有效</span>
               </div>
            </div>
            <div class="gift_pack_dialog_content">
                <div class="gift_pack_dialog_content_item" v-for="(item,index) in list" :key="index">
                    <div class="gift_pack_dialog_content_item_img">
                        <img :src="item.img" alt="" :style="{width: item.imgWidth + 'px', height: item.imgHeight + 'px',marginBottom: item.imgMarginBottom + 'px'}">
                    </div>
                    <h5>{{ item.name }}</h5>
                    <span><b>{{ item.value }}</b>{{ item.unit }}</span>
                </div>
            </div>
            <button class="gift_pack_btn" @click="login"></button>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref,defineEmits,defineExpose } from 'vue';
import gift_pack1 from '@/assets/images/index_images/gift_pack1.svg'
import gift_pack2 from '@/assets/images/index_images/gift_pack2.svg'
import gift_pack3 from '@/assets/images/index_images/gift_pack3.svg'
import gift_pack4 from '@/assets/images/index_images/gift_pack4.svg'
import gift_pack5 from '@/assets/images/index_images/gift_pack5.svg'
import gift_pack6 from '@/assets/images/index_images/gift_pack6.svg'
let emit=defineEmits(['login'])
let dialogVisible=ref(false)
let close=()=>{
    dialogVisible.value=false
}
let login=()=>{
    dialogVisible.value=false
    emit('login')
}
let list=ref([
    {
        name:'精品音色',
        // value:"400000", 
        value:"2000",
        unit:'字符',
        img:gift_pack1,
        imgWidth:49,
        imgHeight:40,
        imgMarginBottom:18
    },
    {
        name:'甄享音色',
        // value:"400000",
        value:"2000",
        unit:'字符',
        img:gift_pack2,
        imgWidth:51,
        imgHeight:39,
        imgMarginBottom:18
    },
    {
        name:'至甄音色',
        value:"2000",
        unit:'字符快速试听',
        img:gift_pack3,
        imgWidth:50,
        imgHeight:38,
        imgMarginBottom:18
    },
    {
        name:'快速试听',
        value:"30000",
        unit:'字符',
        img:gift_pack4,
        imgWidth:48,
        imgHeight:48,
        imgMarginBottom:20
    },
    {
        name:'数字人视频合成',
        value:"30",
        unit:'秒',
        img:gift_pack5,
        imgWidth:56,
        imgHeight:56,
        imgMarginBottom:16
    },
    {
        name:'一键成片',
        value:"60",
        unit:'算粒',
        img:gift_pack6,
        imgWidth:42,
        imgHeight:42,
        imgMarginBottom:23
    },
])
defineExpose({
    dialogVisible
})
</script>
<style lang="scss">
.el-dialog{
    overflow-x: hidden;
    &.gift_pack_dialog{
        background-image: url('@/assets/images/index_images/gift_pack.svg');
        height: 608px;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 0 0;
        background-color: transparent;
        padding: 0;
        overflow: visible;
        box-shadow: none;
        .el-dialog__header{
            padding: 0;
            position: absolute;
            top: -31px;
            right: 0px;
            .gift_pack_close{
                width: 40px;
                height: 40px;
                cursor: pointer;
            }
            .el-dialog__headerbtn{
                display: none;
            }
        }
        .el-dialog__body{
            padding: 0;
            height: 608px; /* 和弹窗高度一致 */
            overflow: visible; /* 隐藏溢出 */
            position: relative; /* 绝对定位元素的参照 */
            .gift_pack_dialog_top{
                padding: 100px 260px 0 39px;
                width: 100%;
                box-sizing: border-box;
                display: flex;
                margin-bottom:19px;
                .gift_pack_dialog_top_title{
                    display: flex;
                    flex-direction: column;
                    .gift_pack_dialog_top_title_label{
                        width: 346px;
                        height: 46px;
                        margin-bottom: 3px;
                        margin-left: -4px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .gift_pack_dialog_top_title_describe{
                        display: flex;
                        align-items: center;
                        span{
                            font-weight: 400;
                            font-size: 20px;
                            line-height: 48px;
                            color: #191A15;
                            margin-right: 12px;
                            &:last-child{
                                margin-right: 0;
                            }
                        }
                    }
                }
                .gift_pack_dialog_top_tip{
                    margin-left: auto;
                    span{
                        padding-top: 9px;
                        font-weight: 400;
                        font-size: 17px;
                        line-height: 48px;
                        color: #666666;
                    }
                }
            }
            .gift_pack_dialog_content{
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                .gift_pack_dialog_content_item{
                    width: 261px;
                    height: 159px;
                    margin-right: 23px;
                    margin-bottom: 11px;
                    box-sizing: border-box;
                    background: radial-gradient(132.08% 132.08% at 50% 50%, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 100%);
                    border: 2px solid rgba(255, 255, 255, 0.41);
                    border-radius: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    .gift_pack_dialog_content_item_img{
                        line-height: 0;
                    }
                    h5{
                        margin: 0;
                        font-weight: normal;
                        font-weight: 600;
                        font-size: 22px;
                        line-height: 31px;
                        color: #12321B;
                    }
                    span{
                        font-size: 16px;
                        line-height: 22px;
                        color: #12321B;
                        b{
                            margin-right: 5px;
                            font-size: 23px;
                            line-height: 28px;
                            color: #DE25E0;
                            font-family: 'DingTalk JinBuTi';
                        }
                    }
                    &:nth-child(3n){
                        margin-right: 0;
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
            .gift_pack_btn{
                background-image: url('@/assets/images/index_images/gift_pack_btn.png');
                width: 342px;
                height: 100px;
                background-repeat: no-repeat;
                background-size: cover;
                background-position: 0 0;
                background-color: transparent;
                border: none;
                position: absolute;
                bottom: -68px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1;
                cursor: pointer;
                position: absolute;
            }
        }
        
    }
}

</style>