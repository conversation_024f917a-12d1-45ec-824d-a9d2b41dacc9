<template>
  <div class="headbar-container">
    <Action />
  </div>
</template>

<script setup>
// import Logo from '../logo/index.vue'
// import Crumb from './components/crumb/index.vue'
import Action from './components/action/index.vue'
// import Menu from './components/menu/index.vue'

// const menuStore = useMenuStore()
// const { collapse } = storeToRefs(menuStore)
//
// const themeStore = useThemeStore()
//
// const menuLayoutMode = computed(() => themeStore.layout.menuLayoutMode)

/**
 * @description: 菜单栏折叠事件
 * @param {*}
 * @return {*}
 * @author: gumingchen
 */
// const collapseHandle = () => {
//   collapse.value = !collapse.value
// }
</script>

<style lang="scss" scoped>
.headbar-container {
  z-index: 10;
  height: auto;
  min-height: 50px;
  background-color: #ffffff;
  box-shadow: none;
}
</style>
