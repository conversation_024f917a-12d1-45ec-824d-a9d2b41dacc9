import { ref, watch, computed } from 'vue'

export function useCheckWithin24h(loginStore) {
  const buildTime = ref('')
  const buyTime = ref('')

  const isWithin24Hours = (timeStr) => {
    if (!timeStr) return false
    const targetTime = new Date(timeStr)
    if (isNaN(targetTime.getTime())) return false
    const now = new Date()
    const diff = now.getTime() - targetTime.getTime()
    return diff >= 0 && diff <= 24 * 60 * 60 * 1000
    
  }

  // 监听 loginStore 里对应时间变化，实时更新 buildTime 和 buyTime
  watch(
    () => loginStore.memberInfo?.frist_build_tts,
    (val) => {
      buildTime.value = val || ''
    },
    { immediate: true,deep:true }
  )

  watch(
    () => loginStore.memberInfo?.frist_buy_subscription,
    (val) => {
      buyTime.value = val || ''
    },
    { immediate: true,deep:true  }
  )

  // 计算是否在24小时内，实时响应
  const isBuildWithin24h = computed(() => isWithin24Hours(buildTime.value))
  const isBuyWithin24h = computed(() => isWithin24Hours(buyTime.value))

  // 新增方法，根据规则返回状态码
const getStatus = () => {
  // 先判断数字人购买时间
if (loginStore?.memberInfo?.frist_buy_digi) {
  return 0;
}
  console.log(buildTime.value, buyTime.value, '时间判断')

  const hasBuild = !!buildTime.value && buildTime.value.trim() !== ''
  const hasBuy = !!buyTime.value && buyTime.value.trim() !== ''

  const buildIn24 = hasBuild && isWithin24Hours(buildTime.value)
  const buyIn24 = hasBuy && isWithin24Hours(buyTime.value)

  if (!hasBuild && !hasBuy) return 0 // 两个都无效或空串

  if (buildIn24 && buyIn24) return 2

  if (buildIn24 && !buyIn24) return 1

  if (!buildIn24 && buyIn24) return 2

  // 两个都有值但都不在24小时内，返回0
  return 0
}
  return {
    buildTime,
    buyTime,
    isBuildWithin24h,
    isBuyWithin24h,
    getStatus,
  }
}
