<template>
    <img src="@/assets/images/index_images/limit_time_dialog.svg" style="display:none" alt="预加载背景图" loading="eager"/>
    <el-dialog v-model="dialogVisible" class="limit_time_dialog" :class="type==1?'limit_time_synthesis_dialog':''" width="888px" :show-close="false" append-to="#app" :style="{ backgroundImage: `url(${bgImage})` }">
        <template #header>
            <div class="limit_time_dialog_close" @click="close">
                <img src="@/assets/images/index_images/limit_time_dialog_close.svg" alt="">
            </div>
        </template>
        <template #default>
                <div class="limit_time_dialog_title">
                    <div class="limit_time_dialog_title_img">
                        <img src="@/assets/images/index_images/limit_time_dialog_title_img.svg" alt="">
                    </div>
                    <div class="limit_time_dialog_title_info" >
                        <span v-if="type==1">恭喜您完成首次AI配音体验，获得专属限时优惠权益</span>
                        <span v-else style="margin-right: 13px;">恭喜你获赠<b>2</b>项福利</span>
                        <b>过期作废</b>
                    </div>
                </div>
                <div class="limit_time_dialog_content">
                    <div class="limit_time_dialog_content_time_label">
                        <el-button>仅限24小时</el-button>
                    </div>
                    <!--AI配音首次合成 9.9三天 -->
                    <!-- <template v-if="type==1">
                        <div class="limit_time_dialog_synthesis_content">
                            <div class="limit_time_dialog_synthesis_content_describe">
                                <h5>恭喜您完成首次AI配音体验，</h5>
                                <span>获得专属限时优惠权益！</span>
                            </div>
                            <div class="limit_time_dialog_synthesis_content_info">
                                <div class="limit_time_dialog_synthesis_content_info_price">
                                    ¥{{limit_time_obj.synthesis.price}}
                                </div>
                                <div class="limit_time_dialog_synthesis_content_info_benefit">
                                    {{limit_time_obj.synthesis.time}}天 SVIP
                                </div>
                            </div>
                            <div class="limit_time_dialog_synthesis_content_btns">
                                <el-button @click="receive(limit_time_obj.synthesis)">
                                    立即领取
                                </el-button>
                            </div>
                        </div>
                    </template> -->
                    <!--AI配音首次购买会员 2项福利 -->
                    <!-- <template v-else> -->
                        <div class="limit_time_dialog_buy_content">
                            <div class="limit_time_dialog_buy_content_list">
                                <div class="limit_time_dialog_buy_content_list_item">
                                    <div class="limit_time_dialog_buy_content_list_item_title">
                                        <h5>福利1</h5>
                                    </div>
                                    <div class="limit_time_dialog_buy_content_list_item_describe">
                                        <span>恭喜您成为我们的AI配音会员，同时获得数字人专属限时优惠权益！</span>
                                    </div>
                                    <div class="limit_time_dialog_buy_content_list_item_info">
                                        <div class="limit_time_dialog_buy_content_list_item_info_price">
                                            <!-- <img src="@/assets/images/index_images/limit_time_dialog_buy_content_list_item_info1.svg" style="width: 68px;" alt=""> -->
                                            ¥ <span>{{limit_time_obj.buy.discount.price}}</span>
                                        </div>
                                        <div class="limit_time_dialog_buy_content_list_item_info_text">
                                            <b>{{limit_time_obj.buy.discount.time}}</b>天数字人会员体验卡
                                        </div>
                                        <div class="limit_time_dialog_buy_content_list_item_info_btn">
                                            <!-- <img src="@/assets/images/index_images/limit_time_dialog_buy_content_list_item_info_btn.svg" alt=""> -->
                                            <el-button  @click="receive(limit_time_obj.buy.discount)">立即领取</el-button>
                                        </div>
                                    </div>
                                </div>
                                <div class="limit_time_dialog_buy_content_list_item">
                                    <div class="limit_time_dialog_buy_content_list_item_title">
                                        <h5>福利2</h5>
                                    </div>
                                    <div class="limit_time_dialog_buy_content_list_item_describe">
                                        <span>恭喜您获得限时折扣权益！</span>
                                    </div>
                                    <div class="limit_time_dialog_buy_content_list_item_info">
                                        <div class="limit_time_dialog_buy_content_list_item_info_price">
                                          95 <span>折</span> 
                                        </div>
                                        <div class="limit_time_dialog_buy_content_list_item_info_text">
                                            数字人会员
                                        </div>
                                        <div class="limit_time_dialog_buy_content_list_item_info_btn">
                                            <!-- <img src="@/assets/images/index_images/limit_time_dialog_buy_content_list_item_info_btn.svg" alt=""> -->
                                             <el-button @click="receive(limit_time_obj.buy.allowance)">立即领取</el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <!-- </template> -->
                </div>
                
           
        </template>
    </el-dialog>
</template>
<script setup>
import { ref, reactive, onMounted, watch,defineExpose} from 'vue'
import limit_time_dialog_buy_content_list_item_info1 from "@/assets/images/index_images/limit_time_dialog_buy_content_list_item_info1.svg"
import bgImage from '@/assets/images/index_images/limit_time_dialog.svg'
import { usePayStore } from '@/stores/modules/payStore.js' 
import {getPlanDetail,getDetail} from '@/api/account.js'
import { useloginStore } from '@/stores/login'
import { ElMessage } from "element-plus";
let loginStore = useloginStore()
let payStore= usePayStore()
let dialogVisible= ref(false)
let type=ref('')
let close=()=>{
    dialogVisible.value=false
}
let receive=(item)=>{
    payStore.setLimitedTime(item)
    let url = `${window.location.origin}/membership`
    if(item.type=='buy'&&item.benefit_type=='allowance'){
        url = `${url}?nav=digital`;
    }
	window.open(url, '_blank');
}
let limit_time_obj=ref({
    synthesis:{
        type:'synthesis',
        price:9.9,
        time:3
    },
    buy:{
        //优惠
        discount:{
            type:'buy',
            benefit_type:'discount',
            price:9.9,
            time:3,
            request_data:{}
        },
        // 折扣
        allowance:{
            type:'buy',
            benefit_type:'allowance',
            price:9.5,
            request_data:{}
        },
    }
})
let getPay=async()=>{
    let res={}
    if(type.value==1){
       res=await getPlanDetail({userId:loginStore.userId})
    }else{
       res=await getDetail({userId:loginStore.userId})
    }
    if(res.code==0){
        if(res.data){
             if(type.value==1){
                limit_time_obj.value.synthesis.price=res.data.discountPrice
                limit_time_obj.value.synthesis.time=res.data.cycle
                limit_time_obj.value.synthesis.request_data=res.data
             }else{
                limit_time_obj.value.buy.discount.price=res.data.discountPrice
                limit_time_obj.value.buy.discount.time=res.data.cycle
                limit_time_obj.value.buy.discount.request_data=res.data
             }
        }
    }else{
        ElMessage.error(res.msg)
    }
    
}
watch(()=>dialogVisible.value,(newVal,oldVal)=>{
    if(newVal){
        getPay()
    }
})
defineExpose({
    dialogVisible,
    type
})
</script>
<style lang="scss">
.el-dialog{
    &.limit_time_dialog{
        // background-image: url('@/assets/images/index_images/limit_time_dialog.svg');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: 0 0;
        width: 482px;
        height: 578px;
        background-color: transparent;
        padding: 0;
        overflow: visible;
        box-shadow: none;
        .el-dialog__header{
            padding: 0;
            position: relative;
           
            .limit_time_dialog_close{
                position: absolute;
                top:-20px;
                right: 0;
                width: 33px;
                height: 33px;
                cursor: pointer;
            }
        }
        .el-dialog__body{
            padding: 13px 24px 0 33px;
            overflow: hidden; 
                .limit_time_dialog_title{
                    display: flex;
                    flex-direction: column;
                    .limit_time_dialog_title_img{
                        margin-bottom: 10px;
                        width: 200px;
                        height: 60px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .limit_time_dialog_title_info{
                        display: flex;
                        align-items: center;
                        margin-bottom: 13px;
                     
                        span{
                            margin-right: 6px;
                            font-size: 15px;
                            line-height: 20px;
                            color: #12321B;
                            b{
                                font-size: 18px;
                                line-height: 20px;
                                color: #FF578D;
                                margin: 0 6px;
                            }
                        }
                        b{
                            font-size: 12px;
                            line-height: 20px;
                            color: #969696;
                            font-weight: 200;
                           
                        }
                    }
                }
                .limit_time_dialog_content{
                    padding-top: 8px;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    height: 369px;
                    .limit_time_dialog_content_time_label{
                        width: 135px;
                        height: 40px;
                        align-self: flex-end;
                        margin-top: -8px;
                        .el-button {
                            position: relative;
                            z-index: 0;
                            background: #D1FD4F;
                            border-radius: 12px;
                            border: none;
                            overflow: visible; // 允许伪元素溢出显示
                            color: #050B0D;
                            font-size: 22px;
                            line-height: 20px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100%;
                            width: 100%;

                            span {
                                font-size: 22px;
                                line-height: 20px;
                                color: #050B0D;
                                position: relative;
                                z-index: 3;
                            }

                            &::before {
                                content: "";
                                position: absolute;
                                top: 0px;    // 边框宽度
                                left: 0px;
                                right: 0px;
                                bottom: 0px;
                                border-radius: 12px; // 主元素12px + 2px边框宽度
                                background: linear-gradient(180deg, #6781F3 0%, #FFFFFF 100%);
                                z-index: 1;
                                width: 100%;
                                height: 100%;
                            }

                            &::after {
                                content: "";
                                position: absolute;
                                top: 1px;
                                left: 1px;
                                border-radius: 12px;
                                background: #D1FD4F;
                                z-index:2;
                                width: 133px;
                                height: 38px;
                            }
                            }
        
                      
                    }
                    // AI配音合成
                    .limit_time_dialog_synthesis_content{
                        flex: 1;
                        display: flex;
                        margin-top: 14px;
                        flex-direction: column;
                        align-items: center;
                        .limit_time_dialog_synthesis_content_describe{
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            text-align: center;
                            color: #FFFFFF;
                            font-weight: 100;
                            margin-bottom: 19px;
                            h5{
                                font-size: 27px;
                                line-height: 32px;
                                margin: 0;
                                font-weight: 100;
                            }
                            span{
                                font-size: 22px;
                                line-height: 32px;
                            }
                        }
                        .limit_time_dialog_synthesis_content_info{
                            background-image: url('@/assets/images/index_images/limit_time_dialog_synthesis_content_info_bg.svg');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                            background-position: 0 0;
                            display: flex;
                            align-items: center;
                            width: 388px;
                            height: 121px;
                            margin-bottom: 15px;
                            .limit_time_dialog_synthesis_content_info_price{
                                font-family: 'Inter';
                                font-style: normal;
                                font-weight: 700;
                                font-size: 48px;
                                line-height: 60px;
                                text-transform: uppercase;
                                color: #FF2467;
                                width: 140px;
                                text-align: center;
                            }
                            .limit_time_dialog_synthesis_content_info_benefit{
                                flex: 1;
                                text-align: center;
                                font-family: 'Inter';
                                font-style: normal;
                                font-weight: 500;
                                font-size: 40px;
                                line-height: 60px;
                                text-transform: uppercase;
                                color: #674300;
                            }
                        }
                        .limit_time_dialog_synthesis_content_btns{
                            width: 182px;
                            height: 49px;
                            .el-button{
                                background: linear-gradient(180deg, #FF588E 0%, #FF2165 100%), linear-gradient(180deg, #F77272 0%, #EA2828 100%);
                                border-radius: 100px;
                                width: 100%;
                                height: 100%;
                                padding: 0;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                span{
                                    font-family: 'Yuanti SC';
                                    font-style: normal;
                                    font-weight: 400;
                                    font-size: 28px;
                                    line-height: 49px;
                                    display: flex;
                                    align-items: center;
                                    text-align: center;
                                    text-transform: uppercase;
                                    color: #FFFFFF;
                                }
                            }
                        }
                    }
                    //AI配音购买
                    .limit_time_dialog_buy_content{
                        display: flex;
                        position: absolute;
                        left: 0;
                        top:0;
                        width: 100%;
                        height: 100%;
                        .limit_time_dialog_buy_content_list{
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            .limit_time_dialog_buy_content_list_item{
                                flex: 1;
                                padding: 24px 26px 0 25px;
                                .limit_time_dialog_buy_content_list_item_title{
                                    h5{
                                        font-style: normal;
                                        font-weight: 500;
                                        font-size: 14px;
                                        line-height: 14px;
                                        color: #D1FD4F;
                                        margin: 0;
                                        margin-bottom: 5px;
                                    }
                                }
                                .limit_time_dialog_buy_content_list_item_describe{
                                    margin-bottom: 8px;
                                    font-weight: 200;
                                    span{
                                        font-size: 16px;
                                        line-height: 23px;
                                        color: #E0E6FF;
                                        margin-bottom: 12px;
                                    }
                                }
                                .limit_time_dialog_buy_content_list_item_info{
                                    background-image: url('@/assets/images/index_images/limit_time_dialog_buy_content_list_item_bg.svg');
                                    background-repeat: no-repeat;
                                    background-size: 100% 100%;
                                    background-position: 0 0;
                                    display: flex;
                                    align-items: center;
                                    padding-right: 9px;
                                    width: 379px;
                                    height: 60px;
                                    .limit_time_dialog_buy_content_list_item_info_price{
                                        width: 97px;
                                        display: flex;
                                        justify-content: center;
                                        align-items: baseline;
                                        margin-right: 13px;
                                        font-weight: 400;
                                        img{
                                            height: 60px;
                                        }
                                    }
                                    .limit_time_dialog_buy_content_list_item_info_text{
                                        font-size: 14px;
                                        line-height: 60px;
                                        display: flex;
                                        align-items: flex-end;
                                        text-align: center;
                                        text-transform: uppercase;
                                        color: #333333;
                                       
                                        b{
                                            font-size: 19px;
                                            margin-right: 2px;
                                            display: inline-block;
                                            font-weight: normal;
                                        }
                                    }
                                    .limit_time_dialog_buy_content_list_item_info_btn{
                                        margin-left: auto;
                                        width: 72px;
                                        height: 27px;
                                        box-sizing: border-box;
                                        // img{
                                        //     width: 100%;
                                        //     height: 100%;
                                        // }
                                        .el-button{
                                            background: linear-gradient(180deg, #FF588E 0%, #FF2165 100%), linear-gradient(180deg, #F77272 0%, #EA2828 100%);
                                            border-radius: 100px;
                                            width: 100%;
                                            height: 100%;
                                            padding:7px 8px;
                                            span{
                                                font-size: 14px;
                                                line-height: 14px;
                                                display: flex;
                                                align-items: center;
                                                text-align: center;
                                                text-transform: uppercase;
                                                color: #FFFFFF;
                                            }
                                            &:active{
                                                border: none;
                                            }
                                        }
                                    }   
                                }
                                &:first-child{
                                    padding-top: 33px;
                                    .limit_time_dialog_buy_content_list_item_info{
                                        .limit_time_dialog_buy_content_list_item_info_price{
                                            font-family: 'Yuanti SC';
                                            font-style: normal;
                                            font-weight: 400;
                                            font-size: 22px;
                                            line-height: 60px;
                                            text-align: center;
                                            text-transform: uppercase;
                                            color: #FFFFFF;
                                            span{
                                                font-size: 39px;
                                            }
                                        }
                                    }
                                }
                                &:nth-child(2){
                                    .limit_time_dialog_buy_content_list_item_info{
                                        .limit_time_dialog_buy_content_list_item_info_price{
                                            font-family: 'Yuanti SC';
                                            font-style: normal;
                                            font-weight: 400;
                                            font-size: 39px;
                                            line-height: 60px;
                                            text-align: center;
                                            text-transform: uppercase;
                                            color: #FFFFFF;
                                            span{
                                                font-size: 22px;
                                            }
                                        }
                                    }
                                }   
                            }
                        }
                    }

                }
                
        }
        &.limit_time_synthesis_dialog{
            background-image: url('@/assets/images/index_images/limit_time_synthesis_bg.svg');
            box-shadow: none;
        }
        
    }
}

</style>