<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const handleLogoClick = () => {
  router.push('/H4Home')
}

// const props = defineProps({
//   collapse: {
//     type: Boolean,
//     default: () => true
//   }
// })

// const adminerStore = useAdminerStore()
//
// const { tenant } = storeToRefs(adminerStore)
</script>

<template>
  <div class="logo-container padding-n-10 flex flex_j_c-center">
    <img src="/img/tobu.png" alt="logo" srcset="" class="logo_image" @click="handleLogoClick">
<!--    v-if="tenant.name || tenant.logo">-->
<!--    <transition name="el-fade-in" mode="out-in">-->
<!--      <div class="flex flex-item_f-1 flex_j_c-center flex_a_i-center" v-if="!collapse">-->
<!--        <el-image class="width-34 height-34" :src="tenant.logo" />-->
<!--        <div class="margin_l-10 ellipse">-->
<!--          {{ tenant.name }}-->
<!--        </div>-->
<!--      </div>-->
<!--      <div class="flex flex-item_f-1 flex_j_c-center flex_a_i-center" v-else>-->
<!--        <el-image class="width-34 height-34" :src="tenant.logo" v-if="tenant.logo" />-->
<!--        <div class="margin_l-10 ellipse" v-else>-->
<!--          {{ tenant.name }}-->
<!--        </div>-->
<!--      </div>-->
<!--    </transition>-->
  </div>
</template>

<style lang="scss" scoped>
.logo-container {
  //height: var(--gl-headbar-height);
  //line-height: var(--gl-headbar-height);
  height:50px;
  line-height:50px;
  background-color: #ffffff;
  //background-color: #006eff;
  transition: width 0.4s;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 45px;
  .logo_image{
    height:32px;
    cursor: pointer;
  }


}
</style>
