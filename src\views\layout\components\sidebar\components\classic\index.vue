<script setup>
import SubMenu from '../../../sub-menu/index.vue'
import Logo from '../../../logo/index.vue'
import { useMenuStore } from "@/stores/index.js";
import { storeToRefs } from 'pinia'
import { watchEffect, onMounted,onActivated } from 'vue'
import { useRoute } from 'vue-router'
//引入路由js
import { staticRouter, errorRouter } from "@/router/modules/staticRouter";
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
const menuStore = useMenuStore()
// console.log('menuStore',menuStore)
// const themeStore = useThemeStore()
// const menuTheme = computed(() => themeStore.color.menu)
const { active } = storeToRefs(menuStore)
// console.log('displayedMenus',displayedMenus)

const route = useRoute()
const routeHandle = argRoute => {
	const name = argRoute.name
	console.log('当前菜单激活项:', name)
	active.value = name || 'home' // 如果name为空，默认为home
}
let income_show=()=>{
	let originalChildren = [...staticRouter[0].children];
	console.log(loginStore.memberInfo,'loginStore.memberInfo');
	const show_income = loginStore?.memberInfo?.income_need;

	if (show_income) {
		staticRouter[0].children = [...originalChildren];
	} else {
		staticRouter[0].children = originalChildren.filter(item => item.titleName !== '我的收益');
	}
console.log(staticRouter[0].children, originalChildren,'staticRouter[0].children');

	return staticRouter[0].children
	
}
// 组件挂载时立即设置激活菜单
onMounted(() => {
	income_show()
	// 确保active有值，如果没有则设为默认值'home'
	if (!active.value) {
		active.value = 'home'
	}
	console.log('初始化菜单active值:', active.value)
})

watchEffect(() => {
	routeHandle(route)
})

</script>
<template>
	<div class="sidebar-classic-container flex flex_d-column">
		<!--  logo以及名称  -->
		<Logo />
		<!--    <div class="flex-item_f-1"></div>-->
		<el-scrollbar class="flex-item_f-1">
			<el-menu :default-active="active" style="width:220px;" :background-color="'#FAFAFA'"
				text-color="#414244" active-text-color="#fff" :unique-opened="false">
				<SubMenu v-for="(item, index) in income_show()" :index="String(index)" :key="item.id"
					:data="item" />
			</el-menu>
		</el-scrollbar>
	</div>
</template>

<style lang="scss" scoped>
.sidebar-classic-container {
	// z-index: 10; // todo: 忘记当初为什么会加这个属性
	//background-color: var(--gl-sidebar-background-color);
	background-color: #FAFAFA;

	// box-shadow: var(--el-box-shadow-light);
	.el-menu:not(.el-menu--collapse) {
		width: var(--gl-sidebar-classic-width); // todo: 侧边栏的宽度
	}

	.el-menu {
		border: none;
		padding: 0 20px;

		::v-deep(.el-menu-item) {
			border-radius: 10px;
			height: 40px;
			margin-bottom: 8px;
		}

		::v-deep(.el-menu-item.is-active) {
			background-color: #0AAF60 !important;
			/* 修改激活项的背景色 */
			color: #FFFFFF !important;
			/* 修改激活项的文本颜色 */
			height: 40px;
		}

		::v-deep(.el-menu-item:hover) {
			background-color: #0AAF60;
			color: #FFFFFF;
		}

		::v-deep(.el-menu-item),
		::v-deep(.el-sub-menu),
		::v-deep(.el-sub-menu__title) {
			//  //display: block;
			//  //overflow: hidden;
			//  //text-overflow:ellipsis;
			//  //white-space: nowrap;
			//
			//  background-color: var(--main-color) !important; /* 修改激活项的背景色 */
			// color: #5d5d5f !important;
			/* 修改激活项的文本颜色 */
			//color: #f00 !important; /* 修改激活项的文本颜色 */
			//height:40px;
			border-radius: 10px;
			margin-bottom: 8px;

			&>.el-sub-menu__icon-arrow {
				position: absolute;
				right: 8px;
			}
		}

		::v-deep(.el-sub-menu__title):hover {
			background-color: #0AAF60;
			color: #FFFFFF;
		}
	}
}

/* 调整sidebar菜单样式 */
:deep(.el-sub-menu__title) {
	height: 40px !important;
	line-height: 40px !important;
}

:deep(.el-menu-item) {
	height: 40px !important;
	line-height: 40px !important;
}

/* 调整图标垂直对齐 */
:deep(.el-icon) {
	margin-top: -2px;
	vertical-align: middle;
}
</style>
