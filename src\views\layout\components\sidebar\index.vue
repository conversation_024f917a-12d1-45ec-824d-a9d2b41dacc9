<script setup>
import Classic from './components/classic/index.vue'
import { storeToRefs } from 'pinia'
import {watchEffect, onMounted} from 'vue'
import { useRoute } from 'vue-router'
// import Subfield from './components/subfield/index.vue'
import { useMenuStore } from '@/stores/index.js'
const route = useRoute()
const menuStore = useMenuStore()
// const themeStore = useThemeStore()
// const menuLayoutMode = computed(() => themeStore.layout.menuLayoutMode)
//
const { active } = storeToRefs(menuStore)
// 判断路由菜单样式以哪种模式从呈现出来
// const component = computed(() => {
//   let result = ''
//   switch (menuLayoutMode.value) {
//     case 1:
//       result = Classic
//       break
//     case 2:
//       result = Subfield
//       break
//   }
//
//   return result
// })
// console.log('888',component.value)
/**
 * @description: 路由变化事件
 * @param {*}
 * @return {*}
 * @author: gumingchen
 */
const routeHandle = argRoute => {
  const name = argRoute.name
  console.log('当前路由名称:', name)
  active.value = name || 'home' // 如果name为空，默认设置为home
}

// 组件挂载时立即设置激活菜单
onMounted(() => {
  // 确保立即执行一次路由处理，设置默认激活菜单
  routeHandle(route)
})

watchEffect(() => {
  routeHandle(route)
})
</script>

<template>
  <component :is="Classic" />
</template>

<style lang="scss" scoped>

</style>
