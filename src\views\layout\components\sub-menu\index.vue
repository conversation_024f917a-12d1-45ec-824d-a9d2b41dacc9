<script setup>
import SubTtem from './index.vue'
import { useRouter, useRoute } from 'vue-router'
import { MenuType } from '@/common/enums'
import { computed, getCurrentInstance, ref, onMounted, watch } from 'vue'
import { useMenuStore } from "@/stores/index.js";
import { usePreviewStore } from "@/stores/previewStore";
import { useMusicStore } from "@/stores/modules/musicStore";
import { useUmeng } from '@/utils/umeng/hook'; // 导入友盟埋点

const { proxy } = getCurrentInstance();
const menuStore = useMenuStore();
const active = computed(() => menuStore.active);
// 初始化埋点
const umeng = useUmeng();

const route = useRoute();
const router = useRouter();

const props = defineProps({
	data: {
		type: Object,
		required: true
	}
})

// 判断当前菜单项是否激活
const isActive = computed(() => {
	return active.value === props.data.name;
});

// 鼠标悬停状态
const isHovered = ref(false);

// console.log('pppppp',props.data)

// 判断是否是Element Plus图标
const isElementIcon = computed(() => {
	if (!props.data.icon) return false
	// Element Plus图标通常首字母大写，如Money, Home等
	return /^[A-Z]/.test(props.data.icon)
})

// 判断是否是本地图片
const isLocalImage = computed(() => {
	if (!props.data.icon) return false
	// 检查是否包含图片文件扩展名
	return /\.(png|jpg|jpeg|gif|svg)$/i.test(props.data.icon)
})

// 生成悬停时的图片路径
const getHoverIconPath = (originalPath) => {
	if (!originalPath || !isLocalImage.value) return originalPath;
	
	// 创建图片映射关系对象
	const hoverImageMap = {
		'gongzuotai.png': 'Home (首页)1.png',        // 工作台
		'Vector1.png': 'Home (首页)2.png',             // AI工具
		'People1.png': 'Home (首页)3.png',             // 我的空间
		'Application_(应用).png': 'Home (首页)4.png',   // 音色商店
		'Diamonds_(钻石).png': 'Home (首页)5.png',      // 会员计划
		'API1.png': 'API2.png'                         // API服务
	};
	
	// 从完整路径中提取文件名
	const fileName = originalPath.split('/').pop();
	
	// 检查是否有对应的悬停图片
	if (hoverImageMap[fileName]) {
		// 替换文件名，保持路径结构
		return originalPath.replace(fileName, hoverImageMap[fileName]);
	}
	
	// 如果没有特定映射，使用通用规则：在文件扩展名前添加1
	const lastDotIndex = originalPath.lastIndexOf('.');
	if (lastDotIndex > 0) {
		const nameWithoutExt = originalPath.substring(0, lastDotIndex);
		const extension = originalPath.substring(lastDotIndex);
		return `${nameWithoutExt}1${extension}`;
	}
	
	return originalPath;
};

// 动态图标路径计算属性
const currentIcon = computed(() => {
	if (!props.data.icon || !isLocalImage.value) return props.data.icon;
	
	// 如果鼠标悬停或菜单项处于激活状态，返回对应的图片路径
	if (isHovered.value || isActive.value) {
		return getHoverIconPath(props.data.icon);
	}
	
	// 否则返回原始图片路径
	return props.data.icon;
});

// 鼠标进入事件处理
const handleMouseEnter = () => {
	isHovered.value = true;
};

// 鼠标离开事件处理
const handleMouseLeave = () => {
	isHovered.value = false;
};

// 判断用户是否已登录
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;

	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

/**
 * 菜单点击事件
 * @param name 路由名称
 */
const clickHandle = (e) => {
	console.log('点击菜单项:', props.data.name)

	// 添加埋点代码
	umeng.trackEvent(
		'页面导航', 
		'点击导航菜单', 
		`${props.data.titleName || props.data.name || '未知菜单'}`, 
		''
	);

	// 检查是否是"我的空间"相关的菜单项或"我的收益"
	const isMySpaceRelated = props.data.name && (
		props.data.name === 'mySpace' ||
		props.data.name === 'myWorks' ||
		props.data.name === 'myMaterials' ||
		props.data.name === 'myCustomizations' ||
		props.data.name === 'income'||  // 添加"我的收益"
		props.data.name === 'DigitalHumanEditor'
		// props.data.name === 'membership-nav'//会员计划
	);

	// 如果是"我的空间"相关菜单或"我的收益"且用户未登录，则弹出登录弹窗
	if (isMySpaceRelated && !checkUserLogin()) {
		proxy.$modal.open('组合式标题');
		return;
	}
	
	// 设置当前激活的菜单项
	menuStore.active = props.data.name;
	
	// 一键成片特殊处理：清空预览数据后再跳转
	if (props.data.name === 'Editor') {
		const previewStore = usePreviewStore();
		const musicStore = useMusicStore();
		
		console.log('清空Pinia预览数据');
		
		// 清空预览标题
		previewStore.setTitle('');
		// 清空预览内容
		previewStore.setContent('');
		// 清空选中角色
		previewStore.setRole(null);
		// 清空视频列表和选中的视频ID
		previewStore.setVideoList([]);
		previewStore.setSelectedVideoIds([]);
		// 清空已提取内容和时间线标记
		previewStore.clearExtractedContent();
		previewStore.setIsTimelineContent(false);
		// 清空当前视频URL和时间戳
		previewStore.setCurrentVideoUrl('');
		previewStore.setVideoTimestamps([]);
		
		// 清空音乐背景
		musicStore.clearAll();
		
		// 跳转到一键成片页面
		router.replace({ name: 'Editor', query: { refresh: 'true' } });
		return;
	}
	
	// 专业云剪特殊处理：添加token
	if (props.data.name === 'CloudEdit' && props.data.type === MenuType.URL) {
		let token = '';
		try {
			const userStr = localStorage.getItem('user');
			if (userStr) {
				const userObj = JSON.parse(userStr);
				token = userObj.token || '';
			}
		} catch (error) {
			console.error('获取token失败:', error);
		}
		
		// 使用window.open打开新页面，带token参数
		window.open(`${props.data.url}?token=${token}`, '_blank');
		return;
	}
	
	// 其他菜单项的正常处理逻辑
	switch (props.data.type) {
		case MenuType.URL: // 外链
			if (props.data.url.startsWith('http')) {
				// 如果是完整的http URL，使用window.open打开
				window.open(props.data.url)
			} else {
				// 如果是内部路径，使用路由导航
				router.push(props.data.url)
			}
			break
		case MenuType.ROUTER: // 路由
		case MenuType.MENU: // 菜单
		case MenuType.IFRAME: // iframe
			router.push({ name: props.data.name })
			break
	}
}

// 组件挂载时，检查是否需要高亮工作台
onMounted(() => {
	// 确保悬停状态正确初始化为false
	isHovered.value = false;
	
	// 如果当前是首页/工作台，主动设置高亮
	if (props.data.name === 'home') {
		console.log('工作台菜单项已加载')
	}
});

// 监听路由变化，确保悬停状态正确重置
watch(() => route.path, () => {
	// 路由切换时重置悬停状态
	isHovered.value = false;
}, { immediate: true });

const handleSubMenuClick = (e) => {
	// 只处理工作台主菜单点击
	if (props.data.name === 'home') {
		// 阻止默认的下拉行为
		e.preventDefault();
		e.stopPropagation();
		router.push({ name: 'home' });
	}
	// 其他菜单项不做特殊处理，让默认的下拉行为正常执行
};
</script>

<template>
	<el-menu-item-group title="Group One" v-if="data.type === 5" :index="data.name || data.id + ''">
		<template #title>
			<!--      <Iconfont :name="data.icon" class="padding_r-5" v-if="data.icon" />-->
			<span>{{ data.titleName }}</span>
		</template>
		<SubTtem v-for="item in data.children" :key="item.id" :data="item" />
	</el-menu-item-group>
	<el-sub-menu 
		v-else-if="data.children && data.children.length > 0" 
		:index="data.name || data.id + ''"
		@mouseenter="handleMouseEnter"
		@mouseleave="handleMouseLeave"
	>
		<template #title @click="handleSubMenuClick">
			<el-icon size="15" color="#409EFC" v-if="data.icon">
				<!-- 使用Element Plus图标 -->
				<component :is="data.icon" v-if="isElementIcon && data.icon" />
				<!-- 使用本地图片 -->
				<img 
					v-else-if="isLocalImage" 
					:src="currentIcon" 
					:alt="data.titleName" 
					class="menu-local-icon"
				/>
				<!-- 使用自定义Iconfont图标 -->
				<Iconfont :name="data.icon" class="padding_r-5" v-else-if="data.icon" />
			</el-icon>
			<span>{{ data.titleName }}</span>
		</template>
		<SubTtem v-for="item in data.children" :key="item.id" :data="item" />
	</el-sub-menu>
	<el-menu-item 
		v-else
		:index="data.name || data.id + ''" 
		@click="clickHandle" 
		:class="{'is-active': isActive}"
		@mouseenter="handleMouseEnter"
		@mouseleave="handleMouseLeave"
	>
		<!--    <Iconfont :name="data.icon" class="padding_r-5" v-if="data.icon" />-->
		<el-icon size="15" color="#409EFC">
			<!-- 使用Element Plus图标 -->
			<component :is="data.icon" v-if="isElementIcon && data.icon" />
			<!-- 使用本地图片 -->
			<img 
				v-else-if="isLocalImage" 
				:src="currentIcon" 
				:alt="data.titleName" 
				class="menu-local-icon"
			/>
			<!-- 使用自定义Iconfont图标 -->
			<Iconfont :name="data.icon" class="padding_r-5" v-else-if="data.icon" />
		</el-icon>
		<template #title>
			<span>{{ data.titleName }}</span>
		</template>
	</el-menu-item>
</template>

<style lang="scss" scoped>
/* 确保局部样式也保持一致的高度 */
:deep(.el-sub-menu__title) {
  height: 40px !important;
  line-height: 40px !important;
  cursor: pointer;
}

:deep(.el-menu-item) {
  height: 40px !important;
  line-height: 40px !important;
}

/* 调整图标垂直对齐 */
:deep(.el-icon) {
  margin-top: -2px;
  vertical-align: middle;
}

/* 本地图片图标样式 - 添加平滑过渡效果 */
.menu-local-icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
  display: block;
  transition: all 0.3s ease; /* 添加过渡动画，使图片切换更平滑 */
  cursor: pointer; /* 鼠标指针样式 */
}

/* 悬停效果增强 */
.menu-local-icon:hover {
  transform: scale(1.1); /* 悬停时轻微放大效果 */
}

/* 确保激活状态有明显的样式 */
:deep(.el-menu-item.is-active) {
  background-color: var(--main-color) !important;
  color: #5d5d5f !important;
}

/* 确保标题部分完全可点击 */
:deep(.el-sub-menu__title) {
  > * {
    pointer-events: none;
  }
}
</style>
