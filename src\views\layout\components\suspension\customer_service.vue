<template>
    <div class="customer_service">
        <span class="customer_service_title">
            微信客服
        </span>
        <div class="customer_service_qrcode">
            <img src="@/assets/img/duanjunyi.png" alt="">
        </div>
        <span class="customer_service_tip">添加客服，随时答疑</span>
    </div>
</template>
<script setup>
import { reactive, ref, defineExpose, onMounted } from "vue"
// import customerService from "@/assets/images/index_images/customer_service.png"
let qrcode = ref('')
// onMounted(() => {
//     qrcode.value = customerService
// })
defineExpose({
    qrcode
})
</script>
<style lang="scss">
.customer_service {
    padding: 10px 21px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .customer_service_title {
        line-height: 20px;
        font-size: 16px;
        color: #000;
        margin-bottom: 11px;
        display: inline-block;
        font-weight: 500;
    }

    .customer_service_qrcode {
        width: 118px;
        height: 118px;
        margin-bottom: 11px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .customer_service_tip {
        font-size: 12px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.45);

    }

}
</style>