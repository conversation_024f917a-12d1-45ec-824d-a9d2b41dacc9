<template>
  <div
    class="suspension_tool"
    :style="{ top: position.y, left: position.x }"
    @mousedown="startDrag"
  >
    <div class="suspension_tool_item">
      <el-popover
        trigger="hover"
        :placement="popoverPlacement"
        :offset="30"
        popper-class="suspension_popover"
        width="188px"
        ref="popoverRef"
      >
        <template #default>
          <customerService></customerService>
        </template>
        <template #reference>
          <img :src="contact" class="contact-image" alt="" />
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import contact from "@/assets/images/index_images/contact.png";
import customerService from "./customer_service.vue";

const DESIGN_WIDTH = 1920;
const DESIGN_HEIGHT = 953;
const TOOL_WIDTH = 68; // 悬浮按钮宽度
const TOOL_HEIGHT = 68; // 悬浮按钮高度

let position = ref({ x: "1850px", y: (DESIGN_HEIGHT / 2) + "px" }); // 默认悬浮位置
let isDragging = ref(false);
let offset = ref({ x: 0, y: 0 });
let popoverRef = ref(null);

// 判断是否是MacBook（苹果笔记本）
function isMacBook() {
  const isMac = navigator.platform === "MacIntel";
  const dpr = window.devicePixelRatio || 1;
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;

  const macBookScreenSizes = [
    { width: 1280, height: 800 },
    { width: 1440, height: 900 },
    { width: 2560, height: 1600 },
    { width: 2880, height: 1800 },
    { width: 3072, height: 1920 },
  ];

  const tolerance = 10;
  const matchScreenSize = macBookScreenSizes.some(
    (size) =>
      Math.abs(screenWidth - size.width) <= tolerance &&
      Math.abs(screenHeight - size.height) <= tolerance
  );

  return isMac && dpr >= 2 && matchScreenSize;
}

// 计算 popover 的位置
const popoverPlacement = computed(() => {
  const xNum = parseFloat(position.value.x);
   let xPercentage =''
  if(window.innerWidth>1920){
   xPercentage = (xNum / window.innerWidth) * 100;
  }else{
   xPercentage = (xNum / DESIGN_WIDTH) * 100;
  } 

  return xPercentage < 50 ? "right" : "left";
});

let startDrag = (event) => {
  event.preventDefault();
  isDragging.value = true;
  const targetRect = event.currentTarget.getBoundingClientRect();


  if (isMacBook()) {
    offset.value.x = event.clientX - targetRect.left;
    offset.value.y = event.clientY - targetRect.top;
  } else {
    offset.value.x = event.clientX - (targetRect.left + window.scrollX);
    offset.value.y = event.clientY - (targetRect.top + window.scrollY);
  }

  document.addEventListener("mousemove", onDrag);
  document.addEventListener("mouseup", stopDrag);
  window.addEventListener("mouseleave", stopDrag);
  window.addEventListener("blur", stopDrag)
};

let onDrag = (event) => {
  if (!isDragging.value) return;

  let newX, newY;

  if (isMacBook()) {
    newX = event.clientX - offset.value.x;
    newY = event.clientY - offset.value.y;

    // 边界限制，防止拖出屏幕
    newX = Math.min(
      Math.max(0, newX),
      window.innerWidth - TOOL_WIDTH
    );
    newY = Math.min(
      Math.max(0, newY),
      window.innerHeight - TOOL_HEIGHT
    );


    position.value.x = `${newX}px`;
    position.value.y = `${newY}px`;
  } else {
    let scaledX =''
    let scaledY
    // 先计算设计稿坐标
    if(window.innerWidth>1920){
      scaledX = (event.clientX - offset.value.x)
      scaledY = (event.clientY - offset.value.y) 
    }else{
      scaledX = (event.clientX - offset.value.x) * (DESIGN_WIDTH / window.innerWidth);
      scaledY = (event.clientY - offset.value.y) * (DESIGN_HEIGHT / window.innerHeight);
    }


    // 边界限制，防止拖出设计稿范围
     if(window.innerWidth>1920){
      // scaledX = Math.min(Math.max(0, scaledX), window.innerWidth - TOOL_WIDTH);
      // scaledY = Math.min(Math.max(0, scaledY), window.innerHeight - TOOL_HEIGHT);
    }else{
      scaledX = Math.min(Math.max(0, scaledX), DESIGN_WIDTH - TOOL_WIDTH);
      scaledY = Math.min(Math.max(0, scaledY), DESIGN_HEIGHT - TOOL_HEIGHT);
    }
console.log(event,offset.value,window.innerWidth>1920,window.innerWidth/DESIGN_WIDTH,scaledX,scaledY,'设置2');
    position.value.x = `${scaledX}px`;
    position.value.y = `${scaledY}px`;
  }

  nextTick(() => {
    if (popoverRef.value && popoverRef.value.updatePopper) {
      popoverRef.value.updatePopper();
    }
  });
};
let adjustPositionWithinBounds=async()=>{
  
  
  await nextTick()
  console.log(position.value.x,window.innerWidth-TOOL_WIDTH,position.value.x>(window.innerWidth-TOOL_WIDTH),'adjustPositionWithinBounds');
  if(parseFloat(position.value.x) >(window.innerWidth-TOOL_WIDTH)){
     position.value.x=window.innerWidth-TOOL_WIDTH+'px'
  }
  if(parseFloat(position.value.y)>window.innerHeight-TOOL_HEIGHT){
     position.value.y=window.innerHeight-TOOL_HEIGHT+'px'
  }

}
let stopDrag = () => {
  if (!isDragging.value) return; // 防止重复调用
  console.log('stopDrag');
  
  isDragging.value = false;
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
  window.removeEventListener("mouseleave", stopDrag);
  window.removeEventListener("blur", stopDrag)
};

onMounted(() => {
  const vw = window.innerWidth;
  const vh = window.innerHeight;

  // 初始x坐标，设计稿1920px，转换为视口坐标时可能超出
  let initX = 1920;
  let initY = DESIGN_HEIGHT / 2;
console.log(isMacBook(),'isMacBook');
  // 如果是MacBook，直接用视口坐标调整初始位置
  if (isMacBook()) {
    if (vw > 1920) {
      initX = 1920;
      if (initX + TOOL_WIDTH > vw) {
        initX = vw - TOOL_WIDTH - 10;
      }
      initY = vh / 2;
      position.value.x = `${initX}px`;
      position.value.y = `${initY}px`
    }else{
      // 如果初始位置超出视口宽度，调整到视口右边缘减去按钮宽度和边距
      if (initX + TOOL_WIDTH > vw) {
        initX = vw - TOOL_WIDTH - 10; // 10px边距
      }
      // y轴居中
      initY = vh / 2;
      position.value.x = `${initX}px`;
      position.value.y = `${initY}px`;
    }
    
  } else {
    if (vw > 1920) {
      // 非Mac且宽度大于1920的逻辑
      // 这里可以选择固定initX为1920，或者靠右边界
      initX = 1920;
      if (initX  < vw) {
        initX = vw - TOOL_WIDTH - 10;
      }
      initY = vh / 2;
      position.value.x = `${initX}px`;
      position.value.y = `${initY}px`;
    }
    // // 其他设备，按设计稿比例缩放到视口
    // let scaledX = (initX / DESIGN_WIDTH) * vw;
    // let scaledY = (initY / DESIGN_HEIGHT) * vh;

    // // 边界限制
    // scaledX = Math.min(scaledX, vw - TOOL_WIDTH - 10);
    // scaledY = Math.min(Math.max(0, scaledY), vh - TOOL_HEIGHT - 10);

    // position.value.x = `${scaledX}px`;
    // position.value.y = `${scaledY}px`;
  }

  console.log("当前设备是否MacBook:", isMacBook());
  console.log("窗口宽高:", vw, vh);
  window.addEventListener("resize", adjustPositionWithinBounds);
});
</script>

<style lang="scss">
.suspension_tool {
  position: fixed;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: grab;
  z-index: 200;
  box-shadow: 0px 0px 16px 0px rgba(6, 79, 13, 0.1);
  color: #000;

  .suspension_tool_item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 68px;
    height: 68px;
    background-color: #fff;
    border-radius: 4px 0 0 4px;
    cursor: pointer;

    img {
      width: 29px;
      height: 29px;
    }
  }

  &:active {
    cursor: grabbing; /* 拖动时显示抓取光标 */
  }
}

.suspension_popover {
  &.is-light {
    &.el-popover {
      border-radius: 8px;
      padding: 0;
      box-sizing: border-box;
    }
  }
}
</style>
