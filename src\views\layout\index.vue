<template>
  <div class="layout-container height-full flex overflow-auto">




    <Sidebar />
    <div class="navigation-container flex-item_f-1 flex flex_d-column overflow-auto">
      <Headbar  />
      <router-view v-slot="{ Component }">
        <component :is="Component" class="flex-item_f-1" />
      </router-view>
    </div>

    

<!--        <component :is="component" class="navigation-container">-->
<!--          <template #headbar>-->
<!--            <Headbar />-->
<!--          </template>-->
<!--          <template #tabsbar v-if="showTabs">-->
<!--            <Tabsbar />-->
<!--          </template>-->
<!--          <template #default v-if="!refresh">-->
<!--            <View class="margin-20 flex-item_f-1" transition="left-in-right-out" rename />-->
<!--          </template>-->
<!--        </component>-->
    <!--    <Websocket />-->
  </div>
</template>

<script setup>




import Sidebar from './components/sidebar/index.vue'
import Headbar from './components/headbar/index.vue'
// import Tabsbar from './components/tabsbar/index.vue'
// import NavigationActive from './components/navigation/active/index.vue'
// import NavigationFixed from './components/navigation/fixed/index.vue'
// import Websocket from './components/websocket/index.vue'
//
// const themeStore = useThemeStore()
//
// const navigationMode = computed(() => themeStore.layout.navigationMode)
// const showTabs = computed(() => {
//   return themeStore.layout.showTabs
// })
// const refresh = computed(() => {
//   return themeStore.refresh
// })
// const menuLayoutMode = computed(() => themeStore.layout.menuLayoutMode)
//
// const component = computed(() => {
//   let result = ''
//   switch (navigationMode.value) {
//     case 1:
//       result = NavigationFixed
//       break
//     case 2:
//       result = NavigationActive
//       break
//   }
//   return result
// })
</script>

<style lang="scss">
.layout-container {
  z-index: 0;
  //background-color: var(--gl-content-background-color);
  background-color: #fff;
  .navigation-container {
    z-index: 0;
    display: flex;
    flex-direction: column;
    box-shadow: none; /* 确保没有阴影 */
    background-color: #FAFAFA;
    /* 确保headbar下方内容能填充剩余空间 */
    & > *:not(.headbar-container) {
      flex: 1;
    }
  }
}
</style>
