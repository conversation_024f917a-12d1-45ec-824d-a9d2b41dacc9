<template>
    <el-dialog class="ai_copywriting_create_dialog"  v-model="dialogDetailVisible" width="760px" append-to="#app" @close="close" :show-close="false">
         <template #header>
            <img src="@/assets/images/aiImages/ai_copywriting_tempalte_more_header_close.svg" class="ai_copywriting_create_dialog_close" @click="close" alt="">
         </template>
        <el-scrollbar  ref="messageList"  v-loading="loading ? loadingOptions : false" >
            <div v-if="isThinking||isRefreshing" class="ai_copywriting_create_thinking">
                <div class="ai_copywriting_create_thinking_text">
                    <span>💭</span>
                    <div class="ai_copywriting_create_thinking_wave">
                        <template v-if="isThinking">
                          正在思考中
                        </template>
                        <template v-else>
                          重新生成中...
                        </template>
                    </div>
                </div>
			</div>
            <div v-else>
                <div class="ai_copywriting_create_list" ref="ai_copywriting_create_list">
                    <div class="ai_copywriting_create_list_item" :class="msg.isUser?'user':''" v-for="(msg, index) in messages" :key="index" v-html="renderMarkdown(msg.isTyping ? msg.displayContent : msg.content)" @contextmenu="(e) => copyWithContext(index, e)"></div>
                </div>
                
            </div>
        </el-scrollbar>
        <div class="ai_copywriting_create_bottom">
            <div class="ai_copywriting_create_bottom_symbol">
                <img src="@/assets/images/aiImages/ai_copywriting_textarea_img.png" class="ai_copywriting_create_bottom_symbol_img" alt="">
                <img src="@/assets/images/aiImages/ai_copywriting_creating.png" style="width: 81px;" v-if="isThinking||isRefreshing||createing"  class="ai_copywriting_create_bottom_symbol_label" alt="">
                <img src="@/assets/images/aiImages/ai_copywriting_created.png"  style="width: 64px;"  v-else class="ai_copywriting_create_bottom_symbol_label" alt="">
            </div>
            <div class="ai_copywriting_create_bottom_btns" @click.stop="stopThink">
                <template v-if="createing">
                   <span class="ai_copywriting_create_bottom_think_icon"></span>
                   <span class="ai_copywriting_create_bottom_think_text">停止创作中</span>
                </template>
                <template v-else>
                    <el-button  @click="createCopyWriting">重新创作</el-button>
                    <el-button @click="insert_input">插入下方</el-button>
                </template>
            </div>
        </div>
    </el-dialog>
</template>
<script setup>
import { ref,defineExpose,defineEmits,watch,nextTick } from 'vue'
import { marked } from 'marked';
let emit=defineEmits(['createCopyWriting','insert_input','stopWriting','recover_toolbar'])
let dialogDetailVisible = ref(false)
let isThinking=ref(false)//文案创作思考
let createing=ref(false)//文案创作中
let isRefreshing=ref(false)//文案创作重新生成
let messages = ref([]); // 聊天记录
let messageList = ref(null);
let inputText = ref(''); // 输入框内容
let renderMarkdown = (content) => {
	try {
		return marked(content);
	} catch (e) {
		console.error('Markdown 渲染错误:', e);
		return content;
	}
};
let loading = ref(false)
let loadingOptions= {
  text: '文案创作中...',
  background: 'rgba(255, 255, 255, 0.7)'
}
//重新创作
let createCopyWriting=()=>{
    messages.value=[]
    messageList.value=[]
    emit('createCopyWriting',inputText.value)
}
let close=()=>{
    messages.value=[]
    isThinking.value=false
    isRefreshing.value=false
    dialogDetailVisible.value=false
    inputText.value=''
    emit("recover_toolbar")

}
let ai_copywriting_create_list =ref(null)
let scrollToBottom = () => {
     if (ai_copywriting_create_list.value) {
      const container = ai_copywriting_create_list.value;
      // 获取最新高度
      const scrollHeight = container.scrollHeight;
      // 使用 scrollTo 滚动到底部，behavior 可选 'auto' 或 'smooth'
      if(messageList.value){
          messageList.value.scrollTo({
            top: scrollHeight,
            behavior: 'auto',
          });
      }
    }
};
// 传统复制方法
const fallbackCopyTextToClipboard = (text) => {
	try {
		// 确保复制的文本已过滤颜文字
		const filteredText = removeEmojisAndSpecialSymbols(text);
		
		const textArea = document.createElement("textarea");
		textArea.value = filteredText;
		textArea.style.position = "fixed";
		textArea.style.top = "0";
		textArea.style.left = "0";
		textArea.style.width = "2em";
		textArea.style.height = "2em";
		textArea.style.padding = "0";
		textArea.style.border = "none";
		textArea.style.outline = "none";
		textArea.style.boxShadow = "none";
		textArea.style.background = "transparent";

		document.body.appendChild(textArea);
		textArea.focus();
		textArea.select();

		const successful = document.execCommand("copy");
		document.body.removeChild(textArea);

		if (successful) {
			// ElMessage.success("复制成功");
		} else {
			ElMessage.error("复制失败");
		}
	} catch (err) {
		ElMessage.error("复制失败");
		console.error("复制失败:", err);
	}
};
// 通用复制到剪贴板函数
const copyToClipboard = (text) => {
	// 确保复制前过滤颜文字
	const filteredText = removeEmojisAndSpecialSymbols(text);
	
	if (navigator.clipboard && window.isSecureContext) {
		navigator.clipboard
			.writeText(filteredText)
			.then(() => {
				// ElMessage.success("复制成功");
			})
			.catch(() => {
				fallbackCopyTextToClipboard(filteredText);
			});
	} else {
		fallbackCopyTextToClipboard(filteredText);
	}
};
// 添加复制菜单函数
const copyWithContext = (index, e) => {
	e.preventDefault();
	e.stopPropagation();
	const msg = messages.value[index];
	
	// 创建上下文菜单
	const menu = document.createElement('div');
	menu.className = 'copy-context-menu';
	menu.style.position = 'fixed';
	menu.style.top = e.clientY + 'px';
	menu.style.left = e.clientX + 'px';
	menu.style.zIndex = '10000';
	menu.style.background = 'white';
	menu.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
	menu.style.borderRadius = '4px';
	menu.style.padding = '8px 0';
	
	// 菜单选项1：复制为纯文本
	const plainTextOption = document.createElement('div');
	plainTextOption.innerText = '复制为纯文本';
	plainTextOption.style.padding = '8px 16px';
	plainTextOption.style.cursor = 'pointer';
	plainTextOption.style.fontSize = '14px';
	plainTextOption.onmouseover = () => {
		plainTextOption.style.backgroundColor = '#f5f7fa';
	};
	plainTextOption.onmouseout = () => {
		plainTextOption.style.backgroundColor = 'transparent';
	};
	plainTextOption.onclick = () => {
		// convertMarkdownToPlainText已经包含了颜文字过滤
		const plainText = convertMarkdownToPlainText(msg.content);
		copyToClipboard(plainText);
		document.body.removeChild(menu);
	};
	
	// 菜单选项2：复制原始格式
	const originalOption = document.createElement('div');
	originalOption.innerText = '复制原始内容';
	originalOption.style.padding = '8px 16px';
	originalOption.style.cursor = 'pointer';
	originalOption.style.fontSize = '14px';
	originalOption.onmouseover = () => {
		originalOption.style.backgroundColor = '#f5f7fa';
	};
	originalOption.onmouseout = () => {
		originalOption.style.backgroundColor = 'transparent';
	};
	originalOption.onclick = () => {
		// 原始内容也需要过滤颜文字
		copyToClipboard(removeEmojisAndSpecialSymbols(msg.content));
		document.body.removeChild(menu);
	};
	
	// 添加选项到菜单
	menu.appendChild(plainTextOption);
	menu.appendChild(originalOption);
	document.body.appendChild(menu);
	
	// 点击页面其他区域关闭菜单
	const closeMenu = (e) => {
		if (!menu.contains(e.target)) {
			document.body.removeChild(menu);
			document.removeEventListener('mousedown', closeMenu);
		}
	};
	setTimeout(() => {
		document.addEventListener('mousedown', closeMenu);
	}, 100);
};
let stopThink=()=>{
    emit('stopWriting')

}
let insert_input=()=>{
     const container = ai_copywriting_create_list.value;
  if (container) {
    // 删除所有同时带有 ai_copywriting_create_list_item 和 user 类的元素
    const itemsToRemove = container.querySelectorAll('.ai_copywriting_create_list_item.user');
    itemsToRemove.forEach(item => item.remove());
  }

  // 触发事件，传递处理后的 innerHTML
  emit('insert_input', container ? container.innerHTML : '');

  // 关闭弹窗或浮层
  close();
 
}
watch(
	() => messages,
	async () => {
		await nextTick();
		scrollToBottom();
	},
	{ deep: true }
);
defineExpose({
    dialogDetailVisible,
    isThinking,
    messages,
    isRefreshing,
    inputText,
    createing,
    loading 
    
})
</script>
<style lang="scss">
.ai_copywriting_create_dialog{
    box-sizing: border-box;
    padding: 16px 20px;
    background: #FFFFFF;
    border-radius: 8px;
    position: relative;
    .el-dialog__header{
        position: absolute;
        right:20px;
        top: 16px;
        z-index: 2;
         .ai_copywriting_create_dialog_close{
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
    }
   
    .el-dialog__body{
        .el-scrollbar{
            height: 313px;
            margin-bottom: 19px;
            .ai_copywriting_create_thinking {
                position: relative;
                overflow: hidden;
                .ai_copywriting_create_thinking_text {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    position: relative;
                    z-index: 1;

                    span {
                        font-size: 1.2em;
                        animation: float 2s ease-in-out infinite;
                    }
                }

                .ai_copywriting_create_thinking_wave {
                    position: relative;
                    display: inline-block;
                    color: #606266;

                    &::after {
                        content: "...";
                        animation: dots 1.5s infinite;
                    }
                }
                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg,
                            rgba(255, 255, 255, 0) 0%,
                            rgba(255, 255, 255, 0.6) 50%,
                            rgba(255, 255, 255, 0) 100%);
                    animation: wave 1.5s infinite;
                }
            }
            .ai_copywriting_create_list{
                display: flex;
                flex-direction: column;
                .ai_copywriting_create_list_item{
                   font-size: 14px;
                   line-height: 24px;
                   color: #000;
                    :deep(p) {
                        margin: 8px 0;
                    }
                    
                    :deep(pre) {
                        background-color: #f6f8fa;
                        border-radius: 6px;
                        padding: 16px;
                        overflow: auto;
                        font-size: 85%;
                        line-height: 1.45;
                        margin: 12px 0;
                    }
                    
                    :deep(code) {
                        background-color: rgba(175, 184, 193, 0.2);
                        border-radius: 6px;
                        padding: 0.2em 0.4em;
                        font-size: 85%;
                        font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
                    }
                    
                    :deep(ul), :deep(ol) {
                        padding-left: 2em;
                        margin: 8px 0;
                    }
                    
                    :deep(blockquote) {
                        padding: 0 1em;
                        color: #656d76;
                        border-left: 0.25em solid #d0d7de;
                        margin: 12px 0;
                    }
                    
                    :deep(table) {
                        border-collapse: collapse;
                        width: 100%;
                        margin: 12px 0;
                        
                        th, td {
                            border: 1px solid #d0d7de;
                            padding: 6px 13px;
                        }
                        
                        tr:nth-child(2n) {
                            background-color: #f6f8fa;
                        }
                    }
                }
            }
        }
        .ai_copywriting_create_bottom{
            padding-top: 10px;
            display: flex;
            align-items: center;
            .ai_copywriting_create_bottom_symbol{
                display: flex;
                align-items: center;
                .ai_copywriting_create_bottom_symbol_img{
                    width: 22px;
                    height: 22px;
                    margin-right: 8px;
                }
                .ai_copywriting_create_bottom_symbol_label{
                    height: 16px;
                }
            }
            .ai_copywriting_create_bottom_btns{
                margin-left: auto;
                display: flex;
                align-items:center;
                .ai_copywriting_create_bottom_think_icon{
                    width: 10px;
                    height: 10px;
                    background: rgba(0, 0, 0, 0.45);
                    margin-right: 7px;
                    cursor: pointer;
                }
                .ai_copywriting_create_bottom_think_text{
                    font-size: 14px;
                    line-height: 24px;
                    color: rgba(0, 0, 0, 0.45);
                    cursor: pointer;
                }
                .el-button{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 80px;
                    height: 32px;
                    background: linear-gradient(90deg, #3E9EFD 0%, #7956FF 100%);
                    border-radius: 2px;
                    font-size: 14px;
                    line-height: 24px;
                    color: #FFFFFF;
                    margin: 0;
                    margin-right: 8px;
                    cursor: pointer;
                    border: none;
                    &:last-child{
                        margin-right: 0;
                }
            }    
        }
    }
}
}
</style>