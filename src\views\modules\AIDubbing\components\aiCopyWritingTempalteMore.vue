<template>
    <el-dialog class="ai_copywriting_tempalte_more_dialog" width="1068px"  v-model="dialogDetailVisible" append-to="#app" :show-close="false">
        <template #header>
            <div class="ai_copywriting_tempalte_more_header">
                <span class="ai_copywriting_tempalte_more_header_title">多种视频文案创作模版，润色校对</span>          
                <img src="@/assets/images/aiImages/ai_copywriting_tempalte_more_header_close.svg" class="ai_copywriting_tempalte_more_header_close" @click="close" alt="">
             </div>
        </template>
        <template #default>
            <div class="ai_copywriting_tempalte_more_content">
                <div class="ai_copywriting_tempalte_more_content_list">
                    <div class="ai_copywriting_tempalte_more_content_list_item" v-for="item in list" :key="item.id" :class="ai_copywriting_more_current==item.id?'active':''" @click="change_copywriting_more(item)">
                        <div class="type-icon">
                            <img  :src="item.ossUrl"  alt="" >
                        </div>
                        
                    </div>
                   
                </div>
                <div class="ai_copywriting_tempalte_more_editor">
                        <!-- 高亮输入框，contenteditable 替换 el-input -->
                        <div
                            class="ai_copywriting_tempalte_more_input"
                            contenteditable="true"
                            ref="ai_copywriting_more_input_ref"
                            :placeholder="`请输入文案`"
                            @input="ai_copywriting_input_more"
                            @keydown.enter="handleKeydownMore"
                            @paste="handlePaste"
                            @compositionstart="handleCompositionStart"
                            @compositionend="handleCompositionEnd"
                        ></div>
                        <div class="ai_copywriting_tempalte_more_buttons">
                      
                            <div class="ai_copywriting_tempalte_more_group">
                                <el-button @click="send_message" link class="ai_copywriting_tempalte_more_btn" circle>
                                    <img  src="@/assets/images/aiImages/ai_copywriting_toolbar_send.svg" alt="发送" />
                                </el-button>
                            </div>
                        </div>
                    </div>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref,defineExpose,defineEmits,defineProps,watch,nextTick } from 'vue'
import { useAiCopywriting } from '@/views/modules/AIDubbing/hook/useAiCopywriting.js';
import { ElMessage, ElMessageBox,ElLoading } from 'element-plus'
let dialogDetailVisible = ref(false)
let props = defineProps({
    ai_copywriting_create_ref: {
    type: String,
    required: false,
    default: ''
  }
})
let emit = defineEmits(['send_message','recover_toolbar'])
let list=ref([])
let close=()=>{
    // ai_copywriting_more_current.value=''
    // list.value=[]
    dialogDetailVisible.value=false
    emit('recover_toolbar')
}
let send_message=()=>{
    emit('send_message',inputText.value)
    close()
}
let {
     change_copywriting_more,
     ai_copywriting_more_input_ref,
     ai_copywriting_more_current,
     handleKeydown,
     inputText
    //  handlePaste,
    //  handleCompositionStart,
    //  handleCompositionEnd
    } = useAiCopywriting();
let handleKeydownMore=(event)=>{
    handleKeydown(event,props.ai_copywriting_create_ref,ElLoading)
    close()
}
  let getCaretPosition = (editableDiv) => {
    let caretOffset = 0;
    let doc = editableDiv.ownerDocument || editableDiv.document;
    let win = doc.defaultView || doc.parentWindow;
    let sel;
    if (typeof win.getSelection != 'undefined') {
      sel = win.getSelection();
      if (sel.rangeCount > 0) {
        let range = win.getSelection().getRangeAt(0);
        let preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(editableDiv);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        caretOffset = preCaretRange.toString().length;
      }
    }
    return caretOffset;
  };
  let isComposing = ref(false)
  let cleanAllHtmlTags=(container)=>{
    const text = container.innerText || container.textContent || '';
    container.innerText = text;
 }
 let setCaretPosition=(editableDiv, pos)=>{
  editableDiv.focus();
  const selection = window.getSelection();
  selection.removeAllRanges();

  let currentPos = 0;
  const nodeStack = [editableDiv];
  let node, stop = false;
  const range = document.createRange();

  while (!stop && (node = nodeStack.pop())) {
    if (node.nodeType === 3) { // 文本节点
      const nextPos = currentPos + node.length;
      if (pos >= currentPos && pos <= nextPos) {
        range.setStart(node, pos - currentPos);
        range.collapse(true);
        stop = true;
        break;
      }
      currentPos = nextPos;
    } else {
      // 逆序遍历子节点，保证顺序正确
      for (let i = node.childNodes.length - 1; i >= 0; i--) {
        nodeStack.push(node.childNodes[i]);
      }
    }
  }

  if (!stop) {
    range.selectNodeContents(editableDiv);
    range.collapse(false);
  }

  selection.addRange(range);
}
    // 输入事件处理
  let ai_copywriting_input_more = (e) => {
     if (isComposing.value) return;

    const editableDiv = e.target;
    const caretPos = getCaretPosition(editableDiv);

    // cleanAllHtmlTags(editableDiv);

    setCaretPosition(editableDiv, caretPos);

    inputText.value = editableDiv.innerText;
  };
let handlePaste=(e)=> {
  e.preventDefault();
  const text = (e.clipboardData || window.clipboardData).getData('text');
  document.execCommand('insertText', false, text);
}
let handleCompositionStart=()=>{
  isComposing.value = true;
}
let handleCompositionEnd=(e)=>{
  isComposing.value = false;
  ai_copywriting_input_more(e);
}
watch(dialogDetailVisible, async(newVal, oldVal) => {
  if(newVal){
    await nextTick()
    change_copywriting_more(list.value[0])
  }
},{immediate:true, deep:true});
//设置模板话术颜色
defineExpose({
    dialogDetailVisible,
    list
})
</script>
<style lang="scss">
.ai_copywriting_tempalte_more_dialog{
    padding: 0;
    border-radius: 8px;
    .el-dialog__header{
        padding: 0;
        .ai_copywriting_tempalte_more_header{
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 18px 16px;
            background: #FFFFFF;
            border-bottom: 1px solid #DEDEDF;
            position: relative;
            box-sizing: border-box;
            .ai_copywriting_tempalte_more_header_title{
                font-size: 16px;
                line-height: 22px;
                color: #1D2129;
            }
            .ai_copywriting_tempalte_more_header_close{
                position: absolute;
                top: 19px;
                right: 16px;
                margin-left: auto;
                width: 20px;
                height: 20px;
                cursor: pointer;
            }
        }
    }
    .ai_copywriting_tempalte_more_content{
        padding: 32px 33px  48px;
        box-sizing: border-box;
        .ai_copywriting_tempalte_more_content_list{
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 16px;
            .ai_copywriting_tempalte_more_content_list_item{
                width: 241px;
                height: 85px;
                margin-right: 12px;
                margin-bottom: 16px;
                border-radius: 8px;
                overflow: hidden;
                border: 1px solid rgba(222, 222, 223, 0.76);
                display: flex;
                align-items: center;
                justify-content: flex-start;
                background: #FFFFFF;
                .type-icon {
                    width: 183px;
                    height: 85px;
                    margin-right: 0;
                    overflow: hidden;
                }
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                &:nth-child(4n){
                    margin-right: 0;
                }
                &.active{
                    border: 1px solid #0AAF60;
                }
            }
        }
        .ai_copywriting_tempalte_more_editor{
            border: 1px solid #DCDCDC;
            width: 100%;
            min-height: 118px;
            padding:12px;
            background: #fff; /* 纯色背景 */
            border-radius: 8px;
            overflow: visible;
            position: relative;
            display: flex;
            .ai_copywriting_tempalte_more_input {
                flex: 1;
                position: relative;
                z-index: 2;
                font-size: 16px;
                font-family: inherit;
                line-height:22px;
                box-sizing: border-box;
                /* border: 1px solid #dcdfe6; */
                // border-radius: 20px;
                background: #fff;
                outline: none;
                padding: 0 ;
                color: #353D49;
                caret-color: #000;
                resize: none;
                overflow-y: auto;
                // font{
                //    color: #353D49!important; 
                // }
                // 兼容placeholder
                &:empty:before {
                    content: attr(placeholder);
                    color: #bbb;
                }
                
            }
            .ai_copywriting_tempalte_more_buttons {
                align-self: flex-end;
                .ai_copywriting_tempalte_more_group {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    .ai_copywriting_tempalte_more_btn {
                        padding: 6px;
                        width: 30px;
                        height: 30px;
                        border: none;
                        :hover {
                            background-color: #fff;
                        }
                        img {
                            width: 30px;
                            height: 30px;
                        }
                    }
                }
            }
            &:before {
                    content: "";
                    position: absolute;
                    top: 0; 
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 8px;
                    padding: 1px; /* 边框宽度 */
                    background: linear-gradient(290.19deg, #7956FF 28.18%, #3E9EFD 78.95%);
                    /* 挖空中间区域，露出主元素背景 */
                    -webkit-mask:
                        linear-gradient(#fff 0 0) content-box, 
                        linear-gradient(#fff 0 0);
                    -webkit-mask-composite: destination-out;
                    mask-composite: exclude;

                    pointer-events: none;
                    z-index: -1;
                }
        }
        
    }
    
}
</style>