<template>
    <div class="ai_match">
        <!-- <div class="ai_match_title">
            <img src="@/assets/images/aiImages/ai_match_title.svg" class="ai_match_title_icon" alt="">
            <img src="@/assets/images/aiImages/ai_match_title_close.svg" class="ai_match_title_close" @click="ai_match_close" alt="">
        </div> -->
        <div class="ai_match_loading" v-if="loading">
            <div class="ai_match_loading_bar_boxs">
                <div v-for="n in 6" :key="n" class="ai_match_loading_bar"></div>
            </div>
            <img src="@/assets/images/aiImages/ai_match_label.svg" class="ai_match_label" alt="">
        </div>
        <el-scrollbar  ref="scroll" always v-else>

        <div class="ai_match_list" >
           <template v-if="list.length > 0">
                <div class="ai_match_list_item" v-for="(item,index) in list" :key="index" @click="choose(item)" :class="item.isSelected?'current':''">
                    <div class="ai_match_list_item_blend">
                        <span class="ai_match_list_item_blend_rate">{{item.match}}%</span>
                        <span class="ai_match_list_item_blend_label">匹配度</span>
                    </div>
                    <div class="ai_match_list_item_avator">
                        <img :src="item.avatarUrl" alt="">
                    </div>
                    <div class="ai_match_list_item_info">
                        <div class="ai_match_list_item_info_name">
                            <span>{{getDisplayName(item)}}</span>
                            <img :src="getImage(item.membershipGrade)" alt="">
                        </div>
                        <div class="ai_match_list_item_info_age">
                            年龄：{{ item.ageGroup }}
                        </div>
                        <div class="ai_match_list_item_info_area">
                            擅长领域：{{ item.sceneCategory }}
                        </div>
                        <div class="ai_match_list_item_info_sound">
                            声音特质：{{getDisplayEmotionTag(item)}}
                        </div>
                    </div>
                </div>
           </template>
           <template v-else>
                <el-empty description="暂无数据" />
           </template>
        </div>
    </el-scrollbar>
    </div>
</template>
<script setup>
import {ref,reactive,defineEmits,defineExpose} from "vue"
import aiMatchVip from '@/assets/images/aiImages/ai_match_vip.svg'
import aiMatchSvip from '@/assets/images/aiImages/ai_match_svip.svg'
import aiMatchAttain from '@/assets/images/aiImages/ai_match_attain.svg'
let emit=defineEmits(['ai_match_close','choose_ai'])
let loading=ref(false)
let ai_match_close=()=>{
    emit('ai_match_close')
}
let list=ref([])
let scroll=ref(null)
let handleScroll=()=>{

}
let choose=(data)=>{
    list.value.map((item)=>{
        item.isSelected=false
    })
    data.isSelected=true
    emit('choose_ai',data)
}

// 根据是否有搜索关键词返回平台昵称
const getDisplayName = (item, keyword) => {
    if (!keyword || keyword.trim() === '') {
        return item.platformNickname || ''
    }

    const lowerKeyword = keyword.toLowerCase()
    const nickname = (item.platformNickname || '').toLowerCase()

    if (nickname.includes(lowerKeyword)) {
        return item.platformNickname || ''
    }

    return item.platformNickname || ''
}

// 根据是否有搜索关键词返回情感标签
const getDisplayEmotionTag = (item, keyword) => {
    if (!keyword || keyword.trim() === '') {
        return item.emotionTags || ''
    }

    const lowerKeyword = keyword.toLowerCase()
    const emotionTags = (item.emotionTags || '').toLowerCase()
    const sceneCategory = (item.sceneCategory || '').toLowerCase()

    if (emotionTags.includes(lowerKeyword)) {
        return item.emotionTags || ''
    }

    if (sceneCategory.includes(lowerKeyword)) {
        return `${item.emotionTags || ''} [${item.sceneCategory}]`
    }

    return item.emotionTags || ''
}
let getImage=(sign)=>{
    if(sign=='VIP'){
        return aiMatchVip
    }else if(sign=='SVIP'){
        return aiMatchSvip
    }else{
        return aiMatchAttain
    }
}
defineExpose({
    list,
    loading
})
</script>
<style lang="scss" scoped>
.ai_match{
    width: 100%;
    // padding: 0 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-bottom: 80px;
    .ai_match_title{
        display: flex;
        align-items: center;
        height: 62px;
        margin-bottom: 14px;
        box-sizing: border-box;
        border-bottom: 1px solid #F5F5F5;
        .ai_match_title_icon{
            width: 118px;
            height: 29px;
        }
        .ai_match_title_close{
            margin-left: auto;
            width: 24px;
            height: 24px;
            cursor: pointer;
        }

    }
    .ai_match_list{
        display: flex;
        flex: 1;
        flex-direction: column;
        .el-scrollbar{
            flex: 1; /* 让滚动条容器撑满剩余高度 */
        }
        .ai_match_list_item{
            box-sizing: border-box;
            padding: 16px 24px;
            width: 100%;
            height: 129px;
            background: #FFFFFF;
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            display: flex;
            cursor: pointer;
            position: relative;
            margin-bottom: 8px;
            overflow: hidden;
            .ai_match_list_item_avator{
                width: 70px;
                height: 70px;
                border-radius: 4px;
                margin-right: 20px;
                overflow: hidden;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .ai_match_list_item_info{
                display: flex;
                flex-direction: column;
                .ai_match_list_item_info_name{
                    display: flex;
                    align-items: center;
                    margin-bottom: 13px;
                    span{
                        font-size: 18px;
                        line-height: 20px;
                        color: #000000;
                        margin-right: 13px;
                    }
                    img{

                    }
                }
                .ai_match_list_item_info_age{
                    font-size: 14px;
                    line-height: 20px;
                    color: #808080;
                    margin-bottom: 2px;
                }
                .ai_match_list_item_info_area{
                    font-size: 14px;
                    line-height: 20px;
                    color: #808080;
                    margin-bottom: 2px;
                }
                .ai_match_list_item_info_sound{
                    font-size: 14px;
                    line-height: 20px;
                    color: #808080;
                }   
            }
            .ai_match_list_item_blend{
                position: absolute;
                right: 0;
                top: 0;
                padding:  6px 5px 6px 12px;
                box-sizing: border-box;
                height: 32px;
                background: linear-gradient(290.19deg, #7956FF 28.18%, #3E9EFD 78.95%), linear-gradient(270.97deg, #6FEAEA -8.14%, #00E175 92.84%);
                border-radius: 100px 8px 0px 100px;
                color: #fff;
                display: flex;
                align-items: center;
                line-height: 20px;
                .ai_match_list_item_blend_rate{
                    margin-right: 4px;
                    font-size: 16px;
                    display: flex;
                    align-items: center;
                    
                }
                .ai_match_list_item_blend_label{
                    font-size: 14px;
                    display: flex;
                    align-items: center;

                }
            }
            &.current{
                background: #F7F7F9;
                border: 1px solid #F5F5F5;
            }
        }
  
    }
    .ai_match_loading{
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .ai_match_loading_bar_boxs{
            display: flex;
            align-items: center; /* 中间对齐 */
            gap: 6px;
            height: 57px;
            margin: 24px auto;
            .ai_match_loading_bar {
                width: 8px;
                height: 35px; /* 默认高度 */
                background-color: #80F1AD; /* 默认颜色 */
                border-radius: 3px;
                animation-name: wave;
                animation-duration: 1.8s;
                animation-iteration-count: infinite;
                animation-timing-function: ease-in-out;
                transform-origin: center center; /* 缩放中心为中间 */
            }

            /* 不同的动画延迟，依次错开 */
            .ai_match_loading_bar:nth-child(1) {
                animation-delay: 0s;
            }
            .ai_match_loading_bar:nth-child(2) {
                animation-delay: 0.3s;
            }
            .ai_match_loading_bar:nth-child(3) {
                animation-delay: 0.6s;
            }
            .ai_match_loading_bar:nth-child(4) {
                animation-delay: 0.9s;
            }
            .ai_match_loading_bar:nth-child(5) {
                animation-delay: 1.2s;
            }
            .ai_match_loading_bar:nth-child(6) {
                animation-delay: 1.5s;
            }
        }
       
        .ai_match_label{
            width: 93px;
            height:22px;
        }
    }
}
/* 关键帧动画：高度和颜色同步变化 */
@keyframes wave {
    0%, 100% {
      height: 35px;
      background-color: #80F1AD;
      opacity: 0.7;
    }
    50% {
      height: 57px;
      background-color: #0AAF60;
      opacity: 1;
    }
  }
</style>