<template>
  <div>
    <audio
        @timeupdate="updateProgress"
        controls
        ref="audioRef"
        style="display: none"
    >
      <source :src="audioUrl" type="audio/mpeg" />
      您的浏览器不支持音频播放
    </audio>
    <div class="audio_right">
<!--      <img-->
<!--          v-if="!audioIsPlay"-->
<!--          @click="playAudio"-->
<!--          class="audio_icon"-->
<!--          src="../../../assets/img/play.png"-->
<!--          alt="播放"-->
<!--      />-->
<!--      <img-->
<!--          v-if="audioIsPlay"-->
<!--          @click="playAudio"-->
<!--          class="audio_icon"-->
<!--          src="../../../assets/img/pause.png"-->
<!--          alt="暂停"-->
<!--      />-->
      <!--   播放暂停icon   -->
      <div class="audio_icon" @click="playAudio">
        <Iconfont
            color="#fff"
            size="14px"
            :name="!audioIsPlay?'pause-fill':'bofang'"
        />
      </div>
<!--  @mouseenter="handleMouseEnter"-->
<!--  @mouseleave="handleMouseLeave"-->
<!--   @mouseover="handleMouseOver"-->
<!-- @click="handleMouseOver"-->
      <div
        class="slider_wrapper slider_wrapper1"
        ref="sliderWrapper"
        @click="onSliderClick"
      >
          <el-slider
              class="slider_box"
              style="margin-right:0"
              v-model="currentProgress"
              :show-tooltip="false"
              @input="handleProgressChange"
          />
        </div>
      <!--    时间  -->
      <div class="audio_time">
        <span class="audio_current">{{ audioStart }}</span>
        &nbsp;/&nbsp;
        <span class="audio_total">{{ durationTime }}</span>
      </div>



<!--      <div class="audio_time">-->
<!--        <span class="audio_current">{{ audioStart }}</span>-->
<!--        &nbsp;/&nbsp;-->
<!--        <span class="audio_total">{{ durationTime }}</span>-->
<!--      </div>-->
      <div class="volume">
        <div class="volume_progress" v-show="audioHuds">
          
            <el-slider
                vertical
                height="100px"
                class="volume_bar"
                v-model="audioVolume"
                :show-tooltip="false"
                @change="handleAudioVolume"
            />
    
        </div>
<!--        <img-->
<!--            class="volume_icon"-->
<!--            v-if="audioVolume <= 0"-->
<!--            @click.stop="audioHuds = !audioHuds"-->
<!--            src="../../../assets/img/audio_mute.png"-->
<!--            alt=""-->
<!--        />-->
<!--        <img-->
<!--            class="volume_icon"-->
<!--            v-if="audioVolume > 0"-->
<!--            @click.stop="audioHuds = !audioHuds"-->
<!--            src="../../../assets/img/audio_high.png"-->
<!--            alt=""-->
<!--        />-->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch,defineEmits,defineExpose } from "vue";
import { ElMessage } from 'element-plus'
let emit= defineEmits(['change_play_status','stopPlay'])
const props = defineProps({
  audioUrl: String,        //试听的链接
  isPauseTtsAudio: Boolean     //是否暂停播放试听
});

// durationTime
// watch(() => duration, (newVal) => {
//   console.log('duration',newVal)
// })
let onSliderClick=(event)=>{
  if (!sliderWrapper.value) return;

  const rect = sliderWrapper.value.getBoundingClientRect();

  // 鼠标点击相对进度条左边界的距离（视觉坐标）
  const clickX = event.clientX - rect.left;

  // 反缩放，得到设计稿坐标系下的点击位置
  const realClickX = clickX / scale.value;

  // 设计稿宽度（未缩放宽度）
  const sliderWidthDesign = rect.width / scale.value;
console.log(realClickX,sliderWidthDesign);

  // 计算百分比
  let percent = realClickX / sliderWidthDesign;

  // 限制在0~1之间
  if (percent < 0) percent = 0;
  if (percent > 1) percent = 1;

  // 更新进度条和音频播放时间
  currentProgress.value = percent * 100;
  audioRef.value.currentTime = percent * duration.value;
  audioStart.value = transTime(audioRef.value.currentTime);
}
let userInteracted = ref(false);
// 监听用户交互事件，允许播放
window.addEventListener('click', () => {
  userInteracted.value = true;
});
// console.log('iiuiuoiuo',props.audioUrl)
watch(() => props.audioUrl, (newVal) => {
  // console.log('pppppppppp',props.audioUrl,newVal)
  if (audioRef.value) {
    // console.log('68576846',newVal)
    audioRef.value.src = newVal;
    // console.log('000000',audioRef.value)
    audioRef.value.load();  // 强制重新加载音频‌:ml-citation{ref="4" data="citationList"}
     currentProgress.value = 0
  }
  // console.log('iiiiiiiiiiiiiiiiiiiiiiiiii',newVal,duration.value)
  // 监听音频文件成功之后立即播放
  if(newVal&& userInteracted.value) {
    audioRef.value.play();
    audioIsPlay.value = false;
    // audioRef.value.load();
  }
});



const audioIsPlay = ref(false); //音频是否在播放
const audioStart = ref("0:00");
const durationTime = ref("0:00"); //音频的总时长，显示的时间格式
const duration = ref(0); //音频的总时长
const audioVolume = ref(80); //音量的默认值是0.8
const audioHuds = ref(false); //是否显示音量slider
const audioRef = ref(null);
const currentProgress = ref(0);


watch(() => props.isPauseTtsAudio, (newVal, oldVal) => {
  if (newVal) {
    console.log('ooooooooo',newVal)
    // 如果 isPauseTtsAudio 为 true，试听暂停
    handleCloseMusic();
  }
});
function handleCloseMusic() {
  audioRef.value.pause();
  audioIsPlay.value = true;
}
onMounted(() => {
  calculateDuration();
});
// 获取音频时长
function calculateDuration() {
  var myVid = audioRef.value;
  myVid.loop = false;
  myVid.src = props.audioUrl;

  // console.log('pppppooo',props.audioUrl)
  myVid.addEventListener('play', () => {
    console.log('音频开始播放');
    emit('change_play_status','play')
  });
  myVid.addEventListener('pause', () => {
    console.log('音频开始播放');
    emit('change_play_status','pause')
  });
  // 监听音频播放完毕
  myVid.addEventListener(
      "ended",
      function () {
         emit('change_play_status','end')
         console.log('音频播放结束');
         audioIsPlay.value = true;
         currentProgress.value = 0;
      },
      false
  );
  durationTime.value = transTime(0); //换算成时间格式
  console.log(myVid,'oooooooooooooooooooooooooooooo')
  if (myVid != null) {
    
    myVid.oncanplay = function () {
      duration.value = myVid.duration; // 计算音频时长
      durationTime.value = transTime(myVid.duration); //换算成时间格式
    };
    myVid.volume = 0.8; // 设置默认音量50%
    // 进入页面默认开始播放
    // audioRef.value.play();
    audioIsPlay.value = true;
  }
}
// 音频播放时间换算
function transTime(duration) {

  
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  const formattedMinutes = String(minutes).padStart(2, "0"); //padStart(2,"0") 使用0填充使字符串长度达到2
  const formattedSeconds = String(seconds).padStart(2, "0");
  return `${formattedMinutes}:${formattedSeconds}`;
}
//暂停
let audio_pause=()=>{
  audioRef.value.pause();
  audioIsPlay.value = true;
}
//停止
let audio_stop=()=>{
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value.currentTime = 0;
      // 手动触发 ended 事件
    let endedEvent = new Event('ended');
    audioRef.value.dispatchEvent(endedEvent);
  }
  audioIsPlay.value = true;
  currentProgress.value = 0;
  audioStart.value = transTime(0);

  Promise.resolve().then(() => {
    emit('change_play_status','end');
  });
}
// 播放暂停控制
function playAudio() {

  // 判断有没有音频文件
  if(!props.audioUrl){
    ElMessage({
      message: '暂无音频文件',
      type: 'warning',
    })
    return
  }


  if (audioRef.value.paused) {
    audioRef.value.play();
    audioIsPlay.value = false;
    emit('stopPlay')
  } else {
    audio_pause()
  }
}
// 根据当前播放时间，实时更新进度条
function updateProgress(e) {
  console.log(audioRef.value.currentTime);
  var value = e.target.currentTime / e.target.duration;
  if (audioRef.value.play) {
    // currentProgress.value = value * 100/scale.value;
    currentProgress.value = value* 100
    audioStart.value = transTime(audioRef.value.currentTime);
  }
}
//调整播放进度
const handleProgressChange = (val) => {
  console.log(val);
  if (!val) {
    return;
  }
  // let currentTime = duration.value * (val / 100);
  // 改重置播放条前面的文字读不出来的问题
  let currentTime = duration.value * (val / 100);
  console.log(currentTime,duration.value,val,'currentTime');
  // currentProgress.value = val/scale.value;
  currentProgress.value = val;
  console.log(currentProgress.value,'currentProgress');
  
  // 更新音频的当前播放时间
  audioRef.value.currentTime = currentTime;

  
};
//调整音量
const handleAudioVolume = (val) => {
  audioRef.value.volume = val / 100;
};
const sliderWrapper = ref(null);
const isMouseDown = ref(false);// 标记鼠标是否按下

// 鼠标进入，开始允许拖动跳转（模拟之前的mousedown逻辑）
const handleMouseEnter = (event) => {
  isMouseDown.value = true;
  updateAudioCurrentTime(event);
};

// 鼠标离开，结束拖动跳转（模拟之前的mouseup和mouseleave逻辑）
const handleMouseLeave = () => {
  if (isMouseDown.value) {
    isMouseDown.value = false;
  }
};

// 鼠标悬浮移动时，如果鼠标处于按下状态，实时更新播放时间
const handleMouseOver = (event) => {
  console.log(event,'event');
  
  if (!isMouseDown.value) return;
  updateAudioCurrentTime(event);
};
let scale=ref(window.innerWidth/1920)
// 根据鼠标事件更新音频当前播放时间
const updateAudioCurrentTime = (event) => {
  if (!sliderWrapper.value || !audioRef.value || !duration.value) return;

  const rect = sliderWrapper.value.getBoundingClientRect();
  let offsetX = event.clientX - rect.left;
 // 限制 offsetX 在滑轨范围内
  if (offsetX < 0) offsetX = 0;
  if (offsetX > rect.width) offsetX = rect.width;
  // 计算百分比

  const percent = offsetX / rect.width;
  // 计算对应播放时间
  const newTime = duration.value * percent;
  // 设置音频当前播放时间
console.log(newTime,'newTime');

  audioRef.value.currentTime = newTime;
  // 更新进度条和时间显示
  // currentProgress.value = percent * 100/scale.value;
  currentProgress.value = percent * 100
  audioStart.value = transTime(newTime);
};
defineExpose({handleCloseMusic,handleMouseOver,audio_pause,audio_stop,playAudio})


</script>

<style lang="scss" scoped>
.audio_right {
  //width: 230px;
  height: 44px;
  display: flex;
  align-items: center;
  //background: linear-gradient(to left, #2e7bff 0%, #8ee7ff 100%);
  background-color: #F7F7F9;
  border-radius: 40px;
  padding: 0 12px;
  box-sizing: border-box;
  position: relative;
  .slider_box {
    width: 160px;
    height: 4px;
    border-radius: 5px;
    background-color: #f1f1f1;
    flex: 1;
    //margin: 0 8px 4px;
    margin:0 20px;
    ::v-deep(.el-slider__button-wrapper) {
      width: 12px;
    }
  }
  .audio_icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    //margin-bottom: 4px;
    border-radius: 50%;
    cursor: pointer;
    background-color: #0AAF60;
  }
  .audio_time {
    color: #000;
    //overflow: hidden;
    font-size: 12px;
    //position: absolute;
    //bottom: 3px;
    //left: 80px;
    .audio_total {
      float: right;
    }
    .audio_current {
      float: left;
    }
  }
   .slider_wrapper {
    flex: 1;
    display: flex;
    margin-right: 27px;
    &.slider_wrapper1{
      .el-slider{
        margin-right: 0;
      }
    }
  }
}
.volume {
  position: relative;
  .volume_progress {
    width: 32px;
    height: 140px;
    position: absolute;
    top: -142px;
    right: -4px;
  }
  .volume_bar {
    background: #fff;
    border-radius: 4px;
  }
  .volume_icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.el-slider__button-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.slider_box,
.volume_bar {
  .el-slider__button {
    width: 8px;
    height: 8px;
    border: none;
  }
  .el-slider__bar {
    background: #0AAF60;
  }
}
.slider_box {
  .el-slider__button-wrapper {
    width: 8px;
  }
}
.volume_bar {
  .el-slider__runway {
    margin: 0 14px !important;
  }
}
</style>

