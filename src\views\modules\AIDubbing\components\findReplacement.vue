<template>
  <div class="find_replacement_popup">
    <div class="find_replacement_popup_title">
      <span class="find_replacement_popup_title_text">查找替换</span>
      <span class="find_replacement_popup_title_tip" v-if="total>0">
        第<span class="find_replacement_popup_title_tip_number">{{ current }}</span>处/共<span class="find_replacement_popup_title_tip_number">{{ total }}</span>处
      </span>
    </div>
    <el-form label-width="auto"  :rules="rules" :model="form" ref="formRef"  class="find_replacement_popup_form" @submit.prevent>
        <!-- :validate-status="usernameError ? 'error' : ''"  :error="usernameError" -->
      <el-form-item :class="containerClass"  prop="findInput"
       
       >
        <el-input
          v-model="form.findInput"
          placeholder="请输入想要查找的内容"
          @keyup.enter="onFind"
          @focus="onFocus"
          @blur="onBlur"
        >
          <template #prefix>
            <img src="@/assets/images/aiImages/findReplacement_find.svg" alt="" />
          </template>
          <template #suffix>
            <span @click="onFind"  :class="form.findInput!=''?'has_input':''">查找</span>
          </template>
          <template #append>
            <div class="find_somewhere">
              <img
                src="@/assets/images/aiImages/find_somewhere_upward.svg"
                alt=""
                class="find_somewhere_item"
                @click="onUpward"
              />
              <img
                src="@/assets/images/aiImages/find_somewhere_downward.svg"
                alt=""
                class="find_somewhere_item"
                @click="onDownward"
              />
            </div>
          </template>
        </el-input>
      </el-form-item>
      <div class="has_find_text" v-if="total>0">
       {{ form.findInput}}
      </div>
      <el-form-item v-if="total>0"  prop="replaceInput">
        <el-input
          v-model="form.replaceInput"
          placeholder="请输入替换内容"
          @keyup.enter="onReplace"
        />
      </el-form-item>
    </el-form>
    <div class="find_replacement_popup_btns" :class="total>0&&form.replaceInput!=''?'han_replace':''">
      <span class="find_replacement_popup_btns_item" @click="onReplace">替换</span>
      <span class="find_replacement_popup_btns_item" @click="onReplaceAll">替换全部</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch,reactive,computed,defineEmits,defineProps,nextTick } from 'vue'

let props = defineProps({
  total: {
    type: Number,
    default: 0,
  },
  current: {
    type: Number,
    default: 0,
  },
})
let rules = reactive({
  findInput: [
    { required: true, message: '请输入想要查找的内容', trigger: 'blur' },
    {
      validator: async (rule, value, callback) => {
        console.log(value,props.total,777);
        
        await nextTick()
        if ( props.total === 0) {
          callback(new Error('未查找到内容，请重新输入'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  replaceInput: [
    { required: true, message: '请先输入需要替换的文字在进行替换', trigger: 'blur' },
    
  ]
})
let formRef = ref(null)
let emit = defineEmits(['find', 'upward', 'downward', 'replace', 'replaceAll'])
let focused = ref(false)
let usernameError = ref('')
let form = reactive({
    findInput:'',//查找内容
    replaceInput:''//替换内容
})
watch(
  () => props.total,
  (newVal) => {
    if (newVal === 0) {
      // form.findInput= ''
      form.replaceInput = ''
    }
  }
)
// 计算外层容器class
let containerClass = computed(() => {
  return {
    'input-container-focused': focused.value,
  }
})
let onFocus=()=>{
  focused.value = true
}

let onBlur=()=>{
  focused.value = false
}
let onFind=()=>{
  if (form.findInput.trim() === '') return
  emit('find', form.findInput.trim())
//   validateUsername('findInput')
    formRef.value.validateField('findInput')
}

let onUpward=()=>{
  emit('upward')
}

let onDownward=()=> {
  emit('downward')
}

let onReplace=()=> {
  if (form.replaceInput.trim() === '') return
  emit('replace', form.replaceInput.trim())
}

let onReplaceAll=()=> {
  if (form.replaceInput.trim() === '') return
  emit('replaceAll', form.replaceInput.trim())
}
let init=()=>{

}
</script>

<style lang="scss">
.find_replacement_popup {
  padding: 32px;
  padding-top: 24px;
  width: 464px;
  box-sizing: border-box;
  .find_replacement_popup_title {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    line-height: 24px;
    .find_replacement_popup_title_text {
      font-size: 16px;
      color: #1d2129;
      margin: 0;
    }
    .find_replacement_popup_title_tip {
      margin-left: auto;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      .find_replacement_popup_title_tip_number {
        color: #1890ff;
      }
    }
  }
  .find_replacement_popup_form {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    .el-form-item {
      margin-bottom: 0;
      width: 100%;
      .el-form-item__content {
        .el-input {
          width: 100%;
          .el-input__wrapper {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            width: 323px;
            height: 40px;
            background: #ffffff;
            border: 1px solid #dcdfE6;
            border-radius: 4px 0 0 4px;
            box-shadow: none;
            padding: 8px 12px;
            font-size: 14px;
            line-height: 22px;
            .el-input__prefix {
              img {
                width: 14px;
                height: 14px;
                margin-right: 6px;
              }
            }
            .el-input__inner {
              color: #353d49;
              &::placeholder {
                color: #a8abb2;
              }
            }
            .el-input__suffix {
              line-height: 22px;
              cursor: pointer;
              .el-input__suffix-inner {
                span {
                  color: rgba(0, 0, 0, 0.45);
                  margin: 0;
                  &.has_input {
                    color: #0aaf60;
                  }

                }
              }
            }
          }
          .el-input-group__append {
            padding-left: 5px;
            padding-right: 0;
            background-color: transparent;
            box-shadow: none;
            .find_somewhere {
              box-sizing: border-box;
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 8px 9px;
              width: 72px;
              height: 39px;
              border: 1px solid #dcdfE6;
              border-radius: 0 4px 4px 0;
              display: flex;
              align-items: center;
              .find_somewhere_item {
                cursor: pointer;
                margin-right: 4px;
                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }
        }
      }
      &.input-container-focused{
        .el-form-item__content {
            .el-input {
                .el-input__wrapper {
                    border-color: #0AAF60;
                }
            }
            }
      }
      &.is-error{
        .el-form-item__content {
            .el-input {
                .el-input__wrapper {
                    border-color: #FF3B30;
                }
            }
            }
      }
    }
    .has_find_text {
      margin-top: 43px;
      margin-bottom: 16px;
      font-size: 16px;
      line-height: 24px;
      color: #1D2129;
      align-self: flex-start;
    }
  }
  .find_replacement_popup_btns {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .find_replacement_popup_btns_item {
      margin-right: 21px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.45);
      &:last-child {
        margin-right: 0;
      }
    }
    &.han_replace{
        .find_replacement_popup_btns_item {
            color: #0AAF60;
        }
    }
  }
}
</style>