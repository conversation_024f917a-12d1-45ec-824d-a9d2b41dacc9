<template>
    <div class="multilingual_popup">
        <div class="multilingual_popup_tip">
            *请选择与文案相匹配的发音语言，以提高语种发音准确度。
        </div>
        <div class="multilingual_popup_list">
            <div class="multilingual_popup_list_item" :class="item.name==current_language?'current':''" v-for="item in list" :key="item.value" @click="choose(item)">
                {{ item.name }}
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref,defineEmits,defineExpose } from 'vue'
let emit=defineEmits(['choose'])
let list = ref([{
    value:'auto',
    name: 'AI适配'
},{
    value:'Chinese',
    name: '国语'
},{
    value:'Chinese,Yue',
    name: '粤语'
},{
    value:'English',
    name: '英语'
},{
    value:'Korean',
    name: '韩语'
},{
    value:'Japanese',
    name: '日语'
},{
    value:'German',
    name: '德语'
},{
    value:'Russian',
    name: '俄语'
},{
    value:"Arabic",
    name: '阿拉伯语'
},{
    value:'Dutch',
    name: '荷兰语'
},{
    value:'Turkish',
    name: '土耳其语'
},{
    value:'Polish',
    name: '波兰语'
},{
    value:'Finnish',
    name: '芬兰语'
},{
    value:'Hindi',
    name: '印地语'
},{
    value:'Greek',
    name: '希腊语'
},{
    value:'Czech',
    name: '捷克语'
},{
    value:'Romanian',
    name: '罗马尼亚语'
},{
    value:'Italian',
    name: '意大利语'
},{
    value:'Thai',
    name: '泰语'
},{
    value:'Spanish',
    name: '西班牙语'
},{
    value:'Portuguese',
    name: '葡萄牙语'
},{
    value:'French',
    name: '法语'
},{
    value:'Vietnamese',
    name: '越南语'
},{
    value:'Ukrainian',
    name: '乌克兰语'
},{
    value:'Indonesian',
    name: '印度尼西亚语'
}])
let choose=(data,no_close)=>{
   current_language.value=data.name;
   if(!no_close){
    emit('choose',data)
   }
  
}
let current_language=ref('中文')
let init_language=()=>{
    current_language.value='auto'
}
//对外暴露展示方法
let choose_item=(value)=>{
    let obj=list.value.filter(((item)=>item.value==value))[0]
    choose(obj,'no_close')
}
defineExpose({
    init_language,
    choose_item
})
</script>
<style lang="scss" scoped>
.multilingual_popup{
    padding: 19px 16px;
    padding-bottom: 10px;
    padding-right: 7px;
    box-sizing: border-box;
    .multilingual_popup_tip{
        font-size: 14px;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        margin-bottom: 14px;
    }
    .multilingual_popup_list{
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        flex: none;
        order: 0;
        flex-grow: 0;
        gap: 9px;
        width: 531px;
        .multilingual_popup_list_item{
            box-sizing: border-box;
            padding: 5px 10px;
            border: 1px solid #EFEFF1;
            border-radius: 100px;
            font-size: 14px;
            line-height: 18px;
            color: #000000;
            cursor: pointer;
            &.current{
                border-color: #0AAF60;
            }
        }
    }
}
</style>