<template>
    <el-dialog v-model="dialogVisible" class="package_buy_commercialDubbing_dialog" width="512px">
        <template #header>
            <img src="@/assets/images/soundStore/detail_package_close.png" class="package_buy_commercialDubbing_close" @click="close" alt="">
            <div class="package_buy_commercialDubbing_title">
                <img src="@/assets/images/soundStore/commercialDubbing_warn.svg" alt="">
                <span>温馨提示</span>
            </div>
        </template>
        <template #default>
            <p>
                <template v-if="character_enough">
                    本次下载需耗费 50 字符，是否继续？
                </template>
                <template v-else>
                    当前试听字符数不足，无法试听！
                </template>
            </p>
        </template>
        <template #footer>
            <button @click="submit" >确定</button>
            <button @click="cancel" v-if="character_enough">取消</button>
        </template>
        
    </el-dialog>
</template>
<script setup>
import { ref, defineExpose,} from 'vue';
let dialogVisible=ref(false)
let close=()=>{
    dialogVisible.value=false
}
let character_enough=ref(true)
let submit=(platformNickname)=>{
    if(!character_enough){
        close()
    }
}
let cancel=()=>{
    dialogVisible.value=false
}
defineExpose({
    dialogVisible,
})
</script>
<style lang="scss">
.package_buy_commercialDubbing_dialog{
    border-radius: 4px;
    padding: 32px;
    padding-top: 24px;
    box-sizing: border-box;
    position: relative;
    .el-dialog__headerbtn{
       display: none;
    }
    .el-dialog__header{
        padding: 0;
       .package_buy_commercialDubbing_close{
            width: 13px;
            height: 13px;
            top: 18px;
            right: 17px;
            position: absolute;
            cursor: pointer;
       }
       .package_buy_commercialDubbing_title{
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            img{
                width: 20px;
                height: 20px;
                margin-right: 8px;
            }
            span{
                font-size: 16px;
                color: #1D2129;
                line-height: 24px;
            }
       }

    }
    .el-dialog__body{
        p{
            font-size: 14px;
            color: #1D2129;
            line-height: 22px;
            margin: 0;
            margin-bottom: 24px;
        }
    }
    .el-dialog__footer{
        padding: 0;
        display: flex;
        justify-content: flex-end;
        button{
            padding: 5px 16px;
            color: #4E5969;
            border-radius: 2px 2px 2px 2px;
            font-size: 14px;
            background: #F2F3F5;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            margin-right: 8px;
            line-height: 22px;
            &:last-child{
                background: #0AAF60;
                color: #FFFFFF;
                margin-right: 0;
            }
        }

    }
}
</style>