import { ref,watch,nextTick } from 'vue';
import { questionAPl, getChatRecord<PERSON>pi, creationList } from "@/api/creation";
import { formatTextForHTML } from '@/utils/textFormat'; // 导入格式化函数
import axios from "axios";
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
// 添加打字效果定时器引用
let typingTimer = ref(null);
let ai_copywriting_list = ref([]); // AI文案工具栏顶部列表
let ai_copywriting_toolbar_current = ref(''); // AI文案工具栏顶部列表当前选中
let ai_copywriting_toolbar_current_obj = ref({}); // AI文案工具栏顶部列表当前选中对象
let ai_copywriting_more_current=ref('')// AI文案更多顶部列表当前选中
let ai_copywriting_more_current_obj=ref({})// AI文案更多顶部列表当前选中对象
let ai_copywriting_more_input_ref= ref(null); // AI文案更多输入框
let inputText = ref(''); // AI文案输入框内容
let isComposing = ref(false);

export function useAiCopywriting({
  proxy={},
  templateList = async () => ({ data: [] }),
  hasImg = { value: false },
  ai_copywriting_show = { value: false },
  ai_copywriting_tempalte_more_ref = { value: { dialogDetailVisible: false, list: [] } },
  ai_copywriting_create_ref= { value: { dialogDetailVisible: false, list: [] } },
  ai_copywriting_toolbar_input_ref,
  show_suspended_toolbar,
  editorRef
}={}) {

 


  // AI文案
  let ai_copywriting = async () => {
    if (!loginStore.token) {
      proxy.$modal.open('组合式标题');
      return;
    }
    let list = await templateList({ in_use: 2 });
    console.log(list, 'list');
    show_suspended_toolbar.value=false
    ai_copywriting_list.value = list.data;
    ai_copywriting_toolbar_current.value = '';
    ai_copywriting_toolbar_current_obj.value = {};
    console.log(hasImg,'hasImg');
    
    // hasImg.value = false;
   
    ai_copywriting_show.value = true;
    change_copywriting_toolbar(list.data[0])
await nextTick();
setTimeout(() => {
  if (editorRef.value) {
    const scrollbarView = editorRef.value.closest('.el-scrollbar__view');
    if (scrollbarView) {
      scrollbarView.scrollTop = scrollbarView.scrollHeight;
      console.log(scrollbarView, scrollbarView.scrollTop, scrollbarView.scrollHeight, '滚动到底部');
    } else {
      editorRef.value.scrollTop = editorRef.value.scrollHeight;
      console.log(editorRef.value, editorRef.value.scrollTop, editorRef.value.scrollHeight, '滚动到底部（fallback）');
    }
  }
}, 200);

    
  };

  // AI文案工具栏切换
  let change_copywriting_toolbar = async(item) => {
    ai_copywriting_toolbar_current.value = item.id;
    ai_copywriting_toolbar_current_obj.value = item;
    let html = formatTextForHTML(item.theme,'');

    await nextTick()
    if (ai_copywriting_toolbar_input_ref.value) {
      ai_copywriting_toolbar_input_ref.value.innerHTML = html;
      inputText.value = html;
      ai_copywriting_toolbar_input_ref.value.focus();
    }
  };
  // AI文案工具栏更多
  let ai_copywriting_more = () => {
    ai_copywriting_show.value = false;
    ai_copywriting_more_current.value = '';
    ai_copywriting_more_current_obj.value = {};
    ai_copywriting_tempalte_more_ref.value.dialogDetailVisible = true;
    ai_copywriting_tempalte_more_ref.value.list=ai_copywriting_list.value
    // change_copywriting_more(ai_copywriting_list.value[0])

  };
  let stopWriting=(ai_copywriting_create_ref)=>{
    ai_copywriting_create_ref.createing = false
    console.log(typingTimer.value,'停止');
    
    clearInterval(typingTimer.value);
  }
  // 获取光标位置
  let getCaretPosition = (editableDiv) => {
    let caretOffset = 0;
    let doc = editableDiv.ownerDocument || editableDiv.document;
    let win = doc.defaultView || doc.parentWindow;
    let sel;
    if (typeof win.getSelection != 'undefined') {
      sel = win.getSelection();
      if (sel.rangeCount > 0) {
        let range = win.getSelection().getRangeAt(0);
        let preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(editableDiv);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        caretOffset = preCaretRange.toString().length;
      }
    }
    return caretOffset;
  };

  // 设置光标位置
  let setCaretPosition = (editableDiv, chars) => {
    if (chars >= 0) {
      let selection = window.getSelection();
      let nodeStack = [editableDiv],
        node,
        foundStart = false,
        charCount = 0,
        stop = false;
      while (!stop && (node = nodeStack.pop())) {
        if (node.nodeType === 3) {
          let nextCharCount = charCount + node.length;
          if (!foundStart && chars <= nextCharCount) {
            let range = document.createRange();
            range.setStart(node, chars - charCount);
            range.collapse(true);
            selection.removeAllRanges();
            selection.addRange(range);
            stop = true;
          }
          charCount = nextCharCount;
        } else {
          let i = node.childNodes.length;
          while (i--) {
            nodeStack.push(node.childNodes[i]);
          }
        }
      }
    }
  };

  // 输入事件处理
  let ai_copywriting_input = (e) => {
    if (isComposing.value) return; // 输入法组合期间不做高亮渲染
    let editableDiv = e.target;
    let caretPos = getCaretPosition(editableDiv);
    let text = editableDiv.innerText;
    inputText.value = text;
    let html = formatTextForHTML(text,'');
    if (editableDiv.innerHTML !== html) {
      editableDiv.innerHTML = html;
      setCaretPosition(editableDiv, caretPos);
    }
  };

  // 处理回车键
  let handleKeydown = (event,ai_copywriting_create_ref) => {
    console.log(ai_copywriting_create_ref,'handleKeydown');
    if(ai_copywriting_create_ref){
      ai_copywriting_create_ref.value=ai_copywriting_create_ref
    }
    if (!event.shiftKey) {
      event.preventDefault();
      ai_copywriting_send_message(ai_copywriting_create_ref.value);
    }
  };

  // 处理粘贴事件
  let handlePaste = (e) => {
    e.preventDefault();
    let text = (e.clipboardData || window.clipboardData).getData('text');
    document.execCommand('insertText', false, text);
  };

  // 组合输入开始
  let handleCompositionStart = () => {
    isComposing.value = true;
  };

  // 组合输入结束
  let handleCompositionEnd = (e) => {
    isComposing.value = false;
    ai_copywriting_input(e);
  };
// 获取当前时间
let getCurrentTime = () =>
	new Date().toLocaleTimeString("zh-CN", {
		hour: "2-digit",
		minute: "2-digit",
});
// 自定义函数获取用户ID，当userId为null时使用默认值'11'
let getUserId = () => {
	return loginStore.userId || '11'
}
let createRequest=ref(null)
let extractTextFromHTML=(htmlString)=>{
  // 创建一个临时div容器
  const tempDiv = document.createElement('div');
  // 设置HTML内容
  tempDiv.innerHTML = htmlString;
  // 返回纯文本内容
  return tempDiv.textContent || tempDiv.innerText || '';
}
// 发送文案请求
let ai_copywriting_send_message = async (ai_copywriting_create_ref,data) => {
      console.log('发送内容:', inputText.value);
    if(data){
      inputText.value=data
    }
    if (!inputText.value || !inputText.value.trim()) {
      ElMessage.error('请输入创作提示');
      return;
    }
    ai_copywriting_show.value = false
    ai_copywriting_create_ref.dialogDetailVisible=true
    ai_copywriting_create_ref.inputText=inputText.value
    let userMsg = {
      content: extractTextFromHTML(inputText.value),
      time: getCurrentTime(),
      isUser: true,
      isNew: true,
      isThinking: true,
    };

    ai_copywriting_create_ref.isThinking = true;
    let messages=ai_copywriting_create_ref.messages
    messages.push(userMsg);

    let currentInput = inputText.value;
    inputText.value = '';

    await nextTick()
    console.log(ai_copywriting_create_ref.loading,'ai_copywriting_create_ref');
    ai_copywriting_create_ref.loading=true
    // let loadingInstance = ElLoading.service({
    //   target:dialogDom,  // 指定加载动画挂载到 myRef 对应的 DOM 元素
    //   lock: true,
    //   text: '文案创作中...',
    //   background: 'rgba(255, 255, 255, 0.7)',
    // });

    try {
      let questionWithHistory = currentInput;
      if (messages && messages.length > 1) {
        let historyText = messages
          .slice(0, -1)
          .map((msg) => {
            if (!msg) return '';
            return `${msg.isUser ? 'user：' : 'ai：'}${msg.content || ''}`;
          })
          .join('\n');
        questionWithHistory = `${historyText}\nuser：${currentInput}`;
      }
      let userId = getUserId() || '';
      createRequest.value = axios.CancelToken.source();
      let res = await questionAPl({
        userId: userId,
        question: extractTextFromHTML(questionWithHistory),
	      cancelToken: createRequest.value.token
      });
      ai_copywriting_create_ref.isThinking = false;
      if (res.status_code === 310) {
        // showLimitDialog.value = true;
        ai_copywriting_create_ref.loading=false
        return;
      }
      let aiContent = res.content.result.ai;
      let aiResponse = {
        content: aiContent,
        displayContent: '',
        time: getCurrentTime(),
        isUser: false,
        isNew: true,
        isThinking: false,
        isTyping: true,
      };
      messages.push(aiResponse);
      startTypingEffect(messages.length - 1, aiContent,ai_copywriting_create_ref);
      setTimeout(() => {
        messages.forEach((msg) => (msg.isNew = false));
        // scrollToBottom();
      }, 1000);
    } catch (error) {
      ai_copywriting_create_ref.isThinking = false;
      if (error && error.response && error.response.status_code === 310) {
        // showLimitDialog.value = true;
        ai_copywriting_create_ref.loading=false
        return;
      }
      ElMessage.error('生成失败，请稍后重试');
      console.error('生成失败:', error);
    } finally {
      ai_copywriting_create_ref.loading=false
    }
  };
// 添加打字效果函数
let startTypingEffect = (messageIndex, fullText,ai_copywriting_create_ref) => {
  ai_copywriting_create_ref.createing=true
  let messages=ai_copywriting_create_ref.messages
	// 确保不超出数组范围
	if (messageIndex < 0 || messageIndex >= messages.length) {
		return;
	}
	
	// 初始化显示内容为空
	messages[messageIndex].displayContent = "";
	
	// 字符计数器
	let charIndex = 0;
	// 字符总数
	let totalChars = fullText.length;
	// 随机打字速度 - 每个字符25-40毫秒
	
	// 清除可能存在的旧定时器
	if (typingTimer.value) {
		clearInterval(typingTimer.value);
	}

  
	// 设置定时器逐字显示
	typingTimer.value = setInterval(() => {
		// 每次显示一个新字符
		charIndex++;
		
		// 更新显示内容
		messages[messageIndex].displayContent = fullText.substring(0, charIndex);
		
		// 自动滚动到底部，保持最新消息可见
		// scrollToBottom();
		
		// 所有字符显示完成后清除定时器
		if (charIndex >= totalChars) {
      ai_copywriting_create_ref.createing=false
			clearInterval(typingTimer.value);
			messages[messageIndex].isTyping = false; // 打字效果结束
			typingTimer.value = null;
		}
	}, 80); // 调整为更慢的速度：80-120毫秒
  	console.log(typingTimer.value,'赋值定时器');
};
// 滚动到底部
let scrollToBottom = () => {
	// if (messageList.value) {
	// 	messageList.value.scrollTo({
	// 		top: messageList.value.scrollHeight,
	// 		behavior: "smooth",
	// 	});
	// }
};
let setCaretAfterLastSpan=(container)=>{
 const spans = container.querySelectorAll('span');
  if (spans.length === 0) return;

  const lastSpan = spans[spans.length - 1];
  const range = document.createRange();
  const sel = window.getSelection();

  if (lastSpan.nextSibling) {
    range.setStart(lastSpan.nextSibling, 0);
  } else {
    const emptySpan = document.createElement('span');
    // 方案1：零宽空格
    emptySpan.textContent = '\u200B';
    // 方案2：换行符
    // emptySpan.appendChild(document.createElement('br'));
    container.appendChild(emptySpan);
    range.setStart(emptySpan, 0);
  }

  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
}
//设置模板话术颜色
let formatAllTextColor = (text, color = '#1890FF') => {
   if (!text) return '';
  const chars = Array.from(text);
  const spans = chars.map(char => `<span style="color: ${color}">${char}</span>`);
  spans.push('<span>&nbsp;</span>'); // 空白节点
  return spans.join('');
};
//存储原始数据
let raw_data=ref()
  // AI文案更多切换
  let change_copywriting_more = async(item) => {


    
  ai_copywriting_more_current.value = item.id;
  ai_copywriting_more_current_obj.value = item;
  await nextTick()
  if (ai_copywriting_more_input_ref.value) {
    raw_data.value=item.theme
    let html = formatAllTextColor(item.theme);
    ai_copywriting_more_input_ref.value.innerHTML = html;
 
    
    inputText.value = html;
    ai_copywriting_more_input_ref.value.focus();
      // 设置光标位置
    setCaretAfterLastSpan(ai_copywriting_more_input_ref.value);
   
    
    }
  };
  
  
watch(
  [
    () => ai_copywriting_show?.value?? false,
    () => ai_copywriting_create_ref?.value?.dialogDetailVisible?? false,
    () => ai_copywriting_tempalte_more_ref?.value?.dialogDetailVisible?? false
  ],
  ([show, createDialog, templateDialog]) => {
    show_suspended_toolbar.value = !show && !createDialog && !templateDialog;
  },
);
  return {
    ai_copywriting_list,
    ai_copywriting_toolbar_current,
    ai_copywriting_toolbar_current_obj,
    inputText,
    isComposing,
    ai_copywriting,
    change_copywriting_toolbar,
    ai_copywriting_more,
    ai_copywriting_input,
    getCaretPosition,
    setCaretPosition,
    handleKeydown,
    handlePaste,
    handleCompositionStart,
    handleCompositionEnd,
    ai_copywriting_send_message,
    stopWriting,
    change_copywriting_more,
    ai_copywriting_more_input_ref,
    ai_copywriting_more_current,
    ai_copywriting_more_current_obj,
  };
}
