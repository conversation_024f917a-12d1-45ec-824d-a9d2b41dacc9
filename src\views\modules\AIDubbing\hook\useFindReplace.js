// useFindReplace.js
import { ref,watch, nextTick } from 'vue'
import { ElMessage} from 'element-plus'
export function useFindReplace(editorRef,findReplacementVisible) {
  let matches = ref([]) // 所有匹配的 <b> 元素
  let findText = ref('')
  let currentIndex = ref(0)

  // 清理所有高亮标签
  let clearHighlights = () => {
    console.log('清理样式');
    
    if (!editorRef.value) return
    let root = editorRef.value
    let highlights = root.querySelectorAll('b.match')
    highlights.forEach(b => {
      let textNode = document.createTextNode(b.textContent)
      b.parentNode.replaceChild(textNode, b)
    })
    matches.value = []
    currentIndex.value = 0
  }

  // 查找并高亮
  let highlightMatches = (findStr) => {
    if (!editorRef.value) return
    clearHighlights()
    if (!findStr) return

    let index = 0

    function walk(node) {
      if (node.nodeType === 3) {
        let text = node.nodeValue
        let regex = new RegExp(findStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
        let match
        let lastIndex = 0
        let frag = document.createDocumentFragment()
        while ((match = regex.exec(text)) !== null) {
          if (match.index > lastIndex) {
            frag.appendChild(document.createTextNode(text.slice(lastIndex, match.index)))
          }
          let b = document.createElement('b')
          b.className = 'match'
          b.dataset.id = index
          b.textContent = match[0]
          frag.appendChild(b)
          index++
          lastIndex = regex.lastIndex
        }
        if (lastIndex < text.length) {
          frag.appendChild(document.createTextNode(text.slice(lastIndex)))
        }
        if (index > 0) {
          node.parentNode.replaceChild(frag, node)
        }
      } else if (node.nodeType === 1 && node.childNodes && !['SCRIPT', 'STYLE', 'B'].includes(node.tagName)) {
        for (let i = 0; i < node.childNodes.length; i++) {
          walk(node.childNodes[i])
        }
      }
    }

    walk(editorRef.value)
    matches.value = Array.from(editorRef.value.querySelectorAll('b.match'))
    currentIndex.value = 0
    updateCurrentHighlight()
  }

  // 更新当前高亮样式
  let updateCurrentHighlight = () => {
    matches.value.forEach((el, i) => {
      if (i === currentIndex.value) {
        el.classList.add('current')
        el.scrollIntoView({ behavior: 'smooth', block: 'center' })
      } else {
        el.classList.remove('current')
      }
    })
  }

  // 上一个匹配
  let handleUpward = () => {
    if (matches.value.length === 0) return
    currentIndex.value = (currentIndex.value - 1 + matches.value.length) % matches.value.length
    updateCurrentHighlight()
  }

  // 下一个匹配
  let handleDownward = () => {
    if (matches.value.length === 0) return
    currentIndex.value = (currentIndex.value + 1) % matches.value.length
    updateCurrentHighlight()
  }

  // 替换当前匹配
  let handleReplace = (replaceStr) => {
    if (matches.value.length === 0) return
    let currentEl = matches.value[currentIndex.value]
    if (!currentEl) return
    let textNode = document.createTextNode(replaceStr)
    currentEl.parentNode.replaceChild(textNode, currentEl)
    matches.value.splice(currentIndex.value, 1)
    if (currentIndex.value >= matches.value.length) {
      currentIndex.value = matches.value.length - 1
    }
    updateCurrentHighlight()
    ElMessage.success('替换成功')
  }

  // 替换全部匹配
  let handleReplaceAll = (replaceStr) => {
    matches.value.forEach(el => {
      let textNode = document.createTextNode(replaceStr)
      el.parentNode.replaceChild(textNode, el)
    })
    matches.value = []
    currentIndex.value = 0
    ElMessage.success('全部替换成功')
  }

  // 处理查找事件
  let handleFind = (findStr) => {
    findText.value = findStr
    nextTick(() => {
      highlightMatches(findStr)
    })
  }
  // 初始化

  let findReplaceInit=()=>{
    currentIndex.value = 0
    matches.value = []
    clearHighlights()
  }
watch(()=>findReplacementVisible.value,(newValue,oldValue)=>{
  console.log(newValue,'关闭查找弹窗');
  
  if(!newValue){
    clearHighlights()
  }
},{immediate:true,deep:true})
  return {
    matches,
    findText,
    currentIndex,
    clearHighlights,
    highlightMatches,
    updateCurrentHighlight,
    handleUpward,
    handleDownward,
    handleReplace,
    handleReplaceAll,
    handleFind,
    findReplaceInit
  }
}
