import { ref,nextTick } from 'vue'

export function useInsertEmotion({
    targetElements = ref([]),
    insert_emotion_ref = ref(null),
    currentItem = ref(null),
    extraContentIndex = ref(null)
}) {


  // 设置元素 ref

let setRef = (index) => {

  
	return (el) => {
		if (el) {
			targetElements.value[index] = el; // 将元素存储到数组中
		}
	};
};
  // 插入额外内容逻辑
 let insert_emotion = async (item, index,centerX) => {
  console.log(targetElements.value,'targetElements');
  
  const clickedTop = targetElements.value[index]?.offsetTop
  if (clickedTop === undefined) {
    extraContentIndex.value = null
    return
  }

  const currentTop = extraContentIndex.value !== null ? targetElements.value[extraContentIndex.value]?.offsetTop : null

  // 如果点击的是当前显示额外内容的那一行，且内容相同，则不切换
  if (clickedTop === currentTop && currentItem.value === item) {
    // 内容相同，不重复调用
    return
  }

  // 点击了不同的行或不同内容，切换额外内容位置
  extraContentIndex.value = null // 先关闭已有额外内容

  // 找到点击行最后一个元素的索引
  let lastIndexInRow = index
  for (let i = index + 1; i < targetElements.value.length; i++) {
    if (targetElements.value[i]?.offsetTop !== clickedTop) break
    lastIndexInRow = i
  }

  extraContentIndex.value = lastIndexInRow

  await nextTick()
  console.log(insert_emotion_ref.value,'insert_emotion_ref.value');
  
   insert_emotion_ref.value&&insert_emotion_ref.value[0].init_emotion(item,centerX)
  // 记录当前显示的item
  currentItem.value = item
}
  return {
    setRef,
    insert_emotion,
  
  }
}
