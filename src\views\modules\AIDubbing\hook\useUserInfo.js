// src/composables/useUserBenefits.js
import { useloginStore } from '@/stores/login'
import { showUserBenefits } from '@/api/login.js'
import { ref } from 'vue'

export function useUserBenefits() {
  const loginStore = useloginStore()
  const userLoading = ref(false)
  const userError = ref(null)

  // 防重复调用机制：记录上次调用时间
  let lastFetchTime = 0
  const FETCH_INTERVAL = 2000 // 2秒内不重复调用

  // 获取并更新会员权益信息，返回接口数据，异常抛出
  const fetchUserBenefits = async () => {
    if (!loginStore.userId || loginStore.userId === '') {
      // 用户未登录，直接返回null或抛异常
      return null
    }

    // 防重复调用检查
    const now = Date.now()
    if (now - lastFetchTime < FETCH_INTERVAL) {
      console.log('⏰ 用户权益刷新请求过于频繁，跳过本次调用')
      return loginStore.memberInfo // 返回当前缓存的数据
    }
    lastFetchTime = now

    userLoading.value = true
    userError.value = null

    try {
      let res = await showUserBenefits({ userId: loginStore.userId })
      if(res.code!=0){
        ElMessage.error(res.msg) 
        return 
      }
      let member_data=res?.data?.content
      if (member_data.result) {
        loginStore.setMemberInfo(member_data.result)
        return member_data.result  // 返回结果
      } else {
        const err = new Error('接口返回数据无效')
        userError.value = err
        throw err
      }
    } catch (err) {
      userError.value = err
      throw err
    } finally {
      userLoading.value = false
    }
  }

  return {
    userLoading,
    userError,
    fetchUserBenefits,
  }
}
