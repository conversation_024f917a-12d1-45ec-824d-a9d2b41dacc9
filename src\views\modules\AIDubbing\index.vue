<script setup>
// 定义组件名称，用于keep-alive排除
defineOptions({
  name: 'AIDubbing'
})

//合成语音接口
import { synthesized_speechApi, Sound_ListApi, Sound_tabs_listApi, filter_sound_listApi, batchCreateAPI, queryTextAPI,queryUserUsedVoiceName,queryUserBuyVoiceName,selectVoiceByAI,templateList,extractWavByMp3,cloneList } from '@/api_my/AlDubb'
import {bookmarkList,bookmarkToggle} from '@/api/soundStore.js'
import { chekSensitive_Api } from '@/api_my/commercialDubb'
import jingpin from '@/assets/images/aiImages/jingpin.svg';
import musicIcon from '@/assets/images/aiImages/music.png';
import zhenxiang from '@/assets/images/aiImages/zhenxiang.svg';
import { useUmeng } from '@/utils/umeng/hook';
import { createAIDubbingAnalytics } from '@/utils/umeng/modules/AIDubbing';
import { useAIDubbingStore } from '@/stores/modules/AIDubbing.js'
import soundEffects from '@/views/constant/musicModal/soundEffects.vue'
import GlobalimportLetter from "@/views/constant/importLetters/importLetter.vue";
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
import { nanoid } from 'nanoid';
import { useRoute, useRouter } from 'vue-router';
// import { parseString } from 'htmlparser2';
import { DomHandler } from 'domhandler'
import * as htmlparser2 from "htmlparser2";

import { Parser } from 'htmlparser2';
import axios from 'axios';

import { pinyin, html, polyphonic } from 'pinyin-pro';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { getPinyinWithCharacters } from '@/common/utils/pinyinUtil.js';
import { getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref, watch, watchEffect, computed, onActivated } from 'vue'
import AudioPlayer from "./components/audioPlayer.vue";
import Index from "@/views/modules/soundStore/index.vue";
import eventBus from "@/common/utils/eventBus.js";
import { useSoundStore } from '@/stores/modules/soundStore.js'
import daoru from '@/assets/images/aiImages/daoru.svg'
import duoyinzi from '@/assets/images/aiImages/duoyinzi.svg'
import bieming from '@/assets/images/aiImages/bieming.svg'
import charutingdun from '@/assets/images/aiImages/charutingdun.svg'
import shuzifuhao from '@/assets/images/aiImages/shuzifuhao.svg'
import beijingyinle from '@/assets/images/aiImages/beijingyinle.svg'
import yinxiao from '@/assets/images/aiImages/yinxiao.svg'
import pinyin_line from '@/assets/images/aiImages/pinyin_line.svg'
import find_replacement_icon from '@/assets/images/aiImages/find_replacement.svg'
import multilingual_icon from '@/assets/images/aiImages/multilingual.svg'
import trialListeningDialog from './components/trial_listening_dialog.vue'
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import { useloginStore } from '@/stores/login'
import aiMatch from './components/aiMatch.vue'
import collectImage from '@/assets/images/commercialImages/collectImage.svg'
import collectNoImage from '@/assets/images/commercialImages/collectNoImage.svg'

import { useAiCopywriting } from '@/views/modules/AIDubbing/hook/useAiCopywriting.js';
import aiCopyWritingCreate from '@/views/modules/AIDubbing/components/aiCopyWritingCreate.vue';
import aiCopyWritingTempalteMore from '@/views/modules/AIDubbing/components/aiCopyWritingTempalteMore.vue';
import findReplacement from '@/views/modules/AIDubbing/components/findReplacement.vue';
import multilingualPopup from '@/views/modules/AIDubbing/components/multilingual_popup.vue';
import { useFindReplace } from '@/views/modules/AIDubbing/hook/useFindReplace.js'
import InsertEmotion from '@/views/modules/AIDubbing/components/insert_emotion.vue';
import { useInsertEmotion  } from '@/views/modules/AIDubbing/hook/useInsertEmotion.js'
let rate=1
let targetElements = ref([]);
let cancelNavRequest=ref(null)
let { userLoading, userError, fetchUserBenefits } = useUserBenefits()
let loginStore = useloginStore()

// import scissors_2_line from '@/assets/images/aiImages/scissors_2_line.svg'
// import {crawlTextByMediaFile} from "@/api_my/AlDubb/index.js";
// import { useUmeng } from '@/utils/umeng/hook' // 导入友盟埋点
import customerService from '@/assets/images/index_images/customer_service.png';
const { proxy } = getCurrentInstance();


const route = useRoute();
let router = useRouter()
const useAIDubbing = useAIDubbingStore()
const soundStore = useSoundStore()
let traceId = ref('')
// 初始化友盟埋点
const umeng = useUmeng();
const AIDubbingAnalytics = createAIDubbingAnalytics(umeng);
// 下载按钮
const downlaodButtonArr = reactive([
	// { name: '试听下载', url: 'zimu' },
	{ name: '合成下载', url: 'xiazai',children: [ { name: 'mp3格式' ,index:0}, { name: 'wav格式',index:1 } ]  },
	// {name:'下载视频',url:'shipin1'},
	{ name: '字幕下载', url: 'zimu' },
])

// 操作多音字等按钮
const iconsArr = reactive([
	{ IconName: daoru, name: '导入文案' },
	// { IconName: duoyinzi, name: '多音字' },
	{ IconName: bieming, name: '读音替换' },
	{ IconName: charutingdun, name: '停顿' },
	{ IconName: shuzifuhao, name: '数字符号' },
	{ IconName: beijingyinle, name: '背景音乐' },
	{ IconName: yinxiao, name: '音效' },
	{ IconName: pinyin_line, name: '查看拼音' },
	{ IconName: find_replacement_icon, name: '查找替换' ,className:'find_replacement_popover',needFixed:true,key: 'findReplacement'},
	{ IconName: multilingual_icon, name: '多语种适配',className:'multilingual_popover' ,needFixed:true,key: 'multilingual'}
	// { IconName: duoyinzi, name: '多音字' },
	// { IconName: 'scissors-2-line', name: '去剪辑' },
])
// 点击操作多音字等按钮
// const IconNum = ref(0)
const clickIcons = (e, index) => {
	// const target = event.target.closest('[data-id]');
	// console.log('==========',target)
	// if(!target){
	//   if(target.dataset.id!=='parent-1'){
	//     var div = document.querySelector('div[contenteditable="true"]');
	//     // console.log('77',div)
	//     if (div) { // 检查div是否存在
	//       var children = div.children;
	//       // console.log('ppp',children)
	//       for (var i = 0; i < children.length; i++) {
	//         children[i].classList.remove('tts-tag');
	//       }
	//     }
	//
	//   }

	// }
	if(index!=6){
		pinyinBool.value=false
	}
	showPopover.value = false
	switch (index) {
		case 0:
			// proxy.$letterModal.openImportLetter()
			Letter.value.importDialogVisible = true
			break;
		case 1:
			// 点击读音替换
			isSelectedAlias()
			break;
		case 2:
			// 停顿弹窗
			stopPopover.value = true
			e.stopPropagation();
			break;
		case 3:
			// 先判断是否是纯数字
			isNanFun()
			break;
		case 4:
			proxy.$musicmodal.open(slideValue.value)
			break;
		case 5:
			// proxy.$effectsmodal.open()
			// console.log(effects.value.effectsDialogVisible)
			effects.value.effectsDialogVisible = true
			break;
		case 6:
			// 查看拼音
			pinyinFun()
			break;
		case 7:
			//查找替换
			find_replacement()
			break;
		case 8:

			//多语种
			multilingual()
			break;
		// 多音字
		// case 7:
		// 	// IconNum.value = index
		// 	//多音字操作  点击多音字弹出选择多音字弹窗
		// 	//点击多音字  根据多因去查询这个字的多有多音字 polyphonicList
		// 	gethomograph()
		// 	break;
		//去剪辑
		// case 8:
		// 	if (!localStorage.getItem('user')) {
		// 		proxy.$modal.open()
		// 		return
		// 	}
		// 	if (route.query.projectId) {
		// 		// 跳转到剪辑的页面
		// 		window.open(`https://yunjian.peiyinbangshou.com/App?projectId=${Number(route.query.projectId)}&token=${JSON.parse(localStorage.getItem('user'))?.token}`)
		// 	} else {
		// 		saveTextHtml(1)
		// 	}
		// 	// console.log(JSON.parse(localStorage.getItem('user'))?.token)
		// 	// return
		// 	break;
	}
}
// div编辑区域调节音量大小
const slideValue = ref(70)
// 中间主体左边文案搜索
const search_copywritingType = ref('')

// 中间主体右边银色搜索
const input_search = ref('')
const search_speaker = () => {
	console.log('搜索');

	clearEmotion()
	// 判断搜索框是否为空
	if (!input_search.value || input_search.value.trim() === '') {
		// 如果为空，重置搜索
		resetSearch()
		return
	}

	// 搜索时应用所有筛选条件
	let filteredList = performSearch(allSoundList.value)

	// 如果选择的是"全部"，按搜索匹配精确度和SVIP优先排序
	if (selecteVoiceTypeNum.value === '全部') {
		filteredList = sortVoicesBySearchMatch(filteredList, input_search.value);
	} else {
		filteredList.sort(() => Math.random() - 0.5);
	}

	soundList.value = filteredList

	// 在搜索后，确保标签显示与筛选结果一致
	// 检查各级分类是否为"全部"
	if (selecteVoiceTypeNum.value === '全部') {
		// 第一级为"全部"，确保所有下级也是"全部"
		selecteGenderNum.value = '全部'
		selecteSecondNum.value = '全部'
		selecteUniqueNum.value = '全部'
		// 更新标签基于搜索结果
		updateAllTagsBasedOnSearch(filteredList)
		// 确保thirdArrList包含所有可能的标签
		thirdArrList.value = [...uniqueList.value]
	} else if (selecteUniqueNum.value !== '全部') {
		// 保持当前状态，但确保第4级可见性是正确的
		updateLevelTags(soundList.value, 4);
	} else if (selecteSecondNum.value !== '全部') {
		// 如果第3级有选择，则更新第4级标签
		updateLowerLevelTags(filteredList, 3)
	} else if (selecteGenderNum.value !== '全部') {
		// 如果第2级有选择，则更新第3、4级标签
		updateLowerLevelTags(filteredList, 2)
	} else if (selecteVoiceTypeNum.value !== '全部') {
		// 如果第1级有选择，则更新第2、3、4级标签
		updateLowerLevelTags(filteredList, 1)
	} else {
		// 如果没有选择任何分类，则基于搜索结果更新所有标签
		updateAllTagsBasedOnSearch(filteredList)
	}
}

// 监听搜索框输入，实现实时搜索
watch(input_search, (newVal) => {
	if (newVal.trim() !== '') {
		search_speaker()
	} else {
		// 如果搜索框为空，则恢复原始音色列表，并根据当前已选的筛选条件过滤
		resetSearch()
	}
})
//清空表情
let clearEmotion=()=>{
	currentItem.value=null
	extraContentIndex.value=null
	//清空语言和表情
	current_language.value='auto'
	current_emotion.value='neutral'
}
// 重置搜索，恢复原始音色列表
const resetSearch = () => {
	clearEmotion()
	// 清空搜索框
	input_search.value = ''

	// 不修改当前的分类选择，保持用户已选择的分类
	// 但确保如果当前有选择某级分类，其下级分类必须是"全部"
	if (selecteVoiceTypeNum.value !== '全部') {
		if (selecteGenderNum.value !== '全部' || selecteSecondNum.value !== '全部' || selecteUniqueNum.value !== '全部') {
			selecteGenderNum.value = '全部'
			selecteSecondNum.value = '全部'
			selecteUniqueNum.value = '全部'
		}
	} else if (selecteGenderNum.value !== '全部') {
		if (selecteSecondNum.value !== '全部' || selecteUniqueNum.value !== '全部') {
			selecteSecondNum.value = '全部'
			selecteUniqueNum.value = '全部'
		}
	} else if (selecteSecondNum.value !== '全部') {
		if (selecteUniqueNum.value !== '全部') {
			selecteUniqueNum.value = '全部'
		}
	}

	// 恢复音色列表和标签
	filter_listFun()
}

// 搜索框清除按钮的处理函数
const handleClear = () => {
	resetSearch()
}

// 基于搜索结果更新所有标签
const updateAllTagsBasedOnSearch = (filteredResults) => {
	if (!filteredResults || filteredResults.length === 0) {
		// 如果没有匹配结果，不要重置标签，而是显示"无结果"状态
		return
	}

	// 收集搜索结果中所有可能的标签值
	const voiceTypes = new Set(['全部'])
	const genders = new Set(['全部'])
	const scenes = new Set(['全部'])
	const recommendTags = new Set(['全部'])

	filteredResults.forEach(item => {
		// 收集音色类型
		if (item.membershipGrade) {
			voiceTypes.add(item.membershipGrade)
		}
		if (item.grade) {
			voiceTypes.add(item.grade)
		}

		// 收集性别和年龄信息
		if (item.gender) {
			const genderInfo = item.gender.split('、')
			genderInfo.forEach(info => {
				genders.add(info.trim())
				// 提取年龄段信息
				if (info.includes('青年') || info.includes('中年') || info.includes('老年') || info.includes('少年')) {
					genders.add(info.trim())
				}
			})
		}
		if (item.ageGroup) {
			genders.add(item.ageGroup)
		}

		// 收集场景分类
		if (item.sceneCategory) {
			scenes.add(item.sceneCategory)
		}

		// 收集推荐标签
		if (item.recommendTags) {
			const tags = typeof item.recommendTags === 'string'
				? item.recommendTags.split(',')
				: Array.isArray(item.recommendTags)
					? item.recommendTags
					: [item.recommendTags]

			tags.forEach(tag => {
				if (tag && typeof tag === 'string') {
					recommendTags.add(tag.trim())
				}
			})
		}
	})

	// 更新各个标签的可见性
	voiceTypeArr.value.forEach(item => {
		item.visible = voiceTypes.has(item.name) || item.name === '全部'
	})

	gendersArr.value.forEach(item => {
		item.visible = genders.has(item.name) || item.name === '全部'
	})

	secondArr.value.forEach(item => {
		item.visible = scenes.has(item.name) || item.name === '全部'
	})

	if (thirdArrList.value) {
		thirdArrList.value.forEach(item => {
			item.visible = recommendTags.has(item.name) || item.name === '全部'
		})
	}
}

// 专门用于搜索的筛选函数，应用所有筛选条件
const performSearch = (items) => {
	if (!items || items.length === 0) return []

	return items.filter(item => {
		// 应用所有级别的筛选条件

		// 第1级：音色类型
		if (selecteVoiceTypeNum.value !== '全部') {
			// 针对SVIP和VIP特殊处理
			if (selecteVoiceTypeNum.value === 'SVIP' && item.membershipGrade !== 'SVIP') {
				return false
			} else if (selecteVoiceTypeNum.value === 'VIP' && item.membershipGrade !== 'VIP') {
				return false
			}
			// 针对其他分类，使用grade字段
			else if (!['SVIP', 'VIP'].includes(selecteVoiceTypeNum.value) && item.grade !== selecteVoiceTypeNum.value) {
				return false
			}
		}

		// 第2级：性别/年龄
		if (selecteGenderNum.value !== '全部') {
			const genderAgeValue = selecteGenderNum.value

			// 检查性别字段
			if (['男', '女'].includes(genderAgeValue)) {
				// 有些数据可能是"男、青年"格式，需要分割检查
				const genderInfo = (item.gender || '').split('、')
				if (!genderInfo.includes(genderAgeValue)) {
					return false
				}
			}
			// 检查年龄段
			else if (['青年', '中年', '老年', '少年', '中老年'].includes(genderAgeValue)) {
				// 优先检查ageGroup字段
				if (item.ageGroup) {
					if (item.ageGroup !== genderAgeValue) {
						return false
					}
				}
				// 如果没有ageGroup字段，尝试从gender字段中提取
				else {
					const genderInfo = (item.gender || '').split('、')
					if (!genderInfo.some(info => info.includes(genderAgeValue))) {
						return false
					}
				}
			}
		}

		// 第3级：场景分类
		if (selecteSecondNum.value !== '全部' && item.sceneCategory !== selecteSecondNum.value) {
			return false
		}

		// 第4级：推荐标签
		if (selecteUniqueNum.value !== '全部') {
			// 有些recommendTags可能是数组或逗号分隔的字符串
			const tags = typeof item.recommendTags === 'string'
				? item.recommendTags.split(',')
				: Array.isArray(item.recommendTags)
					? item.recommendTags
					: [item.recommendTags]

			if (!tags.some(tag => tag === selecteUniqueNum.value || tag.includes(selecteUniqueNum.value))) {
				return false
			}
		}

		// 搜索关键词：平台昵称
		if (input_search.value && input_search.value.trim() !== '') {
			const keyword = input_search.value.toLowerCase()
			const platformNickname = (item.platformNickname || '').toLowerCase()
			const emotionTags = (item.emotionTags || '').toLowerCase()
			const sceneCategory = (item.sceneCategory || '').toLowerCase()

			// 匹配名称、情感标签或场景分类
			if (!platformNickname.includes(keyword) &&
				!emotionTags.includes(keyword) &&
				!sceneCategory.includes(keyword)) {
				return false
			}
		}

		return true
	})
}


// 中间主体右边音色列表滚动事件
const scroll = ref(null);
const handleScroll = (e) => {
	const wrapRef = scroll.value.wrapRef;
	// console.log('wrapRef.scrollHeight',wrapRef.scrollHeight)   //内容总高度560
	// console.log('wrapRef.clientHeight',wrapRef.clientHeight)    //可视区域高度300
	// console.log('event.scrollTop',e.scrollTop)    //// 滚动条距盒子顶部高度260
	let poor = wrapRef.scrollHeight - wrapRef.clientHeight;
	// 判断滚动到底部
	if (e.scrollTop + 20 >= poor) {
		console.log('ppppppp到底了')
	}
}

// 中间主体右边语速滑块
const speechValue = ref(1)
// 语调
const intonationValue = ref(0)

// 是否播放音乐
const isPauseTtsAudio = ref(false)

// 点击文案类型中更多按钮
const clickMoreIcon = () => {
	proxy.$AImodal.open()
}
// 输入
const MAX_LENGTH = 5000
const textLength = ref(0);
const editorRef = ref(null)
const isComposing = ref(false)



let timeoutId = null; // 保存定时器ID
// 输入的文本内容
const editorContent = ref('')
let aiPostion=ref('')
// // 输入事件处理
const handleInput = async(event) => {
	let scrollbarEl = document.querySelector('.main_content_bottom_left');
	console.log(editorRef.value.textContent.length,editorRef.value.textContent);

	hasImg.value = editorRef.value.textContent.length>0
	const content = ref('');
	const text = event.target.innerText || event.target.textContent;
	// 监听用户输入
	clearTimeout(timeoutId); // 清除之前的定时器
	timeoutId = setTimeout(() => {
		// 获取输入内容
		const content = editorRef.value
		// console.log('用户停止输入:',selectedTextList);
		if (localStorage.getItem('user')) {
			saveTextHtml()
		}

		// 在此处执行后续操作，如提交数据或更新状态
	}, 1500); // 800毫秒延迟
	if (text.length >= MAX_LENGTH) {
		// 截断内容到最大长度
		event.target.innerText = text.substring(0, MAX_LENGTH);
		// // 设置光标到内容末尾
		// const range = document.createRange();
		// const sel = window.getSelection();
		// range.setStart(event.target.firstChild, MAX_LENGTH);
		// range.collapse(true);
		// sel.removeAllRanges();
		// sel.addRange(range);
	} else {
		// 更新内容（虽然这里内容没有变化，但为了确保响应式，可以保留此行代码）
		// content.value = text;
	}
	editorContent.value = event.target.textContent
	// 设置底部文本长度
	textLength.value = event.target.textContent.length
	let editor = document.getElementById('editor');
	if (editor) {
		let rect = editor.getBoundingClientRect();

		// if (scrollbarEl) {
		// 	const height = scrollbarEl.offsetHeight;
		// 	console.log();

		// 	if(aiPostion.value>height){
		// 		aiPostion.value = height-(250*(window.innerHeight/953))
		// 	}
		// }
		const height = scrollbarEl.getBoundingClientRect().bottom
		let height1=0
		let max_height=0
		if(window.innerHeight>953){
			height1=rect.bottom-25
			max_height=height-80
		}else{
			console.log(rect.bottom,window.innerHeight/953,rate,'位移');
			
			height1=(rect.bottom/(window.innerHeight/953))-(25/(window.innerHeight/953))
			max_height=(height/(window.innerWidth/1920))-80
		}
		await nextTick();
	setTimeout(() => {
		if (editorRef.value) {
			const scrollbarView = editorRef.value.closest('.el-scrollbar__view');
			if (scrollbarView) {
				scrollbarView.scrollTop = scrollbarView.scrollHeight
			} else {
				editorRef.value.scrollTop = editorRef.value.scrollHeight
			}
		}
	}, 200);
		if(height1>max_height){
			aiPostion.value = max_height
		}else{
			aiPostion.value = height1
		}
		// if(rate<1){
		// 	aiPostion.value=aiPostion.value*rate
		// }
		console.log(height1,'height1');

		if(height1>max_height-90){
			copywriting_aiPostion.value=max_height-90
		}else{
			copywriting_aiPostion.value=aiPostion.value
		}
	}


}
let copywriting_aiPostion=ref(0)

// 输入时，保存文本html结构 ,以及多音字数组
const saveTextHtml = (param) => {
	synthetic_button_type.value == ''
	// console.log('JSON.parse(localStorage.getItem(\'user\'))',JSON.parse(localStorage.getItem('user')))

	if (editorRef.value.innerHTML && !route.query.albumId) {
		// 仅在点击"去剪辑"按钮时调用API（param=1时）
		if (param === 1) {
			// 创建全屏loading
			const loadingInstance = ElLoading.service({
				lock: true,
				text: '正在准备跳转到剪辑页面...',
				background: 'rgba(0, 0, 0, 0.7)'
			})

			// 点击去剪辑时调用API
			handleBatchCreateAPI('saveTextHtml', loadingInstance, param)
		} else {
			// 普通输入时，只记录日志但不调用API
			console.log('输入更新，跳过batchCreateAPI调用');
		}
	}
}
let batchCreate_id=ref('')
//合成 下载 试听操作更新到我的作品
let handleBatchCreateAPI = (type) => {
	if(show_list_nav_current.value==2&&selectetMycurrent.value=='克隆音色'){
		current_source.value=2
	}else{
		current_source.value=1
	}
	let params = [{
		title: editorRef.value.textContent.slice(0, 10),
		copywriting: editorRef.value.innerHTML,
		type: "1",//type  类型（0:一键成片 1:AI配音 2:专业云剪 3:AI商配）
		albumId: 0,
		userId: JSON.parse(localStorage.getItem('user'))?.userId || '', //用户id
		ossAudioKey: useAIDubbing.bgmusic_url|| '',  //背景音乐
		subTitleFile: captions_url.value,//字幕文件
		status: '1',
		// heteronym: selectedTextList, //多音字列表
		traceId: traceId.value,
		ossAudioUrl:audioUrl.value,//合成的音频文件
		vedioId:synthesis_data.value.id,//音色id
		vedioPersonImg:synthesis_data.value.avatarUrl,//头像
		voicePerson:synthesis_data.value.platformNickname,//名字
		contentLength:textLength.value,
		subtitleJson:download_captions_url.value,
		createType:(synthetic_button_type.value == 2||synthetic_button_type.value == 3)?1:2,//1试听2合成
		emotion:current_emotion.value,//情绪
		languageBoost:current_language.value,//语言
		source:current_source.value//1 普通音色 2我的克隆
	}]
	if(batchCreate_id.value!=''){
		params[0].id=batchCreate_id.value
	}
	// if (synthetic_button_type.value == 2||synthetic_button_type.value == 3) {
	// 	params.map((item) => {
	// 		params[0].isTrialListen = true
	// 	})
	// }
	batchCreateAPI(params).then(res => {
		batchCreate_id.value=res?.data?.workIds[0]
		if (type == 'saveTextHtml') {
			// 关闭loading
			loadingInstance.close()


			if (param == 1) {
				let { code, data } = res

				console.log(batchCreate_id.value)
				if (data?.projectId) {
					window.open(`https://yunjian.peiyinbangshou.com/App?projectId=${data.projectId}&token=${JSON.parse(localStorage.getItem('user'))?.token}`)
				}
			}
			if (res.code != 0) {
				ElMessage({
					message: '保存失败',
					type: 'warning',
				})
			}
		}

	}).catch(err => {
		if (type == 'saveTextHtml') {
			// 发生错误时也要关闭loading
			loadingInstance.close()
		}
		console.log(err)
		// ElMessage({
		// 	message: '操作失败，请稍后重试',
		// 	type: 'error'
		// })
	})
}
// 禁止方向键、删除键和退格键（仅作为示例，可能需要根据实际需求调整）
// const preventDefault = (event) => {
//   event.preventDefault();
// }
onUnmounted(() => { // 清理监听
	// eventBus.off('update-data');
	clearTimeout(timeoutId);
	close_music_div()
	window.removeEventListener('click', handleClickOutside);
	window.removeEventListener('click', handleClickOutside1);
	window.removeEventListener('click', handleClickOutside2)
	clearInterval(full_loading_timer);
});


// 查看拼音按钮
const pinyinBool = ref(false)
const pinyinResult = reactive([])
const pinyinFun = () => {
	// const aa = pinyin(editorContent.value,{ type: 'array' })
	pinyinBool.value = !pinyinBool.value
	if (pinyinBool) {
		// const result = getPinyinWithCharacters(editorContent.value);
		const result = getPinyinWithCharacters(editorRef.value.innerHTML)
		console.log( editorRef.value.innerHTML, 7777);

		pinyinResult.splice(0, pinyinResult.length, ...result);
	}
}
// 鼠标按下事件
const handleKeyUp = (e) => {
	if (e.keyCode == 37 || e.keyCode == 38 || e.keyCode == 39 || e.keyCode == 40) {
		// mouseup()
	}
}
//  多音字弹窗
const showPopover = ref(false)
// 多音字列表
const polyphonicList = ref([])
// 点击多音字时调用的函数
const gethomograph = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	console.log(range, 'range');

	// console.log('llllllll',range.commonAncestorContainer.nextElementSibling)
	// if(range.commonAncestorContainer.nextElementSibling){
	//   showPopover.value = true
	//   return
	// }
	if (range.toString().length !== 1) {
		ElMessage({
			message: '请滑选单个汉字',
			type: 'warning',
		})
		return
	}
	// const resultString = polyphonic(range.toString(),{ type: 'array' });
	const resultString = polyphonic(range.toString(), { toneType: 'num', type: 'array' });
	console.log('range.toString()', range.toString());
	console.log('resultString', resultString); // 输出
	if (resultString[0].length > 0) {
		polyphonicList.value = []
		polyphonicList.value = resultString[0]
		setTimeout(() => {
			showPopover.value = true
		})
	}
}
// 点击多音字中的某个读音，更新多音字读音在页面上
const clickPolyphonic = (item) => {
	restoreSelection()
	getSelectedText(item)
	showPopover.value = false
}




// 创建唯一标识数组  并保存起来  当点击读音替换还是数字符号的时候保存起来，后面替换内容会用到
const createUniqueArr = []
// 记录选中的读音替换以及多音字     以及相对应替换的文字，  以键值对形式
// 保存选择的多音字数组列表
let selectedTextList = reactive([])
// 判断选区后面是否有div的方法
function isDivAfterRange(range) {
	const { endContainer, endOffset } = range;
	let node = endContainer;
	let offset = endOffset;

	// 情况1：选区结束在文本节点中间
	if (node.nodeType === Node.TEXT_NODE) {
		if (offset < node.length) return false; // 文本节点内不可能包含div
		node = node.parentNode;
		offset = Array.from(node.childNodes).indexOf(endContainer) + 1;
	}

	// 情况2：处理元素节点
	let currentNode = node;
	let nextIndex = offset;

	// 创建遍历器
	const walker = document.createTreeWalker(
		document.body,
		NodeFilter.SHOW_ELEMENT,
		{
			acceptNode: n =>
				n.tagName === 'DIV' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
		}
	);

	// 定位到当前节点位置
	walker.currentNode = currentNode;

	// 检查后续兄弟节点
	if (currentNode.childNodes[nextIndex]) {
		const nextNode = currentNode.childNodes[nextIndex];
		if (nextNode.tagName === 'DIV') return true;
	}

	// 遍历后续节点
	while (walker.nextSibling()) {
		if (walker.currentNode.tagName === 'DIV') return true;
	}

	// 深度遍历父节点链
	// while (currentNode.parentNode) {
	//   currentNode = currentNode.parentNode;
	//   const children = Array.from(currentNode.childNodes);
	//   const index = children.indexOf(node) + 1;
	//
	//   if (children.slice(index).some(n => n.tagName === 'DIV')) {
	//     return true;
	//   }
	//   node = currentNode;
	// }

	return false;
}
// // 多音字操作
// editorRef  //  输入文本的ref
const getSelectedText = (item) => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	const hasTextAfter = isDivAfterRange(range);
	console.log('llllllll', hasTextAfter)
	if (hasTextAfter) {
		console.log(range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side'))
		range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = item
		let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
		selectedTextList.map(child => {
			if (child.id == dataId) {
				child.name = `${range.toString()}/(${item})`
			}
		})
		editorRefTriggerInput()
		return
	}
	// if(range.commonAncestorContainer.nextElementSibling){
	//   range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = item
	//   let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
	//   selectedTextList.map(child=>{
	//     if(child.id==dataId){
	//       child.name = `${range.toString()}/(${item})`
	//     }
	//   })
	//   return
	// }

	let id = nanoid(4)
	var tipBox = document.createElement("div");
	tipBox.innerHTML = `<span data-side='${id}'>${item}</span> <button style='font-size:10px; margin-left:5px;'>×</button>`;
	tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("data-attr", "duoyinzi");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	tipBox.style.cursor = "pointer";
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.pointerEvents = "auto";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	// {word:range.toString(),replacement:item}

	selectedTextList.push({
		id: id,
		name: `${range.toString()}/(${item})`
	})
	editorRefTriggerInput()
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();

		// console.log(e.target.parentElement.firstElementChild.getAttribute('data-side'))
		let click_id = e.target.parentElement.firstElementChild.getAttribute('data-side')
		selectedTextList.map((child, index) => {
			if (child.id == click_id) {
				selectedTextList.splice(index, 1)
			}
		})
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// range.deleteContents()
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


// 读音替换弹窗
const aliasPopover = ref(false)
const aliasValue = ref('')  //读音替换input
// 判断有没有选择文字
const isSelectedAlias = () => {
	restoreSelection()
	const selection = window.getSelection();


	const range = selection.getRangeAt(0)
	console.log(range.toString(), 'range');
	// console.log('llllllll',range.toString().length)
	if (range.toString().length === 0) {
		ElMessage({
			message: '请至少选一个汉字',
			type: 'warning',
		})
		return
	}
	aliasPopover.value = true

}

// 点击弹窗中添加读音替换按钮
const addAlias = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		})
		return "";
	}
	var sel = window.getSelection();
	var selectedText = sel.toString().trim();
	if (!selectedText) {
		ElMessage({
			message: '请先选中内容',
			type: 'warning',
		})
		return "";
	}
	var tipBox = document.createElement("div");
	let id = nanoid(4)
	tipBox.innerHTML = "<span class='change'>" + aliasValue.value + "</span> <button style='font-size:14px; margin-left:5px;'>×</button>";
	tipBox.setAttribute("data-type", "alias");
	tipBox.setAttribute("data-key", selectedText);
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid purple";
	tipBox.style.backgroundColor = "#f0e0ff";
	tipBox.style.padding = "5px";
	tipBox.style.borderRadius = "6px";
	tipBox.style.display = "inline-block";
	tipBox.style.marginLeft = "5px";
	tipBox.style.cursor = "pointer";
	tipBox.style.pointerEvents = "auto";

	createUniqueArr.push(id)



	const span = document.createElement('span');
	span.className = id;
	span.id = id;
	range.surroundContents(span);
	// processedHTML.value = getReplacedContent('editor', 'data-shuzi', aliasValue.value);


	editorRefTriggerInput()
	// range.deleteContents();
	var closeBtn = tipBox.querySelector("button");
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		const prevElement = tipBox.previousSibling
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "alias") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);

		range.insertNode(tipBox);
	} else {
		alert("未检测到选中文本");
	}
	// if (!window.aliasMapping) {
	//   window.aliasMapping = {};
	// }

	aliasPopover.value = false
	aliasValue.value = ''
}



// 遍历指定div下所有节点、查找指定类名并替换文本
// function replaceDivText(containerId, targetClass, newText) {
//   // 获取容器div元素‌:ml-citation{ref="1,5" data="citationList"}
//   const container = document.getElementById(containerId);
//   if (!container) return null;
//
//   // 递归遍历所有子节点‌:ml-citation{ref="4,5" data="citationList"}
//   const traverseNodes = (node) => {
//     if (node.nodeType === Node.ELEMENT_NODE) {
//       // 检查当前节点是否包含目标类名‌:ml-citation{ref="1,4" data="citationList"}
//       if (node.classList.contains(targetClass)) {
//         node.textContent = newText; // 替换文本内容‌:ml-citation{ref="3,6" data="citationList"}
//       }
//       // 继续遍历子节点‌:ml-citation{ref="4,5" data="citationList"}
//       Array.from(node.children).forEach(child => traverseNodes(child));
//     }
//   };
//
//   traverseNodes(container);
//   return container.innerHTML; // 返回处理后的HTML内容‌:ml-citation{ref="1,6" data="citationList"}
// }


/**
 * 克隆容器DOM结构并执行安全替换
 * @param {string} containerId 容器元素ID
 * @param {string} targetClass 目标类名
 * @param {string} newText 新文本内容
 * @returns {string} 处理后的HTML字符串
 */

// 根据地址栏id查询需要回显的数据
const queryTextFun = async () => {
	console.log(soundList.value,'queryTextFun');

	if(route.query.extraction&&useAIDubbing.extraction){
		nextTick(()=>{
			handleCallLetter(useAIDubbing.extraction)
		})
	}
	const id = route.query.albumId
	if (id) {
		queryTextAPI({
			userId: JSON.parse(localStorage.getItem('user'))?.userId || '',
			id
		}).then(res => {
			const { code, data = {} } = res || {};
			console.log(data)
			if (code == 0) {
				editorRef.value.innerHTML = data.copywriting
				// selectedTextList = data.heteronym || []
				editorRefTriggerInput()
				useAIDubbing.bgmusic_url=data.ossAudioKey//背景音乐
				captions_url.value=data.subTitleFile//字幕文件
				// selectedTextList=data.heteronym
				traceId.value=data.traceId
				audioUrl.value=data.ossAudioUrl//合成的音频文件
				synthesis_data.value.id=data.vedioId//音色id
				synthesis_data.value.avatarUrl=data.vedioPersonImg//头像
				synthesis_data.value.platformNickname=data.voicePerson//名字
				download_captions_url.value=data.subtitleJson//字幕文件
				let index=soundList.value.findIndex((item)=>item.id==data.vedioId)
				// selectSoundItem(soundList.value[index],index)
				batchCreate_id.value=data.id
				synthetic_button_type.value=(data.createType==1?2:1)
				console.log(batchCreate_id.value,'soundList.value');


			} else {
				ElMessage({
					message: res.msg,
					type: 'warning',
				})
			}
			// console.log(res)
		}).catch(err => {
			// console.log(err)
		})
	}
}

let hasRun = ref(false)
// 示例调用   获取数字替换以及读音替换替换之后的文本内容 （编辑的所有内容）
onMounted(() => {
	doSomething()
})
onActivated(() => {
  doSomething()
})
let doSomething=()=>{
  if (hasRun.value) return
  the_init()
  hasRun.value = true
  rate=getBodyScale()
}
// 示例调用   获取数字替换以及读音替换替换之后的文本内容 （编辑的所有内容）
let the_init=()=>{
	const app = document.getElementById('app');
	if (app) {
		const rect = app.getBoundingClientRect();
		const width = rect.width;
		const height = rect.height;
		// alert(`设备宽度：${window.innerWidth},设备高度：${window.innerWidth}，app宽度：${width},app高度：${height}`)
	}

	console.log('AI配音页面 onMounted 触发 - 每次进入都会执行');

	// editorRef.value.innerHTML = ddd.value

	soundListFun()
	get_Sound_tabs_list()
	window.addEventListener('click', handleClickOutside);
	window.addEventListener('click', handleClickOutside1);
	window.addEventListener('click', handleClickOutside2)
	// 确保始终默认选中"全部"
	selecteVoiceTypeNum.value = '全部'
	slider_input_init()
	full_loading_timer = setInterval(() => {
		full_loading_active_index.value = (full_loading_active_index.value + 1) % 6;
	}, 300); // 300ms切换一次，和动画节奏对应
	init_aiPostion()
	let editor = editorRef.value;
	if (!editor) return;

	// 先解绑，防止重复绑定
	editor.removeEventListener('dragover', handleDragOver);
	editor.removeEventListener('drop', handleDrop);

	editor.addEventListener('dragover', handleDragOver);
	editor.addEventListener('drop', handleDrop);
}
let handleDragOver=(e)=>{
  e.preventDefault();
}
let handleDrop=(e)=>{
  e.preventDefault();
   if (isInserting) return; // 防止重复触发
  isInserting = true;

  const dataTransfer = e.dataTransfer;

  if (dataTransfer.files && dataTransfer.files.length > 0) {
    for (let i = 0; i < dataTransfer.files.length; i++) {
      const file = dataTransfer.files[i];
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(event) {
          insertImageAtCursor(event.target.result);
          isInserting = false; // 插入完成，释放锁
        };
        reader.readAsDataURL(file);
        break; // 只插入第一张图片
      }
    }
  } else {
    const htmlData = dataTransfer.getData('text/html');
    if (htmlData) {
      const div = document.createElement('div');
      div.innerHTML = htmlData;
      const img = div.querySelector('img');
      if (img) {
        insertImageAtCursor(img.src);
      } else {
        const text = dataTransfer.getData('text/plain');
        insertTextAtCursor(text);
      }
    } else {
      const text = dataTransfer.getData('text/plain');
      insertTextAtCursor(text);
    }
    isInserting = false; // 释放锁
  }
  

}
let getBodyScale=()=>{
  const el = document.querySelector('#app');
  if (!el) return 1;
  const transform = getComputedStyle(el).transform;
  if (!transform || transform === 'none') return 1;
  const values = transform.match(/matrix\((.+)\)/)[1].split(', ');
  return parseFloat(values[0]); // scale
}

let init_aiPostion=async()=>{
	await nextTick()
	setTimeout(()=>{
		let editor = document.getElementById('editor');
		let rect = editor.getBoundingClientRect();
		if(window.innerHeight>953){
			aiPostion.value=rect.bottom-25
		}else{
			aiPostion.value=(rect.bottom/(window.innerHeight/953))-(25/(window.innerHeight/953))
		}

		console.log(rate,'rate');
		
		// if(rate<1){
		// 	aiPostion.value=aiPostion.value*rate+65*rate
		// }
		copywriting_aiPostion.value=aiPostion.value
	},400)

}
// 音色标签列表
// 精品和珍享数据列表
const voiceTypeArr = ref([{ name: '全部', selected: false, visible: true, level: 1 }])
// const gendersArr = ref([{name:'sex',children:[{name:'全部',selected:false}]}])
const gendersArr = ref([{ name: '全部', selected: false, visible: true, level: 2 }])
// const secondArr = ref([{name:'scene',children:[{name:'全部',selected:false}]}])
const secondArr = ref([{ name: '全部', selected: false, visible: true, level: 3 }])
// const thirdArr = ref([{name:'three',children:[{name:'全部',selected:false}]}])
const thirdArr = ref([{ name: '全部', selected: false, visible: true, level: 4 }])
// 第三层去重后的数组
const uniqueList = ref()
// 第三层需要展示的数据
const thirdArrList = ref()
//我的音色展示的数据
let myArrList = ref([
	{
		name: '收藏', selected: false, visible: true, level: 0,
	},{
		name: '已购', selected: false, visible: true, level: 0,
	},{
		name: '历史', selected: false, visible: true, level: 0,
	},{
		name: '克隆音色', selected: false, visible: true, level: 0,
	}
])

// 将默认值从'全部'改为'SVIP'
const selecteVoiceTypeNum = ref('全部')
const selecteVoiceType = (item) => {
	clearEmotion()
	selecteVoiceTypeNum.value = item.name

	// 当选择任何一级分类时，自动将下级分类设为"全部"
	selecteGenderNum.value = '全部'
	selecteSecondNum.value = '全部'
	selecteUniqueNum.value = '全部'

	// 如果选择"全部"，重置该级别以下所有标签可见性
	if (item.name === '全部') {
		resetTagsVisibilityByLevel(2) // 重置第2级以下标签
		resetTagsVisibilityByLevel(3) // 重置第3级标签
		resetTagsVisibilityByLevel(4) // 重置第4级标签

		// 确保所有下级分类都显示"全部"项被选中
		selecteGenderNum.value = '全部'
		selecteSecondNum.value = '全部'
		selecteUniqueNum.value = '全部'

		// 确保thirdArrList包含所有可能的标签，而不仅仅是当前筛选的
		thirdArrList.value = [...uniqueList.value]
	}

	// 进行筛选并更新所有下级标签可见性
	let filteredList = performLevelFilter(allSoundList.value, 1, item.name)

	// 如果选择的是"全部"，确保SVIP音色排在最前面
	if (item.name === '全部') {
		// 使用通用排序函数确保SVIP优先
		filteredList = sortVoicesByMembership(filteredList);
	} else {
		// 其他筛选条件下随机排序
		filteredList.sort(() => Math.random() - 0.5);
	}

	soundList.value = filteredList

	// 更新所有下级标签的可见性
	updateLowerLevelTags(filteredList, 1)
}

const selecteGenderNum = ref('全部')
const selecteGender = (item) => {
	clearEmotion()
	selecteGenderNum.value = item.name

	// 当选择第2级分类时，自动将第3级和第4级设为"全部"
	selecteSecondNum.value = '全部'
	selecteUniqueNum.value = '全部'

	// 如果选择"全部"，重置该级别以下所有标签可见性
	if (item.name === '全部') {
		resetTagsVisibilityByLevel(3) // 重置第3级标签
		resetTagsVisibilityByLevel(4) // 重置第4级标签

		// 确保所有下级分类都显示"全部"项被选中
		selecteSecondNum.value = '全部'
		selecteUniqueNum.value = '全部'

		// 确保thirdArrList包含所有可能的标签
		thirdArrList.value = [...uniqueList.value]
	}

	// 进行筛选并更新所有下级标签可见性
	let filteredList = performLevelFilter(allSoundList.value, 2, item.name)

	// 根据选择的分类决定排序方式
	if (selecteVoiceTypeNum.value === '全部') {
		// 当选择"全部"时，使用甄享优先排序
		soundList.value = sortVoicesByMembership(filteredList)
	} else {
		// 其他筛选条件下随机排序
		soundList.value = filteredList.sort(() => Math.random() - 0.5)
	}

	// 更新所有下级标签的可见性
	updateLowerLevelTags(filteredList, 2)
}

const selecteSecondNum = ref('全部')
const selecteSecond = (item, index) => {
	clearEmotion()
	selecteUniqueNum.value = '全部'
	selecteSecondNum.value = item.name

	// 如果选择"全部"，重置该级别以下所有标签可见性
	if (item.name === '全部') {
		resetTagsVisibilityByLevel(4) // 重置第4级标签
		thirdArrList.value = [...uniqueList.value]

		// 确保第4级显示为"全部"
		selecteUniqueNum.value = '全部'
	} else {
		// 更新第4级标签列表时仍然需要确保列表显示正确的标签
		thirdOriginArr.value[index].recommend_tags.map(item => {
			item.name = item.recommend_tags
		})
		// 确保第一个选项是"全部"，然后添加所有相关标签
		thirdArrList.value = [{ name: '全部', selected: false, visible: true, level: 4 }, ...thirdOriginArr.value[index].recommend_tags]
	}

	// 进行筛选并更新所有下级标签可见性
	let filteredList = performLevelFilter(allSoundList.value, 3, item.name)

	// 根据选择的分类决定排序方式
	if (selecteVoiceTypeNum.value === '全部') {
		// 当选择"全部"时，使用甄享优先排序
		soundList.value = sortVoicesByMembership(filteredList)
	} else {
		// 其他筛选条件下随机排序
		soundList.value = filteredList.sort(() => Math.random() - 0.5)
	}

	// 更新所有下级标签的可见性
	updateLowerLevelTags(filteredList, 3)
}

const selecteUniqueNum = ref('全部')
const selecteUnique = (item) => {
	clearEmotion()
	// 修改了第四级选择逻辑，确保与商配页面行为一致
	// 选择第四级标签时，不会对第三级标签的可见性造成影响
	// 这样用户可以按照第三级选择的条件进行浏览，不会因为选择第四级而导致第三级的标签发生变化
	selecteUniqueNum.value = item.name

	// 在第四级（最后一级）使用不同的处理方式以保持上级标签不变
	// 不调用普通的filter_listFun，而是使用特殊的过滤方法

	// 直接根据当前所有筛选条件过滤数据
	let filteredList = allSoundList.value.filter(sound => {
		// 检查会员类型筛选
		if (selecteVoiceTypeNum.value !== '全部') {
			if (selecteVoiceTypeNum.value === 'SVIP' && sound.membershipGrade !== 'SVIP') {
				return false;
			} else if (selecteVoiceTypeNum.value === 'VIP' && sound.membershipGrade !== 'VIP') {
				return false;
			} else if (!['SVIP', 'VIP'].includes(selecteVoiceTypeNum.value) && sound.grade !== selecteVoiceTypeNum.value) {
				return false;
			}
		}

		// 检查性别/年龄筛选
		if (selecteGenderNum.value !== '全部') {
			const genderInfo = (sound.gender || '').split('、');
			if (['男', '女'].includes(selecteGenderNum.value)) {
				if (!genderInfo.includes(selecteGenderNum.value)) {
					return false;
				}
			} else if (['青年', '中年', '老年', '少年', '中老年'].includes(selecteGenderNum.value)) {
				if (sound.ageGroup) {
					if (sound.ageGroup !== selecteGenderNum.value) {
						return false;
					}
				} else {
					if (!genderInfo.some(info => info.includes(selecteGenderNum.value))) {
						return false;
					}
				}
			}
		}

		// 检查场景类别筛选
		if (selecteSecondNum.value !== '全部' && sound.sceneCategory !== selecteSecondNum.value) {
			return false;
		}

		// 检查推荐标签筛选
		if (selecteUniqueNum.value !== '全部') {
			const tags = typeof sound.recommendTags === 'string'
				? sound.recommendTags.split(',')
				: Array.isArray(sound.recommendTags)
					? sound.recommendTags
					: [sound.recommendTags];

			if (!tags.some(tag => tag === selecteUniqueNum.value || tag.includes(selecteUniqueNum.value))) {
				return false;
			}
		}

		// 检查搜索条件
		if (input_search.value && input_search.value.trim() !== '') {
			const keyword = input_search.value.toLowerCase();
			const platformNickname = (sound.platformNickname || '').toLowerCase();
			const emotionTags = (sound.emotionTags || '').toLowerCase();
			const sceneCategory = (sound.sceneCategory || '').toLowerCase();

			if (!platformNickname.includes(keyword) &&
				!emotionTags.includes(keyword) &&
				!sceneCategory.includes(keyword)) {
				return false;
			}
		}

		return true;
	});

	// 如果选择的是"全部"，确保SVIP音色排在最前面
	if (selecteVoiceTypeNum.value === '全部') {
		filteredList = sortVoicesByMembership(filteredList);
	} else {
		// 其他筛选条件下随机排序
		filteredList.sort(() => Math.random() - 0.5);
	}

	// 更新展示的音色列表
	soundList.value = filteredList;

	// 关键：不更新上级标签的可见性，保持当前所有标签的状态不变
	// 这是实现与CommercialDubbing相同行为的关键
}

// 根据当前级别进行筛选，返回筛选后的结果
const performLevelFilter = (items, level, value) => {
	if (!items || items.length === 0 || value === '全部') {
		// 如果选择了"全部"或没有数据，只应用更高级别的筛选条件
		return filterByHigherLevels(items, level)
	}

	return items.filter(item => {
		// 首先检查高级别的筛选条件
		if (!passesHigherLevelFilters(item, level)) {
			return false
		}

		// 然后检查当前级别
		switch (level) {
			case 1: // 音色类型级别
				// 针对SVIP和VIP特殊处理
				if (value === 'SVIP' && item.membershipGrade !== 'SVIP') {
					return false
				} else if (value === 'VIP' && item.membershipGrade !== 'VIP') {
					return false
				}
				// 针对其他分类，使用grade字段
				else if (!['SVIP', 'VIP'].includes(value) && item.grade !== value) {
					return false
				}
				break

			case 2: // 性别/年龄级别
				const genderAgeValue = value

				// 检查性别字段
				if (['男', '女'].includes(genderAgeValue)) {
					// 有些数据可能是"男、青年"格式，需要分割检查
					const genderInfo = (item.gender || '').split('、')
					if (!genderInfo.includes(genderAgeValue)) {
						return false
					}
				}
				// 检查年龄段
				else if (['青年', '中年', '老年', '少年', '中老年'].includes(genderAgeValue)) {
					// 优先检查ageGroup字段
					if (item.ageGroup) {
						if (item.ageGroup !== genderAgeValue) {
							return false
						}
					}
					// 如果没有ageGroup字段，尝试从gender字段中提取
					else {
						const genderInfo = (item.gender || '').split('、')
						if (!genderInfo.some(info => info.includes(genderAgeValue))) {
							return false
						}
					}
				}
				break

			case 3: // 场景分类级别
				if (item.sceneCategory !== value) {
					return false
				}
				break
		}

		return true
	})
}

// 检查是否通过所有更高级别的筛选
const passesHigherLevelFilters = (item, currentLevel) => {
	// 检查第1级（如果当前级别大于1）
	if (currentLevel > 1 && selecteVoiceTypeNum.value !== '全部') {
		// SVIP/VIP特殊处理
		if (selecteVoiceTypeNum.value === 'SVIP' && item.membershipGrade !== 'SVIP') {
			return false
		} else if (selecteVoiceTypeNum.value === 'VIP' && item.membershipGrade !== 'VIP') {
			return false
		}
		// 其他分类使用grade字段
		else if (!['SVIP', 'VIP'].includes(selecteVoiceTypeNum.value) && item.grade !== selecteVoiceTypeNum.value) {
			return false
		}
	}

	// 检查第2级（如果当前级别大于2）
	if (currentLevel > 2 && selecteGenderNum.value !== '全部') {
		const genderAgeValue = selecteGenderNum.value

		// 检查性别
		if (['男', '女'].includes(genderAgeValue)) {
			const genderInfo = (item.gender || '').split('、')
			if (!genderInfo.includes(genderAgeValue)) {
				return false
			}
		}
		// 检查年龄段
		else if (['青年', '中年', '老年', '少年', '中老年'].includes(genderAgeValue)) {
			if (item.ageGroup) {
				if (item.ageGroup !== genderAgeValue) {
					return false
				}
			} else {
				const genderInfo = (item.gender || '').split('、')
				if (!genderInfo.some(info => info.includes(genderAgeValue))) {
					return false
				}
			}
		}
	}

	// 检查第3级（如果当前级别大于3）
	if (currentLevel > 3 && selecteSecondNum.value !== '全部') {
		if (item.sceneCategory !== selecteSecondNum.value) {
			return false
		}
	}

	return true
}

// 只根据高于指定级别的条件进行筛选
const filterByHigherLevels = (items, belowLevel) => {
	if (!items || items.length === 0) return []

	return items.filter(item => passesHigherLevelFilters(item, belowLevel))
}

// 根据选中条件过滤音色列表
const filter_listLoading = ref(false)
const filter_listFun = () => {
	filter_listLoading.value = true

	try {
		// 判断是否有搜索关键词
		if (input_search.value && input_search.value.trim() !== '') {
			// 如果有搜索关键词，使用performSearch进行搜索
			let filteredList = performSearch(allSoundList.value)

			// 如果选择的是"全部"，按SVIP优先排序
			if (selecteVoiceTypeNum.value === '全部') {
				// 使用通用排序函数确保SVIP优先
				filteredList = sortVoicesByMembership(filteredList);
			} else {
				// 其他筛选条件下随机排序
				filteredList.sort(() => Math.random() - 0.5);
			}

			soundList.value = filteredList
		} else {
			// 如果没有搜索关键词，使用当前筛选条件过滤
			let filteredList = filterByCurrentConditions(allSoundList.value)

			// 如果选择的是"全部"，按SVIP优先排序
			if (selecteVoiceTypeNum.value === '全部') {
				// 使用通用排序函数确保SVIP优先
				filteredList = sortVoicesByMembership(filteredList);
			} else {
				// 其他筛选条件下随机排序
				filteredList.sort(() => Math.random() - 0.5);
			}

			soundList.value = filteredList
		}

		// 确保始终更新所有标签可见性
		// 检查各级分类是否为"全部"
		if (selecteVoiceTypeNum.value === '全部') {
			// 第一级为"全部"，确保所有下级也是"全部"
			selecteGenderNum.value = '全部'
			selecteSecondNum.value = '全部'
			selecteUniqueNum.value = '全部'
			// 重置所有标签
			resetTagsVisibility()
			// 确保thirdArrList包含所有可能的标签
			thirdArrList.value = [...uniqueList.value]
		} else if (selecteUniqueNum.value !== '全部') {
			// 保持当前状态，但确保第4级可见性是正确的
			updateLevelTags(soundList.value, 4);
		} else if (selecteSecondNum.value !== '全部') {
			// 如果第3级有选择，则更新第4级标签
			updateLowerLevelTags(soundList.value, 3)
		} else if (selecteGenderNum.value !== '全部') {
			// 如果第2级有选择，则更新第3、4级标签
			updateLowerLevelTags(soundList.value, 2)
		} else if (selecteVoiceTypeNum.value !== '全部') {
			// 如果第1级有选择，则更新第2、3、4级标签
			updateLowerLevelTags(soundList.value, 1)
		} else if (input_search.value && input_search.value.trim() !== '') {
			// 如果有搜索关键词但没有选择任何分类，更新所有标签
			updateAllTagsBasedOnSearch(soundList.value)
		} else {
			// 如果没有任何筛选条件，重置所有标签
			resetTagsVisibility()
		}
	} catch (err) {
		console.error('本地筛选音色失败:', err)
		soundList.value = []
	} finally {
		filter_listLoading.value = false
	}
}

// 根据当前选中的筛选条件过滤音色列表
const filterByCurrentConditions = (items) => {
	if (!items || items.length === 0) return []

	return items.filter(item => {
		// 第1级：音色类型
		if (selecteVoiceTypeNum.value !== '全部') {
			// 针对SVIP和VIP特殊处理
			if (selecteVoiceTypeNum.value === 'SVIP' && item.membershipGrade !== 'SVIP') {
				return false
			} else if (selecteVoiceTypeNum.value === 'VIP' && item.membershipGrade !== 'VIP') {
				return false
			}
			// 针对其他分类，使用grade字段
			else if (!['SVIP', 'VIP'].includes(selecteVoiceTypeNum.value) && item.grade !== selecteVoiceTypeNum.value) {
				return false
			}
		}

		// 第2级：性别/年龄
		if (selecteGenderNum.value !== '全部') {
			const genderAgeValue = selecteGenderNum.value

			// 检查性别字段
			if (['男', '女'].includes(genderAgeValue)) {
				// 有些数据可能是"男、青年"格式，需要分割检查
				const genderInfo = (item.gender || '').split('、')
				if (!genderInfo.includes(genderAgeValue)) {
					return false
				}
			}
			// 检查年龄段
			else if (['青年', '中年', '老年', '少年', '中老年'].includes(genderAgeValue)) {
				// 优先检查ageGroup字段
				if (item.ageGroup) {
					if (item.ageGroup !== genderAgeValue) {
						return false
					}
				}
				// 如果没有ageGroup字段，尝试从gender字段中提取
				else {
					const genderInfo = (item.gender || '').split('、')
					if (!genderInfo.some(info => info.includes(genderAgeValue))) {
						return false
					}
				}
			}
		}

		// 第3级：场景分类
		if (selecteSecondNum.value !== '全部' && item.sceneCategory !== selecteSecondNum.value) {
			return false
		}

		// 第4级：推荐标签
		if (selecteUniqueNum.value !== '全部') {
			// 有些recommendTags可能是数组或逗号分隔的字符串
			const tags = typeof item.recommendTags === 'string'
				? item.recommendTags.split(',')
				: Array.isArray(item.recommendTags)
					? item.recommendTags
					: [item.recommendTags]

			if (!tags.some(tag => tag === selecteUniqueNum.value || tag.includes(selecteUniqueNum.value))) {
				return false
			}
		}

		return true
	})
}




const thirdOriginArr = ref([])
const get_Sound_tabs_list = () => {
	Sound_tabs_listApi({ tts: '4' }).then(res => {
		// console.log('456',res)
		if (res.code == 0) {
			// console.log('798798',res.data)
			let { data } = res
			let { genders, sceneMetadata, grade } = data
			// genders
			// 精品和珍享数组
			grade.map((item, index) => {
				voiceTypeArr.value.push({
					name: item.grade,
					selected: false,
					visible: true,
					level: 1
				})
			})

			// 排序voiceTypeArr，确保"甄选"在"精选"前面
			voiceTypeArr.value.sort((a, b) => {
				// 保持"全部"始终在最前面
				if (a.name === '全部') return -1;
				if (b.name === '全部') return 1;
				// 将"精选"放到"甄选"后面
				if (a.name === '精选' && b.name === '甄选') return 1;
				if (a.name === '甄选' && b.name === '精选') return -1;
				// 其他情况保持原来的顺序
				return 0;
			});

			// 第一层数组
			genders.map((item, index) => {
				gendersArr.value.push({
					name: item.gender,
					selected: false,
					visible: true,
					level: 2
				})
			})

			// 在这里重新排序gendersArr，确保"男"和"女"排在前面
			// 创建一个排序函数，将"男"和"女"排在前面，其他保持原有顺序
			gendersArr.value.sort((a, b) => {
				// "全部"始终保持在最前面
				if (a.name === '全部') return -1;
				if (b.name === '全部') return 1;
				// "男"排第二位
				if (a.name === '男') return -1;
				if (b.name === '男') return 1;
				// "女"排第三位
				if (a.name === '女') return -1;
				if (b.name === '女') return 1;
				// 其他项保持原有顺序
				return 0;
			});

			// 这里不需要传统商配标签,后端已处理
			// sceneMetadata.shift()
			thirdOriginArr.value = sceneMetadata
			// Object.assign(thirdOriginArr, JSON.parse(JSON.stringify(sceneMetadata)))
			thirdOriginArr.value.map(item => {
				item.recommend_tags.map(child => {
					child.selected = false
					child.visible = true
					child.level = 4
				})
			})
			// console.log('originArr',thirdOriginArr.value)
			// 第二层数组
			sceneMetadata.map(item => {
				item.recommend_tags.map(child => {
					// 第三层数组
					thirdArr.value.push({
						name: child.recommend_tags,
						selected: false,
						visible: true,
						level: 4
					})
				})
				// 第二层数组
				secondArr.value.push({
					name: item.scene_category,
					selected: false,
					visible: true,
					level: 3
				})
			})

			// console.log('ooo',thirdArr.value)
			uniqueList.value = thirdArr.value.filter(
				(item, index) => thirdArr.value.findIndex(i => i.name === item.name) === index
			);
			// console.log('uniqueList',uniqueList)
			thirdArrList.value = uniqueList.value

			// 移除自动选择SVIP的代码
			// 确保默认选中"全部"
			console.log("当前选中的标签: ", selecteVoiceTypeNum.value);
		} else {

		}
	})
}


// 查询音色列表函数
// 音色列表顶部筛列表


const categories = ref([])



let insert_emotion_ref=ref(null)
let  currentItem = ref(null)//当前表情显示项
let extraContentIndex = ref(null)//当前表情显示项
let { setRef, insert_emotion } = useInsertEmotion({
	targetElements,
	insert_emotion_ref,
	currentItem,
	extraContentIndex
})
// 点击列表中某一项
let SoundItemId = ref('')
let choose_timbre = ref({})

const selectSoundItem = async(item,index,e) => {
  await nextTick()
  let el = targetElements.value[index];
  let rect = '';
  let centerX=''
  let style=''
  console.log(el,'selectSoundItem');

      if (el) {
        rect = el.getBoundingClientRect();
		style= window.getComputedStyle(el);
        centerX = rect.left + (rect.width / 2)-Math.floor((parseFloat(style.marginRight))/2)
      } else {
        console.warn('元素引用不存在');
      }

	console.log(item, 999);
	current_language.value='auto'
	current_emotion.value='neutral'
	// if()
	choose_timbre.value = item
	SoundItemId.value = item.voiceName
	//恢复语速和语调默认值
	speechValue.value = 1
	//语调
	intonationValue.value = 0

	targetElements.value=[]
	await nextTick()
	insert_emotion(item, index,centerX)

}

let soundList = ref([])
const originalSoundList = ref([])
// 新增变量，用于存储所有音色数据，避免重复请求
const allSoundList = ref([])

// 本地筛选函数，根据筛选条件过滤allSoundList中的数据
const filterSoundListLocally = () => {
	if (!allSoundList.value || allSoundList.value.length === 0) {
		soundList.value = []
		// 在搜索时不重置标签显示，以保持当前筛选状态
		return
	}

	// 将筛选逻辑整合到这里，使其与单独的层级筛选函数保持一致
	let filteredList = allSoundList.value.filter(item => {
		// 检查音色类型 - 使用membershipGrade进行SVIP/VIP筛选
		if (selecteVoiceTypeNum.value !== '全部') {
			// 针对SVIP和VIP特殊处理
			if (selecteVoiceTypeNum.value === 'SVIP' && item.membershipGrade !== 'SVIP') {
				return false
			} else if (selecteVoiceTypeNum.value === 'VIP' && item.membershipGrade !== 'VIP') {
				return false
			}
			// 针对其他分类，仍然使用grade字段
			else if (!['SVIP', 'VIP'].includes(selecteVoiceTypeNum.value) && item.grade !== selecteVoiceTypeNum.value) {
				return false
			}
		}

		// 检查性别和年龄段 - 使用gender或ageGroup字段
		if (selecteGenderNum.value !== '全部') {
			const genderAgeValue = selecteGenderNum.value;

			// 检查性别字段
			if (['男', '女'].includes(genderAgeValue)) {
				// 有些数据可能是"男、青年"格式，需要分割检查
				const genderInfo = (item.gender || '').split('、');
				if (!genderInfo.includes(genderAgeValue)) {
					return false;
				}
			}
			// 检查年龄段
			else if (['青年', '中年', '老年', '少年', '中老年'].includes(genderAgeValue)) {
				// 优先检查ageGroup字段
				if (item.ageGroup) {
					if (item.ageGroup !== genderAgeValue) {
						return false;
					}
				}
				// 如果没有ageGroup字段，尝试从gender字段中提取
				else {
					const genderInfo = (item.gender || '').split('、');
					if (!genderInfo.some(info => info.includes(genderAgeValue))) {
						return false;
					}
				}
			}
		}

		// 检查场景分类 - sceneCategory字段
		if (selecteSecondNum.value !== '全部' && item.sceneCategory !== selecteSecondNum.value) {
			return false
		}

		// 检查推荐标签 - recommendTags字段
		if (selecteUniqueNum.value !== '全部') {
			// 有些recommendTags可能是数组或逗号分隔的字符串
			const tags = typeof item.recommendTags === 'string'
				? item.recommendTags.split(',')
				: Array.isArray(item.recommendTags)
					? item.recommendTags
					: [item.recommendTags];

			if (!tags.some(tag => tag === selecteUniqueNum.value || tag.includes(selecteUniqueNum.value))) {
				return false;
			}
		}

		// 检查搜索关键词 - 主要是平台昵称
		if (input_search.value && input_search.value.trim() !== '') {
			const keyword = input_search.value.toLowerCase()
			const platformNickname = (item.platformNickname || '').toLowerCase()

			if (!platformNickname.includes(keyword)) {
				return false
			}
		}

		return true
	})

	// 根据选择的分类决定排序方式
	if (selecteVoiceTypeNum.value === '全部') {
		// 当选择"全部"时，使用甄享优先排序
		soundList.value = sortVoicesByMembership(filteredList)
	} else {
		// 其他筛选条件下随机排序，与原代码保持一致
		soundList.value = filteredList.sort(() => Math.random() - 0.5)
	}
}

const soundListFun = async() => {
	SoundItemId.value = ''
	cancelNavRequest.value = axios.CancelToken.source();
	queryTextFun()
	if(route.query.clone&&soundStore.cloneData){
		show_list_nav_current.value=2
		await selectetMyClick('克隆音色')
		let index = soundList.value.findIndex((item) => item.id == route.query.voiceId)
			console.log(index,soundList.value,'克隆音色');
		if (index >= 0) {
			SoundItemId.value = soundList.value[index].voiceName
			scrollToElement()


			selectSoundItem(soundList.value[index],index)
			soundStore.setCloneData(null)
		}
		return
	}
	Sound_ListApi({ tts: '4',userId:JSON.parse(localStorage.getItem('user'))?.userId || '',cancelToken: cancelNavRequest.value.token }).then((res) => {
		// console.log('456',res)
		if (res.code == 0) {
			if (res.data.length > 0) {
				res.data.map((item) => {
					item.isPlay = false
					// item.bookmark=false
				})
			}

			// 保存所有数据到allSoundList以便本地筛选
			allSoundList.value = JSON.parse(JSON.stringify(res.data))
			Object.assign(originalSoundList.value, JSON.parse(JSON.stringify(res.data)))

			// 处理音色列表，确保SVIP音色排在前面
			let processedData = JSON.parse(JSON.stringify(res.data));

			// 使用更严格的排序逻辑
			processedData = sortVoicesByMembership(processedData);

			// 更新显示列表
			soundList.value = processedData;

			if (route.query.choose && soundStore.chooseData) {
				let index = soundList.value.findIndex((item) => item.id == soundStore.chooseData.id)

				if (index >= 0) {
					SoundItemId.value = soundList.value[index].voiceName
					scrollToElement()

					selectSoundItem(soundList.value[index],index)
					soundStore.setChooseData(null)

				} else {
					ElMessage({ message: '未在ai配音中找到该音色!', type: 'warning' });
				}
			}

			// console.log('ppp',soundList)
		} else {
			allSoundList.value = []
			soundList.value = []
			originalSoundList.value = []
		}

	}).catch(err => {
		console.log(err)
		allSoundList.value = []
		soundList.value = []
		originalSoundList.value = []
	})
}

// 按会员等级对音色进行排序的通用函数
// 确保甄享（SVIP）音色排在前面，精品（VIP及其他）音色排在后面
const sortVoicesByMembership = (voices) => {
	// 复制数组以避免直接修改原数组
	const sortedVoices = [...voices];

	// 按音色标签分组：甄享（SVIP）、其他所有音色
	const zhengxiangVoices = sortedVoices.filter(item => item.membershipGrade === 'SVIP');
	const otherVoices = sortedVoices.filter(item => item.membershipGrade !== 'SVIP');

	// 创建通用的recommendDegree比较函数
	const compareByRecommendDegree = (a, b) => {
		// 获取recommendDegree，没有则默认为999999（排在最后）
		const levelA = a.recommendDegree || 999999;
		const levelB = b.recommendDegree || 999999;
		// 升序排列（值小的排前面）
		return levelA - levelB;
	};

	// 对甄享组（SVIP）内部进行排序
	zhengxiangVoices.sort((a, b) => {
		return compareByRecommendDegree(a, b);
	});

	// 对其他音色进行排序（包括VIP和其他等级）
	otherVoices.sort((a, b) => {
		// 首先按membershipGrade排序，VIP优于其他等级
		if (a.membershipGrade === 'VIP' && b.membershipGrade !== 'VIP') return -1;
		if (a.membershipGrade !== 'VIP' && b.membershipGrade === 'VIP') return 1;

		// 在相同membershipGrade下，按grade属性排序
		if (a.grade && b.grade && a.grade !== b.grade) {
			// 例如：让"精选"排在"甄选"前面
			if (a.grade === '精品' && b.grade === '甄享') return -1;
			if (a.grade === '甄享' && b.grade === '精品') return 1;
		}

		// 同一grade下按recommendDegree排序
		if (a.grade === b.grade) {
			return compareByRecommendDegree(a, b);
		}

		return 0; // 保持原有顺序
	});

	// 按甄享（SVIP） > 其他音色的顺序合并数组
	return [...zhengxiangVoices, ...otherVoices];
}

// 专门用于搜索场景的排序函数，优先显示完全匹配的结果
// 排序优先级：完全匹配+SVIP > 完全匹配+其他 > 模糊匹配+SVIP > 模糊匹配+其他
const sortVoicesBySearchMatch = (voices, searchKeyword) => {
	if (!searchKeyword || !searchKeyword.trim()) {
		// 如果没有搜索关键词，回退到普通排序
		return sortVoicesByMembership(voices);
	}

	const keyword = searchKeyword.toLowerCase().trim();

	// 按搜索匹配精确度分组
	const exactMatches = []; // 完全匹配
	const partialMatches = []; // 模糊匹配

	voices.forEach(voice => {
		const platformNickname = (voice.platformNickname || '').toLowerCase();
		const emotionTags = (voice.emotionTags || '').toLowerCase();
		const sceneCategory = (voice.sceneCategory || '').toLowerCase();

		// 判断是否为完全匹配（平台昵称完全等于搜索关键词）
		const isExactMatch = platformNickname === keyword;

		// 判断是否为模糊匹配（包含搜索关键词）
		const isPartialMatch = platformNickname.includes(keyword) ||
		                      emotionTags.includes(keyword) ||
		                      sceneCategory.includes(keyword);

		if (isExactMatch) {
			exactMatches.push(voice);
		} else if (isPartialMatch) {
			partialMatches.push(voice);
		}
	});

	// 对完全匹配组内部按会员等级排序
	const sortedExactMatches = sortVoicesByMembership(exactMatches);

	// 对模糊匹配组内部按会员等级排序
	const sortedPartialMatches = sortVoicesByMembership(partialMatches);

	// 返回：完全匹配在前，模糊匹配在后
	return [...sortedExactMatches, ...sortedPartialMatches];
}

const processedHTML = ref(null)
// getReplacedContent('editor', 'data-shuzi', '新文本');
function getReplacedContent(containerId, targetClass, newText) {
	// 获取原始容器元素并克隆副本‌:ml-citation{ref="1,6" data="citationList"}
	const originalDiv = document.getElementById(containerId);
	if (!originalDiv) return null;
	const clonedDiv = originalDiv.cloneNode(true);

	// 递归遍历克隆节点‌:ml-citation{ref="4,8" data="citationList"}
	const traverseAndReplace = (node) => {
		if (node.nodeType === Node.ELEMENT_NODE) {
			// 类名匹配检测‌:ml-citation{ref="1,3" data="citationList"}
			if (node.id == targetClass) {
				// if (node.classList?.contains(targetClass)) {
				node.textContent = newText; // 安全文本替换‌:ml-citation{ref="6,7" data="citationList"}
			}
			// 深度遍历子元素‌:ml-citation{ref="4,8" data="citationList"}
			Array.from(node.children).forEach(child => traverseAndReplace(child));
		}
	};

	traverseAndReplace(clonedDiv);
	return clonedDiv.innerHTML; // 返回处理后的副本内容‌:ml-citation{ref="6,8" data="citationList"}
}





// 数字符号
const figurePopover = ref(false)
const figureList = reactive([
	{ title: '数值', num: '四五四五四' },
	{ title: '数值', num: '四十五' },
])

const isNanFun = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	if (!/^\d+$/.test(range.toString())) {
		ElMessage({
			message: '请选择纯数字文本',
			type: 'warning',
		})
		return
	} else {
		figureList[0].num = numberToPinyin(range.toString())
		figureList[1].num = numberToChinese(range.toString())
		figurePopover.value = true
	}
}

const clickFigure = (item) => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// 数字符号弹窗
	let id = nanoid(4)
	var tipBox = document.createElement("div");
	tipBox.innerHTML = "<span class='change'>" + item.num + "</span> <button style='font-size:14px; margin-left:5px;'>×</button>";
	tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("data-attr", "shuzifuhao");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid green";
	tipBox.style.backgroundColor = "#e0ffe0";
	tipBox.style.padding = "5px";
	tipBox.style.borderRadius = "6px";
	// tipBox.style.fontSize = "14px";
	tipBox.style.display = "inline-block";
	tipBox.style.marginLeft = "5px";
	tipBox.style.cursor = "pointer";
	tipBox.style.pointerEvents = "auto";
	createUniqueArr.push(id)
	const span = document.createElement('span');
	span.className = id;
	span.id = id;
	range.surroundContents(span);
	editorRefTriggerInput()
	// processedHTML.value = getReplacedContent('editor', 'data-shuzi', item.num);
	// console.log('processedHTML.value',processedHTML.value)
	// debugger
	var closeBtn = tipBox.querySelector("button");
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();

		// // 获取父节点及所有子节点‌:ml-citation{ref="8" data="citationList"}
		// const wrapper = document.createElement('span');
		// const parent = wrapperNode.parentNode;
		// const children = Array.from(wrapperNode.childNodes);
		//
		// // 在父节点中插入原始内容‌:ml-citation{ref="1,3" data="citationList"}
		// children.forEach(child => {
		//   parent.insertBefore(child, wrapperNode);
		// });
		//
		// // 移除包裹节点‌:ml-citation{ref="8" data="citationList"}
		// parent.removeChild(wrapperNode);


		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		const prevElement = tipBox.previousSibling
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
		tipBox.parentNode.removeChild(span)

	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	figurePopover.value = false
}

// 数字到中文字符映射
const numberMap = {
	'0': '零',
	'1': '一',
	'2': '二',
	'3': '三',
	'4': '四',
	'5': '五',
	'6': '六',
	'7': '七',
	'8': '八',
	'9': '九'
};
// 直接拼音转写
function numberToPinyin(num) {
	const numStr = String(num);
	let result = '';
	for (const char of numStr) {
		result += numberMap[char];
	}
	return result;
}
function numberToChinese(num) {
	const numberMap = {
		'0': '零',
		'1': '一',
		'2': '二',
		'3': '三',
		'4': '四',
		'5': '五',
		'6': '六',
		'7': '七',
		'8': '八',
		'9': '九'
	};

	const unitMap = {
		0: '',
		1: '十',
		2: '百',
		3: '千',
		4: '万',
		5: '十万',
		6: '百万',
		7: '千万',
		8: '亿'
	};

	let numStr = String(num);
	let result = '';
	let length = numStr.length;

	for (let i = 0; i < length; i++) {
		const currentDigit = numStr[i];
		const position = length - i - 1;

		if (currentDigit === '0') {
			// 当前位是零，且不在最高位
			if (i !== 0) {
				result += numberMap[currentDigit];
			}
			continue;
		}

		let part = '';

		part += numberMap[currentDigit];

		if (position > 0) {
			part += unitMap[position] || '';
		}

		result += part;
	}

	return result === '' ? '零' : result;
}









// 停顿弹窗
const stopPopover = ref(false)
const standstillList = reactive([
	'200', '400', '600', '800', '1000',
])
const clickStandstill = (item) => {
	// console.log(item)
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	var tipBox = document.createElement("div");
	tipBox.innerHTML = "<span>" + item + "m</span> <button data-type='number' style='font-size:10px;'>×</button>";
	// tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("data-attr", "tingdun");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.pointerEvents = "auto";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	editorRefTriggerInput()
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// 关闭弹窗
	stopPopover.value = false
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


// 弹窗位置
let topPopover = ref(0)
let leftPopover = ref(0)
const selection = window.getSelection();
const mouseup = (e) => {
	if (selection.rangeCount > 0) {
		// 动态改变多音字弹窗位置
		topPopover.value = e.pageY + 16
		leftPopover.value = e.pageX - 20
		// range.surroundContents(span);
		// document.getElementById('editor').appendChild(span)
		// range.insertNode(span); // 将带有样式的span重新插入到文档中。
		// // 重新设置选择范围以包含整个span。
		// range.setStartAfter(span); // 移动到span之后。
		// range.setEndAfter(span); // 同样移动到span之后。
		// selection.removeAllRanges(); // 清除之前的选中范围。
		// selection.addRange(range); // 设置新的选中范围。
	}
}

// 移除可编辑div下span中存在的文字并把文字移到文本节点中
// function clearHighlights() {
//   document.querySelectorAll('#editor span').forEach(span => {
//     span.outerHTML = span.innerHTML;
//   });
// }
// 从我的空间调过来的时候显示文字的删除操作
const feedBackEditorOperate = (event) => {
	// 读音替换
	let attr = event.target.parentNode.getAttribute('data-type')
	// 多音字
	let dataAttr = event.target.parentNode.getAttribute('data-attr')
	const prevElement = event.target.parentNode.previousSibling
	const parentNode = event.target.parentNode
	console.log('event', event.target.tagName, parentNode)
	// 用于元素回显时，点击叉号事件,删除读音替换元素
	if (event.target.tagName === 'BUTTON' && attr == 'alias') {
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		if (event.target.parentNode.previousSibling.innerHTML === '') {
			event.target.parentNode.previousSibling.remove()
		}
		event.target.parentNode.remove()
	}
	//删除多音字元素    回显时
	if (event.target.tagName === 'BUTTON' && dataAttr == 'duoyinzi') {
		let click_id = event.target.parentElement.firstElementChild.getAttribute('data-side')
		selectedTextList.map((child, index) => {
			if (child.id == click_id) {
				selectedTextList.splice(index, 1)
			}
		})
		event.target.parentNode.remove()
	}
	if (event.target.tagName === 'BUTTON' && (dataAttr == 'tingdun' || dataAttr == 'yinxiao')) {
		event.target.parentNode.remove()
	}
	// 数字符号时，删除
	if (event.target.tagName === 'BUTTON' && attr == 'shuzifuhao') {
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		if (event.target.parentNode.previousSibling.innerHTML === '') {
			event.target.parentNode.previousSibling.remove()
		}
		event.target.parentNode.remove()
	}



}





let selectionRange; // 存储选区范围以便恢复
const handleClick = (event) => {
	show_suspended_toolbar.value=true
	const selection = window.getSelection();
	selectionRange = selection.getRangeAt(0).cloneRange(); // 保存当前选区范围
	feedBackEditorOperate(event)
	// 你可以在这里添加其他逻辑，例如阻止默认行为等。
	event.preventDefault();
}
// 恢复选取
const restoreSelection = () => {
	if (selectionRange) {
		const selection = window.getSelection();
		selection.removeAllRanges(); // 清除现有选区
		selection.addRange(selectionRange); // 恢复选区范围
	}
}

// 事件代理模式（父容器监听）
const handleContainerClick = (event) => {
	// stopPopover.value = false

	// console.log('8888888888888',event)

	// console.log('oooooo',event.target.closest('[data-id]'))
	const target = event.target.closest('[data-id]');
	// console.log('454545',target)
	// is_show_volume.value = false
	try {

		if (target) {

		} else {
			// 关闭停顿弹窗
			stopPopover.value = false
			showPopover.value = false
			figurePopover.value = false
			aliasPopover.value = false
		}

		// if(!target){
		//   if(target.dataset.id!=='parent-3'){
		//     stopPopover.value = false
		//   //   var div = document.querySelector('div[contenteditable="true"]');
		//   //   // console.log('77',div)
		//   //   if (div) { // 检查div是否存在
		//   //     var children = div.children;
		//   //     // console.log('ppp',children)
		//   //     for (var i = 0; i < children.length; i++) {
		//   //       children[i].classList.remove('tts-tag');
		//   //     }
		//   //   }
		//   }
		// }else{
		//   stopPopover.value = true
		// }

		// showPopover.value = false

	} catch (e) {
		// console.log('ppp',e)
		// var div = document.querySelector('div[contenteditable="true"]');
		// // console.log('77',div)
		// if (div) { // 检查div是否存在
		//   var children = div.children;
		//   // console.log('ppp',children)
		//   for (var i = 0; i < children.length; i++) {
		//     children[i].classList.remove('tts-tag');
		//   }
		//
		// }

		// showPopover.value = false
	}
};



// 点击顶部下载按钮
const clickDownlaodButton = (e, index) => {
	console.log(captions_url.value, 4444444444);
	synthetic_button_type.value = ''

	if (index == 0 && audioUrl.value == '') {
		ElMessage({
			message: '暂无音频文件',
			type: 'warning',
		})
		return
	}
	if (index == 1 && captions_url.value == '') {
		ElMessage({
			message: '暂无字幕文件',
			type: 'warning',
		})
		return
	}
	// handleBatchCreateAPI()
	const link = document.createElement('a');
	textInfo=gettxt()
	console.log(textInfo,synthesis_data.value.platformNickname,'textInfo');

	let txt=`${textInfo.slice(0, 5)}—${synthesis_data.value.platformNickname}`
	console.log(index,'index');

	if (index == 0) {
		// link.href = audioUrl.value; // 文件 URL
		// console.log(`${txt}.mp3`,999);

		// link.download = `111.mp3`; // 默认文件名
		fetch(audioUrl.value)
		.then(response => response.blob()) // 获取二进制 Blob
		.then(blob => {
		const url = URL.createObjectURL(blob); // 创建本地 URL

		link.href = url;
		link.download = `${txt}.mp3`; // 设置下载文件名

		link.click(); // 触发下载

		// 1秒后释放 URL，避免内存泄漏
		setTimeout(() => {
			URL.revokeObjectURL(url);
		}, 1000);
		})
		.catch(err => {
		console.error('下载失败:', err);
		});
	} else {
		// 下载srt文件
		const srtContent = download_captions_url.value;
		// 创建 Blob 并生成 URL
		const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' });
		const url = URL.createObjectURL(blob);

		link.href = url;
		link.download = `${txt}.srt`;

		// 触发下载后释放 URL
		setTimeout(() => {
		URL.revokeObjectURL(url);
		}, 1000);
	}
	link.style.display = 'none';
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}
// const htmlToText = (html) => {
//   let text = '';
//   const parser = new Parser({
//     ontext: (data) => { text += data; } // 仅捕获文本节点‌:ml-citation{ref="1" data="citationList"}
//   }, { decodeEntities: true });
//   parser.write(html);
//   parser.end();
//   return text.trim();
// };
// 点击音乐面板中的喇叭按钮
const is_show_volume = ref(false)
const click_volume = () => {
	is_show_volume.value = !is_show_volume.value
	slideVisible.value=true
}
// 点击关闭音乐面板中的叉号按钮
const close_music_div = () => {
	is_show_volume.value = false
	useAIDubbing.bgmusic_url = ''
	useAIDubbing.bgmusicObj = {}

}

// 返回的音频文件
const audioUrl = ref('')
// 返回的字幕文件
const captions_url = ref('')
//下载的字幕文件
let download_captions_url=ref('')
// function getCustomAttributeValues(divId, attributeName) {
//   const container = document.getElementById(divId);
//   if (!container) return [];
//   const selector = `[${attributeName}]`;
//   return Array.from(container.querySelectorAll(selector))
//       .map(element => element.getAttribute(attributeName))
//       .filter(value => value !== null);  // ‌:ml-citation{ref="2,5" data="citationList"}
// }


// function replaceHttpLinks(text, replacementFormat) {
//   // 使用正则表达式匹配HTTP或HTTPS地址
//   var urlPattern = /(https?:\/\/[^\s]+)/g;
//
//   // 使用replace方法替换匹配的链接
//   return text.replace(urlPattern, function(url) {
//     // 根据指定的格式生成替换后的字符串
//     return replacementFormat.replace('{url}', url);
//   });
// }

function extractAndTruncateChineseChars(text) {
	// 使用正则表达式匹配[sound:(.*?)]格式的内容，但此处我们不需要捕获这部分内容，因此使用非捕获组(?:...)
	// 同时，我们使用正则表达式匹配所有中文字符，并确保只保留前10个中文字符
	var chineseCharPattern = /([\u4e00-\u9fff])/g;
	var match = text.match(chineseCharPattern);

	if (match) {
		// 如果匹配到了中文字符，则取第一个匹配结果（即前10个中文字符）
		var truncatedChineseChars = match[0];

		// 使用替换方法将原始文本中的中文字符替换为截取后的前10个中文字符
		// 注意：这里假设我们只替换首次出现的连续中文字符序列，如果需要替换所有，则需要调整逻辑
		var resultText = text.replace(/[\u4e00-\u9fff]+/, truncatedChineseChars);

		// 由于我们可能只替换了部分中文字符，而原始文本中可能包含其他非中文字符和[sound:...]链接
		// 因此，我们需要确保返回的字符串仍然包含这些未修改的部分
		// 为了实现这一点，我们可以使用一个更复杂的正则表达式来精确匹配和替换
		// 但在这里，为了简化，我们假设上述替换已经足够（即原始文本中只有一个连续的中文字符序列需要替换）

		// 另外，需要注意的是，如果原始文本中的中文字符少于10个，则上述代码将直接返回这些字符（无需截断）

		return resultText;
	} else {
		// 如果没有匹配到中文字符，则直接返回原始文本（或者可以根据需要返回其他值或执行其他操作）
		return text;
	}
}

// 合成语音
// 加载动画
const loading = ref(false)
const trial_listening = ref(false)
let synthetic_button_type = ref()
let handle_selectedTextList = [] //多音字列表
let textInfo = ''  // 合成的文本内容
let sensitiveSubText=ref('确定')
// const htmlContent = '<div id="container"><p>Hello, world!</p><p>Another paragraph.</p></div>';
const syntheticAudioButton = async (type) => {
	audioUrl.value=''
	captions_url.value=''
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
	if (!localStorage.getItem('user')) {
		proxy.$modal.open()
		return
	}
	// 只要点击合成和试听按钮就全部停掉音色列表中播放
	stop_timbre_play()

	// 添加友盟埋点
	if (type == 1) {
		// 合成音频按钮
		AIDubbingAnalytics.trackSynthesisAudio(SoundItemId.value);
		loading.value = true
	} else {
		// 快速合成按钮
		AIDubbingAnalytics.trackQuickSynthesis(SoundItemId.value);
		trial_listening.value = true
	}
	// // 通过CSS属性选择器定位目标元素‌:ml-citation{ref="3,4" data="citationList"}
	const container = document.querySelector('#editor');  // 目标父级div
	// // 提取ID名称集合
	//   const elements_data_IdList = [];
	//   elements_data_Id.forEach(element => {
	//     console.log(element.dataset)
	//     // if (element['dataset']) elements_data_IdList.push(element['dataset'].name);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	//   });
	// 获取音频链接
	// const audio_list = []
	// const create_background_divs = editorRef.value.getElementsByClassName('create_background');
	// create_background_divs.forEach(element => {
	//   console.log(element)
	//   // if (element['dataset']) audio_list.push(element['dataset'].id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	// });
	// console.log(audio_list)
	// console.log('container',container)
	const elementsWithId = container.querySelectorAll('[id]');  // 获取所有带id属性的子元素‌:ml-citation{ref="3,4" data="citationList"}
	// console.log('elementsWithId',elementsWithId)
	// // 提取ID名称集合
	const idList = [];
	elementsWithId.forEach(element => {
		if (element.id) idList.push(element.id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	});
	// const targets = editorRef.value.getElementsByClassName('data-shuzi');
	const changContents = editorRef.value.getElementsByClassName('change');
	// console.log('targets',targets)
	// console.log('changContents',changContents)
	// const nanoidArray = []
	// for(let i=0;i<idList.length;i++){
	//   nanoidArray.push(nanoid(4))
	// }
	// console.log('nanoidArray',nanoidArray)
	let originalArr = []
	Array.from(elementsWithId).forEach((el, idx) => {
		originalArr.push({
			id: idList[idx],
			text: el.innerText,
		})
	});
	let alternativeArr = []
	Array.from(changContents).forEach((el, idx) => {
		alternativeArr.push({
			id: idList[idx],
			text: el.innerText,
		})
	});
	console.log('ppp', alternativeArr)
	// console.log('originalArr',originalArr)
	// console.log('alternativeArr',alternativeArr)
	const originalDiv = document.getElementById('editor');
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	if (alternativeArr.length > 0) {
		for (let j = 0; j < originalArr.length; j++) {
			processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
			if (parentDiv) {
				const elements = parentDiv.querySelectorAll('*');
				elements.forEach(element => {
					if (element.id === alternativeArr[j].id) {
						element.textContent = alternativeArr[j].text; // 替换文本
					}
				});
				// processedHTML.value = elements
				// const targetDivs = parentDiv.querySelectorAll("div[data-type]");
				// targetDivs.forEach(div => div.remove());
			}
		}
	}
	// for(let j=0;j<originalArr.length;j++){
	//   processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
	//   if (parentDiv) {
	//     const elements = parentDiv.querySelectorAll('*');
	//     elements.forEach(element => {
	//         if (element.id === alternativeArr[j].id) {
	//           element.textContent = alternativeArr[j].text; // 替换文本
	//         }
	//     });
	//     // processedHTML.value = elements
	//     // const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	//     // targetDivs.forEach(div => div.remove());
	//   }
	// }
	const elements = parentDiv.querySelectorAll("[data-type='number11']");
	console.log('elements', elements)
	const parentElement = document.getElementsByClassName('parent')
	// console.log(parentElement)
	const dataIdValues = [];
	Array.from(parentElement).forEach(parent => {
		const spans = parent.getElementsByTagName('span'); // 获取当前 parent 下的所有 span 元素
		Array.from(spans).forEach(span => {
			const dataId = span.getAttribute('data-id');
			// 如果 data-id 存在，则添加到数组中
			if (dataId) {
				dataIdValues.push(dataId);
			}
		});
		// const spans = parent.querySelectorAll('span');
		// // 遍历每个 span 元素
		// spans.forEach(span => {
		//   // 获取 span 元素的 data-id 属性值
		//   const dataId = span.getAttribute('data-id');
		//   // 如果 data-id 存在，则添加到数组中
		//   if (dataId) {
		//     dataIdValues.push(dataId);
		//   }
		// });
	});
	// 获取到的音效链接
	console.log('dataIdValues', dataIdValues)
	Array.from(elements).map((element, index) => {
		// console.log('element',element)
		var linkElement = document.createElement('a');
		// linkElement.href = dataIdValues[index]; // 设置链接地址
		linkElement.textContent = '[sound:' + dataIdValues[index] + ']'; // 复制 div 的内容到链接
		// linkElement.style.backgroundColor = 'lightgreen'; // 设置链接背景颜色
		// element.textContent = dataIdValues[index]; // 替换内容
		// 用链接替换 div
		element.replaceWith(linkElement);
	})
	// const targetDivs = parentDiv.querySelectorAll('div:not([data-type="number11"])');
	const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	targetDivs.forEach(div => div.remove());
	const targetDivs1 = parentDiv.querySelectorAll("button[data-type]");
	targetDivs1.forEach(div => div.remove());
	// console.log('opdsfi',parentDiv)
	// 需要传的字符串信息   注意  其中有数字就会转  需要处理

	textInfo = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
		// console.log(parseInt(val))
		return `<#${parseInt(val) / 1000}#>`
	})
	// console.log('jjjjj',textInfo)
	textInfo = textInfo.replace(/\n/g, '');
	// console.dir('8888888',textInfo)
	// return

	if (type == 2||type==3) {
		let parts = textInfo.split(/(\[sound:.*?\])+/);
		let finalText = parts.filter(part => part.trim() !== '')
		// console.log('finalText',finalText)
		let length = 0
		for (let i = 0; i < finalText.length; i++) {
			// console.log(finalText[i])
			if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
				if (finalText[i].length <= 500) {
					length += finalText[i].length
				} else {
					finalText[i] = finalText[i].substring(0, 500 - length);
					// debugger
				}
			}
		}
		// console.dir(finalText.join(''))
		// debugger
		textInfo = finalText.join('')
		if(type==3){
			textInfo=textAfterCaret.value
		}
	}
	if (!textInfo) {
		ElMessage({
			message: '请先输入文本信息',
			type: 'warning',
		})
		loading.value = false
		trial_listening.value = false
		return
	}

	if (!SoundItemId.value) {
		ElMessage({
			message: '请先选择声音',
			type: 'warning',
		})
		loading.value = false
		trial_listening.value = false
		return
	}
	handle_selectedTextList = []

	selectedTextList.map(item => {
		handle_selectedTextList.push(item.name)
	})
	synthetic_button_type.value = type
	full_loading.value=true
	// 检查输入的文本是否包含敏感词
	const res = await chekSensitive_Api({
		txt: textInfo.replace(/\[sound:[^\]]*\]|<#.*?#>/g, '').trim()
	})

	const {
		code,
		data // 不直接解构，先检查 data
	} = res || {}; // 如果 res 为 null，则使用空对象

	const result = data?.content?.result ?? null; // 使用可选链和空值合并运算符
	const status_code = data?.status_code;

	// console.log('result',res)
	if (result && result.length > 0) {
		full_loading.value=false
		sensitiveSubText.value='确定'
		ElMessageBox.confirm(
			`文本中包含${result.join('、')}敏感词，是否继续生成音频？`,
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				// type: 'warning',
				closeOnPressEscape: false,
				closeOnClickModal: false,
				showClose: false,
				beforeClose: (action, instance, done) => {
				if (action === "confirm") {
					instance.confirmButtonText = '生成中';
					instance.confirmButtonLoading = true; // 显示加载状态
					full_loading.value=true
					// 在这里执行你的生成音频的逻辑
					popUp_query_button(instance, () => {
						// 操作完成后更新按钮文本
						instance.confirmButtonLoading = false; // 隐藏加载状态
						done(); // 关闭弹窗
					});
				} else {
					cancel_check()
					done(); // 取消操作
				}
			}

			}
		).then(res => {
			// console.log('77777777777')
		}).catch(err => {
			loading.value = false
			trial_listening.value = false
		})
	} else {
		//直接合成音频
		popUp_query_button()
	}
}

// 检测敏感词后点击确定按钮继续生成样音
const popUp_query_button = (instance, done) => {
	let params = {
		user_id: JSON.parse(localStorage.getItem('user'))?.userId || '',
		text: textInfo,
		voice_id: SoundItemId.value,
		audio_format: "mp3",
		chunk_size: 2048,
		speed: speechValue.value, //语速
		vol: slideValue.value / 10, //音量
		pitch: intonationValue.value,  //语调
		pronunciation: handle_selectedTextList, //多音字列表
		bgm_url: useAIDubbing.bgmusic_url,
		vol_main:volumeValue.value/100, //背景音量
		emotion:current_emotion.value,//情绪
		language_boost:current_language.value,//语言
		version:2//ai配音 传2   商配传1
	}
	if (synthetic_button_type.value == 2||synthetic_button_type.value == 3) {
		params.isTrialListen = true
	}
	synthesized_speechApi(params).then(async(res) => {
		full_loading.value=false
		console.log('77', res)
		let { data, code } = res
		// console.log('78979846',data,code)
		if (code == 0) {
			let { status_code, content } = data
			// console.log('llll',status_code, content,content.length)
			if (status_code == 200) {
				synthesis_data.value = choose_timbre.value
				let { result } = content
				// console.log('oooo787898798',result)
				if (result) {
					await fetchUserBenefits()
					console.log('result',result)
					audioUrl.value = result.audio_file
					captions_url.value = result.subtitle_file
					traceId.value = result.trace_id
					download_captions_url.value=result.srt
					// 保存到我的作品,登录之后才可以保存
					if (localStorage.getItem('user')) {
						saveTextHtml()
					}
					if(synthetic_button_type.value!=3){
						handleBatchCreateAPI()
					}

					// console.log('audioUrl.value',audioUrl.value)
				} else {
					audioUrl.value = ''
					captions_url.value = ''
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})
				}
				loading.value = false
				trial_listening.value = false
				if (instance) {
					instance.confirmButtonLoading = false
					done&&done()
				}
			} else {
				console.log(status_code, 'err1');
				audioUrl.value = ''
				captions_url.value = ''
				loading.value = false
				trial_listening.value = false
				if (alert_dialog_code_arr.value.includes(status_code)) {
					done&&done()
					public_open_alert_dialog(instance, done, status_code)
					return
				} else {
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})

				}
			}
			done&&done()
		} else {
			audioUrl.value = ''
			captions_url.value = ''
			loading.value = false
			trial_listening.value = false
			if (instance) {
				instance.confirmButtonLoading = false
				done&&done()
			}
			ElMessage({
				message: '音频转义失败，请重新上传',
				type: 'warning',
			})
			done&&done()
		}
	}).catch((err) => {
		console.log(err, 'err');
		full_loading.value=false
		if (alert_dialog_code_arr.value.includes(err.response.status_code)) {
			public_open_alert_dialog(instance, done, err.response.status_code)
		}
		console.log('11', err)
	})
}
// 当点击合成音频底部播放按钮时，暂停所有播放音色播放
const stop_timbre_play = () => {
	soundList.value.map((child, idx) => {
		soundList.value[idx].isPlay = false
	})
	audioRef.value.src = ''
	audioRef.value.pause()
}

// 点击播放音色列表中某一项，播放
const AudioPlayerRef = ref(null)
const audioRef = ref(null);
let audioMap =  ref({})
const playAudio = (item, index) => {
	audioRef.value.src = ''
	audioRef.value.pause()
	// 关闭页面底部合成音频播放
	AudioPlayerRef.value.handleCloseMusic()
	// isPauseTtsAudio.value = true
	// console.log('audioRef',audioRef.value)
	soundList.value.map((child, idx) => {
		if (item.id == child.id) {
			if (soundList.value[idx].isPlay) {
				soundList.value[idx].isPlay = false
				audioRef.value.pause()
			} else {
				soundList.value[idx].isPlay = true
				// audioRef.value.src = item.audioUrl[current_emotion.value]

			console.log(show_list_nav_current.value,'听');

				if(show_list_nav_current.value==1||(show_list_nav_current.value==2&&selectetMycurrent=='克隆音色')){
					audioRef.value.src=item.audioUrl

				}else{
					audioMap.value = JSON.parse(item.audioUrl);
					audioRef.value.src=getAudioUrl(current_emotion.value)
				}
				console.log(audioRef.value.src,'选择情绪声音');
				nextTick(() => {
					audioRef.value.play()
				})
			}
		} else {
			soundList.value[idx].isPlay = false
		}
	})
}
// 定义函数，传入情绪，返回对应链接
let getAudioUrl=(emotion)=>{
  if (!emotion || typeof emotion !== 'string') {
    console.warn('传入的情绪无效:', emotion);
    return null;
  }
  const trimmedEmotion = emotion.trim();
  return audioMap.value[trimmedEmotion] || null;
}
// 选择音效时监听接收的数据
// eventBus.on('update-data', (data) => {
//   console.log('接收数据4545:', data); // 输出：1
//   //插入音效到页面指定位置
//   click_effects_item(data)
//
// });

watch(()=>useAIDubbing,(newVal)=>{
	console.log('背景音乐音量',newVal);

if(newVal?.bgmusic_volume){
  slideValue.value=useAIDubbing.bgmusic_volume
}

},{deep:true,immediate:true})
// 导入文案监听
const handleCallLetter = (data) => {
	// console.log('8643541635456',data)
	editorRef.value.innerHTML = data;
	// 触发输入框input事件
	editorRef.value.dispatchEvent(new InputEvent('input'));
}
// 主动触发输入框input事件
const editorRefTriggerInput = () => {
	editorRef.value.dispatchEvent(new InputEvent('input'));
}

const Letter = ref(null)
const effects = ref(null)
const handleCallParent = (data) => {
	// console.log('收到子组件事件:', data);
	click_effects_item(data)

};

const click_effects_item = (item) => {
	// console.log(item)
	let value_url = item.storagePath || item.ossPath
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	// var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
// 	let container = range.startContainer;
// 	console.log(container,'前后node');
//   	let offset = range.startOffset;

//   // 如果是文本节点，container.parentNode 才是元素节点
//   if (container.nodeType === 3) {
//     const parent = container.parentNode;
//     const childNodes = parent.childNodes;


//     console.log('文本节点的父节点:', parent);
//     console.log('兄弟节点:',range.startContainer,childNodes);


//   }
	// 如果是文本节点，container.parentNode 才是元素节点
	// if (container.nodeType === 3) {
	// 	container = container.parentNode;
	// }

	// // 获取当前光标所在节点的子节点列表
	// const childNodes = container.childNodes;

	// // 获取光标前一个节点和后一个节点
	// const prevNode = offset > 0 ? childNodes[offset - 1] : null;
	// const nextNode = offset < childNodes.length ? childNodes[offset] : null;


	// // 定义判断节点是否为音效元素的函数
	// function isEffectNode(node) {
	// 	if (!node || node.nodeType !== 1) return false; // 不是元素节点
	// 	return node.classList.contains('parent') && node.getAttribute('data-type') === 'number22';
	// }

	// if (isEffectNode(prevNode) || isEffectNode(nextNode)) {
	// 	ElMessage({
	// 	message: '当前位置前后已存在音效，不能重复添加',
	// 	type: 'warning',
	// 	});
	// 	return "";
	// }
	// const div = document.createElement('div');
	// div.style.width = '60px';
	// div.style.height = '30px';
	// div.style.padding = "0 5px";
	// div.style.marginLeft = "5px";
	// div.style.backgroundImage = 'url(http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D)';
	// div.style.backgroundSize = '20px 20px';
	// div.style.backgroundRepeat = "no-repeat";
	// // div.style.backgroundColor = "#eff8f2";
	// div.style.border = "1px solid #ddd";
	// div.style.backgroundColor = "#ddd";
	// // div.style.backgroundPosition = "center left";
	// div.style.backgroundPosition = "5px center";
	// div.style.borderRadius = "6px";
	// div.setAttribute("data-type", "number");
	// div.setAttribute("contentEditable", "false");
	// div.style.display = "inline-block";
	// div.style.position = "relative";
	// div.style.pointerEvents = "auto";
	// // div.innerHTML = "<button data-type='effect' style='font-size:10px;'>×</button>";
	// // var closeBtn = tipBox.querySelector("button");
	// // closeBtn.style.border = "none";
	// // closeBtn.style.padding = "4px";
	// // closeBtn.style.fontSize = "14px";
	// // closeBtn.style.cursor = "pointer";
	// // closeBtn.style.backgroundColor = "#fff";
	// // div.style.display = "flex";         // 启用弹性布局
	// // div.style.justifyContent = "flex-end";  // 右对齐
	// // div.style.alignItems = "center";    // 顶部对齐
	// div.innerHTML = `<button data-type='effect' style='padding: 2px 5px;cursor: pointer;user-select: none;'>×</button>`;
	// const button = div.querySelector("button[data-type='effect']");
	// button.style.position = "absolute";
	// button.style.right = "5px";
	// button.style.top = "2px";
	// button.addEventListener("click", function(e) {
	//   e.stopPropagation();
	//   if (div.parentNode) div.parentNode.removeChild(div);
	// });
	//
	//
	// range.insertNode(div);
	// // 整光标位置
	// const newRange = document.createRange();
	// newRange.setStartAfter(div);
	// newRange.collapse(false);
	// selection.removeAllRanges();
	// selection.addRange(newRange);

	// if(range.commonAncestorContainer.nextElementSibling){
	//   range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = value_url
	//   // let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
	//   // selectedTextList.map(child=>{
	//   //   if(child.id==dataId){
	//   //     child.name = `${range.toString()}/(${item})`
	//   //   }
	//   // })
	//   return
	// }

	// let id = nanoid(4)

	var tipBox = document.createElement("div");
	tipBox.innerHTML = `<span class='change' data-id=${value_url}></span> <button data-type='effect' style='font-size:10px;'>×</button>`
	// tipBox.innerHTML = "<img> <button data-type='effect' style='font-size:10px;'></button>";
	// var img = tipBox.querySelector("img")
	// img = document.createElement('img');
	// img.src = 'http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D'; // 替换为实际路径
	// // img.alt = '动态插入的图片';
	// img.style.width = '10px';
	// img.style.height = '10px';
	// tipBox.setAttribute("data-id", id);
	tipBox.setAttribute("data-attr", "yinxiao");
	tipBox.setAttribute("data-type", "number11");
	tipBox.setAttribute("data-type1", "number22");
	tipBox.setAttribute("class", "parent");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.width = '60px'
	// tipBox.style.height = '100px'
	//
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	tipBox.style.marginLeft = "5px";
	tipBox.style.pointerEvents = "auto";
	// tipBox.className = 'create_background'
	// tipBox.style.backgroundImage = "url(@/assects/images/aiImages/jingpin.png)"
	// tipBox.style.backgroundSize = "100%";
	// tipBox.style.backgroundRepeat = "no-repeat";
	// tipBox.style.backgroundColor = '#f00
	tipBox.style.backgroundImage = `url(${musicIcon})`
	tipBox.style.backgroundSize = '20px 20px';
	tipBox.style.backgroundRepeat = "no-repeat";
	tipBox.style.backgroundPosition = "5px center";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	closeBtn.style.marginLeft = "24px";
	editorRefTriggerInput()
	// let id = nanoid(4)
	// const span = document.createElement('span');
	// // // span.style.display = "none";
	// // // span.style.width = '50px'
	// // // console.log(item)
	// // // span.innerText = 'llll'
	// // span.className = id;
	// // // span.data-id = id;
	// span.setAttribute('data-name', id);
	// range.surroundContents(span);
	// selection.removeAllRanges();
	// span.appendChild(range.extractContents());
	// range.insertNode(span);
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		// const prevElement = tipBox.previousSibling
		// // console.log('ppp',prevElement)
		// if (!prevElement) return;
		// // // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		// const fragment = document.createDocumentFragment();
		// while (prevElement.firstChild) {
		//   fragment.appendChild(prevElement.firstChild);
		// }
		// // // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		// prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		// prevElement.parentNode.replaceChild(fragment, prevElement);
		// prevElement.remove()
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});

	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});

	if (selection.rangeCount > 0) {
		range.collapse(false);
		console.log(range,'range');

		let prevNode = null;
		let nextNode = null;
		const container = range.startContainer;
		const offset = range.startOffset;
		if (container.nodeType === 3) {
			const parent = container.parentNode;
		const childNodes = parent.childNodes;
		const index = Array.prototype.indexOf.call(childNodes, container);

		if (offset === 0) {
		// 光标在文本节点开头，前一个兄弟节点是 childNodes[index - 1]
		prevNode = index > 0 ? childNodes[index - 1] : null;
		nextNode = container;
		} else if (offset === container.length) {
		// 光标在文本节点末尾，后一个兄弟节点是 childNodes[index + 1]
		prevNode = container;
		nextNode = index < childNodes.length - 1 ? childNodes[index + 1] : null;
		} else {
		// 光标在文本节点中间，前后节点都是该文本节点本身
		prevNode = container;
		nextNode = container;
		}
	} else if (container.nodeType === 1) { // 元素节点
		const childNodes = container.childNodes;
		prevNode = offset > 0 ? childNodes[offset - 1] : null;
		nextNode = offset < childNodes.length ? childNodes[offset] : null;
	}
	// 判断前后节点是否有 data-type="number22"
	function hasNumber22(node) {
		if (!node) return false;
		if (node.nodeType === 1 && node.getAttribute('data-type1') === 'number22') {
			return true;
		}
		return false;
	}

	if (hasNumber22(prevNode) || hasNumber22(nextNode)) {
		ElMessage({
			message: '同一个地方不允许插入多个音效',
			type: 'warning',
		});
		return; // 阻止插入
	}

		range.insertNode(tipBox);
		// 获取 div 的父节点
		//     const parent = div.parentNode;
		// // 在父节点中，将 span 插入到 div 前面
		//     parent.insertBefore(span, div);
		console.log(tipBox)
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// 关闭弹窗
	// effects.value.effectsDialogVisible = false
	// stopPopover.value = false
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


let handlePopoverClick = (event) => {
	event.stopPropagation();
};

let handleClickOutside = async(event) => {
	const popoverElement = document.querySelector('.pause-popover');
	if (popoverElement && !popoverElement.contains(event.target)) {
		stopPopover.value = false;
	}
	if (event.button === 0) {
		menuVisible.value = false
	}
};
let handleClickOutside1 = (event) => {
	console.log();

const editorElement = document.getElementById('editor');
  if (editorElement && !editorElement.contains(event.target)) {
    menuVisible.value = false;
  }
}
let volumePanel = ref(null)
let handleClickOutside2=(event)=>{

	 const popoverElements = document.querySelectorAll('.volume-popover');
  let clickedInside = false;

  popoverElements.forEach(el => {
    if (el.contains(event.target)) {
      clickedInside = true;
    }
  });

  if (!clickedInside) {
    is_show_volume.value = false;
  }
}
let trial_listening_dialog_ref = ref(null)
let show_down_load_item = (index) => {
	let result = true
	switch (index) {
		case 0:
			if (audioUrl.value == '' && synthetic_button_type.value != 2) {
				result = false
			}
			break;
		case 1:
			if (captions_url.value == '') {
				result = false
			}
			break;
		default:
			break;
	}
	return result
}
// 鼠标滑动选择文本时
const selectstart = (e) => { }
//声音商店选中自动滑动到指定位置
let scrollToElement = () => {
  nextTick(() => {
    let scrollbar = scroll.value;
    let target = targetElements.value.find(el => el && el.dataset.voiceName == SoundItemId.value);

    if (target && scrollbar) {
      // 计算目标元素相对于滚动容器的偏移
      let targetOffset = 0;
      let el = target;
      const container = scrollbar.$el;

      while (el && el !== container) {
        targetOffset += el.offsetTop;
        el = el.offsetParent;
      }

      // 滚动到目标位置
      scrollbar.scrollTo({ top: targetOffset, behavior: 'smooth' });
    }
  });
};
//合成的voiceName
let synthesis_data = ref({})
//提示弹窗
let showAlertDialog = ref(false)
let alert_title = ref('')
let alert_message = ref('')
let alert_confirm_txt = ref('')
let alert_cancel_txt = ref('')
let alert_dialog_code = ref('')
let alert_dialog_code_arr = ref([301, 302, 303, 312])
let alert_dialog_code_status = ref(0)
let showCancelButton=ref(true)
//提示弹窗展示公共逻辑
let public_open_alert_dialog = (instance, done, status) => {
	alert_dialog_code.value = status
	showCancelButton.value=true
	alert_dialog_code_status.value=''
	//试听
	if (synthetic_button_type.value == 2) {
		switch (status) {
			//未登录字符不足
			case 301:
			case 312:
				alert_title.value = '温馨提示'
				if (loginStore.memberInfo.level.level == 0) {
					alert_message.value = '当前试听字符数不足，请先开通会员！'
					alert_confirm_txt.value = '开通会员'
					alert_dialog_code_status.value = 1
					alert_cancel_txt.value = '我知道了'
				}else if (loginStore.memberInfo.level.level == 1) {
					alert_message.value = '当前试听字符数不足，请升级 SVIP 继续使用！'
					alert_confirm_txt.value = '升级 SVIP'
					alert_dialog_code_status.value = 1
					alert_cancel_txt.value = '我知道了'
				}else{
					alert_message.value = '当前试听字符数不足'
					showCancelButton.value=false
					alert_confirm_txt.value = '我知道了'
				}
				showAlertDialog.value = true;
				break;
		}
	} else {
		//合成
		switch (status) {
			// 未登录
			case 301:
				alert_title.value = '体验额度用尽'
				alert_message.value = '您的合成额度已用尽，请开通会员使用'
				alert_confirm_txt.value = '开通会员'
				alert_cancel_txt.value = '我知道了'
				alert_dialog_code_status.value = 2
				showAlertDialog.value = true;
				break;
			// vip买臻享
			case 303:
				if (loginStore.memberInfo.level.level == 1) {
					alert_title.value = '温馨提示'
					alert_message.value = '请购买臻享音色或升级 SVIP 继续使用！'
					alert_confirm_txt.value = '升级 SVIP'
					alert_cancel_txt.value = '购买音色'
					alert_dialog_code_status.value = 4
					showAlertDialog.value = true;
				}else if(loginStore.memberInfo.level.level == 2){
					alert_title.value = '温馨提示'
					alert_message.value = '您的合成字符已用尽，请购买字符包继续使用！'
					alert_confirm_txt.value = '购买字符包'
					alert_cancel_txt.value = '暂不购买'
					alert_dialog_code_status.value = 5
					showAlertDialog.value = true;
				} else if(loginStore.memberInfo.level.level == 0){
					alert_title.value = '温馨提示'
					alert_message.value = '您的合成额度已用尽，请开通会员使用'
					alert_confirm_txt.value = '开通会员'
					alert_cancel_txt.value = '我知道了'
					alert_dialog_code_status.value = 2
					showAlertDialog.value = true;
				}
				break;
			//vip买精品 字符不足
			case 302:
			case 312:
				if (loginStore.memberInfo.level.level == 1) {
					alert_title.value = '字符数不足'
					alert_message.value = '当前会员套餐内字符额度已用尽，如需继续使用请购买字符包或升级SVIP会员'
					alert_confirm_txt.value = '购买字符包'
					alert_cancel_txt.value = '升级 SVIP'
					alert_dialog_code_status.value = 3
					showAlertDialog.value = true;
				}else if(loginStore.memberInfo.level.level == 0){
					alert_title.value = '体验额度用尽'
					alert_message.value = '您的合成额度已用尽，请开通会员使用'
					alert_confirm_txt.value = '开通会员'
					alert_cancel_txt.value = '我知道了'
					alert_dialog_code_status.value = 2
					showAlertDialog.value = true;
				}else{
					alert_title.value = '温馨提示'
					alert_message.value = '您的合成字符已用尽，请购买字符包继续使用！'
					alert_confirm_txt.value = '购买字符包'
					alert_cancel_txt.value = '暂不购买'
					alert_dialog_code_status.value = 5
					showAlertDialog.value = true;
				}
				break;
			//svip买精品/臻享 字符不足
			case 302:
			case 303:
			case 312:
				if (loginStore.memberInfo.level.level == 2) {
					alert_title.value = '温馨提示'
					alert_message.value = '当前字符额度已用尽！'
					alert_confirm_txt.value = '购买字符包'
					alert_cancel_txt.value = '暂不购买'
					alert_dialog_code_status.value = 5
					showAlertDialog.value = true;
				}
				break;
			default:
				break;
		}
	}
}
let handleOpenMember = () => {
	console.log(alert_dialog_code_status.value,'弹出框');
	// 关闭弹窗
	showAlertDialog.value = false;
	let status = alert_dialog_code_status.value
	let url1 = ''
	switch (status) {
		case 1:
		case 2:
		case 4:
			url1 = `${window.location.origin}/membership`;
			window.open(url1, '_blank');
			break;
			// 判断是否在layout布局内
			// if (route && route.currentRoute && route.currentRoute.value.path.includes('/layout')) {
			// 	// 如果在layout布局内，使用内部路由导航
			// 	// router.push({ name: 'membership-nav' });
			// 	const url = `${window.location.origin}/membership?nav=character`;
			// 	window.open(url, '_blank');
			// } else {
			// 	// 否则通过URL跳转
			// 	window.location.href = '/membership';
			// }
		case 3:
		case 5:
			// router.push({ name: 'membership', query: { nav: 'character' } });
			const url = `${window.location.origin}/membership?nav=character`;
			window.open(url, '_blank');
			break;
		default:
			break;
	}
}
const handleCloseLimitDialog = () => {
	let status = alert_dialog_code_status.value
	switch (status) {
		case 4:
			// router.push({ name: 'soundStore', query: { buy: true } });
			const url = `${window.location.origin}/soundStore?buy=true&type=single&voice_id=${SoundItemId.value}`;
			window.open(url, '_blank');
			soundStore.setbuySound({
				type: 'single',
				voice_id: SoundItemId.value
			})
			break;
		case 3:
			let url1 = `${window.location.origin}/membership`;
			window.open(url1, '_blank');
			// // 判断是否在layout布局内
			// if (route && route.currentRoute && route.currentRoute.value.path.includes('/layout')) {
			// 	// 如果在layout布局内，使用内部路由导航
			// 	// router.push({ name: 'membership-nav' });
			// 	const url = `${window.location.origin}/membership`;
			// 	window.open(url, '_blank');
			// } else {
			// 	// 否则通过URL跳转
			// 	window.location.href = '/membership';
			// }
		default:
			break;
	}
	// 关闭会员限制弹窗
	showAlertDialog.value = false;
};
// // 粘贴处理
// const handlePaste = (e) => {
//   e.preventDefault()
//   const text = (e.clipboardData || window.clipboardData).getData('text')
//   const currentText = editorRef.value.innerText
//   const remainingSpace = MAX_LENGTH - currentText.length
//
//   if (remainingSpace <= 0) return
//
//   const pastedText = text.slice(0, remainingSpace)
//   document.execCommand('insertText', false, pastedText)
// }
//
// // 中文输入法处理
// const handleCompositionStart = () => {
//   isComposing.value = true
// }
//
// const handleCompositionEnd = (e) => {
//   isComposing.value = false
//   const newText = e.target.innerText
//   if (newText.length > MAX_LENGTH) {
//     editorRef.value.innerText = enforceLimit(newText)
//   }
// }
//
// // 替换功能示例
// const replaceText = (start, end, newText) => {
//   const current = editorRef.value.innerText
//   const modified = current.slice(0, start) + newText + current.slice(end)
//
//   if (modified.length > MAX_LENGTH) {
//     console.error('替换失败：超过字数限制')
//     return false
//   }
//
//   editorRef.value.innerText = modified
//   return true
// }
// let savedRange;
// const editable = ref(null);
// document.addEventListener('click', function(event) {
//   // Prevent default selection reset when clicking outside the contenteditable div
//   event.preventDefault();
//   if (savedRange) {
//     const selection = window.getSelection();
//     selection.removeAllRanges();
//     selection.addRange(savedRange);
//
//     setTimeout(()=>{
//
//       console.log('888888888888888',selection.getRangeAt(0).toString())
//     },1000)
//
//   }
// });

// 监听选区变化事件
// document.addEventListener('selectionchange', function() {
//   const selection = window.getSelection();
//
//   // var range = selection.getRangeAt(0);
//   // console.log('range.toString()',range.toString())
//
//   if (selection.toString()) {
//     selectedText = selection.toString();
//     console.log('更新选中内容:', selectedText);
//   }
// });
//
// // 监听点击事件，处理获取选中的内容
// document.addEventListener('click', function(event) {
//   setTimeout(function() {
//     const currentSelection = window.getSelection().toString();
//     if (!currentSelection) {
//       if (selectedText !== '') {
//         console.log('当前未选择文字，取最近一次选中内容:', selectedText);
//         // 在这里处理需要使用的选中的内容
//       } else {
//         console.log('没有任何选中内容');
//       }
//     } else {
//       console.log('点击后选区内容:', currentSelection);
//     }
//   }, 100); // 添加延迟以确保选区更新
// });

// 根据筛选结果更新相关标签的显示状态
const updateRelatedTags = (filteredResults) => {
	// 如果没有筛选结果，恢复所有标签为可见
	if (!filteredResults || filteredResults.length === 0) {
		resetTagsVisibility()
		return
	}

	// 收集筛选结果中存在的各类标签
	const existingVoiceTypes = new Set()
	const existingGenders = new Set()
	const existingSceneCategories = new Set()
	const existingRecommendTags = new Set()

	// 添加"全部"选项，确保它始终可见
	existingVoiceTypes.add('全部')
	existingGenders.add('全部')
	existingSceneCategories.add('全部')
	existingRecommendTags.add('全部')

	// 分析筛选结果中的标签
	filteredResults.forEach(item => {
		// 收集音色类型 (SVIP/VIP和grade)
		if (item.membershipGrade) {
			existingVoiceTypes.add(item.membershipGrade)
		}
		if (item.grade) {
			existingVoiceTypes.add(item.grade)
		}

		// 收集性别和年龄段
		if (item.gender) {
			// 处理可能的复合格式，如"男、青年"
			const genderInfo = item.gender.split('、')
			genderInfo.forEach(info => {
				existingGenders.add(info.trim())
				// 尝试提取年龄段信息（如青年、中年、老年）
				if (info.includes('青年') || info.includes('中年') || info.includes('老年') || info.includes('少年')) {
					existingGenders.add(info.trim())
				}
			})
		}

		// 如果有专门的年龄组字段
		if (item.ageGroup) {
			existingGenders.add(item.ageGroup)
		}

		// 收集场景分类
		if (item.sceneCategory) {
			existingSceneCategories.add(item.sceneCategory)
		}

		// 收集推荐标签
		if (item.recommendTags) {
			const tags = typeof item.recommendTags === 'string'
				? item.recommendTags.split(',')
				: Array.isArray(item.recommendTags)
					? item.recommendTags
					: [item.recommendTags]

			tags.forEach(tag => {
				if (tag && typeof tag === 'string') {
					existingRecommendTags.add(tag.trim())
				}
			})
		}
	})

	// 更新各标签的可见性
	// 1. 音色类型标签
	voiceTypeArr.value.forEach(item => {
		item.visible = existingVoiceTypes.has(item.name) || item.name === '全部'
	})

	// 2. 性别/年龄段标签
	gendersArr.value.forEach(item => {
		item.visible = existingGenders.has(item.name) || item.name === '全部'
	})

	// 3. 场景分类标签
	secondArr.value.forEach(item => {
		item.visible = existingSceneCategories.has(item.name) || item.name === '全部'
	})

	// 4. 推荐标签
	thirdArrList.value.forEach(item => {
		item.visible = existingRecommendTags.has(item.name) || item.name === '全部'
	})
}

// 重置所有标签为可见状态
const resetTagsVisibility = () => {
	resetTagsVisibilityByLevel(1)
	resetTagsVisibilityByLevel(2)
	resetTagsVisibilityByLevel(3)
	resetTagsVisibilityByLevel(4)
}

// 重置指定级别的标签为可见状态
const resetTagsVisibilityByLevel = (level) => {
	switch (level) {
		case 1:
			voiceTypeArr.value.forEach(item => item.visible = true)
			break
		case 2:
			gendersArr.value.forEach(item => item.visible = true)
			break
		case 3:
			secondArr.value.forEach(item => item.visible = true)
			break
		case 4:
			if (thirdArrList.value) {
				thirdArrList.value.forEach(item => item.visible = true)
			}
			break
		default:
			// 重置所有级别
			voiceTypeArr.value.forEach(item => item.visible = true)
			gendersArr.value.forEach(item => item.visible = true)
			secondArr.value.forEach(item => item.visible = true)
			if (thirdArrList.value) {
				thirdArrList.value.forEach(item => item.visible = true)
			}
			break
	}
}

// 根据筛选级别更新下一级标签的显示状态
const updateNextLevelTags = (filteredResults, currentLevel) => {
	// 如果没有筛选结果，重置下一级的标签可见性
	if (!filteredResults || filteredResults.length === 0) {
		resetTagsVisibilityByLevel(currentLevel + 1)
		return
	}

	// 所有级别的"全部"选项始终可见
	// 只需要更新当前选中级别的下一级标签

	// 定义下一级的标签数组
	let nextLevelTags
	let tagField

	switch (currentLevel) {
		case 1: // 第一级是voiceTypeArr，下一级是gendersArr
			nextLevelTags = gendersArr.value
			// 收集筛选结果中存在的性别/年龄标签
			const existingGenders = new Set(['全部'])

			filteredResults.forEach(item => {
				// 收集性别和年龄段
				if (item.gender) {
					// 处理可能的复合格式，如"男、青年"
					const genderInfo = item.gender.split('、')
					genderInfo.forEach(info => {
						existingGenders.add(info.trim())
						// 尝试提取年龄段信息（如青年、中年、老年）
						if (info.includes('青年') || info.includes('中年') || info.includes('老年') || info.includes('少年')) {
							existingGenders.add(info.trim())
						}
					})
				}

				// 如果有专门的年龄组字段
				if (item.ageGroup) {
					existingGenders.add(item.ageGroup)
				}
			})

			// 更新标签可见性
			nextLevelTags.forEach(item => {
				item.visible = existingGenders.has(item.name) || item.name === '全部'
			})
			break

		case 2: // 第二级是gendersArr，下一级是secondArr
			nextLevelTags = secondArr.value
			// 收集筛选结果中存在的场景分类
			const existingSceneCategories = new Set(['全部'])

			filteredResults.forEach(item => {
				if (item.sceneCategory) {
					existingSceneCategories.add(item.sceneCategory)
				}
			})

			// 更新标签可见性
			nextLevelTags.forEach(item => {
				item.visible = existingSceneCategories.has(item.name) || item.name === '全部'
			})
			break

		case 3: // 第三级是secondArr，下一级是thirdArrList
			nextLevelTags = thirdArrList.value
			if (!nextLevelTags) return // 确保thirdArrList已初始化

			// 收集筛选结果中存在的推荐标签
			const existingRecommendTags = new Set(['全部'])

			filteredResults.forEach(item => {
				if (item.recommendTags) {
					const tags = typeof item.recommendTags === 'string'
						? item.recommendTags.split(',')
						: Array.isArray(item.recommendTags)
							? item.recommendTags
							: [item.recommendTags]

					tags.forEach(tag => {
						if (tag && typeof tag === 'string') {
							existingRecommendTags.add(tag.trim())
						}
					})
				}
			})

			// 更新标签可见性
			nextLevelTags.forEach(item => {
				item.visible = existingRecommendTags.has(item.name) || item.name === '全部'
			})
			break

		default:
			// 最后一级没有下一级，不需要更新
			break
	}
}

// 根据筛选级别更新所有下级标签的显示状态
const updateLowerLevelTags = (filteredResults, currentLevel) => {
	if(show_list_nav_current.value!=0) return 
	// 如果没有筛选结果，重置所有下级标签可见性
	if (!filteredResults || filteredResults.length === 0) {
		// 重置从当前级别+1到最后一级的所有标签
		for (let level = currentLevel + 1; level <= 4; level++) {
			resetTagsVisibilityByLevel(level)
		}
		return
	}

	// 所有级别的"全部"选项始终可见
	// 根据当前级别，更新所有下级标签

	// 根据当前级别处理不同的下级标签
	switch (currentLevel) {
		case 1: // 第一级是voiceTypeArr，更新2、3、4级
			// 处理第2级（性别/年龄）
			updateLevelTags(filteredResults, 2)
			// 同时处理第3级（场景分类）
			updateLevelTags(filteredResults, 3)
			// 同时处理第4级（推荐标签）
			updateLevelTags(filteredResults, 4)
			break

		case 2: // 第二级是gendersArr，更新3、4级
			// 处理第3级（场景分类）
			updateLevelTags(filteredResults, 3)
			// 同时处理第4级（推荐标签）
			updateLevelTags(filteredResults, 4)
			break

		case 3: // 第三级是secondArr，更新第4级
			// 处理第4级（推荐标签）
			updateLevelTags(filteredResults, 4)
			break

		default:
			// 最后一级没有下级，不需要更新
			break
	}
}

// 辅助函数：更新特定级别的标签
const updateLevelTags = (filteredResults, level) => {
	let tags
	let tagExtractor

	switch (level) {
		case 2: // 性别/年龄级别
			tags = gendersArr.value
			// 收集筛选结果中存在的性别/年龄标签
			const existingGenders = new Set(['全部'])

			filteredResults.forEach(item => {
				// 收集性别和年龄段
				if (item.gender) {
					// 处理可能的复合格式，如"男、青年"
					const genderInfo = item.gender.split('、')
					genderInfo.forEach(info => {
						existingGenders.add(info.trim())
						// 尝试提取年龄段信息（如青年、中年、老年）
						if (info.includes('青年') || info.includes('中年') || info.includes('老年') || info.includes('少年')) {
							existingGenders.add(info.trim())
						}
					})
				}

				// 如果有专门的年龄组字段
				if (item.ageGroup) {
					existingGenders.add(item.ageGroup)
				}
			})

			// 更新标签可见性
			tags.forEach(item => {
				item.visible = existingGenders.has(item.name) || item.name === '全部'
			})
			break

		case 3: // 场景分类级别
			tags = secondArr.value
			// 收集筛选结果中存在的场景分类
			const existingSceneCategories = new Set(['全部'])

			filteredResults.forEach(item => {
				if (item.sceneCategory) {
					existingSceneCategories.add(item.sceneCategory)
				}
			})

			// 更新标签可见性
			tags.forEach(item => {
				item.visible = existingSceneCategories.has(item.name) || item.name === '全部'
			})
			break

		case 4: // 推荐标签级别
			tags = thirdArrList.value
			if (!tags) return // 确保thirdArrList已初始化

			// 收集筛选结果中存在的推荐标签
			const existingRecommendTags = new Set(['全部'])

			filteredResults.forEach(item => {
				if (item.recommendTags) {
					const tags = typeof item.recommendTags === 'string'
						? item.recommendTags.split(',')
						: Array.isArray(item.recommendTags)
							? item.recommendTags
							: [item.recommendTags]

					tags.forEach(tag => {
						if (tag && typeof tag === 'string') {
							existingRecommendTags.add(tag.trim())
						}
					})
				}
			})

			// 更新标签可见性
			tags.forEach(item => {
				item.visible = existingRecommendTags.has(item.name) || item.name === '全部'
			})
			break
	}
}

// 高亮显示匹配的文本
const highlightMatchText = (text, keyword) => {
	if (!keyword || keyword.trim() === '' || !text) {
		return text
	}

	// 防止XSS攻击，对关键词进行转义
	const escapeRegExp = (string) => {
		return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
	}

	const escapedKeyword = escapeRegExp(keyword.toLowerCase())
	const regex = new RegExp(`(${escapedKeyword})`, 'gi')

	return text.replace(regex, '<span style="color: #0AAF60; font-weight: bold;">$1</span>')
}

// 根据是否有搜索关键词返回平台昵称
const getDisplayName = (item) => {
	if (!input_search.value || input_search.value.trim() === '') {
		return item.platformNickname || ''
	}

	// 检查是否匹配昵称
	if ((item.platformNickname || '').toLowerCase().includes(input_search.value.toLowerCase())) {
		return highlightMatchText(item.platformNickname || '', input_search.value)
	}

	// 如果昵称不匹配，正常显示
	return item.platformNickname || ''
}

// 根据是否有搜索关键词返回情感标签
const getDisplayEmotionTag = (item) => {
	if (!input_search.value || input_search.value.trim() === '') {
		return item.emotionTags || ''
	}

	// 检查是否匹配情感标签
	if ((item.emotionTags || '').toLowerCase().includes(input_search.value.toLowerCase())) {
		return highlightMatchText(item.emotionTags || '', input_search.value)
	}

	// 检查是否匹配场景分类
	if ((item.sceneCategory || '').toLowerCase().includes(input_search.value.toLowerCase())) {
		// 如果匹配场景分类，添加场景分类信息到标签
		return `${item.emotionTags || ''} <span style="color: #0AAF60; font-weight: bold;">[${item.sceneCategory}]</span>`
	}

	// 如果都不匹配，正常显示
	return item.emotionTags || ''
}
let intonationSlider=ref(null)
let speechSlider = ref(null);
let volumeSlider = ref(null);
let volumeValue=ref(100)//试听和合成音量调节
let formatTooltipIntonation = (val) => val + '%';
let formatTooltipSpeech = (val) => val.toFixed(2) + 'x';
let formatTooltipVolume = (val) => val + '%';
let addUnitToInput=(inputEl, unit)=>{
  if (!inputEl) return;
  inputEl.classList.add('unit-input');
  inputEl.setAttribute('data-unit', unit);
}
// 输入时保持单位显示（防止被清除）
let onIntonationInput=(val)=>{
  intonationValue.value = val;
  nextTick(() => {
    addUnitToInput(intonationSlider.value.$el.querySelector('.el-input__wrapper'), '%');
  });
}

let onSpeechInput=(val)=>{
  speechValue.value = val;
  nextTick(() => {
    addUnitToInput(speechSlider.value.$el.querySelector('.el-input__wrapper'), 'x');
  });
}
let slider_input_init=()=>{
	nextTick(() => {
		addUnitToInput(intonationSlider.value.$el.querySelector('.el-input__wrapper'), '%');
		addUnitToInput(speechSlider.value.$el.querySelector('.el-input__wrapper'), 'x');
		addUnitToInput(volumeSlider.value.$el.querySelector('.el-input__wrapper'), '%');
	});
}
let show_fold_list=ref(false)//是否折叠音色列表
let show_ai_match=ref(false)//是否展示ai智能匹配
let ai_match_ref=ref(null)//ai智能匹配
//点击展开音色列表
let fold_list_click=()=>{
	show_fold_list.value=false
	show_ai_match.value=true
	if(show_list_nav_current.value==1&&sortedArr2.value.length>0){
		nextTick(()=>{
			if(ai_match_ref.value){
				ai_match_ref.value.list=sortedArr2.value.slice(0,10)
			}

		})
	}
	// show_list_left.value=false
}
//音色列表悬浮按钮
let fold_list_btn_click=()=>{
	show_fold_list.value=true
	show_ai_match.value=false
	// show_list_left.value=false
}
let show_list_left=ref(false)//左侧折叠只有点击收起音色列表展示
let gettxt=()=>{
	const originalDiv = document.getElementById('editor');
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	textInfo = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
		// console.log(parseInt(val))
		return `<#${parseInt(val) / 1000}#>`
	})
	// console.log('jjjjj',textInfo)
	textInfo = textInfo.replace(/\n/g, '');
	let parts = textInfo.split(/(\[sound:.*?\])+/);
	let finalText = parts.filter(part => part.trim() !== '')
	// console.log('finalText',finalText)
	let length = 0
	for (let i = 0; i < finalText.length; i++) {
		// console.log(finalText[i])
		if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
			if (finalText[i].length <= 500) {
				length += finalText[i].length
			} else {
				finalText[i] = finalText[i].substring(0, 500 - length);
				// debugger
			}
		}
	}
	// console.dir(finalText.join(''))
	// debugger

	textInfo = finalText.join('')
	return textInfo
}
let sortedArr2=ref([])
//点击ai智能匹配
let ai_match_click=()=>{
	if(!loginStore.token){
		proxy.$modal.open('组合式标题')
		return
	}
	show_fold_list.value=true
	show_ai_match.value=true
	nextTick(async()=>{
	ai_match_ref.value.loading=true
	textInfo=gettxt()
	if(textInfo==''){
		ElMessage({
			message: '请先输入文本信息',
			type: 'warning',
		})
		ai_match_ref.value.loading=false
		return
	}
	await soundListFun()
	cancelNavRequest.value = axios.CancelToken.source();
	let apiCall =await selectVoiceByAI({userId: JSON.parse(localStorage.getItem('user'))?.userId || '',question:textInfo,cancelToken: cancelNavRequest.value.token })
	try {
	const res = await apiCall
	console.log(res,'res');
	if(res?.code === 0 &&res?.data?.status_code==200&& res?.data?.content?.result.length>0){
		console.log(res,res.data,'res');
		// 建立 id -> index 映射
		const idOrderMap = {}
		  // 建立 id -> match 值映射
  		const idToMatchMap = {}
		res.data.content.result.forEach(([id,matchValue ], index) => {
			idOrderMap[id] = index
			idToMatchMap[id] = matchValue
		})
		// 给 soundList 中对应对象添加 match 字段
		soundList.value.forEach(item => {
			if(idToMatchMap.hasOwnProperty(item.id)){
				item.match = idToMatchMap[item.id]/5*100
			} else {
			item.match = null // 或者不赋值，根据需求
			}
		})
		// 根据映射排序 arr2
		sortedArr2.value = soundList.value.slice().sort((a, b) => {
		const indexA = idOrderMap[a.id]
		const indexB = idOrderMap[b.id]

		// 如果某个 id 不在 arr1 中，放到后面
		if (indexA === undefined) return 1
		if (indexB === undefined) return -1

		return indexA - indexB
		})

		// show_list_left.value=false

			if(ai_match_ref.value){

				// setTimeout(() => {
					ai_match_ref.value.loading=false
					ai_match_ref.value.list=sortedArr2.value.slice(0,10)
				// }, 500);
			}

	}else{
		ElMessage({
			message: '匹配失败，暂无相应音色',
			type: 'error',
		})
		soundList.value = []
		originalSoundList.value = []
		ai_match_ref.value.loading=false
	}
	} catch (err) {
		console.log(err, 'err')
		soundList.value = []
		originalSoundList.value = []
		ai_match_ref.value.loading=false
	}
})
}
//点击折叠音色列表
let expand_list_click=()=>{
	show_fold_list.value=true
	// show_list_left.value=true
}
let show_list_nav_current=ref(0)
let change_list_nav=(data)=>{
	console.log(data);
	soundList.value = []
	originalSoundList.value = []
	cancelNavRequest.value&&cancelNavRequest.value.cancel('请求已被取消');

	clearEmotion()
	show_list_nav_current.value=data
	selectetMycurrent.value=''
	if(data==0){
		isShow.value = true
		soundListFun()
	}else if(data==1){
		isShow.value = false
		ai_match_click()
	}else if(data==2){
		isShow.value = false
		if(!loginStore.token){
			proxy.$modal.open('组合式标题')
			soundList.value=[]
			return
		}
		selectetMyClick('收藏')
	}

}
let selectetMycurrent=ref('')
let selectetMyClick=async(data)=>{
	return new Promise(async (resolve, reject) => {
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
         return resolve(new Error('未登录'))
    }
	clearEmotion()
	soundList.value = []
	originalSoundList.value = []
	selectetMycurrent.value = data
	allSoundList.value =[]
	// 根据 data 选择对应的接口调用
	let apiCall
	cancelNavRequest.value = axios.CancelToken.source();
	try {
	if (data === '已购') {
		const userId = JSON.parse(localStorage.getItem('user'))?.userId || ''
		apiCall = await queryUserBuyVoiceName({ userId,tts: 4 })
	} else if (data === '收藏') {
		apiCall =await bookmarkList({ tts: 4,userId: JSON.parse(localStorage.getItem('user'))?.userId || '' ,cancelToken: cancelNavRequest.value.token})
		console.log(apiCall,'收藏');
	} else if (data === '历史'){


		apiCall =await queryUserUsedVoiceName({ userId: JSON.parse(localStorage.getItem('user'))?.userId || '' ,tts: 4 ,cancelToken: cancelNavRequest.value.token})

	}else{
		apiCall =await cloneList({ userId: JSON.parse(localStorage.getItem('user'))?.userId || '',cancelToken: cancelNavRequest.value.token })
			console.log(apiCall,'克隆');
	}

	const res = await apiCall
	if(data != '收藏'&&res.code === 0 && res?.data?.content?.result.length>0){

		res.data.content.result.forEach(item => {
			item.isPlay = false
			item.isSelected = false
			Object.assign(item, item.info)
		})
		let data1=JSON.parse(JSON.stringify(keysToCamelCase(res.data.content.result)))
		if(data=='历史'){
			data1=data1.slice(0,10)
		}
		Object.assign(originalSoundList.value, data1)
	}else if (data == '收藏' && res.length > 0) {


		res.forEach(item => {
			item.isPlay = false
			item.isSelected = false
		})
		Object.assign(originalSoundList.value, JSON.parse(JSON.stringify(res)))
	} else if (res.data.length > 0) {
		res.data.forEach(item => {
			item.isPlay = false
			item.isSelected = false
		})
		Object.assign(originalSoundList.value, JSON.parse(JSON.stringify(res.data)))
	}else{
		originalSoundList.value = []
	}
	// 处理音色列表，确保SVIP音色排在前面
	let processedData = JSON.parse(JSON.stringify(originalSoundList.value));
	allSoundList.value=JSON.parse(JSON.stringify(originalSoundList.value));
	// 使用更严格的排序逻辑
	processedData = sortVoicesByMembership(processedData);
	// 更新显示列表
	soundList.value = processedData;
	  resolve(res)  // 请求成功，resolve结果
	} catch (err) {
		console.log(err, 'err')
		soundList.value = []
		originalSoundList.value = []
		allSoundList.value=[]
		resolve(err)
	}
	  })
}
let toCamelCase=(str)=>{
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}
let keysToCamelCase=(obj)=>{
  if (Array.isArray(obj)) {
    return obj.map(v => keysToCamelCase(v))
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = toCamelCase(key)
      acc[camelKey] = keysToCamelCase(obj[key])
      return acc
    }, {})
  }
  return obj
}
let slideVisible=ref(false)
let speechVisible=ref(false)
let intonationVisible=ref(false)
let volumeVisible=ref(false)
let slideValueClose=()=>{
	slideVisible.value = false
}
let intonationValueClose=()=>{
	intonationVisible.value = false
}
let speechValueClose=()=>{
	speechVisible.value = false
}
let volumeValueClose=()=>{
	volumeVisible.value = false
}
//ai智能推荐选择
let choose_ai=(data)=>{
	selectSoundItem(data)
}
let ai_match_close=()=>{
	show_ai_match.value=false
	show_fold_list.value=false
}
let menuVisible = ref(false)
let menuRef = ref(null)
let dropdownRef=ref(null)
let getTextNodes=(node)=>{
  let textNodes = []
  if (node.nodeType === Node.TEXT_NODE) {
    textNodes.push(node)
  } else {
    node.childNodes.forEach(child => {
      textNodes = textNodes.concat(getTextNodes(child))
    })
  }
  return textNodes
}
let rightPos=ref(0)
let textAfterCaret=ref('')
//点击右键展示试听菜单
let handleRightClick = (event) => {
  if(!hasImg.value) return

    event.preventDefault() // 阻止默认右键菜单
    menuVisible.value = true

    // 获取选中的文本
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      textAfterCaret.value = selection.toString()
    } else {
      textAfterCaret.value = ''
    }
    console.log('选中的文本:', textAfterCaret.value)

    // 你可以根据选中范围计算百分比等
    const editor = editorRef.value
    if (!editor) return

    let charIndex = 0
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      // 计算选区开始位置的字符索引
      const textNodes = getTextNodes(editor)
      let found = false
      for (const node of textNodes) {
        if (node === range.startContainer) {
          charIndex += range.startOffset
          found = true
          break
        } else {
          charIndex += node.textContent.length
        }
      }
    }
    const totalLength = editor.innerText.length
    rightPos.value = totalLength > 0 ? (charIndex / totalLength) * 100 : 0

}

//右键试听
let right_listen=()=>{
	console.log(textAfterCaret,'textAfterCaret');
	menuVisible.value = false
		if(textAfterCaret.value==''){
			ElMessage({
				message: '请先选择文本信息',
				type: 'warning',
			})
			return
		}
		syntheticAudioButton(3)

}
let hasImg = ref(false)

let  hasChooseVoiceImg=(element)=>{


 const text = element.innerText || element.textContent || ''
 console.log(text,text.length>0,4444444444);
 return text.length>0
}
//音色列表收藏
let collectClick=async(item)=>{
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let status='',tip='',result=0
    if(item.bookmark==0){
        result=1
        status='success'
        tip='收藏成功'
    }else{
        result=0
        status='error'
        tip='取消收藏'
    }

    try {
        let data=await bookmarkToggle({voiceId:item.id,type:result,tts:4,userId:loginStore.userId})
        if(data.code==1){
            ElMessage({ message:data.msg , type: 'error' });
            return
        }
        item.bookmark=result
        ElMessage[status](tip);
    }catch (error) {
        console.log(error,'error');
        ElMessage({ message:'数据加载失败，请刷新页面重试' , type: 'error' });

    }
}
let full_loading=ref(false)//全屏loading
let full_loading_timer = null;//全屏loading定时器
let full_loading_active_index = ref(0);//全屏loading当前柱子
let ai_copywriting_create_ref = ref(null);//AI文案创作弹窗
let ai_copywriting_show = ref(false); // AI文案弹窗
let ai_copywriting_tempalte_more_ref = ref(null);// AI文案模板更多弹窗
let ai_copywriting_toolbar_input_ref = ref(null); // AI文案工具栏输入框
let show_suspended_toolbar=ref(true)//悬浮工具栏
let {
      ai_copywriting_list,
      ai_copywriting_toolbar_current,
      ai_copywriting_toolbar_current_obj,
      inputText,
      ai_copywriting,
      change_copywriting_toolbar,
      ai_copywriting_more,
      ai_copywriting_input,
      handleKeydown,
      handlePaste,
      handleCompositionStart,
      handleCompositionEnd,
      ai_copywriting_send_message,
	  stopWriting
    } = useAiCopywriting({
      proxy,
      templateList,
	  hasImg,
	  ai_copywriting_show,
	  ai_copywriting_tempalte_more_ref,
	  ai_copywriting_create_ref,
	  ai_copywriting_toolbar_input_ref,
	  show_suspended_toolbar,
	  editorRef
    });//1.2文案创作相关方法
	//文案创作重新生成
	let createCopyWriting=(data)=>{
		ai_copywriting_send_message(ai_copywriting_create_ref.value,data)
	}
	//文案创作插入编译器
	let insert_input=(data)=>{
		console.log('插入');
		editorRef.value.insertAdjacentHTML('beforeend', data);
	}
	let recover_toolbar=()=>{
		 if (editorRef.value) {
			// 创建一个新的 input 事件
			const event = new Event('input', {
				bubbles: true,
				cancelable: true,
			});
			// 触发事件
			editorRef.value.dispatchEvent(event);
			show_suspended_toolbar.value=true
		}
	}
	let try_listen_status=ref('end')//设置试听播放状态
	//试听设置状态
	let change_play_status=(data)=>{
		console.log('试听状态',data);

		if(synthetic_button_type.value==2||synthetic_button_type.value==3){
			try_listen_status.value=data
		}
	}
	//试听按钮控制
	let try_listen_change=()=>{
		AudioPlayerRef.value.playAudio()
	}
	//试听暂停
	let try_listen_stop=()=>{
		console.log('试听暂停');

		AudioPlayerRef.value.audio_stop()
	}
	//类型下载
	let clickDownlaodChildButton=async(e)=>{
		console.log('点击二级菜单', e)
		synthetic_button_type.value = ''
		let audio_url=audioUrl.value
		if ( audio_url == '') {
				ElMessage({
					message: '暂无音频文件',
					type: 'warning',
				})
				return
		}
		let suffix='mp3'
		if (e === 1) {
			try {
				const userId = JSON.parse(localStorage.getItem('user'))?.userId || '';
				const data = await extractWavByMp3({ userId, audioUrl: audio_url });
				if (data.code === 0) {
					audio_url = data.data.content.result || '';
					suffix='wav'
				} else {
					audio_url=''
					ElMessage({ message: data.msg || '处理失败', type: 'warning' });
				}
			} catch (err) {
				audio_url=''
				ElMessage({ message: '请求异常', type: 'error' });
				console.error(err);
			}
			}
		const link = document.createElement('a');
		textInfo=gettxt()
		console.log(textInfo,synthesis_data.value.platformNickname,'textInfo');
		let txt=`${textInfo.slice(0, 5)}—${synthesis_data.value.platformNickname}`
		fetch(audio_url)
		.then(response => response.blob()) // 获取二进制 Blob
		.then(blob => {
			const url = URL.createObjectURL(blob); // 创建本地 URL

			link.href = url;
			link.download = `${txt}.${suffix}`; // 设置下载文件名

			link.click(); // 触发下载

			// 1秒后释放 URL，避免内存泄漏
			setTimeout(() => {
				URL.revokeObjectURL(url);
			}, 1000);
		})
		.catch(err => {
			console.error('下载失败:', err);
		});
		link.style.display = 'none';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
	let findReplacementVisible=ref(false)
	let multilingualVisible=ref(false)
	let multilingual_popup_ref=ref(null)
	// 点击页面非指定class元素时关闭popover
	let useClickOutside=(visibleRef, excludeClassName)=>{
		const handler = (event) => {
			const target = event.target
			if (!target) return

			// 判断点击元素是否是排除的class或其子元素
			if (target.closest(`.${excludeClassName}`)) {
			// 点击的是排除区域，不关闭
			return
			}

			// 点击了其他地方，关闭popover
			visibleRef.value = false
		}

		onMounted(() => {
			document.addEventListener('click', handler)
		})

		onUnmounted(() => {
			document.removeEventListener('click', handler)
		})
	}
	useClickOutside(findReplacementVisible, 'find_replacement_popover')
	useClickOutside(multilingualVisible, 'multilingual_popover')
	useClickOutside(ai_copywriting_show,'ai_copywriting_toolbar')
	let allVisibles = {
		findReplacement: findReplacementVisible,
		multilingual: multilingualVisible,
	}
	let {
		matches,
		findText,
		currentIndex,
		clearHighlights,
		highlightMatches,
		updateCurrentHighlight,
		handleUpward,
		handleDownward,
		handleReplace,
		handleReplaceAll,
		handleFind,
		findReplaceInit
	} = useFindReplace(editorRef,findReplacementVisible)
	//查找替换弹窗
	let find_replacement=()=>{
		findReplaceInit()
		findReplacementVisible.value=true
	}
	//多语种弹窗
	let multilingual=async()=>{
		multilingualVisible.value=true

	}
	watch(multilingualVisible, async(newVal, oldVal) => {
		if(newVal){
			await nextTick()
			multilingual_popup_ref.value[0].choose_item(current_language.value)
		}
	},{immediate:true,deep:true})

	//当前情绪
	let current_emotion=ref('neutral')
	//选择情绪
	let choose_emotion=(data)=>{
		current_emotion.value=data.value
		let index=soundList.value.findIndex(item=>item.voiceName==SoundItemId.value)
		if (soundList.value[index].isPlay) {
			playAudio(soundList.value[index], index)
			playAudio(soundList.value[index], index)
		}
	}
	//当前语言
	let current_language=ref('auto')
	let current_source=ref(1)//1 普通音色 2我的克隆
	//选择语言
	let choose_language=(data)=>{
		current_language.value=data.value
		setTimeout(()=>{
			multilingualVisible.value=false
		},100)

	}
	//清空文本
   let clear_text=()=>{
	if (editorRef.value) {
		editorRef.value.innerText = '';
		hasImg.value=false
		textLength.value = 0
	}
   }
   let initialized = false;
     watch(() => loginStore.token, (newVal, oldVal) => {
      console.log('token变化:', newVal);
       if (!initialized) {
			initialized = true;
			return;  // 第一次执行跳过刷新
		}
		if (!newVal) {
			window.location.reload();
			useAIDubbing.bgmusic_url = ''
			useAIDubbing.bgmusicObj = {}
		}
    }, { immediate: true,deep:true });
let cancel_check=()=>{
	full_loading.value=false
}


let insertImageAtCursor=(src)=>{
  const sel = window.getSelection();
  if (!sel.rangeCount) return;

  const range = sel.getRangeAt(0);
  range.deleteContents();

  const img = document.createElement('img');
  img.src = src;
  img.style.maxWidth = '100%'; // 限制图片宽度，防止撑破布局

  range.insertNode(img);

  // 把光标移到图片后面
  range.setStartAfter(img);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
}
let insertTextAtCursor=(text)=>{
  const sel = window.getSelection();
  if (!sel.rangeCount) return;

  const range = sel.getRangeAt(0);
  range.deleteContents();

  const textNode = document.createTextNode(text);
  range.insertNode(textNode);

  range.setStartAfter(textNode);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
}
// 是否显示个性声音定制按钮
const isShow = ref(true)
// 跳转克隆页面
const toClone = ()=>{
	router.push('/VoiceClone')
}
</script>

<template>
	<img src="@/assets/images/aiImages/zhenxiang.svg" style="display:none" alt="预加载背景图" loading="eager"/>
	<img src="@/assets/images/aiImages/jingpin.svg" style="display:none" alt="预加载背景图" loading="eager"/>
	<div class="main_content ai_dubbing" id="app_out_in" @click="handleContainerClick">
		<!-- 导入文案弹窗 -->
		<GlobalimportLetter ref="Letter" @call-parent="handleCallLetter"></GlobalimportLetter>
		<!--  音效弹窗  -->
		<soundEffects ref="effects" @call-parent="handleCallParent"></soundEffects>
		<!-- 播放音色列表 -->
		<audio ref="audioRef">
			<source type="audio/mpeg">
		</audio>

		<!--    多音字选择弹窗div   -->
		<el-popover :visible="showPopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;height:fit-content`">
			<div class="flex" contenteditable="false">
				<div style="padding:3px 5px;background-color: #fff;border: 1px solid #e5e5e5;border-radius: 4px"
					class="polyphonic_item margin_r-8 cursor-pointer font-size-13"
					v-for="(item, index) in polyphonicList" :key="index" @click="clickPolyphonic(item)">{{ item }}</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!--  停顿弹窗  -->
		<el-popover :visible="stopPopover" popper-class="polyphonic pause-popover" @click="handlePopoverClick"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;height:fit-content`">
			<div class="flex" contenteditable="false">
				<div style="padding:3px 5px;background-color: #fff;border: 1px solid #e5e5e5;border-radius: 4px"
					class="polyphonic_item margin_r-8 cursor-pointer font-size-13"
					v-for="(item, index) in standstillList" :key="index" @click="clickStandstill(item)">{{ item }}ms
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!-- 数字符号弹窗  -->
		<el-popover :visible="figurePopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;height:fit-content`">
			<div contenteditable="false">
				<div style="margin-bottom:4px;" class="polyphonic_item1 cursor-pointer font-size-13"
					v-for="(item, index) in figureList" :key="index" @click="clickFigure(item)">
					<div class="font-size-12 iii">
						<span class="margin_r-10">{{ item.title }}</span>
						<span>{{ item.num }}</span>
					</div>
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>

		<!--  读音替换弹窗 -->
		<el-popover :visible="aliasPopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:180px;height:fit-content`">
			<div contenteditable="false">
				<el-input v-model="aliasValue" placeholder="请输入读音替换"></el-input>
				<div class="button_div flex flex_a_i-center margin_t-10">
					<el-button @click="addAlias" type="primary" size="default" style="margin:auto;">确定</el-button>
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!-- 查找替换弹窗 -->

		<!--  的<span class="NTUY" id="NTUY">风</span>-->
		<!--  <div data-type="alias" data-key="风" contenteditable="false" style="border: 1px solid purple; background-color: rgb(240, 224, 255); padding: 5px; border-radius: 6px; display: inline-block; margin-left: 5px; cursor: pointer; pointer-events: auto;">-->
		<!--    <span class="change">44</span>-->
		<!--    <button style="font-size:14px; margin-left:5px;">×</button>-->
		<!--  </div>-->
		<!--  格<span class="EXQz" id="EXQz">77</span>-->
		<!--  <div data-type="alias" data-key="和" contenteditable="false" style="border: 1px solid purple; background-color: rgb(240, 224, 255); padding: 5px; border-radius: 6px; display: inline-block; margin-left: 5px; cursor: pointer; pointer-events: auto;">-->
		<!--    <span class="change">77</span>-->
		<!--    <button style="font-size:14px; margin-left:5px;">×</button>-->
		<!--  </div>-->
		<div class="main_content_bottom flex">
			<!--    中间左边输入文案  -->
			<div class="width-full height-full main_content_bottom_item_left ai_dubbing_left margin_r-30  flex flex_d-column"
				style="padding-bottom: 6px">

				<!--    输入文案顶部操作图标      -->
				<div class="main_content_bottom_item_left_top flex "
					contenteditable="false">
					<div class="main_content_bottom_item_left_top_left flex margin_r-13">
						<!-- ai智能匹配 -->
						 <!-- <div class="ai_Intelligent_matching cursor-pointer" @click="ai_match_click">
							<img src="@/assets/images/aiImages/ai_Intelligent_matching.svg" alt="">
							<img class="ai_Intelligent_matching_icon" src="@/assets/images/aiImages/ai_Intelligent_matching_new.svg" alt="">
						 </div> -->
						<div class="main_content_bottom_item_left_top_left_finish">
							<div class="main_content_bottom_item_left_top_left_finish_item" v-loading="trial_listening">
								<template v-if="audioUrl==''||synthetic_button_type!=2">
									<div class="main_content_bottom_item_left_top_left_finish_item_content" @click.stop="syntheticAudioButton(2)">
										<img src="@/assets/images/aiImages/try_listen_disable.svg" class="main_content_bottom_item_left_top_left_finish_item_img" alt="">
										<span class="main_content_bottom_item_left_top_left_finish_item_text">试听</span>
									</div>
								 </template>
								<template v-else>
									<div class="main_content_bottom_item_left_top_left_finish_item_content" @click="try_listen_stop" v-if="try_listen_status=='play'">
										<img src="@/assets/images/aiImages/try_listen_play.svg" class="main_content_bottom_item_left_top_left_finish_item_img" alt=""/>
										<span class="main_content_bottom_item_left_top_left_finish_item_text">试听</span>
									</div>
									<div class="main_content_bottom_item_left_top_left_finish_item_content"  @click.stop="syntheticAudioButton(2)" v-else>
										<img src="@/assets/images/aiImages/try_listen_pause.svg" class="main_content_bottom_item_left_top_left_finish_item_img" alt=""/>
										<span class="main_content_bottom_item_left_top_left_finish_item_text">试听</span>
									</div>	
								</template>
							</div>
							<div class="main_content_bottom_item_left_top_left_finish_item" @click="syntheticAudioButton(1)" v-loading="loading">
								<el-tooltip  effect="dark" content="合成将消耗字符数" placement="top-start"  append-to="#app">
									<div class="main_content_bottom_item_left_top_left_finish_item_content">
										<img src="@/assets/images/aiImages/synthetic.svg" v-if="synthetic_button_type==1&&audioUrl!=''" class="main_content_bottom_item_left_top_left_finish_item_img" alt="">
										<img src="@/assets/images/aiImages/synthetic_disable.svg" v-else class="main_content_bottom_item_left_top_left_finish_item_img" alt="">
										<span class="main_content_bottom_item_left_top_left_finish_item_text">合成</span>
									</div>
								</el-tooltip>
							</div>
							<div class="main_content_bottom_item_left_top_left_finish_item" 	v-if="(audioUrl != '' || captions_url != '')&&synthetic_button_type!=2">
								
									<el-dropdown placement="bottom" trigger="click" popper-class="aibubbing_download_menu"  append-to="#app"
									
										:popper-options="{
											modifiers: [
												{
												name: 'offset',
												options: {
													offset: [16,13]
												}
												}
											]
										}"
										>
										<div class="main_content_bottom_item_left_top_left_finish_item_content">
											<img src="@/assets/images/aiImages/download.svg" alt="" class="main_content_bottom_item_left_top_left_finish_item_img">
											<span class="main_content_bottom_item_left_top_left_finish_item_text">去下载</span>
										</div>
												<template #dropdown>
													<el-dropdown-menu>
														<template v-for="(item, index) in downlaodButtonArr[0].children" :key="index">
															<el-dropdown-item  class="has-children" @click.stop="clickDownlaodChildButton(index)">
																
																<span>{{ item.name }}</span>
														
															</el-dropdown-item>
														</template>
													</el-dropdown-menu>
												</template>
									</el-dropdown>
							</div>
						</div>
						<div class="main_content_bottom_item_left_top_left_tool">
							<div class="main_content_bottom_item_left_top_left_item flex  flex_j_c-center flex_a_i-center cursor-pointer "
								v-for="(item, index) in iconsArr" :key="index" @click="clickIcons($event, index)"
								:data-id="`parent-${index}`" :class="item.className?item.className:''"       ref="iconRefs">
								<!-- <Iconfont class="margin_b-6" size="26px" :name="`${item.IconName}`" /> -->

								<template v-if="item.needFixed">
									<el-popover  trigger="click"  v-model:visible="allVisibles[item.key].value" append-to="#app"
										:popper-class="item.className ? `${item.className} action_bar_popover` : 'action_bar_popover'"
										width="fit-content"
										:popper-options="{
											modifiers: [
											{
												name: 'offset',
												options: {
												offset: [0,0] // [skidding, distance]
												}
											}
											]
										}"
									>
											<template #default>
												<findReplacement v-show="findReplacementVisible&&item.key=='findReplacement'"
												ref="findReplaceRef"
													:total="matches.length"
													:current="currentIndex + 1"
													@find="handleFind"
													@upward="handleUpward"
													@downward="handleDownward"
													@replace="handleReplace"
													@replaceAll="handleReplaceAll"
												/>
												<multilingualPopup v-if="multilingualVisible&&item.key=='multilingual'" ref="multilingual_popup_ref" @choose="choose_language" />
											</template>
											<template #reference>

												<div class="flex flex_j_c-center flex_d-column flex_a_i-center">
													<div class="main_content_bottom_item_left_top_left_item_img">
														<img :src="item.IconName" alt="">
													</div>
													<span class="font-size-14">{{ item.name }}</span>
												</div>

											</template>
									</el-popover>
								</template>
								<template v-else>
									<div class="main_content_bottom_item_left_top_left_item_img">
										<img :src="item.IconName" alt="">
									</div>
									<span class="font-size-14">{{ item.name }}</span>
								</template>
							</div>
						</div>
						
					</div>
					<div class="main_content_bottom_item_left_top_right">
						<div class="bg_music_box  height-42 font-size-12 padding-10 flex flex_a_i-center flex_j_c-space-between" :class="Object.keys(synthesis_data).length > 0?'margin_r-12':''"
						v-if="useAIDubbing.bgmusic_url">
							<div class="main_content_bottom_item_left_top_right_left flex flex_a_i-center">
								<img src="@/assets/images/aiImages/aibubbing_bg_volume.svg" class="width-18 height-14 margin_r-3  cursor-pointer volume-popover" alt="" @click="click_volume">
								<div
									class="main_content_bottom_item_left_top_right_left_right width-130 height-full font-size-14 flex flex_d-column flex_j_c-center">
									<div >{{ useAIDubbing.bgmusicObj?.materialName ||
										useAIDubbing.bgmusicObj?.musicName }}</div>
								</div>
							</div>
							<img src="@/assets/images/aiImages/aibubbing_bg_close.svg" class="bg_music_box_close width-24 height-24 cursor-pointer" @click="close_music_div" alt="">
						</div>
						<!-- 已选音色 -->
						<!-- v-if="Object.keys(synthesis_data).length > 0" -->
						<div class="choose_timbre " v-if="Object.keys(synthesis_data).length > 0">
							<div class="choose_timbre_img">
								<img v-if="synthesis_data.avatarUrl!=''" :src="synthesis_data.avatarUrl" alt="">
								<!-- <div class="choose_timbre_img_status">

								</div> -->
							</div>

							<span>{{ synthesis_data.platformNickname }}</span>
						</div>
						<el-tooltip class="box-item" effect="dark" content="展开音色" placement="bottom" v-if="show_fold_list&&!show_ai_match">
							<img src="@/assets/images/aiImages/fold_list.svg" class="fold_list cursor-pointer"  @click="fold_list_click" alt="">
						</el-tooltip>
					</div>


				</div>
				<!--    输入文案div      -->
				<div class="main_content_bottom_item_left_bottom">
					<el-scrollbar>
						<!--    音量div    -->
						<div ref="volumePanel" class="position_volume width-40 height-160 flex flex_d-column flex_j_c-space-between flex_a_i-center padding-6-n volume-popover"
							data-id="`parent-100`" v-show="is_show_volume">
							<span class="position_volume_span1 font-size-12">{{ slideValue }}%</span>
							<el-slider v-model="slideValue" vertical height="100px" size="small"
								:show-tooltip="false" 	@keydown.enter.prevent="slideValueClose"  />
							<span class="position_volume_span2 font-size-12">音量</span>
						</div>
						<!--   显示字数/清空文本     -->
						<div id="charCount" class="main_content_bottom_untill font-size-12">
							<div class="number-words main_content_bottom_untill_item">
								<span>{{ textLength }}</span>/{{ MAX_LENGTH }}
							</div>
							<div class="clear_text main_content_bottom_untill_item" v-if="hasImg" @click="clear_text">
								<img src="@/assets/images/aiImages/main_content_bottom_untill_clear_text.svg" alt="">清空文本
							</div>
						</div>
						<!--   输入文案div     -->
						<!--          @paste="handlePaste"-->
						<!--          @compositionstart="handleCompositionStart"-->
						<!--          @compositionend="handleCompositionEnd"-->

						<div v-show="pinyinResult.length > 0 && pinyinBool" class="copywriting-div padding-20">
							<div v-for="item in pinyinResult" :key="item.character"
								style="display: inline-block; text-align: center; margin: 5px;">
								<div style="font-size: 14px; color: #303133;font-weight: 500">{{ item.pinyin }}</div>
								<div style="font-size: 14px;font-weight: 500">{{ item.character }}</div>
							</div>
						</div>
						<!--           @selectstart="selectstart"  @keyup="handleKeyUp"-->
						<div v-show="!pinyinBool" ref="editorRef" contenteditable="true" @input="handleInput"
							id="editor" data-id="`parent-10`"
							style="line-height: 22px;letter-spacing: 2px;font-weight:500; "
							:style="{'padding-bottom':ai_copywriting_show?'136px':'45px'}"
							class="copywriting-div dzm-textarea padding-25 font-size-14"
							placeholder="粘贴或输入文本内容，限5000个字内" @mouseup="mouseup" @click="handleClick"  @contextmenu="handleRightClick">
						</div>
						<!--    显示多音字弹窗      -->
						<!--          <el-popover-->
						<!--            placement="bottom"-->
						<!--            :width="200"-->
						<!--            trigger="click"-->
						<!--            :visible="popoverBool"-->
						<!--            class="popupStyle"-->
						<!--            style="top:200px;"-->
						<!--          >-->
						<!--            <template v-slot:content>-->
						<!--              454545-->
						<!--            </template>-->

						<!--          </el-popover>-->

						<!--          <el-popover ref="popover" trigger="click" placement="bottom" :width="200" style="position: absolute; top:2000px;left:200px;"-->
						<!--                      v-model:visible="popoverBool">-->
						<!--           -->

						<!--            <div >···内容···</div>-->
						<!--          </el-popover>-->





						<!--          <div class="AICopywriting_div width-440 height-90 margin_l-30 flex flex_d-column flex_a_i-center flex_j_c-space-between">-->
						<!--            <div class="AICopywriting_div_top height-40 flex flex_a_i-center padding-n-10">-->
						<!--              <img src="@/assets/images/aiImages/aiImgIcon.png" alt="" class="width-24 height-24">-->
						<!--              <div class="width-1 height-16 margin-n-5"></div>-->
						<!--              <el-input placeholder="输入您想要的文案类型" v-model="search_copywritingType" style="border:none"></el-input>-->
						<!--            </div>-->
						<!--            <div class="AICopywriting_div_bottom height-40 width-full flex flex_a_i-center flex_j_c-space-between">-->
						<!--              <div class="AICopywriting_div_bottom_left height-full flex flex_a_i-center">-->
						<!--                <div class="AICopywriting_div_bottom_left_item width-80 height-30 margin_r-10 flex flex_a_i-center flex_j_c-center cursor-pointer" v-for="item in 4">企业宣传</div>-->
						<!--              </div>-->
						<!--              <div class="AICopywriting_div_bottom_right height-full flex flex_a_i-center flex_j_c-flex-end font-size-14 cursor-pointer" @click="clickMoreIcon">-->
						<!--                更多-->
						<!--                <Iconfont-->
						<!--                    size="12px"-->
						<!--                    name="jiantou"-->
						<!--                />-->
						<!--              </div>-->
						<!--            </div>-->
						<!--          </div>-->

					</el-scrollbar>
					<!--   语速 语调 音量    -->

					<div class="main_content_bottom_left flex flex_a_i-center margin_r-35">
					 <!-- 语调 -->
					<el-popover placement="top" :width="600" trigger="hover" popper-class="aibubbing_gradient_slider_popover intonation_popover" v-model:visible="intonationVisible">
						<div class="flex flex_a_i-center main_content_bottom_left_speed_speech">
						<el-slider
							v-model="intonationValue"
							:min="-12"
							:max="12"
							:step="1"
							show-input
							:show-tooltip="false"
							@keydown.enter.prevent="intonationValueClose"
							:format-tooltip="formatTooltipIntonation"
							ref="intonationSlider"
							@input="onIntonationInput"
						/>
						</div>
						<template #reference>
						<div class="main_content_bottom_left_left_button height-30 flex flex_a_i-center padding-n-12 margin_r-12 cursor-pointer font-size-14">
							<span class="margin_r-12">语调</span>
							<span>{{ intonationValue }}%</span>
						</div>
						</template>
					</el-popover>
					<!-- 语速 -->
					<el-popover placement="top" :width="600" trigger="hover" popper-class="aibubbing_gradient_slider_popover speech_popover"  v-model:visible="speechVisible">
						<div class="flex flex_a_i-center main_content_bottom_left_speed_speech">
						<el-slider
							v-model="speechValue"
							:min="0.5"
							:max="2.0"
							:step="0.01"
							show-input
							:show-tooltip="false"
							@keydown.enter.prevent="speechValueClose"
							:format-tooltip="formatTooltipSpeech"
							ref="speechSlider"
							@input="onSpeechInput"
						/>
						</div>
						<template #reference>
						<div class="main_content_bottom_left_left_button height-30 flex flex_a_i-center padding-n-12 margin_r-12 cursor-pointer font-size-14">
							<span class="margin_r-12">语速</span>
							<span>{{ speechValue.toFixed(2) }}x</span>
						</div>
						</template>
					</el-popover>

					<!-- 音量 -->
					<el-popover
						placement="top"
						:width="600"
						trigger="hover"
						popper-class="aibubbing_gradient_slider_popover volume_popover"
						v-model:visible="volumeVisible"
					>
						<div class="flex flex_a_i-center main_content_bottom_left_speed_speech">
						<el-slider
							v-model="volumeValue"
							:min="0"
							:max="1000"
							:step="1"
							show-input
							:show-tooltip="false"
							@keydown.enter.prevent="volumeValueClose"
							:format-tooltip="formatTooltipVolume"
							ref="volumeSlider"
						/>
						</div>
						<template #reference>
						<div class="main_content_bottom_left_left_button height-30 flex flex_a_i-center cursor-pointer font-size-14">
							<span class="margin_r-12">音量</span>
							<span>{{ volumeValue }}%</span>
						</div>
						</template>
					</el-popover>
					</div>
					<!-- AI工具栏悬浮 -->
					 <!-- v-if="hasImg" -->
					<div class="ai_suspended_toolbar"  :style="{ top: aiPostion + 'px' }"  v-if="show_suspended_toolbar">
						<!-- <div class="ai_suspended_toolbar_item" @click="ai_copywriting"> -->
							<img src="@/assets/images/aiImages/ai_copywriting.svg" @click="ai_copywriting" alt="" >
							<!-- AI文案 -->
						<!-- </div> -->
						<!-- <div class="ai_suspended_toolbar_item"  @click="change_list_nav(1)">
							<img src="@/assets/images/aiImages/ai_choose_voice.svg" alt="" >
							AI选音色
						</div> -->
					</div>
					<!-- AI文案工具栏悬浮 -->
					  <!-- v-if="ai_copywriting_show" -->
					<div class="ai_copywriting_toolbar"  :style="{ top: copywriting_aiPostion + 'px' }"  v-if="ai_copywriting_show">
						<div class="ai_copywriting_toolbar_top">
							<div class="ai_copywriting_toolbar_top_list">
								<div class="ai_copywriting_toolbar_top_list_item" v-for="item in ai_copywriting_list.slice(0,4)" :key="item.id" @click="change_copywriting_toolbar(item)" :class="ai_copywriting_toolbar_current==item.id?'active':''">
									{{ item.templateName }}
								</div>
							</div>
							<div class="ai_copywriting_toolbar_top_more"  @click="ai_copywriting_more">
								<span>更多模版</span>
								<img src="@/assets/images/aiImages/ai_copywriting_toolbar_top_more.svg">
							</div>
						</div>
						<div class="ai_copywriting_container">
							<!-- 高亮输入框，contenteditable 替换 el-input -->
							<img src="@/assets/images/aiImages/ai_copywriting_textarea_img.png" class="ai_copywriting_textarea_img" alt="">
							<div
								class="ai_copywriting_toolbar_input"
								contenteditable="true"
								ref="ai_copywriting_toolbar_input_ref"
								:placeholder="`请输入文案`"
								@input="ai_copywriting_input"
								@keydown.enter="handleKeydown($event, ai_copywriting_create_ref)"
								@paste="handlePaste"
								@compositionstart="handleCompositionStart"
								@compositionend="handleCompositionEnd"
							></div>
							<div class="ai_copywriting_toolbar_buttons">
								<div class="ai_copywriting_toolbar_group">
									<el-button @click="ai_copywriting_send_message(ai_copywriting_create_ref)" link class="ai_copywriting_toolbar_btn" circle>
										<img  src="@/assets/images/aiImages/ai_copywriting_toolbar_send.svg" alt="发送" />
									</el-button>
								</div>
							</div>
						</div>
					</div>
					<!--AI文案创作弹窗-->
					<aiCopyWritingCreate ref="ai_copywriting_create_ref" @createCopyWriting="createCopyWriting" @insert_input="insert_input" @stopWriting="stopWriting(ai_copywriting_create_ref)" @recover_toolbar="recover_toolbar"></aiCopyWritingCreate>
					<!--AI文案查看更多弹窗-->
					<aiCopyWritingTempalteMore ref="ai_copywriting_tempalte_more_ref" :ai_copywriting_create_ref="ai_copywriting_create_ref" @send_message="createCopyWriting"  @recover_toolbar="recover_toolbar"></aiCopyWritingTempalteMore>
					 <!-- <img src="@/assets/images/aiImages/ai_choose_voice.png" class="ai_choose_voice cursor-pointer"   :style="{ top: aiPostion + 'px' }" v-if="hasImg"   @click="change_list_nav(1)" alt=""> -->
				</div>
				<!--   底部播放按钮等   -->
				<div class="speaker_content_bottom  flex flex_a_i-center flex_j_c-space-between">


					<!--        <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语速</span>-->
					<!--          <el-slider class="flex-item_f-8" v-model="speechValue" :min="0.5" :max="2.0" :step="0.1" :show-tooltip="false"/>-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ speechValue }}x</span>-->
					<!--        </div>-->
					<!--        <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语调</span>-->
					<!--          <el-slider class="flex-item_f-8" v-model="intonationValue" :min="-12" :max="12" :step="1" :show-tooltip="false"/>-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ intonationValue }}</span>-->
					<!--        </div>-->
					<!--    底部音频    -->
					<div class="speaker_content_bottom_bottom flex-item_f-2">
						<AudioPlayer ref="AudioPlayerRef" :audioUrl="audioUrl" :isPauseTtsAudio="isPauseTtsAudio" @change_play_status="change_play_status"
							@stopPlay="stop_timbre_play"></AudioPlayer>
					</div>
					<!--    中间 合成音频  快速试听    -->
							<!--  -->
					<div class="speaker_content_bottom_middle  flex flex_a_i-center flex_j_c-space-around padding_l-36">
						<!-- <div class="speaker_content_bottom_middle_item_right flex flex_a_i-center flex_j_c-center cursor-pointer margin_r-12  width-90"
							v-loading="trial_listening">
							<template v-if="try_listen_status=='end'">
								<div class="try_listen" @click.stop="syntheticAudioButton(2)" >
									<span class="text-align-center">试听</span>
								</div>
							</template>
							<template v-else>
								<div class="try_listen">
									<el-tooltip effect="dark" :content="try_listen_status=='play'?'暂停':'播放'" placement="top" append-to="#app"  popper-class="try_listen-tooltip">
										<div class="try_listen_item" @click="try_listen_change">
												<img src="@/assets/images/aiImages/try_listen_play.svg" alt="" v-if="try_listen_status=='play'">
												<img src="@/assets/images/aiImages/try_listen_pause.svg" alt="" v-else>
										</div>
									</el-tooltip>
									<el-tooltip  effect="dark" content="停止" placement="top" append-to="#app" popper-class="try_listen-tooltip">
									<div class="try_listen_item"  @click="try_listen_stop">
											<img src="@/assets/images/aiImages/try_listen_stop.svg" alt="">
									</div>
									</el-tooltip>
								</div>
							</template>
						</div> -->
						<!-- <el-dropdown placement="top" trigger="click" popper-class="aibubbing_download_menu"  append-to="#app"
							v-if="(audioUrl != '' || captions_url != '')&&synthetic_button_type!=2"
							>
							<div
								class="speaker_content_bottom_middle_item_right speaker_content_bottom_middle_item_download  flex flex_a_i-center flex_j_c-center cursor-pointer">
								<span class="span2 margin_l-10 text-align-center">去下载</span>
								<img src="@/assets/images/aiImages/download.svg" alt="" class="width-9 height-5">
							</div>
									<template #dropdown>
										 <el-dropdown-menu>
											<template v-for="(item, index) in downlaodButtonArr" :key="index">
												<template v-if="show_down_load_item(index)">
												<el-dropdown-item v-if="item.children && item.children.length" class="has-children"> -->
													<!-- 父菜单项，嵌套一个独立的 el-dropdown 实现子菜单 -->
													 <!-- <el-dropdown
														trigger="hover"
														placement="right-start"
														append-to="#app"
														popper-class="aibubbing_download_children_menu"
														:popper-options="{
														modifiers: [
															{
															name: 'offset',
															options: {
																offset: [126,-41]
															}
															}
														]
														}"
														@command="clickDownlaodChildButton"
													>
														<span>{{ item.name }}<img class="aibubbing_download_menu_arrow" src="@/assets/images/aiImages/aibubbing_download_menu_arrow.svg" alt=""></span>
														<template #dropdown>
														<el-dropdown-menu>
															<el-dropdown-item
															v-for="(child, cIndex) in item.children"
															:key="cIndex"
															:command="child"
															>
															{{ child.name }}
															</el-dropdown-item>
														</el-dropdown-menu>
														</template>
													</el-dropdown>
												</el-dropdown-item>
												<el-dropdown-item v-else @click.stop="clickDownlaodButton($event, item)">
													{{ item.name }}
												</el-dropdown-item>
												</template>
											</template>
											</el-dropdown-menu>
									</template>
						</el-dropdown> -->
						<!-- <el-tooltip class="box-item" effect="dark" content="合成将消耗字符数" placement="top-start"  append-to="#app">
							<div class="speaker_content_bottom_middle_item_left flex flex_a_i-center flex_j_c-center cursor-pointer"
								@click="syntheticAudioButton(1)" v-loading="loading">
								<span class="span1">合成音频</span>
							</div>
						</el-tooltip> -->
					</div>
				</div>
			</div>
			<!--    中间右边音色列表  -->
			<div class="width-full height-full main_content_bottom_item_right " v-if="!show_fold_list||show_ai_match">
				<div
					class="main_content_bottom_item_right_top_search flex flex_a_i-center flex_j_c-space-between margin_b-16">
					<div class="main_content_bottom_item_right_top_search_left font-size-13">
						<!-- <img src="@/assets/images/aiImages/expand_list.svg" @click="expand_list_click" class="expand_list cursor-pointer  margin_r-4"  alt=""> -->
						<span class="main_content_bottom_item_right_top_search_left_item margin_r-32 cursor-pointer" :class="show_list_nav_current==0?'current':''" @click="change_list_nav(0)">全部音色</span>
						<span class="main_content_bottom_item_right_top_search_left_item margin_r-32 cursor-pointer" :class="show_list_nav_current==1?'current':''" @click="change_list_nav(1)"><img src="@/assets/images/aiImages/ai_Intelligent_matching.png " alt="" class="width-60 height-20 "></span>
						<span class="main_content_bottom_item_right_top_search_left_item cursor-pointer" :class="show_list_nav_current==2?'current':''" @click="change_list_nav(2)">我的音色</span>
					</div>
					<div
						class="main_content_bottom_item_right_top_search_right flex flex_a_i-center flex_j_c-space-between">
						<img src="@/assets/images/aiImages/aidubbing_list_search.svg" @click="search_speaker" alt="">
						<el-input
							v-model="input_search"
							placeholder="请输入想找的音色关键词"
							class="margin_r-10"
							@keyup.enter="search_speaker"
							@clear="handleClear"
							clearable>
						</el-input>
					</div>
					<!-- 跳转至声音克隆页面图标 -->
					 <img v-if="isShow" src="@/assets/img/soundCustom.png" alt="" class="soundCustom" @click="toClone" />
				</div>
			<template v-if="show_list_nav_current!=1">
				<!--   选择项   -->
				<div class="speaker_content margin_b-24">
					<template v-if="show_list_nav_current==0">
						<div class="speaker_content_list flex flex_j_c-flex-start ">
							<div class="speaker_content_list_item margin_r-13 10 margin_b-10 padding-5 font-size-12 text-align-cetner cursor-pointer"
								:class="{ 'is_active': selecteVoiceTypeNum == item.name }" v-for="item in voiceTypeArr"
								@click="selecteVoiceType(item)" v-show="item.visible">
								{{ item.name }}
							</div>
						</div>

						<!--      性别      -->
						<div class="speaker_content_list flex flex_j_c-flex-start ">
							<div class="speaker_content_list_item margin_r-13 10 margin_b-10 padding-5 font-size-12 text-align-cetner cursor-pointer"
								:class="{ 'is_active': selecteGenderNum == item.name }" v-for="item in gendersArr"
								@click="selecteGender(item)" v-show="item.visible">
								{{ item.name }}
							</div>
						</div>
						<!--     第二层数组     -->
						<div class="speaker_content_list flex flex_j_c-flex-start ">
							<div class="speaker_content_list_item margin_r-13 10 margin_b-10 padding-5 font-size-12 text-align-cetner cursor-pointer"
								:class="{ 'is_active': selecteSecondNum == item.name }" v-for="(item, index) in secondArr"
								@click="selecteSecond(item, index - 1)" v-show="item.visible">
								{{ item.name }}
							</div>
						</div>
						<!--  第三层数组   uniqueList     -->
						<div class="speaker_content_list flex flex_j_c-flex-start ">
							<div class="speaker_content_list_item margin_r-13 10 margin_b-10 padding-5 font-size-12 text-align-cetner cursor-pointer"
								:class="{ 'is_active': selecteUniqueNum == item.name }" v-for="item in thirdArrList"
								@click="selecteUnique(item)" v-show="item.visible">
								{{ item.name }}
							</div>
						</div>
					</template>
					<template v-else>
						<div class="speaker_content_list flex flex_j_c-flex-start ">
							<div class="speaker_content_list_item margin_r-13 10 margin_b-10 padding-5 font-size-12 text-align-cetner cursor-pointer"
								:class="{ 'is_active': selectetMycurrent == item.name }" v-for="item in myArrList"
								@click="selectetMyClick(item.name)">
								{{ item.name }}
							</div>
						</div>
					</template>
				</div>
				<!--  中间人物列表    -->
				<div class="speaker_content_middle" v-loading="filter_listLoading" style="height:70%;">
					<el-scrollbar @scroll="handleScroll" ref="scroll" always>
						<div class="speaker_content_middle_list flex flex_j_c-flex-start" v-if="soundList.length > 0" ref="listContainer">
							<template v-for="(item, index) in soundList" :key="item.id">
							<div class="speaker_content_middle_list_item  flex flex_a_i-center flex_j_c-center margin_r-13 margin_b-12 cursor-pointer width-115 height-151"
								 @click="selectSoundItem(item,index)"
								:ref="setRef(index)" :data-voice-name="item.voiceName"
								:style="item.voiceName == SoundItemId ? `background-image:linear-gradient(134deg, rgba(10, 175, 96, 1), rgba(255, 214, 0, 1))` : ''">
								<!-- 收藏 -->
								<div class="speaker_content_middle_list_item_collect" v-if="selectetMycurrent!='克隆音色'">
									<img :src="item.bookmark==1 ? collectImage : collectNoImage" alt="" @click.stop="collectClick(item)">
								 </div>
								<!--       精品和珍享图片         -->
								<div class="position_image overflow-hidden"  v-if="selectetMycurrent!='克隆音色'">
									<img :src="item.membershipGrade == 'SVIP' ? zhenxiang : jingpin"
										style="width: 55px;height: 18px" alt="">
								</div>
								<!--      心图片          -->
								<!--                <img src="@/assets/images/aiImages/collectIcon.png" alt="" class="collectIcon width-20 height-20">-->
								<div class="speaker_content_middle_list_item_insideDiv  width-113 height-149 flex flex_d-column flex_a_i-center "
									>
									<!--    头像  名称     -->
									<div class="speaker_content_middle_list_item_insideDiv_avatar margin_t-29">
										<el-avatar :size="65" :src="item.avatarUrl" />
										<template v-if="selectetMycurrent!='克隆音色'">
											<div class="speaker_content_middle_list_item_insideDiv_avatar_position_div  width-20 height-20 flex flex_a_i-center flex_j_c-center"
												v-show="!item.isPlay" @click="playAudio(item, index)">
												<Iconfont color="#fff" size="12px" name="bofang" />
											</div>

											<div class="speaker_content_middle_list_item_insideDiv_avatar_position_zanting  width-20 height-20 flex flex_a_i-center flex_j_c-center"
												v-show="item.isPlay" @click="playAudio(item, index)">
												<Iconfont color="#fff" size="12px" name="pause-fill" />
											</div>
										</template>

									</div>
									<div
										class="speaker_content_middle_list_item_insideDiv_nickName  font-size-14"
										v-html="getDisplayName(item)">
									</div>
									<div class="matching_degree margin_t-5"
										:style="item.voiceName == SoundItemId ? `color: #0AAF60;` : ''"
										v-html="getDisplayEmotionTag(item)">
									</div>
								</div>
							</div>
							<!-- 额外内容插入点 -->

							   <InsertEmotion
									v-if="extraContentIndex !== null && extraContentIndex === index&&!(show_list_nav_current==1||(show_list_nav_current==2&&selectetMycurrent=='克隆音色'))"
									@choose="choose_emotion"
									ref="insert_emotion_ref"
								/>
							<!-- <div
								v-if="extraContentIndex !== null && extraContentIndex === index"
								class="insert_emotion"
							>
								<span>这里是插入的额外内容</span>
								<button
								@click.stop="closeExtraContent"
								style="position: absolute; top: 8px; right: 8px; background: #f00; color: #fff; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer;"
								>
								关闭
								</button>
							</div> -->
							</template>
						</div>
						<el-empty description="暂无数据" v-else />
					</el-scrollbar>
				</div>
			</template>
			<!--ai智能匹配  -->
			<template v-else>
				<aiMatch ref="ai_match_ref" @choose_ai="choose_ai" @ai_match_close="ai_match_close"></aiMatch>
			</template>
				<!--   语速  语调   -->
				<!--        <div class="speaker_content_bottom">-->
				<!--          &lt;!&ndash;   语速 语调    &ndash;&gt;-->
				<!--          <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语速</span>-->
				<!--            <el-slider class="flex-item_f-8" v-model="speechValue" :min="0.5" :max="2.0" :step="0.1" :show-tooltip="false"/>-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ speechValue }}x</span>-->
				<!--          </div>-->
				<!--          <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语调</span>-->
				<!--            <el-slider class="flex-item_f-8" v-model="intonationValue" :min="-12" :max="12" :step="1" :show-tooltip="false"/>-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ intonationValue }}</span>-->
				<!--          </div>-->
				<!--          &lt;!&ndash;    中间 合成音频  快速试听    &ndash;&gt;-->
				<!--          <div class="speaker_content_bottom_middle height-100 flex flex_a_i-center flex_j_c-space-around padding-n-40">-->
				<!--            <div class="speaker_content_bottom_middle_item_left flex flex_a_i-center flex_j_c-center cursor-pointer" @click="syntheticAudioButton(1)" v-loading="loading">-->
				<!--              <img src="@/assets/images/aiImages/synthetic_audio.png" alt="" class="width-22 height-22">-->
				<!--              <span class="span1 margin_l-10" >合成音频</span>-->
				<!--            </div>-->
				<!--            <div class="speaker_content_bottom_middle_item_right flex flex_a_i-center flex_j_c-center cursor-pointer" @click="syntheticAudioButton(2)" v-loading="trial_listening">-->
				<!--              <img src="@/assets/images/aiImages/trial_listening.png" alt="" class="width-20 height-20">-->
				<!--              <span class="span2 margin_l-10 text-align-center" >快速试听</span>-->
				<!--            </div>-->
				<!--          </div>-->
				<!--          &lt;!&ndash;    底部音频    &ndash;&gt;-->
				<!--          <div class="speaker_content_bottom_bottom">-->
				<!--            <AudioPlayer ref="AudioPlayerRef" :audioUrl="audioUrl" :isPauseTtsAudio="isPauseTtsAudio" @stopPlay="stop_timbre_play"></AudioPlayer>-->
				<!--          </div>-->
				<!--        </div>-->
				<!--      </el-scrollbar>-->
				<!-- 折叠音色列表按钮	 -->
				<img src="@/assets/images/aiImages/fold_list_btn.svg" class="fold_list_btn cursor-pointer" @click="fold_list_btn_click" alt="">
			</div>
		</div>
	</div>
	<AlertDialog v-model:visible="showAlertDialog" type="warning" :title="alert_title" :message="alert_message"
		:confirm-button-text="alert_confirm_txt" :cancel-button-text="alert_cancel_txt" :show-cancel-button="showCancelButton"
		:custom-confirm-class="true" :custom-cancel-class="true" :show-fee-explanation="false"
		@confirm="handleOpenMember" @cancel="handleCloseLimitDialog" />
	<trialListeningDialog ref="trial_listening_dialog_ref"></trialListeningDialog>
	<!-- 右键试听菜单 -->
	<el-dropdown placement="top-start" 	v-if="menuVisible"   ref="menuRef"		:style="{ position: 'fixed', top: `${topPopover}px`, left: `${leftPopover}px` }"
	>
	<el-button class="right_listen" @click="right_listen"><img src="@/assets/images/aiImages/right_listen.svg" alt=""> 试听 </el-button>
    </el-dropdown>
	<div class="full_screen_loading" v-if="full_loading">
		<div class="full_screen_loading_bar_boxs">
			<div
			v-for="(n, index) in 6"
			:key="index"
			:class="['full_screen_loading_bar', { active: index === full_loading_active_index }]"
			></div>
		</div>
		<span>正在{{synthetic_button_type==2||synthetic_button_type==3?'试听':'合成'}}音频，请稍后...</span>
	</div>

</template>

<style scoped lang="scss">
.create_background {
	width: 20px;
	height: 20px;
	background-image: url("http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D");
}

//选中文字展示样式
.wenziDivStyle {
	border: none;
	background-color: #eff8f2;
	padding: 5px;
	display: inline-block;
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.cursor = "pointer";
	// tipBox.style.pointerEvents = "auto";
}

::v-deep(.polyphonic_item1) {
	//background-color: #006eff;
	display: inline-block;
	width: 100%;
	height: 30px;
	line-height: 30px;

	.iii {
		width: 100%;
		height: 50%;
	}
}

.polyphonic_item1:hover {
	background-color: rgba(129, 107, 107, 0.21);
}

//修改滚动条颜色
//::v-deep(.el-scrollbar__bar.is-vertical) {
//  background-color: rgba(0, 0, 0, 0.1); /* 修改背景颜色 */
//}
::v-deep(.el-scrollbar__bar.is-vertical div) {
	background-color: #0aaf60;
	/* 修改滚动条颜色 */
	opacity: 1;
}

#editor {
	user-select: text;
	/* 确保文本可被选择 */
}

::v-deep(.tts-tag) {
	background-color: #f00 !important;
}


::v-deep(.el-popper__arrow::before) {
	//width: 0;
	//height:0;
	//border: none;
}

::v-deep(.el-popper__arrow::before) {
	/* position: absolute; */
	width: 0px;
	height: 0px;
	//z-index: -1;
	content: "";
	//transform: rotate(45deg);
	//background: var(--el-text-color-primary);
	box-sizing: border-box;
}

::v-deep(.el-popper.is-light, .el-popper.is-light>.el-popper__arrow):before {
	//background: var(--el-bg-color-overlay);
	//border: 0px solid var(--el-border-color-light);
}

::v-deep(.el-slider__button) {
	height: 14px;
	width: 14px;
}

::v-deep(.el-slider__bar) {
	//background: #0aaf60;
	// background: linear-gradient(108deg, #0AAF60 0%, #FFD600 100%);
}

::v-deep(.el-slider__button) {
	border: 2px solid #0aaf60;
}

.main_content {
	background-color: #f7f7f9;
	overflow: hidden;
	position: relative;

	&_top {
		height: 70px;

		//background-color: #67c23a;
		&_item {
			width: 100px;
			height: 30px;
			text-align: center;
			line-height: 30px;
			border-radius: 4px;
			color: #121212;
			background-color: #fff;
			font-size: 12px;
		}
	}

	&_bottom {
		// height: calc(100vh - 100px);
		flex:1;
		&_item_left {
			background-color: #fff;
			flex: 1;
			min-width: 650px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;

			//左边div顶部
			&_top {
				// height: 70px;

				//background-color: #006eff;
				&_left {}

				&_right {


					&_left {
						&_right {
							div {
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;

								&:first-child {
									color: #121212;
								}

								&:last-child {
									font-size: 14px;
									line-height: 20px;
									color: #353D49;
								}
							}
						}
					}

					&_right {}
				}
			}

			//输入文案div样式
			&_bottom {
				flex: 1;
				background-color: #f7f7f9;
				border-radius: 8px;
				position: relative;
				outline: none;
				//overflow-x:hidden;
				overflow-y: hidden;
				position: relative;
				padding-bottom: 50px;
				.el-scrollbar{
					position: static;
					::v-deep(.el-scrollbar__view){
						height: 100%;
						overflow-y: auto;
					}
				}
				//多音字弹窗样式
				//弹窗样式
				//.popupStyle {
				//  :v-deep(.el-popper){
				//
				//    //top: 200px!important;
				//    //left: 200px!important;
				//  }
				//
				//}
				.position_volume {
					position: absolute;
					top: 0px;
					right: 160px;
					background-color: #fff;
					border-radius: 6px;

					&_span1 {}

					// 控制滑块的默认颜色
					::v-deep(.el-slider__bar) {
						background: #0aaf60;
					}

					::v-deep(.el-slider__button) {
						border: 2px solid #0aaf60;
					}

					&_span2 {}
				}

				//输入字数样式
				.main_content_bottom_untill {
					position: absolute;
					bottom: 17px;
					right: 12px;
					color: rgba(0,0,0,0.45);
					display: flex;
					align-items: center;
					font-size: 12px;
					line-height: 14px;
					height: 18px;
					.main_content_bottom_untill_item{
						position: relative;
						padding:0 12px;
						display: flex;
						align-items: center;
						&::after{
							content:'';
							position: absolute;
							width: 1px;
							height: 12px;
							background: #E4E9ED;
							right: 0;
							top: 50%;
							transform: translateY(-50%);
						}
						&.number-words{
							span{
								color: #368DFF;
							}
						}
						&.clear_text{
							font-size: 12px;
							line-height: 14px;
							color: #888889;
							cursor: pointer;
							img{
								width: 12px;
								height: 12px;
								margin-right: 4px;
							}
						}
						&:first-child{
							padding-left: 0;
						}
						&:last-child{
							padding-right: 0;
							&::after{
								background-color: transparent;
							}
						}
					}

				}

				//文案div样式
				.copywriting-div {
					width: 100%;
					//height: 100%;
					// min-height: 100px;
					//background-color: #dff;
					// overflow-y: auto;
					outline: none;
				}

				/* 输入框 */
				.dzm-textarea {
					background: none;
					outline: none;
					//padding: 10px 10px 30px;
					//border: 1px solid #eeeeee;
					//border-radius: 4px;
					word-wrap: break-word;
					word-break: break-all;
					-webkit-user-modify: read-write-plaintext-only;
					// padding-bottom: 50px;
					&::selection {
						background-color: #BAD3F8;
						color: #202328; /* 选中时文字颜色 */
					}
					::v-deep(b){
						// background-color: red;
						&.match{
							background-color: #C8FF94;
							&.current{
								background-color: #94FFF1;
							}
						}
					}



				}

				/* 输入框为空时显示 placeholder */
				.dzm-textarea:empty:before {
					content: attr(placeholder);
					color: #cdcdcd;
				}

				/* 输入框获取焦点时移除 placeholder */
				.dzm-textarea:focus:before {
					// content: none;
					line-height: 18px;
				}

				//ai 文案div
				.AICopywriting_div {

					//background-color: #67c23a;
					&_top {
						width: 100%;
						border-radius: 20px;
						border: 2px solid #0AAF60;

						div {
							border: 1px solid #0AAF60;
						}

						::v-deep(.el-input__wrapper) {
							background-color: #F7F7F7 !important;
							border-radius: 40px !important;
							box-shadow: none;
						}
					}

					&_bottom {

						//background-color: #006eff;
						&_left {

							//background-color: #67c23a;
							&_item {
								background: #FFFFFF;
								border-radius: 16px;
								font-weight: 500;
								font-size: 12px;
								color: #0AAF60;
							}
						}

						&_right {
							font-weight: 400;
							color: #7E838D;
						}
					}
				}

				.main_content_bottom_left_speed_speech {
					width: 520px;
					.gradient-slider{
						width: 520px;
					}
					.font-size-14 {
						color: #fff;
						position: absolute;
						right: 25px;
						top: 50%;
						transform: translateY(-50%);
					}
				}
				// AI悬浮工具栏
				.ai_suspended_toolbar {
					position: fixed;
					z-index: 1;
					left: 66px;
					height: 36px;
					display: flex;
					align-items: center;
					padding: 6px 12px;
					//background: #F4FAFF;
					/* 纯色背景 */
					// box-shadow: 0px 4px 11px rgba(0, 0, 0, 0.1);
					border-radius: 8px;
					overflow: visible;
					margin-top: 10px;
					img{
						width:94px;
						height: 34px;
						cursor: pointer;
					}
					// .ai_suspended_toolbar_item {
					// 	position: relative;
					// 	padding: 0 12px;
					// 	cursor: pointer;
					// 	display: flex;
					// 	align-items: center;
					// 	justify-content: center;

					// 	img {
					// 		width: 12px;
					// 		height: 12px;
					// 		margin-right: 7px;
					// 	}

					// 	span {
					// 		font-size: 14px;
					// 		line-height: 24px;
					// 		color: #16162E;
					// 	}

					// 	&::after {
					// 		position: absolute;
					// 		top: 50%;
					// 		right: 0;
					// 		content: "";
					// 		width: 1px;
					// 		height: 11px;
					// 		background: #D3D3D2;
					// 		transform: translateY(-50%);
					// 	}

					// 	&:first-child {
					// 		padding-left: 0;
					// 	}

					// 	&:last-child {
					// 		padding-right: 0;

					// 		&::after {
					// 			display: none;
					// 		}

					// 	}
					// }

					// &:before {
					// 	content: "";
					// 	position: absolute;
					// 	top: 0;
					// 	left: 0;
					// 	right: 0;
					// 	bottom: 0;
					// 	border-radius: 8px;
					// 	padding: 1px;
					// 	/* 边框宽度 */
					// 	background: linear-gradient(290.19deg, #7956FF 28.18%, #3E9EFD 78.95%);
					// 	/* 挖空中间区域，露出主元素背景 */
					// 	-webkit-mask:
					// 		linear-gradient(#fff 0 0) content-box,
					// 		linear-gradient(#fff 0 0);
					// 	-webkit-mask-composite: destination-out;
					// 	mask-composite: exclude;

					// 	pointer-events: none;
					// 	z-index: -1;
					// }

				}

				// AI文案工具栏悬浮
				.ai_copywriting_toolbar {
					position: fixed;
					z-index: 3;
					left: 66px;
					display: flex;
					flex-direction: column;
					width: 760px;
					margin-top: 10px;
					.ai_copywriting_toolbar_top {
						display: flex;
						align-items: center;
						padding-right: 10px;
						margin-bottom: 10px;

						.ai_copywriting_toolbar_top_list {
							display: flex;
							align-items: center;

							.ai_copywriting_toolbar_top_list_item {
								margin-right: 5px;
								box-sizing: border-box;
								display: flex;
								justify-content: center;
								align-items: center;
								padding: 3px 10px;
								height: 30px;
								background: #FFFFFF;
								border: 1px solid #E2E2E2;
								border-radius: 8px;
								font-size: 14px;
								line-height: 24px;
								color: #16162E;
								cursor: pointer;

								&:last-child {
									margin-right: 0;
								}

								&.active {
									overflow: visible;
    								position: relative;
									// background: rgba(138, 112, 240, 0.05);
									// box-shadow: 0 2px 10px rgba(138, 112, 240, 0.15);
									&::after{
										content: '';
										position: absolute;
										top: 0;
										left: 0;
										width: 100%;
										height: 100%;
										border-radius: 8px;
										padding: 1px;
										background: linear-gradient(290.19deg, #7956ff 28.18%, #3e9efd 78.95%);
										-webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
										-webkit-mask-composite: destination-out;
										mask-composite: exclude;
										pointer-events: none;
										z-index: 1;
									}
								}
							}
						}

						.ai_copywriting_toolbar_top_more {
							margin-left: auto;
							display: flex;
							align-items: center;
							cursor: pointer;

							span {
								font-size: 14px;
								line-height: 24px;
								color: #16162E;
								margin-right: 4px;
							}

							img {
								width: 16px;
								height: 16px;
							}
						}
					}

					.ai_copywriting_container {
						z-index: 3;
						width: 100%;
						min-height: 36px;
						display: flex;
						padding: 13px;
						padding-left: 20px;
						background: #fff;
						/* 纯色背景 */
						border-radius: 8px;
						overflow: visible;
						position: relative;

						.ai_copywriting_textarea_img {
							width: 22px;
							height: 22px;
							margin-right: 16px;
							align-self: flex-start;
						}

						.ai_copywriting_toolbar_input {
							flex: 1;
							position: relative;
							z-index: 2;
							font-size: 14px;
							font-family: inherit;
							line-height: 20px;
							box-sizing: border-box;
							/* border: 1px solid #dcdfe6; */
							// border-radius: 20px;
							background: #fff;
							outline: none;
							padding: 0;
							color: #353D49;
							caret-color: #000;
							resize: none;
							overflow-y: auto;

							// 兼容placeholder
							&:empty:before {
								content: attr(placeholder);
								color: #bbb;
							}

						}

						.ai_copywriting_toolbar_buttons {
							align-self: flex-end;

							.ai_copywriting_toolbar_group {
								display: flex;
								align-items: center;
								gap: 15px;

								.ai_copywriting_toolbar_btn {
									padding: 6px;
									width: 30px;
									height: 30px;
									border: none;

									:hover {
										background-color: #fff;
									}

									img {
										width: 30px;
										height: 30px;
									}
								}
							}
						}

						&:before {
							content: "";
							position: absolute;
							top: 0;
							left: 0;
							width: 100%;
							height: 100%;
							border-radius: 8px;
							padding: 1px;
							/* 边框宽度 */
							background: linear-gradient(290.19deg, #7956FF 28.18%, #3E9EFD 78.95%);
							/* 挖空中间区域，露出主元素背景 */
							-webkit-mask:
								linear-gradient(#fff 0 0) content-box,
								linear-gradient(#fff 0 0);
							-webkit-mask-composite: destination-out;
							mask-composite: exclude;

							pointer-events: none;
							z-index: -1;
						}
					}
				}

			}


			//  中间主体右边
			.speaker_content_bottom {
				//background-color: #f00;



				//合成音频和快速试听按钮
				.speaker_content_bottom_middle {

					&_item_left,
					&_item_right {
						//width: 100px;
						//height: 40px;
						border-radius: 8px;

						// .span1,
						// .span2 {
						// 	font-weight: 500;
						// 	font-size: 12px;
						// }

						.span1 {
							color: #fff;
						}

						.span2 {
							color: #0BAF60;
						}
					}

					&_item_left {
						background: #0BAF60;
					}

					&_item_right {
						border: 2px solid #0AAF60;
					}
				}

				//中间主体右边播放语音组件
				//.speaker_content_bottom_bottom{
				//
				//}
			}












		}



		//右边音色列表
		&_item_right {
			background-color: #fff;
			max-width: 600px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;

			&_top_search {
				&_left {
					font-weight: 500;
					color: #121212;
					position: relative;

					&::before {
						content: '';
						display: inline-block;
						width: 100%;
						height: 4px;
						position: absolute;
						left: 0;
						bottom: -4px;
						//z-index: -1;
						//margin: 0px;
						//border-radius: inherit; /*important*/
						background: linear-gradient(to right, #0AAF60, #FFD600);
					}
				}

				&_right {
					width: 260px;
					height: 32px;
					background: #F7F7F9;
					border-radius: 18px;

					::v-deep(.el-input__wrapper) {
						//align-items: center;
						background-color: #F7F7F7 !important;
						border-radius: 40px !important;
						// border: none;
						// outline: none !important;
						box-shadow: none;
					}
				}
			}

			//  选择项样式
			.speaker_content {

				//background-color: #006eff;
				//height:40px;
				&_list {
					flex-wrap: wrap;

					//background-color: #F7F7F9;
					//height:40px;
					&_item {
						//width: 28px;
						//height: 20px;
						//background-color: #888888;
						//font-weight: 400;
						//font-size: 12px;
						//color: #121212;
						//line-height: 20px;
						//text-align: center;
						//font-style: normal;
						flex-shrink: 0;
						//font-family: PingFangSC, PingFang SC;
						border-radius: 4px;
						color: #121212;
						font-weight: 500;
					}

					.is_active {
						//background: rgba(239, 179, 153, 0.46);
						border-radius: 4px;
						color: #FB6D30;
					}

				}
			}

			//  中间任务列表
			.speaker_content_middle {
				flex: 1;
				//background-color: #ddffff;
				//overflow-y: auto;
				overflow-y: hidden;
				&_list {
					flex-wrap: wrap;

					&_item {
						background-color: #F7F7F9;
						border-radius: 8px;
						position: relative;
						overflow: hidden;

						//珍享和精品图片div
						.position_image {
							position: absolute;
							top: 0;
							left: 0;
							line-height: 0;
						}

						//收藏图片图片
						.collectIcon {
							position: absolute;
							top: 6px;
							right: 14px;
						}

						&_insideDiv {
							border-radius: 8px;
							background-color: #F7F7F9;

							&_avatar {
								position: relative;
								line-height: 0;
								//background-color: #006eff;
								&_position_div {
									display: none;
									position: absolute;
									border-radius: 20px;
									right: 0;
									bottom: 0;
									background: rgba(18, 18, 18, 0.6);
								}

								&_position_zanting {
									position: absolute;
									border-radius: 20px;
									right: 0;
									bottom: 0;
									background: rgba(18, 18, 18, 0.6);
								}

								&:hover {

									//background-color: #006eff;
									.speaker_content_middle_list_item_insideDiv_avatar_position_div {
										display: flex;
										position: absolute;
										border-radius: 20px;
										right: 0;
										bottom: 0;
										background: rgba(18, 18, 18, 0.6);
									}
								}
							}

							&_nickName {
								font-size: 14px;
								line-height: 18px;
								color: #1B2337;
								margin-top:5px;
							}

							// .matching_degree {
							// 	//background: #fff;
							// 	border-radius: 6px;
							// 	padding: 3px 10px;
							// 	color: #0AAF60;
							// 	font-size: 12px;
							// }
						}
					}

				}
			}


		}
	}

	&.ai_dubbing {
		padding: 16px 24px 23px;
		box-sizing: border-box;
		flex:1;
		display:flex;
		.main_content_bottom_item_left {
			padding: 24px;
			box-sizing: border-box;
			min-width: 660px;
			padding-top: 19px;
			.choose_timbre {
				box-sizing: border-box;
				display: flex;
				align-items: center;
				padding:4px;
				padding-right: 10px;
				height: 42px;
				background: #FFFFFF;
				border: 1px solid #0AAF60;
				border-radius: 100px;
				box-sizing: border-box;
				.choose_timbre_img{
					width: 34px;
					height: 34px;
					margin-right: 4px;
					border-radius: 50%;
					overflow: hidden;
					background-color: #F2F2F2;
					img {
						width: 100%;
						height: 100%;
					}
					.choose_timbre_img_status{

					}
				}


				span {
					font-size: 14px;
					color: #353D49;
					min-width: 42px;

				}
			}

			.main_content_bottom_item_left_top {
				// height: 48px;
				margin-bottom: 19px;
				.main_content_bottom_item_left_top_left{
					// height: 48px;
					.main_content_bottom_item_left_top_left_finish{
						display: flex;
						align-items: center;
						margin-right: 24px;
						padding-right: 25px;
						position: relative;
						.main_content_bottom_item_left_top_left_finish_item{
							margin-right: 24px;
							cursor: pointer;
							padding: 2px 3px;
							.main_content_bottom_item_left_top_left_finish_item_content{
								display: flex;
								flex-direction: column;
								align-items: center;
								.main_content_bottom_item_left_top_left_finish_item_img{
									width: 20px;
									height: 20px;
									margin-bottom: 6px;
								}
								.main_content_bottom_item_left_top_left_finish_item_text{
									font-size: 14px;
									line-height: 22px;
									display: inline-block;
								}
							}
							
							&:last-child{
								margin-right: 0;
							}
							&:hover{
								background-color: #F7F7F9;
							}
						}
						&::after{
							content: '';
							position: absolute;
							right: 0;
							top:2px;
							width: 1px;
							height: 48px;
							background-color: rgba(0,0,0,0.1);
						}
					}
					.ai_Intelligent_matching{
						position: relative;
						width: 70px;
						height: 48px;
						margin-right: 21px;
						margin-top: -5px;
						.ai_Intelligent_matching_icon{
							position: absolute;
							right: -15px;}
					}
					.ai_Intelligent_matching{
						position: relative;
						width: 70px;
						height: 48px;
						margin-right: 21px;
						margin-top: -5px;
						.ai_Intelligent_matching_icon{
							position: absolute;
							right: -15px;
							top: -9px;
							z-index: 1;
						}
					}
					.main_content_bottom_item_left_top_left_tool{
						display: flex;
						align-items: center;
					}
					.main_content_bottom_item_left_top_left_item {
						display: flex;
						flex-direction: column;
						// align-items: center;
						justify-content: center;
						// margin-right: 13px;
						margin-right: 24px;
						padding: 2px 3px;
						// margin-bottom: 13px;
						position: relative;
						min-width: 2em;
						.main_content_bottom_item_left_top_left_item_img {
							width: 20px;
							height: 20px;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 6px;
							// margin-right: 6px;
							img {
								width: 20px;
								height: 20px;
							}

							.iconfont {
								margin-bottom: 0;
							}
						}

						span {
							font-size: 14px;
							color: #111419;
							line-height: 22px;
							text-align: center;
							display: inline-block;
							min-width: 2em;
							;
						}

						&:last-child {
							margin-right: 0;

							&::after {
								background-color: transparent;
							}

						}

						// &::after{
						// 	content: '';
						// 	position: absolute;
						// 	right: 0;
						// 	top: 50%;
						// 	transform: translateY(-50%);
						// 	width: 1px;
						// 	height: 12px;
						// 	background-color: #E3E3E4;
						// }
						&:hover{
							background-color: #F7F7F9;
							border-radius: 3px;
						}
					}
				}
					.main_content_bottom_item_left_top_right {
						right: -11px;
						top: 2px;
						margin-left: auto;
						display: flex;
						align-items: center;
						.bg_music_box{
							position: relative;
							background: #FFFFFF;
							/* 主色 */
							border: 1px solid #0AAF60;
							border-radius: 100px;
							padding: 9px 11px;
							box-sizing: border-box;
						}
						.fold_list{
							width: 43px;
							height: 42px;
						}
					}
				}
							// 音量语速音调
							.main_content_bottom_left {
						position: absolute;
						bottom: 15px;
						z-index: 3;
						left: 12px;
						flex-shrink: 0;
						//margin-right:40px;
						//background-color: #ddd;
						//background-color: #67c23a;
						//::v-deep(.gradient-slider-popover) {
						//
						//  .speaker_content_bottom_left_speed_speech{
						//    .gradient-slider {
						//      /* 必须覆盖的基准色 */
						//      --el-slider-main-bg-color: transparent;
						//      .el-slider__bar {
						//        background: linear-gradient(90deg, #ff6b6b, #ff8787);
						//        height: 4px;
						//      }
						//      .el-slider__button {
						//        border-color: #ff6b6b;
						//        background: linear-gradient(45deg, #ff6b6b, #ff8787);
						//      }
						//    }
						//  }
						//
						//
						//}


						//
						//:deep(.custom-popover){
						//  .speaker_content_bottom_left_speed_speech{
						//    .gradient-slider{
						//
						//    }
						//          ::v-deep(.el-slider__bar){
						//            //background: #0aaf60;
						//            background: linear-gradient( 108deg, #FFD600 0%, #0AAF60 100%);
						//          }
						//          ::v-deep(.el-slider__button){
						//            border: 2px solid #0aaf60;
						//          }
						//  }
						//}


						//&_speed_speech{
						//  //font-weight: 400;
						//  //font-size: 14px;
						//  //color: #121212;
						//  background-color: #f00;
						//
						//  ::v-deep(.el-popover){
						//
						//
						//    .slider{
						//       控制滑块的默认颜色
						//      :：v-deep(.el-slider__bar){
						//        //background: #0aaf60;
						//        background: linear-gradient( 108deg, #FFD600 0%, #0AAF60 100%);
						//      }
						//      ::v-deep(.el-slider__button){
						//        border: 2px solid #0aaf60;
						//      }
						//    }
						//
						//
						//  }
						//
						//}


						&_left_button {
							background: #F5F5F5;
							border-radius: 4px;

							span {
								&:first-child {
									color: #9d9d9d;
								}

								&:last-child {
									color: #565d66;
								}
							}
						}

						//.position_volume{
						//  position: absolute;
						//  top:20px;
						//  right: 20px;
						//  background-color: #fff;
						//  border-radius: 6px;
						//  &_span1{
						//
						//  }
						//  // 控制滑块的默认颜色
						//  ::v-deep(.el-slider__bar){
						//    background: #0aaf60;
						//  }
						//  ::v-deep(.el-slider__button){
						//    border: 2px solid #0aaf60;
						//  }
						//
						//  &_span2{}
						//}
					}
					.main_content_bottom_left_left_button {
						position: relative;
						border: 1px solid #D3D3D2;
						border-radius: 2px;
						&::after {
							content: '';
							position: absolute;
							right: -18px;
							top: 50%;
							background: rgba(0, 0, 0, 0.1);
							width: 1px;
							height: 15px;
							transform: translateY(-50%);
						}

						&:last-child {
							padding-left: 10px;
							padding-right: 10px;
							margin-right: 0;

							&::after {
								content: '';
								background-color: transparent;
							}
						}

						span {
							&:first-child {
								font-size: 14px;
								color: rgba(0, 0, 0, 0.45);
							}

							&:last-child {
								font-size: 14px;
								color: #353D49;
							}
						}
				}
		}

		.speaker_content_bottom {
			padding: 30px 0 25px;
			flex-wrap: wrap;
			.speaker_content_bottom_bottom {
				display: flex;
				align-items: center;
				background: #F7F7F9;
				border-radius: 100px;
				padding: 9px 18px 9px 8px;
				box-sizing: border-box;

				&>div {
					width: 100%;

					:deep(.audio_right) {
						padding-left: 0;
						padding-right: 0;
						height: fit-content;

						.audio_icon {
							width: 27px;
							height: 27px;
						}

						.el-slider {
							margin-left: 11px;
							margin-right: 27px;
							width: fit-content;
							.el-slider__runway {
								background: #E7E7E7;
								border-radius: 100px;

								.el-slider__button {
									width: 9px;
									height: 12px;
									box-sizing: border-box;
									background: #0AAF60;
									border: 1px solid #FFFFFF;
								}

								.el-slider__bar {
									background: #0AAF60;
								}
							}

						}

						.audio_time {
							font-size: 12px;
							line-height: 24px;
							color: #353D49;

						}
					}
				}
			}

			.speaker_content_bottom_middle_item_left,
			.speaker_content_bottom_middle_item_right {
				// padding: 8px 17px;
				// padding: 8px;
				border: 1px solid #D3D3D2;
				font-size: 14px;
				line-height: 24px;
				border-radius: 4px;
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 90px;
				height: 40px;
				color: #353D49;
			}
			.speaker_content_bottom_middle_item_right{
				.try_listen{
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					span{
						color: rgba(0, 0, 0, 0.45);
					}
					.try_listen_item{
						display: flex;
						align-items: center;
						justify-content: center;
						position: relative;
						flex: 1;
						height: 100%;
						img{
							width: 20px;
							height: 20px;
						}
						&::after{
							content: '';
							position: absolute;
							right: 0;
							top: 50%;
							transform: translateY(-50%);
							width: 1px;
							height: 10px;
							background-color: #EFEFF1;
							z-index: 1;
						}
						&:last-child{
							&::after{
								background-color: transparent;
							}
						}
					}
				}
			}
			.speaker_content_bottom_middle_item_left {
				background: #0AAF60;
				border: none;
			}

			.speaker_content_bottom_middle_item_download {
				margin-right: 12px;
				padding-left: 9px;
				padding-right: 13px;
				display: flex;

				.span2 {
					margin-left: 0;
					margin-right: 12px;
					font-size: 14px;
					line-height: 24px;
					color: #353D49;
				}
			}
		}

		.main_content_bottom_item_right {
			max-width: 688px;
			position: relative;
			display: flex;
			flex-direction: column;
			padding: 12px 24px;
			overflow-y: hidden;
			padding-bottom: 24px;
			.main_content_bottom_item_right_top_search {
				padding-top: 7px;
				border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
				.main_content_bottom_item_right_top_search_left {
					font-size: 14px;
					line-height: 22px;
					color: #000000;
					display: flex;
					align-items: center;
					.expand_list{
						width: 20px;
						height: 20px;
						margin-right: 1px;
					}
					.main_content_bottom_item_right_top_search_left_item{
						font-size: 14px;
						line-height: 51px;
						height: 51px;
						display: flex;
						align-items: center;
						color: rgba(0, 0, 0, 0.45);
						&.current{
							color: #353D49;
							border-bottom: 2px solid #0AAF60;
						}
						&:last-child{
							margin-right: 0;
						}
					}
					&::before {
						background-image: none;
					}

				}

				.main_content_bottom_item_right_top_search_right {
					width: 250px;
					height: 37px;
					border-radius: 6px;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					background: #F6F7F9;
					padding: 4px 11px;
					img{
						margin-right: 4px;
						width: 16px;
						height: 16px;
					}
					.el-input {
						height: 30px;
						::v-deep(.el-input__wrapper){
							padding: 0;
							.el-input__inner{
								font-size: 13px;
								display: flex;
								align-items: center;

								&::placeholder{
									color: #9DA3AC;
								}
							}
						}


					}

					.el-button {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 79px;
						height: 32px;
						/* 主色 */
						background: #0AAF60;
						border-radius: 100px;
						font-size: 14px;
						line-height: 22px;
						color: #FFFFFF;
						/* input-field */



					}
				}
				.soundCustom{
					position: absolute;
					right: 24px;
					top: 80px;
					cursor: pointer;
				}
			}

			.speaker_content_list_item {
				font-size: 14px;
				line-height: 22px;
				color: #353D49;
				padding: 0;
				// &:first-child{
				// 	margin-right: 25px;
				// }
				&.is_active {
					color: #0AAF60;
				}

			}
			.speaker_content_middle_list_item{
				// &:nth-child(5n){
				// 	margin-right: 0;
				// }
				.speaker_content_middle_list_item_collect{
					position: absolute;
					top: 9px;
					right: 9px;
					width: 16px;
					height: 14px;
					z-index: 1;
				}
			}
			.matching_degree {
				padding: 0 6px;
				height: 18px;
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 12px;
				line-height: 18px;
				display: flex;
				color: rgba(0, 0, 0, 0.45);
				text-align: center;
			}
			.fold_list_btn{
				position: absolute;
				top: 50%;
				left: -18px;
				transform: translateY(-50%);
				z-index: 1;
				width: 24px;
				height: 62px;
			}
		}
	}
}
.full_screen_loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8); /* 半透明遮罩 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  .full_screen_loading_bar_boxs {
	display: flex;
	align-items: center;
	gap: 7px;
	height:41px;
	margin-bottom: 58px;
    .full_screen_loading_bar {
		width: 6px;
		height: 25px;
		background-color: #80F1AD; /* 默认浅绿色 */
		border-radius: 3px;
		animation-name: waveColor;
		animation-duration: 1.8s;
		animation-iteration-count: infinite;
		animation-timing-function: ease-in-out;
		transform-origin: center center;
		&:nth-child(1) {
			animation-delay: 0s;
		}
		&:nth-child(2) {
			animation-delay: 0.3s;
		}
		&:nth-child(3) {
			animation-delay: 0.6s;
		}
		&:nth-child(4) {
			animation-delay: 0.9s;
		}
		&:nth-child(5) {
			animation-delay: 1.2s;
		}
		&:nth-child(6) {
			animation-delay: 1.5s;
		}
		// &.active {
		// 	background-color: #0AAF60; /* 深绿色 */
		// 	opacity: 1;
		// }
	}
 }
	span{
		font-weight: 500;
		font-size: 15px;
		line-height: 21px;
		letter-spacing: -0.02em;
		color: #FFFFFF;
	}

}
/* 动画关键帧 */
@keyframes waveColor {
  0%, 100% {
    transform: scaleY(1);
    background-color: #80F1AD; /* 浅绿色 */
    opacity: 1;
  }
  50% {
    transform: scaleY(1.8);
    background-color: #0AAF60; /* 深绿色 */
    opacity: 1;
  }
}
</style>
<style lang="scss">
.aibubbing_download_menu {
	margin-bottom: -8px;
	box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	border: 1px solid #D3D3D2;
	/* dropdown-menu */
	box-sizing: border-box;
    height: auto;
    z-index: 9999 !important;
	.el-popper__arrow {
		display: none;
	}

	.el-dropdown-menu {
		padding: 4px 0;
		background-color: #fff;

		.el-dropdown-menu__item {
			display: flex;
			align-items: center;
			padding: 5px 8px;
			color: #1D2129;
			width: 83px;
			box-sizing: border-box;
			margin-bottom: 4px;
			span{
				font-size: 14px;
				line-height: 18px;
			}
			.el-dropdown{
				width: 100%;
				height: 100%;
				span{
					display: block;
					width: 100%;
					line-height: 22px;
					padding: 7px 0;
					display: flex;
					align-items: center;
					.aibubbing_download_menu_arrow{
						margin-left: auto;
						width: 12px;
						height: 12px;
					}
				}
			}
			&:hover {
				background: #F2F3F5;
				span{
					color: #0AAF60;
				}
			}
			&:last-child{
				margin-bottom: 0;
			}
		}
	}
}

.aibubbing_gradient_slider_popover {
	&.is-light {
		padding: 8px 9px 8px 16px;
		background: rgba(29, 33, 41, 0.85);
		border-radius: 2px;

		.el-slider__runway {
			background: rgba(255, 255, 255, 0.65);
			height: 2px;

			.el-slider__bar{
				height: 2px;
				background-color: #fff;
			}
		}

		.el-slider__button-wrapper {
			top: 50%;
			height: 100%;
		}

		.el-slider__stop {
			background: #EFF0F3;
			box-sizing: border-box;
			width: 4px;
			height: 4px;
			top: 50%;
			transform: translateY(-50%);
		}

		.el-slider__button {
			width: 12px;
			height: 12px;
			box-sizing: border-box;
			top: 50%;
			border: none;
		}



		.el-slider__marks {
			display: none;
		}
		.el-slider__input {
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			padding: 0px;
			height: 22px;
			border: 1px solid #4A4A4A;
			border-radius: 4px;
			flex: none;
			.el-input-number__decrease{
				left:7px;
				background-image: url('@/assets/images/aiImages/slider_reduce.svg');
				background-size: 8px 1px;
			}
			.el-input-number__increase{
				right: 7px;
				background-image: url('@/assets/images/aiImages/slider_add.svg');
				background-size: 8px 8px;
			}
			.el-input-number__decrease,.el-input-number__increase{
				width: 8px;
				height: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: transparent;
				color: #Fff;
				top: 50%;
				transform: translateY(-50%);
				border: none;
				font-size: 0;
				background-position: center center;
				background-repeat: no-repeat;
			}
			.el-input{
				.el-input__wrapper{
					background-color: transparent;
					padding: 0 18px;
					border: none;
					box-shadow: none;
					position: relative;
					.el-input__inner{
						color: #fff;
						padding-right: 13px; /* 给单位留空间 */
					}
					&::after {
						content: attr(data-unit);
						position: absolute;
						right: 0;
						top: 0;
						width: 30px;
						height: 30px;
						z-index: 1000;
						// transform: translateY(-50%);
						color: #fff;
						// pointer-events: none;
						// user-select: none;
						font-size: 14px;
					}
				}
			}
		}
		.el-popper__arrow {
			&::before {
				background-color: transparent;
				border: none;
				background-image: url('@/assets/images/aiImages/gradient_arrow.png');
				background-position: 0 0;
				background-repeat: no-repeat;
				background-size: 11px 5px;
				transform: rotate(360deg);
				top: 5px;
			}
		}
		&.intonation_popover{
			.el-slider__input{
				width: 80px;
			}
		}
		&.speech_popover{
			.el-slider__input{
				width: 80px;
			}
		}
		&.volume_popover{
			.el-slider__input{
				width: 78px;
			}
		}
	}



}
.right_listen{
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	width: 72px;
	height: 30px;
	background: #353D49;
	border: 1px solid #29313D;
	box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.13);
	border-radius: 5px;
	font-size: 14px;
	color:#fff;
	img{
		width: 13px;
		height: 13px;
		right: 3px;
	}
	&:hover{
		background: #353D49;
		color:#fff;
	}

}
.aibubbing_download_children_menu{
	height: auto;
	z-index: 9999 !important;
	.el-popper__arrow{
		display: none;
	}
	.el-scrollbar{
		.el-scrollbar__wrap{
			.el-scrollbar__view{
				.el-dropdown-menu{
					.el-dropdown-menu__item{
						display: flex;
						align-items: center;
						padding: 7px 8px;
						width: 112px;
						height: 28px;
						font-size: 14px;
						color: #1D2129;
						&:hover{
							background-color: #F2F3F5;
							color: #0AAF60;
						}
					}
				}
			}
		}
	}

}
.el-popper{
	&.is-light{
		&.action_bar_popover{
			padding: 0;
			border-radius: 3px;
			box-shadow: 0px 4px 18px rgba(0, 0, 0, 0.04);
			.el-popper__arrow{
				display: none;
			}
		}
	}
}
.try_listen-tooltip{
	// margin-left: 24px;
}
</style>
