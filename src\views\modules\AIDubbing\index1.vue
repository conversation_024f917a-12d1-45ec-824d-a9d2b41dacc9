<script setup>

//合成语音接口
import { synthesized_speechApi, Sound_ListApi, Sound_tabs_listApi, filter_sound_listApi, batchCreateAPI, queryTextAPI } from '@/api_my/AlDubb'
import { chekSensitive_Api } from '@/api_my/commercialDubb'
import jingpin from '@/assets/images/aiImages/jingpin.svg';
import musicIcon from '@/assets/images/aiImages/music.png';
import zhenxiang from '@/assets/images/aiImages/zhenxiang.svg';
import { useAIDubbingStore } from '@/stores/modules/AIDubbing.js'
import soundEffects from '@/views/constant/musicModal/soundEffects.vue'
import GlobalimportLetter from "@/views/constant/importLetters/importLetter.vue";

import { nanoid } from 'nanoid';
import { useRoute } from 'vue-router';
// import { parseString } from 'htmlparser2';
import { DomHand<PERSON> } from 'domhandler'
import * as htmlparser2 from "htmlparser2";

import { Parser } from 'htmlparser2';


import { pinyin, html, polyphonic } from 'pinyin-pro';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { getPinyinWithCharacters } from '@/common/utils/pinyinUtil.js';
import { getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref, watch, watchEffect, computed } from 'vue'
import AudioPlayer from "./components/audioPlayer.vue";
import Index from "@/views/modules/soundStore/index.vue";
import eventBus from "@/common/utils/eventBus.js";
import { useSoundStore } from '@/stores/modules/soundStore.js'
// import {crawlTextByMediaFile} from "@/api_my/AlDubb/index.js";
const { proxy } = getCurrentInstance();
const route = useRoute();
const useAIDubbing = useAIDubbingStore()
const soundStore = useSoundStore()
let traceId = ref('')
// 下载按钮
const downlaodButtonArr = reactive([
	{ name: '下载音频', url: 'xiazai' },
	// {name:'下载视频',url:'shipin1'},
	{ name: '下载字幕', url: 'zimu' },
])

// 操作多音字等按钮
const iconsArr = reactive([
	{ IconName: 'daoru', name: '导入文案' },
	{ IconName: 'duoyinzi', name: '多音字' },
	{ IconName: 'bieming', name: '别名' },
	{ IconName: 'charutingdun', name: '停顿' },
	{ IconName: 'shuzifuhao', name: '数字符号' },
	{ IconName: 'beijingyinle', name: '背景音乐' },
	{ IconName: 'yinxiao', name: '音效' },
	{ IconName: 'pinyin_line', name: '查看拼音' },
	{ IconName: 'scissors-2-line', name: '去剪辑' },
])
// 点击操作多音字等按钮
// const IconNum = ref(0)
const clickIcons = (e, index) => {
	// const target = event.target.closest('[data-id]');
	// console.log('==========',target)
	// if(!target){
	//   if(target.dataset.id!=='parent-1'){
	//     var div = document.querySelector('div[contenteditable="true"]');
	//     // console.log('77',div)
	//     if (div) { // 检查div是否存在
	//       var children = div.children;
	//       // console.log('ppp',children)
	//       for (var i = 0; i < children.length; i++) {
	//         children[i].classList.remove('tts-tag');
	//       }
	//     }
	//
	//   }

	// }
	showPopover.value = false
	switch (index) {
		case 0:
			// proxy.$letterModal.openImportLetter()
			Letter.value.importDialogVisible = true
			break;
		case 1:
			// IconNum.value = index
			//多音字操作  点击多音字弹出选择多音字弹窗
			//点击多音字  根据多因去查询这个字的多有多音字 polyphonicList
			gethomograph()
			break;
		case 2:
			// 点击别名
			isSelectedAlias()
			break;
		case 3:
			// 停顿弹窗
			stopPopover.value = true
			e.stopPropagation();
			break;
		case 4:
			// 先判断是否是纯数字
			isNanFun()
			break;
		case 5:
			proxy.$musicmodal.open()
			break;
		case 6:
			// proxy.$effectsmodal.open()
			// console.log(effects.value.effectsDialogVisible)
			effects.value.effectsDialogVisible = true
			break;
		case 7:
			// 查看拼音
			pinyinFun()
			break;
		case 8:
			if (!localStorage.getItem('user')) {
				proxy.$modal.open()
				return
			}
			if (route.query.projectId) {
				// 跳转到剪辑的页面
				window.open(`https://yunjian.peiyinbangshou.com/App?projectId=${Number(route.query.projectId)}&token=${JSON.parse(localStorage.getItem('user'))?.token}`)
			} else {
				saveTextHtml(1)
			}
			// console.log(JSON.parse(localStorage.getItem('user'))?.token)
			// return
			break;
	}
}
// div编辑区域调节音量大小
const slideValue = ref(70)
// 中间主体左边文案搜索
const search_copywritingType = ref('')

// 中间主体右边银色搜索
const input_search = ref('')
const search_speaker = () => {
	filter_listFun()
}


// 中间主体右边音色列表滚动事件
const scroll = ref(null);
const handleScroll = (e) => {
	const wrapRef = scroll.value.wrapRef;
	// console.log('wrapRef.scrollHeight',wrapRef.scrollHeight)   //内容总高度560
	// console.log('wrapRef.clientHeight',wrapRef.clientHeight)    //可视区域高度300
	// console.log('event.scrollTop',e.scrollTop)    //// 滚动条距盒子顶部高度260
	let poor = wrapRef.scrollHeight - wrapRef.clientHeight;
	// 判断滚动到底部
	if (e.scrollTop + 20 >= poor) {
		console.log('ppppppp到底了')
	}
}

// 中间主体右边语速滑块
const speechValue = ref(1)
// 语调
const intonationValue = ref(0)

// 是否播放音乐
const isPauseTtsAudio = ref(false)

// 点击文案类型中更多按钮
const clickMoreIcon = () => {
	proxy.$AImodal.open()
}
// 输入
const MAX_LENGTH = 5000
const textLength = ref(0);
const editorRef = ref(null)
const isComposing = ref(false)



let timeoutId = null; // 保存定时器ID
// 输入的文本内容
const editorContent = ref('')
// // 输入事件处理
const handleInput = (event) => {
	const content = ref('');
	const text = event.target.innerText || event.target.textContent;
	// 监听用户输入
	clearTimeout(timeoutId); // 清除之前的定时器
	timeoutId = setTimeout(() => {
		// 获取输入内容
		const content = editorRef.value
		// console.log('用户停止输入:',selectedTextList);
		if (localStorage.getItem('user')) {
			saveTextHtml()
		}
		// 在此处执行后续操作，如提交数据或更新状态
	}, 1500); // 800毫秒延迟
	if (text.length >= MAX_LENGTH) {
		// 截断内容到最大长度
		event.target.innerText = text.substring(0, MAX_LENGTH);
		// // 设置光标到内容末尾
		// const range = document.createRange();
		// const sel = window.getSelection();
		// range.setStart(event.target.firstChild, MAX_LENGTH);
		// range.collapse(true);
		// sel.removeAllRanges();
		// sel.addRange(range);
	} else {
		// 更新内容（虽然这里内容没有变化，但为了确保响应式，可以保留此行代码）
		// content.value = text;
	}
	editorContent.value = event.target.textContent
	// 设置底部文本长度
	textLength.value = event.target.textContent.length
}


// 输入时，保存文本html结构 ,以及多音字数组
const saveTextHtml = (param) => {
	// console.log('JSON.parse(localStorage.getItem(\'user\'))',JSON.parse(localStorage.getItem('user')))

	if (editorRef.value.innerHTML && !route.query.albumId) {
		// 仅在点击"去剪辑"按钮时调用API（param=1时）
		if (param === 1) {
			// 创建全屏loading
			const loadingInstance = ElLoading.service({
				lock: true,
				text: '正在准备跳转到剪辑页面...',
				background: 'rgba(0, 0, 0, 0.7)'
			})
			
			// 点击去剪辑时调用API
			batchCreateAPI([{
				title: editorRef.value.textContent.slice(0, 10),
				copywriting: editorRef.value.innerHTML,
				type: "1",
				albumId: 0,
				userId: JSON.parse(localStorage.getItem('user'))?.userId || '', //用户id
				ossAudioKey: audioUrl.value,  //音频文件
				subTitleFile: captions_url.value,  //字幕文件
				status: '1',
				heteronym: selectedTextList, //多音字列表
				traceId: traceId.value
			}]).then(res => {
				// 关闭loading
				loadingInstance.close()
				
				console.log(res)
				if (param == 1) {
					let { code, data } = res
					if (data?.projectId) {
						window.open(`https://yunjian.peiyinbangshou.com/App?projectId=${data.projectId}&token=${JSON.parse(localStorage.getItem('user'))?.token}`)
					}
				}
				if (res.code != 0) {
					ElMessage({
						message: '保存失败',
						type: 'warning',
					})
				}
			}).catch(err => {
				// 发生错误时也要关闭loading
				loadingInstance.close()
				
				console.log(err)
				ElMessage({
					message: '操作失败，请稍后重试',
					type: 'error'
				})
			})
		} else {
			// 普通输入时，只记录日志但不调用API
			console.log('输入更新，跳过batchCreateAPI调用');
		}
	}
}
// 禁止方向键、删除键和退格键（仅作为示例，可能需要根据实际需求调整）
// const preventDefault = (event) => {
//   event.preventDefault();
// }
onUnmounted(() => { // 清理监听
	// eventBus.off('update-data');
	clearTimeout(timeoutId);
	window.removeEventListener('click', handleClickOutside);
});


// 查看拼音按钮
const pinyinBool = ref(false)
const pinyinResult = reactive([])
const pinyinFun = () => {
	// const aa = pinyin(editorContent.value,{ type: 'array' })
	pinyinBool.value = !pinyinBool.value
	if (pinyinBool) {
		const result = getPinyinWithCharacters(editorContent.value);
		pinyinResult.splice(0, pinyinResult.length, ...result);
	}
}
// 鼠标按下事件
const handleKeyUp = (e) => {
	if (e.keyCode == 37 || e.keyCode == 38 || e.keyCode == 39 || e.keyCode == 40) {
		// mouseup()
	}
}
//  多音字弹窗
const showPopover = ref(false)
// 多音字列表
const polyphonicList = ref([])
// 点击多音字时调用的函数
const gethomograph = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll',range.commonAncestorContainer.nextElementSibling)
	// if(range.commonAncestorContainer.nextElementSibling){
	//   showPopover.value = true
	//   return
	// }
	if (range.toString().length !== 1) {
		ElMessage({
			message: '请滑选单个汉字',
			type: 'warning',
		})
		return
	}
	// const resultString = polyphonic(range.toString(),{ type: 'array' });
	const resultString = polyphonic(range.toString(), { toneType: 'num', type: 'array' });
	console.log('range.toString()', range.toString());
	console.log('resultString', resultString); // 输出
	if (resultString[0].length > 0) {
		polyphonicList.value = []
		polyphonicList.value = resultString[0]
		setTimeout(() => {
			showPopover.value = true
		})
	}
}
// 点击多音字中的某个读音，更新多音字读音在页面上
const clickPolyphonic = (item) => {
	restoreSelection()
	getSelectedText(item)
	showPopover.value = false
}




// 创建唯一标识数组  并保存起来  当点击别名还是数字符号的时候保存起来，后面替换内容会用到
const createUniqueArr = []
// 记录选中的别名以及多音字     以及相对应替换的文字，  以键值对形式
// 保存选择的多音字数组列表
let selectedTextList = reactive([])
// 判断选区后面是否有div的方法
function isDivAfterRange(range) {
	const { endContainer, endOffset } = range;
	let node = endContainer;
	let offset = endOffset;

	// 情况1：选区结束在文本节点中间
	if (node.nodeType === Node.TEXT_NODE) {
		if (offset < node.length) return false; // 文本节点内不可能包含div
		node = node.parentNode;
		offset = Array.from(node.childNodes).indexOf(endContainer) + 1;
	}

	// 情况2：处理元素节点
	let currentNode = node;
	let nextIndex = offset;

	// 创建遍历器
	const walker = document.createTreeWalker(
		document.body,
		NodeFilter.SHOW_ELEMENT,
		{
			acceptNode: n =>
				n.tagName === 'DIV' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
		}
	);

	// 定位到当前节点位置
	walker.currentNode = currentNode;

	// 检查后续兄弟节点
	if (currentNode.childNodes[nextIndex]) {
		const nextNode = currentNode.childNodes[nextIndex];
		if (nextNode.tagName === 'DIV') return true;
	}

	// 遍历后续节点
	while (walker.nextSibling()) {
		if (walker.currentNode.tagName === 'DIV') return true;
	}

	// 深度遍历父节点链
	// while (currentNode.parentNode) {
	//   currentNode = currentNode.parentNode;
	//   const children = Array.from(currentNode.childNodes);
	//   const index = children.indexOf(node) + 1;
	//
	//   if (children.slice(index).some(n => n.tagName === 'DIV')) {
	//     return true;
	//   }
	//   node = currentNode;
	// }

	return false;
}
// // 多音字操作
// editorRef  //  输入文本的ref
const getSelectedText = (item) => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	const hasTextAfter = isDivAfterRange(range);
	console.log('llllllll', hasTextAfter)
	if (hasTextAfter) {
		console.log(range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side'))
		range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = item
		let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
		selectedTextList.map(child => {
			if (child.id == dataId) {
				child.name = `${range.toString()}/(${item})`
			}
		})
		editorRefTriggerInput()
		return
	}
	// if(range.commonAncestorContainer.nextElementSibling){
	//   range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = item
	//   let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
	//   selectedTextList.map(child=>{
	//     if(child.id==dataId){
	//       child.name = `${range.toString()}/(${item})`
	//     }
	//   })
	//   return
	// }

	let id = nanoid(4)
	var tipBox = document.createElement("div");
	tipBox.innerHTML = `<span data-side='${id}'>${item}</span> <button style='font-size:10px; margin-left:5px;'>×</button>`;
	tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("data-attr", "duoyinzi");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	tipBox.style.cursor = "pointer";
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.pointerEvents = "auto";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	// {word:range.toString(),replacement:item}

	selectedTextList.push({
		id: id,
		name: `${range.toString()}/(${item})`
	})
	editorRefTriggerInput()
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();

		// console.log(e.target.parentElement.firstElementChild.getAttribute('data-side'))
		let click_id = e.target.parentElement.firstElementChild.getAttribute('data-side')
		selectedTextList.map((child, index) => {
			if (child.id == click_id) {
				selectedTextList.splice(index, 1)
			}
		})
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// range.deleteContents()
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


// 别名弹窗
const aliasPopover = ref(false)
const aliasValue = ref('')  //别名input
// 判断有没有选择文字
const isSelectedAlias = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll',range.toString().length)
	if (range.toString().length === 0) {
		ElMessage({
			message: '请至少选一个汉字',
			type: 'warning',
		})
		return
	}
	aliasPopover.value = true

}

// 点击弹窗中添加别名按钮
const addAlias = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		})
		return "";
	}
	var sel = window.getSelection();
	var selectedText = sel.toString().trim();
	if (!selectedText) {
		ElMessage({
			message: '请先选中内容',
			type: 'warning',
		})
		return "";
	}
	var tipBox = document.createElement("div");
	let id = nanoid(4)
	tipBox.innerHTML = "<span class='change'>" + aliasValue.value + "</span> <button style='font-size:14px; margin-left:5px;'>×</button>";
	tipBox.setAttribute("data-type", "alias");
	tipBox.setAttribute("data-key", selectedText);
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid purple";
	tipBox.style.backgroundColor = "#f0e0ff";
	tipBox.style.padding = "5px";
	tipBox.style.borderRadius = "6px";
	tipBox.style.display = "inline-block";
	tipBox.style.marginLeft = "5px";
	tipBox.style.cursor = "pointer";
	tipBox.style.pointerEvents = "auto";

	createUniqueArr.push(id)



	const span = document.createElement('span');
	span.className = id;
	span.id = id;
	range.surroundContents(span);
	// processedHTML.value = getReplacedContent('editor', 'data-shuzi', aliasValue.value);


	editorRefTriggerInput()
	// range.deleteContents();
	var closeBtn = tipBox.querySelector("button");
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		const prevElement = tipBox.previousSibling
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		// 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、

		// var key = tipBox.getAttribute("data-key");
		// if (window.aliasMapping && key in window.aliasMapping) {
		//   delete window.aliasMapping[key];
		// }
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "alias") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		alert("未检测到选中文本");
	}
	// if (!window.aliasMapping) {
	//   window.aliasMapping = {};
	// }

	aliasPopover.value = false
	aliasValue.value = ''
}



// 遍历指定div下所有节点、查找指定类名并替换文本
// function replaceDivText(containerId, targetClass, newText) {
//   // 获取容器div元素‌:ml-citation{ref="1,5" data="citationList"}
//   const container = document.getElementById(containerId);
//   if (!container) return null;
//
//   // 递归遍历所有子节点‌:ml-citation{ref="4,5" data="citationList"}
//   const traverseNodes = (node) => {
//     if (node.nodeType === Node.ELEMENT_NODE) {
//       // 检查当前节点是否包含目标类名‌:ml-citation{ref="1,4" data="citationList"}
//       if (node.classList.contains(targetClass)) {
//         node.textContent = newText; // 替换文本内容‌:ml-citation{ref="3,6" data="citationList"}
//       }
//       // 继续遍历子节点‌:ml-citation{ref="4,5" data="citationList"}
//       Array.from(node.children).forEach(child => traverseNodes(child));
//     }
//   };
//
//   traverseNodes(container);
//   return container.innerHTML; // 返回处理后的HTML内容‌:ml-citation{ref="1,6" data="citationList"}
// }


/**
 * 克隆容器DOM结构并执行安全替换
 * @param {string} containerId 容器元素ID
 * @param {string} targetClass 目标类名
 * @param {string} newText 新文本内容
 * @returns {string} 处理后的HTML字符串
 */

// 根据地址栏id查询需要回显的数据
const queryTextFun = async () => {
	const id = route.query.albumId
	if (id) {
		queryTextAPI({
			userId: JSON.parse(localStorage.getItem('user'))?.userId || '',
			id
		}).then(res => {
			const { code, data = {} } = res || {};
			console.log(data)
			if (code == 0) {
				editorRef.value.innerHTML = data.copywriting
				selectedTextList = data.heteronym || []
				editorRefTriggerInput()
			} else {
				ElMessage({
					message: res.msg,
					type: 'warning',
				})
			}
			// console.log(res)
		}).catch(err => {
			// console.log(err)
		})
	}
}


// 示例调用   获取数字替换以及别名替换之后的文本内容 （编辑的所有内容）
onMounted(() => {
	// editorRef.value.innerHTML = ddd.value
	queryTextFun()
	soundListFun()
	get_Sound_tabs_list()

	window.addEventListener('click', handleClickOutside);
})

// 音色标签列表
// 精品和珍享数据列表
const voiceTypeArr = ref([{ name: '全部', selected: false }])
// const gendersArr = ref([{name:'sex',children:[{name:'全部',selected:false}]}])
const gendersArr = ref([{ name: '全部', selected: false }])
// const secondArr = ref([{name:'scene',children:[{name:'全部',selected:false}]}])
const secondArr = ref([{ name: '全部', selected: false }])
// const thirdArr = ref([{name:'three',children:[{name:'全部',selected:false}]}])
const thirdArr = ref([{ name: '全部', selected: false }])
// 第三层去重后的数组
const uniqueList = ref()
// 第三层需要展示的数据
const thirdArrList = ref()



const selecteVoiceTypeNum = ref('全部')
const selecteVoiceType = (item) => {
	selecteVoiceTypeNum.value = item.name
	filter_listFun()
}


const selecteGenderNum = ref('全部')
const selecteGender = (item) => {
	selecteGenderNum.value = item.name
	filter_listFun()
}
const selecteSecondNum = ref('全部')
const selecteSecond = (item, index) => {
	selecteUniqueNum.value = '全部'
	selecteSecondNum.value = item.name
	if (index < 0) {
		thirdArrList.value = [...uniqueList.value]
	} else {
		thirdOriginArr.value[index].recommend_tags.map(item => {
			item.name = item.recommend_tags
		})
		thirdArrList.value = [{ name: '全部', selected: false }, ...thirdOriginArr.value[index].recommend_tags]
	}
	filter_listFun()



}
const selecteUniqueNum = ref('全部')
const selecteUnique = (item) => {
	selecteUniqueNum.value = item.name
	filter_listFun()
}

// 根据选中条件过滤音色列表
const filter_listLoading = ref(false)
const filter_listFun = () => {
	filter_listLoading.value = true
	let obj = {
		gender: selecteGenderNum.value == '全部' ? '' : selecteGenderNum.value,
		// id:00,
		voiceType: selecteVoiceTypeNum.value == '全部' ? '' : selecteVoiceTypeNum.value,
		sceneCategory: selecteSecondNum.value == '全部' ? '' : selecteSecondNum.value,
		recommendTags: selecteUniqueNum.value == '全部' ? '' : selecteUniqueNum.value,
		platformNickname: input_search.value,
		tts: '1'
	}

	if (selecteVoiceTypeNum.value == '全部') {
		delete obj.voiceType
	}

	if (input_search.value == '') {
		delete obj.platformNickname
	}
	if (selecteGenderNum.value == '全部') {
		delete obj.gender
	}
	if (selecteSecondNum.value == '全部') {
		delete obj.sceneCategory
	}
	if (selecteUniqueNum.value == '全部') {
		delete obj.recommendTags
	}
	filter_sound_listApi(obj).then((res) => {
		// console.log(res)
		if (res.code == 0) {

			if (res.data.length > 0) {
				res.data.map((item) => {
					item.isPlay = false
				})
			}


			soundList.value = res.data.sort(() => Math.random() - 0.5);
			// soundList.value.reverse()
			// console.log('soundList.value',soundList.value)

		} else {
			soundList.value = []
		}
		filter_listLoading.value = false
	}).catch(err => {
		console.log(err)
		soundList.value = []
		filter_listLoading.value = false
	})
}




const thirdOriginArr = ref([])
const get_Sound_tabs_list = () => {
	Sound_tabs_listApi({ tts: '1' }).then(res => {
		// console.log('456',res)
		if (res.code == 0) {
			// console.log('798798',res.data)
			let { data } = res
			let { genders, sceneMetadata, voiceType } = data
			// genders
			// 精品和珍享数组
			voiceType.map((item, index) => {
				voiceTypeArr.value.push({
					name: item.voiceType,
					selected: false,
				})
			})
			// 第一层数组
			genders.map((item, index) => {
				gendersArr.value.push({
					name: item.gender,
					selected: false,
				})
			})
			// 这里不需要传统商配标签,后端已处理
			// sceneMetadata.shift()
			thirdOriginArr.value = sceneMetadata
			// Object.assign(thirdOriginArr, JSON.parse(JSON.stringify(sceneMetadata)))
			thirdOriginArr.value.map(item => {
				item.recommend_tags.map(child => {
					child.selected = false
				})
			})
			// console.log('originArr',thirdOriginArr.value)
			// 第二层数组
			sceneMetadata.map(item => {
				item.recommend_tags.map(child => {
					// 第三层数组
					thirdArr.value.push({ name: child.recommend_tags, selected: false })
				})
				// 第二层数组
				secondArr.value.push({ name: item.scene_category, selected: false })
			})

			// console.log('ooo',thirdArr.value)
			uniqueList.value = thirdArr.value.filter(
				(item, index) => thirdArr.value.findIndex(i => i.name === item.name) === index
			);
			// console.log('uniqueList',uniqueList)
			thirdArrList.value = uniqueList.value


		} else {

		}
	})
}


// 查询音色列表函数
// 音色列表顶部筛列表


const categories = ref([])




// 点击列表中某一项
let SoundItemId = ref('')
const selectSoundItem = (item) => {
	SoundItemId.value = item.voiceName
	//恢复语速和语调默认值
	speechValue.value = 1
	//语调
	intonationValue.value = 0
}
let soundList = ref([])
const originalSoundList = ref([])
const soundListFun = () => {
	Sound_ListApi({ tts: '1' }).then(res => {
		// console.log('456',res)
		originalSoundList.value.filter(item =>
			item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
		)
		if (res.code == 0) {
			if (res.data.length > 0) {
				res.data.map((item) => {
					item.isPlay = false
				})
			}

			Object.assign(originalSoundList.value, JSON.parse(JSON.stringify(res.data)))
			Object.assign(soundList.value, JSON.parse(JSON.stringify(res.data)))
			// console.log('ppp',soundList)
		} else {
			soundList.value = []
			originalSoundList.value = []
		}
	}).catch(err => {
		console.log(err)
		soundList.value = []
		originalSoundList.value = []
	})

}


const processedHTML = ref(null)
// getReplacedContent('editor', 'data-shuzi', '新文本');
function getReplacedContent(containerId, targetClass, newText) {
	// 获取原始容器元素并克隆副本‌:ml-citation{ref="1,6" data="citationList"}
	const originalDiv = document.getElementById(containerId);
	if (!originalDiv) return null;
	const clonedDiv = originalDiv.cloneNode(true);

	// 递归遍历克隆节点‌:ml-citation{ref="4,8" data="citationList"}
	const traverseAndReplace = (node) => {
		if (node.nodeType === Node.ELEMENT_NODE) {
			// 类名匹配检测‌:ml-citation{ref="1,3" data="citationList"}
			if (node.id == targetClass) {
				// if (node.classList?.contains(targetClass)) {
				node.textContent = newText; // 安全文本替换‌:ml-citation{ref="6,7" data="citationList"}
			}
			// 深度遍历子元素‌:ml-citation{ref="4,8" data="citationList"}
			Array.from(node.children).forEach(child => traverseAndReplace(child));
		}
	};

	traverseAndReplace(clonedDiv);
	return clonedDiv.innerHTML; // 返回处理后的副本内容‌:ml-citation{ref="6,8" data="citationList"}
}





// 数字符号
const figurePopover = ref(false)
const figureList = reactive([
	{ title: '数值', num: '四五四五四' },
	{ title: '数值', num: '四十五' },
])

const isNanFun = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	if (!/^\d+$/.test(range.toString())) {
		ElMessage({
			message: '请选择纯数字文本',
			type: 'warning',
		})
		return
	} else {
		figureList[0].num = numberToPinyin(range.toString())
		figureList[1].num = numberToChinese(range.toString())
		figurePopover.value = true
	}
}

const clickFigure = (item) => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// 数字符号弹窗
	let id = nanoid(4)
	var tipBox = document.createElement("div");
	tipBox.innerHTML = "<span class='change'>" + item.num + "</span> <button style='font-size:14px; margin-left:5px;'>×</button>";
	tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("data-attr", "shuzifuhao");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid green";
	tipBox.style.backgroundColor = "#e0ffe0";
	tipBox.style.padding = "5px";
	tipBox.style.borderRadius = "6px";
	// tipBox.style.fontSize = "14px";
	tipBox.style.display = "inline-block";
	tipBox.style.marginLeft = "5px";
	tipBox.style.cursor = "pointer";
	tipBox.style.pointerEvents = "auto";
	createUniqueArr.push(id)
	const span = document.createElement('span');
	span.className = id;
	span.id = id;
	range.surroundContents(span);
	editorRefTriggerInput()
	// processedHTML.value = getReplacedContent('editor', 'data-shuzi', item.num);
	// console.log('processedHTML.value',processedHTML.value)
	// debugger
	var closeBtn = tipBox.querySelector("button");
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();

		// // 获取父节点及所有子节点‌:ml-citation{ref="8" data="citationList"}
		// const wrapper = document.createElement('span');
		// const parent = wrapperNode.parentNode;
		// const children = Array.from(wrapperNode.childNodes);
		//
		// // 在父节点中插入原始内容‌:ml-citation{ref="1,3" data="citationList"}
		// children.forEach(child => {
		//   parent.insertBefore(child, wrapperNode);
		// });
		//
		// // 移除包裹节点‌:ml-citation{ref="8" data="citationList"}
		// parent.removeChild(wrapperNode);


		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		const prevElement = tipBox.previousSibling
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
		tipBox.parentNode.removeChild(span)

	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	figurePopover.value = false
}

// 数字到中文字符映射
const numberMap = {
	'0': '零',
	'1': '一',
	'2': '二',
	'3': '三',
	'4': '四',
	'5': '五',
	'6': '六',
	'7': '七',
	'8': '八',
	'9': '九'
};
// 直接拼音转写
function numberToPinyin(num) {
	const numStr = String(num);
	let result = '';
	for (const char of numStr) {
		result += numberMap[char];
	}
	return result;
}
function numberToChinese(num) {
	const numberMap = {
		'0': '零',
		'1': '一',
		'2': '二',
		'3': '三',
		'4': '四',
		'5': '五',
		'6': '六',
		'7': '七',
		'8': '八',
		'9': '九'
	};

	const unitMap = {
		0: '',
		1: '十',
		2: '百',
		3: '千',
		4: '万',
		5: '十万',
		6: '百万',
		7: '千万',
		8: '亿'
	};

	let numStr = String(num);
	let result = '';
	let length = numStr.length;

	for (let i = 0; i < length; i++) {
		const currentDigit = numStr[i];
		const position = length - i - 1;

		if (currentDigit === '0') {
			// 当前位是零，且不在最高位
			if (i !== 0) {
				result += numberMap[currentDigit];
			}
			continue;
		}

		let part = '';

		part += numberMap[currentDigit];

		if (position > 0) {
			part += unitMap[position] || '';
		}

		result += part;
	}

	return result === '' ? '零' : result;
}









// 停顿弹窗
const stopPopover = ref(false)
const standstillList = reactive([
	'200', '400', '600', '800', '1000',
])
const clickStandstill = (item) => {
	// console.log(item)
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	var tipBox = document.createElement("div");
	tipBox.innerHTML = "<span>" + item + "m</span> <button data-type='number' style='font-size:10px;'>×</button>";
	// tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("data-attr", "tingdun");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.pointerEvents = "auto";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	editorRefTriggerInput()
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// 关闭弹窗
	stopPopover.value = false
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


// 弹窗位置
let topPopover = ref(0)
let leftPopover = ref(0)
const selection = window.getSelection();
const mouseup = (e) => {
	if (selection.rangeCount > 0) {
		// 动态改变多音字弹窗位置
		topPopover.value = e.pageY + 16
		leftPopover.value = e.pageX - 20
		// range.surroundContents(span);
		// document.getElementById('editor').appendChild(span)
		// range.insertNode(span); // 将带有样式的span重新插入到文档中。
		// // 重新设置选择范围以包含整个span。
		// range.setStartAfter(span); // 移动到span之后。
		// range.setEndAfter(span); // 同样移动到span之后。
		// selection.removeAllRanges(); // 清除之前的选中范围。
		// selection.addRange(range); // 设置新的选中范围。
	}
}

// 移除可编辑div下span中存在的文字并把文字移到文本节点中
// function clearHighlights() {
//   document.querySelectorAll('#editor span').forEach(span => {
//     span.outerHTML = span.innerHTML;
//   });
// }
// 从我的空间调过来的时候显示文字的删除操作
const feedBackEditorOperate = (event) => {
	// 别名
	let attr = event.target.parentNode.getAttribute('data-type')
	// 多音字
	let dataAttr = event.target.parentNode.getAttribute('data-attr')
	const prevElement = event.target.parentNode.previousSibling
	const parentNode = event.target.parentNode
	console.log('event', event.target.tagName, parentNode)
	// 用于元素回显时，点击叉号事件,删除别名元素
	if (event.target.tagName === 'BUTTON' && attr == 'alias') {
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		if (event.target.parentNode.previousSibling.innerHTML === '') {
			event.target.parentNode.previousSibling.remove()
		}
		event.target.parentNode.remove()
	}
	//删除多音字元素    回显时
	if (event.target.tagName === 'BUTTON' && dataAttr == 'duoyinzi') {
		let click_id = event.target.parentElement.firstElementChild.getAttribute('data-side')
		selectedTextList.map((child, index) => {
			if (child.id == click_id) {
				selectedTextList.splice(index, 1)
			}
		})
		event.target.parentNode.remove()
	}
	if (event.target.tagName === 'BUTTON' && (dataAttr == 'tingdun' || dataAttr == 'yinxiao')) {
		event.target.parentNode.remove()
	}
	// 数字符号时，删除
	if (event.target.tagName === 'BUTTON' && attr == 'shuzifuhao') {
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		if (event.target.parentNode.previousSibling.innerHTML === '') {
			event.target.parentNode.previousSibling.remove()
		}
		event.target.parentNode.remove()
	}



}





let selectionRange; // 存储选区范围以便恢复
const handleClick = (event) => {
	const selection = window.getSelection();
	selectionRange = selection.getRangeAt(0).cloneRange(); // 保存当前选区范围
	feedBackEditorOperate(event)
	// 你可以在这里添加其他逻辑，例如阻止默认行为等。
	event.preventDefault();
}
// 恢复选取
const restoreSelection = () => {
	if (selectionRange) {
		const selection = window.getSelection();
		selection.removeAllRanges(); // 清除现有选区
		selection.addRange(selectionRange); // 恢复选区范围
	}
}


























// 事件代理模式（父容器监听）
const handleContainerClick = (event) => {
	// stopPopover.value = false

	// console.log('8888888888888',event)

	// console.log('oooooo',event.target.closest('[data-id]'))
	const target = event.target.closest('[data-id]');
	// console.log('454545',target)
	// is_show_volume.value = false
	try {

		if (target) {

		} else {
			// 关闭停顿弹窗
			stopPopover.value = false
			showPopover.value = false
			figurePopover.value = false
			aliasPopover.value = false
		}

		// if(!target){
		//   if(target.dataset.id!=='parent-3'){
		//     stopPopover.value = false
		//   //   var div = document.querySelector('div[contenteditable="true"]');
		//   //   // console.log('77',div)
		//   //   if (div) { // 检查div是否存在
		//   //     var children = div.children;
		//   //     // console.log('ppp',children)
		//   //     for (var i = 0; i < children.length; i++) {
		//   //       children[i].classList.remove('tts-tag');
		//   //     }
		//   //   }
		//   }
		// }else{
		//   stopPopover.value = true
		// }

		// showPopover.value = false

	} catch (e) {
		// console.log('ppp',e)
		// var div = document.querySelector('div[contenteditable="true"]');
		// // console.log('77',div)
		// if (div) { // 检查div是否存在
		//   var children = div.children;
		//   // console.log('ppp',children)
		//   for (var i = 0; i < children.length; i++) {
		//     children[i].classList.remove('tts-tag');
		//   }
		//
		// }

		// showPopover.value = false
	}
};



// 点击顶部下载按钮
const clickDownlaodButton = (e, index) => {
	if (index == 0 && audioUrl.value == '') {
		ElMessage({
			message: '暂无音频文件',
			type: 'warning',
		})
		return
	}
	if (index == 1 && captions_url.value == '') {
		ElMessage({
			message: '暂无字幕文件',
			type: 'warning',
		})
		return
	}
	const link = document.createElement('a');
	if (index == 0) {
		link.href = audioUrl.value; // 文件 URL
		link.download = 'document.mp3'; // 默认文件名
	} if (index == 1) {
		link.href = captions_url.value; // 文件 URL
		link.download = 'document.txt'; // 默认文件名
	}
	link.style.display = 'none';
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}







// const htmlToText = (html) => {
//   let text = '';
//   const parser = new Parser({
//     ontext: (data) => { text += data; } // 仅捕获文本节点‌:ml-citation{ref="1" data="citationList"}
//   }, { decodeEntities: true });
//   parser.write(html);
//   parser.end();
//   return text.trim();
// };


// 点击音乐面板中的喇叭按钮
const is_show_volume = ref(false)
const click_volume = () => {
	is_show_volume.value = !is_show_volume.value
}
// 点击关闭音乐面板中的叉号按钮
const close_music_div = () => {
	is_show_volume.value = false
	useAIDubbing.bgmusic_url = ''
	useAIDubbing.bgmusicObj = {}

}







// 返回的音频文件
const audioUrl = ref('')
// 返回的字幕文件
const captions_url = ref('')
// function getCustomAttributeValues(divId, attributeName) {
//   const container = document.getElementById(divId);
//   if (!container) return [];
//   const selector = `[${attributeName}]`;
//   return Array.from(container.querySelectorAll(selector))
//       .map(element => element.getAttribute(attributeName))
//       .filter(value => value !== null);  // ‌:ml-citation{ref="2,5" data="citationList"}
// }


// function replaceHttpLinks(text, replacementFormat) {
//   // 使用正则表达式匹配HTTP或HTTPS地址
//   var urlPattern = /(https?:\/\/[^\s]+)/g;
//
//   // 使用replace方法替换匹配的链接
//   return text.replace(urlPattern, function(url) {
//     // 根据指定的格式生成替换后的字符串
//     return replacementFormat.replace('{url}', url);
//   });
// }

function extractAndTruncateChineseChars(text) {
	// 使用正则表达式匹配[sound:(.*?)]格式的内容，但此处我们不需要捕获这部分内容，因此使用非捕获组(?:...)
	// 同时，我们使用正则表达式匹配所有中文字符，并确保只保留前10个中文字符
	var chineseCharPattern = /([\u4e00-\u9fff])/g;
	var match = text.match(chineseCharPattern);

	if (match) {
		// 如果匹配到了中文字符，则取第一个匹配结果（即前10个中文字符）
		var truncatedChineseChars = match[0];

		// 使用替换方法将原始文本中的中文字符替换为截取后的前10个中文字符
		// 注意：这里假设我们只替换首次出现的连续中文字符序列，如果需要替换所有，则需要调整逻辑
		var resultText = text.replace(/[\u4e00-\u9fff]+/, truncatedChineseChars);

		// 由于我们可能只替换了部分中文字符，而原始文本中可能包含其他非中文字符和[sound:...]链接
		// 因此，我们需要确保返回的字符串仍然包含这些未修改的部分
		// 为了实现这一点，我们可以使用一个更复杂的正则表达式来精确匹配和替换
		// 但在这里，为了简化，我们假设上述替换已经足够（即原始文本中只有一个连续的中文字符序列需要替换）

		// 另外，需要注意的是，如果原始文本中的中文字符少于10个，则上述代码将直接返回这些字符（无需截断）

		return resultText;
	} else {
		// 如果没有匹配到中文字符，则直接返回原始文本（或者可以根据需要返回其他值或执行其他操作）
		return text;
	}
}




// 合成语音
// 加载动画
const loading = ref(false)
const trial_listening = ref(false)
let handle_selectedTextList = [] //多音字列表
let textInfo = ''  // 合成的文本内容
// const htmlContent = '<div id="container"><p>Hello, world!</p><p>Another paragraph.</p></div>';
const syntheticAudioButton = async (type) => {
	if (!localStorage.getItem('user')) {
		proxy.$modal.open()
		return
	}
	// 只要点击合成和试听按钮就全部停掉音色列表中播放
	stop_timbre_play()

	if (type == 1) {
		loading.value = true
	} else {
		trial_listening.value = true
	}
	// // 通过CSS属性选择器定位目标元素‌:ml-citation{ref="3,4" data="citationList"}
	const container = document.querySelector('#editor');  // 目标父级div
	// // 提取ID名称集合
	//   const elements_data_IdList = [];
	//   elements_data_Id.forEach(element => {
	//     console.log(element.dataset)
	//     // if (element['dataset']) elements_data_IdList.push(element['dataset'].name);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	//   });
	// 获取音频链接
	// const audio_list = []
	// const create_background_divs = editorRef.value.getElementsByClassName('create_background');
	// create_background_divs.forEach(element => {
	//   console.log(element)
	//   // if (element['dataset']) audio_list.push(element['dataset'].id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	// });
	// console.log(audio_list)
	// console.log('container',container)
	const elementsWithId = container.querySelectorAll('[id]');  // 获取所有带id属性的子元素‌:ml-citation{ref="3,4" data="citationList"}
	// console.log('elementsWithId',elementsWithId)
	// // 提取ID名称集合
	const idList = [];
	elementsWithId.forEach(element => {
		if (element.id) idList.push(element.id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	});
	// const targets = editorRef.value.getElementsByClassName('data-shuzi');
	const changContents = editorRef.value.getElementsByClassName('change');
	// console.log('targets',targets)
	// console.log('changContents',changContents)
	// const nanoidArray = []
	// for(let i=0;i<idList.length;i++){
	//   nanoidArray.push(nanoid(4))
	// }
	// console.log('nanoidArray',nanoidArray)
	let originalArr = []
	Array.from(elementsWithId).forEach((el, idx) => {
		originalArr.push({
			id: idList[idx],
			text: el.innerText,
		})
	});
	let alternativeArr = []
	Array.from(changContents).forEach((el, idx) => {
		alternativeArr.push({
			id: idList[idx],
			text: el.innerText,
		})
	});
	console.log('ppp', alternativeArr)
	// console.log('originalArr',originalArr)
	// console.log('alternativeArr',alternativeArr)
	const originalDiv = document.getElementById('editor');
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	if (alternativeArr.length > 0) {
		for (let j = 0; j < originalArr.length; j++) {
			processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
			if (parentDiv) {
				const elements = parentDiv.querySelectorAll('*');
				elements.forEach(element => {
					if (element.id === alternativeArr[j].id) {
						element.textContent = alternativeArr[j].text; // 替换文本
					}
				});
				// processedHTML.value = elements
				// const targetDivs = parentDiv.querySelectorAll("div[data-type]");
				// targetDivs.forEach(div => div.remove());
			}
		}
	}
	// for(let j=0;j<originalArr.length;j++){
	//   processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
	//   if (parentDiv) {
	//     const elements = parentDiv.querySelectorAll('*');
	//     elements.forEach(element => {
	//         if (element.id === alternativeArr[j].id) {
	//           element.textContent = alternativeArr[j].text; // 替换文本
	//         }
	//     });
	//     // processedHTML.value = elements
	//     // const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	//     // targetDivs.forEach(div => div.remove());
	//   }
	// }
	const elements = parentDiv.querySelectorAll("[data-type='number11']");
	console.log('elements', elements)
	const parentElement = document.getElementsByClassName('parent')
	// console.log(parentElement)
	const dataIdValues = [];
	Array.from(parentElement).forEach(parent => {
		const spans = parent.getElementsByTagName('span'); // 获取当前 parent 下的所有 span 元素
		Array.from(spans).forEach(span => {
			const dataId = span.getAttribute('data-id');
			// 如果 data-id 存在，则添加到数组中
			if (dataId) {
				dataIdValues.push(dataId);
			}
		});
		// const spans = parent.querySelectorAll('span');
		// // 遍历每个 span 元素
		// spans.forEach(span => {
		//   // 获取 span 元素的 data-id 属性值
		//   const dataId = span.getAttribute('data-id');
		//   // 如果 data-id 存在，则添加到数组中
		//   if (dataId) {
		//     dataIdValues.push(dataId);
		//   }
		// });
	});
	// 获取到的音效链接
	console.log('dataIdValues', dataIdValues)
	Array.from(elements).map((element, index) => {
		// console.log('element',element)
		var linkElement = document.createElement('a');
		// linkElement.href = dataIdValues[index]; // 设置链接地址
		linkElement.textContent = '[sound:' + dataIdValues[index] + ']'; // 复制 div 的内容到链接
		// linkElement.style.backgroundColor = 'lightgreen'; // 设置链接背景颜色
		// element.textContent = dataIdValues[index]; // 替换内容
		// 用链接替换 div
		element.replaceWith(linkElement);
	})
	// const targetDivs = parentDiv.querySelectorAll('div:not([data-type="number11"])');
	const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	targetDivs.forEach(div => div.remove());
	const targetDivs1 = parentDiv.querySelectorAll("button[data-type]");
	targetDivs1.forEach(div => div.remove());
	// console.log('opdsfi',parentDiv)
	// 需要传的字符串信息   注意  其中有数字就会转  需要处理

	textInfo = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
		// console.log(parseInt(val))
		return `<#${parseInt(val) / 1000}#>`
	})
	// console.log('jjjjj',textInfo)
	textInfo = textInfo.replace(/\n/g, '');
	// console.dir('8888888',textInfo)
	// return




	if (type == 2) {
		let parts = textInfo.split(/(\[sound:.*?\])+/);
		let finalText = parts.filter(part => part.trim() !== '')
		// console.log('finalText',finalText)
		let length = 0
		for (let i = 0; i < finalText.length; i++) {
			// console.log(finalText[i])
			if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
				if (finalText[i].length <= 500) {
					length += finalText[i].length
				} else {
					finalText[i] = finalText[i].substring(0, 500 - length);
					// debugger
				}
			}
		}
		// console.dir(finalText.join(''))
		// debugger
		textInfo = finalText.join('')
	}
	if (!textInfo) {
		ElMessage({
			message: '请先输入文本信息',
			type: 'warning',
		})
		loading.value = false
		trial_listening.value = false
		return
	}

	if (!SoundItemId.value) {
		ElMessage({
			message: '请先选择声音',
			type: 'warning',
		})
		loading.value = false
		trial_listening.value = false
		return
	}
	handle_selectedTextList = []
	selectedTextList.map(item => {
		handle_selectedTextList.push(item.name)
	})
	// 检查输入的文本是否包含敏感词
	const res = await chekSensitive_Api({
		txt: textInfo,
	})
	const {
		code,
		data // 不直接解构，先检查 data
	} = res || {}; // 如果 res 为 null，则使用空对象

	const result = data?.content?.result ?? null; // 使用可选链和空值合并运算符
	const status_code = data?.status_code;
	// console.log('result',res)
	if (result && result.length > 0) {
		ElMessageBox.confirm(
			`文本中包含${result.join('、')}敏感词，是否继续生成音频？`,
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				// type: 'warning',
				closeOnPressEscape: false,
				closeOnClickModal: false,
				showClose: false,
				beforeClose: (action, instance, done) => {
					if (action === "confirm") {
						instance.confirmButtonLoading = true;
						popUp_query_button(instance, done)
					} else {
						done()
					}
				}

			}
		).then(res => {
			// console.log('77777777777')
		}).catch(err => {
			loading.value = false
			trial_listening.value = false
		})
	} else {
		//直接合成音频
		popUp_query_button()
	}
}

// 检测敏感词后点击确定按钮继续生成样音
const popUp_query_button = (instance, done) => {
	synthesized_speechApi({
		user_id: JSON.parse(localStorage.getItem('user'))?.userId || '',
		text: textInfo,
		voice_id: SoundItemId.value,
		audio_format: "mp3",
		chunk_size: 2048,
		speed: speechValue.value, //语速
		vol: slideValue.value / 10, //音量
		pitch: intonationValue.value,  //语调
		pronunciation: handle_selectedTextList, //多音字列表
		bgm_url: useAIDubbing.bgmusic_url
	}).then((res) => {
		// console.log('77',res)
		let { data, code } = res
		// console.log('78979846',data,code)
		if (code == 0) {
			let { status_code, content } = data
			// console.log('llll',status_code, content,content.length)
			if (status_code == 200) {
				let { result } = content
				// console.log('oooo787898798',result)
				if (result) {
					// console.log('result',result)
					audioUrl.value = result.audio_file
					captions_url.value = result.subtitle_file
					traceId.value = result.trace_id
					// 保存到我的作品,登录之后才可以保存
					if (localStorage.getItem('user')) {
						saveTextHtml()
					}
					// console.log('audioUrl.value',audioUrl.value)
				} else {
					audioUrl.value = ''
					captions_url.value = ''
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})
				}
				loading.value = false
				trial_listening.value = false
				if (instance) {
					instance.confirmButtonLoading = false
					done()
				}
			} else {
				audioUrl.value = ''
				captions_url.value = ''
				loading.value = false
				trial_listening.value = false
				ElMessage({
					message: '音频转义失败，请重新上传',
					type: 'warning',
				})
			}
		} else {
			audioUrl.value = ''
			captions_url.value = ''
			loading.value = false
			trial_listening.value = false
			if (instance) {
				instance.confirmButtonLoading = false
				done()
			}
			ElMessage({
				message: '音频转义失败，请重新上传',
				type: 'warning',
			})
		}


	}).catch((err) => {
		console.log('11', err)
	})
}



// 当点击合成音频底部播放按钮时，暂停所有播放音色播放
const stop_timbre_play = () => {
	soundList.value.map((child, idx) => {
		soundList.value[idx].isPlay = false
	})
	audioRef.value.src = ''
	audioRef.value.pause()
}

// 点击播放音色列表中某一项，播放
const AudioPlayerRef = ref(null)
const audioRef = ref(null);
const playAudio = (item, index) => {
	audioRef.value.src = ''
	audioRef.value.pause()
	// 关闭页面底部合成音频播放
	AudioPlayerRef.value.handleCloseMusic()
	// isPauseTtsAudio.value = true
	// console.log('audioRef',audioRef.value)
	soundList.value.map((child, idx) => {
		if (item.id == child.id) {
			if (soundList.value[idx].isPlay) {
				soundList.value[idx].isPlay = false
				audioRef.value.pause()
			} else {
				soundList.value[idx].isPlay = true
				audioRef.value.src = item.audioUrl
				nextTick(() => {
					audioRef.value.play()

				})
			}
		} else {
			soundList.value[idx].isPlay = false
		}
	})
}
// 选择音效时监听接收的数据
// eventBus.on('update-data', (data) => {
//   console.log('接收数据4545:', data); // 输出：1
//   //插入音效到页面指定位置
//   click_effects_item(data)
//
// });

// watch(()=>useAIDubbing.sound_effects_url_obj,(newVal)=>{
//   console.log('787878',newVal)
// })
// 导入文案监听
const handleCallLetter = (data) => {
	// console.log('8643541635456',data)
	editorRef.value.innerHTML = data;
	// 触发输入框input事件
	editorRef.value.dispatchEvent(new InputEvent('input'));
}
// 主动触发输入框input事件
const editorRefTriggerInput = () => {
	editorRef.value.dispatchEvent(new InputEvent('input'));
}



const Letter = ref(null)
const effects = ref(null)
const handleCallParent = (data) => {
	// console.log('收到子组件事件:', data);
	click_effects_item(data)

};

const click_effects_item = (item) => {
	// console.log(item)
	let value_url = item.storagePath || item.ossPath
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	// var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	// const div = document.createElement('div');
	// div.style.width = '60px';
	// div.style.height = '30px';
	// div.style.padding = "0 5px";
	// div.style.marginLeft = "5px";
	// div.style.backgroundImage = 'url(http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D)';
	// div.style.backgroundSize = '20px 20px';
	// div.style.backgroundRepeat = "no-repeat";
	// // div.style.backgroundColor = "#eff8f2";
	// div.style.border = "1px solid #ddd";
	// div.style.backgroundColor = "#ddd";
	// // div.style.backgroundPosition = "center left";
	// div.style.backgroundPosition = "5px center";
	// div.style.borderRadius = "6px";
	// div.setAttribute("data-type", "number");
	// div.setAttribute("contentEditable", "false");
	// div.style.display = "inline-block";
	// div.style.position = "relative";
	// div.style.pointerEvents = "auto";
	// // div.innerHTML = "<button data-type='effect' style='font-size:10px;'>×</button>";
	// // var closeBtn = tipBox.querySelector("button");
	// // closeBtn.style.border = "none";
	// // closeBtn.style.padding = "4px";
	// // closeBtn.style.fontSize = "14px";
	// // closeBtn.style.cursor = "pointer";
	// // closeBtn.style.backgroundColor = "#fff";
	// // div.style.display = "flex";         // 启用弹性布局
	// // div.style.justifyContent = "flex-end";  // 右对齐
	// // div.style.alignItems = "center";    // 顶部对齐
	// div.innerHTML = `<button data-type='effect' style='padding: 2px 5px;cursor: pointer;user-select: none;'>×</button>`;
	// const button = div.querySelector("button[data-type='effect']");
	// button.style.position = "absolute";
	// button.style.right = "5px";
	// button.style.top = "2px";
	// button.addEventListener("click", function(e) {
	//   e.stopPropagation();
	//   if (div.parentNode) div.parentNode.removeChild(div);
	// });
	//
	//
	// range.insertNode(div);
	// // 整光标位置
	// const newRange = document.createRange();
	// newRange.setStartAfter(div);
	// newRange.collapse(false);
	// selection.removeAllRanges();
	// selection.addRange(newRange);

	// if(range.commonAncestorContainer.nextElementSibling){
	//   range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = value_url
	//   // let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
	//   // selectedTextList.map(child=>{
	//   //   if(child.id==dataId){
	//   //     child.name = `${range.toString()}/(${item})`
	//   //   }
	//   // })
	//   return
	// }

	// let id = nanoid(4)

	var tipBox = document.createElement("div");
	tipBox.innerHTML = `<span class='change' data-id=${value_url}></span> <button data-type='effect' style='font-size:10px;'>×</button>`
	// tipBox.innerHTML = "<img> <button data-type='effect' style='font-size:10px;'></button>";
	// var img = tipBox.querySelector("img")
	// img = document.createElement('img');
	// img.src = 'http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D'; // 替换为实际路径
	// // img.alt = '动态插入的图片';
	// img.style.width = '10px';
	// img.style.height = '10px';
	// tipBox.setAttribute("data-id", id);
	tipBox.setAttribute("data-attr", "yinxiao");
	tipBox.setAttribute("data-type", "number11");
	tipBox.setAttribute("class", "parent");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.width = '60px'
	// tipBox.style.height = '100px'
	//
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	tipBox.style.marginLeft = "5px";
	tipBox.style.pointerEvents = "auto";
	// tipBox.className = 'create_background'
	// tipBox.style.backgroundImage = "url(@/assects/images/aiImages/jingpin.png)"
	// tipBox.style.backgroundSize = "100%";
	// tipBox.style.backgroundRepeat = "no-repeat";
	// tipBox.style.backgroundColor = '#f00
	tipBox.style.backgroundImage = `url(${musicIcon})`
	tipBox.style.backgroundSize = '20px 20px';
	tipBox.style.backgroundRepeat = "no-repeat";
	tipBox.style.backgroundPosition = "5px center";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	closeBtn.style.marginLeft = "24px";
	editorRefTriggerInput()
	// let id = nanoid(4)
	// const span = document.createElement('span');
	// // // span.style.display = "none";
	// // // span.style.width = '50px'
	// // // console.log(item)
	// // // span.innerText = 'llll'
	// // span.className = id;
	// // // span.data-id = id;
	// span.setAttribute('data-name', id);
	// range.surroundContents(span);
	// selection.removeAllRanges();
	// span.appendChild(range.extractContents());
	// range.insertNode(span);
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();



		// const prevElement = tipBox.previousSibling
		// // console.log('ppp',prevElement)
		// if (!prevElement) return;
		// // // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		// const fragment = document.createDocumentFragment();
		// while (prevElement.firstChild) {
		//   fragment.appendChild(prevElement.firstChild);
		// }
		// // // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		// prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		// prevElement.parentNode.replaceChild(fragment, prevElement);
		// prevElement.remove()
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		editorRefTriggerInput()
	});

	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});

	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
		// 获取 div 的父节点
		//     const parent = div.parentNode;
		// // 在父节点中，将 span 插入到 div 前面
		//     parent.insertBefore(span, div);
		console.log(tipBox)
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// 关闭弹窗
	// effects.value.effectsDialogVisible = false
	// stopPopover.value = false
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


let handlePopoverClick= (event) => {
      event.stopPropagation();
};


let  handleClickOutside= (event) => {
  const popoverElement = document.querySelector('.pause-popover');
  if (popoverElement && !popoverElement.contains(event.target)) {
    stopPopover.value = false;
  }
};



// 鼠标滑动选择文本时
const selectstart = (e) => { }
// // 粘贴处理
// const handlePaste = (e) => {
//   e.preventDefault()
//   const text = (e.clipboardData || window.clipboardData).getData('text')
//   const currentText = editorRef.value.innerText
//   const remainingSpace = MAX_LENGTH - currentText.length
//
//   if (remainingSpace <= 0) return
//
//   const pastedText = text.slice(0, remainingSpace)
//   document.execCommand('insertText', false, pastedText)
// }
//
// // 中文输入法处理
// const handleCompositionStart = () => {
//   isComposing.value = true
// }
//
// const handleCompositionEnd = (e) => {
//   isComposing.value = false
//   const newText = e.target.innerText
//   if (newText.length > MAX_LENGTH) {
//     editorRef.value.innerText = enforceLimit(newText)
//   }
// }
//
// // 替换功能示例
// const replaceText = (start, end, newText) => {
//   const current = editorRef.value.innerText
//   const modified = current.slice(0, start) + newText + current.slice(end)
//
//   if (modified.length > MAX_LENGTH) {
//     console.error('替换失败：超过字数限制')
//     return false
//   }
//
//   editorRef.value.innerText = modified
//   return true
// }
// let savedRange;
// const editable = ref(null);
// document.addEventListener('click', function(event) {
//   // Prevent default selection reset when clicking outside the contenteditable div
//   event.preventDefault();
//   if (savedRange) {
//     const selection = window.getSelection();
//     selection.removeAllRanges();
//     selection.addRange(savedRange);
//
//     setTimeout(()=>{
//
//       console.log('888888888888888',selection.getRangeAt(0).toString())
//     },1000)
//
//   }
// });

// 监听选区变化事件
// document.addEventListener('selectionchange', function() {
//   const selection = window.getSelection();
//
//   // var range = selection.getRangeAt(0);
//   // console.log('range.toString()',range.toString())
//
//   if (selection.toString()) {
//     selectedText = selection.toString();
//     console.log('更新选中内容:', selectedText);
//   }
// });
//
// // 监听点击事件，处理获取选中的内容
// document.addEventListener('click', function(event) {
//   setTimeout(function() {
//     const currentSelection = window.getSelection().toString();
//     if (!currentSelection) {
//       if (selectedText !== '') {
//         console.log('当前未选择文字，取最近一次选中内容:', selectedText);
//         // 在这里处理需要使用的选中的内容
//       } else {
//         console.log('没有任何选中内容');
//       }
//     } else {
//       console.log('点击后选区内容:', currentSelection);
//     }
//   }, 100); // 添加延迟以确保选区更新
// });
</script>

<template>
	<div class="main_content padding-n-30" id="app_out_in" @click="handleContainerClick">
		<!-- 导入文案弹窗 -->
		<GlobalimportLetter ref="Letter" @call-parent="handleCallLetter"></GlobalimportLetter>
		<!--  音效弹窗  -->
		<soundEffects ref="effects" @call-parent="handleCallParent"></soundEffects>
		<!-- 播放音色列表 -->
		<audio ref="audioRef">
			<source type="audio/mpeg">
		</audio>

		<!--    多音字选择弹窗div   -->
		<el-popover :visible="showPopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;`">
			<div class="flex" contenteditable="false">
				<div style="padding:3px 5px;background-color: #fff;border: 1px solid #e5e5e5;border-radius: 4px"
					class="polyphonic_item margin_r-8 cursor-pointer font-size-13"
					v-for="(item, index) in polyphonicList" :key="index" @click="clickPolyphonic(item)">{{ item }}</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!--  停顿弹窗  -->
		<el-popover :visible="stopPopover" popper-class="polyphonic pause-popover"  @click="handlePopoverClick" 
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;`">
			<div class="flex" contenteditable="false">
				<div style="padding:3px 5px;background-color: #fff;border: 1px solid #e5e5e5;border-radius: 4px"
					class="polyphonic_item margin_r-8 cursor-pointer font-size-13"
					v-for="(item, index) in standstillList" :key="index" @click="clickStandstill(item)">{{ item }}ms</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!-- 数字符号弹窗  -->
		<el-popover :visible="figurePopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;height:auto`">
			<div contenteditable="false">
				<div style="margin-bottom:4px;" class="polyphonic_item1 cursor-pointer font-size-13"
					v-for="(item, index) in figureList" :key="index" @click="clickFigure(item)">
					<div class="font-size-12 iii">
						<span class="margin_r-10">{{ item.title }}</span>
						<span>{{ item.num }}</span>
					</div>
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>

		<!--  别名弹窗 -->
		<el-popover :visible="aliasPopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:180px;height:auto`">
			<div contenteditable="false">
				<el-input v-model="aliasValue" placeholder="请输入别名"></el-input>
				<div class="button_div flex flex_a_i-center margin_t-10">
					<el-button @click="addAlias" type="primary" size="default" style="margin:auto;">确定</el-button>
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>


		<!--  的<span class="NTUY" id="NTUY">风</span>-->
		<!--  <div data-type="alias" data-key="风" contenteditable="false" style="border: 1px solid purple; background-color: rgb(240, 224, 255); padding: 5px; border-radius: 6px; display: inline-block; margin-left: 5px; cursor: pointer; pointer-events: auto;">-->
		<!--    <span class="change">44</span>-->
		<!--    <button style="font-size:14px; margin-left:5px;">×</button>-->
		<!--  </div>-->
		<!--  格<span class="EXQz" id="EXQz">77</span>-->
		<!--  <div data-type="alias" data-key="和" contenteditable="false" style="border: 1px solid purple; background-color: rgb(240, 224, 255); padding: 5px; border-radius: 6px; display: inline-block; margin-left: 5px; cursor: pointer; pointer-events: auto;">-->
		<!--    <span class="change">77</span>-->
		<!--    <button style="font-size:14px; margin-left:5px;">×</button>-->
		<!--  </div>-->






		<div class="main_content_top flex flex_a_i-center">
			<div class="main_content_top_item flex flex_a_i-center flex_j_c-center margin_r-10 cursor-pointer"
				v-for="(item, index) in downlaodButtonArr" :key="index" @click.stop="clickDownlaodButton($event, index)">
				<Iconfont class="margin_r-4" size="16px" :name="`${item.url}`" />
				<span>{{ item.name }}</span>
			</div>
		</div>


		<div class="main_content_bottom flex">
			<!--    中间左边输入文案  -->
			<div class="width-full height-full main_content_bottom_item_left margin_r-20 padding-20 flex flex_d-column"
				style="padding-bottom: 6px">
				<!--    输入文案顶部操作图标      -->
				<div class="main_content_bottom_item_left_top flex flex_j_c-space-between flex_a_i-center"
					contenteditable="false">
					<div class="main_content_bottom_item_left_top_left flex">
						<div class="main_content_bottom_item_left_top_left_item flex flex_d-column flex_j_c-center flex_a_i-center cursor-pointer margin_r-30"
							v-for="(item, index) in iconsArr" :key="index" @click="clickIcons($event, index)"
							:data-id="`parent-${index}`">
							<Iconfont class="margin_b-6" size="26px" :name="`${item.IconName}`" />
							<span class="font-size-12">{{ item.name }}</span>
						</div>
					</div>

					<div class="main_content_bottom_item_left_top_right width-200 height-40 font-size-12 padding-10 flex flex_a_i-center flex_j_c-space-between"
						v-if="useAIDubbing.bgmusic_url">
						<!--    叉号按钮      -->
						<Iconfont color="#959698" class="cursor-pointer position-icon" size="16px" name="chahao"
							@click="close_music_div" />
						<div class="main_content_bottom_item_left_top_right_left flex flex_a_i-center">
							<!-- <img src="@/assets/images/aiImages/yinyueIcon.png" alt="" style="width: 30px;height: 30px">-->
							<Iconfont class="cursor-pointer margin_r-5" color="#14b265" size="24px" name="yinle" />
							<div
								class="main_content_bottom_item_left_top_right_left_right width-130 height-full font-size-14 flex flex_d-column flex_j_c-center">
								<div class="margin_b-5">{{ useAIDubbing.bgmusicObj?.materialName ||
									useAIDubbing.bgmusicObj?.musicName }}</div>
								<!-- <div class="font-size-12">444</div>-->
							</div>
						</div>
						<div class="main_content_bottom_item_left_top_right_right" @click="click_volume">
							<Iconfont class="margin_r-8 cursor-pointer" size="20px" name="shengyin_shiti" />
						</div>
					</div>


				</div>
				<!--    输入文案div      -->
				<div class="main_content_bottom_item_left_bottom">
					<el-scrollbar>
						<!--    音量div    -->
						<div class="position_volume width-40 height-160 flex flex_d-column flex_j_c-space-between flex_a_i-center padding-6-n"
							data-id="`parent-100`" v-show="is_show_volume">
							<span class="position_volume_span1 font-size-12">{{ slideValue }}%</span>
							<el-slider v-model="slideValue" vertical height="100px" size="small"
								:show-tooltip="false" />
							<span class="position_volume_span2 font-size-12">音量</span>
						</div>
						<!--   显示字数     -->
						<div id="charCount" class="position-number-words font-size-12">{{ textLength }}/{{ MAX_LENGTH }}
						</div>
						<!--   输入文案div     -->
						<!--          @paste="handlePaste"-->
						<!--          @compositionstart="handleCompositionStart"-->
						<!--          @compositionend="handleCompositionEnd"-->

						<div v-show="pinyinResult.length > 0 && pinyinBool" class="copywriting-div padding-20">
							<div v-for="item in pinyinResult" :key="item.character"
								style="display: inline-block; text-align: center; margin: 5px;">
								<div style="font-size: 14px; color: #303133;font-weight: 500">{{ item.pinyin }}</div>
								<div style="font-size: 14px;font-weight: 500">{{ item.character }}</div>
							</div>
						</div>
						<!--           @selectstart="selectstart"  @keyup="handleKeyUp"-->
						<div v-show="!pinyinBool" ref="editorRef" contenteditable="true" @input="handleInput"
							id="editor" data-id="`parent-10`"
							style="line-height: 22px;letter-spacing: 2px;font-weight:500; "
							class="copywriting-div dzm-textarea padding-20 font-size-14"
							placeholder="粘贴或输入文本内容，限5000个字内" @mouseup="mouseup" @click="handleClick">
						</div>
						<!--    显示多音字弹窗      -->
						<!--          <el-popover-->
						<!--            placement="bottom"-->
						<!--            :width="200"-->
						<!--            trigger="click"-->
						<!--            :visible="popoverBool"-->
						<!--            class="popupStyle"-->
						<!--            style="top:200px;"-->
						<!--          >-->
						<!--            <template v-slot:content>-->
						<!--              454545-->
						<!--            </template>-->

						<!--          </el-popover>-->

						<!--          <el-popover ref="popover" trigger="click" placement="bottom" :width="200" style="position: absolute; top:2000px;left:200px;"-->
						<!--                      v-model:visible="popoverBool">-->
						<!--           -->

						<!--            <div >···内容···</div>-->
						<!--          </el-popover>-->





						<!--          <div class="AICopywriting_div width-440 height-90 margin_l-30 flex flex_d-column flex_a_i-center flex_j_c-space-between">-->
						<!--            <div class="AICopywriting_div_top height-40 flex flex_a_i-center padding-n-10">-->
						<!--              <img src="@/assets/images/aiImages/aiImgIcon.png" alt="" class="width-24 height-24">-->
						<!--              <div class="width-1 height-16 margin-n-5"></div>-->
						<!--              <el-input placeholder="输入您想要的文案类型" v-model="search_copywritingType" style="border:none"></el-input>-->
						<!--            </div>-->
						<!--            <div class="AICopywriting_div_bottom height-40 width-full flex flex_a_i-center flex_j_c-space-between">-->
						<!--              <div class="AICopywriting_div_bottom_left height-full flex flex_a_i-center">-->
						<!--                <div class="AICopywriting_div_bottom_left_item width-80 height-30 margin_r-10 flex flex_a_i-center flex_j_c-center cursor-pointer" v-for="item in 4">企业宣传</div>-->
						<!--              </div>-->
						<!--              <div class="AICopywriting_div_bottom_right height-full flex flex_a_i-center flex_j_c-flex-end font-size-14 cursor-pointer" @click="clickMoreIcon">-->
						<!--                更多-->
						<!--                <Iconfont-->
						<!--                    size="12px"-->
						<!--                    name="jiantou"-->
						<!--                />-->
						<!--              </div>-->
						<!--            </div>-->
						<!--          </div>-->

					</el-scrollbar>
				</div>


				<!--   底部播放按钮等   -->
				<div class="speaker_content_bottom height-60 flex flex_a_i-center flex_j_c-space-between">
					<!--   语速 语调    -->

					<div class="speaker_content_bottom_left height-full flex flex_a_i-center margin_r-20">
						<el-popover placement="bottom" :width="600" trigger="click"
							popper-class="gradient-slider-popover">
							<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
								<el-slider show-stops class="flex-item_f-8 slider gradient-slider"
									v-model="intonationValue" :min="-12" :max="12" :step="1" :show-tooltip="false" />
								<span class="font-size-14 margin_l-10">{{ intonationValue }}</span>
							</div>
							<template #reference>
								<div
									class="speaker_content_bottom_left_left_button  height-30 flex flex_a_i-center padding-n-10 margin_r-10 cursor-pointer font-size-14">
									<span class="margin_r-10 width-30">语调</span>
									<span class="width-30">{{ intonationValue }}%</span>
								</div>
							</template>
						</el-popover>



						<el-popover placement="bottom" :width="600" trigger="click"
							popper-class="gradient-slider-popover">
							<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
								<el-slider show-stops class="flex-item_f-8 slider gradient-slider" v-model="speechValue"
									:min="0.5" :max="2.0" :step="0.1" :show-tooltip="false" />
								<span class="font-size-14 margin_l-10">{{ speechValue }}</span>
							</div>
							<template #reference>
								<div
									class="speaker_content_bottom_left_left_button  height-30 flex flex_a_i-center padding-n-10 margin_r-10 cursor-pointer font-size-14">
									<span class="margin_r-10 width-30">语速</span>
									<span class="width-30">{{ speechValue }}x</span>
								</div>
							</template>
						</el-popover>


					</div>

					<!--        <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语速</span>-->
					<!--          <el-slider class="flex-item_f-8" v-model="speechValue" :min="0.5" :max="2.0" :step="0.1" :show-tooltip="false"/>-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ speechValue }}x</span>-->
					<!--        </div>-->
					<!--        <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语调</span>-->
					<!--          <el-slider class="flex-item_f-8" v-model="intonationValue" :min="-12" :max="12" :step="1" :show-tooltip="false"/>-->
					<!--          <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ intonationValue }}</span>-->
					<!--        </div>-->
					<!--    底部音频    -->
					<div class="speaker_content_bottom_bottom flex-item_f-2">
						<AudioPlayer ref="AudioPlayerRef" :audioUrl="audioUrl" :isPauseTtsAudio="isPauseTtsAudio"
							@stopPlay="stop_timbre_play"></AudioPlayer>
					</div>
					<!--    中间 合成音频  快速试听    -->
					<div
						class="speaker_content_bottom_middle height-full flex flex_a_i-center flex_j_c-space-around padding_l-20">

						<el-tooltip class="box-item" effect="dark" content="合成将消耗字符数" placement="top-start">
							<div class="speaker_content_bottom_middle_item_left flex flex_a_i-center flex_j_c-center cursor-pointer margin_r-10 height-40 width-100"
								@click="syntheticAudioButton(1)" v-loading="loading">
								<img src="@/assets/images/aiImages/synthetic_audio.png" alt=""
									class="width-18 height-18">
								<span class="span1 margin_l-5">合成音频</span>
							</div>
						</el-tooltip>
						<div class="speaker_content_bottom_middle_item_right flex flex_a_i-center flex_j_c-center cursor-pointer height-40 width-100"
							@click="syntheticAudioButton(2)" v-loading="trial_listening">
							<img src="@/assets/images/aiImages/trial_listening.png" alt="" class="width-18 height-18">
							<span class="span2 margin_l-10 text-align-center">快速试听</span>
						</div>
					</div>

				</div>








			</div>

			<!--    中间右边音色列表  -->
			<div class="width-full height-full main_content_bottom_item_right padding-20" style="overflow: hidden">
				<!--      <el-scrollbar>-->
				<!--   顶部搜索   -->
				<div
					class="main_content_bottom_item_right_top_search flex flex_a_i-center flex_j_c-space-between margin_b-20">
					<div class="main_content_bottom_item_right_top_search_left font-size-13">音色列表</div>
					<div
						class="main_content_bottom_item_right_top_search_right flex flex_a_i-center flex_j_c-space-between">
						<el-input v-model="input_search" placeholder="输入想找的音色关键词" class="margin_r-10"></el-input>
						<el-button type="success" round @click="search_speaker">搜索</el-button>
					</div>
				</div>
				<!--   选择项   -->
				<div class="speaker_content margin_b-10">
					<div class="speaker_content_list flex flex_j_c-flex-start margin_b-6">
						<div class="speaker_content_list_item margin_r-20 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteVoiceTypeNum == item.name }" v-for="item in voiceTypeArr"
							@click="selecteVoiceType(item)">
							{{ item.name }}
						</div>
					</div>

					<!--      性别      -->
					<div class="speaker_content_list flex flex_j_c-flex-start margin_b-6">
						<div class="speaker_content_list_item margin_r-20 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteGenderNum == item.name }" v-for="item in gendersArr"
							@click="selecteGender(item)">
							{{ item.name }}
						</div>
					</div>
					<!--     第二层数组     -->
					<div class="speaker_content_list flex flex_j_c-flex-start margin_b-6">
						<div class="speaker_content_list_item margin_r-20 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteSecondNum == item.name }" v-for="(item, index) in secondArr"
							@click="selecteSecond(item, index - 1)">
							{{ item.name }}
						</div>
					</div>
					<!--  第三层数组   uniqueList     -->
					<div class="speaker_content_list flex flex_j_c-flex-start margin_b-6">
						<div class="speaker_content_list_item margin_r-20 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteUniqueNum == item.name }" v-for="item in thirdArrList"
							@click="selecteUnique(item)">
							{{ item.name }}
						</div>
					</div>
				</div>
				<!--  中间人物列表    -->

				<div class="speaker_content_middle margin_b-30 " v-loading="filter_listLoading" style="height:70%;">
					<el-scrollbar @scroll="handleScroll" ref="scroll" always>
						<div class="speaker_content_middle_list flex flex_j_c-flex-start" v-if="soundList.length > 0">
							<div class="speaker_content_middle_list_item width-100 height-120 flex flex_a_i-center flex_j_c-center margin_r-12 margin_b-20 cursor-pointer"
								v-for="item in soundList" :key="item.id" @click="selectSoundItem(item)"
								:style="item.voiceName == SoundItemId ? `background-image:linear-gradient(134deg, rgba(10, 175, 96, 1), rgba(255, 214, 0, 1));background-color:#f1fbf6` : 'background-color:#f7f7f9'">
								<!--       精品和珍享图片         -->
								<div class="position_image overflow-hidden">
									<img :src="item.voiceType == '臻享' ? zhenxiang : jingpin"
										style="width: 54px;height: 20px" alt="">
								</div>
								<!--      心图片          -->
								<!--                <img src="@/assets/images/aiImages/collectIcon.png" alt="" class="collectIcon width-20 height-20">-->
								<div class="speaker_content_middle_list_item_insideDiv width-96 height-116 flex flex_d-column flex_a_i-center flex_j_c-center"
									:style="item.voiceName == SoundItemId ? `background-color:#f1fbf6` : ''">
									<!--    头像  名称     -->
									<div class="speaker_content_middle_list_item_insideDiv_avatar margin_t-14">
										<el-avatar :size="46" :src="item.avatarUrl" />

										<div class="speaker_content_middle_list_item_insideDiv_avatar_position_div  width-20 height-20 flex flex_a_i-center flex_j_c-center"
											v-show="!item.isPlay" @click="playAudio(item, index)">
											<Iconfont color="#fff" size="12px" name="bofang" />
										</div>

										<div class="speaker_content_middle_list_item_insideDiv_avatar_position_zanting  width-20 height-20 flex flex_a_i-center flex_j_c-center"
											v-show="item.isPlay" @click="playAudio(item, index)">
											<Iconfont color="#fff" size="12px" name="pause-fill" />
										</div>


									</div>
									<div
										class="speaker_content_middle_list_item_insideDiv_nickName margin_t-4 font-size-14">
										{{
											item.platformNickname }}</div>
									<!--                  <div class="matching_degree margin_t-4">89%</div>-->
								</div>
							</div>
						</div>
						<el-empty description="暂无数据" v-else />
					</el-scrollbar>
				</div>

				<!--   语速  语调   -->
				<!--        <div class="speaker_content_bottom">-->
				<!--          &lt;!&ndash;   语速 语调    &ndash;&gt;-->
				<!--          <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语速</span>-->
				<!--            <el-slider class="flex-item_f-8" v-model="speechValue" :min="0.5" :max="2.0" :step="0.1" :show-tooltip="false"/>-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ speechValue }}x</span>-->
				<!--          </div>-->
				<!--          <div class="speaker_content_bottom_speed_speech flex flex_a_i-center flex_j_c-space-between">-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_first margin_r-10 flex-item_f-1 text-align-center">语调</span>-->
				<!--            <el-slider class="flex-item_f-8" v-model="intonationValue" :min="-12" :max="12" :step="1" :show-tooltip="false"/>-->
				<!--            <span class="speaker_content_bottom_speed_speech_span_last margin_l-10 flex-item_f-1 text-align-center">{{ intonationValue }}</span>-->
				<!--          </div>-->
				<!--          &lt;!&ndash;    中间 合成音频  快速试听    &ndash;&gt;-->
				<!--          <div class="speaker_content_bottom_middle height-100 flex flex_a_i-center flex_j_c-space-around padding-n-40">-->
				<!--            <div class="speaker_content_bottom_middle_item_left flex flex_a_i-center flex_j_c-center cursor-pointer" @click="syntheticAudioButton(1)" v-loading="loading">-->
				<!--              <img src="@/assets/images/aiImages/synthetic_audio.png" alt="" class="width-22 height-22">-->
				<!--              <span class="span1 margin_l-10" >合成音频</span>-->
				<!--            </div>-->
				<!--            <div class="speaker_content_bottom_middle_item_right flex flex_a_i-center flex_j_c-center cursor-pointer" @click="syntheticAudioButton(2)" v-loading="trial_listening">-->
				<!--              <img src="@/assets/images/aiImages/trial_listening.png" alt="" class="width-20 height-20">-->
				<!--              <span class="span2 margin_l-10 text-align-center" >快速试听</span>-->
				<!--            </div>-->
				<!--          </div>-->
				<!--          &lt;!&ndash;    底部音频    &ndash;&gt;-->
				<!--          <div class="speaker_content_bottom_bottom">-->
				<!--            <AudioPlayer ref="AudioPlayerRef" :audioUrl="audioUrl" :isPauseTtsAudio="isPauseTtsAudio" @stopPlay="stop_timbre_play"></AudioPlayer>-->
				<!--          </div>-->
				<!--        </div>-->
				<!--      </el-scrollbar>-->
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.create_background {
	width: 20px;
	height: 20px;
	background-image: url("http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D");
}

//选中文字展示样式
.wenziDivStyle {
	border: none;
	background-color: #eff8f2;
	padding: 5px;
	display: inline-block;
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.cursor = "pointer";
	// tipBox.style.pointerEvents = "auto";
}

::v-deep(.polyphonic_item1) {
	//background-color: #006eff;
	display: inline-block;
	width: 100%;
	height: 30px;
	line-height: 30px;

	.iii {
		width: 100%;
		height: 50%;
	}
}

.polyphonic_item1:hover {
	background-color: rgba(129, 107, 107, 0.21);
}

//修改滚动条颜色
//::v-deep(.el-scrollbar__bar.is-vertical) {
//  background-color: rgba(0, 0, 0, 0.1); /* 修改背景颜色 */
//}
::v-deep(.el-scrollbar__bar.is-vertical div) {
	background-color: #0aaf60;
	/* 修改滚动条颜色 */
	opacity: 1;
}

#editor {
	user-select: text;
	/* 确保文本可被选择 */
}

::v-deep(.tts-tag) {
	background-color: #f00 !important;
}


::v-deep(.el-popper__arrow::before) {
	//width: 0;
	//height:0;
	//border: none;
}

::v-deep(.el-popper__arrow::before) {
	/* position: absolute; */
	width: 0px;
	height: 0px;
	//z-index: -1;
	content: "";
	//transform: rotate(45deg);
	//background: var(--el-text-color-primary);
	box-sizing: border-box;
}

::v-deep(.el-popper.is-light, .el-popper.is-light>.el-popper__arrow):before {
	//background: var(--el-bg-color-overlay);
	//border: 0px solid var(--el-border-color-light);
}

::v-deep(.el-slider__button) {
	height: 14px;
	width: 14px;
}

::v-deep(.el-slider__bar) {
	//background: #0aaf60;
	background: linear-gradient(108deg, #FFD600 0%, #0AAF60 100%);
}

::v-deep(.el-slider__button) {
	border: 2px solid #0aaf60;
}

.main_content {
	background-color: #f7f7f9;
	overflow: hidden;
	position: relative;

	&_top {
		height: 70px;

		//background-color: #67c23a;
		&_item {
			width: 100px;
			height: 30px;
			text-align: center;
			line-height: 30px;
			border-radius: 4px;
			color: #121212;
			background-color: #fff;
			font-size: 12px;
		}
	}

	&_bottom {
		height: calc(100vh - 150px);

		&_item_left {
			background-color: #fff;
			flex: 1;
			min-width: 650px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;

			//左边div顶部
			&_top {
				height: 70px;

				//background-color: #006eff;
				&_left {}

				&_right {
					background-image: url("@/assets/images/aiImages/top_right_bg.png");
					background-repeat: no-repeat;
					background-size: cover;
					position: relative;

					.position-icon {
						position: absolute;
						top: -7px;
						right: -7px;
					}

					&_left {
						&_right {
							div {
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;

								&:first-child {
									color: #121212;
								}

								&:last-child {
									color: #7E838D;
								}
							}
						}
					}

					&_right {}
				}
			}

			//输入文案div样式
			&_bottom {
				flex: 1;
				background-color: #f7f7f9;
				border-radius: 8px;
				position: relative;
				outline: none;
				//overflow-x:hidden;
				overflow-y: auto;
				position: relative;


				//多音字弹窗样式
				//弹窗样式
				//.popupStyle {
				//  :v-deep(.el-popper){
				//
				//    //top: 200px!important;
				//    //left: 200px!important;
				//  }
				//
				//}
				.position_volume {
					position: absolute;
					top: 20px;
					right: 20px;
					background-color: #fff;
					border-radius: 6px;

					&_span1 {}

					// 控制滑块的默认颜色
					::v-deep(.el-slider__bar) {
						background: #0aaf60;
					}

					::v-deep(.el-slider__button) {
						border: 2px solid #0aaf60;
					}

					&_span2 {}
				}

				//输入字数样式
				.position-number-words {
					position: absolute;
					bottom: 5px;
					right: 20px;
					color: #888d97;
				}

				//文案div样式
				.copywriting-div {
					width: 100%;
					//height: 100%;
					min-height: 100px;
					//background-color: #dff;
					overflow-y: auto;
					outline: none;
				}

				/* 输入框 */
				.dzm-textarea {
					background: none;
					outline: none;
					//padding: 10px 10px 30px;
					//border: 1px solid #eeeeee;
					//border-radius: 4px;
					word-wrap: break-word;
					word-break: break-all;
					-webkit-user-modify: read-write-plaintext-only;
				}

				/* 输入框为空时显示 placeholder */
				.dzm-textarea:empty:before {
					content: attr(placeholder);
					color: #cdcdcd;
				}

				/* 输入框获取焦点时移除 placeholder */
				.dzm-textarea:focus:before {
					// content: none;
					line-height: 18px;
				}

				//ai 文案div
				.AICopywriting_div {

					//background-color: #67c23a;
					&_top {
						width: 100%;
						border-radius: 20px;
						border: 2px solid #0AAF60;

						div {
							border: 1px solid #0AAF60;
						}

						::v-deep(.el-input__wrapper) {
							background-color: #F7F7F7 !important;
							border-radius: 40px !important;
							box-shadow: none;
						}
					}

					&_bottom {

						//background-color: #006eff;
						&_left {

							//background-color: #67c23a;
							&_item {
								background: #FFFFFF;
								border-radius: 16px;
								font-weight: 500;
								font-size: 12px;
								color: #0AAF60;
							}
						}

						&_right {
							font-weight: 400;
							color: #7E838D;
						}
					}
				}
			}


			//  中间主体右边
			.speaker_content_bottom {
				//background-color: #f00;

				.speaker_content_bottom_left {
					flex-shrink: 0;
					//margin-right:40px;
					//background-color: #ddd;
					//background-color: #67c23a;
					//::v-deep(.gradient-slider-popover) {
					//
					//  .speaker_content_bottom_left_speed_speech{
					//    .gradient-slider {
					//      /* 必须覆盖的基准色 */
					//      --el-slider-main-bg-color: transparent;
					//      .el-slider__bar {
					//        background: linear-gradient(90deg, #ff6b6b, #ff8787);
					//        height: 4px;
					//      }
					//      .el-slider__button {
					//        border-color: #ff6b6b;
					//        background: linear-gradient(45deg, #ff6b6b, #ff8787);
					//      }
					//    }
					//  }
					//
					//
					//}


					//
					//:deep(.custom-popover){
					//  .speaker_content_bottom_left_speed_speech{
					//    .gradient-slider{
					//
					//    }
					//          ::v-deep(.el-slider__bar){
					//            //background: #0aaf60;
					//            background: linear-gradient( 108deg, #FFD600 0%, #0AAF60 100%);
					//          }
					//          ::v-deep(.el-slider__button){
					//            border: 2px solid #0aaf60;
					//          }
					//  }
					//}


					//&_speed_speech{
					//  //font-weight: 400;
					//  //font-size: 14px;
					//  //color: #121212;
					//  background-color: #f00;
					//
					//  ::v-deep(.el-popover){
					//
					//
					//    .slider{
					//       控制滑块的默认颜色
					//      :：v-deep(.el-slider__bar){
					//        //background: #0aaf60;
					//        background: linear-gradient( 108deg, #FFD600 0%, #0AAF60 100%);
					//      }
					//      ::v-deep(.el-slider__button){
					//        border: 2px solid #0aaf60;
					//      }
					//    }
					//
					//
					//  }
					//
					//}


					&_left_button {
						background: #F5F5F5;
						border-radius: 4px;

						span {
							&:first-child {
								color: #9d9d9d;
							}

							&:last-child {
								color: #565d66;
							}
						}
					}

					//.position_volume{
					//  position: absolute;
					//  top:20px;
					//  right: 20px;
					//  background-color: #fff;
					//  border-radius: 6px;
					//  &_span1{
					//
					//  }
					//  // 控制滑块的默认颜色
					//  ::v-deep(.el-slider__bar){
					//    background: #0aaf60;
					//  }
					//  ::v-deep(.el-slider__button){
					//    border: 2px solid #0aaf60;
					//  }
					//
					//  &_span2{}
					//}
				}

				//合成音频和快速试听按钮
				.speaker_content_bottom_middle {

					&_item_left,
					&_item_right {
						//width: 100px;
						//height: 40px;
						border-radius: 8px;

						.span1,
						.span2 {
							font-weight: 500;
							font-size: 12px;
						}

						.span1 {
							color: #fff;
						}

						.span2 {
							color: #0BAF60;
						}
					}

					&_item_left {
						background: #0BAF60;
					}

					&_item_right {
						border: 2px solid #0AAF60;
					}
				}

				//中间主体右边播放语音组件
				//.speaker_content_bottom_bottom{
				//
				//}
			}












		}



		//右边音色列表
		&_item_right {
			background-color: #fff;
			max-width: 600px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;

			&_top_search {
				&_left {
					font-weight: 500;
					color: #121212;
					position: relative;

					&::before {
						content: '';
						display: inline-block;
						width: 100%;
						height: 4px;
						position: absolute;
						left: 0;
						bottom: -4px;
						//z-index: -1;
						//margin: 0px;
						//border-radius: inherit; /*important*/
						background: linear-gradient(to right, #0AAF60, #FFD600);
					}
				}

				&_right {
					width: 260px;
					height: 32px;
					background: #F7F7F9;
					border-radius: 18px;

					::v-deep(.el-input__wrapper) {
						//align-items: center;
						background-color: #F7F7F7 !important;
						border-radius: 40px !important;
						// border: none;
						// outline: none !important;
						box-shadow: none;
					}
				}
			}

			//  选择项样式
			.speaker_content {

				//background-color: #006eff;
				//height:40px;
				&_list {
					flex-wrap: wrap;

					//background-color: #F7F7F9;
					//height:40px;
					&_item {
						//width: 28px;
						//height: 20px;
						//background-color: #888888;
						//font-weight: 400;
						//font-size: 12px;
						//color: #121212;
						//line-height: 20px;
						//text-align: center;
						//font-style: normal;
						flex-shrink: 0;
						//font-family: PingFangSC, PingFang SC;
						border-radius: 4px;
						color: #121212;
						font-weight: 500;
					}

					.is_active {
						//background: rgba(239, 179, 153, 0.46);
						border-radius: 4px;
						color: #FB6D30;
					}
				}
			}

			//  中间任务列表
			.speaker_content_middle {

				//background-color: #ddffff;
				//overflow-y: auto;
				&_list {
					flex-wrap: wrap;

					&_item {
						background-color: #F7F7F9;
						border-radius: 8px 32px 8px 8px;
						position: relative;
						overflow: hidden;

						//珍享和精品图片div
						.position_image {
							position: absolute;
							top: 0;
							left: 0;
						}

						//收藏图片图片
						.collectIcon {
							position: absolute;
							top: 6px;
							right: 14px;
						}

						&_insideDiv {
							border-radius: 8px 32px 8px 8px;
							background-color: #F7F7F9;

							&_avatar {
								position: relative;

								//background-color: #006eff;
								&_position_div {
									display: none;
									position: absolute;
									border-radius: 20px;
									right: 0;
									bottom: 0;
									background: rgba(18, 18, 18, 0.6);
								}

								&_position_zanting {
									position: absolute;
									border-radius: 20px;
									right: 0;
									bottom: 0;
									background: rgba(18, 18, 18, 0.6);
								}

								&:hover {

									//background-color: #006eff;
									.speaker_content_middle_list_item_insideDiv_avatar_position_div {
										display: flex;
										position: absolute;
										border-radius: 20px;
										right: 0;
										bottom: 0;
										background: rgba(18, 18, 18, 0.6);
									}
								}
							}

							&_nickName {
								font-weight: 500;
								//font-family: PingFangSC, PingFang SC;
								color: #121212;
							}

							.matching_degree {
								//background: #fff;
								border-radius: 6px;
								padding: 3px 10px;
								color: #0AAF60;
								font-size: 12px;
							}
						}
					}
				}
			}


		}
	}
}
</style>
