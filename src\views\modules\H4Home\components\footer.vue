<template>
    <!-- 底部区域 -->
    <div class="footer-section">
        <img :src="mainImg" alt="底部图片" class="footer-image" @load="handleFooterImageLoaded" />
        <!-- 添加底部文字内容 -->
        <div class="footer-content">
            <!-- 主要内容区域使用flex布局 -->
            <div class="footer-main-content">
                <!-- 中间区域 - 创作模块 -->
                <div class="footer-column creation-module">
                    <h3>创作模块</h3>
                    <ul>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/AIDubbing')">AI配音</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/commercialDubbing')">AI商配</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/ContentCreation')">一键成片</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/cloudEditor', true)">专业云剪</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/soundStore')">音色商店</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/mySpace/myWorks')">我的作品</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/mySpace/myMaterials')">我的素材</a></li>
                    </ul>
                </div>
                <!-- 新增AI工具栏目 -->
                <div class="footer-column ai-tools">
                    <h3>AI工具</h3>
                    <ul>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/ParsingVideo')">一键解析</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/extraction')">文案提取</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/compose')">文案创作</a></li>
                        <li><a href="javascript:;" @click="handleCreationModuleClick('/watermark')">去水印字幕</a></li>
                    </ul>
                </div>

                <!-- 中间区域 - 用户支持 -->
                <div class="footer-column user-support">
                    <h3>用户支持</h3>
                    <ul>
                        <li><a href="javascript:;" @click="handleUserSupportClick('/soundStore')">爆款声音推荐</a></li>
                        <li><a href="javascript:;"
                                @click="openExternalLink('https://pcnrmu4lahrm.feishu.cn/wiki/RevuwiVIrifw3tkNV7Vc0xganFb')">操作手册</a>
                        </li>
                        <li><a href="javascript:;" @click="handleUserSupportClick('/agreement?type=user')">服务协议</a></li>
                        <li><a href="javascript:;" @click="handleUserSupportClick('/agreement?type=privacy')">隐私条款</a></li>
                    </ul>
                </div>

                <!-- 右侧区域 - 联系我们 -->
                <div class="footer-column contact-us">
                    <h3>联系我们</h3>
                    <p class="address-text">地址：山东省济南市历下区工业南路100-3三庆枫润大厦20层</p>
                    <p>代理及商务合作：13210515063</p>
                    <p>官方邮箱：<EMAIL></p>

                    <!-- 公司简介添加到联系我们下方 -->
                    <div class="company-profile">
                        <p class="profile-text">
                            <span class="profile-title">公司简介：</span>出奇（山东）数字科技有限公司创始人团队深耕配音行业多年，打通真人+AI配音的完整链路，业务能力覆盖传统商配、有声书、角色互动、语音助手、智能交互等与声音相关的多种领域，为客户提供360度无死角的数字化音频解决方案。
                        </p>
                    </div>
                </div>

                <!-- 二维码区域 -->
                <div class="qrcode-container">
                    <div class="qrcode-item">
                        <img src="@/assets/img/heihei1.png" alt="配音帮手公众号" />
                        <span>配音帮手公众号</span>
                    </div>
                    <div class="qrcode-item">
                        <img src="@/assets/img/heihei2.png" alt="配音帮手客服微信" />
                        <span>配音帮手客服微信</span>
                    </div>
                    <div class="qrcode-item">
                        <img src="@/assets/img/heihei3.png" alt="新二维码" />
                        <span>配音帮手抖音号</span>
                    </div>
                </div>
            </div>

            <!-- 底部版权信息 -->
            <div class="footer-copyright">
                <p>©<EMAIL> &nbsp;&nbsp; 出奇（山东）数字科技有限公司 &nbsp;&nbsp; 鲁ICP备2024117943号-1 &nbsp;&nbsp; <a
                        href="javascript:;" @click="handleUserSupportClick('/agreement?type=privacy')">隐私条款</a>
                    &nbsp;&nbsp; <a href="javascript:;" @click="handleUserSupportClick('/agreement?type=user')">用户协议</a>
                </p>
            </div>
        </div>
    </div>
</template>
<script setup>
import { reactive, ref, onMounted, watch, provide, getCurrentInstance, nextTick, onBeforeUnmount } from 'vue'
import { setConfig, ScaleStrategy, updateScaleFactor } from '@/utils/scaleHelper'
import { useloginStore } from '@/stores/login'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 导入底部图片
import mainImg from '@/assets/img/Main.png'
const loginStore = useloginStore()
// 添加图片加载状态跟踪变量
const imagesLoaded = ref({
    banners: false,
    bigImages: false,
    footerImage: false
});
const { proxy } = getCurrentInstance();
const router = useRouter()
const checkUserLogin = () => {
    return loginStore.token
}
// 处理底部图片加载完成事件
const handleFooterImageLoaded = () => {
    imagesLoaded.value.footerImage = true;
    checkAllImagesLoaded();
};
// 检查所有关键图片是否都已加载完成
const checkAllImagesLoaded = () => {
    // 当所有关键图片都加载完成时，重新应用缩放
    if (imagesLoaded.value.banners && imagesLoaded.value.bigImages && imagesLoaded.value.footerImage) {
        console.log('所有关键图片加载完成，重新应用缩放');
        // 延迟一点时间确保DOM已完全更新
        setTimeout(() => {
            setConfig({
                strategy: ScaleStrategy.COVER,
                maintainAspectRatio: true,
                removeBottomSpace: true
            });
            updateScaleFactor(true); // 强制更新缩放
        }, 100);
    }
};
// 处理创作模块链接点击
const handleCreationModuleClick = (path, isCloudEditor = false) => {
    // 检查用户是否登录，如果未登录且不是公开页面则提示登录
    const isMySpaceRelated = path.startsWith('/mySpace');

    if (isMySpaceRelated && !checkUserLogin()) {
        proxy.$modal.open('组合式标题');
        return;
    }

    // 如果是云剪辑路径，调用云剪辑跳转方法
    if (isCloudEditor) {
        openCloudEditor();
        return;
    }

    // 否则使用常规路由跳转
    router.push(path);
    console.log('导航到：', path);
}
// 跳转到云剪辑系统（新窗口打开）
const openCloudEditor = (projectId = null) => {
    console.log('准备跳转到云剪辑...');

    try {
        // 安全地获取用户token
        let token = "";
        const userStorage = localStorage.getItem('user');
        if (userStorage) {
            try {
                const userData = JSON.parse(userStorage);
                token = userData?.token || '';
                console.log('已获取token:', token ? '成功' : '为空');
            } catch (parseError) {
                console.error('解析用户数据失败:', parseError);
            }
        } else {
            console.log('localStorage中未找到用户信息');
        }

        // 构建URL
        let url = `https://yunjian.peiyinbangshou.com/App?token=${token}`;
        // 如果有项目ID，添加到URL中
        if (projectId) {
            url += `&projectId=${Number(projectId)}`;
        }

        console.log('跳转URL:', url);
        

        // 使用window.open在新窗口打开
        const newWindow = window.open(url, '_blank');

        // 检查新窗口是否被阻止
        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            ElMessage.warning('浏览器可能阻止了弹出窗口，请检查浏览器设置');
            console.warn('弹出窗口可能被阻止');
        } else {
            console.log('云剪辑已在新窗口打开');
        }
    } catch (e) {
        console.error('跳转云剪辑失败:', e);
        ElMessage.error('跳转失败，请稍后重试');
    }
}
// 处理用户支持链接点击
const handleUserSupportClick = (path) => {
    // 直接使用路由导航到对应页面
    router.push(path);
    console.log('导航到用户支持页面：', path);
}

// 打开外部链接
const openExternalLink = (url) => {
    window.open(url, '_blank');
    console.log('打开外部链接：', url);
}
onMounted(() => {
    // 检查底部图片是否已加载
    const footerImage = document.querySelector('.footer-image');
    if (footerImage) {
        imagesLoaded.value.footerImage = footerImage.complete;
        if (!footerImage.complete) {
            footerImage.addEventListener('load', handleFooterImageLoaded);
        }
    }
})


</script>
<style lang="scss" scoped>
/* 底部区域样式 */
.footer-section {
    margin-top: auto;
    width: 100%;
    margin-bottom: 0 !important;
    position: relative;

    .footer-image {
        width: 100%;
        height: auto;
        display: block;
        margin-bottom: 0;
    }

    .footer-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        color: #fff;
        font-size: 14px;  /* 基础字体大小调整为14px */
        line-height: 1.5;
        padding: 40px 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        /* 主内容区域使用弹性布局 */
        .footer-main-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            width: 100%;
            margin-bottom: 20px;
            padding: 0 20px;
        }

        h3 {
            font-size: clamp(14px, 1.2vw, 18px);  /* 标题字体调整为更大 */
            margin-bottom: 15px;
            font-weight: 500;
            line-height: 1.2;
        }

        a {
            color: #fff;
            text-decoration: none;
            font-size: clamp(12px, 1vw, 14px);  /* 链接字体调整 */
            transition: color 0.3s;
            line-height: 1.5;

            &:hover {
                color: #0AAF60;
            }
        }

        /* 列布局 */
        .footer-column {
            margin-bottom: 20px;
            font-weight: 300;
            flex: 0 1 auto;
            margin-right: 15px;
            /* 确保列之间有足够空间 */

            ul {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    margin-bottom: 8px;
                    font-size: clamp(12px, 1vw, 14px);  /* 列表项字体调整 */

                    &:last-child {
                        margin-bottom: 0;
                    }

                    a {
                        font-size: clamp(12px, 1vw, 14px);  /* 列表项中的链接字体调整 */
                    }
                }
            }

            p {
                font-size: clamp(12px, 1vw, 14px);  /* 段落字体调整 */
                margin: 0 0 8px 0;
                white-space: normal;
                line-height: 1.4;
            }
        }

        /* 联系我们列 */
        .contact-us {
            max-width: 30%;
            text-align: left;
            font-weight: 300;

            .address-text {
                white-space: normal;
                display: block;
                font-size: clamp(12px, 1vw, 14px);  /* 地址文字调整 */
            }

            /* 公司简介样式（嵌套在联系我们中） */
            .company-profile {
                width: 100%;

                .profile-text {
                    font-size: clamp(12px, 1vw, 14px);  /* 公司简介文字调整 */
                    line-height: 1.4;
                    margin-top: 8px;
                    white-space: normal;
                    text-align: justify;

                    .profile-title {
                        font-weight: bold;
                    }
                }
            }
        }

        /* 二维码容器样式 */
        .qrcode-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            align-items: flex-start;
            max-width: 30%;

            .qrcode-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                img {
                    width: clamp(60px, 7vw, 100px);
                    height: clamp(60px, 7vw, 100px);
                    border-radius: 4px;
                }

                span {
                    margin-top: 5px;
                    font-size: clamp(12px, 1vw, 14px);  /* 二维码描述文字调整 */
                    color: white;
                    text-align: center;
                    font-weight: 300;
                    word-break: keep-all;
                }
            }
        }

        /* 底部版权信息 */
        .footer-copyright {
            width: 100%;
            text-align: center;
            font-weight: 400;
            color: #5C5F63;
            margin-top: auto;

            p {
                margin: 0;
                padding: 0 20px;
                font-size: 12px !important;  /* 版权信息稍小一些 */
                line-height: 1.5;

                a {
                    color: #5C5F63;
                    font-size: 12px !important;  /* 版权信息链接 */

                    &:hover {
                        color: #0AAF60;
                    }
                }
            }
        }
    }
}

/* 媒体查询：针对小屏幕设备 */
@media screen and (max-width: 1100px) {
    .footer-section .footer-content {
        padding: 25px 10px;
        
        .footer-main-content {
            justify-content: flex-start;
            padding: 0 10px;
        }

        .footer-column {
            margin-right: 10px;
            max-width: 45%;

            a, li, p, .address-text {
                font-size: clamp(11px, 0.9vw, 13px);  /* 小屏幕稍微缩小字体 */
            }
        }

        .contact-us {
            max-width: 45%;
            
            .company-profile {
                max-width: 100%;
                
                .profile-text {
                    font-size: clamp(11px, 0.9vw, 13px);  /* 小屏幕公司简介 */
                }
            }
        }

        .qrcode-container {
            max-width: 100%;
            justify-content: flex-start;
            margin-top: 15px;
            
            .qrcode-item {
                img {
                    width: clamp(50px, 5vw, 80px);
                    height: clamp(50px, 5vw, 80px);
                }
                
                span {
                    font-size: clamp(11px, 0.9vw, 13px);  /* 小屏幕二维码描述 */
                }
            }
        }
    }
}

/* 针对更小屏幕的样式调整 */
@media screen and (max-width: 900px) {
    .footer-section .footer-content {
        padding: 20px 5px;
        font-size: 12px;  /* 更小屏幕基础字体 */

        h3 {
            margin-bottom: 10px;
            font-size: clamp(12px, 1.1vw, 16px);  /* 更小屏幕的标题 */
        }
        
        .footer-column {
            flex: 0 0 40%;
            margin-bottom: 15px;
            
            a, li, p, .address-text {
                font-size: clamp(10px, 0.85vw, 12px);  /* 更小屏幕文字 */
            }
        }
        
        .contact-us {
            flex: 0 0 90%;
            max-width: 90%;
            order: 4;
            
            .company-profile .profile-text {
                font-size: clamp(10px, 0.85vw, 12px);  /* 更小屏幕公司简介 */
            }
        }
        
        .qrcode-container {
            order: 5;
            gap: 10px;
            
            .qrcode-item span {
                font-size: clamp(10px, 0.85vw, 12px);  /* 更小屏幕二维码描述 */
            }
        }
    }
}
</style>
<style lang="scss">
/* 修复底部图片容器可能的边框问题 */
.footer-section {
    border-bottom: 0 !important;
    margin-bottom: 0 !important;
    line-height: 0;
    display: block;
    font-size: 0;
    background-color: transparent;
    position: relative;

    img {
        display: block;
        vertical-align: bottom;
        border: none;
        margin: 0;
        padding: 0;
        width: 100%;
    }
}
</style>