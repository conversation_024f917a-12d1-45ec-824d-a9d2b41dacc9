<template>
    <div class="h4-home-container">
        <!-- 头部导航栏 -->
        <Headbar :fixedHeader="true" />
        <div class="nav-placeholder"></div>

        <div class="banner-section">
            <el-carousel :interval="5000" class="responsive-carousel">
                <el-carousel-item v-for="(banner, index) in banners" :key="index">
                    <div class="banner-content" 
                         :style="{ backgroundImage: `url(${banner.imgSrc})` }"
                         @click="handleBannerClick(banner)">
                    </div>
                </el-carousel-item>
            </el-carousel>
        </div>

        <!-- 大图展示区域 -->
        <div class="full-width-images">
            <div v-for="(img, index) in bigImages" :key="index" class="full-width-image">
                <img :src="typeof img === 'object' && img.imgSrc ? img.imgSrc : img" :alt="`大图${index + 1}`" @load="handleBigImagesLoaded" />
                <!-- 添加透明点击区域，位置对应图片中的按钮位置 -->
                <div 
                    v-if="typeof img === 'object' && img.clickPosition"
                    class="clickable-area" 
                    @click="navigateToFeature(img.route || `/feature${index+1}`)"
                    @mouseenter="startArrowAnimation($event)"
                    :style="{
                        bottom: img.clickPosition.bottom,
                        left: img.clickPosition.left,
                        // width: img.clickPosition.width,
                        // height: img.clickPosition.height,
                    }"
                >
                    <img src="@/assets/img/xingxing.png" alt="星星" class="icon-left" />
                    <span class="button-text">{{img.buttonText || '免费试用'}}</span>
                    <img src="@/assets/img/fangxiang.png" alt="方向" class="icon-right" />
                </div>
            </div>
        </div>
        <!-- 底部区域 -->
        <Footer></Footer>
    </div>
    <!-- 登录弹窗 -->
    <loginOut ref="login_out_ref"></loginOut>
    <!-- 创作者社群弹窗 -->
    <creatorCommunity ref="creator_community_ref"></creatorCommunity>
    <!-- 账号信息弹窗 -->
    <accountInfoDialog ref="account_info_dialog_ref"></accountInfoDialog>
</template>

<script setup>
import { reactive, ref, onMounted, watch, provide, getCurrentInstance, nextTick, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useloginStore } from '@/stores/login'
import { setConfig, ScaleStrategy, updateScaleFactor } from '@/utils/scaleHelper'
import information from '@/views/account/information.vue'
// 导入登录相关组件
import loginOut from '@/views/account/login_out.vue'
import creatorCommunity from '@/views/account/creator_community_dialog.vue'
import accountInfoDialog from '@/views/account/account_info_dialog.vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import {
    Document,
    VideoPlay,
    Headset,
    EditPen,
    HomeFilled,
    Microphone,
    Shop,
    Star,
    User
} from '@element-plus/icons-vue'

// 导入轮播图图片
import banner1 from '@/assets/img/banner1.png'
import banner2 from '@/assets/img/banner2.png'
import banner3 from '@/assets/img/banner3.png'
import banner4 from '@/assets/img/banner4.png'
import banner5 from '@/assets/img/banner5.png'

// 导入头像相关资源
import avatar from '@/assets/images/account/avatar.png'
import avatarSign from '@/assets/images/account/avatar_sign.png'
import avatarSvipSign from '@/assets/images/account/avatar_svip_sign.svg'

// 导入大图
import datu1 from '@/assets/img/datu1.png'
import datu2 from '@/assets/img/datu2.png'
import datu3 from '@/assets/img/datu3.png'
import datu4 from '@/assets/img/datu4.png'
// 使用OSS链接替代本地图片
const datu5 = 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/datu5.png'
const datu6 = 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/datu6.png'
import xingxing from '@/assets/img/xingxing.png'
import fangxiang from '@/assets/img/fangxiang.png'

import Footer from './components/footer.vue'
const { proxy } = getCurrentInstance();
const router = useRouter()
const route = useRoute()
const loginStore = useloginStore()

// 弹窗引用
const login_out_ref = ref(null)
const creator_community_ref = ref(null)
const account_info_dialog_ref = ref(null)

// 头像相关数据
const user = reactive({
    avatar: avatar,
    avatarSign: avatarSign,
    avatarSvipSign: avatarSvipSign
})

// 监听登录状态变化
watch(() => loginStore.userInfo, (newValue) => {
    if (newValue) {
        user.avatar = newValue.avatar || avatar
        // 可以根据用户类型设置不同的标记
        if (newValue.benefitLevel) {
            switch (newValue.benefitLevel) {
                case 0:
                    user.avatarSign = '' // 不显示标记
                    break
                case 1:
                    user.avatarSign = avatarSign
                    break
                case 2:
                    user.avatarSign = avatarSvipSign
                    break
                default:
                    user.avatarSign = ''
            }
        } else {
            user.avatarSign = '' // 不显示标记
        }
    }
}, { deep: true, immediate: true })

// 弹窗位置配置
const popperOptions = ref({
    modifiers: [
        {
            name: 'offset',
            options: {
                offset: [-130, 27],
            },
        },
    ],
})

// 检查用户登录状态
const checkUserLogin = () => {
    return loginStore.token
}

// 处理登录点击
const handleLogin = () => {

        proxy.$modal.open('组合式标题')
        return 

}

// 提供给子组件的方法
const accountInfoClick = () => {
    if (account_info_dialog_ref.value) {
        account_info_dialog_ref.value.dialogVisable = true
    }
}

const creator_community = () => {
    if (creator_community_ref.value) {
        creator_community_ref.value.dialogVisable = true
    }
}

const loginout = () => {
    if (login_out_ref.value) {
        login_out_ref.value.dialogVisible = true
    }
}

// 提供方法给子组件
provide('accountInfoClick', accountInfoClick)
provide('creator_community', creator_community)
provide('loginout', loginout)

// 导航菜单数据
const navItems = reactive([
    { title: '工作台', route: '/home', active: false },
    // { title: '真人配音', route: '/realVoice', active: false },
    { title: '音色商店', route: '/soundStore', active: false },
    { title: '会员计划', route: '/membership', active: false },
    { title: '我的空间', route: '/mySpace/myWorks', active: false }
])

// 导航到页面路由
const navigateToPage = (path) => {
    const isMySpaceRelated =  (
		// path === '/membership' ||
		path === '/mySpace/myWorks'
	);
    if(isMySpaceRelated&&!checkUserLogin()){
        proxy.$modal.open('组合式标题')
        return 
    }
    router.push(path)
    // 更新激活状态
    navItems.forEach(item => {
        item.active = item.route === path
    })
}

// 跳转到云剪辑系统（新窗口打开）
const openCloudEditor = (projectId = null) => {
    console.log('准备跳转到云剪辑...');
    
    try {
        // 安全地获取用户token
        let token = "";
        const userStorage = localStorage.getItem('user');
        if (userStorage) {
            try {
                const userData = JSON.parse(userStorage);
                token = userData?.token || '';
                console.log('已获取token:', token ? '成功' : '为空');
            } catch (parseError) {
                console.error('解析用户数据失败:', parseError);
            }
        } else {
            console.log('localStorage中未找到用户信息');
        }
        
        // 构建URL
        let url = `https://yunjian.peiyinbangshou.com/App?token=${token}`;  // 云剪辑外部URL地址
        // 如果有项目ID，添加到URL中
        if (projectId) {
            url += `&projectId=${Number(projectId)}`;  // 支持直接打开特定项目
        }
        
        console.log('跳转URL:', url);
        
        // 使用window.open在新窗口打开
        const newWindow = window.open(url, '_blank');  // 云剪辑在新窗口打开，保留当前页面状态
        
        // 检查新窗口是否被阻止
        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            ElMessage.warning('浏览器可能阻止了弹出窗口，请检查浏览器设置');
            console.warn('弹出窗口可能被阻止');
        } else {
            console.log('云剪辑已在新窗口打开');
        }
    } catch (e) {
        console.error('跳转云剪辑失败:', e);
        ElMessage.error('跳转失败，请稍后重试');
    }
}

// 轮播图数据
const banners = reactive([
    {
        imgSrc: banner1,
        route: '/home'
    },
    {
        imgSrc: banner2,
        route: '/ContentCreation'  // 一键成片功能路由
    },
    {
        imgSrc: banner3,
        route: '/commercialDubbing'
    },
    {
        imgSrc: banner4,
        route: '/AIDubbing'
    },
    {
        imgSrc: banner5,
        route: '/cloudEditor'  // 云剪辑功能路由
    }
])

// 用于调试按钮点击区域的标志
const debugMode = ref(true) // 设置为true以显示点击区域，完成调试后改为false

// 大图数据
const bigImages = reactive([
    {
        imgSrc: datu1,
        route: '/AIDubbing',
        clickPosition: { bottom: '19%', left: '16%', width: '10%', height: '8%' },
        buttonText: '免费试用'
    },
    {
        imgSrc: datu2,
        route: '/AIDubbing',
        clickPosition: { bottom: '19.5%', left: '56.5%', width: '10%', height: '8%' },
        buttonText: '免费试用'
    },
    {
        imgSrc: datu3,
        route: '/commercialDubbing',
        clickPosition: { bottom: '26%', left: '16%', width: '10%', height: '8%' },
        buttonText: '免费试用'
    },
    {
        imgSrc: datu4,
        // route: '/feature4',
        // clickPosition: { bottom: '12%', left: '42%', width: '16%', height: '8%' }
    },
    {
        route: '/ContentCreation',  // 一键成片功能路由 - 暂不跳转
        imgSrc: datu5,
        clickPosition: { bottom: '21.5%', left: '15%', width: '10%', height: '8%' },
        buttonText: '免费试用'
    },
    {
        imgSrc: datu6,
        route: '/cloudEditor',  // 云剪辑功能路由 - 暂不跳转
        clickPosition: { bottom: '24.5%', left: '55.5%', width: '10%', height: '8%' },
        buttonText: '免费试用'
    }
])

// 添加图片加载状态跟踪变量
const imagesLoaded = ref({
    banners: false,
    bigImages: false,
    footerImage: false
});



// 处理轮播图加载完成事件
const handleBannerImagesLoaded = () => {
    imagesLoaded.value.banners = true;
    checkAllImagesLoaded();
};

// 处理大图加载完成事件
const handleBigImagesLoaded = () => {
    imagesLoaded.value.bigImages = true;
    checkAllImagesLoaded();
};

// 检查所有关键图片是否都已加载完成
const checkAllImagesLoaded = () => {
    // 当所有关键图片都加载完成时，重新应用缩放
    if (imagesLoaded.value.banners && imagesLoaded.value.bigImages && imagesLoaded.value.footerImage) {
        console.log('所有关键图片加载完成，重新应用缩放');
        // 延迟一点时间确保DOM已完全更新
        setTimeout(() => {
            setConfig({
                strategy: ScaleStrategy.COVER,
                maintainAspectRatio: true,
                removeBottomSpace: true
            });
            updateScaleFactor(true); // 强制更新缩放
        }, 100);
    }
};

// 导航到功能页面
const navigateToFeature = (path) => {
    // 禁止一键成片和云剪辑的跳转
    // if (path === '/cloudEditor' || path === '/ContentCreation') {
    //     console.log('该功能暂未开放，敬请期待');
    //     return;
    // }
    
    // 如果是云剪辑路径，调用云剪辑跳转方法
    if (path === '/cloudEditor') {
        openCloudEditor();  // 云剪辑使用专用方法跳转到外部链接
        return;
    }
    
    router.push(path)  // 一键成片使用普通路由跳转到内部页面
    console.log('导航到：', path)
}

// 处理轮播图点击
const handleBannerClick = (banner) => {
    if (banner.route) {
        navigateToFeature(banner.route)
    }
}

// 开始箭头动画并设置4秒后停止
const startArrowAnimation = (event) => {
    const iconRight = event.currentTarget.querySelector('.icon-right');
    if (iconRight) {
        // 先移除可能已有的类，确保动画重新开始
        iconRight.classList.remove('arrow-animate');
        
        // 强制回流，保证动画重新开始
        void iconRight.offsetWidth;
        
        // 添加动画类
        iconRight.classList.add('arrow-animate');
        
        // 设置4秒后移除动画类
        setTimeout(() => {
            iconRight.classList.remove('arrow-animate');
        }, 4000);
    }
}

// 防止页面下拉显示白色区域
const preventOverscroll = () => {
    const html = document.documentElement;
    const body = document.body;
    
    // 如果滚动超出范围，重置滚动位置
    if (window.scrollY > 0) {
        window.scrollTo(0, 0);
    }
    
    // 防止橡皮筋效果（尤其是在移动设备上）
    html.style.overflow = 'hidden';
    body.style.overflow = 'hidden';
    
    return () => {
        // 清理函数，恢复默认行为
        html.style.overflow = '';
        body.style.overflow = '';
    };
};

// 在onMounted内设置scaleHelper
onMounted(() => {
    // 为H4Home页面设置COVER缩放策略，确保填满屏幕
    setConfig({
        strategy: ScaleStrategy.COVER,
        maintainAspectRatio: true,
        removeBottomSpace: true
    });
    
    // 延迟检查并处理图片加载情况
    nextTick(() => {
        // 检查轮播图是否已加载
        const bannerImages = document.querySelectorAll('.banner-content');
        let bannersComplete = true;
        bannerImages.forEach(img => {
            const backgroundImage = window.getComputedStyle(img).backgroundImage;
            if (backgroundImage === 'none') {
                bannersComplete = false;
            }
        });
        imagesLoaded.value.banners = bannersComplete;
        
        // 检查大图是否已加载
        const bigImages = document.querySelectorAll('.full-width-image img');
        let bigImagesComplete = true;
        bigImages.forEach(img => {
            if (!img.complete) {
                bigImagesComplete = false;
                img.addEventListener('load', handleBigImagesLoaded);
            }
        });
        imagesLoaded.value.bigImages = bigImagesComplete;
        
        
        
        // 立即检查一次所有图片是否已加载
        checkAllImagesLoaded();
    });
    
    // 添加滚动控制
    const cleanup = preventOverscroll();
    
    // 保存清理函数引用，以便在组件卸载时调用
    scrollCleanup.value = cleanup;
});

// 保存滚动事件清理函数的引用
const scrollCleanup = ref(null);

// 组件卸载前清理滚动事件监听
onBeforeUnmount(() => {
    if (scrollCleanup.value) {
        scrollCleanup.value();
    }
});
</script>

<style lang="scss" scoped>
.h4-home-container {
    padding: 0;
    padding-bottom: 0;
    overflow-y: scroll;
    overflow-x: clip; /* 使用clip代替auto，防止下拉显示空白区域 */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    min-height: 100vh; /* 确保至少填满视口高度 */
    display: flex;
    flex-direction: column;
    
    /* 轮播图区域 - 响应式优化 */
    .banner-section {
        padding: 0;
        width: 100%;
        max-width: 1800px;
        margin: 0 auto;
        margin-top: 140px; /* 增加上边距，避免被顶部轮播图(60px)和导航栏(60px)遮挡，多加20px间距 */
        
        .responsive-carousel {
            width: 100%;
            border-radius: 24px;
            overflow: hidden;
            height: auto;
            
            /* 使用aspect-ratio保持宽高比，确保不同屏幕尺寸下轮播图高度自适应 */
            &::v-deep(.el-carousel__container) {
                height: 0 !important;
                padding-bottom: 31.25%; /* 16:5比例，相当于1920×600 */
                position: relative;
                
                .el-carousel__item {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .banner-content {
            height: 100%;
            width: 100%;
            background-size: cover;
            background-position: center;
            position: relative;
        }
    }

    /* 大图展示区域样式 - 自适应优化 */
    .full-width-images {
        margin: 20px auto;
        width: 100%;
        max-width: 1800px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 0 15px;
        box-sizing: border-box;

        .full-width-image {
            width: 100%;
            display: block;
            position: relative;
            cursor: pointer;
            overflow: hidden;

            img {
                width: 100%;
                height: auto;
                display: block;
                object-fit: contain;
            }
            
            .clickable-area {
                position: absolute;
                cursor: pointer;
                border-radius: 24px;
                z-index: 10;
                background-color: #0AAF60;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                overflow: hidden;
                padding: 0 12px;
                width: 182px;
                height: 48px;
                
                .icon-left {
                    width: 22px;
                    height: 22px;
                    margin-right: 6px;
                    margin-left: -5px; /* 向左偏移5px */
                    display: inline-flex;
                    align-self: center;
                }
                
                .icon-right {
                    width: 16px;
                    height: 16px;
                    margin-left: 6px;
                    margin-right: -5px; /* 向右偏移5px */
                    transition: transform 0.3s ease;
                    display: inline-flex;
                    align-self: center;
                    
                    &.arrow-animate {
                        animation: arrow-bounce 0.5s 2 ease-in-out;
                    }
                }
                
                .button-text {
                    color: #ffffff;
                    font-weight: 600;
                    font-size: 14px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                    transition: transform 0.3s ease;
                    display: inline-flex;
                    align-self: center;
                    line-height: 1;
                }
                
                &:hover {
                    background-color: #0BC06A;
                    // transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(10, 175, 96, 0.3);
                    
                    .button-text {
                        /* 移除文字放大效果 */
                    }
                }
                
                &:active {
                    transform: translateY(1px);
                    background-color: #099E56;
                    box-shadow: 0 2px 8px rgba(10, 175, 96, 0.2);
                    transition-duration: 0.1s;
                }
                
                @keyframes arrow-bounce {
                    0%, 100% {
                        transform: translateX(0);
                    }
                    50% {
                        transform: translateX(15px);
                    }
                }
            }
        }
    }
    
}


/* Hide extra action icons in Headbar */
:deep(.action-container .digit),
:deep(.action-container img.margin_l-12) {
    display: none !important;
}
</style>
<style lang="scss">
.information-tooltip {
    padding: 0;
    .el-popper__arrow {
        display: none; /* 确保箭头被隐藏 */
    }
}

/* 全局样式，去除底部线条 */
html, body {
    margin: 0;
    padding: 0;
    border: none;
    overflow-x: hidden;
    overscroll-behavior: none; /* 防止过度滚动和回弹效果 */
}



/* 额外的全局样式修复 */
body {
    background-color: transparent; /* 移除黑色背景 */
    position: fixed; /* 固定body位置，防止滚动 */
    width: 100%;
    height: 100%;
}

.h4-home-container {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    overflow-x: hidden;
    max-width: 100%;
}

/* 移除任何可能的元素间隙 */
* {
    box-sizing: border-box;
}

.h4-home-container .action-container .digit,
.h4-home-container .action-container img.margin_l-12 {
  display: none;
}
</style>