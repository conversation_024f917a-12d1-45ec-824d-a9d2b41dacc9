<template>
	<div class="documentation-container">
		<Headbar />
		<div class="content-wrapper">
			<h1>API开发文档</h1>
			<div class="content">
				<!-- 文档内容将在后续添加 -->
				<p>文档内容正在建设中...</p>
			</div>
			<div class="button-group">
				<div class="back-button" @click="goBack">返回</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue';

const router = useRouter();

// 返回上一页
const goBack = () => {
	// 如果是从新窗口打开的，则关闭当前窗口
	if (window.opener) {
		window.close();
	} else {
		// 否则返回上一页
		router.go(-1);
	}
};

// 示例：如何在其他组件中打开此页面
const openDocumentationInNewTab = () => {
	// 这段代码应该放在想要打开文档页面的组件中
	const documentationUrl = '/documentation';
	window.open(documentationUrl, '_blank');
};
</script>

<style lang="scss" scoped>
.documentation-container {
	width: 100%;
	min-height: 100vh;
	background-color: #F9F9F9;
	display: flex;
	flex-direction: column;

	.content-wrapper {
		padding: 40px;
		flex: 1;
	}

	h1 {
		font-size: 32px;
		color: #091221;
		margin-bottom: 30px;
		text-align: center;
	}

	.content {
		background-color: #fff;
		border-radius: 8px;
		padding: 30px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
		min-height: 500px;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 20px;
		color: #666;
	}

	.button-group {
		margin-top: 20px;
		display: flex;
		gap: 15px;
	}

	.back-button {
		display: inline-block;
		padding: 10px 20px;
		background-color: #0AAF60;
		color: #FFFFFF;
		border-radius: 4px;
		cursor: pointer;
		transition: background-color 0.3s;

		&:hover {
			background-color: darken(#0AAF60, 5%);
		}
	}
}
</style>