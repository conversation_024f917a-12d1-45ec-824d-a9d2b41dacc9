<template>
	<div class="api-service-container">
		<Headbar />
		<div class="api-banner">
			<img src="@/assets/img/APIone.png" alt="API服务" class="api-banner-image" />
			<!-- 添加透明覆盖层在"立即体验"按钮上 -->
			<div class="experience-button-overlay" @click="goToAIDubbing"></div>
			<!-- 添加透明覆盖层在"查看开发文档"按钮上 -->
			<div class="doc-button-overlay" @click="viewDocumentation"></div>
		</div>
		<div class="api-content">
			<img src="@/assets/img/APItwo.png" alt="API服务说明" class="api-content-image" />
		</div>
		<div class="slogan-text">
			尊享卓越音质，打造听觉盛宴
		</div>

		<!-- 加载中状态 -->
		<div v-if="loading" class="loading-container">
			<div class="loading-spinner"></div>
			<div class="loading-text">加载中...</div>
		</div>

		<!-- 正常显示数据 -->
		<template v-else>
			<!-- 第一行卡片 -->
			<div class="content-block">
				<div v-for="(service, index) in firstRowServices" :key="service.id" class="service-item">
					<div class="item-top">
						<div class="avatar">
							<img :src="service.avatar" :alt="service.name" />
						</div>
						<div class="info">
							<div class="name">{{ service.name }}</div>
							<div class="type">{{ service.description }}</div>
						</div>
					</div>
					<div class="item-bottom">
						<!-- 只显示第一个音频播放器 -->
						<div class="audio-player-container">
							<div class="audio-player" v-if="service.audioSamples && service.audioSamples.length > 0">
								<div class="play-button" @click="togglePlay($event, service.audioSamples[0].url, index)">
									<i class="play-icon"></i>
								</div>
								<div class="audio-visualizer">
									<span v-for="i in 25" :key="i"></span>
								</div>
								<div class="quality">{{ service.audioSamples[0].duration || '00:00' }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 第二行卡片 -->
			<div class="content-block second-row">
				<div v-for="(service, index) in secondRowServices" :key="service.id" class="service-item">
					<div class="item-top">
						<div class="avatar">
							<img :src="service.avatar" :alt="service.name" />
						</div>
						<div class="info">
							<div class="name">{{ service.name }}</div>
							<div class="type">{{ service.description }}</div>
						</div>
					</div>
					<div class="item-bottom">
						<!-- 只显示第一个音频播放器 -->
						<div class="audio-player-container">
							<div class="audio-player" v-if="service.audioSamples && service.audioSamples.length > 0">
								<div class="play-button" @click="togglePlay($event, service.audioSamples[0].url, index + firstRowServices.length)">
									<i class="play-icon"></i>
								</div>
								<div class="audio-visualizer">
									<span v-for="i in 25" :key="i"></span>
								</div>
								<div class="quality">{{ service.audioSamples[0].duration || '00:00' }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 查看全部按钮 -->
			<div class="view-all-button-container">
				<button class="view-all-button" @click="viewAllServices">查看全部</button>
				<button class="view-price-button" @click="viewVoicePrice">查看音色价格</button>
			</div>
		</template>

		<!-- 语音生成应用场景标题 -->
		<div class="scene-title">六大语音生成应用场景</div>

		<!-- 语音生成应用场景内容 -->
		<div class="scene-content">
			<!-- 左侧导航栏 -->
			<div class="scene-nav">
				<div v-for="item in sceneItems" :key="item.id" class="nav-item" :class="{ active: item.active }"
					@click="selectScene(item.id)">
					<!-- 左侧图标 -->
					<div class="nav-item-icon">
						<!-- 各导航项选中状态时显示对应的选中图标 -->
						<img v-if="item.id === 1 && item.active" :src="iconSelectedImg1" alt="场景图标">
						<img v-else-if="item.id === 2 && item.active" :src="iconSelectedImg2" alt="场景图标">
						<img v-else-if="item.id === 3 && item.active" :src="iconSelectedImg3" alt="场景图标">
						<img v-else-if="item.id === 4 && item.active" :src="iconSelectedImg4" alt="场景图标">
						<img v-else-if="item.id === 5 && item.active" :src="iconSelectedImg5" alt="场景图标">
						<img v-else-if="item.id === 6 && item.active" :src="iconSelectedImg6" alt="场景图标">
						<!-- 其他情况显示普通图标 -->
						<img v-else :src="item.icon" alt="场景图标">
					</div>

					<!-- 中间文字区域 -->
					<div class="nav-item-content">
						<div class="nav-item-title">{{ item.title }}</div>
						<div class="nav-item-desc">{{ item.desc }}</div>
					</div>

					<!-- 右侧箭头图标 -->
					<div class="nav-item-arrow">
						<img v-if="item.active" :src="selectedArrowImg" alt="箭头">
						<img v-else :src="item.arrow" alt="箭头">
					</div>
				</div>
			</div>

			<!-- 右侧展示区域 -->
			<div class="scene-display">
				<div v-if="activeSceneId === 1" class="scene-content-display">
					<!-- 场景一内容：教育 -->
					<div class="scene-content-wrapper">
						<img :src="educationImg" alt="教育场景" class="scene-image" />
					</div>
				</div>
				<div v-else-if="activeSceneId === 2" class="scene-content-display">
					<!-- 场景二内容：政务 -->
					<div class="scene-content-wrapper">
						<img :src="governmentImg" alt="政务场景" class="scene-image" />
					</div>
				</div>
				<div v-else-if="activeSceneId === 3" class="scene-content-display">
					<!-- 场景三内容：出版 -->
					<div class="scene-content-wrapper">
						<img :src="publishImg" alt="出版场景" class="scene-image" />
					</div>
				</div>
				<div v-else-if="activeSceneId === 4" class="scene-content-display">
					<!-- 场景四内容：医疗 -->
					<div class="scene-content-wrapper">
						<img :src="medicalImg" alt="医疗场景" class="scene-image" />
					</div>
				</div>
				<div v-else-if="activeSceneId === 5" class="scene-content-display">
					<!-- 场景五内容：电商 -->
					<div class="scene-content-wrapper">
						<img :src="ecommerceImg" alt="电商场景" class="scene-image" />
					</div>
				</div>
				<div v-else-if="activeSceneId === 6" class="scene-content-display">
					<!-- 场景六内容：商业配音 -->
					<div class="scene-content-wrapper">
						<img :src="businessDubbingImg" alt="商业配音场景" class="scene-image" />
					</div>
				</div>
			</div>
		</div>

		<!-- 新增大尺寸内容区域 -->
		<div class="large-content-section">
			<div class="section-title">产品价格套餐</div>
			
			<!-- 套餐卡片区域 -->
			<div class="packages-container">
				<div class="package-card">
					<div class="card-header">
						<span class="package-title">测试包</span>
						<div class="divider-line"></div>
					</div>
					<div class="card-content">
						<div class="content-title">对接技术测试</div>
						<div class="content-desc">可获3000测试字符</div>
					</div>
				</div>
				<div class="package-card">
					<div class="card-header">
						<span class="package-title">基础包</span>
						<div class="divider-line"></div>
					</div>
					<div class="card-content">
						<div class="content-title">预充5000，送300元</div>
						<div class="content-desc">可获得5300元字符资源</div>
					</div>
				</div>
				<div class="package-card">
					<div class="card-header">
						<span class="package-title">进阶包</span>
						<div class="divider-line"></div>
					</div>
					<div class="card-content">
						<div class="content-title">预充10000元，送1500元</div>
						<div class="content-desc">可获得11500元字符资源包</div>
					</div>
				</div>
				<div class="package-card">
					<div class="card-header">
						<span class="package-title">高级包</span>
						<div class="divider-line"></div>
					</div>
					<div class="card-content">
						<div class="content-title">预充50000元，送8000元</div>
						<div class="content-desc">可获得58000元字符资源</div>
					</div>
				</div>
			</div>
			
			<!-- 备注说明区域 -->
			<div class="notes-container">
				<div class="note-item">1.由于不同配音老师对应的字符消耗不同，无法在资源包中列出所得字符总量，具体合作细节，请沟通客服详谈。</div>
				<div class="note-item">2.如需专属并发通路，请联系客服详谈。</div>
				<!-- 新增右侧按钮 -->
				<button class="notes-right-button" @click="showContactDialog">联系客服</button>
			</div>
			
			<!-- 添加的新说明区域 -->
			<!-- <div class="additional-notes-container">
				<div class="additional-note-item">*1.标点符号，ssml标签都按照发音人字符收费计算。（ASCII码范围内1个字为1个字符；非ASCII码范围1个字为2个字符。）*2.字幕：2元/百万字符。*3.充值后不可退款，购买前请确认。</div>
			</div> -->
			
			<!-- 新添加的白色背景区域 -->
			<div class="white-background-container">
				<div class="contact-left">
					<div class="contact-title">联系我们</div>
					<div class="contact-desc-bg">
						<span class="contact-desc">大额语音生成充值优惠，声音克隆和需求定制，可以联系：</span>
					</div>
				</div>
				<div class="contact-center">
					<div class="center-left">段经理：18866833793</div>
					<div class="center-right">
						<button class="copy-btn" @click="copyToClipboard('18866833793')">复制号码</button>
					</div>
				</div>
				<div class="contact-right">
					<div class="right-left">王经理：18710081985</div>
					<div class="right-right">
						<button class="copy-btn" @click="copyToClipboard('18710081985')">复制号码</button>
					</div>
				</div>
			</div>
		</div>

		<!-- 开发文档区域 -->
		<div class="doc-section">
			<div class="doc-title">开发文档</div>
			<div class="image-container">
				<img src="@/assets/img/beijingtu8.png" alt="开发文档" class="doc-image" />
				<!-- 添加覆盖在图片上的按钮区域 -->
				<div class="doc-button" @click="viewDocumentation">查看开发文档</div>
			</div>
		</div>
		
		<!-- 添加新图片区域 -->
		<div class="additional-doc-section">
			<!-- <img src="@/assets/img/beijngtu88.png" alt="开发文档补充" class="additional-doc-image" /> -->
			<div class="service-text">企业顾问为您全程服务</div>
			<div class="stats-container">
				<div class="stat-item">
					<div class="stat-title">沟通效率</div>
					<div class="stat-value">
						<span class="arrow">↑</span><span class="number">120</span><span class="unit">%</span>
					</div>
				</div>
				<div class="stat-item">
					<div class="stat-title">人效提升</div>
					<div class="stat-value">
						<span class="arrow">↑</span><span class="number">60</span><span class="unit">%</span>
					</div>
				</div>
				<div class="stat-item">
					<div class="stat-title">工作产出</div>
					<div class="stat-value">
						<span class="arrow">↑</span><span class="number">10</span><span class="unit">倍</span>
					</div>
				</div>
			</div>
			<div class="disclaimer-text">注：数据来自特定团队在其公司使用语音生成的情况，不同组织效果不同</div>
			<div class="qr-code-section">
				<img :src="qrCodeImg" alt="技术微信群" class="qr-code-image" />
				<div class="qr-code-caption">
					<p>*开发接入问题可以加入技术微信群，</p>
					<p>将有专人为你解答</p>
				</div>
			</div>
		</div>
		
		<!-- 添加底部区域 -->
		<Footer />

		<!-- 真人配音弹窗 -->
		<FreeTrialSound ref="freeTrialSoundRef" />

		<!-- 联系客服弹窗 -->
		<Contact ref="contactRef" />

	</div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed } from 'vue';
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue';
import Footer from '@/views/modules/H4Home/components/footer.vue'; // 导入Footer组件
import FreeTrialSound from '@/views/modules/realVoice/components/index/free_trial_sound.vue';
import Contact from '@/views/modules/realVoice/components/index/contact.vue';
import axios from 'axios';
import { useRouter } from 'vue-router'; // 导入路由器
import { getApiList } from '@/api/soundStore'; // 导入新增的API接口
import { ElMessage } from 'element-plus'; // 导入Element Plus的消息提示组件
import touxiangImg from '@/assets/img/touxiang12.png'; // 导入头像图片
import kapian1Img from '@/assets/img/kapian1.png'; // 导入卡片背景图片
import beijingse2Img from '@/assets/img/beijingse2.jpg'; // 导入背景图片
import iconSceneImg1 from '@/assets/img/APItubiao1.png'; // 导入场景图标1
import iconSceneImg2 from '@/assets/img/APItubiao2.png'; // 导入场景图标2
import iconSceneImg3 from '@/assets/img/APItubiao3.png'; // 导入场景图标3
import iconSceneImg4 from '@/assets/img/APItubiao4.png'; // 导入场景图标4
import iconSceneImg5 from '@/assets/img/APItubiao5.png'; // 导入场景图标5
import iconSceneImg6 from '@/assets/img/APItubiao6.png'; // 导入场景图标6
import iconArrowImg from '@/assets/img/iconpark-icon.png'; // 导入箭头图标
import iconSelectedImg1 from '@/assets/img/xuanzhong1.png'; // 导入教育选中状态图标
import iconSelectedImg2 from '@/assets/img/xuanzhong2.png'; // 导入社交媒体选中状态图标
import iconSelectedImg3 from '@/assets/img/xuanzhong3.png'; // 导入影视制作选中状态图标
import iconSelectedImg4 from '@/assets/img/xuanzhong4.png'; // 导入有声读物选中状态图标
import iconSelectedImg5 from '@/assets/img/xuanzhong5.png'; // 导入游戏开发选中状态图标
import iconSelectedImg6 from '@/assets/img/xuanzhong6.png'; // 导入智能客服选中状态图标
import educationImg from '@/assets/img/教育.png'; // 导入教育场景图片
import governmentImg from '@/assets/img/政务.png'; // 导入政务场景图片
import publishImg from '@/assets/img/出版.png'; // 导入出版场景图片
import medicalImg from '@/assets/img/医疗.png'; // 导入医疗场景图片
import ecommerceImg from '@/assets/img/电商.png'; // 导入电商场景图片
import businessDubbingImg from '@/assets/img/商业配音.png'; // 导入商业配音场景图片
import selectedArrowImg from '@/assets/img/xuanzhong7.png'; // 导入选中状态的箭头图标
import beijingtu8 from '@/assets/img/beijingtu8.png'; // 添加图片导入
import beijingtu88 from '@/assets/img/beijngtu88.png'; // 添加图片导入
import qrCodeImg from '@/assets/img/weweima.png'; // 导入二维码图片

// 初始化路由器
const router = useRouter();

// 定义响应式数据
const apiServices = ref([]);
const loading = ref(false);

// 应用场景相关数据
const sceneItems = ref([
	{
		id: 1,
		title: '教育',
		desc: '教材有声化、AI教师语音批改、互动问答。',
		icon: iconSceneImg1,
		arrow: iconArrowImg,
		active: true
	},
	{
		id: 2,
		title: '政务',
		desc: '政策播报、应急指挥。',
		icon: iconSceneImg2,
		arrow: iconArrowImg,
		active: false
	},
	{
		id: 3,
		title: '出版',
		desc: '有声书批量生成（需多角色音色切换与音色匹配推荐功能）。',
		icon: iconSceneImg3,
		arrow: iconArrowImg,
		active: false
	},
	{
		id: 4,
		title: '医疗',
		desc: '语音病历生成、健康宣教。',
		icon: iconSceneImg4,
		arrow: iconArrowImg,
		active: false
	},
	{
		id: 5,
		title: '电商',
		desc: '面向MCN机构，批量视频输出需求。',
		icon: iconSceneImg5,
		arrow: iconArrowImg,
		active: false
	},
	{
		id: 6,
		title: '商业配音',
		desc: '调用我们的音色进行稿件录制。',
		icon: iconSceneImg6,
		arrow: iconArrowImg,
		active: false
	}
]);

// 当前选中的场景
const activeSceneId = ref(1);

// 选择场景
const selectScene = (id) => {
	activeSceneId.value = id;
	sceneItems.value.forEach(item => {
		item.active = item.id === id;
	});
};

// 每行显示4个卡片
const firstRowServices = computed(() => apiServices.value.slice(0, 4));
const secondRowServices = computed(() => apiServices.value.slice(4, 8));

// 是否显示"查看全部"按钮
const showViewAllButton = computed(() => {
	// 始终显示查看全部按钮
	return true;
});

// 查看全部服务
const viewAllServices = () => {
	console.log('新标签页打开音色商店');
	window.open('/soundStore', '_blank'); // 在新标签页打开音色商店
};

// 添加查看音色价格方法
const viewVoicePrice = () => {
	// 直接在新标签页打开语雀文档
	window.open('https://www.yuque.com/qstwuyan/zwh63k/tbtagezgptfe91kg?singleDoc#', '_blank');
};

// 格式化音频时长为mm:ss格式
const formatDuration = (seconds) => {
	if (!seconds || isNaN(seconds)) return '00:00';
	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = Math.floor(seconds % 60);
	return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 获取音频时长（异步）
const getAudioDuration = async (url) => {
	if (!url) return '00:00';
	
	return new Promise((resolve) => {
		const audio = new Audio();
		audio.onloadedmetadata = () => {
			resolve(formatDuration(audio.duration));
		};
		audio.onerror = () => {
			resolve('00:00');
		};
		audio.src = url;
	});
};

// 修改数据处理部分，计算音频时长
const fetchApiServices = async () => {
	loading.value = true;

	try {
		const response = await getApiList();
		console.log('接口原始返回数据:', response);
		
		// 根据实际接口返回结构处理数据
		const mappedServices = response.map(item => {
			console.log('处理单条数据:', item.platformNickname);
			const audioUrl = item.audioUrl || '';
			return {
				id: item.id || `api-${Date.now()}`,
				name: item.platformNickname || '未命名服务',
				description: `${item.emotionTags || ''} ${item.sceneCategory || ''}`.trim() || '暂无描述',
				avatar: item.avatarUrl || touxiangImg,
				audioSamples: [
					{ id: 1, url: audioUrl, duration: '00:00' }
				]
			};
		});
		
		apiServices.value = mappedServices;
		
		// 异步获取音频时长
		mappedServices.forEach(async (service, index) => {
			if (service.audioSamples && service.audioSamples.length > 0 && service.audioSamples[0].url) {
				const duration = await getAudioDuration(service.audioSamples[0].url);
				if (apiServices.value[index] && apiServices.value[index].audioSamples) {
					apiServices.value[index].audioSamples[0].duration = duration;
				}
			}
		});
		
		// 如果数据少于8个，重复使用现有数据填充
		const originalLength = apiServices.value.length;
		if (originalLength > 0 && originalLength < 8) {
			let index = 0;
			while (apiServices.value.length < 8) {
				// 复制已有数据并修改ID避免重复
				const copyItem = JSON.parse(JSON.stringify(apiServices.value[index % originalLength]));
				copyItem.id = `${copyItem.id}-copy-${index}`;
				apiServices.value.push(copyItem);
				index++;
			}
			console.log('填充后数据:', apiServices.value);
		}
	} catch (err) {
		console.error('获取API服务数据失败:', err);
		// 发生错误时使用模拟数据
		apiServices.value = getMockData();
	} finally {
		loading.value = false;
	}
};

// 模拟数据
const getMockData = () => {
	// 这里可以根据需要定义不同的模拟数据
	const mockData = [];
	const mockNames = ['奇妙才', '童声', '中年男声', '元气少女', '知性女声', '磁性男声', '温柔女声', '浑厚男声'];
	const mockDescriptions = [
		'幽默诙谐，亲切甜美',
		'天真活泼，童趣十足',
		'稳重自然，富有磁性',
		'活力四射，朝气蓬勃',
		'睿智优雅，温婉动听',
		'深沉有力，充满魅力',
		'柔和亲切，抚慰人心',
		'浑厚有力，宏伟大气'
	];

	for (let i = 0; i < 8; i++) {
		mockData.push({
			id: `mock-${i}`,
			name: mockNames[i],
			description: mockDescriptions[i],
			avatar: touxiangImg, // 使用导入的图片
			audioSamples: [
				{ id: 1, url: '', duration: '00:00' },
				{ id: 2, url: '', duration: '00:00' }
			]
		});
	}

	return mockData;
};

// 音频播放器对象
const audioPlayer = ref(null);
const currentPlayingIndex = ref(-1);

// 停止当前播放的音频
const stopCurrentAudio = () => {
	if (audioPlayer.value && currentPlayingIndex.value !== -1) {
		audioPlayer.value.pause();
		const allPlayers = document.querySelectorAll('.audio-player');
		allPlayers.forEach(p => {
			p.classList.remove('is-playing');
		});
		currentPlayingIndex.value = -1;
	}
};

// 处理全局点击事件
const handleGlobalClick = (event) => {
	// 检查点击的元素是否在播放器区域内
	const clickedElement = event.target;
	const audioPlayerContainer = clickedElement.closest('.audio-player-container');
	
	// 如果点击的不是播放器区域且当前有音频在播放，则停止播放
	if (!audioPlayerContainer && currentPlayingIndex.value !== -1) {
		stopCurrentAudio();
	}
};

// 切换播放状态
const togglePlay = (event, url, index) => {
	// 阻止事件冒泡，避免触发全局点击事件
	event.stopPropagation();
	
	if (!url) {
		console.warn('无效的音频URL');
		return;
	}
	
	// 获取当前点击的播放器
	const player = event.currentTarget.closest('.audio-player');
	
	// 如果点击的是已播放的，则暂停
	if (currentPlayingIndex.value === index) {
		audioPlayer.value.pause();
		player.classList.remove('is-playing');
		currentPlayingIndex.value = -1;
		return;
	}
	
	// 停止之前播放的音频
	const allPlayers = document.querySelectorAll('.audio-player');
	allPlayers.forEach(p => {
		p.classList.remove('is-playing');
	});
	
	// 创建新的音频播放器或重置现有的
	if (!audioPlayer.value) {
		audioPlayer.value = new Audio();
	}
	
	// 设置音频源并播放
	audioPlayer.value.src = url;
	audioPlayer.value.onloadedmetadata = () => {
		// 音频加载完毕后播放
		audioPlayer.value.play();
		player.classList.add('is-playing');
		currentPlayingIndex.value = index;
	};
	
	audioPlayer.value.onerror = (error) => {
		console.error('音频播放失败:', error);
		player.classList.remove('is-playing');
		currentPlayingIndex.value = -1;
	};
};

// 复制到剪贴板功能
const copyToClipboard = (text) => {
	// 使用现代剪贴板API
	if (navigator.clipboard) {
		navigator.clipboard.writeText(text)
			.then(() => {
				// 使用Element Plus的吐司提示
				ElMessage.success('复制成功');
			})
			.catch(err => {
				console.error('复制失败: ', err);
				// 失败时使用回退方法
				fallbackCopyToClipboard(text);
			});
	} else {
		// 对于不支持clipboard API的浏览器，使用回退方法
		fallbackCopyToClipboard(text);
	}
};

// 回退的复制方法（兼容性更好）
const fallbackCopyToClipboard = (text) => {
	const textArea = document.createElement('textarea');
	textArea.value = text;
	
	// 使textarea不可见
	textArea.style.position = 'fixed';
	textArea.style.left = '-999999px';
	textArea.style.top = '-999999px';
	document.body.appendChild(textArea);
	
	textArea.focus();
	textArea.select();

	try {
		const successful = document.execCommand('copy');
		if (successful) {
			ElMessage.success('复制成功');
		} else {
			console.error('复制失败');
		}
	} catch (err) {
		console.error('复制失败: ', err);
	}

	document.body.removeChild(textArea);
};

// 添加查看开发文档方法
const viewDocumentation = () => {
	// 在新窗口或新标签页打开文档页面
	window.open('https://docs.peiyinbangshou.com/docs', '_blank');
};

// 弹窗组件ref
const freeTrialSoundRef = ref(null);
// 联系客服弹窗组件ref
const contactRef = ref(null);

// 修改跳转到AI配音页面的方法为弹出弹窗
const goToAIDubbing = () => {
	console.log('显示真人配音弹窗');
	if (freeTrialSoundRef.value) {
		freeTrialSoundRef.value.dialogVisible = true;
	}
};

// 新增联系客服方法
const showContactDialog = () => {
	console.log('显示联系客服弹窗');
	if (contactRef.value) {
		contactRef.value.dialogVisible = true;
	}
};

// 页面加载时获取数据
onMounted(() => {
	console.log('API服务页面已加载');
	fetchApiServices();
	
	// 添加全局点击监听器
	document.addEventListener('click', handleGlobalClick);
});

// 组件卸载时清理监听器
onUnmounted(() => {
	// 移除全局点击监听器
	document.removeEventListener('click', handleGlobalClick);
	
	// 停止当前播放的音频
	if (audioPlayer.value) {
		audioPlayer.value.pause();
		audioPlayer.value = null;
	}
});
</script>

<style lang="scss" scoped>
.api-service-container {
	width: 100%;
	min-height: calc(100vh - 60px);
	background-color: #F9F9F9;
	overflow-y: auto;
	overflow-x: hidden;

	// 加载状态样式
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100px 0;

		.loading-spinner {
			width: 40px;
			height: 40px;
			border: 4px solid rgba(0, 0, 0, 0.1);
			border-radius: 50%;
			border-top: 4px solid #007BFF;
			animation: spin 1s linear infinite;
		}

		.loading-text {
			margin-top: 20px;
			font-size: 18px;
			color: #555;
		}
	}

	.api-banner {
		margin-top: 20px;
		text-align: center;
		margin-bottom: 0;
		position: relative; /* 添加相对定位 */

		.api-banner-image {
			max-width: 100%;
			display: block;
			margin: 0 auto; // 添加水平居中
		}

		/* 添加立即体验按钮的透明覆盖层 */
		.experience-button-overlay {
			position: absolute;
			width: 210px;
			height: 50px;
			left: 44%;
			top: 55%; /* 根据图片中按钮位置调整 */
			transform: translateX(-50%);
			cursor: pointer;
			z-index: 10;
			/* 用于调试的背景色，实际使用时可以删除或设为transparent */
			/* background-color: rgba(255, 0, 0, 0.2); */
		}

		/* 添加查看开发文档按钮的透明覆盖层 */
		.doc-button-overlay {
			position: absolute;
			width: 210px;
			height: 50px;
			left: 56%;
			top: 55%; /* 根据图片中按钮位置调整 */
			transform: translateX(-50%);
			cursor: pointer;
			z-index: 10;
			/* 用于调试的背景色，实际使用时可以删除或设为transparent */
			/* background-color: rgba(0, 255, 0, 0.2); */
		}
	}

	.slogan-text {
		text-align: center;
		margin-top: 28px;
		font-size: 42px;
		color: #091221;
		font-weight: bold;
	}

	// 场景标题样式
	.scene-title {
		text-align: center;
		margin-top: 155px;
		font-size: 42px;
		color: #091221;
		font-weight: 600;
	}

	// 场景内容容器
	.scene-content {
		width: 1635px;
		height: 736px;
		margin: 69px auto 0;
		display: flex;
		justify-content: space-between;

		// 左侧导航栏
		.scene-nav {
			width: 398px;
			height: 100%;
			display: flex;
			flex-direction: column;

			.nav-item {
				width: 398px;
				height: 106px;
				background-color: #F5F5F5;
				border-radius: 8px;
				cursor: pointer;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				position: relative;
				padding: 0;
				box-sizing: border-box;

				&:not(:first-child) {
					margin-top: 20px;
				}

				&.active {
					background-color: #0AAF60;
					box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

					.nav-item-content {
						.nav-item-title {
							color: #FFFFFF !important;
						}

						.nav-item-desc {
							color: #FFFFFF !important;
						}
					}
				}

				&:hover:not(.active) {
					background-color: #EAEAEA;
				}

				// 左侧图标
				.nav-item-icon {
					width: 33px;
					height: 33px;
					margin-left: 26px;
					display: flex;
					align-items: center;
					justify-content: center;

					img {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}
				}

				// 中间文字区域
				.nav-item-content {
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					margin-left: 27px;

					.nav-item-title {
						font-size: 18px;
						font-weight: 500;
						color: #333333;
						margin-top: 16px;
					}

					.nav-item-desc {
						font-size: 12px;
						color: #666666;
						margin-top: 15px;
						margin-bottom: 35px;
						line-height: 18px;
					}
				}

				// 右侧箭头
				.nav-item-arrow {
					width: 20px;
					height: 20px;
					position: absolute;
					right: 0px;
					top: 50%;
					transform: translateY(-50%);

					img {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}
				}
			}
		}

		// 右侧展示区域
		.scene-display {
			width: 1207px;
			height: 100%;
			.scene-content-display {
				width: 100%;
				height: 100%;
				padding: 0;
				// 场景内容样式
				.scene-content-wrapper {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;

					.scene-image {
						width: 1201px;
						height: 736px;
						object-fit: cover;
					}
				}
			}
		}
	}

	.content-block {
		width: 1635px;
		height: 282px;
		margin: 15px auto 0;
		display: flex;
		justify-content: space-between;

		&.second-row {
			margin-top: 39px;
			margin-bottom: 30px;
		}

		.service-item {
			width: 393px;
			height: 282px;
			overflow: hidden;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
			border-radius: 21px;
			background-image: url('@/assets/img/kapian1.png'); /* 添加背景图片 */
			background-size: cover; /* 确保背景图片覆盖整个区域 */
			background-position: center; /* 居中显示背景图片 */
			background-repeat: no-repeat; /* 不重复显示背景图片 */

			.item-top {
				width: 100%;
				height: 131px;
				background-color: transparent; /* 移除背景色，使用父元素背景图 */
				display: flex;
				align-items: center;
				padding: 0 20px;
				box-sizing: border-box;
				border-radius: 21px 21px 0 0;

				.avatar {
					width: 80px;
					height: 80px;
					border-radius: 50%;
					overflow: hidden;

					img {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}

				.info {
					margin-left: 18px;
					display: flex;
					flex-direction: column;
					justify-content: center;

					.name {
						font-size: 30px;
						font-weight: 600;
						color: #000B1A;
						line-height: 1.2;
						text-shadow: 0 1px 2px rgba(255,255,255,0.8); /* 添加文字阴影增强可见性 */
					}

					.type {
						font-size: 16px;
						color: #101010;
						margin-top: 12px;
						text-shadow: 0 1px 2px rgba(255,255,255,0.8); /* 添加文字阴影增强可见性 */
					}
				}
			}

			.item-bottom {
				width: 100%;
				height: 151px;
				background-color: transparent; /* 改为透明以显示整体背景图 */
				border-radius: 0 0 21px 21px;
				padding-top: 40px;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				align-items: center;

				.audio-player-container {
					width: 100%;
					display: flex;
					justify-content: center;
					margin-top: 20px;
				}

				.audio-player {
					width: 317px;
					height: 42px;
					border: 1px solid rgba(255, 255, 255, 0.6); /* 调整边框颜色 */
					background-color: rgba(255, 255, 255, 0.7); /* 添加半透明背景 */
					border-radius: 21px;
					display: flex;
					align-items: center;
					padding: 0 15px;
					box-sizing: border-box;
					cursor: pointer;

					.play-button {
						width: 20px;
						height: 20px;
						margin-right: 10px;
						position: relative;
						cursor: pointer;

						.play-icon {
							position: absolute;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%);
							width: 0;
							height: 0;
							border-top: 6px solid transparent;
							border-bottom: 6px solid transparent;
							border-left: 10px solid #000;
						}
					}

					.audio-visualizer {
						flex: 1;
						height: 13px;
						display: flex;
						align-items: center;
						justify-content: space-evenly;
						/* 更紧密的间距 */
						border-radius: 6px;
						padding: 0 2px;

						span {
							display: inline-block;
							width: 2px;
							background-color: #B2C9BE;
							border-radius: 5px;

							/* 默认状态下也有不同高度 */
							&:nth-child(1) {
								height: 5px;
							}

							&:nth-child(2) {
								height: 8px;
							}

							&:nth-child(3) {
								height: 10px;
							}

							&:nth-child(4) {
								height: 7px;
							}

							&:nth-child(5) {
								height: 12px;
							}

							&:nth-child(6) {
								height: 6px;
							}

							&:nth-child(7) {
								height: 9px;
							}

							&:nth-child(8) {
								height: 11px;
							}

							&:nth-child(9) {
								height: 8px;
							}

							&:nth-child(10) {
								height: 10px;
							}

							&:nth-child(11) {
								height: 6px;
							}

							&:nth-child(12) {
								height: 13px;
							}

							&:nth-child(13) {
								height: 9px;
							}

							&:nth-child(14) {
								height: 7px;
							}

							&:nth-child(15) {
								height: 12px;
							}

							&:nth-child(16) {
								height: 6px;
							}

							&:nth-child(17) {
								height: 10px;
							}

							&:nth-child(18) {
								height: 8px;
							}

							&:nth-child(19) {
								height: 11px;
							}

							&:nth-child(20) {
								height: 5px;
							}

							&:nth-child(21) {
								height: 9px;
							}

							&:nth-child(22) {
								height: 12px;
							}

							&:nth-child(23) {
								height: 7px;
							}

							&:nth-child(24) {
								height: 10px;
							}

							&:nth-child(25) {
								height: 8px;
							}
						}
					}

					/* 播放状态下的样式 */
					&.is-playing {
						.play-button {
							.play-icon {
								width: 10px;
								height: 12px;
								border: none;
								display: flex;
								justify-content: space-between;

								&:before,
								&:after {
									content: "";
									display: block;
									width: 3px;
									height: 100%;
									background-color: #000;
								}
							}
						}

						.audio-visualizer span {
							animation: sound 1s infinite ease-in-out;

							&:nth-child(1) {
								animation-delay: 0.0s;
							}

							&:nth-child(2) {
								animation-delay: 0.1s;
							}

							&:nth-child(3) {
								animation-delay: 0.2s;
							}

							&:nth-child(4) {
								animation-delay: 0.3s;
							}

							&:nth-child(5) {
								animation-delay: 0.4s;
							}

							&:nth-child(6) {
								animation-delay: 0.5s;
							}

							&:nth-child(7) {
								animation-delay: 0.6s;
							}

							&:nth-child(8) {
								animation-delay: 0.7s;
							}

							&:nth-child(9) {
								animation-delay: 0.8s;
							}

							&:nth-child(10) {
								animation-delay: 0.9s;
							}

							&:nth-child(11) {
								animation-delay: 1.0s;
							}

							&:nth-child(12) {
								animation-delay: 0.1s;
							}

							&:nth-child(13) {
								animation-delay: 0.2s;
							}

							&:nth-child(14) {
								animation-delay: 0.3s;
							}

							&:nth-child(15) {
								animation-delay: 0.4s;
							}

							&:nth-child(16) {
								animation-delay: 0.5s;
							}

							&:nth-child(17) {
								animation-delay: 0.2s;
							}

							&:nth-child(18) {
								animation-delay: 0.3s;
							}

							&:nth-child(19) {
								animation-delay: 0.1s;
							}

							&:nth-child(20) {
								animation-delay: 0.4s;
							}

							&:nth-child(21) {
								animation-delay: 0.5s;
							}

							&:nth-child(22) {
								animation-delay: 0.6s;
							}

							&:nth-child(23) {
								animation-delay: 0.7s;
							}

							&:nth-child(24) {
								animation-delay: 0.8s;
							}

							&:nth-child(25) {
								animation-delay: 0.9s;
							}
						}
					}

					.quality {
						margin-left: 10px;
						font-size: 14px;
						color: #000;
						font-weight: 500;
					}
				}
			}
		}
	}

	// 查看全部按钮样式
	.view-all-button-container {
		display: flex;
		justify-content: center;
		margin-top: 45px;
		gap: 20px; /* 添加按钮之间的间距 */
		margin-bottom: 45px; /* 保持原有的下边距 */

		.view-all-button {
			width: 220px;
			height: 48px;
			background-color: #0AAF60;
			color: #FFFFFF;
			border: none;
			font-size: 16px;
			font-weight: 500;
			cursor: pointer;
			transition: background-color 0.3s ease;

			&:hover {
				background-color: darken(#0AAF60, 5%);
			}

			&:focus {
				outline: none;
			}
		}

		.view-price-button {
			width: 220px;
			height: 48px;
			background-color: #F9F9F9;
			border: 1px solid #0AAF60;
			color: #0AAF60;
			font-size: 16px;
			font-weight: 500;
			cursor: pointer;
			transition: background-color 0.3s ease, color 0.3s ease;


			&:focus {
				outline: none;
			}
		}
	}

	.api-content {
		text-align: center;
		margin-top: 0;
		/* 移除与上方图片的间距 */

		.api-content-image {
			max-width: 100%;
			display: block;
			margin: 0 auto; // 添加水平居中
		}
	}

	.page-title {
		font-size: 24px;
		color: #333;
		margin-bottom: 20px;
		padding-bottom: 10px;
		border-bottom: 1px solid #eaeaea;
	}

	.content-wrapper {
		background-color: #fff;
		border-radius: 8px;
		padding: 20px;
		margin: 20px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
		min-height: 500px;
	}

	.placeholder-text {
		color: #999;
		font-size: 16px;
		text-align: center;
		padding: 100px 0;
	}

	// 新增大尺寸内容区域
	.large-content-section {
		height: 747px;
		margin: 54px auto 0;

		.section-title {
			font-size: 42px;
			color: #091221;
			text-align: center;
			font-weight: 600;
			padding-top: 40px;
		}

		// 套餐卡片区域
		.packages-container {
			width: 1630px;
			height: 244px;
			background-color: #FFFFFF;
			margin: 38px auto 0;
			display: flex;
			justify-content: space-between;

			.package-card {
				width: 408px;
				height: 244px;
				background-color: #FFFFFF;
				box-sizing: border-box;

				.card-header {
					height: 88px;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					position: relative;

					.package-title {
						font-size: 18px;
						font-weight: 500;
						color: #091221;
					}

					.divider-line {
						width: 278px;
						height: 1px;
						background-color: #EAEAEA;
						position: absolute;
						bottom: 0;
					}
				}

				.card-content {
					height: 155px;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					.content-title {
						font-size: 24px;
						font-weight: 500;
						color: #0AAF60;
						margin-bottom: 20px;
					}

					.content-desc {
						font-size: 16px;
						color: #333C48;
					}
				}
			}
		}
		
		// 备注说明区域
		.notes-container {
			width: 1626px;
			height: 121px;
			margin: 0 auto;
			padding-left:126px;
			padding-right: 250px; /* 为右侧按钮预留空间 */
			display: flex;
			flex-direction: column;
			// justify-content: space-between;
			box-sizing: border-box;
			background-color: #FFFFFF;
			background-image: url('@/assets/img/beijingse2.jpg');
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			position: relative; /* 添加相对定位 */

			.note-item {
				font-size: 14px;
				color: #666666;
				line-height: 1.5;
				
				&:first-child {
					margin-top: 34px;
					margin-bottom: 11px;
				}
				
				&:last-child {
					margin-bottom: 28px;
				}
			}

			// 新增右侧按钮
			.notes-right-button {
				position: absolute;
				right: 84px;
				top: 50%;
				transform: translateY(-50%);
				width: 146px;
				height: 48px;
				background-color: #0AAF60;
				color: #FFFFFF;
				border: none;
				font-size: 16px;
				font-weight: 500;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0;
				border-radius: 4px;
			}
		}
		
		// 添加的新说明区域
		.additional-notes-container {
			width: 1626px;
			margin: 55px auto 0;
			box-sizing: border-box;
			
			.additional-note-item {
				font-size: 16px;
				color: #666D76;
				line-height: 1.5;
				text-align: left;
				
				&:first-child {
					margin-bottom: 11px;
				}
			}
		}
		
		// 新添加的白色背景区域
		.white-background-container {
			width: 1626px;
			height: 159px;
			background-color: #FFFFFF;
			margin: 19px auto 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			
			.copy-btn {
				width: 112px;
				height: 28px;
				border: 1px solid #0AAF60;
				background: #FFFFFF;
				color: #0AAF60;
				font-size: 16px;
				border-radius: 4px;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0;
			}

			.contact-left {
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				align-items: flex-start;
				width: 388px;
				margin-left: 42px;
			}

			.contact-title {
				font-size: 32px;
				color: #091221;
				font-weight: bold;
				margin-bottom: 16px;
			}

			.contact-desc-bg {
				width: 388px;
				height: 30px;
				background: #0AAF601F;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 4px;
			}

			.contact-desc {
				font-size: 14px;
				color: #666D76;
				text-align: center;
			}

			.contact-center {
				width: 503px;
				height: 88px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				background: transparent;
				margin: 0 auto;
				position: relative;
				border-radius: 18px;
				box-shadow: 0px 1px 6px 0px #0912210A;

				.center-left {
					height: 100%;
					display: flex;
					align-items: center;
					margin-left: 26px;
					font-size: 20px;
					color: #0AAF60;
					font-weight: 500;
				}

				.center-right {
					height: 100%;
					display: flex;
					align-items: center;
					margin-right: 21px;
				}
			}

			.contact-right {
				width: 503px;
				height: 88px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				background: transparent;
				position: relative;
				border-radius: 18px;
				box-shadow: 0px 1px 6px 0px #0912210A;
				margin-right: 78px;

				.right-left {
					height: 100%;
					display: flex;
					align-items: center;
					margin-left: 26px;
					font-size: 20px;
					color: #0AAF60;
					font-weight: 500;
				}

				.right-right {
					height: 100%;
					display: flex;
					align-items: center;
					margin-right: 21px;
				}
			}
		}
	}

	// 开发文档区域
	.doc-section {
		width: 1920px;
		height: 550px;
		background-color: #FFFFFF;
		margin: 0 auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.doc-title {
			font-size: 42px;
			color: #091221;
			font-weight: 600;
			margin-top: 118px;
		}
		
		.image-container {
			position: relative;
			width: 1630px;
			height: 233px;
			margin-top: 49px;
		}
		
		.doc-image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		
		// 覆盖在图片上的按钮区域
		.doc-button {
			position: absolute;
			width: 215px; // 按钮宽度，根据实际情况调整
			height: 40px; // 按钮高度，根据实际情况调整
			left: 50%; // 居中定位
			top: 64%; // 垂直居中
			transform: translate(-50%, 0); // 调整位置
			background-color: transparent; // 透明背景
			display: flex;
			align-items: center;
			justify-content: center;
			color: transparent; // 文字透明，使用图片上的文字
			cursor: pointer; // 显示小手指针
			border-radius: 4px; // 可以添加圆角使交互区域更明确
			
			&:hover {
				background-color: rgba(10, 175, 96, 0.1); // 鼠标悬停时添加轻微背景色
			}
		}
	}
	
	// 添加新图片区域
	.additional-doc-section {
		width: 1635px;
		height: 264px;
		background-color: #0AAF60;
		margin: 53px auto;
		position: relative;
		
		.additional-doc-image {
			width: 1920px;
			height: 494px;
			object-fit: cover;
		}

		.service-text {
			position: absolute;
			top: 44px;
			left: 83px;
			font-size: 40px;
			color: #FFFFFF;
			font-weight: 400;
			font-family: 'Inter', sans-serif;
		}

		.stats-container {
			position: absolute;
			top: 124px;
			left: 83px;
			display: flex;
			gap: 80px;
		}

		.stat-item {
			color: #FFFFFF;
			font-family: 'Inter', sans-serif;
			text-align: center;
			display: flex;
			flex-direction: column;
			gap: 0;
		}

		.stat-title {
			font-size: 20px;
			font-weight: 400;
		}

		.stat-value {
			display: flex;
			align-items: baseline;
			justify-content: center;

			.arrow {
				font-size: 18px;
				font-weight: 500;
				margin-right: 5px;
			}

			.number {
				font-size: 31.74px;
				font-weight: 500;
			}

			.unit {
				font-size: 12px;
				font-weight: 400;
				margin-left: 5px;
			}
		}

		.disclaimer-text {
			position: absolute;
			top: 210px;
			left: 83px;
			font-size: 12px;
			color: #F3F7FD !important; /* 强制文字颜色为#F3F7FD，用于诊断 */
			font-family: 'Inter', sans-serif;
			font-weight: 400;
		}

		.qr-code-section {
			position: absolute;
			right: 100px;
			top: 50%;
			transform: translateY(-50%);
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10px;
		}

		.qr-code-image {
			width: 120px;
			height: 120px;
		}

		.qr-code-caption {
			font-family: 'Inter', sans-serif;
			font-size: 11px;
			color: #F3F7FD;
			text-align: center;
			font-weight: 400;

			p {
				margin: 0;
			}
		}
	}
}

@keyframes sound {
	0% {
		transform: scaleY(1.3);
	}

	50% {
		transform: scaleY(0.4);
	}

	100% {
		transform: scaleY(1.3);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
</style>