
<template>
    <div class="extraction">
        <div class="cont">
            <div class="extraction_head">
                <h2>智能加字幕</h2>
            </div>
            <template v-if="!activeIndex">
                <!-- 上传格式 -->
                <div class="sharing">
                    <div class="sharing_caption">
                        <span>*</span>
                        <span>上传格式：</span>
                    </div>
                    <div class="card">
                        <div
                            @click="cardClick(index, card.type)"
                            :class="{
                                cardStyleOne: cardIndex == index,
                                cardStyleTwo: cardIndex != index,
                            }"
                            v-for="(card, index) in cardList"
                            :key="index"
                        >
                            <div class="container">
                                <div class="inner-oval"></div>
                            </div>
                            <span class="text">{{ card.name }}</span>
                        </div>
                    </div>
                </div>

                <!-- 视频文件 -->
                <div class="Video">
                    <div class="video_link" v-if="!typeIndex">
                        <div class="video_link_one">
                            <div class="aaa">
                                <div class="video_hade">
                                    <span>* </span>
                                    <span>{{ videoName }}</span>
                                </div>
                                <!-- 这是视频文件 -->
                                <div class="child" v-show="!typeIndex">
                                    <AudioUpload
                                        accept-type="video"
                                        action-url="/custom-upload-url"
                                        accept-types="audio/mpeg,audio/wav"
                                        @upload-success="handleSuccess"
                                        @upload-error="handleError"
                                        type="captions"
                                        :id="1"
                                    />
                                </div>
                            </div>
                            <!-- 制作按钮 -->
                            <div class="btnMake">
                                <el-button
                                    @click="videoClick('文件')"
                                    :disabled="videoIsParsing"
                                    :loading="videoIsParsing"
                                >
                                    {{ isParsing ? "制作中..." : "开始制作" }}
                                </el-button>
                                <transition>
                                    <p
                                        v-show="videoIsParsing"
                                        class="parsing-tip"
                                    >
                                        解析需要几秒钟，请耐心等待
                                    </p>
                                </transition>
                            </div>
                        </div>
                        <!-- 生成结果 -->
                        <div class="consists">
                            <ConsistsChild
                                :videoInfo="videoInfo"
                                @handleCopy="handleCopyTitle"
                                @download="handleDownload"
                            />
                        </div>
                    </div>

                    <!-- 我的空间 -->
                    <div v-show="isShow && !activeIndex" class="room">
                        <AudioUpload
                            ref="childRef"
                            accept-type="video"
                            action-url="/custom-upload-url"
                            accept-types="audio/mpeg,audio/wav"
                            @upload-success="handleSuccess"
                            @upload-error="handleError"
                            :before-trigger="showConfirmDialog"
                            type="captions"
                            :id="2"
                        />
                        <!-- 制作按钮 -->
                        <div class="btnMake">
                            <el-button
                                @click="videoClick('空间')"
                                :disabled="spaceIsParsing"
                                :loading="spaceIsParsing"
                            >
                                {{ spaceIsParsing ? "制作中..." : "开始制作" }}
                            </el-button>
                            <transition>
                                <p v-show="spaceIsParsing" class="parsing-tip">
                                    解析需要几秒钟，请耐心等待
                                </p>
                            </transition>
                        </div>
                        <!-- 生成结果 -->
                        <div class="consists">
                            <ConsistsChild
                                :videoInfo="videoInfo"
                                @handleCopy="handleCopyTitle"
                                @download="handleDownload"
                            />
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- 底部推荐 -->
        <FooterChild />
        <!-- 我的空间 -->
        <AlbumDialog
            v-model="showDialog"
            @cancel="handleCancel"
            @confirm="handleConfirm"
        />
    </div>
</template>

<!-- 在 Tabs.vue 中添加颜色切换逻辑 -->
<script setup>
import { ref, computed } from "vue";
import AudioUpload from "../../../components/AudioUpload/index.vue";
import AlbumDialog from "../../../components/AlbumDialog/index.vue";
import ConsistsChild from "../../../components/ConsistsChild/index.vue";
import FooterChild from "../../../components/FooterChild/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";
const activeIndex = ref(0);
const cardIndex = ref(0);
const typeIndex = ref(0);
const languageIndex = ref(0);
const showDialog = ref(false);
// const isParsing = ref(false);
const isShow = ref(false);
const childRef = ref(null); // 子组件引用

// 视频格式动态展示
const videoName = ref("视频文件：");

// 上传格式
const cardList = ref([
    {
        name: "视频文件",
        type: 2,
    },
    {
        name: "我的空间",
        type: 3,
    },
]);

// 上传格式
const cardClick = (index, type) => {
    cardIndex.value = index;
    typeIndex.value = index;
    console.log(cardIndex.value, typeIndex.value, "1111");
    if (type == 2) {
        videoName.value = "视频文件：";
        isShow.value = false;
        if (childRef.value?.currentFile) {
            childRef.value.currentFile.value = false;
        }
    }
    if (type == 3) {
        showDialog.value = true;
    }
};

const handleSuccess = (response) => {
    console.log("上传成功", response);
};

const handleError = (error) => {
    console.error("上传失败", error);
};

// 确定按钮操作
const handleConfirm = (selectedAlbum) => {
    if (Object.keys(selectedAlbum).length > 0) {
        isShow.value = true;
        childRef.value.currentFile = selectedAlbum;
    } else {
        isShow.value = false;
    }
};

// 取消按钮操作

const handleCancel = (selectedCancel) => {
    isShow.value = true;
};

// 在我的空间中，组件需要阻断默认的上传，需要打开dialog上传
const showConfirmDialog = () => {
    showDialog.value = true;
};

// 字幕字数方法以及需要的字段
const numbers = ref([8, 10, 12]);
const selectedIndex = ref(0);
const showCustomInput = ref(false);
const customValue = ref("");

const selectNumber = (index, num) => {
    selectedIndex.value = index;
    showCustomInput.value = false;
    console.log(num);
};

const handleCustomClick = () => {
    showCustomInput.value = true;
    selectedIndex.value = -1;
};

const confirmCustom = () => {
    if (customValue.value) {
        numbers.value.push(customValue.value);
        selectedIndex.value = numbers.value.length - 1;
    }
    showCustomInput.value = false;
    customValue.value = "";
};

// 新增输入处理逻辑
const handleNumberInput = (value) => {
    // 清除非数字字符（包括科学计数法的'e'）
    customValue.value = value.replace(/[^0-9]/g, "");

    // 限制最大输入长度
    if (customValue.value.length > 4) {
        customValue.value = customValue.value.slice(0, 4);
    }
    console.log(customValue.value, 11);
};

// 生成结果的方法

// 添加视频信息响应式对象
const videoInfo = ref({
    name: "已成功为视频加上字幕",
    type: "字幕",
    title: "222",
    duration: "",
    size: "",
    coverUrl: "",
});
// 新增下载处理函数
const handleDownload = async (Obj, currentUrl) => {
    console.log(Obj);

    try {
        if (!Obj) {
            throw new Error("无效的下载参数");
        }

        let blob;
        if (Obj.type === "字幕") {
            const textContent = Obj.extractedText.value.join("\n");
            if (!textContent.trim()) {
                ElMessage.warning("没有可下载的内容");
                return;
            }
            blob = new Blob([textContent], {
                type: "text/plain;charset=utf-8",
            });
        } else {
            // 添加请求取消逻辑
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);

            const response = await fetch("/api/download", {
                method: "POST",
                body: JSON.stringify({
                    type: Obj.type,
                    url: currentUrl,
                }),
                signal: controller.signal,
            });
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`下载失败: ${response.statusText}`);
            }
            blob = await response.blob();
        }

        // 统一处理下载逻辑
        const fileExtension =
            Obj.type === "字幕" ? "txt" : Obj.type === "video" ? "mp4" : "jpg";
        const sanitizedTitle =
            videoInfo.value.title?.replace(/[<>:"/\\|?*]/g, "") || "未命名文件";
        const filename = `${sanitizedTitle}_${Date.now()}.${fileExtension}`;

        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        link.style.display = "none";

        document.body.appendChild(link);
        link.click();

        // 统一资源清理
        URL.revokeObjectURL(url);
        document.body.removeChild(link);

        ElMessage.success("下载已开始");
    } catch (error) {
        console.error("下载失败:", error);
        const errorMessage =
            error.name === "AbortError"
                ? "下载超时，请检查网络连接"
                : error.message.startsWith("下载失败:")
                ? error.message
                : "文件下载失败，请重试";

        ElMessage.error(errorMessage);
    }
};

// 修改解析函数
const handleParseOne = async () => {
    try {
        isParsing.value = true;
        // 模拟接口请求
        const mockResponse = await new Promise((resolve) =>
            setTimeout(() => {
                resolve({
                    data: {
                        type: "字幕",
                        title:
                            "动态视频标题_" +
                            Math.random().toString(36).slice(2, 7),
                        duration: "03:25",
                        size: "86.5MB",
                        coverUrl:
                            "https://pic.52112.com/2020/04/13/JPG-200413_328/gCaPae4zjp_small.jpg",
                    },
                });
            }, 1000)
        );

        // 更新视频信息
        videoInfo.value = {
            title: mockResponse.data.title,
            duration: mockResponse.data.duration,
            size: mockResponse.data.size,
            coverUrl: mockResponse.data.coverUrl,
        };
    } catch (error) {
        ElMessage.error("解析失败");
    } finally {
        isParsing.value = false;
    }
};
// handleParseOne();

// 复制标题
const handleCopyTitle = async (videoInfo) => {
    if (!videoInfo.title) {
        ElMessage.warning("暂无可复制的标题");
        return;
    }

    try {
        await navigator.clipboard.writeText(videoInfo.title);
        ElMessage.success("标题已复制到剪贴板");
    } catch (err) {
        console.error("复制失败:", err);
        ElMessage.error("复制失败，请手动复制");
    }
};

// 去除按钮方法
// 这是视频文件的判断字段
const videoIsParsing = ref(false);
const videoClick = (type) => {
    console.log(111);
    if (type == "视频") {
        videoIsParsing.value = true;
    }
    if (type == "空间") {
        spaceIsParsing.value = true;
    }
    // ElMessage.success("制作成功");
    // // if (!videoUrl.value) return;
    // // isParsing.value = true;

    // setTimeout(() => {
    //     // isParsing.value = true;
    //     // showResult.value = true;
    //     // 模拟解析结果
    //     // videoSrc.value = "https://example.com/video.mp4";
    //     // videoCover.value = "https://example.com/cover.jpg";
    // }, 2000);
};
</script>

<style lang="scss" scoped>
.extraction {
    .cont {
        margin-left: 20px;
        margin-bottom: 100px;
    }
    .extraction_head {
        display: flex;
        align-items: center;

        .tabs {
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            span {
                margin-left: 20px;
            }
            .tabsActive {
                color: #18ad25;
            }
        }

        .vertical-line {
            width: 3px; // 宽度调节点（默认3px→5px）
            height: 25px; // 高度调节点（默认50px→撑满全屏）
            background: linear-gradient(
                to bottom,
                #000 100%,
                // 顶部实色
                transparent // 底部渐隐（可选效果）
            );
            margin-left: 20px; // 水平居中
        }
    }
    .sharing {
        margin-top: 10px;
        display: flex;
        align-items: center;
        .card {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-left: 4px;
            .cardStyleOne {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 126px;
                height: 37px;
                text-align: center;
                line-height: 40px;
                // margin-left: 20px;
                border-radius: 5px;
                color: #18ad25;
                background-color: #e7f7ea;
                border: 1px solid #e7f7ea;
                .text {
                    margin-left: 5px;
                }
                .container {
                    width: 20px;
                    height: 20px;
                    background: #4caf50; /* 绿色背景 */
                    border-radius: 50%; /* 圆形 */
                    position: relative;
                }

                .inner-oval {
                    width: 10px;
                    height: 10px;
                    background: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    border-radius: 50%; /* 椭圆效果 */
                }
                &:nth-child(2) {
                    margin-left: 20px;
                }
            }
            .cardStyleTwo {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 126px;
                height: 37px;
                border-radius: 5px;
                text-align: center;
                line-height: 40px;
                // margin-left: 20px;
                border: 1px solid #eeeef0;
                .text {
                    margin-left: 5px;
                }
                .container {
                    width: 20px;
                    height: 20px;
                    background: white; /* 绿色背景 */
                    border-radius: 50%; /* 圆形 */
                    position: relative;
                }
                .inner-oval {
                    width: 20px;
                    height: 20px;
                    border: 1px solid #eeeef0;
                    background: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    border-radius: 50%; /* 椭圆效果 */
                }
                &:nth-child(2) {
                    margin-left: 20px;
                }
            }
        }
    }
    // 视频设置
    .Video {
        display: flex;
        margin-top: 30px;
        .video_link {
            width: 100%;
            .video_link_one {
                display: flex;
                flex-direction: column;
                .aaa{
                    display: flex;
                }
            }
            .consists {
                // width: 100%;
                width: 70%;
            }
        }
        .child {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 500px;
            :deep(.el-input__wrapper) {
                border: none !important;
                box-shadow: none !important;

                // 去除hover状态边框
                &:hover {
                    box-shadow: none !important;
                }

                // 去除聚焦状态边框
                &.is-focus {
                    box-shadow: none !important;
                }
            }
            :deep(.image-container) {
                height: 150px;
                border: 1px solid #eeeef0;
            }
            :deep(.action-buttons) {
                bottom: -107px;
            }
            .btn {
                width: 126px;
                height: 37px;
                cursor: pointer;
                margin-top: 20px;
                color: #fff;
                text-align: center;
                border-radius: 5px;
                line-height: 30px;
                background-color: #18ad25;
            }
            // 生成结果
            // .result {
            //     margin-top: 40px;
            //     .result_count {
            //         display: flex;
            //         align-items: center;
            //         .checkmark {
            //             display: flex;
            //             align-items: center;
            //             .checkmark-bold {
            //                 background: #4caf50;
            //                 width: 23px;
            //                 height: 23px;
            //                 border-radius: 50%;
            //                 position: relative;
            //                 left: 20px;
            //             }

            //             .checkmark-bold::after {
            //                 content: "";
            //                 position: absolute;
            //                 left: 22%; /* 调整定位补偿线宽变化 */
            //                 top: 25%;
            //                 width: 14px;
            //                 height: 9px;
            //                 border: 4px solid #fff; /* 线宽从4px增加到6px */
            //                 border-top: none;
            //                 border-right: none;
            //                 transform: rotate(-45deg);
            //                 box-sizing: border-box; /* 保持尺寸稳定 */
            //             }
            //             .text {
            //                 margin: 2px 0 0 25px;
            //                 font-size: 16px;
            //                 color: #4caf50;
            //             }
            //         }
            //     }
            //     .TextOnOffToggle {
            //         color: #18ad25;
            //         margin: 8px 0 0 100px;
            //     }
            //     .video-container {
            //         margin: 20px 0 0 100px;
            //         width: 70%;
            //         background: #fff;

            //         .video_count {
            //             display: flex;
            //             align-items: center;
            //             justify-content: space-between;
            //             .video_one {
            //                 width: 50%;
            //                 display: flex;
            //                 flex-direction: column;
            //                 .video-header {
            //                     background: #f8f7fc;
            //                     padding: 15px 12px;
            //                     border-radius: 5px;

            //                     .left-section {
            //                         display: flex;
            //                         align-items: center;
            //                         gap: 8px;

            //                         .video-icon {
            //                             font-size: 24px;
            //                             color: #409eff;
            //                             width: 32px;
            //                             height: 32px;
            //                         }

            //                         .video-details {
            //                             .title {
            //                                 font-size: 14px;
            //                                 line-height: 1.5;
            //                                 font-weight: 500;
            //                             }

            //                             .meta {
            //                                 font-size: 12px;
            //                                 color: #909399;
            //                                 span + span {
            //                                     margin-left: 8px;
            //                                 }
            //                             }
            //                         }
            //                     }

            //                     .right-actions {
            //                         display: flex;
            //                         align-items: center;
            //                         justify-content: end;
            //                         // gap: 6px;
            //                         margin-top: 5px;

            //                         .btn {
            //                             padding: 6px;
            //                             border: 1px solid #ececef;
            //                             color: #606266;
            //                             &:hover {
            //                                 background: #f0f2f5;
            //                             }
            //                         }

            //                         .el-button--primary {
            //                             background: #18ad25;
            //                             border: 1px solid #18ad25;
            //                             color: #fff;
            //                             border-radius: 4px;
            //                             padding: 6px 12px;
            //                         }
            //                         .preview,
            //                         .copy {
            //                             width: 80px;
            //                             height: 30px;
            //                         }
            //                         .download-btn {
            //                             width: 80px;
            //                             height: 30px;
            //                             color: #18ad25;
            //                             background: #d9efda;
            //                             border-color: #d9efda;
            //                             border-radius: 4px;
            //                             padding: 6px 12px;
            //                         }
            //                     }
            //                 }

            //                 .video-content {
            //                     width: 100%;
            //                     height: 235px;
            //                     margin-top: 10px;
            //                     object-fit: cover;
            //                     border-radius: 0 0 4px 4px;
            //                 }
            //             }
            //             .video_two {
            //                 width: 50%;
            //                 margin-left: 40px;
            //                 display: flex;
            //                 flex-direction: column;
            //                 .video-header {
            //                     background: #f8f7fc;
            //                     padding: 17px 12px;
            //                     border-radius: 5px;

            //                     .left-section {
            //                         display: flex;
            //                         align-items: center;
            //                         gap: 8px;

            //                         .video-icon {
            //                             font-size: 24px;
            //                             color: #409eff;
            //                             width: 32px;
            //                             height: 32px;
            //                         }

            //                         .cover-text {
            //                             font-size: 14px;
            //                             color: #606266;
            //                             font-weight: 500;
            //                         }
            //                     }

            //                     .right-actions {
            //                         display: flex;
            //                         align-items: center;
            //                         justify-content: end;
            //                         margin-top: 5px;
            //                         .btn {
            //                             padding: 6px;
            //                             color: #606266;
            //                             border: 1px solid #ececef;
            //                             color: #606266;
            //                             &:hover {
            //                                 background: #f0f2f5;
            //                             }
            //                         }
            //                         .preview {
            //                             width: 80px;
            //                             height: 30px;
            //                         }
            //                         .download-btn {
            //                             width: 80px;
            //                             height: 30px;
            //                             font-size: 14px;
            //                             color: #18ad25;
            //                             background: #d9efda;
            //                             border-color: #d9efda;
            //                             border-radius: 4px;
            //                             padding: 6px 12px;
            //                         }
            //                     }
            //                 }

            //                 .img-content {
            //                     width: 100%;
            //                     height: 235px;
            //                     margin-top: 10px;
            //                     object-fit: cover;
            //                     border-radius: 0 0 4px 4px;
            //                 }
            //             }
            //         }
            //     }
            // }
        }
    }

    // 制作按钮样式
    .btnMake {
        margin-top: 30px;
        .el-button {
            width: 126px;
            height: 40px;
            color: #fff;
            border: 1px solid #19ad25;
            background-color: #19ad25;
        }
    }
    .room{
        width: 100%;
        .consists {
                // width: 100%;
                width: 70%;
            }
    }
}
</style>