<template>
	<div class="clone-service-page">
		<!-- 顶部导航 -->
		<Headbar />

		<!-- 占位，避免内容被固定头部遮挡 -->
		<div class="nav-placeholder"></div>

		<!-- 顶部宣传语 -->
		<div class="hero-title">克隆AI声音，随时随地配音</div>
		<div class="description">*说明：音色克隆功能仅配音会员可购买，且克隆后的音色使用时需要消耗字符，请确保已开通VIP或者SVIP会员。</div>
		<!-- 主体内容区域 -->
		<div class="clone-service-container">
			<div
				class="feature-card"
				v-for="(card, idx) in cards"
				:key="idx"
			>
				<div class="card-header" :style="{
					background: `url(${getHeaderImage(idx)}), ${getHeaderBackgroundColor(idx)}`,
					backgroundSize: 'cover, cover',
					backgroundPosition: 'center, center',
					backgroundRepeat: 'no-repeat, no-repeat'
				}">
					<div class="header-title">{{ getCardTitle(idx) }}</div>
					<!-- 只在快速克隆卡片（第一个卡片）显示piaochuang图片 -->
					<img
						v-if="idx === 0"
						:src="piaochuang"
						alt="首克送一个月VIP"
						class="corner-badge"
					/>
				</div>
				<div class="card-content">
					<!-- 所有卡片都显示描述文字 -->
					<div class="card-features">
						<div class="feature-item">
							<span class="bullet-point">•</span>
							<span class="feature-text">{{ getFeatureText1(idx) }}</span>
						</div>
						<div class="feature-item">
							<span class="bullet-point">•</span>
							<span class="feature-text" v-html="getFeatureText2(idx)"></span>
						</div>
					</div>
					<!-- 所有卡片都显示两个并排的div -->
					<div class="feature-boxes">
						<div class="feature-box">
							<div class="user-content">
								<img :src="getUserAvatar(idx, 0)" alt="用户头像" class="avatar" />
								<div class="user-name">{{ getUserName(idx, 0) }}</div>
							</div>
							<div class="user-intro">{{ getUserIntro(idx, 0) }}</div>
							<!-- 播放按钮 -->
							<div class="play-buttons">
								<div class="play-button-group">
									<img
										:src="getPlayButtonImage(`${idx}_box1`, 'button1')"
										alt="播放按钮1"
										class="play-button"
										@click="togglePlayState(`${idx}_box1`, 'button1')"
									/>
									<span class="button-label">本人声音</span>
								</div>
								<div class="play-button-group">
									<img
										:src="getPlayButtonImage(`${idx}_box1`, 'button2')"
										alt="播放按钮2"
										class="play-button"
										@click="togglePlayState(`${idx}_box1`, 'button2')"
									/>
									<span class="button-label">克隆声音</span>
								</div>
							</div>
						</div>
						<div class="feature-box">
							<div class="user-content">
								<img :src="getUserAvatar(idx, 1)" alt="用户头像" class="avatar" />
								<div class="user-name">{{ getUserName(idx, 1) }}</div>
							</div>
							<div class="user-intro">{{ getUserIntro(idx, 1) }}</div>
							<!-- 播放按钮 -->
							<div class="play-buttons">
								<div class="play-button-group">
									<img
										:src="getPlayButtonImage(`${idx}_box2`, 'button1')"
										alt="播放按钮1"
										class="play-button"
										@click="togglePlayState(`${idx}_box2`, 'button1')"
									/>
									<span class="button-label">本人声音</span>
								</div>
								<div class="play-button-group">
									<img
										:src="getPlayButtonImage(`${idx}_box2`, 'button2')"
										alt="播放按钮2"
										class="play-button"
										@click="togglePlayState(`${idx}_box2`, 'button2')"
									/>
									<span class="button-label">克隆声音</span>
								</div>
							</div>
						</div>
					</div>
					<!-- 所有卡片都显示立即复刻按钮 -->
					<button
						:class="['clone-action-button', { 'premium-button': idx === 1, 'sft-button': idx === 2 }]"
						@click="handleCardClick(card.action)"
					>
						{{ getButtonText(idx) }}
					</button>
				</div>
			</div>
		</div>

		<!-- 底部 -->
		<Footer />

		<!-- 联系客服弹窗 -->
		<Contact ref="contactRef" />
	</div>
</template>

<script setup>
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import Footer from '@/views/modules/H4Home/components/footer.vue'
import { useRouter } from 'vue-router'
import { ref, onMounted, onUnmounted } from 'vue'
import Contact from '@/views/modules/realVoice/components/index/contact.vue'
import imageone from '@/assets/img/imageone.png'
import imagekelong1 from '@/assets/img/imagekelong1.png'
import imgkelong3 from '@/assets/img/imgkelong3.png'
import piaochuang from '@/assets/img/piaochuang.png'
import touxiang1 from '@/assets/img/touxiang1.png'
import touxiang2 from '@/assets/img/touxiang2.png'
import kelongimg1 from '@/assets/img/kelopngimg1.png'
import kelongimg2 from '@/assets/img/kelopngimg2.png'
import kelongimg3 from '@/assets/img/kelopngimg3.png'
import kelongimg4 from '@/assets/img/kelopngimg4.png'

const router = useRouter()
const contactRef = ref(null)

// 播放按钮状态管理（支持所有卡片）
const playStates = ref({
	// 快速克隆卡片
	'0_box1': { button1: false, button2: false },
	'0_box2': { button1: false, button2: false },
	// 精品克隆卡片
	'1_box1': { button1: false, button2: false },
	'1_box2': { button1: false, button2: false },
	// SFT克隆卡片
	'2_box1': { button1: false, button2: false },
	'2_box2': { button1: false, button2: false }
})

// 音频对象管理（支持所有卡片）
const audioInstances = ref({
	'0_box1': { button1: null, button2: null },
	'0_box2': { button1: null, button2: null },
	'1_box1': { button1: null, button2: null },
	'1_box2': { button1: null, button2: null },
	'2_box1': { button1: null, button2: null },
	'2_box2': { button1: null, button2: null }
})

// 音频URL配置
const audioUrls = {
	'0_box1': {
		button1: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E7%8E%8B%E4%B9%9F%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 王也本人声音
		button2: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E7%8E%8B%E4%B9%9F%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 王也复刻声音
	},
	'0_box2': {
		button1: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E9%A1%BE%E7%9F%A5%E5%A4%8F%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 顾知夏本人声音
		button2: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E9%A1%BE%E7%9F%A5%E5%A4%8F%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 顾知夏复刻声音
	},
	'1_box1': {
		button1: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E8%8B%8F%E7%A6%BE%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 苏禾本人声音
		button2: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E8%8B%8F%E7%A6%BE%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 苏禾复刻声音
	},
	'1_box2': {
		button1: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%98%8E%E8%BF%9C%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 明远本人声音
		button2: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%98%8E%E8%BF%9C%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 明远复刻声音
	},
	'2_box1': {
		button1: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%9D%8E%E4%BF%AE%E8%BF%9C%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 李修远本人声音
		button2: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%9D%8E%E4%BF%AE%E8%BF%9C%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 李修远复刻声音
	},
	'2_box2': {
		button1: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%B2%88%E7%9F%A5%E9%81%A5%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 沈知遥本人声音
		button2: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%B2%88%E7%9F%A5%E9%81%A5%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 沈知遥复刻声音
	}
}

// 卡片数据数组
const cards = [
	{ action: 'quick_clone' },
	{ action: 'premium_clone' },
	{ action: 'sft_clone' }
]

// 获取卡片标题
const getCardTitle = (index) => {
	const titles = ['快速克隆', '精品克隆', 'SFT克隆']
	return titles[index] || '声音克隆'
}

// 获取卡片描述
const getCardDescription = (index) => {
	const descriptions = [
		'快速生成AI声音模型，适合日常配音需求',
		'高质量声音克隆，专业配音效果',
		'深度学习训练，极致声音还原'
	]
	return descriptions[index] || '专业声音克隆服务'
}

// 获取按钮文字
const getCardButtonText = (index) => {
	const buttonTexts = ['立即体验', '联系客服', '联系客服']
	return buttonTexts[index] || '了解更多'
}

// 获取第一段特色描述文字
const getFeatureText1 = (index) => {
	const texts = [
		'仅需录制5-30秒内语料',
		'棚录30分钟以上语料，精细语音复制',
		'棚录2-3小时语料，超精细监督微调语音复制'
	]
	return texts[index] || '专业声音克隆服务'
}

// 获取第二段特色描述文字
const getFeatureText2 = (index) => {
	const texts = [
		'开放环境中录制秒级别录音即可极速拥有专属定制音<br>色，可满足基本的配音需求，支持32个小语种。',
		'高品质声音复刻，95%音色还原，情感丰富，适用于各类<br>场景的有声创作及日常对话，支持32个小语种。',
		'99%超细节还原真实音色，情感细腻，可完美进行各类<br>场景的声音播报，支持32个小语种。'
	]
	return texts[index] || '为您提供高质量声音克隆体验'
}

// 获取用户名字
const getUserName = (cardIndex, boxIndex) => {
	const names = [
		// 快速克隆卡片的名字
		['王也', '顾知夏'],
		// 精品克隆卡片的名字
		['苏禾', '明远'],
		// SFT克隆卡片的名字
		['李修远', '沈知遥']
	]
	return names[cardIndex]?.[boxIndex] || '用户'
}

// 获取用户头像
const getUserAvatar = (cardIndex, boxIndex) => {
	const avatars = [
		// 快速克隆卡片的头像
		[
			'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E7%8E%8B%E4%B9%9F.png', // 王也
			'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E9%A1%BE%E7%9F%A5%E5%A4%8F.png' // 顾知夏
		],
		// 精品克隆卡片的头像
		[
			'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E8%8B%8F%E7%A6%BE.png', // 苏禾
			'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%98%8E%E8%BF%9C.png' // 明远
		],
		// SFT克隆卡片的头像
		[
			'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%9D%8E%E4%BF%AE%E8%BF%9C.png', // 李修远
			'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%B2%88%E7%9F%A5%E9%81%A5.png' // 沈知遥
		]
	]
	return avatars[cardIndex]?.[boxIndex] || touxiang1
}

// 获取用户简介
const getUserIntro = (cardIndex, boxIndex) => {
	const intros = [
		// 快速克隆卡片的简介
		['该案例适用于短视频创作及各类影视二创。', '该案例适用于第一人称视角的独白或走心旁白。'],
		// 精品克隆卡片的简介
		['此案例适用于日常对话，广告、走心旁白等。', '此案例适用于短视频创作、书籍介绍、国学介绍等。'],
		// SFT克隆卡片的简介
		['此案例适用于汇报片、宣传片等风格的商业配音录制。', '此案例适用于纪录片，宣传片等风格的商业配音录制。']
	]
	return intros[cardIndex]?.[boxIndex] || '用户简介'
}

// 获取头部背景图片
const getHeaderImage = (index) => {
	const images = [imageone, imagekelong1, imgkelong3] // 快速克隆、精品克隆、SFT克隆
	return images[index] || imageone
}

// 获取头部背景色（作为图片的后备）
const getHeaderBackgroundColor = (index) => {
	const colors = [
		'#4A90E2', // 快速克隆 - 蓝色
		'#8B5CF6', // 精品克隆 - 紫色
		'linear-gradient(135deg, #2D1B69 0%, #0D36B1 100%)'  // SFT克隆 - 渐变色（左侧更深的紫色，右侧深蓝色）
	]
	return colors[index] || '#4A90E2'
}

// 获取按钮文字
const getButtonText = (index) => {
	const texts = ['立即复刻', '立即咨询', '立即咨询'] // 快速克隆、精品克隆、SFT克隆
	return texts[index] || '立即复刻'
}

// 停止其他正在播放的音频
const stopOtherAudios = (currentBoxId, currentButtonId) => {
	Object.keys(audioInstances.value).forEach(boxId => {
		Object.keys(audioInstances.value[boxId]).forEach(buttonId => {
			if (boxId !== currentBoxId || buttonId !== currentButtonId) {
				const audio = audioInstances.value[boxId][buttonId]
				if (audio && !audio.paused) {
					audio.pause()
					audio.currentTime = 0
					playStates.value[boxId][buttonId] = false
				}
			}
		})
	})
}

// 播放按钮点击事件处理
const togglePlayState = (boxId, buttonId) => {
	const isCurrentlyPlaying = playStates.value[boxId][buttonId]

	if (isCurrentlyPlaying) {
		// 当前正在播放，停止播放
		const audio = audioInstances.value[boxId][buttonId]
		if (audio) {
			audio.pause()
			audio.currentTime = 0
		}
		playStates.value[boxId][buttonId] = false
		console.log(`停止播放: Box ${boxId} Button ${buttonId}`)
	} else {
		// 当前未播放，开始播放
		// 先停止其他正在播放的音频
		stopOtherAudios(boxId, buttonId)

		// 创建或获取音频对象
		let audio = audioInstances.value[boxId][buttonId]
		if (!audio) {
			audio = new Audio(audioUrls[boxId][buttonId])
			audioInstances.value[boxId][buttonId] = audio

			// 添加音频事件监听
			audio.addEventListener('ended', () => {
				playStates.value[boxId][buttonId] = false
				console.log(`播放结束: Box ${boxId} Button ${buttonId}`)
			})

			audio.addEventListener('error', (e) => {
				playStates.value[boxId][buttonId] = false
				console.error(`音频播放错误: Box ${boxId} Button ${buttonId}`, e)
				alert('音频播放失败，请检查网络连接')
			})
		}

		// 开始播放
		audio.currentTime = 0
		audio.play().then(() => {
			playStates.value[boxId][buttonId] = true
			console.log(`开始播放: Box ${boxId} Button ${buttonId}`)
		}).catch((error) => {
			console.error(`播放失败: Box ${boxId} Button ${buttonId}`, error)
			playStates.value[boxId][buttonId] = false
			alert('音频播放失败，请检查网络连接或音频文件')
		})
	}
}

// 获取播放按钮图片
const getPlayButtonImage = (boxId, buttonId) => {
	const isPlaying = playStates.value[boxId][buttonId]

	if (boxId === 'box1') {
		return buttonId === 'button1'
			? (isPlaying ? kelongimg3 : kelongimg1)
			: (isPlaying ? kelongimg4 : kelongimg2)
	} else {
		return buttonId === 'button1'
			? (isPlaying ? kelongimg3 : kelongimg1)
			: (isPlaying ? kelongimg4 : kelongimg2)
	}
}

// 卡片点击事件处理
const handleCardClick = (action) => {
	if (action === 'quick_clone') {
		router.push({ name: 'voiceClone' });
	} else if (action === 'premium_clone' || action === 'sft_clone') {
		if (contactRef.value) {
			contactRef.value.dialogVisible = true;
		}
	} else {
		console.log('Card action:', action);
		// 后续可以为其他卡片添加跳转逻辑
	}
}

// 全局点击事件处理函数
const handleGlobalClick = (event) => {
	// 检查点击的元素是否是播放按钮或其子元素
	const isPlayButton = event.target.closest('.play-button') ||
	                    event.target.closest('.play-img') ||
	                    event.target.classList.contains('play-button') ||
	                    event.target.classList.contains('play-img')

	// 如果不是播放按钮，则停止所有音频播放
	if (!isPlayButton) {
		// 停止所有正在播放的音频
		Object.keys(audioInstances.value).forEach(boxId => {
			Object.keys(audioInstances.value[boxId]).forEach(buttonId => {
				const audio = audioInstances.value[boxId][buttonId]
				if (audio && !audio.paused) {
					audio.pause()
					audio.currentTime = 0
					playStates.value[boxId][buttonId] = false
				}
			})
		})
	}
}

onMounted(() => {
	// 添加全局点击事件监听器，点击其他区域时停止播放
	document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
	// 清理全局事件监听器
	document.removeEventListener('click', handleGlobalClick)
})
</script>

<style lang="scss" scoped>
.clone-service-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background: #f8f9fa;
	overflow-y: auto;
}

/* 占位高度与头部一致（64px） */
.nav-placeholder {
	height: 64px;
	width: 100%;
}

.clone-service-container {
	flex: 1;
	padding: 0 20px 100px;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	gap: 40px;
	flex-wrap: nowrap; /* 确保水平排列，不换行 */
	max-width: 1200px;
	margin-left: auto;
	margin-right: auto;
}

/* 响应式：在小屏幕上允许换行 */
@media (max-width: 1200px) {
	.clone-service-container {
		flex-wrap: wrap;
	}
}

.clone-service-container h1 {
	margin: 0 0 16px 0;
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.clone-service-container p {
	font-size: 16px;
	color: #666;
}

.hero-title {
	margin-top: 150px; /* nav-placeholder 64px +31 = 95px 总距离顶部 */
	text-align: center;
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	font-size: 33px;
	font-weight: 600;
	color: #333;
}

.description{
	font-size: 16px;
	color: rgba(0, 0, 0, 0.45);
	margin: 68px 0 15px 300px;
}

.feature-card {
	width: 411.22px;
	height: 503px;
	border-radius: 12px;
	background: #ffffff;
	border: 1px solid #e5e7eb;
	box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	position: relative;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	flex-direction: column;
	overflow: visible; /* 允许飘窗图片超出卡片边界显示 */
}

.feature-card:hover {
	transform: translateY(-8px);
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-header {
	width: 100%; /* 改为100%以完全覆盖卡片宽度 */
	height: 110px; /* 调整为110px */
	display: flex;
	align-items: center; /* 垂直居中 */
	padding: 0 27px; /* 距离左侧27px */
	position: relative;
	overflow: visible; /* 允许子元素超出边界显示，实现飘窗效果 */
	border-radius: 12px 12px 0 0; /* 添加顶部圆角以匹配卡片样式 */
}

.header-title {
	font-size: 18px;
	font-weight: 600;
	color: #ffffff; /* 白色文字，在背景图上更清晰 */
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* 添加文字阴影增强可读性 */
}

.corner-badge {
	position: absolute;
	top: 15px; /* 距离顶部边缘15px */
	right: -6px; /* 向右超出卡片边界20px，实现飘窗效果 */
	max-width: 120px; /* 限制最大宽度，避免图片过大 */
	max-height: 80px; /* 限制最大高度 */
	object-fit: contain; /* 保持图片比例 */
	z-index: 10; /* 确保图片在最上层 */
}

.card-content {
	padding: 20px 15px 30px; /* 调整间距，适应新布局 */
	display: flex;
	flex-direction: column;
	justify-content: flex-start; /* 改为顶部对齐，避免布局混乱 */
	flex: 1; /* 占据剩余空间 */
	text-align: center;
}

.card-features {
	margin-bottom: 15px; /* 减少底部间距 */
	text-align: left; /* 描述文字左对齐 */
}

.feature-item {
	display: flex;
	align-items: flex-start; /* 顶部对齐，适应多行文字 */
	margin-bottom: 12px;
	line-height: 1.5;
}

.feature-item:last-child {
	margin-bottom: 0;
}

.bullet-point {
	color: #353D49; /* 修改为深灰色 */
	font-weight: bold;
	margin-right: 8px;
	font-size: 16px;
	flex-shrink: 0; /* 防止圆点被压缩 */
}

.feature-text {
	color: #374151; /* 深灰色文字 */
	font-size: 14px;
	font-weight: 400;
	line-height: 1.5;
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.feature-boxes {
	display: flex;
	justify-content: center; /* 居中对齐 */
	gap: 12px; /* 两个div之间的间距 */
	margin-top: 15px; /* 距离上方描述文字15px */
	margin-bottom: 0; /* 移除底部间距，因为这是最后一个元素 */
}

.feature-box {
	width: 167px; /* 固定宽度 */
	height: 190px; /* 固定高度 */
	border-radius: 12px; /* 圆角 */
	border: 1px solid #D3D3D2; /* 浅灰色边框 */
	background-color: #FFFFFF; /* 白色背景 */
	flex-shrink: 0; /* 防止被压缩 */
	padding: 12px 7px; /* 内边距：顶部12px，左右7px */
	box-sizing: border-box; /* 包含padding在宽高内 */
	display: flex;
	flex-direction: column;
}

.user-content {
	display: flex;
	align-items: center; /* 垂直居中对齐，使名字与头像中间位置对齐 */
	margin-bottom: 11px; /* 与简介文字的间距 */
}

.avatar {
	width: 47px; /* 头像宽度47px */
	height: 47px; /* 头像高度47px */
	border-radius: 50%; /* 圆形裁剪效果 */
	object-fit: cover; /* 保持图片比例并裁剪 */
	border: 2px solid #f0f0f0; /* 添加浅色边框 */
	margin-right: 12px; /* 与名字的间距 */
}

.user-name {
	font-size: 14px;
	font-weight: 500;
	color: #374151; /* 深灰色文字 */
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	line-height: 1.2;
}

.user-intro {
	font-size: 12px;
	font-weight: 400;
	color: #666666; /* 中等灰色 */
	line-height: 1.4; /* 确保多行文字有合适的间距 */
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	word-wrap: break-word; /* 确保长单词能够换行 */
	overflow-wrap: break-word; /* 现代浏览器的换行属性 */
	text-align: left !important; /* 强制左对齐，覆盖父容器的居中设置 */
	margin-bottom: 11px; /* 与播放按钮的间距 */
}

.play-buttons {
	display: flex;
	justify-content: center; /* 横向居中展示 */
	gap: 15px; /* 两个按钮组之间的间距 */
}

.play-button-group {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5px; /* 按钮和标签之间的间距 */
}

.play-button {
	width: 35px; /* 按钮宽度 */
	height: 35px; /* 按钮高度 */
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 50%; /* 圆形按钮 */
}

.play-button:hover {
	transform: scale(1.1); /* hover时稍微放大 */
	opacity: 0.8;
}

.button-label {
	font-size: 12px;
	font-weight: 400;
	color: #666666;
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	text-align: center;
	white-space: nowrap; /* 防止文字换行 */
}

.clone-action-button {
	width: 381px;
	height: 43.49px;
	background-color: #4583F8; /* 蓝色背景 */
	border: none; /* 无边框 */
	border-radius: 6px; /* 圆角 */
	color: #FFFFFF; /* 白色文字 */
	font-size: 15px;
	font-weight: 400; /* normal */
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	cursor: pointer;
	transition: all 0.3s ease;
	margin-top: 22px; /* 距离上方div框22px */
	margin-left: auto;
	margin-right: auto;
	display: block; /* 块级元素，便于居中 */
}

.clone-action-button:hover {
	background-color: #3a73e6; /* hover时稍微深一点的蓝色 */
	transform: translateY(-2px); /* 轻微上移效果 */
	box-shadow: 0 4px 8px rgba(69, 131, 248, 0.3); /* 添加阴影效果 */
}

/* 精品克隆按钮特殊样式 */
.clone-action-button.premium-button {
	background-color: #9E65E9; /* 紫色背景 */
}

.clone-action-button.premium-button:hover {
	background-color: #8A4FD3; /* hover时稍微深一点的紫色 */
	box-shadow: 0 4px 8px rgba(158, 101, 233, 0.3); /* 紫色阴影效果 */
}

/* SFT克隆按钮特殊样式 */
.clone-action-button.sft-button {
	background: linear-gradient(135deg, #633DCA 0%, #0D36B1 100%); /* 渐变色背景 */
}

.clone-action-button.sft-button:hover {
	background: linear-gradient(135deg, #5632B8 0%, #0B2E9F 100%); /* hover时稍微深一点的渐变色 */
	box-shadow: 0 4px 8px rgba(99, 61, 202, 0.3); /* 渐变色阴影效果 */
}

.card-description {
	font-size: 16px;
	color: #6b7280;
	line-height: 1.6;
	margin-bottom: 40px;
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
}

.card-action-button {
	width: 96px;
	height: 31px;
	background: #FFFFFF;
	color: #51D19B;
	border: 1px solid #51D19B;
	border-radius: 6px;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	margin: 0 auto;
	display: flex;
	align-items: center;
	justify-content: center;
}

.card-action-button:hover {
	background: #51D19B;
	color: #ffffff;
	transform: translateY(-2px);
}
</style>