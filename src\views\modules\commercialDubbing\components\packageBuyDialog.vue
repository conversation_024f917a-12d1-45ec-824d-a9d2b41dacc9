<template>
    <el-dialog v-model="dialogVisible" class="package_buy_commercialDubbing_dialog" width="512px">
        <template #header>
            <img src="@/assets/images/soundStore/detail_package_close.png" class="package_buy_commercialDubbing_close" @click="close" alt="">
            <div class="package_buy_commercialDubbing_title">
                <img src="@/assets/images/soundStore/commercialDubbing_warn.svg" alt="">
                <span>您还未拥有该声音</span>
            </div>
        </template>
        <template #default>
            <p>请购买进阶版套餐包继续使用</p>
        </template>
        <template #footer>
            <button @click="buy_package('基础版套餐包')" v-if="pack_type.includes('基础版套餐包')">购买基础套餐包</button>
            <button @click="buy_package('进阶版套餐包')">购买进阶套餐包</button>
        </template>
        
    </el-dialog>
</template>
<script setup>
import { ref, defineExpose, reactive,watch } from 'vue';
import { useSoundStore } from '@/stores/modules/soundStore.js' 
import { useRouter } from 'vue-router'
let soundStore = useSoundStore()
let router = useRouter()
let dialogVisible=ref(false)
let pack_type=ref([])
let voiceName=ref('')
let close=()=>{
    dialogVisible.value=false
}
let buy_package=(platformNickname)=>{
    soundStore.setOpenSoundPack(platformNickname)
    router.push({ path: '/soundStore',query:{package:true} })
}
watch(()=>dialogVisible,(newVal)=>{
 if(newVal){
    pack_type.value=[]
  
    
    soundStore.all_package.map((item)=>{
        // console.log(item.data,voiceName.value,'watch');
        
       let index=item.data.findIndex(item1=>item1.voiceName==voiceName.value)
       console.log(soundStore.all_package,voiceName.value,index,999);
       index>=0&&pack_type.value.push(item.platformNickname)
    })
 }
} ,{ deep: true ,immediate:true})
defineExpose({
    dialogVisible,
    voiceName
})
</script>
<style lang="scss">
.package_buy_commercialDubbing_dialog{
    border-radius: 4px;
    padding: 32px;
    padding-top: 24px;
    box-sizing: border-box;
    position: relative;
    .el-dialog__headerbtn{
       display: none;
    }
    .el-dialog__header{
        padding: 0;
       .package_buy_commercialDubbing_close{
            width: 13px;
            height: 13px;
            top: 18px;
            right: 17px;
            position: absolute;
            cursor: pointer;
       }
       .package_buy_commercialDubbing_title{
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            img{
                width: 20px;
                height: 20px;
                margin-right: 8px;
            }
            span{
                font-size: 16px;
                color: #1D2129;
                line-height: 24px;
            }
       }

    }
    .el-dialog__body{
        p{
            font-size: 14px;
            color: #1D2129;
            line-height: 22px;
            margin: 0;
            margin-bottom: 24px;
        }
    }
    .el-dialog__footer{
        padding: 0;
        display: flex;
        justify-content: flex-end;
        button{
            padding: 5px 16px;
            color: #4E5969;
            border-radius: 2px 2px 2px 2px;
            font-size: 14px;
            background: #F2F3F5;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            margin-right: 8px;
            line-height: 22px;
            &:last-child{
                background: #0AAF60;
                color: #FFFFFF;
                margin-right: 0;
            }
        }

    }
}
</style>