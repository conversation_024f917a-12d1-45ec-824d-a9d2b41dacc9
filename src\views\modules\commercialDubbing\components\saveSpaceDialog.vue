<template>
    <el-dialog v-model="dialogVisible" class="save_space_dialog" width="420px" v-loading="loading">
        <template #header>
            <img src="@/assets/images/aiImages/save_space_dialog_close.svg" class="save_space_dialog_close" @click="close" alt="">
            <span class="save_space_dialog_title">保存到我的空间</span>
        </template>
        <template #default>
            <el-checkbox-group v-model="checkedValues" @change="handleChange" class="vertical-checkbox-group">
                <el-checkbox :label="item.id" v-for="(item,index) in list" :index="String(index)">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
        </template>
        <template #footer>
            <button @click="cancel" >取消</button>
            <button @click="save">确定</button>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref, defineExpose, reactive,watch,defineEmits } from 'vue';
import { ElMessage } from 'element-plus'
let emit=defineEmits("save")
let dialogVisible=ref(false)
let checkedValues=ref([])
let params=ref([])
let loading=ref(false)
let list=ref([])
let close=()=>{
    dialogVisible.value=false
}
let handleChange=(val)=>{
    // 保证 checkedValues 只有一个元素
    if (val.length > 1) {
    // 只保留最后选中的那个
        checkedValues.value = [val[val.length - 1]];
    }
}
let cancel=()=>{
    close()
}
let save=()=>{
    if(checkedValues.value.length==0){
        ElMessage({
            message: '请选择一个项目',
            type: 'warning',
        })
        return
    }
    params.value.map((item)=>{
        item.albumId=checkedValues.value[0]
    })
    loading.value=true
    emit('save',params.value)
}
watch(()=>dialogVisible,(newVal)=>{
 if(newVal){
    checkedValues.value=[]
 }
} ,{ deep: true ,immediate:true})
defineExpose({
    dialogVisible,
    params,
    loading,
    list,
    close
})
</script>
<style lang="scss">
.save_space_dialog{
    border-radius: 4px;
    padding: 32px;
    padding: 16px 18px;
    padding-top: 0;
    box-sizing: border-box;
    position: relative;
    .el-dialog__headerbtn{
       display: none;
    }
    .el-dialog__header{
        position: relative;
        display: flex;
        justify-content: center;
        padding: 16px 0;
        margin-bottom: 12px;
       .save_space_dialog_title{
            font-size: 18px;
            line-height: 22px;
            color: #1D2129;
            text-align: center;
          
       }
       .save_space_dialog_close{
            position: absolute;
            top: 17px;
            right: -1px;
            z-index: 1;
            width: 20px;
            height: 20px;
            cursor: pointer;
       }

    }
    .el-dialog__body{
       padding: 16px 24px 44px;
       width: 100%;
       box-sizing: border-box;
       .el-checkbox-group{
        display: flex;
        flex-direction: column;
        .el-checkbox{
            line-height: 0;
            height: fit-content;
            margin-bottom: 24px;
            .el-checkbox__input{
                .el-checkbox__inner{
                    width: 16px;
                    height: 16px;
                    border: 1px solid #D9D9D9;
                    border-radius: 3px;
                    box-shadow: none;
                    box-sizing: border-box;
                }
               &.is-checked{
                .el-checkbox__inner{
                    background-color: #0AAF60;
                    border-color: #0AAF60;
                }
               }
            }
            .el-checkbox__label{
                font-size: 16px;
                line-height: 24px;
                color: #202328;
                padding-left: 12px;
            }
            &.is-checked{
                .el-checkbox__label{
                
                    font-size: 16px;
                    line-height: 24px;
                    color: #0AAF60;
                }
            }
            &:last-child{
                margin-bottom: 0;
            }
        }
       
       }
    }
    .el-dialog__footer{
        padding: 0;
        display: flex;
        justify-content: flex-end;
        button{
            flex: 1;
            padding: 5px 16px;
            color: #1D2129;
            border-radius: 2px 2px 2px 2px;
            font-size: 14px;
            background: #F2F3F5;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            margin-right: 8px;
            line-height: 22px;
            &:last-child{
                background: #0AAF60;
                color: #FFFFFF;
                margin-right: 0;
            }
        }

    }

}
</style>