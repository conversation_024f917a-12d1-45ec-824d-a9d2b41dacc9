<template>
    <div class="content-wrapper">
        <!-- 历史记录面板 -->
        <div class="history-panel" v-show="pageStore.showHistoryList">
            <!-- 标题行 -->
            <div class="history-header">
                <h2 class="user-title">用户对话</h2>
                <el-button class="close-btn" @click="closeHistory">返回</el-button>
            </div>

            <!-- 消息容器 -->
            <div class="card-container" ref="containerRef">
                <!-- 对话卡片 -->
                <div class="merged-card">
                    <!-- 头部区域 -->
                    <div class="card-head"></div>

                    <!-- 消息主体 -->
                    <div class="card-body" v-for="(dateData, index) in mergedData" :key="index"
                        @click="backClick(index)">
                        <span class="time">{{ dateData.time }}</span>

                        <!-- 循环具体对话条目 -->
                        <div v-for="(dialog, dialogIndex) in dateData.ai" :key="dialogIndex">
                            <div class="user">
                                <el-icon>
                                    <ChatLineRound />
                                </el-icon>
                                <div class="user-text">{{ dialog.user }}</div>
                            </div>
                            <div class="ai-response">{{ dialog.ai }}</div>
                        </div>
                        <!-- 备注：点击历史记录项将跳转到对应的聊天页面，并加载相关对话内容 -->
                    </div>
                </div>

                <!-- 新增加载状态 -->
                <div v-if="isLoading" class="card-body loading-status">
                    <el-icon class="loading-icon">
                        <Loading />
                    </el-icon>
                    正在加载...
                </div>
                <div v-if="noMoreData" class="card-body no-more">
                    没有更多历史记录了
                </div>
            </div>
        </div>

        <!-- 默认展示的内容  -->
        <div v-show="!pageStore.showHistoryList">
            <!-- 新增标题 -->
            <h2 class="gallery-title">
                多种视频文案创作模板，润色校对，一键成文
            </h2>
            <!-- 新增右侧按钮 -->
            <el-button link class="history-button" @click="showHistoryClick">
                <el-icon>
                    <Clock />
                </el-icon>
                <span>历史对话</span>
            </el-button>
            <div class="image-gallery" v-loading="loading">
                <div v-for="(item, index) in images" :key="index" class="gallery-item" @click="imgClick(item)">
                    <img :src="item.ossUrl" :alt="item.templateName" loading="lazy" class="grid-image" />
                </div>
            </div>

            <!-- 输入框 -->
            <div class="custom-input-container">
                <!-- 高亮输入框，contenteditable 替换 el-input -->
                <div
                    class="custom-input"
                    contenteditable="true"
                    ref="inputRef"
                    :placeholder="'请输入你要撰写的主题'"
                    @input="handleInput"
                    @keydown.enter="handleKeydown"
                    @paste="handlePaste"
                    @focus="handleInputFocus"
                    @blur="handleInputBlur"
                    :style="{minHeight: '70px'}"
                    @compositionstart="handleCompositionStart"
                    @compositionend="handleCompositionEnd"
                ></div>
                <div class="action-buttons">
                    <div class="button-group">
                        <!-- 历史记录按钮 -->
                        <el-tooltip content="历史记录" placement="top" effect="light"
                            :popper-style="{ border: 'none', padding: '8px 12px' }" :offset="1" :show-arrow="false">
                            <el-button @click="showHistoryClick" link class="icon-btn">
                                <img class="icon-img" src="../../../assets/img/26.png" />
                            </el-button>
                        </el-tooltip>
                        <span class="divider"></span>
                        <!-- 返回对话按钮 -->
                        <el-tooltip content="返回对话" placement="top" effect="light"
                            :popper-style="{ border: 'none', padding: '8px 12px' }" :offset="1" :show-arrow="false">
                            <el-button @click="goToChat" link class="icon-btn">
                                <img class="icon-img" src="../../../assets/img/19.png" />
                            </el-button>
                        </el-tooltip>
                        <span class="divider"></span>
                        <el-button @click="handleSend" class="icon-btn" circle>
                            <img class="icon-img" src="../../../assets/img/20.png" />
                        </el-button>
                    </div>
                </div>
            </div>
            <div class="ai-disclaimer">内容由AI生成，请仔细甄别</div>
        </div>
        
        <!-- 添加会员限制弹窗 -->
        <AlertDialog 
            v-model:visible="showLimitDialog"
            type="warning"
            title="会员功能"
            message="非会员每日只能使用15次，请开通会员使用"
            confirm-button-text="开通会员"
            cancel-button-text="我知道了"
            :show-cancel-button="true"
            :custom-confirm-class="true"
            :custom-cancel-class="true"
            :show-fee-explanation="false"
            @confirm="handleOpenMember"
            @cancel="handleCloseLimitDialog"
        />
    </div>
</template>
<script setup>
import { ref, onMounted, computed, watch, getCurrentInstance } from "vue";
import { Microphone, Promotion, ChatLineRound, Clock, Loading } from "@element-plus/icons-vue";
import _ from "lodash";
import {
    creationList,
    questionAPl,
    getHistoryApi,
    getHistoryByDayApi,
} from "@/api/creation";
import { ElMessage, ElLoading } from "element-plus";
import { usePageStore } from "@/stores/page";
import { useMessageStore } from "@/stores/message";
import { storeToRefs } from "pinia";
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import { useRouter } from 'vue-router'; // 引入路由
import { formatTextForHTML } from "@/utils/textFormat"; // 导入文本格式化函数
import { useUmeng } from '@/utils/umeng/hook' // 导入友盟埋点

// 获取组件实例
const { proxy } = getCurrentInstance();

// 获取路由实例
const router = useRouter();

// 初始化埋点
const umeng = useUmeng();

const messageStore = useMessageStore();
const pageStore = usePageStore();
// 使用 storeToRefs 来保持响应性
const { showHistoryList } = storeToRefs(pageStore);
const inputText = ref("");
const formattedText = ref(""); // 添加格式化后的文本引用
const images = ref([]);
const loading = ref(true);
const inputRef = ref(null); // 添加输入框引用

// 添加会员限制相关状态
const showLimitDialog = ref(false);

// 会员限制处理方法
const handleOpenMember = () => {
    // 关闭弹窗
    showLimitDialog.value = false;
    
    // 导航到会员页面
    try {
        // 判断是否在layout布局内
        if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
            // 如果在layout布局内，使用内部路由导航
            router.push({ name: 'membership-nav' });
        } else {
            // 否则通过URL跳转
            window.location.href = '/membership';
        }
        ElMessage.success("正在跳转到会员页面");
    } catch (error) {
        console.error("导航到会员页面失败:", error);
        ElMessage.error("导航到会员页面失败，请手动前往会员中心");
    }
};

const handleCloseLimitDialog = () => {
    // 关闭会员限制弹窗
    showLimitDialog.value = false;
};

// 判断用户是否已登录
const checkUserLogin = () => {
    // 从本地存储获取user信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;
    
    try {
        const userData = JSON.parse(userStorage);
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== '';
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
};

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

// 列表接口
const creationListClick = async () => {
    try {
        loading.value = true;
        const res = await Promise.race([
            creationList(),
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error('请求超时')), 10000)
            )
        ]);
        
        if (res.length > 0) {
            images.value = res;
        }
    } catch (error) {
        console.error('获取列表失败:', error);
        ElMessage.error('获取列表失败，请稍后重试');
    } finally {
        loading.value = false;
    }
};

// 对话接口
const questionChange = async (inputText) => {
    messageStore.addUserMessage({
        content: inputText,
        time: new Date().toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
        }),
    });

    try {
        const res = await questionAPl({
            userId: getUserId(),
            question: inputText,
        });
        
        // 检查会员限制状态码
        if (res.status_code === 310) {
            // 显示会员限制弹窗
            showLimitDialog.value = true;
            
            // 添加一个空的AI回复以防止null错误
            messageStore.addBotMessage({
                content: {
                    result: {
                        ai: "由于会员限制，无法继续生成内容。"
                    }
                }
            });
            // 返回状态码，让调用方知道结果
            return res.status_code;
        }
        
        // 提交到状态管理前检查响应格式
        if (res && res.content && res.content.result) {
            messageStore.addBotMessage(res);
        } else {
            // 处理返回结构不符合预期的情况
            console.error("API返回格式不符合预期:", res);
            messageStore.addBotMessage({
                content: {
                    result: {
                        ai: "生成内容出错，请稍后重试"
                    }
                }
            });
        }
        
        // 返回状态码，让调用方知道结果
        return res.status_code;
    } catch (err) {
        console.error("获取失败:", err);
        
        // 检查错误是否包含状态码301或310
        if (err && err.response && (err.response.status_code === 301 || err.response.status_code === 310)) {
            // 显示会员限制弹窗
            showLimitDialog.value = true;
            
            // 添加一个空的AI回复以防止null错误
            messageStore.addBotMessage({
                content: {
                    result: {
                        ai: "由于会员限制，无法继续生成内容。"
                    }
                }
            });
            
            // 返回错误中的状态码
            return err.response.status_code;
        } else {
            // 添加错误消息作为AI回复
            messageStore.addBotMessage({
                content: {
                    result: {
                        ai: "生成内容出错，请稍后重试"
                    }
                }
            });
        }
        
        // 出错时返回一个非200状态码
        return 500;
    }
};

// 点击图片/卡片
const imgClick = (item) => {
    // 添加埋点代码
    umeng.trackEvent(
      '文案创作', 
      '点击模板', 
      `${item.templateName || item.theme || '未命名模板'}`, 
      ''
    )
    
    if (item.theme) {
        // 设置原始输入文本
        inputText.value = item.theme;
        // 设置格式化后的文本
        formattedText.value = formatTextForHTML(item.theme);
        
        // 添加短暂延迟后聚焦输入框
        setTimeout(() => {
            inputRef.value && inputRef.value.focus();
        }, 100);
    }
};

// 工具函数：获取光标位置
function getCaretPosition(editableDiv) {
    let caretOffset = 0;
    const doc = editableDiv.ownerDocument || editableDiv.document;
    const win = doc.defaultView || doc.parentWindow;
    let sel;
    if (typeof win.getSelection != "undefined") {
        sel = win.getSelection();
        if (sel.rangeCount > 0) {
            const range = win.getSelection().getRangeAt(0);
            const preCaretRange = range.cloneRange();
            preCaretRange.selectNodeContents(editableDiv);
            preCaretRange.setEnd(range.endContainer, range.endOffset);
            caretOffset = preCaretRange.toString().length;
        }
    }
    return caretOffset;
}

// 工具函数：设置光标位置
function setCaretPosition(editableDiv, chars) {
    if (chars >= 0) {
        const selection = window.getSelection();
        let nodeStack = [editableDiv], node, foundStart = false, charCount = 0, stop = false;
        while (!stop && (node = nodeStack.pop())) {
            if (node.nodeType === 3) { // text node
                let nextCharCount = charCount + node.length;
                if (!foundStart && chars <= nextCharCount) {
                    const range = document.createRange();
                    range.setStart(node, chars - charCount);
                    range.collapse(true);
                    selection.removeAllRanges();
                    selection.addRange(range);
                    stop = true;
                }
                charCount = nextCharCount;
            } else {
                let i = node.childNodes.length;
                while (i--) {
                    nodeStack.push(node.childNodes[i]);
                }
            }
        }
    }
}

const isComposing = ref(false);

const handleCompositionStart = () => {
    isComposing.value = true;
};
const handleCompositionEnd = (e) => {
    isComposing.value = false;
    // 组合输入结束后，手动触发一次高亮渲染
    handleInput(e);
};

const handleInput = (e) => {
    if (isComposing.value) return; // 输入法组合期间不做高亮渲染
    const editableDiv = e.target;
    // 获取当前光标位置（纯文本）
    const caretPos = getCaretPosition(editableDiv);
    // 获取纯文本内容
    const text = editableDiv.innerText;
    inputText.value = text;
    // 用高亮HTML渲染
    const html = formatTextForHTML(text);
    // 只有内容变化时才重渲染，避免死循环
    if (editableDiv.innerHTML !== html) {
        editableDiv.innerHTML = html;
        // 恢复光标
        setCaretPosition(editableDiv, caretPos);
    }
};

const handlePaste = (e) => {
    e.preventDefault();
    const text = (e.clipboardData || window.clipboardData).getData('text');
    document.execCommand('insertText', false, text);
};

const handleInputBlur = (e) => {
    // 可扩展逻辑
};

watch(inputText, (val) => {
    const editableDiv = inputRef.value;
    if (editableDiv && editableDiv.innerText !== val) {
        const html = formatTextForHTML(val);
        editableDiv.innerHTML = html;
    }
});

// 输入框获得焦点时处理
const handleInputFocus = () => {
    // 输入框获得焦点时，保持已有文本不变
    // 如果需要，可以在这里添加其他逻辑
};

// 点击格式化文本区域时聚焦输入框
const focusInput = () => {
    inputRef.value && inputRef.value.focus();
};

// 添加跳转到聊天页面的方法
const goToChat = async () => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }
    
    if (mergedData.value && mergedData.value.length > 0) {
        const time = mergedData.value[0].time.split("-");
        const output = time.join("");

        getHistoryByDayApiChang(output);
        pageStore.currentView = "chat";
    } else {
        pageStore.currentView = "chat";
    }
};

// 分页数据
const currentPage = ref(1);
const isLoading = ref(false);
const noMoreData = ref(false);
const containerRef = ref(null);

// 模拟数据（实际应该从接口获取）
const sourceData = ref([]);

// 模拟异步请求
const fetchMockData = async (page) => {
    return await getHistoryApi({ userId: getUserId() });
};

// 获取数据方法
const getHistoryData = async (page) => {
    try {
        console.log("正在获取历史数据...");
        isLoading.value = true;
        const res = await fetchMockData(page);
        console.log("获取到的历史数据:", res);
        
        if (res && res.content.result) {
            // 直接将API返回的结果赋值给sourceData
            sourceData.value = res.content.result;
        } else {
            sourceData.value = {};
        }
    } catch (error) {
        console.error("获取历史数据失败:", error);
        sourceData.value = {};
    } finally {
        isLoading.value = false;
    }
};

// 将数据转换为 mergedData 格式，并在这里进行排序
const mergedData = computed(() => {
    // 将对象转换为数组，以便进行排序
    if(sourceData.value!=undefined) {
        return Object.entries(sourceData.value)
        .map(([date, items]) => {
            // 转换日期格式（示例：20250224 → 2025-02-24）
            const formatDate = `${date.slice(0, 4)}-${date.slice(4, 6)}-${date.slice(6, 8)}`;

            // 合并对话内容
            const mergedContent = items.map((item) => ({
                user: item.user,
                ai: item.ai,
            }));
            
            return {
                time: formatDate,
                user: `共 ${items.length} 条对话`,
                ai: mergedContent,
                // 添加一个时间戳，用于排序
                timestamp: new Date(formatDate).getTime()
            };
        })
        // 按时间戳倒序排序，最新的在前面
        .sort((a, b) => b.timestamp - a.timestamp);
    } else {
        return [];
    }
});

// 滚动监听
onMounted(() => {
    const container = containerRef.value;
    if (!container) return;

    const scrollHandler = _.throttle(() => {
        const { scrollTop, scrollHeight, clientHeight } = container;
        // 滚动到底部50px时加载
        if (
            scrollHeight - (scrollTop + clientHeight) < 50 &&
            !isLoading.value &&
            !noMoreData.value
        ) {
            currentPage.value++;
        }
    }, 200);

    container.addEventListener("scroll", scrollHandler);
});

// 监听 showHistoryList 的变化
watch(showHistoryList, async (newVal) => {
    if (newVal) {
        console.log("显示历史记录，开始加载数据...");
        currentPage.value = 1;
        await getHistoryData(1);
    }
}, { immediate: true }); // 添加 immediate: true 确保组件挂载时就执行一次

// 历史对话返回
const closeHistory = () => {
    pageStore.showHistoryList = false;
};

// 历史对话方法
const showHistoryClick = () => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }
    
    pageStore.showHistoryList = true;
    // 数据加载会由 watch 触发，不需要在这里调用
};

// 历史记录文字跳转
const backClick = (index) => {
    console.log(mergedData.value[index]);
    const time = mergedData.value[index].time.split("-");
    const output = time.join("");

    // 触发历史记录回显到聊天框里里面的内容
    getHistoryByDayApiChang(output);
    pageStore.currentView = "chat";
};

const getHistoryByDayApiChang = async (output) => {

    const res = await getHistoryByDayApi({
        userId: getUserId(),
        page: "1",
        pageSize: "10",
        day: output,
    });


    messageStore.messages = [];
    res.content.result.forEach((item) => {
        // 添加用户消息
        messageStore.messages.push({
            content: item.user,
            time: new Date(item.time * 1000).toLocaleTimeString("zh-CN", {
                hour: "2-digit",
                minute: "2-digit",
            }),
            isUser: true,
            isNew: false,
        });

        // 添加AI消息
        messageStore.messages.push({
            content: item.ai,
            time: new Date(item.time * 1000).toLocaleTimeString("zh-CN", {
                hour: "2-digit",
                minute: "2-digit",
            }),
            isUser: false,
            isNew: false,
        });
    });
};

const handleKeydown = (event) => {
    console.log(event, 111);

    // 检查是否同时按下Shift键
    if (!event.shiftKey) {
        event.preventDefault(); // 阻止默认换行行为
        handleSend();
    }
    // 如果按住Shift+Enter则允许换行
};

// 输入框提交方法
const handleSend = async () => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }
    
    if (!inputText.value.trim()) {
        ElMessage.error("请输入您要撰写的主题");
        return;
    }

    const userInput = inputText.value.trim();
    inputText.value = ""; // 清空输入框
    
    // 添加埋点代码
    umeng.trackEvent(
      '文案创作', 
      '点击发送', 
      `文案创作-${userInput.substring(0, 20)}${userInput.length > 20 ? '...' : ''}`, 
      ''
    )
    
    // 显示全局loading
    const loadingInstance = ElLoading.service({
        lock: true,
        text: '内容生成中...',
        background: 'rgba(255, 255, 255, 0.7)'
    });
    
    try {
        // 调用问题接口，并等待响应状态码
        const statusCode = await questionChange(userInput);
        
        // 如果状态码是310，不进行跳转
        if (statusCode === 310) {
            console.log('会员限制，不跳转到聊天页面');
            return;
        }
        
        // 如果状态码是200，正常跳转到聊天页面
        if (statusCode === 200) {
            // 跳转到聊天页面
            pageStore.currentView = "chat";
        }
        
        emit("send", userInput);
    } finally {
        // 关闭loading
        loadingInstance.close();
    }
};

// 在原有代码基础上增加：
const emit = defineEmits(["send"]);

// 在组件加载时获取历史记录
onMounted(async () => {
    await creationListClick(); // 获取列表数据
    try {
        // 调用获取历史记录的接口
        const res = await getHistoryApi({ userId: getUserId() });
        console.log("获取到的历史数据:", res);
        sourceData.value = res?.content?.result;
    } catch (error) {
        console.error("获取历史数据失败:", error);
    }
});
</script>

<style lang="scss" scoped>
.content-wrapper {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding-top: 5px;
    overflow-x: hidden;
    // 隐藏滚动条但保留功能
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    &::-webkit-scrollbar {
        display: none; /* Chrome/Safari */
        width: 0;
        height: 0;
    }
    // height: 100vh;
    // 标题样式
    .gallery-title {
        text-align: center;
        font-weight: 700;
        font-size: 24px;
        color: #333;
        margin: 30px auto;
        letter-spacing: 1.2px;
        display: inline-block;
        width: 100%;

        &::after {
            content: "";
            display: block;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            margin: 12px auto 0;
        }
    }

    /* 图片网格样式 */
    .image-gallery {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        max-width: 1200px;
        margin: 0 auto;

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            // aspect-ratio: 4/3;

            &:hover {
                transform: translateY(-4px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);

                .grid-image {
                    transform: scale(1.08);
                }
            }
        }

        .grid-image {
            width: 100%;
            height: 100%;
            // object-fit: cover;
            border-radius: 8px;
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
    }

    :deep(.history-button) {
        position: absolute;
        right: 0;
        top: 11%;
        transform: translateY(-50%);
        padding: 0 !important;

        span {
            font-size: 12px;
            margin-left: 4px;
            vertical-align: middle;
        }

        .el-icon {
            font-size: 14px;
            vertical-align: -2px;
        }

        &:hover {
            color: #67c23a !important;
        }
    }

    // 输入框
    .custom-input-container {
        width: 100%;
        margin-top: 15px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        position: relative;
        border: 1px solid #dcdfe6;
        border-radius: 20px;
        padding: 10px 10px 8px;
        display: flex;
        flex-direction: column;
        min-height: 80px;
        background-color: #ffffff;
        // 格式化显示文本样式
        .formatted-text {
            position: absolute;
            top: 10px;
            left: 12px;
            right: 12px;
            padding: 0;
            font-size: 14px;
            line-height: 1.5;
            z-index: 1;
            pointer-events: none; // 让点击可以穿透到下方的输入框
            overflow: hidden;
            max-height: 200px;
            user-select: none; // 防止文本选择干扰输入
            
            // 确保span内的【】颜色显示正常
            :deep(span) {
                display: inline;
            }
        }

        &:hover {
            border-color: #c0c4cc;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }

        &:focus-within {
            border-color: #409eff;
            box-shadow: 0 2px 12px rgba(32, 160, 255, 0.2);
        }

        .custom-input {
            flex: 1;
            position: relative;
            z-index: 2;
            min-height: 70px;
            font-size: 14px;
            font-family: inherit;
            line-height: 1.5;
            box-sizing: border-box;
            /* border: 1px solid #dcdfe6; */
            // border-radius: 20px;
            background: #fff;
            outline: none;
            padding: 0 0 20px 0 !important;
            color: #333;
            caret-color: #000;
            resize: none;
            overflow-y: auto;
            // 兼容placeholder
            &:empty:before {
                content: attr(placeholder);
                color: #bbb;
            }
        }

        .action-buttons {
            align-self: flex-end;

            .button-group {
                display: flex;
                align-items: center;
                gap: 15px;

                .icon-btn {
                    padding: 6px;
                    width: 30px;
                    height: 30px;
                    border: none;

                    :hover {
                        background-color: #fff;
                    }

                    .icon-img {
                        width: 30px;
                        height: 30px;
                    }
                }

                .divider {
                    width: 1px;
                    height: 30px;
                    background-color: #dcdfe6;
                }
            }
        }
    }

    // 历史对话
    .history-panel {
        margin-top: 15px;
        
        .history-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 15px 0;
            padding-top: 10px;
        }

        // 消息容器
        .card-container {
            height: 80vh; // 新增固定高度
            overflow-y: auto; // 新增滚动
            padding-right: 8px; // 避免滚动条遮挡内容
            background: #fff;

            // 隐藏滚动条方案
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;

            /* IE/Edge */
            /* 自定义滚动条 (保留原有样式基础上新增) */
            &::-webkit-scrollbar {
                display: none;
                /* Chrome/Safari */
                width: 0;
                height: 0;
            }

            // &::-webkit-scrollbar-thumb {
            //     background: #c0c4cc;
            //     border-radius: 4px;
            // }
            .no-more {
                text-align: center;
                color: #c0c4cc;
                font-size: 12px;
                padding: 16px 0;
            }

            .loading-status {
                text-align: center;
                color: #909399;
                padding: 20px 0;

                .loading-icon {
                    animation: rotating 2s linear infinite;
                    margin-right: 8px;
                }
            }

            .merged-card {
                .card-body {
                    cursor: pointer;
                    margin-bottom: 30px;

                    .time {
                        color: #333;
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 10px;
                        display: block;
                        padding: 10px 10px 0;
                    }

                    .user {
                        display: flex;
                        align-items: center;
                        margin-top: 5px;
                        padding: 0 10px 0;

                        .user-text {
                            padding-left: 5px;
                            font-weight: 600;
                            color: #1f2937;
                            line-height: 1.5;
                        }
                    }

                    .ai-response {
                        font-size: 12px;
                        color: #666;
                        line-height: 1.5;
                        /* 多行省略核心代码 */
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        /* 兼容性优化 */
                        max-height: 3em;
                        /* 2行 x 1.5行高 = 3em */
                        position: relative;
                        padding: 0 10px 0;
                    }
                }
            }

            // 新增旋转动画
            @keyframes rotating {
                from {
                    transform: rotate(0deg);
                }

                to {
                    transform: rotate(360deg);
                }
            }
        }
    }
}

.ai-disclaimer {
    font-size: 12px;
    color: #999;
    text-align: center;
    margin-top: 5px;
}
</style>
