<template>
    <!-- 首页组件 -->
    <HomePage v-if="pageStore.currentView === 'home'" @send="handleSend" />

    <!-- 聊天组件 -->
    <ChatPage
        v-else
        :isThinking="pageStore.isThinking"
        :content="pageStore.messageContent"
        @back="pageStore.returnToHome()"
    />
</template>
  
  <script setup>
import { usePageStore } from "@/stores/page";
import HomePage from "./HomePage.vue";
import ChatPage from "./ChatPage.vue";

const pageStore = usePageStore();

const handleSend = (content) => {
    // 这里可以不需要做太多处理，因为数据获取已经在 HomePage 中完成
    pageStore.switchToChat(content);
};
</script>