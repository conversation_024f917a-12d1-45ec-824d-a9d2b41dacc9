<template>
	<div class="digital-human-transition-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<!-- 顶部功能卡片区域 -->
			<div class="feature-cards-section">
				<div class="feature-card" @click="navigateToDigitalHuman">
					<img src="@/assets/img/changjian1.png" alt="创建数字人作品" />
				</div>
				
				<div class="feature-card">
					<img src="@/assets/img/changjian2.png" alt="定制数字人" />
				</div>
			</div>

			<!-- 我的作品区域 -->
			<div class="works-section">
				<div class="section-header">
					<h2>我的作品</h2>
					<a href="#" class="view-more" @click.prevent="navigateToMyWorksDetail">
						查看更多
						<img src="@/assets/img/you1.png" alt="查看更多" class="view-more-icon" />
					</a>
				</div>
				<div class="works-container">
					<DigitalHumanWorks :selectedFileId="null" :workTab="workTab" :pageSize="6" :enableInfiniteScroll="false" :hideCue="true" :showTextButtons="true" />
				</div>
			</div>

			<!-- 我的数字人区域 -->
			<div class="digital-humans-section">
				<div class="section-header">
					<h2>我的数字人</h2>
					<a href="#" class="view-more" @click.prevent="navigateToMyDigitalHumansDetail">
						查看更多
						<img src="@/assets/img/you1.png" alt="查看更多" class="view-more-icon" />
					</a>
				</div>
				<div class="humans-grid">
					<div class="human-item" v-for="(item, index) in digitalHumansProgress" :key="item.id">
						<div class="human-avatar" @mouseenter="showDigitalHumansCreateVideo(index)" @mouseleave="hideDigitalHumansCreateVideo(index)">
							<img src="@/assets/img/ceshi1.png" alt="数字人头像" />
							<div class="create-video-overlay" :class="{ show: digitalHumansHoveredIndex === index && !dropdownVisible }">创建视频</div>
							<!-- 生成中状态 -->
							<div class="generating-overlay" v-if="item.status === 'generating'">
								<img src="@/assets/img/dongtai1.gif" alt="生成中" />
								<p>生成中...{{ item.progress }}%</p>
							</div>
							<!-- 三个点菜单 -->
							<el-dropdown 
								trigger="hover" 
								placement="bottom"
								@command="handleCommand"
								@visible-change="handleDropdownVisibleChange"
								v-if="item.status !== 'generating'"
								class="three-dots-dropdown">
								<div class="three-dots">⋮</div>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item :command="`rename-${index}`">重命名</el-dropdown-item>
										<el-dropdown-item :command="`delete-${index}`">删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
						<div class="human-name">
							<!-- 编辑状态显示输入框 -->
							<div v-if="editingIndex === index" class="edit-name-container">
								<input 
									v-model="editingName" 
									class="edit-name-input" 
									@keyup.enter="saveRename(index)"
									@keyup.esc="cancelRename"
									@blur="saveRename(index)"
									ref="editInput"
									autofocus
								/>
							</div>
							<!-- 正常状态显示名字 -->
							<span v-else>{{ item.name }}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- 公共数字人区域 -->
			<div class="public-humans-section">
				<div class="section-header">
					<h2>公共数字人</h2>
					<a href="#" class="view-more" @click.prevent="navigateToPublicDigitalHumansDetail">
						查看更多
						<img src="@/assets/img/you1.png" alt="查看更多" class="view-more-icon" />
					</a>
				</div>
				<div class="humans-grid">
					<div class="human-item" v-for="(item, index) in publicHumansProgress" :key="item.id">
						<div class="human-avatar" @mouseenter="showPublicHumansCreateVideo(index)" @mouseleave="hidePublicHumansCreateVideo(index)">
							<img src="@/assets/img/ceshi1.png" alt="公共数字人头像" />
							<div class="create-video-overlay" :class="{ show: publicHumansHoveredIndex === index }">创建视频</div>
							<!-- 生成中状态 -->
							<div class="generating-overlay" v-if="item.status === 'generating'">
								<img src="@/assets/img/dongtai1.gif" alt="生成中" />
								<p>生成中...{{ item.progress }}%</p>
							</div>
						</div>
						<div class="human-name">名字</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
// 导入数字人作品组件
import DigitalHumanWorks from '@/views/modules/mySpace/myWorks/components/DigitalHumanWorks.vue'

// 初始化router
const router = useRouter()

// 工作标签状态
const workTab = ref('digital')

// 鼠标悬停状态
const digitalHumansHoveredIndex = ref(null)
const publicHumansHoveredIndex = ref(null)

// 三个点菜单状态
const digitalHumansMenuIndex = ref(null)

// 编辑状态
const editingIndex = ref(null)
const editingName = ref('')

// 下拉菜单状态
const dropdownVisible = ref(false)

// 数字人生成进度数据
const digitalHumansProgress = ref([
	{ id: 1, progress: 0, status: 'normal', name: '名字' },
	{ id: 2, progress: 0, status: 'normal', name: '名字' },
	{ id: 3, progress: 50, status: 'generating', name: '名字' }, // 示例：第3个数字人正在生成中，进度50%
	{ id: 4, progress: 0, status: 'normal', name: '名字' },
	{ id: 5, progress: 0, status: 'normal', name: '名字' },
	{ id: 6, progress: 0, status: 'normal', name: '名字' },
	{ id: 7, progress: 0, status: 'normal', name: '名字' }
])

// 公共数字人生成进度数据
const publicHumansProgress = ref([
	{ id: 1, progress: 0, status: 'normal' },
	{ id: 2, progress: 0, status: 'normal' },
	{ id: 3, progress: 75, status: 'generating' }, // 示例：第3个公共数字人正在生成中，进度75%
	{ id: 4, progress: 0, status: 'normal' },
	{ id: 5, progress: 0, status: 'normal' },
	{ id: 6, progress: 0, status: 'normal' },
	{ id: 7, progress: 0, status: 'normal' }
])

// 监听进度变化，当达到100%时自动恢复状态
const checkProgressCompletion = (progressList) => {
	progressList.forEach(item => {
		if (item.status === 'generating' && item.progress >= 100) {
			item.status = 'normal'
			item.progress = 0
			console.log(`数字人 ${item.id} 生成完成`)
		}
	})
}

// 更新数字人生成进度
const updateDigitalHumansProgress = (newProgress) => {
	digitalHumansProgress.value = newProgress
	checkProgressCompletion(digitalHumansProgress.value)
}

// 更新公共数字人生成进度
const updatePublicHumansProgress = (newProgress) => {
	publicHumansProgress.value = newProgress
	checkProgressCompletion(publicHumansProgress.value)
}

// 显示我的数字人创建视频按钮
const showDigitalHumansCreateVideo = (index) => {
	// 如果下拉菜单正在显示，则不显示创建视频按钮
	if (dropdownVisible.value) {
		return
	}
	digitalHumansHoveredIndex.value = index
}

// 隐藏我的数字人创建视频按钮
const hideDigitalHumansCreateVideo = (index) => {
	digitalHumansHoveredIndex.value = null
}

// 显示公共数字人创建视频按钮
const showPublicHumansCreateVideo = (index) => {
	publicHumansHoveredIndex.value = index
}

// 隐藏公共数字人创建视频按钮
const hidePublicHumansCreateVideo = (index) => {
	publicHumansHoveredIndex.value = null
}

// 处理下拉菜单命令
const handleCommand = (command) => {
	const [action, index] = command.split('-')
	const indexNum = parseInt(index)
	
	if (action === 'rename') {
		renameDigitalHuman(indexNum)
	} else if (action === 'delete') {
		deleteDigitalHuman(indexNum)
	}
}

// 处理下拉菜单显示状态变化
const handleDropdownVisibleChange = (visible) => {
	dropdownVisible.value = visible
	if (visible) {
		// 当下拉菜单显示时，隐藏创建视频按钮
		digitalHumansHoveredIndex.value = null
	}
}

// 重命名数字人
const renameDigitalHuman = (index) => {
	console.log(`重命名数字人 ${index + 1}`)
	editingIndex.value = index
	editingName.value = digitalHumansProgress.value[index].name // 设置为当前名字
}

// 保存重命名
const saveRename = (index) => {
	console.log(`保存重命名: ${editingName.value}`)
	// 更新数据中的名字
	digitalHumansProgress.value[index].name = editingName.value
	// 这里可以调用API保存新名字
	editingIndex.value = null
	editingName.value = ''
}

// 取消重命名
const cancelRename = () => {
	editingIndex.value = null
	editingName.value = ''
}

// 删除数字人
const deleteDigitalHuman = (index) => {
	console.log(`删除数字人 ${index + 1}`)
}

// 导航到数字人作品页面
const navigateToDigitalHuman = () => {
	// 记录来源页面，用于返回时使用
	const fromPage = router.currentRoute.value.path
	router.push({
		path: '/digital-human-editor-page',
		query: { from: fromPage }
	})
}

// 导航到我的作品详情页面
const navigateToMyWorksDetail = () => {
	router.push({
		path: '/my-works-detail'
	})
}

// 导航到我的数字人详情页面
const navigateToMyDigitalHumansDetail = () => {
	router.push({
		path: '/my-digital-humans-detail'
	})
}

// 导航到公共数字人详情页面
const navigateToPublicDigitalHumansDetail = () => {
	router.push({
		path: '/public-digital-humans-detail'
	})
}

// 页面挂载时的初始化逻辑
onMounted(() => {
	console.log('数字人中转页面已加载')
	// 这里可以调用接口获取实际的生成进度
	// getDigitalHumansProgress()
	
	// 启用模拟进度更新（用于演示，实际使用时请注释掉）
	simulateProgressUpdate()
})

// 获取数字人生成进度的接口函数（示例）
// const getDigitalHumansProgress = async () => {
//     try {
//         const response = await fetch('/api/digital-humans/progress')
//         const data = await response.json()
//         updateDigitalHumansProgress(data)
//     } catch (error) {
//         console.error('获取数字人生成进度失败:', error)
//     }
// }

// 获取公共数字人生成进度的接口函数（示例）
// const getPublicHumansProgress = async () => {
//     try {
//         const response = await fetch('/api/public-humans/progress')
//         const data = await response.json()
//         updatePublicHumansProgress(data)
//     } catch (error) {
//         console.error('获取公共数字人生成进度失败:', error)
//     }
// }

// 模拟进度更新的测试函数（用于演示）
const simulateProgressUpdate = () => {
	// 模拟数字人进度更新
	setInterval(() => {
		const updatedProgress = digitalHumansProgress.value.map(item => {
			if (item.status === 'generating' && item.progress < 100) {
				return { ...item, progress: Math.min(item.progress + 10, 100) }
			}
			return item
		})
		updateDigitalHumansProgress(updatedProgress)
	}, 2000) // 每2秒更新一次

	// 模拟公共数字人进度更新
	setInterval(() => {
		const updatedProgress = publicHumansProgress.value.map(item => {
			if (item.status === 'generating' && item.progress < 100) {
				return { ...item, progress: Math.min(item.progress + 15, 100) }
			}
			return item
		})
		updatePublicHumansProgress(updatedProgress)
	}, 1500) // 每1.5秒更新一次
}
</script>

<style scoped lang="scss">
// 全屏应用容器
.digital-human-transition-app {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 78px;
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-y: auto;
	background-color: #FFFFFF;
}

// 顶部功能卡片区域
.feature-cards-section {
	display: flex;
	gap: 20px;
	margin-bottom: 40px;
}

.feature-card {
	cursor: pointer;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}

	img {
		width: 422px;
		height: 112px;
		object-fit: cover;
		border-radius: 8px;
	}
}

// 通用区域样式
.section-header {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 20px;

	h2 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 28px;
		font-weight: 400;
		font-style: normal;
		line-height: 100%;
		letter-spacing: 0;
		vertical-align: middle;
		color: #333;
		margin: 0;
	}

	.view-more {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 18px;
		font-weight: 400;
		font-style: normal;
		line-height: 100%;
		letter-spacing: 0;
		vertical-align: middle;
		color: #0AAF60;
		text-decoration: none;
		margin-left: 24px;
		margin-top: 7px;
		display: flex;
		align-items: center;
		gap: 4px;

		&:hover {
			text-decoration: none;
		}

		.view-more-icon {
			width: 24px;
			height: 24px;
			object-fit: contain;
			margin-left: -6px;
			margin-top: 2px;
		}
	}
}

// 我的作品区域
.works-section {
	margin-bottom: 59px;
}

.works-container {
	// height: 400px; // 设置固定高度，避免页面过长
	overflow: hidden;
	width: 1600px;
	// max-width: calc(100vw - 78px - 40px); // 减去左边距和右边距
}

// 我的数字人区域
.digital-humans-section {
	margin-bottom: 59px;
	padding-left: 0; // 重置左边距，与我的作品对齐
}

// 公共数字人区域
.public-humans-section {
	padding-left: 0; // 重置左边距，与我的作品对齐
}

// 数字人网格样式
.humans-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	gap: 20px;
	width: 1535px;
}

.human-item {
	text-align: center;
	cursor: pointer;
	width: 160px; // 固定宽度，与图片宽度一致
	flex-shrink: 0; // 防止收缩
}

.human-avatar {
	position: relative;
	width: 160px;
	height: 160px;
	border-radius: 8px;
	overflow: hidden;
	background: #f0f0f0;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.create-video-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 84px;
	height: 32px;
	background: #0AAF60;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 500;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
}

.create-video-overlay.show {
	opacity: 1;
	visibility: visible;
}

// 三个点菜单样式
.three-dots-dropdown {
	position: absolute;
	top: 8px;
	right: 8px;
	z-index: 100;
}

.three-dots {
	width: 24px;
	height: 24px;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	font-weight: bold;
	transition: background-color 0.3s ease;
	box-shadow: none !important;
	border: none !important;
	outline: none !important;
}

// 覆盖Element Plus的默认样式
.three-dots-dropdown {
	.el-tooltip__trigger {
		box-shadow: none !important;
		border: none !important;
		outline: none !important;
	}
}



.generating-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #F1F2F4;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 8px;

	img {
		width: 60px;
		height: 60px;
		margin-bottom: 8px;
	}

	p {
		color: #000000A6;
		font-family: 'PingFang SC', sans-serif;
		font-weight: 500;
		font-style: normal;
		font-size: 12px;
		line-height: 21px;
		letter-spacing: 0;
		margin: 0;
		text-align: center;
	}
}

.human-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.edit-name-container {
	display: flex;
	justify-content: center;
	height: 24px;
}

.edit-name-input {
	width: 80px;
	height: 24px;
	border: 1px solid #0AAF60;
	border-radius: 4px;
	padding: 0 8px;
	font-size: 14px;
	color: #333;
	text-align: center;
	outline: none;
	background: white;

	&:focus {
		border-color: #0AAF60;
		box-shadow: 0 0 0 2px rgba(10, 175, 96, 0.2);
	}
}

// 响应式设计
@media (max-width: 768px) {
	.main-content {
		padding: 15px;
		margin-top: 56px;
	}

	.feature-cards-section {
		flex-direction: column;
		gap: 15px;
	}

	.works-grid {
		grid-template-columns: 1fr;
	}

	.humans-grid {
		grid-template-columns: repeat(4, 1fr);
		gap: 12px;
	}

	.human-avatar {
		width: 60px;
		height: 60px;
	}
}
</style>
