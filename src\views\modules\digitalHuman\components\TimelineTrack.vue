<template>
    <!-- 
    时间轴轨道组件主容器
    功能概览：
    - 视频帧序列显示
    - 时间轴事件标记和选择
    - 播放指针拖拽控制
    - 点击时间轴跳转播放位置
    - 可视化时间线管理
    -->
    <div class="timeline-wrapper">
        <!-- 
        时间轴主容器
        核心功能：
        - 处理点击事件进行时间跳转
        - 承载所有时间轴可视化元素
        - 固定1253px宽度适配主面板
        - 115px高度提供充足的操作空间
        -->
        <div class="timeline-container" @click="handleTimelineClick"
            :style="{ '--frame-width': dynamicFrameWidth + 'px' }">
            <!--
            图像序列带：显示视频帧预览
            视觉设计：
            - 基于总时长动态计算帧数和帧宽度
            - 每帧80px高度，动态宽度根据音频时长调整
            - 统一使用数字人预览图
            - 紧密排列无间隙
            - 半透明效果，悬停时高亮
            -->
            <div class="image-sequence-container">
                <div v-for="index in frameCount" :key="index" class="frame-window" :style="frameWindowStyle"></div>
            </div>

            <!-- 
            事件标记层：已隐藏
            原功能：显示时间轴上的事件标记
            当前状态：用户要求完全移除，因此整个层被注释掉
            -->
            <!-- <div class="events-layer">
                <div v-for="event in timelineEvents" :key="event.id" class="event-marker"
                    :class="{ selected: event.id === selectedEventId }"
                    :style="{ left: calculatePosition(event.startTime) }" 
                    @click.stop="store.selectEvent(event.id)">
                </div>
            </div> -->

            <!-- 
            播放指针：支持拖动的时间位置指示器
            核心功能：
            - 实时显示当前播放时间位置
            - 支持鼠标拖拽改变播放时间
            - 拖拽时提供视觉反馈（放大+阴影）
            - 由三角形头部+垂直线组成
            - 阻止点击冒泡避免触发时间跳转
            -->
            <div class="playhead-container" :class="{ dragging: isDragging }"
                :style="{ left: calculatePosition(currentTime) }" @mousedown="handlePointerMouseDown" @click.stop>
                <!-- 播放指针三角形头部：13px高的黑色三角形 -->
                <div class="playhead-triangle"></div>
                <!-- 播放指针垂直线：2px宽的黑色线条，贯穿整个时间轴高度 -->
                <div class="playhead-bar"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
// ========================================
// 📦 依赖导入
// ========================================
// 导入Vue核心组合式API函数
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
// 导入数字人状态管理store
import { useDigitalHumanStore } from '../store/digitalHumanStore';
// 导入Pinia状态响应式工具
import { storeToRefs } from 'pinia';

// ========================================
// 📡 组件Props定义
// ========================================
/**
 * 组件Props定义
 *
 * 🎯 功能：接收父组件传递的数字人配置
 * 📊 数据类型：Object，包含数字人的URL等信息
 * 🔄 响应式：当父组件数字人变化时，时间轴自动同步显示
 */
const props = defineProps({
    digitalHumanConfig: {
        type: Object,
        default: () => ({
            type: 'picture',
            url: '',
            index: null
        })
    }
});

// ========================================
// 🏪 状态管理初始化
// ========================================
// 初始化数字人状态管理store
const store = useDigitalHumanStore();
// 解构获取时间轴相关的响应式状态
const { timelineEvents, currentTime, totalDuration, selectedEventId } = storeToRefs(store);

// ========================================
// 🖱️ 拖拽状态管理
// ========================================
// 播放指针拖拽状态标记
const isDragging = ref(false);
// 时间轴容器DOM引用（预留，当前未使用）
const timelineContainerRef = ref(null);

// ========================================
// 🎞️ 帧数计算系统
// ========================================
/**
 * 动态帧数计算
 * 
 * 🧮 计算策略：
 * 1. 基于容器宽度和帧宽度计算最大显示帧数
 * 2. 基于视频时长计算理想帧数（每2秒1帧）
 * 3. 综合考虑填满容器和视觉合理性
 * 
 * 📐 尺寸参数：
 * - 基础帧宽：50px（最小宽度）
 * - 容器内边距：左右各20px
 * - 帧密度：0.5帧/秒（每2秒显示1帧）
 * 
 * 🎯 约束条件：
 * - 最少8帧确保基础视觉效果
 * - 最多30帧避免过于密集
 * - 优先填满容器宽度
 */
const frameCount = computed(() => {
    // 基础图片宽度（不包含边距，因为已移除margin）
    const baseFrameWidth = 50; // 基础宽度，图片会flex-grow填满剩余空间
    const containerPadding = 40; // 左右各20px内边距

    // 获取实际容器宽度，如果无法获取则使用默认值
    const timelineElement = document.querySelector('.timeline-container');
    const actualWidth = timelineElement ? timelineElement.offsetWidth : 1000;
    const availableWidth = actualWidth - containerPadding;

    // 计算能填满容器的图片数量（每张图片至少50px宽度）
    const maxFramesByWidth = Math.floor(availableWidth / baseFrameWidth);

    // 根据时长计算合理的帧数（每2秒1帧）
    const framesPerSecond = 0.5;
    const idealFrames = Math.ceil(totalDuration.value * framesPerSecond);

    // 优先填满容器宽度，但不超过合理的上限
    // 最少显示8帧以确保视觉效果，最多不超过30帧避免过于密集
    const targetFrames = Math.min(maxFramesByWidth, 30);
    return Math.max(targetFrames, 8);
});



// ========================================
// 🎨 帧窗口样式计算
// ========================================
/**
 * 帧窗口动态样式
 *
 * 🎯 功能：根据数字人配置动态设置背景图片
 * 📊 逻辑：
 * - 有数字人URL时：使用数字人图片作为背景
 * - 没有数字人URL时：使用默认背景或透明
 * 🔄 响应式：当数字人配置变化时自动更新
 */
const frameWindowStyle = computed(() => {
    if (props.digitalHumanConfig && props.digitalHumanConfig.url && props.digitalHumanConfig.url.trim() !== '') {
        return {
            backgroundImage: `url('${props.digitalHumanConfig.url}')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
        };
    } else {
        // 没有数字人时显示默认背景或透明
        return {
            backgroundColor: '#f0f0f0',
            backgroundImage: 'none'
        };
    }
});

// ========================================
// 📍 时间位置映射系统
// ========================================
/**
 * 时间位置计算函数
 * 
 * 🎯 功能：将时间(秒)转换为CSS left位置
 * 🧮 算法：
 * 1. 计算时间在总时长中的百分比
 * 2. 考虑容器内边距的有效区域
 * 3. 使用CSS calc()实现响应式定位
 * 
 * 📏 定位公式：
 * position = 20px起始位置 + (容器宽度-40px) * 时间百分比
 * 
 * @param {number} time - 时间（秒）
 * @returns {string} CSS left位置值
 */
const calculatePosition = (time) => {
    if (totalDuration.value === 0) return '20px'; // 起始位置
    const percentage = (time / totalDuration.value) * 100;
    // 简化计算：20px起始位置 + (容器宽度-40px) * 百分比 
    return `calc(20px + (100% - 40px) * ${percentage / 100})`;
};

// ========================================
// 🕐 时间格式化工具
// ========================================
/**
 * 时间格式化函数
 * 
 * 🎯 功能：将秒数转换为mm:ss格式显示
 * 📝 格式：两位数分钟:两位数秒钟
 * 
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串 (如 "02:30")
 */
const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// ========================================
// 🖱️ 时间轴点击跳转系统
// ========================================
/**
 * 时间轴点击事件处理
 * 
 * 🎯 功能：点击时间轴任意位置跳转到对应时间
 * 🚫 防冲突：拖拽进行中时不响应点击事件
 * 
 * 🧮 位置计算算法：
 * 1. 获取点击位置相对于容器的坐标
 * 2. 减去左边距得到有效区域内的位置
 * 3. 计算位置在有效区域中的百分比
 * 4. 转换为对应的时间并更新播放位置
 * 
 * 📐 边界处理：
 * - 考虑左右各20px的内边距
 * - 点击超出有效区域时限制在边界内
 * 
 * 🎭 副作用：取消当前选中的事件
 * 
 * @param {MouseEvent} e - 鼠标点击事件对象
 */
const handleTimelineClick = (e) => {
    // 如果正在拖动，不处理时间轴点击事件
    if (isDragging.value) return;

    const rect = e.currentTarget.getBoundingClientRect();
    let clickX = e.clientX - rect.left;

    // 考虑左右边距20px的有效区域
    const paddingLeft = 20;
    const paddingRight = 20;
    const effectiveWidth = rect.width - paddingLeft - paddingRight;

    // 调整clickX，减去左边距
    clickX = clickX - paddingLeft;

    // 边界检查
    if (clickX < 0) clickX = 0;
    if (clickX > effectiveWidth) clickX = effectiveWidth;

    const percentage = clickX / effectiveWidth;
    store.setCurrentTime(percentage * totalDuration.value);
    store.selectEvent(null);
};

// ========================================
// 🎮 播放指针拖拽系统
// ========================================
/**
 * 播放指针鼠标按下处理
 * 
 * 🚀 启动拖拽模式：
 * 1. 阻止默认行为和事件冒泡
 * 2. 设置拖拽状态标记
 * 3. 绑定全局鼠标事件监听器
 * 
 * 🌐 全局事件策略：
 * - 监听整个文档的鼠标移动和释放
 * - 确保在容器外部也能正常拖拽
 * - 提供连贯的拖拽体验
 * 
 * @param {MouseEvent} e - 鼠标按下事件对象
 */
const handlePointerMouseDown = (e) => {
    e.preventDefault();
    e.stopPropagation();
    isDragging.value = true;

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handlePointerMouseMove);
    document.addEventListener('mouseup', handlePointerMouseUp);
};

/**
 * 播放指针鼠标移动处理
 * 
 * 🎯 实时跟随：根据鼠标位置实时更新播放时间
 * 🛡️ 状态检查：只在拖拽状态下响应移动事件
 * 
 * @param {MouseEvent} e - 鼠标移动事件对象
 */
const handlePointerMouseMove = (e) => {
    if (!isDragging.value) return;

    updateTimeFromMousePosition(e);
};

/**
 * 播放指针鼠标释放处理
 * 
 * 🛑 结束拖拽：
 * 1. 重置拖拽状态标记
 * 2. 移除全局事件监听器
 * 3. 避免内存泄漏
 * 
 * 🧹 清理工作：确保事件监听器正确移除
 * 
 * @param {MouseEvent} e - 鼠标释放事件对象
 */
const handlePointerMouseUp = (e) => {
    if (!isDragging.value) return;

    isDragging.value = false;

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handlePointerMouseMove);
    document.removeEventListener('mouseup', handlePointerMouseUp);
};

/**
 * 根据鼠标位置更新播放时间
 * 
 * 🧮 核心算法：鼠标位置转时间的精确计算
 * 
 * 计算步骤：
 * 1. 获取时间轴容器的边界矩形
 * 2. 计算鼠标相对于容器的X偏移
 * 3. 减去左边距，得到有效区域内的位置
 * 4. 应用边界限制，确保在有效范围内
 * 5. 计算位置百分比并转换为时间
 * 6. 更新store中的当前播放时间
 * 
 * 📐 边界策略：
 * - 左边界：offsetX不能小于0
 * - 右边界：offsetX不能超过有效宽度
 * - 保证时间值在0到totalDuration之间
 * 
 * @param {MouseEvent} e - 鼠标事件对象
 */
const updateTimeFromMousePosition = (e) => {
    const timelineContainer = document.querySelector('.timeline-container');
    if (!timelineContainer) return;

    const rect = timelineContainer.getBoundingClientRect();
    let offsetX = e.clientX - rect.left;

    // 考虑左右边距20px的有效区域
    const paddingLeft = 20;
    const paddingRight = 20;
    const effectiveWidth = rect.width - paddingLeft - paddingRight;

    // 调整offsetX，减去左边距
    offsetX = offsetX - paddingLeft;

    // 边界检查：确保offsetX在有效时间轴范围内
    if (offsetX < 0) offsetX = 0;
    if (offsetX > effectiveWidth) offsetX = effectiveWidth;

    // 计算对应的时间百分比
    const percentage = offsetX / effectiveWidth;

    // 计算新的播放时间
    const newTime = totalDuration.value * percentage;

    // 更新当前播放时间
    store.setCurrentTime(newTime);
};

// ========================================
// 🧹 生命周期清理
// ========================================
/**
 * 组件卸载前的清理工作
 * 
 * 🛡️ 内存泄漏防护：
 * - 移除可能残留的全局事件监听器
 * - 确保组件销毁时不会留下孤立的事件处理器
 * 
 * 💡 最佳实践：
 * - 在组件卸载时清理所有外部引用
 * - 防止在组件销毁后仍然触发事件处理
 */
onBeforeUnmount(() => {
    document.removeEventListener('mousemove', handlePointerMouseMove);
    document.removeEventListener('mouseup', handlePointerMouseUp);
});
</script>

<style scoped lang="scss">
/* ========================================
   🎞️ 时间轴主容器样式系统
   ======================================== */

/**
 * 时间轴包装器
 * 功能：提供时间轴组件的最外层容器
 * 布局：100%宽度，相对定位为内部元素提供定位基准
 */
.timeline-wrapper {
    width: 100%;
    position: relative;
}

/**
 * 时间轴主容器
 * 
 * 🎨 设计理念：
 * - 固定尺寸确保一致的用户体验
 * - 白色背景提供清晰的视觉对比
 * - 圆角设计增强现代感
 * - 水平居中适配各种屏幕尺寸
 * 
 * 📐 尺寸策略：
 * - 宽度：1253px（与主面板宽度一致）
 * - 高度：115px（提供充足的操作空间）
 * - 内边距：左右各20px（为播放指针预留边界空间）
 * 
 * 🖱️ 交互设计：
 * - 整体可点击进行时间跳转
 * - 光标显示为指针强化可点击性
 * - 溢出隐藏确保内容整洁
 * 
 * 🎯 定位系统：
 * - 相对定位为内部绝对定位元素提供基准
 * - 水平居中通过margin auto实现
 * - 防缩放属性确保在不同缩放级别下的一致性
 */
.timeline-container {
    position: relative;
    /* 🔧 保持固定宽度，通过调整帧宽度来适应不同音频时长 */
    width: 1253px;
    height: 115px;
    background-color: #ffffff;
    overflow: hidden;
    cursor: pointer;
    border-radius: 8px;
    /* 添加防止缩放影响的属性 */
    box-sizing: border-box;
    transform-origin: top left;
    /* 确保在不同缩放级别下边框保持一致 */
    // border: 1px solid #ddd; // 移除边框
    /* 添加左右内边距，给指针留出边界空间 */
    padding: 0 20px;
    margin-left: auto; // 水平居中
    margin-right: auto; // 水平居中
    // 这里移除margin-top，让其紧跟上方元素
    // 左右自动居中，由父容器的align-items: center处理
}


/* ========================================
   🖼️ 图像序列显示系统
   ======================================== */

/**
 * 图像序列带容器
 * 
 * 🎨 视觉设计：
 * - 绝对定位覆盖整个时间轴有效区域
 * - 与容器内边距保持一致的边界
 * - Flex布局实现图片的均匀分布
 * 
 * 📍 定位策略：
 * - 左右边界与容器内边距(20px)保持一致
 * - 顶部对齐，100%高度充满容器
 * - z-index:1 确保在背景层但低于交互元素
 * 
 * 🔧 布局特性：
 * - 水平排列，不允许换行
 * - 溢出隐藏防止内容超出边界
 * - 垂直居中对齐
 * - 防缩放稳定性优化
 */
.image-sequence-container {
    position: absolute;
    top: 0;
    left: 20px;
    /* 与容器padding保持一致 */
    right: 20px;
    /* 与容器padding保持一致 */
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 1;
    /* 确保容器在缩放时保持稳定 */
    box-sizing: border-box;
    /* 防止图片超出容器 */
    overflow: hidden;
    /* 图片紧密排列，填满整个容器 */
    justify-content: flex-start;
    flex-wrap: nowrap;
}

/**
 * 单个帧窗口样式
 * 
 * 🎨 视觉效果：
 * - 统一使用数字人预览图作为背景
 * - 封面模式确保图片完整显示
 * - 居中对齐获得最佳视觉效果
 * - 边框增强视觉边界
 * 
 * 📐 尺寸策略：
 * - 高度：80px（适中的预览尺寸）
 * - 宽度：flex-grow自适应填满剩余空间
 * - 最小圆角：2px（避免间隙问题）
 * - 边距：0（紧密排列无间隙）
 * 
 * 🎭 交互反馈：
 * - 默认半透明(0.8)提供层次感
 * - 悬停时完全不透明增强关注度
 * - 平滑过渡动画提升用户体验
 * 
 * 🖥️ 渲染优化：
 * - 图像渲染优化确保清晰度
 * - 抗锯齿处理在缩放时保持质量
 * - flex属性确保响应式适配
 */
.frame-window {
    height: 80px; // 用户要求的高度
    margin: 0; /* 移除边距，让图片紧密排列 */
    /* 背景图片现在通过内联样式动态设置 */
    border-radius: 2px; /* 减小圆角避免间隙 */
    border: 1px solid #ccc;
    flex-shrink: 0; /* 不允许缩小，保持计算出的宽度 */
    flex-grow: 1; /* 允许放大以填满剩余空间 */
    opacity: 0.8;
    transition: opacity 0.2s ease;
    /* 确保框架窗口尺寸稳定 */
    box-sizing: border-box;
    /* 防止图片在缩放时模糊 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;

    &:hover {
        opacity: 1;
    }
}

/* ========================================
   🏷️ 事件标记系统
   ======================================== */

/**
 * 事件标记层容器
 * 
 * 🎯 功能定位：
 * - 绝对定位覆盖图像序列层之上
 * - 与图像序列容器相同的边界范围
 * - z-index:2 确保标记在图片之上但低于播放指针
 * 
 * 📍 空间管理：
 * - 与容器内边距保持一致
 * - 100%高度充满时间轴容器
 * - 稳定的box-sizing确保尺寸一致性
 */
.events-layer {
    position: absolute;
    top: 0;
    left: 20px;
    /* 与容器padding保持一致 */
    right: 20px;
    /* 与容器padding保持一致 */
    height: 100%;
    z-index: 2;
    /* 确保事件层在缩放时位置准确 */
    box-sizing: border-box;
}

/**
 * 事件标记点样式
 * 
 * 🎨 视觉设计理念：
 * - 圆形设计符合用户对"点"的直觉认知
 * - 纯色背景提供清晰的视觉标识
 * - 绿色主题表达时间轴事件
 * - 居中对齐确保视觉平衡
 * 
 * 📍 定位系统：
 * - 绝对定位支持基于时间的动态位置
 * - translateX(-50%)实现精确水平居中
 * - 顶部10px偏移避免与边界冲突
 * 
 * 🎭 交互体验：
 * - 悬停时背景色变化提供即时反馈
 * - 1.1倍缩放增强视觉关注度
 * - 平滑过渡动画提升操作流畅性
 * - 指针光标明确可点击性
 * 
 * 🎨 状态管理：
 * - 选中状态：橙色边框+橙色背景
 * - 默认状态：绿色背景+透明边框
 * - 悬停状态：绿色半透明背景+缩放效果
 * 
 * 🖥️ 渲染优化：
 * - box-sizing确保尺寸计算准确
 */
.event-marker {
    position: absolute;
    top: 10px;
    transform: translateX(-50%);
    cursor: pointer;
    border: 2px solid transparent;
    background-color: #4CAF50;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    transition: all 0.2s ease;
    /* 确保事件标记在缩放时保持清晰 */
    box-sizing: border-box;

    &:hover {
        background-color: #45a049;
        transform: translateX(-50%) scale(1.2);
    }

    &.selected {
        border-color: #ff6b35;
        background-color: #ff6b35;
    }
}

/* ========================================
   🎯 播放指针系统
   ======================================== */

/**
 * 播放指针容器
 * 
 * 🎨 设计理念：
 * - 最高层级(z-index:3)确保始终可见可操作
 * - 拖拽友好的交互设计
 * - 实时位置跟随当前播放时间
 * 
 * 🖱️ 拖拽体验设计：
 * - grab光标提示可拖拽性
 * - 拖拽时grabbing光标+放大效果
 * - 悬停时增强阴影突出可交互性
 * - 平滑过渡动画提升操作质感
 * 
 * 📍 定位机制：
 * - 绝对定位支持动态位置计算
 * - translateX(-50%)确保指针居中对齐
 * - 100%高度覆盖整个时间轴
 * 
 * 🎭 状态反馈：
 * - 普通状态：标准尺寸+轻微阴影
 * - 悬停状态：保持尺寸+增强阴影
 * - 拖拽状态：1.1倍放大+最强阴影
 * 
 * 🔧 性能优化：
 * - 硬件加速确保拖拽流畅性
 * - 合理的过渡时间平衡性能和体验
 * - 精确的定位计算减少重绘
 */
.playhead-container {
    position: absolute;
    top: 0;
    height: 100%;
    transform: translateX(-50%);
    pointer-events: auto; // 启用指针事件以支持拖动
    z-index: 3;
    display: block;
    cursor: grab; // 显示可拖动的手型光标
    /* 确保播放指针在缩放时位置精确 */
    box-sizing: border-box;
    transition: transform 0.1s ease; // 添加平滑过渡效果

    // 拖动状态样式
    &.dragging {
        cursor: grabbing; // 拖动时显示抓取光标
        transform: translateX(-50%) scale(1.1); // 拖动时略微放大

        .playhead-triangle {
            filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.7)); // 拖动时增强阴影
        }

        .playhead-bar {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5)); // 拖动时增强阴影
        }
    }

    // 悬停效果
    &:hover:not(.dragging) {
        transform: translateX(-50%); // 悬停时轻微放大

        .playhead-triangle {
            filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.6));
        }
    }
}

/**
 * 播放指针三角形头部
 * 
 * 🎨 几何设计：
 * - CSS三角形：使用边框技巧创建向下箭头
 * - 尺寸：8px左右边框 + 13px顶部边框
 * - 对称设计确保视觉平衡
 * 
 * 📍 定位策略：
 * - 绝对定位相对于指针容器
 * - 顶部9px偏移适应时间轴高度
 * - translateX(-50%)确保水平居中
 * 
 * 🎭 视觉效果：
 * - 黑色填充提供清晰对比
 * - 投影效果增强立体感和层次
 * - 状态相关的阴影变化
 * 
 * 🖥️ 渲染品质：
 * - box-sizing确保尺寸计算准确
 * - 过渡动画提升视觉流畅性
 * - 锐利边缘确保三角形清晰
 */
.playhead-triangle {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 13px solid #000000;
    border-bottom: 0;
    position: absolute;
    top: 9px; // 恢复到适应原始图片高度的位置
    left: 50%;
    transform: translateX(-50%);
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5));
    /* 确保三角形在缩放时保持锐利 */
    box-sizing: border-box;
    transition: filter 0.2s ease; // 添加阴影过渡效果
}

/**
 * 播放指针垂直线
 * 
 * 🎨 设计特色：
 * - 细线设计：2px宽度提供精确的时间指示
 * - 黑色填充与三角形保持视觉一致性
 * - 80px高度与帧窗口高度匹配
 * 
 * 📍 精确定位：
 * - 从三角形底部开始(top:18px)
 * - 水平居中对齐指针中心线
 * - 垂直贯穿整个图像序列区域
 * 
 * 🎭 视觉层次：
 * - 基础投影提供轻微立体感
 * - 状态相关的阴影强度变化
 * - 与三角形同步的过渡效果
 * 
 * 🔧 技术细节：
 * - box-sizing确保线条宽度一致
 * - 硬件加速优化动画性能
 * - 过渡动画增强用户体验
 */
.playhead-bar {
    position: absolute;
    top: 18px; // 恢复到从三角形底部开始的位置
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 80px; // 匹配图片高度
    background-color: #000000;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    /* 确保垂直线在缩放时保持一致 */
    box-sizing: border-box;
    transition: filter 0.2s ease; // 添加阴影过渡效果
}


</style>