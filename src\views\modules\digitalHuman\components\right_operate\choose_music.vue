<template>
    <div class="right_operate_drive_music">
        <div class="right_operate_drive_music_title">
            背景音乐
        </div>
        <div class="right_operate_drive_music_content" @click="choose_music" v-if="current_music">
                <div class="right_operate_drive_music_content_img" :style="gradientStyle(current_music.info.color, current_music.info.color1,current_music.info.color2)">
                </div>
                <div class="right_operate_drive_music_content_text">
                    <h4>{{ current_music.info.materialName||current_music.info.musicName }}</h4>
                    <span>{{ current_music.info.time }}</span>
                </div>
            <div class="right_operate_drive_music_content_opeate">
                <div class="right_operate_drive_music_content_opeate_volume" @click.stop="set_volume" ref="set_volume_ref">
                    <img src="@/assets/images/digitalHuman/right_operate_drive_music_content_opeate_volume.svg" alt="">
                    <VolumeSlider
                        v-model:modelValue="volume_value"
                        v-model:visible="showVolumeSlider"
                        :excludeRefs="[set_volume_ref]"
                        @update:modelValue="handleVolumeChange"
                        position="top" 
                        />
                        <!-- 这里可以是 bottom, top, left, right -->
                </div>
                <div class="right_operate_drive_music_content_opeate_delete" @click.stop="handleDelete">
                    <img src="@/assets/images/digitalHuman/right_operate_drive_music_content_opeate_delete.svg" alt="">
                       
                </div>
                <div class="right_operate_drive_music_content_opeate_more">
                    <img src="@/assets/images/digitalHuman/right_operate_drive_text_captions_choose_dub_character_more.svg"
                        alt="">
                </div>
            </div>
            
        </div>
        <div class="right_operate_drive_music_content_empty" v-else @click="choose_music">
            <div class="right_operate_drive_music_content_empty_img">
                <img src="@/assets/images/digitalHuman/right_operate_add.svg" alt="">
            </div>
            <span>添加音乐</span>
        </div>
        <setMusic ref="set_music_ref" @choose="choose" ></setMusic>
      
    </div>
</template>
<script setup>
import { ref,defineExpose } from 'vue'
import setMusic from '@/views/modules/digitalHuman/components/right_operate/set_music1.vue'
import VolumeSlider from '@/views/modules/digitalHuman/components/right_operate/volumeSlider.vue'
let current_music=ref(null)
let set_music_ref = ref(null)
let gradientDivs = ref(null);
let volume_value=ref(80)
let set_volume_ref=ref(null)
let showVolumeSlider=ref(false)
let choose_music=()=>{
    set_music_ref.value.dialogVisible=true
    set_music_ref.value.init(current_music.value)
}
let choose=(val)=>{
    current_music.value=val
}
let hasInsertedKeyframes =ref(false);
let insertKeyframes = () => {
  if (hasInsertedKeyframes.value) return;
  let style = document.createElement('style');
  style.innerHTML = `
    @keyframes neonGradient {
      0% {
        background-position: 0% 100%;
      }
      25% {
        background-position: 50% 75%;
      }
      50% {
        background-position: 100% 0%;
      }
      75% {
        background-position: 50% 25%;
      }
      100% {
        background-position: 0% 100%;
      }
    }
  `;
  document.head.appendChild(style);
  hasInsertedKeyframes.value = true;
};
let gradientStyle = (color, color1, color2, duration = '10s') => {
  insertKeyframes();
  return {
    background: `linear-gradient(
      var(--angle, 0deg),
      ${color} 0%,
      ${color} 25%,
      ${color1} 40%,
      ${color1} 60%,
      ${color2} 75%,
      ${color2} 100%
    )`,
    backgroundSize: '300% 300%',
    animation: `neonGradient ${duration} cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  };
};
let set_volume=()=>{
    showVolumeSlider.value=true
}
let handleVolumeChange=(e)=>{
    volume_value.value=e
}
let handleDelete=()=>{
    current_music.value=null
}
defineExpose({
    current_music
})
</script>
<style lang="scss" scoped>
.right_operate_drive_music {
    width: 100%;
    padding: 12px 18px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    .right_operate_drive_music_title {
        font-size: 14px;
        line-height: 42px;
        color: #000000;
    }
    .right_operate_drive_music_content {
        display: flex;
        align-items: center;
        padding: 8px;
        width: 290px;
        height: 70px;
        background: #F6F7FB;
        border-radius: 4px;
        box-sizing: border-box;
        cursor: pointer;
        .right_operate_drive_music_content_img {
            width: 54px;
            height: 54px;
            border-radius: 4px;
            overflow: hidden;
            margin-right: 10px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .right_operate_drive_music_content_text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            h4 {
                font-size: 14px;
                line-height: 22px;
                color: #000;
                margin: 0;
                margin-bottom: 3px;
                font-weight: normal;
            }
            span {
                font-size: 12px;
                line-height: 22px;
                color: rgba(0, 0, 0, 0.45);
            }
        }
        .right_operate_drive_music_content_opeate{
            margin-left: auto;
            display: flex;
            align-items: center;
            .right_operate_drive_music_content_opeate_volume{
                width: 14px;
                height: 14px;
                margin-right: 12px;
                cursor: pointer;
                position: relative;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .right_operate_drive_music_content_opeate_delete{
                width: 14px;
                height: 14px;
                margin-right: 12px;
                cursor: pointer;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .right_operate_drive_music_content_opeate_more {
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
        }
        
    }
    .right_operate_drive_music_content_empty{
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        padding: 8px;
        width: 100%;
        background: #F6F7FB;
        border: 1px dashed rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        margin-bottom: 12px;
        cursor: pointer;
        .right_operate_drive_music_content_empty_img{
            width: 26px;
            height: 26px;
            margin-right: 6px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        span{
            font-size: 14px;
            line-height: 22px;
            color: #858587;
        }
    }
}
</style>