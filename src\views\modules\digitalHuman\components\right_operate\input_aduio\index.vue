<template>
    <div class="right_operate_drive_aduio_captions">
        <uploadAduio ref="upload_aduio_ref" @emit_data="emit_data"></uploadAduio>
        <chooseMusic ref="choose_music_ref" ></chooseMusic>
    </div>
</template>
<script setup>
import { ref,defineExpose,watch,inject } from "vue"
import uploadAduio from "@/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio.vue"
import chooseMusic from '@/views/modules/digitalHuman/components/right_operate/choose_music.vue'
let digital_human_right_option = inject('digital_human_right_option');
let upload_aduio_ref=ref(null)
let choose_music_ref=ref(null)
let input_aduio_obj=ref({})
let get_data=()=>{
    input_aduio_obj.value={
        upload_aduio:upload_aduio_ref.value.current_upload_aduio,
        choose_music:choose_music_ref.value.current_music
    }
}  
let set_data=(data)=>{
    upload_aduio_ref.value.current_upload_aduio=data.upload_aduio
    upload_aduio_ref.value.open_captions=data.open_captions
    choose_music_ref.value.current_music=data.choose_music
    upload_aduio_ref.value.set_aduio(data.upload_aduio)
}
let emit_data=()=>{
   digital_human_right_option({
        type:'aduio_captions',//text_captions输入文本   aduio_captions音频驱动
        aduio_data:upload_aduio_ref.value?.current_upload_aduio,//接口返回的音频数据  aduio对象（url：音频url   volume：设置的音量） textarea接口返回的文本数据
        open_captions:upload_aduio_ref.value?.open_captions,//是否开启字幕
        choose_music:choose_music_ref.value?.current_music,//选择的背景音乐  具体数据bgm_url当前选择的背景音乐路径   info当前背景音乐接口返回所有数据   current_nav切换类型1推荐音乐2我的音乐  current_classify当current_nav为1时下方的具体类型
        // 新增：详细的字幕数据（包含时间信息）
        subtitle_data_with_time: upload_aduio_ref.value?.current_upload_aduio?.subtitle_data || [],
        audioJson:{
            type: "audio",//文本传tts，音频驱动传audio
            tts: {
                text: [],
                speed: 0, //语速
                audio_man: "",
                pitch:0 //语调
            },
            wav_url:upload_aduio_ref.value?.current_upload_aduio?.aduio?.url||'',
            wav_name:upload_aduio_ref.value?.current_upload_aduio?.aduio?.name||'',
            wav_text:upload_aduio_ref.value?.current_upload_aduio?.textarea||'',
            volume: Math.round(upload_aduio_ref.value?.current_upload_aduio?.aduio?.volume)||'',//音量
            language: "cn",
            voiceId:'',
            voicePerson:'',//音色名称
            voiceImg:''
        }
    }) 
}
watch(() => ({ a: upload_aduio_ref.value?.current_upload_aduio, b:choose_music_ref.value?.current_music}), (newVal, oldVal) => {
    emit_data()
  },
  { deep: true } // 如果监听对象内部深层变化需要加deep
);
defineExpose({
    input_aduio_obj,
    get_data,
    set_data,
    // 🔧 新增：同步字幕开关状态的方法
    syncCaptionsState: (isEnabled) => {
        try {
            // 更新音频上传组件的字幕开关状态
            if (upload_aduio_ref.value && upload_aduio_ref.value.syncCaptionsState) {
                upload_aduio_ref.value.syncCaptionsState(isEnabled);
            } else if (upload_aduio_ref.value) {
                // 备选方案：直接设置字幕开关状态
                upload_aduio_ref.value.open_captions = isEnabled;
                
                // 触发数据更新，确保状态同步到父组件
                emit_data();
            }
        } catch (error) {
            // 同步失败，保持原有状态
        }
    }
})
</script>
<style lang="scss" scoped>
.right_operate_drive_aduio_captions{
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1;
}
</style>
