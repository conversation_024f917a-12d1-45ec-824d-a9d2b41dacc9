<template>
  <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio">
    <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_text">
      <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_name">
        <span>{{aduio_obj.name}}</span>
        <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_detele_img" @click="handleDelete">
          <img
            src="@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_content_aduio_detele.svg"
            alt="" />
        </div>
      </div>
      <el-popover popper-class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover" title="声音控制"
        placement="top-end" v-model:visible="showPopover" ref="popoverRef" width="290" trigger="click" append-to="#app">
        <!-- 弹窗内容 -->
          <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_slider">
            <span class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_slider_text">音量</span>
            <el-slider v-model="sound_control"   :max="100"/>
          </div>
        
        <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_btns">
          <el-button @click="resetVolume" class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_btns_reset">重置</el-button>
          <el-button @click="applyVolume" class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_btns_apply">应用</el-button>
        </div>

        <!-- 触发元素 -->
        <template #reference>
          <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_sound">
            <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_sound_img" ref="soundImgRef"
              @click.stop="togglePopover">
              <img
                src="@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_content_aduio_sound.svg"
                alt="" />
            </div>
            <span>声音控制</span>
          </div>
        </template>
      </el-popover>

    </div>
    <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_content">
      <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_control" @click="togglePlay">
        <img :src="aduio_obj.isPlaying ? playImg : pauseImg" alt="" />
      </div>
      <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_time">
        {{ formatTime(aduio_obj.currentTime) }}/{{ formatTime(aduio_obj.duration) }}
      </div>
      <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_slider">
        <el-slider v-model="aduio_obj.currentTime" :max="aduio_obj.duration" :show-tooltip="false"
          @input="handleProgressChange" @change="handleProgressChangeEnd" />
      </div>
      <div class="right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_volume"
        @click="toggleVolumeSlider" ref="volumeBtnRef">
        <img
          src="@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_volume.svg"
          alt="" />
         <VolumeSlider
          v-model:modelValue="aduio_obj.try_volume"
          v-model:visible="showVolumeSlider"
          :excludeRefs="[volumeBtnRef]"
          @update:modelValue="handleVolumeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick,defineEmits,defineExpose, onActivated } from 'vue'
import { ElMessage } from 'element-plus'
import pauseImg from "@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_control_pause.svg"
import playImg from "@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_control_play.svg"
import VolumeSlider from '@/views/modules/digitalHuman/components/right_operate/volumeSlider.vue'
let aduio_obj = ref({
  url: '',
  try_volume: 80,
  volume: 80,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  audioElement: null,
  name:""
})
let emit=defineEmits(['deleteMusic'])
let showVolumeSlider = ref(false)
let showPopover = ref(false)
let volumeRef = ref(null) // 音量滑块容器
let volumeBtnRef = ref(null) // 音量按钮
let soundImgRef = ref(null) // 声音控制图标
let popoverRef = ref(null) // 弹出框容器
let sound_control=ref()//声音控制
let formatTime = (time) => {
  let minutes = Math.floor(time / 60)
  let seconds = Math.floor(time % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}
let handleDelete = () => {
    aduio_obj.value={}
    handleDeleteMusic()
    ElMessage.success('删除成功')
    emit('deleteMusic')
    console.log('删除音频')
}
let togglePlay = () => {
  if (!aduio_obj.value.audioElement) return
  if (aduio_obj.value.isPlaying) {
    aduio_obj.value.audioElement.pause()
  } else {
    aduio_obj.value.audioElement.play()
  }
  aduio_obj.value.isPlaying = !aduio_obj.value.isPlaying
}
let handleProgressChange = (val) => {
  if (!aduio_obj.value.audioElement) return
  aduio_obj.value.audioElement.currentTime = val
  aduio_obj.value.currentTime = val
}
let handleProgressChangeEnd = (val) => {
  if (!aduio_obj.value.audioElement) return
  aduio_obj.value.audioElement.currentTime = val
  aduio_obj.value.currentTime = val
}
// 滑块改变时更新 try_volume 并同步音频音量
let handleVolumeChange = (val) => {
  aduio_obj.value.try_volume = val
  if (aduio_obj.value.audioElement) {
    aduio_obj.value.audioElement.volume = val / 100
  }
}
// 初始化音频
let setupAudio = () => {

   // 清理旧音频对象
  if (aduio_obj.value.audioElement) {
    aduio_obj.value.audioElement.pause()
    aduio_obj.value.audioElement.src = ''
    aduio_obj.value.audioElement = null
  }

  const audioEl = new Audio(aduio_obj.value.url)
  audioEl.volume = aduio_obj.value.volume / 100
  aduio_obj.value.audioElement = audioEl

  audioEl.addEventListener('canplay', () => {
    aduio_obj.value.duration = audioEl.duration || 0
  })

  audioEl.addEventListener('timeupdate', () => {
    aduio_obj.value.currentTime = audioEl.currentTime
  })

  audioEl.addEventListener('ended', () => {
    aduio_obj.value.isPlaying = false
    aduio_obj.value.currentTime = 0
  })
  if(aduio_obj.value.volume){
      sound_control.value = aduio_obj.value.volume
  }
}
// 切换音量滑块显示
let toggleVolumeSlider = () => {
  showVolumeSlider.value = !showVolumeSlider.value
  if (showVolumeSlider.value) {
    showPopover.value = false
  }
}
// 切换弹出框显示
let togglePopover = () => {
  showPopover.value = !showPopover.value
  if (showPopover.value) {
    showVolumeSlider.value = false
  }
}
//判断点击事件是否发生在指定元素集合内
let isClickInsideElements = async (event, elements) => {
  await nextTick()
  let target = event.target
  for (let el of elements) {
    // 如果是 Vue ref，取 .value
    let dom = el?.$el ?? (el?.value ?? el) // 兼容 Vue 组件实例或 ref 或直接 DOM
    if (dom && dom.contains && dom.contains(target)) {
      return true
    }
  }
  return false
}
let onClickOutside = async (event) => {
  // let volumeElements = [volumeBtnRef.value, volumeRef.value]
  let popoverElements = [soundImgRef.value, popoverRef.value]
  // let insideVolume = await isClickInsideElements(event, volumeElements)
  let insidePopover = await isClickInsideElements(event, popoverElements)

  // console.log(!insideVolume, !insidePopover, 444)
  // if (!insideVolume) {
  //   showVolumeSlider.value = false
  // }
  if (!insidePopover) {
    showPopover.value = false
  }
}
let destroyAudio = () => {
  if (aduio_obj.value.audioElement) {
    aduio_obj.value.audioElement.pause()
    aduio_obj.value.audioElement.src = ''
    aduio_obj.value.audioElement = null
  }
}
// watch(() => aduio_obj.try_volume, (newVal) => {
//   aduio_obj.volume = newVal
//   if (aduio_obj.audioElement) {
//     aduio_obj.audioElement.volume = newVal / 100
//   }
// })
let resetVolume = () => {
  sound_control.value= aduio_obj.value.volume
  ElMessage.success('重置成功')
  setTimeout(()=>{
     showPopover.value=false
  },500)
 }
let applyVolume = () => { 
  aduio_obj.value.volume=sound_control.value
  showPopover.value=false
}
let  handleDeleteMusic=()=>{
  if (aduio_obj.value.audioElement) {
    // 停止播放
    aduio_obj.value.audioElement.pause()
    aduio_obj.value.audioElement.currentTime = 0
  }
  
  // 删除音乐数据，具体根据你的数据结构调整
  // 这里示例清空音频对象的相关属性
  aduio_obj.value.try_volume = 0
  aduio_obj.value.audioElement = null
  aduio_obj.value.musicInfo = null // 假设有音乐信息字段
  // 如果是从列表删除，调用对应删除逻辑
}
onMounted(() => {
  // document.addEventListener('click', onClickOutside)
})

onBeforeUnmount(() => {
  destroyAudio()
  // document.removeEventListener('click', onClickOutside)
})
defineExpose({
  aduio_obj,
  setupAudio
})
</script>
<style lang="scss">
.right_operate_drive_aduio_captions_upload_aduio_content_aduio {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border: 1px solid #EFEFF1;
  border-radius: 5px;
  padding: 12px;
  width: 100%;
  margin-bottom: 10px;
  .right_operate_drive_aduio_captions_upload_aduio_content_aduio_text {
    margin-bottom: 6px;
    display: flex;
    align-items: center;

    .right_operate_drive_aduio_captions_upload_aduio_content_aduio_name {
      display: flex;
      align-items: center;
      flex: 1;
      span {
        margin-right: 4px;
        font-size: 12px;
        line-height: 22px;
        color: #000000;
      }

      .right_operate_drive_aduio_captions_upload_aduio_content_aduio_detele_img {
        cursor: pointer;
        width: 14px;
        height: 14px;
        margin-top: -4px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .right_operate_drive_aduio_captions_upload_aduio_content_aduio_sound {
      margin-left: auto;
      display: flex;
      align-items: center;

      .right_operate_drive_aduio_captions_upload_aduio_content_aduio_sound_img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
        cursor: pointer;
         margin-top: -4px;
        img {
          width: 100%;
          height: 100%;
        }
      }

      span {
        font-size: 12px;
        line-height: 22px;
        color: #353D49;
      }
    }
  }

  .right_operate_drive_aduio_captions_upload_aduio_content_aduio_content {
    display: flex;
    align-items: center;

    .right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_control {
      width: 14px;
      height: 16px;
      margin-right: 6px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_time {
      margin-right: 6px;
      font-size: 12px;
      line-height: 22px;
      color: #000000;
    }

    .right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_slider {
      margin-right: 6px;
      flex: 1;

      .el-slider {
        .el-slider__runway{
          background-color: rgba(0, 0, 0, 0.2);
          height: 4px;

          .el-slider__bar {
            height: 100%;
            background-color: #0AAF60;
          }

          .el-slider__button-wrapper {
            width: 7px;
            height: 7px;
            top: 50%;
            transform: translateY(-50%);

            .el-slider__button {
              width: 100%;
              height: 100%;
              background-color: #0AAF60;
              border: none;


            }
          }
        }
      }
    }

    .right_operate_drive_aduio_captions_upload_aduio_content_aduio_content_volume {
      margin-left: auto;
      width: 16px;
      height: 16px;
      cursor: pointer;
      position: relative;

      img {
        width: 100%;
        height: 100%;
      }

      .right_operate_drive_aduio_captions_upload_aduio_content_aduio_volume_slider {
        position: absolute;
        top: 100%; // 在按钮上方
        left: 50%;
        transform:translateX(-50%) ;
        width: 40px; // 你可以根据需求调节宽度，变长一些
        height: 160px;
        background: #fff; // 背景色，避免遮挡
        padding: 4px 8px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 10;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 6px 0;
        box-sizing: border-box;
        .right_operate_drive_aduio_captions_upload_aduio_content_aduio_volume_slider_text {
          font-size: 12px;
          line-height: 24px;
          color: #000000;
          text-align: center;
          display: inline-block;
          margin-bottom: 10px;
        }
        .el-slider {
          width: 6px;
          flex: 1;

          .el-slider__runway{
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: center;
            width: 6px;
            margin: 0;
            padding: 0;

            .el-slider__bar {
              background-color: #0AAF60;
              height: 100%;
            }

            .el-slider__button-wrapper {
              width: 14px;
              height: 14px;
              left: 50%;
              transform: translateX(-50%);

              .el-slider__button {
                width: 100%;
                height: 100%;
                background-color: #0AAF60;
                border: none;
              }
            }
          }
        }
        .right_operate_drive_aduio_captions_upload_aduio_content_aduio_volume_slider_label{
          font-size: 12px;
          line-height: 24px;
          color: #000000;
          text-align: center;
          display: inline-block;
        }
      }
    }
  }
}
.el-popper{
  &.is-light{
    &.right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover{
        padding: 12px;
        box-sizing: border-box;
        background: #FFFFFF;
        /* 灰底色 */
        border: 1px solid #EFEFF1;
        box-shadow: 0px 1px 9px rgba(0, 0, 0, 0.05);
        border-radius: 5px;
        .el-popover__title{
          font-size: 14px;
          line-height: 22px;
          color: #353D49;
          margin-bottom: 6px;
        }
        .right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_slider{
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          span{
            font-size: 12px;
            line-height: 22px;
            color: #000000;
            margin-right: 6px;
          }
          .el-slider{
            flex: 1;
            height: 10px;
            .el-slider__runway{
              height: 4px;
              background-color: rgba(0, 0, 0, 0.2);
              display: flex;
              justify-content: center;
              margin: 0;
              padding: 0;
              width: 100%;
              .el-slider__bar {
                background-color: #0AAF60;
                height: 100%;
              }

              .el-slider__button-wrapper {
                width: 10px;
                height: 10px;
                top: 50%;
                transform: translateY(-50%);

                .el-slider__button {
                  width: 100%;
                  height: 100%;
                  background-color: #0AAF60;
                  border: none;
                }
              }
            }
          }
        }
        .right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_btns{
          padding-top: 16px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .el-button{
            padding: 5px 14px;
            box-sizing: border-box;
            border-radius: 4px;
            span{
              font-size: 14px;
              line-height: 22px;
              letter-spacing: -0.01px;
            }
            &+.el-button{
              margin-left: 8px;
            }
            &.right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_btns_reset{
              background-color: #D3D3D2;
              span{
                color: #000;
              }
            }
           &.right_operate_drive_aduio_captions_upload_aduio_content_aduio_popover_btns_apply{
             background-color: #0AAF60;
              span{
                color: #fff;
              }
           }
          }
        }
        .el-popper__arrow{
            display: none;
        }
    }
  }
}
</style>