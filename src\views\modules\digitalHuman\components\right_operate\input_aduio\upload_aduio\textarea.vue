<template>
    <div class="right_operate_drive_aduio_captions_upload_aduio_content_textarea">
        <div class="right_operate_drive_aduio_captions_upload_aduio_content_textarea_title">
            <span>文本</span>
            <div class="right_operate_drive_aduio_captions_upload_aduio_content_textarea_title_copy" @click="copy">
                <img src="@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_content_textarea_title_copy.svg" alt="">
            </div>
        </div>
            <el-scrollbar always style="flex: 1;display: flex;flex-direction: column;">
                <div class="right_operate_drive_aduio_captions_upload_aduio_content_textarea_content">
                    {{ textarea }}
                </div>
            </el-scrollbar>
        
    </div>
</template>
<script setup>
import { ref,defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
let textarea = ref('')
let copy = async () => {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    try {
      await navigator.clipboard.writeText(textarea.value);
      ElMessage.success('复制成功');
    } catch (err) {
      ElMessage.error('复制失败，请手动复制');
      console.error('复制失败:', err);
    }
  } else {
    // 传统复制方法
    try {
      const textareaEl = document.createElement('textarea');
      textareaEl.value = textarea.value;
      textareaEl.style.position = 'fixed';  // 防止页面滚动
      textareaEl.style.opacity = '0';       // 隐藏元素
      document.body.appendChild(textareaEl);
      textareaEl.select();
      const successful = document.execCommand('copy');
      document.body.removeChild(textareaEl);
      if (successful) {
        ElMessage.success('复制成功');
      } else {
        ElMessage.error('复制失败，请手动复制');
      }
    } catch (err) {
      ElMessage.error('复制失败，请手动复制');
      console.error('复制失败:', err);
    }
  }
}

defineExpose({
    textarea
})
</script>
<style lang="scss" scoped>
.right_operate_drive_aduio_captions_upload_aduio_content_textarea{
    margin-bottom: 12px;
    box-sizing: border-box;
    width:100%;
    border: 1px solid #EFEFF1;
    border-radius: 5px;
    padding: 12px;
    padding-bottom: 6px;
    display: flex;
    flex-direction: column;
    height: 200px;
    min-height: 0;
    overflow: hidden;
    .right_operate_drive_aduio_captions_upload_aduio_content_textarea_title{
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        span{
            font-size: 12px;
            line-height: 22px;
            color: #000000;
        }
        .right_operate_drive_aduio_captions_upload_aduio_content_textarea_title_copy{
            margin-left: auto;
            width: 16px;
            height: 16px;
            cursor: pointer;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .el-scrollbar{
        flex: 1;
        display: flex; /* 新增 */
        flex-direction: column; /* 新增 */
        min-height: 0; /* 新增 */
        overflow: visible; /* 你想要滚动条外溢 */
        ::v-deep(.el-scrollbar__bar) {
            right: -9px;
        }
        ::v-deep(.el-scrollbar__thumb) {
            background-color: #0AAF60;
            will-change: transform;
            opacity: 1;
        }
        ::v-deep(.el-scrollbar__view){
            width: 100%;
            height: 100%;
            transform: translateZ(0);
        }
        ::v-deep(.el-scrollbar__wrap) {
            padding-right: 9px; /* 给滚动条留空间 */
            /* 取消硬件加速，避免模糊 */
            transform: none !important;
            will-change: scroll-position;
            -webkit-overflow-scrolling: touch;
        }
        .right_operate_drive_aduio_captions_upload_aduio_content_textarea_content{
            width: 100%;
            font-size: 12px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.8);
        }
    }
    

}
</style>