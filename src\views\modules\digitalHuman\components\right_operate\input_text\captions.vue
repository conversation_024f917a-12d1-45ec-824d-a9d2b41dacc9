<template>
    <div class="right_operate_drive_text_captions_textarea">
        <el-scrollbar style="flex:1">
            <div ref="editorRef" contenteditable="true" @input="debouncedInputHandler"
                id="editor" placeholder="粘贴或输入文本内容，限1500个字内" class="right_operate_drive_text_captions_textarea_editor">
            </div>
            <div class="word-count">{{ currentLength }} / 1500</div>
        </el-scrollbar>
        <div class="right_operate_drive_text_captions_textarea_switch">
            <el-switch v-model="open_captions" size="large" style="--el-switch-on-color: #0AAF60; --el-switch-off-color: #F1F2F4" :width="40" active-text="字幕开关"/>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive,defineExpose, watch, inject,defineEmits } from 'vue'
// 最大字数限制
let max_length = ref(1500)
// 当前字数
let currentLength = ref(0)
// 获取编辑器 DOM 引用
let editorRef = ref(null)
let open_captions=ref(true)  // 🎯 优化：字幕开关默认设置为开启状态
let active_color=ref('#0AAF60')
let textInfo=ref('')
let emit = defineEmits(['emit_data'])

// 防抖函数实现
let debounce=(fn, delay = 300)=>{
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}
// 输入事件处理函数
let handleInput=()=>{
    if (!editorRef.value) return
    let text = editorRef.value.innerText || ''

    if (text.length > max_length.value) {
        text = text.slice(0, max_length.value)
        editorRef.value.innerText = text
        placeCaretAtEnd(editorRef.value)
    }
    // 严格限制 currentLength 最大为 max_length.value
    currentLength.value = Math.min(text.length, max_length.value)
    gettxt()
}

// 防抖包装输入事件处理函数
let debouncedInputHandler = debounce(handleInput, 300)

// 将光标移动到内容末尾的辅助函数
let placeCaretAtEnd=(el)=>{
  el.focus()
  if (
    typeof window.getSelection != 'undefined' &&
    typeof document.createRange != 'undefined'
  ) {
    let range = document.createRange()
    range.selectNodeContents(el)
    range.collapse(false)
    let sel = window.getSelection()
    sel.removeAllRanges()
    sel.addRange(range)
  } else if (typeof document.body.createTextRange != 'undefined') {
    let textRange = document.body.createTextRange()
    textRange.moveToElementText(el)
    textRange.collapse(false)
    textRange.select()
  }
}
let gettxt=()=>{
	const originalDiv = document.getElementById('editor');
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	textInfo.value = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
		return `<#${parseInt(val) / 1000}#>`
	})
	textInfo.value = textInfo.value.replace(/\n/g, '');
	let parts = textInfo.value.split(/(\[sound:.*?\])+/);
	let finalText = parts.filter(part => part.trim() !== '')
	let length = 0
	for (let i = 0; i < finalText.length; i++) {
		if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
			if (finalText[i].length <= 1500) {
				length += finalText[i].length
			} else {
				finalText[i] = finalText[i].substring(0, 1500 - length);
			}
		}
	}
	textInfo.value = finalText.join('')

	// 🔧 新增：文本内容变化时发射事件，通知父组件更新字幕显示
	emit('emit_data')
}
let updateEditorContent = () => {
  if (!editorRef.value) return;
  editorRef.value.innerText = textInfo.value;
  placeCaretAtEnd(editorRef.value);
}
// 监听字幕开关状态变化，实时控制字幕显示
watch(open_captions, (newValue) => {
    emit('emit_data')
}, { immediate: false }); // 不立即执行，只在用户操作时触发

defineExpose({
    textInfo,
    open_captions,
    updateEditorContent
})
</script>
<style lang="scss" scoped>
    .right_operate_drive_text_captions_textarea{
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding: 0 18px;
        flex: 1;
        overflow: hidden;
        min-height: 0;
        .el-scrollbar{
            border: 1px solid #EFEFF1;
            border-radius: 5px;
            position: relative;
            padding-bottom:26px;
            flex: 1;
            display: flex; /* 新增 */
            flex-direction: column; /* 新增 */
            min-height: 0; /* 新增 */
            overflow: visible; /* 你想要滚动条外溢 */
            .right_operate_drive_text_captions_textarea_editor{
                width: 100%;
                box-sizing: border-box;
                padding: 6px 12px;
                font-size: 12px;
                line-height: 20px;
                color: #353D49;
                user-select: text;
                outline: none;
                word-wrap: break-word;
                word-break: break-all;
                -webkit-user-modify: read-write-plaintext-only;
                // 兼容placeholder
                &:empty:before {
                    content: attr(placeholder);
                    color: #bbb;
                }
            }
            .word-count{
                position: absolute;
                bottom:6px;
                right: 12px;
                z-index: 1;
                font-size: 12px;
                line-height: 20px;
                color: rgba(0, 0, 0, 0.45);

            }
             ::v-deep(.el-scrollbar__thumb) {
                background-color: #0AAF60;
                will-change: transform;
                opacity: 1;
            }
        }
        .right_operate_drive_text_captions_textarea_switch{
            display: flex;
            align-items: center;
            .el-switch{
                display: flex;
                align-items: center;
                height: 44px;
                ::v-deep(.el-switch__core){
                    min-width: auto;
                    height: 20px;
                    .el-switch__action{
                        width: 16px;
                        height: 16px;
                       
                    }
                }
                 ::v-deep(.el-switch__label){
                    margin-left: 8px;
                    height: 22px;
                    span{
                        font-size: 13px;
                        line-height: 22px;
                        color: #303133;
                    }
                    &.is-active{
                        color: #303133;
                    }
                }
                &.is-checked{
                     ::v-deep(.el-switch__core){
                         .el-switch__action{
                            left: unset;
                            right: 2px;
                         }
                    }
                }
            }
        }
        
    }
</style>