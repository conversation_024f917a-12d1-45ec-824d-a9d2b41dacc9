<template>
  <el-scrollbar style="height: 513px;" ref="scroll" always>
    <div class="dubbing_selection_dialog_content_list" v-loading="loading">
      <template v-if="list.length>0">
          <div class="dubbing_selection_dialog_content_item" v-for="(item, index) in list" :key="index" :data-voice-name="item.voiceName"
            @click="selectSoundItem(item, index)" :class="item.voiceName == SoundItemId?'current':''" :ref="setRef(index)">
            <div class="dubbing_selection_dialog_content_item_grade"  v-if="!(current_nav.option_active==2&&current_nav.selectetMycurrent=='克隆音色')">
              <img :src="item.membershipGrade == 'SVIP' ? zhenxiang : jingpin" alt="">
            </div>
          
            <div class="dubbing_selection_dialog_content_item_container">
              <div class="dubbing_selection_dialog_content_item_collect"  @click.stop="toggle_collect(item,index)" v-if="!(current_nav.option_active==2&&current_nav.selectetMycurrent=='克隆音色')">
                <img :src="item.bookmark == 1 ? collectImage : collectNoImage" alt="">
              </div>
              <div class="dubbing_selection_dialog_content_item_avatar">
                <img :src="item.avatarUrl" alt="" v-if="item.avatarUrl">

                <div class="dubbing_selection_dialog_content_item_aduio" @click="play_sound( index,item)"  v-if="!(current_nav.option_active==2&&current_nav.selectetMycurrent=='克隆音色')">
                  <img :src="item.isPlaying ? pauseImage : playImage" alt="">
                </div>
              </div>
              <div class="dubbing_selection_dialog_content_item_nick" v-html="getDisplayName(item)"></div>
              <div class="dubbing_selection_dialog_content_item_emotion" v-html="getDisplayEmotionTag(item)"></div>
            </div>
          </div>
       </template>
      <el-empty description="暂无相关音色" v-else />
    </div>
  </el-scrollbar>
</template>
<script setup>
import { defineExpose,getCurrentInstance, nextTick, reactive, ref } from 'vue'
import collectImage from '@/assets/images/commercialImages/collectImage.svg'
import collectNoImage from '@/assets/images/commercialImages/collectNoImage.svg'
import pauseImage from '@/assets/images/digitalHuman/dubbing_selection_list_pause.svg'
import playImage from '@/assets/images/digitalHuman/dubbing_selection_list_play.svg'
import { ElMessage } from 'element-plus'
import { useloginStore } from '@/stores/login'
import { bookmarkToggle } from '@/api/soundStore.js'
import zhenxiang from '@/assets/images/aiImages/zhenxiang.svg';
import jingpin from '@/assets/images/aiImages/jingpin.svg';
let list = ref([])
let loading = ref(false)
let currentPromise = null; // 用于跟踪当前的 Promise
let globalAudio = new Audio();
let cancelToken = ref(null)
let input_search = ref('')
let targetElements = ref([]);
// 点击列表中某一项
let SoundItemId = ref('')
let choose_timbre = ref({})
//当前语言
let current_language=ref('auto')
//当前情绪
let current_emotion=ref('neutral')
let current_source=ref(1)//1 普通音色 2我的克隆
let loginStore = useloginStore() 
let { proxy } = getCurrentInstance();
let currentAudio = ref(null);
let scroll=ref(null)
let current_nav=ref({})
let close_aduio = () => {
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value.currentTime = 0;
    currentAudio.value = null;
  }
}
// 定义函数，传入情绪，返回对应链接
let getAudioUrl = (data, emotion) => {
  if (!emotion || typeof emotion !== 'string') {
    return null;
  }
  let trimmedEmotion = emotion.trim();
  return data[trimmedEmotion] || null;
}
let init = async (data) => {
  
  if (data.length == 0 || !data) {
    list.value = [];
    loading.value = false;
    return;
  };

  if (currentPromise) {
    currentPromise.cancel();
  }


  data.map((item) => {
    let audioMap;
    try {
      audioMap = JSON.parse(item.audioUrl);
      item.url = getAudioUrl(audioMap, 'neutral');
    } catch (e) {
      // 解析失败，说明是普通字符串 URL，直接赋值
      item.url = item.audioUrl;
    }
  });

  list.value = data;

  // 初始化每个音频项的默认字段，取消预加载
  list.value.forEach(item => {
    item.duration = 0;
    item.isPlayable = null;
    item.isPlaying = false;
    item.audioElement = null;
    item.isHovered = false;
    item.lastRotation = 0;
    item.volume = 100;
    item.aduio_finished = true;
  });

  loading.value = false;

  // 取消初始化时音频预加载，避免页面加载时请求音频
};
let getDisplayName = (item) => {
  console.log('getDisplayName');
  
  if (!input_search.value || input_search.value.trim() === '') {
    return item.platformNickname || ''
  }
  // 检查是否匹配昵称
  if ((item.platformNickname || '').toLowerCase().includes(input_search.value.toLowerCase())) {
    return highlightMatchText(item.platformNickname || '', input_search.value)
  }
  // 如果昵称不匹配，正常显示
  return item.platformNickname || ''
}
// 高亮显示匹配的文本
let highlightMatchText = (text, keyword) => {
  if (!keyword || keyword.trim() === '' || !text) {
    return text
  }
  // 防止XSS攻击，对关键词进行转义
  let escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }
  let escapedKeyword = escapeRegExp(keyword.toLowerCase())
  let regex = new RegExp(`(${escapedKeyword})`, 'gi')

  return text.replace(regex, '<span style="color: #0AAF60; font-weight: bold;">$1</span>')
}
let getDisplayEmotionTag = (item) => {
  if (!input_search.value || input_search.value.trim() === '') {
    return item.emotionTags || ''
  }
  // 检查是否匹配情感标签
  if ((item.emotionTags || '').toLowerCase().includes(input_search.value.toLowerCase())) {
    return highlightMatchText(item.emotionTags || '', input_search.value)
  }

  // 检查是否匹配场景分类
  if ((item.sceneCategory || '').toLowerCase().includes(input_search.value.toLowerCase())) {
    // 如果匹配场景分类，添加场景分类信息到标签
    return `${item.emotionTags || ''} <span style="color: #0AAF60; font-weight: bold;">[${item.sceneCategory}]</span>`
  }

  // 如果都不匹配，正常显示
  return item.emotionTags || ''
}
let selectSoundItem = async (item, index, e) => {
  await nextTick()
  let el = targetElements.value[index];
  let rect = '';
  let centerX = ''
  let style = ''

  if (el) {
    rect = el.getBoundingClientRect();
    style = window.getComputedStyle(el);
    centerX = rect.left + (rect.width / 2) - Math.floor((parseFloat(style.marginRight)) / 2)
  }

  console.log(item, 999);
  current_language.value = 'auto'
  current_emotion.value = 'neutral'
  // if()
  choose_timbre.value = item
  SoundItemId.value = item.voiceName
  targetElements.value = []
}
let init_choose=(data)=>{
   close_aduio()
   choose_timbre.value={}
   SoundItemId.value=''
   if(data){
    // let index=list.value.findIndex((item)=>item.id==data)
    let index=list.value.findIndex((item)=>item.voiceName==data.character_id)
    SoundItemId.value=data.character_id
    console.log(index,data,'init_choose');
    
    selectSoundItem(list.value[index], index)
    scrollToElement()
   }
}
let toggle_collect=async(item,index)=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let status='',tip='',result=0
    if(list.value[index].bookmark==0){
        result=1
        status='success'
        tip='收藏成功'
    }else{
        result=0
        status='error'
        tip='取消收藏'
    }
    try {
        let data=await bookmarkToggle({voiceId:item.id,type:result,tts:4,userId:loginStore.userId})
        if(data.code==1){
            ElMessage({ message:data.msg , type: 'error' });
            return
        }
        list.value[index].bookmark=result
        ElMessage[status](tip);
    }catch (error) {
        // console.log(error,'error');
        ElMessage({ message:'数据加载失败，请刷新页面重试' , type: 'error' });
        
    }
   
}
//音频控制按钮
let play_sound = (index, data) => {


  let item = list.value[index];
console.log(item,index,'play_sound');
  if (item.isPlaying) {
    // 如果当前正在播放，点击则暂停
    if (item.audioElement) {
      item.audioElement.pause();
      item.audioElement.currentTime = 0;
      item.audioElement = null;
      currentAudio.value = null; // 清空全局引用
    }
    item.isPlaying = false;
    return;
  }

  // 暂停其他所有音频
  list.value.forEach((it, i) => {
    if (i !== index && it.audioElement) {
      it.audioElement.pause();
      it.audioElement.currentTime = 0;
      it.audioElement = null;
      it.isPlaying = false;
    }
  });

  // 播放当前音频
  item.isPlaying = true;

  try {
    
    let audioUrl = item.url || 'http://example.com/default-audio.mp3';
    console.log(item,'url');
    
    item.audioElement = new Audio(audioUrl);
    item.audioElement.volume = item.volume / 100;

    item.audioElement.addEventListener('ended', () => {
      item.isPlaying = false;
      item.audioElement.currentTime = 0;
      item.audioElement = null;
      currentAudio.value = null; // 清空全局引用
    });

    item.audioElement.addEventListener('error', () => {
      item.isPlaying = false;
      item.audioElement = null;
      currentAudio.value = null; // 清空全局引用
      // ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`);
     
    });

    item.audioElement.load();

    let playPromise = item.audioElement.play();
    currentAudio.value = item.audioElement; // 保存当前播放实例
    if (playPromise !== undefined) {
      playPromise.catch(() => {
        // ElMessage.error(`播放失败`);
        item.isPlaying = false;
        item.audioElement = null;
        currentAudio.value = null; // 清空全局引用
      });
    }
  } catch (err) {
    ElMessage.error(`暂时无法播放`);
    item.isPlaying = false;
    item.audioElement = null;
    currentAudio.value = null;
  }
};
let setRef = (index) => {
	return (el) => {
		if (el) {
			targetElements.value[index] = el; // 将元素存储到数组中

		}
	};
};
//声音商店选中自动滑动到指定位置
let scrollToElement = async() => {
    await nextTick()
    await nextTick()

    let scrollbar = scroll.value;
      console.log(targetElements.value,SoundItemId.value,'scrollToElement');
      console.log('targetElements:', targetElements.value.length);
    let target = targetElements.value.find(el =>{
 
      return  el && el.dataset.voiceName == SoundItemId.value
    });
     console.log(target,scrollbar,'scrollToElement1');

    if (target && scrollbar) {
      // 计算目标元素相对于滚动容器的偏移
      let targetOffset = 0;
      let el = target;
      const container = scrollbar.$el;

      while (el && el !== container) {
        targetOffset += el.offsetTop;
        el = el.offsetParent;
      }

      // 滚动到目标位置
      scrollbar.scrollTo({ top: targetOffset, behavior: 'smooth' });
    }

};
defineExpose({
  loading,
  close_aduio,
  init,
  cancelToken,
  input_search,
  init_choose,
  SoundItemId,
  choose_timbre,
  current_nav

})
</script>
<style lang="scss" scoped>
.el-scrollbar {
  overflow: visible;

  ::v-deep(.el-scrollbar__bar) {
    right: -8px;
  }
  ::v-deep(.el-scrollbar__thumb) {
    background-color: #0AAF60;
    will-change: transform;
    opacity: 1;
  }
  ::v-deep(.el-scrollbar__view){
    width: 100%;
    height: 100%;
  }
  .dubbing_selection_dialog_content_list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .dubbing_selection_dialog_content_item {
      width: 115px;
      height: 151px;
      background: #F7F7F9;
      border-radius: 8px;
      position: relative;
      margin-right: 14px;
      margin-bottom: 20px;
      cursor: pointer;
      padding: 1px;
      box-sizing: border-box;
      .dubbing_selection_dialog_content_item_grade{
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
        width: 55px;
        height: 18px;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .dubbing_selection_dialog_content_item_container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 30px;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        background-color: #F7F7F9;
        border-radius: 8px;
        overflow: hidden;
        .dubbing_selection_dialog_content_item_collect {
          position: absolute;
          width: 16px;
          height: 14px;
          right: 9px;
          top: 9px;
          z-index: 1;
          cursor: pointer;
        }

        .dubbing_selection_dialog_content_item_avatar {
          width: 65px;
          height: 65px;
          border-radius: 50%;
          overflow: hidden;
          position: relative;
          margin-bottom: 5px;
          cursor: pointer;
          background-color: #f0f2f5;
          img {
            width: 100%;
            height: 100%;
          }

          .dubbing_selection_dialog_content_item_aduio {
            position: absolute;
            z-index: 2;
            display: none;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.4);
            img{
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              z-index: 3;
              width: 17px;
              height: 21px;
            }
          }

          &:hover {
            .dubbing_selection_dialog_content_item_aduio {
              display: block;
            }
          }

        }

        .dubbing_selection_dialog_content_item_nick {
          font-size: 14px;
          line-height: 18px;
          display: flex;
          align-items: center;
          color: #1B2337;
          margin-bottom: 5px;
        }

        .dubbing_selection_dialog_content_item_emotion {
          font-size: 12px;
          line-height: 18px;
          display: flex;
          align-items: center;
          /* 灰色 */
          color: rgba(0, 0, 0, 0.45);

        }
      }
      

      &:nth-child(8n) {
        margin-right: 0;
      }

      &.current {
        color: #0AAF60;
        background-image:linear-gradient(134deg, rgba(10, 175, 96, 1), rgba(255, 214, 0, 1));
        .dubbing_selection_dialog_content_item_emotion{
          color: #0AAF60;
        }
      }
    }
    .el-empty{
      margin: 0 auto;
    }
  }
}
</style>