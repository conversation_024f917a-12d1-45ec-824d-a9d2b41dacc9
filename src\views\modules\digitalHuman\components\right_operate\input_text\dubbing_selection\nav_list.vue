<template>
    <div class="dubbing_selection_dialog_content_nav" v-loading="loading"  v-if="getCurrentTypeList(current_type) && getCurrentTypeList(current_type)['condition']">
        <div class="dubbing_selection_dialog_content_nav_list" v-for="(tag, index) in Object.keys(getCurrentTypeList(current_type)['condition'])" :key='index'>
            <div class="dubbing_selection_dialog_content_nav_item" v-for="(item, index1) in getCurrentTypeList(current_type)['condition'][tag].arr" :class="current_tag[tag] == item.key ? 'current' : ''" :key="index1" @click="select_tag(tag, item, index1)">
                {{ item.label }}
            </div>
        </div>
    </div>
</template>
<script setup>
import { defineExpose,defineEmits,nextTick, reactive, ref } from 'vue'
let loading=ref(false)
let current_type = ref("4")
let selectLastIndex=ref(0)
let uniqueArray = ref([])//原始所有子列
let current_tag = ref({})
let emit = defineEmits(['changesubNav'])
let tags = ref([
    {
        type: '4',
        title: 'AI配音',
        value: '4',
        condition: {
            grade: {
                name: "领域",
                arr: [

                    
                ]
            },
            gender: {
                name: "语言",
                arr: [

                    
                ]
            },
            sceneCategory: {
                name: "年龄",
                arr: [

                    
                ]
            },
            recommendTags: {
                name: "性别",
                arr: [
                   
                ]
            },
            style: {
                name: "风格",
                arr: [
                   
                ]
            },
            expenses: {
                name: "资费",
                arr: [
                  
                ]
            },
        },


    }, {
        type: '5',
        title: 'AI商配',
        condition: {
            grade: {
                name: "领域",
                arr: [

                ]
            },
            gender: {
                name: "语言",
                arr: [

                 
                ]
            },
            sceneCategory: {
                name: "年龄",
                arr: [

                ]
            },
            recommendTags: {
                name: "性别",
                arr: [
                   
                ]
            },
            style: {
                name: "风格",
                arr: [
                   
                ]
            },
            expenses: {
                name: "资费",
                arr: [
                   
                ]
            },
        }
    }
])
let getCurrentTypeList = (type) => {
    let result = tags.value.filter((item) => {
        return item.type == type
    })
    return result[0]
}
let init=()=>{
     tags.value.map((item) => {
        Object.keys(item.condition).map((item1) => {
            // console.log(item.condition[item1],item1,666);
            const exists = item.condition[item1].arr.some(item => item.key === '全部');
            if (item.condition[item1].arr.length > 0&&!exists) {
          
                item.condition[item1].arr.unshift({
                    key: '全部',
                    label: '全部'
                })
                current_tag.value[item1] = item.condition[item1].arr[0].key
            }


        })
    })
}
let select_tag = (type, tag, index) => {
    selectLastIndex.value=index
    current_tag.value[type] = tag.key
    let nav_data = {}
    Object.keys(current_tag.value).map((item) => {
        if (current_tag.value[item] != '全部') {
            nav_data[item] = current_tag.value[item]
        }
    })

    emit("changesubNav", nav_data)



}
defineExpose({
    loading,
    current_type,
    tags,
    uniqueArray,
    init,
    selectLastIndex
})
</script>
<style lang="scss" scoped>
.dubbing_selection_dialog_content_nav{
    display: flex;
    flex-direction: column;
    .dubbing_selection_dialog_content_nav_list {
        display: flex;
        flex-wrap: wrap;
       
    .dubbing_selection_dialog_content_nav_item {
            line-height: 22px;
            min-width:46px;
            padding: 2px 6px 2px 10px;
            margin-right: 11px;
            margin-bottom: 14px;
            font-size: 14px;
            line-height: 18px;
            color: #353D49;
            cursor: pointer;
            &.current{
                color:#0AAF60;
;
            }
        }
    }
}

</style>