<template>
    <div class="dubbing_selection_dialog_content_serach">
        <img src="@/assets/images/digitalHuman/dubbing_selection_dialog_content_serach.svg" alt="" @click="handleSearch">
        <el-input v-model="searchQuery" style="width: 204px" placeholder="输入名称进行搜索" @input="handleInputChange"/>
    </div>
</template>
<script setup>
import { reactive, ref, defineEmits,defineExpose } from 'vue';
let searchQuery = ref('');
let list = ref([]);
let emit = defineEmits(['search_list']);
let searchKeys = reactive({
    platformNickname: ''
});
// 输入框内容变化处理函数
let handleSearch = () => {

    
    emit('search_list', search_method(list.value, searchQuery.value));
}
// 输入框内容变化处理函数
let handleInputChange = (e) => {
    searchQuery.value = e; // 更新搜索查询
    handleSearch()
}
// 模糊搜索方法
let search_method = (data, query) => {
     if (!query) return data;

  const lowerQuery = query.toLowerCase();

  // 精准匹配
  const exactMatches = data.filter(item =>
    Object.keys(searchKeys).some(key =>
      item[key] && item[key].toLowerCase() === lowerQuery
    )
  );

  // 模糊匹配但排除精准匹配的项
  const fuzzyMatches = data.filter(item =>
    Object.keys(searchKeys).some(key =>
      item[key] && item[key].toLowerCase().includes(lowerQuery)
    ) && !exactMatches.includes(item)
  );

  return [...exactMatches, ...fuzzyMatches];
};
defineExpose({
    list,
    searchQuery
})
</script>
<style lang="scss" scoped>
.dubbing_selection_dialog_content_serach {
    padding: 4px 11px;
    background: #F6F7F9;
    border-radius: 6px;
    margin-left: auto;
    align-items: center;
    display: flex;

    img {
        margin-right: 4px;
        width: 16px;
        height: 16px;
        cursor: pointer;
    }

    ::v-deep(.el-input) {
        .el-input__wrapper {
            border: none;
            box-shadow: none!important;;
            background-color: transparent;
            padding: 0;

            .el-input__inner {
                height: 29px;
                font-size: 13px;
                line-height: 18px;
                color: #353D49;

                &::placeholder {
                    color: #9DA3AC;
                }
            }
            &.is-focus {
                box-shadow: none;
            }

        }
    }

}
</style>