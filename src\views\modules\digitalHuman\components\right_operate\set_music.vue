<template>
<div class="digital_human_set_music">
    <div class="digital_human_set_musicustom_upload">
        <el-upload
            ref="uploadRef"
            class="upload-demo"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            :show-file-list="false"
            accept="audio/*"
            :auto-upload="false"
            >
            <el-button plain >自定义上传</el-button>
        </el-upload>
    </div>
    <div class="digital_human_set_music_list">
        <div class="digital_human_set_music_list_switch_nav">
            <div class="digital_human_set_music_list_switch_nav_item" v-for="(item,index) in list_nav" :key="index" :class="current_nav==item.id?'current':''" @click="change_nav(item)">
                {{ item.name }}
            </div>
        </div>
        <div class="digital_human_set_music_list_content">
          <el-scrollbar style="flex: 1; display: flex; flex-direction: column;" ref="scrollbar">
            <div class="digital_human_set_music_list_content_item"  :class="[item.isPlaying ? 'play' : 'pause']" v-for="(item,index) in music_list" :key="index">
                <div class="digital_human_set_music_list_content_item_img" @click="togglePlay(index)">
                     <img src="" alt="">
                </div>
               <div class="digital_human_set_music_list_content_item_text">
                    <div class="digital_human_set_music_list_content_item_text_name">
                        {{ item.name }}
                    </div>
                     <div class="digital_human_set_music_list_content_item_text_time">
                        {{ item.time }}
                    </div>
               </div>
            </div>
            </el-scrollbar>
        </div>
        <div class="digital_human_set_music_list_volume">
            <span class="digital_human_set_music_list_volume_label">音量</span>
            <el-slider v-model="volume" height="20px"/>
            <div class="digital_human_set_music_list_volume_value">
                {{ volume }}
            </div>
        </div>
               
    </div>
</div>
</template>
<script setup>
import { reactive,ref,onMounted,nextTick  } from "vue";
import { ElMessage, ElLoading } from 'element-plus'
import audioUrl from '@/assets/images/realVoice/audio.mp3';
import { useFileUpload } from '@/views/modules/digitalHuman/utils/upload.js';
const {  fileChange } = useFileUpload();
let current_nav=ref(1)
let list_nav=reactive([
    {name:'推荐',id:1},
    {name:'我的',id:2}
])
let music_list=ref([
    {
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },{
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },{
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },{
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },{
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },{
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },{
        name:'音乐名称',
        url:audioUrl,
        time:'',
        isPlaying: false
    },
]) 
let uploadRef = ref(null)
let scrollbar = ref(null);
let change_nav=(item)=>{
    current_nav.value=item.id
}
let volume=ref(50)
// 当前播放的Audio对象和索引
let currentAudio = null;
let currentIndex = ref(-1);

let formatTime=(seconds)=>{
  const m = Math.floor(seconds / 60);
  const s = Math.floor(seconds % 60);
  return `${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
}
let  togglePlay=(index)=>{
   // 如果点击的是当前播放的音频
  if (currentIndex.value === index) {
    if (currentAudio.paused) {
      currentAudio.play();
    } else {
      currentAudio.pause();
    }
    return;
  }

  // 如果有其他音频在播放，先暂停并销毁
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.src = '';
    currentAudio.load();
    // 重置之前播放音频的状态
    if (currentIndex.value !== -1) {
      music_list.value[currentIndex.value].isPlaying = false;
    }
  }

  // 创建新的Audio对象
  const audio = new Audio(music_list.value[index].url);
  currentAudio = audio;
  currentIndex.value = index;

  // 监听事件
  audio.addEventListener('loadedmetadata', () => {
    music_list.value[index].time = formatTime(audio.duration);
  });

  audio.addEventListener('play', () => {
    music_list.value[index].isPlaying = true;
  });

  audio.addEventListener('pause', () => {
    music_list.value[index].isPlaying = false;
  });

  audio.addEventListener('ended', () => {
    music_list.value[index].isPlaying = false;
  });

  audio.play();
}
let loading = ref(null);
let handleFileChange=async (file, fileList) => {
    loading.value=ElLoading.service({
      fullscreen: true,
      lock: true,
      text: '上传中...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    const uploadRequest = await fileChange(file, fileList);
    console.log(uploadRequest,'uploadRequest');
    change_nav(list_nav[1])
    await nextTick();

    let scroll = scrollbar.value;
    console.log(scroll,'scroll');
    
      scroll.scrollTo({ top: scroll?.wrapRef?.scrollHeight    , behavior: 'smooth' });
    loading.value.close();
}

let beforeUpload = (file) => {
    console.log('beforeUpload');
    
    let isImage = file.type.startsWith('audio/');
    if (!isImage) {
        ElMessage.error('上传文件必须是音频文件！');
    }
    return isImage;
};
onMounted(() => {
  music_list.value.forEach((item, index) => {
    const audio = new Audio(item.url);
    audio.addEventListener('loadedmetadata', () => {
      music_list.value[index].time = formatTime(audio.duration);
      audio.src = '';
      audio.load();
    });
  });
});
</script>
<style lang="scss" scoped>
.digital_human_set_music{
    // width: 100%;
    width: 246px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .digital_human_set_musicustom_upload{
        width: 100%;
        padding: 0 18px;
        box-sizing: border-box;
        margin-bottom: 16px;
        height: 80px;
        .upload-demo{
            ::v-deep(.el-upload){
                width: 100%;
                .el-button{
                    width: 100%;
                    background-color: transparent;
                    border-radius: 2px;
                    border: 1px solid #0AAF60;
                    padding:11px 0;
                    height: 36px;
                    span{
                        display: inline-block;
                        font-size: 14px;
                        line-height: 14px;
                        color: #0AAF60;
                    }
                }
            }
        }
       
    }
    .digital_human_set_music_list{
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow-y: hidden;
        .digital_human_set_music_list_switch_nav{
            display: flex;
            align-items: center;
            padding: 0 18px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            .digital_human_set_music_list_switch_nav_item{
                font-size: 14px;
                line-height: 42px;
                margin-right: 22px;
                color: rgba(0, 0, 0, 0.45);
                cursor: pointer;
                position: relative;
                padding: 0 13px;
                &.current{
                    color: #000;
                    &::after{
                        background-color: #0AAF60;
                        width: 100%;
                        height: 2px;
                        content: '';
                        position: absolute;
                        bottom: -1px;
                        left: 0;
                    }
                    
                }

            }
        }
        .digital_human_set_music_list_content{
            padding: 0;
            width: 100%;
            box-sizing: border-box;
            flex: 1; /* 让父容器撑满剩余空间 */
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            .el-scrollbar{
                padding: 16px 18px 0;
                 ::v-deep(.el-scrollbar__bar.is-vertical) {
                    right: 0 ;
                    width: 8px;
                }

                ::v-deep(.el-scrollbar__thumb) {
                    background-color: #0AAF60;
                    border-radius: 4px;
                    opacity: 1;
                }
            
          
            .digital_human_set_music_list_content_item{
                display: flex;
                align-items: center;
                margin-bottom: 18px;
                flex-shrink: 0; /* 防止缩小 */
               
                .digital_human_set_music_list_content_item_img{
                    width: 66px;
                    height: 66px;
                    border-radius: 4px;
                    overflow: hidden;
                    margin-right: 10px;
                    position: relative;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                    &::before{
                        content: '';
                        position: absolute;
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        background-color: rgba(0, 0, 0, 0.3);
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%,-50%);
                        z-index: 3;
                    }
                }
                .digital_human_set_music_list_content_item_text{
                    display: flex;
                    flex-direction: column;
                    .digital_human_set_music_list_content_item_text_name{
                        font-size: 14px;
                        color: #000;
                        line-height: 22px;
                        margin-bottom: 3px;
                    }
                    .digital_human_set_music_list_content_item_text_time{
                        font-size: 14px;
                        line-height: 22px;
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
                 &.play{
                    .digital_human_set_music_list_content_item_img{
                        &::after{
                            content: '';
                            position: absolute;
                            z-index: 4;
                            width: 13px;
                            height: 19px;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            background-image: url('@/assets/images/soundStore/sound_list_play.png');
                        }
                    }
                    
                }
                &.pause{
                    .digital_human_set_music_list_content_item_img{
                        &::after{
                            content: '';
                            position: absolute;
                            z-index: 4;
                            width: 17px;
                            height: 19px;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            background-image: url('@/assets/images/soundStore/sound_list_pause.png');
                        }
                    }
                }
            }
            }
        }
        .digital_human_set_music_list_volume{
            padding: 14px 18px 11px;
            display: flex;
            align-items: center;
            .digital_human_set_music_list_volume_label{
                margin-right: 9px;
                font-size: 14px;
                line-height: 24px;
                color: rgba(0, 0, 0, 0.45);
            }
            .el-slider{
                flex: 1;
                height: fit-content;
                ::v-deep(.el-slider__runway){
                    background-color:#D6D6D6;
                    height: 2px;
                    .el-slider__bar{
                        background-color: #0AAF60;
                        height: 2px;
                    }
                    .el-slider__button-wrapper{
                        top: -3px;
                        width: 8px;
                        height: 8px;
                        .el-slider__button{
                            width: 8px;
                            height: 8px;
                            background-color: #CAFFE6;
                            border: 1px solid #0AAF60;
                            box-sizing: border-box;
                        }   
                    }
                }
            }
            .digital_human_set_music_list_volume_value{
                margin-left: 8px;
                font-size: 12px;
                line-height: 14px;
                color: #040404;
                width: 2em;
                text-align: right;
            }
        }
    }
}
</style>