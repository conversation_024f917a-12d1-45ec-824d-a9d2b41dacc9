<template>
    <el-dialog v-model="dialogVisible" class="set_music_dialog" width="1068px" :show-close="false" append-to="#app">
        <template #header>
            <div class="set_music_dialog_header">
                <div class="set_music_dialog_header_title">背景音乐</div>
                <img src="@/assets/images/digitalHuman/dubbing_selection_dialog_header_close.svg"
                    class="set_music_dialog_header_close" @click="set_music_dialog_close" alt="">
            </div>
        </template>
        <template #default>
            <div class="set_music_dialog_header_content">
                <div class="set_music_dialog_header_content_nav">
                    <div class="set_music_dialog_header_content_nav_toggle">
                         <el-button v-for="item in navList" :key="item.id"
                            :class="{ 'current': item.id == current_nav }"
                            @click="change_nav(item)">{{ item.name }}</el-button>
                    </div>
                    <div class="set_music_dialog_header_content_nav_classify" v-if="current_nav==1">
                        <div class="set_music_dialog_header_content_nav_classify_item" v-for="(item,index) in classifyList.themeGroups" :class="current_classify==item.theme?'current':''" :key="index"  @click="change_classify(item)">
                            {{ item.theme }}
                        </div>
                    </div>
                    <uploadMusic ref="upload_music_ref" @upload="upload" v-else></uploadMusic>
                </div>
                <musciList ref="musci_list_ref"></musciList>
            </div>
        </template>
         <template #footer>
            <div class="set_music_dialog_btns">
                <el-button @click="cancel" class="set_music_dialog_btns_cancel">取消</el-button>
                <el-button @click="submit" class="set_music_dialog_btns_submit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref,defineExpose,defineEmits,watch, nextTick } from 'vue'
import uploadMusic from '@/views/modules/digitalHuman/components/right_operate/set_music/upload_music.vue'
import musciList from '@/views/modules/digitalHuman/components/right_operate/set_music/music_list.vue'
import { getBGMIApi } from '@/api_my/AlDubb'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore() 
let dialogVisible = ref(false)
let userId=ref('')
let navList=ref([
    {
        id:1,
        name:'推荐音乐'
    },
    {
        id:2,
        name:'我的音乐' 
    }
])
let current_nav=ref(1)
let classifyList=ref([])
let upload_music_ref=ref(null)
let musci_list_ref=ref(null)
let current_classify=ref('')
let set_music_dialog_close=()=>{
    dialogVisible.value=false
}
let current_music_obj=ref(null)      
/**
 * 修改导航项
 *
 * @param item 导航项对象
 */
let change_nav=async(item)=>{
    await nextTick()
    musci_list_ref.value.music_list=[]
    musci_list_ref.value.close_aduio()
    console.log('change_nav');
    
    current_nav.value=item.id
    musci_list_ref.value.music_type=item.id
    if(current_nav.value==1){
        if(current_music_obj.value){
            let index=classifyList.value.themeGroups.findIndex(item=>item.theme==current_music_obj.value.current_classify)
            change_classify(classifyList.value.themeGroups[index])
        
            musci_list_ref.value.togglePlay(current_music_obj.value.info,'scroll')
            current_music_obj.value=null

        }else{
            change_classify(classifyList.value.themeGroups[0])
        }
       
    }else{
        musci_list_ref.value.music_list = classifyList.value.audioMaterials
     
        if(current_music_obj.value){

             musci_list_ref.value.togglePlay(current_music_obj.value.info,'togglePlay')
             current_music_obj.value=null
        }
        classifyList.value.audioMaterials.map((item1)=>{
            getMusicTime(item1)
        })
   
    }
    await nextTick()
    adjustDialogPosition()
    // 调用 el-scrollbar 的 update 方法，刷新滚动条
    if (musci_list_ref.value && musci_list_ref.value.$refs && musci_list_ref.value.$refs.scrollbar) {
       
        console.log('update');
        
        musci_list_ref.value.$refs.scrollbar.update()
    }
    
}
let upload=(item)=>{
    getMusicList(navList.value[1])
}
let change_classify=async(item)=>{
    console.log(item,'change_classify');
    
    await nextTick()
    musci_list_ref.value.music_list=[]
    musci_list_ref.value.close_aduio()
    current_classify.value=item.theme
    musci_list_ref.value.music_list =item.bgmList
    musci_list_ref.value.music_list.map((item1)=>{
        getMusicTime(item1)
    })
}
let emits = defineEmits(['choose'])
let cancel=()=>{
    dialogVisible.value=false
}
let submit=()=>{
    current_music_obj.value={}
    if(current_nav.value==1){
        current_music_obj.value.current_classify=current_classify.value
        current_music_obj.value.bgm_url=musci_list_ref.value.current_music_obj.ossPath
    }else{
        current_music_obj.value.bgm_url=musci_list_ref.value.current_music_obj.storagePath
    }
    current_music_obj.value.current_nav=current_nav.value
    current_music_obj.value.info=musci_list_ref.value.current_music_obj
    emits('choose', current_music_obj.value)
    cancel()
}
let formatDuration = (seconds) => {
    let totalSeconds = Math.floor(seconds); // 取整
    let hours = Math.floor(totalSeconds / 3600); // 计算小时
    let minutes = Math.floor((totalSeconds % 3600) / 60); // 计算分钟
    let remainingSeconds = totalSeconds % 60; // 计算剩余秒数

    // 格式化为两位数
    let formattedHours = String(hours).padStart(2, '0');
    let formattedMinutes = String(minutes).padStart(2, '0');
    let formattedSeconds = String(remainingSeconds).padStart(2, '0');

    // 根据小时数决定返回格式
    if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`; // hh:mm:ss
    } else {
        return `${formattedMinutes}:${formattedSeconds}`; // mm:ss
    }
}
let getMusicTime=(data1)=>{
    return new Promise((resolve, reject) => {
        const audio = new Audio(data1.ossPath||data1.storagePath)
          audio.addEventListener('loadedmetadata', async () => {
            data1.time = formatDuration(audio.duration)
            await nextTick()
            // 触发视图更新：重新赋值一个新数组，Vue 3 会检测到变化
            musci_list_ref.value.music_list = [...musci_list_ref.value.music_list]
          })
          audio.addEventListener('error', async () => {
            data1.time = '00:00'
            await nextTick()
            musci_list_ref.value.music_list = [...musci_list_ref.value.music_list]
          })
    })
}
let getMusicList = async (data) => {
  musci_list_ref.value.loading=true
  try {
    const res = await getBGMIApi({
      userId: userId.value
    })
    console.log(res, 'res')

    if (res.code === 0) {
      let { audioMaterials, themeGroups } = res.data

      // 先给 audioMaterials 初始化属性
      audioMaterials.forEach(item => {
        item.color = getRandomColor()
        item.color1 = getRandomColor()
        item.color2 = getRandomColor()
        item.volume=80
        item.isSelected = false
        item.isPlaying = false
        item.url=item.storagePath
      })

      // 给 themeGroups 里的 bgmList 初始化并异步加载时长
      themeGroups.forEach(item => {
        item.bgmList.forEach(data1 => {
          data1.color = getRandomColor()
          data1.color1 = getRandomColor()
          data1.color2 = getRandomColor()
          data1.isSelected = false
          data1.isPlaying = false
          data1.time = '00:00' // 先给默认时间
          data1.volume=80
          data1.url=data1.ossPath
        })
      })


      classifyList.value = res.data
      console.log(classifyList.value, 'classifyList');
      if(data){
        change_nav(data)
      }else{
        change_nav(navList.value[0])
      }
      musci_list_ref.value.loading=false
    //  musci_list_ref.value.music_type = 1
    //   musci_list_ref.value.music_list = classifyList.value.themeGroups[0].bgmList
      // 设置音乐类型和默认列表
 

    } else {
      classifyList.value = {}
      musci_list_ref.value = {
        music_type: 0,
        music_list: []
      }
    }
  } catch (err) {
    classifyList.value = {}
    musci_list_ref.value = {
      music_type: 0,
      music_list: []
    }
  }
}
let init=async(data)=>{
 
    console.log(data,'init');
    
    await nextTick()
    classifyList.value=[]
    musci_list_ref.value.music_list=[]
    if(data){
        let index= navList.value.findIndex(item=>item.id==data.current_nav)
        getMusicList(navList.value[index])
        current_music_obj.value=data
        if(data.current_nav==1){
            musci_list_ref.value.current_music=current_music_obj.value.info.id
        }else{
            musci_list_ref.value.current_music=current_music_obj.value.info.materialId
        }
        
    }else{
        getMusicList()
    }

}
 // 随机渐变色
let getRandomColor=()=>{
  const R = Math.floor(Math.random() * 130 + 110); // 110-239范围
  const G = Math.floor(Math.random() * 130 + 110);
  const B = Math.floor(Math.random() * 130 + 110);
  return `rgb(${R},${G},${B})`;
}

let adjustDialogPosition=()=>{
  nextTick(() => {
    const dialogEl = document.querySelector('.set_music_dialog');
    if (!dialogEl) return;

    const dialogHeight = dialogEl.offsetHeight;
    const windowHeight = window.innerHeight;
    let appHeight = document.getElementById('app').clientHeight;
    let  scale = windowHeight / 953;
    let top =0
    // 计算居中 top，取整避免子像素
    if(windowHeight>=953){
        top = Math.round((windowHeight - dialogHeight) / 2);
    }else{
        top = Math.round((windowHeight/(windowHeight/953) - dialogHeight) / 2);
    }
    console.log(windowHeight/(windowHeight/953),dialogHeight,top,);
    
    // 设置 top，取消 transform
    dialogEl.style.top = `${top*scale}px`;
    dialogEl.style.transform = 'none';
    //  dialogEl.style.transform = `scale(${scale})`;
    dialogEl.style.margin = '0 auto'; // 保持水平居中
  });
}
watch(()=>dialogVisible.value,async(newVal,oldVal)=>{
    if(newVal){
       userId.value=loginStore?.userId|| ''
      
    }else{
        await nextTick()
        musci_list_ref.value&&musci_list_ref.value.close_aduio()
    }
},{immediate:true,deep:true})
defineExpose({
    dialogVisible,
    init
})
</script>
<style lang="scss">
.set_music_dialog{
    padding: 0;
    border-radius: 8px;
    // top: 50% !important;
    // transform: translateY(-50%) !important;
    // margin: 0 auto!important;
    padding-bottom: 8px;
    .el-dialog__header {
        padding: 0;
        .set_music_dialog_header {
            display: flex;
            align-items: center;
            padding: 18px 16px;
            width: 100%;
            box-sizing: border-box;
            border-bottom: 1px solid #DEDEDF;

            .set_music_dialog_header_title {
                font-size: 16px;
                line-height: 22px;
                color: #1D2129;
            }

            .set_music_dialog_header_close {
                width: 20px;
                height: 20px;
                margin-left: auto;
                cursor: pointer;
            }
        }
    }
    .set_music_dialog_header_content{
        padding: 20px 23px;
        width: 100%;
        box-sizing: border-box;
        .set_music_dialog_header_content_nav{
            .set_music_dialog_header_content_nav_toggle {
                display: flex;
                align-items: center;
                border-radius: 2px;
                overflow: hidden;
                border: 1px solid #0AAF60;
                width: fit-content;
                margin-bottom: 20px;
                .el-button {
                    margin-left: 0;
                    padding: 0;
                    height: 30px;
                    width: 86px;
                    border-left: none;
                    border: none;

                    span {
                        font-size: 14px;
                        line-height: 18px;
                        color: #353D49;
                    }

                    &.current {
                        background-color: #0AAF60;
                        border: none;
                        border-radius: 2px;

                        span {
                            color: #fff;
                        }
                    }

                    &:first-child {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }

                    &:last-child {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                }
            }
            .set_music_dialog_header_content_nav_classify{
                padding: 0 1px;
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                .set_music_dialog_header_content_nav_classify_item{
                    margin-right: 5px;
                    padding: 2px 6px 2px 10px;
                    font-size: 14px;
                    line-height: 18px;
                    color:  #353D49;
                    cursor: pointer;
                    &:last-child{
                        margin-right: 0;
                    }
                    &.current{
                        color: #0AAF60;
                    }
                }
            }
        }
        
    }
    .el-dialog__footer{
        padding: 0;
        .set_music_dialog_btns{
            padding: 16px 24px;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .el-button{
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 9px 34px;
                border-radius: 4px;
                cursor: pointer;
                border: none;
                span{
                    font-size: 14px;
                    line-height: 22px;
                    text-align: center;
                    letter-spacing: -0.01px;
                    color: #FFFFFF;
                }
                &.set_music_dialog_btns_cancel{
                    background: #D3D3D2;
                }
                &.set_music_dialog_btns_submit{
                    background-color:#0AAF60;
                }
                &+.el-button{
                    margin-left: 16px;
                }
            }
        }
    }
}
</style>