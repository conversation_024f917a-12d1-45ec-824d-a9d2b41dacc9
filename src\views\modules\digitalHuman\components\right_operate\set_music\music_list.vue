<template>
    <div class="set_music_upload_list" style="height: 492px;"  v-loading="loading">
        <el-scrollbar style="height: 492px;" ref="scrollbar" always>
          <template v-if="music_list.length>0">
            <div class="set_music_upload_list_item" :class=" getCurrent(item) ? 'current' : ''" :data-id-name="music_type==1?item.id:item.materialId"
                v-for="(item, index) in music_list" :key="index" @click.stop="togglePlay(item)"   :ref="setRef(index)">
                <div class="digital_human_set_music_list_content_item_img" 
                    :ref="el => gradientDivs[index] = el" :style="gradientStyle(item.color, item.color1, item.color2)" >
                    <div class="digital_human_set_music_list_content_item_aduio" @click.stop="play_music(item,index)">
                        <img :src="item.isPlaying ? pauseImage : playImage" alt="">
                    </div>
                </div>
                <div class="digital_human_set_music_list_content_item_text">
                    <div class="digital_human_set_music_list_content_item_text_name">
                       {{ item?.materialName || item?.musicName }}
                    </div>
                    <div class="digital_human_set_music_list_content_item_text_time">
                        {{ item.time }}
                    </div>
                </div>
                <div class="set_music_upload_list_item_select" v-if="getCurrent(item)">
                    <img src="@/assets/images/digitalHuman/set_music_upload_list_select.svg" alt="">
                </div>
            </div>
            </template>
            <el-empty description="暂无相关背景音乐" v-else />
        </el-scrollbar>
    </div>
</template>
<script setup>
import { ref, defineExpose,defineEmits, nextTick } from 'vue'
import pauseImage from '@/assets/images/digitalHuman/dubbing_selection_list_pause.svg'
import playImage from '@/assets/images/digitalHuman/dubbing_selection_list_play.svg'
import { ElMessage } from 'element-plus'
let loading = ref(false)
let music_list = ref([])
let hasInsertedKeyframes =ref(false);
let gradientDivs = ref([]);
let music_type=ref('')
let current_music=ref('')
let current_music_obj=ref({})
let scrollbar = ref(null)
let currentAudio = ref(null);
let emit = defineEmits(['changeMusic'])
let targetElements = ref([]);
// 返回渐变背景样式，角度用CSS变量控制
let insertKeyframes = () => {
  if (hasInsertedKeyframes.value) return;
  let style = document.createElement('style');
  style.innerHTML = `
    @keyframes neonGradient {
      0% {
        background-position: 0% 100%;
      }
      25% {
        background-position: 50% 75%;
      }
      50% {
        background-position: 100% 0%;
      }
      75% {
        background-position: 50% 25%;
      }
      100% {
        background-position: 0% 100%;
      }
    }
  `;
  document.head.appendChild(style);
  hasInsertedKeyframes.value = true;
};
let gradientStyle = (color, color1, color2, duration = '10s') => {
  insertKeyframes();
  return {
    background: `linear-gradient(
      var(--angle, 0deg),
      ${color} 0%,
      ${color} 25%,
      ${color1} 40%,
      ${color1} 60%,
      ${color2} 75%,
      ${color2} 100%
    )`,
    backgroundSize: '300% 300%',
    animation: `neonGradient ${duration} cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  };
};
let getCurrent = (item) => {
  return (music_type.value == 1 ? current_music.value == item.id : current_music.value == item.materialId);
}
let togglePlay=(item,scroll)=>{
    console.log('togglePlay');
    close_aduio()
    if(music_type.value==1){
        current_music.value=item.id
    }else{
        current_music.value=item.materialId
    }
    current_music_obj.value=item
  scroll&&scrollToElement()

}
let play_music=(item,index)=>{
    console.log(item);
    
    if (item.isPlaying) {
    // 如果当前正在播放，点击则暂停
    if (item.audioElement) {
      item.audioElement.pause();
      item.audioElement.currentTime = 0;
      item.audioElement = null;
      currentAudio.value = null; // 清空全局引用
    }
    item.isPlaying = false;
    return;
  }

  // 暂停其他所有音频
  music_list.value.forEach((it, i) => {
    if (i !== index && it.audioElement) {
      it.audioElement.pause();
      it.audioElement.currentTime = 0;
      it.audioElement = null;
      it.isPlaying = false;
    }
  });

  // 播放当前音频
  item.isPlaying = true;

  try {
    
    let audioUrl = item.url || 'http://example.com/default-audio.mp3';
    console.log(item,'url');
    
    item.audioElement = new Audio(audioUrl);
    item.audioElement.volume = item.volume / 100;

    item.audioElement.addEventListener('ended', () => {
      item.isPlaying = false;
      item.audioElement.currentTime = 0;
      item.audioElement = null;
      currentAudio.value = null; // 清空全局引用
    });

    item.audioElement.addEventListener('error', () => {
      item.isPlaying = false;
      item.audioElement = null;
      currentAudio.value = null; // 清空全局引用
      // ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`);
     
    });

    item.audioElement.load();

    let playPromise = item.audioElement.play();
    currentAudio.value = item.audioElement; // 保存当前播放实例
    if (playPromise !== undefined) {
      playPromise.catch(() => {
        // ElMessage.error(`播放失败`);
        item.isPlaying = false;
        item.audioElement = null;
        currentAudio.value = null; // 清空全局引用
      });
    }
  } catch (err) {
    console.log(err);
    
    ElMessage.error(`暂时无法播放`);
    item.isPlaying = false;
    item.audioElement = null;
    currentAudio.value = null;
  }
}
let close_aduio=()=>{
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value.currentTime = 0;
    currentAudio.value = null;
  }
}
let setRef = (index) => {
	return (el) => {
		if (el) {
			targetElements.value[index] = el; // 将元素存储到数组中

		}
	};
};
//背景音乐选中自动滑动到指定位置
let scrollToElement = async() => {
    await nextTick()
    await nextTick()

    let scrollbar1 = scrollbar.value;
    let target = targetElements.value.find(el =>{
 
      return  el && el.dataset.idName ==current_music.value
    });


    if (target && scrollbar1) {
      // 计算目标元素相对于滚动容器的偏移
      let targetOffset = 0;
      let el = target;
      const container = scrollbar1.$el;

      while (el && el !== container) {
        targetOffset += el.offsetTop;
        el = el.offsetParent;
      }

      // 滚动到目标位置
      scrollbar1.scrollTo({ top: targetOffset, behavior: 'smooth' });
    }

};
defineExpose({
    music_list,
    music_type,
    togglePlay,
    update() {
        scrollbar.value?.update()
    },
    close_aduio,
    current_music,
    current_music_obj,
    loading
})
</script>
<style lang="scss" scoped>
.set_music_upload_list {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 32px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    .el-scrollbar {
        overflow: visible;
        ::v-deep(.el-scrollbar__bar) {
            right: -8px;
        }
        ::v-deep(.el-scrollbar__thumb) {
            background-color: #0AAF60;
            will-change: transform;
            opacity: 1;
        }
        ::v-deep(.el-scrollbar__view){
            width: 100%;
            height: 100%;
            transform: translateZ(0);
        }
        ::v-deep(.el-scrollbar__wrap) {
            /* 取消硬件加速，避免模糊 */
            transform: none !important;
            will-change: scroll-position;
            -webkit-overflow-scrolling: touch;
        }
        .set_music_upload_list_item {
            display: flex;
            align-items: center;
            padding: 4px;
            padding-right: 24px;
            cursor: pointer;
            margin-bottom: 12px;
             cursor: pointer;
            .digital_human_set_music_list_content_item_img{
                width: 100%;
                display: flex;
                align-items: center;
                border-radius: 4px;
                width: 66px;
                height: 66px;
                border-radius: 4px;
                margin-right: 10px;
                backface-visibility: hidden;
                position: relative;
                .digital_human_set_music_list_content_item_aduio{
                    position: absolute;
                    z-index: 2;
                    display: none;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    left: 0;
                    background-color: rgba(0, 0, 0, 0.4);
                    img{
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        z-index: 3;
                        width: 17px;
                        height: 21px;
                    }
                }
                 &:hover {
                    .digital_human_set_music_list_content_item_aduio {
                        display: block;
                    }
                }
            }
            .digital_human_set_music_list_content_item_text{
                display: flex;
                flex-direction: column;
                will-change: transform;
                /* 或者 */
                // transform: translateZ(0);
                .digital_human_set_music_list_content_item_text_name{
                    font-size: 14px;
                    line-height: 22px;
                    color: #000000;
                    margin-bottom: 3px;
                    -webkit-font-smoothing: antialiased;
                    text-rendering: optimizeLegibility;
                     will-change: transform;
                }
                .digital_human_set_music_list_content_item_text_time{
                    font-size: 14px;
                    line-height: 22px;
                    color: rgba(0, 0, 0, 0.45);
                    -webkit-font-smoothing: antialiased;
                    text-rendering: optimizeLegibility;
                }
            }
            .set_music_upload_list_item_select{
                margin-left: auto;
                width: 24px;
                height: 24px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            &.current{
                background-color: #F6F7FB;
            }
            &:hover{
                border: 1px solid #0AAF60;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }
        }
    }
}
</style>