<template>
    <div class="set_music_upload">
        <el-upload
            ref="uploadRef"
            class="upload-demo"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            :show-file-list="false"
            accept="audio/*"
            :auto-upload="false"
            >
            <div class="set_music_upload_img">
             <img src="@/assets/images/digitalHuman/set_music_upload_img.svg" alt="">
            </div>
            <span>自定义上传</span>
        </el-upload>
    </div>
</template>
<script setup>
import { ref,defineEmits, nextTick  } from "vue";
import { ElMessage, ElLoading } from 'element-plus'
import { useFileUpload } from '@/views/modules/digitalHuman/utils/upload.js';
import { get_signature } from '@/api_my/AlDubb'
import { useloginStore } from '@/stores/login'
let {  fileChange } = useFileUpload();
let loading = ref(null);
let emit = defineEmits(['upload']);
let loginStore = useloginStore()
let uploadRef=ref(null)
// 获取的文件上传路径
const fileUrl = ref('')
const signatureData = ref({})
let handleFileChange=async (file, fileList) => {
    await nextTick()
    loading.value=ElLoading.service({
      fullscreen: true,
      lock: true,
      text: '上传中...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    const uploadRequest = await fileChange(file, fileList);
   
    console.log(uploadRequest,'uploadRequest');
    loading.value.close();
    uploadRef.value.clearFiles()
    emit('upload');
}

let beforeUpload = async(file) => {
    console.log('beforeUpload');
    await getSignatureFun(file)
    const allowedExtensions = ['mp3', 'wav'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    const isValidExtension = allowedExtensions.includes(fileExtension);
    if (!isValidExtension) {
        this.$message.error('仅支持 MP3/WAV 格式！');
        return false;
    }
    return true;
};
// 上传文件之前获取oss签名
let getSignatureFun=async (file)=>{
  try {
    const response = await get_signature({ userId:loginStore.userId, fileType:file.type})
    let { code , data } = response
    if (code !== 0) {
      return
    }
    signatureData.value = data
    fileUrl.value = `${data.host}/${data.key}?Expires=${data.expire}&OSSAccessKeyId=${data.accessid}&Signature=${data.signature}`
  } catch (err) {
    console.log(err)
  } finally {

  }
}
</script>
<style lang="scss" scoped>
.set_music_upload{
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 8px 35px;
    height: 42px;
    border: 1px solid #0AAF60;
    border-radius: 100px;
    width: fit-content;
    margin-bottom: 20px;
    ::v-deep(.upload-demo){
        width: 100%;
        height: 100%;
        .el-upload{
            .set_music_upload_img{
                width: 24px;
                height: 24px;
                margin-right: 4px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            span{
                font-size: 16px;
                line-height: 22px;
                color: #0AAF60;
            }
        }
        

    }
    
}
</style>