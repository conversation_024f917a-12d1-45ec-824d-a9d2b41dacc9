<template>
  <div
    class="volume-slider"
    :class="`position-${position}`"
    v-if="visible"
    ref="sliderRef"
    @click.stop
  >
    <span class="volume-slider-text">{{ modelValue }}</span>
    <el-slider
      v-model="internalValue"
      :show-tooltip="false"
      vertical
      @input="onInput"
    />
    <!-- <span class="volume-slider-label">音量</span> -->
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, unref } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    required: true,
  },
  visible: {
    type: Boolean,
    default: false,
  },
  // 传入一个或多个 ref，类型是数组或单个 ref
  excludeRefs: {
    type: [Array, Object],
    default: () => [],
  },
  position: { type: String, default: 'bottom' }, // 新增位置
})

const emit = defineEmits(['update:modelValue', 'update:visible'])

const sliderRef = ref(null)
const internalValue = ref(props.modelValue)

// 同步外部传入的 modelValue 到内部
watch(() => props.modelValue, (val) => {
  internalValue.value = val
})

// 内部滑块值变化，向外同步
const onInput = (val) => {
  emit('update:modelValue', val)
}

// 判断点击是否在组件内或排除的 ref 元素内
const isClickInsideOrExclude = (event) => {
  const target = event.target

  // 1. 判断是否点击在组件内
  if (sliderRef.value && sliderRef.value.contains(target)) {
    return true
  }

  // 2. 判断是否点击在排除的 ref 元素内
  let refsArray = []
  if (Array.isArray(props.excludeRefs)) {
    refsArray = props.excludeRefs
  } else if (props.excludeRefs) {
    refsArray = [props.excludeRefs]
  }

  for (const r of refsArray) {
    const el = unref(r)
    if (el && el.contains && el.contains(target)) {
      return true
    }
  }

  return false
}

// 全局点击事件处理，点击外部关闭组件
const onDocumentClick = (event) => {
  if (!isClickInsideOrExclude(event)) {
    emit('update:visible', false)
  }
}

onMounted(() => {
  document.addEventListener('click', onDocumentClick)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', onDocumentClick)
})
</script>

<style scoped lang="scss">
/* 样式保持不变 */
.volume-slider {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 27px;
  height: 109px;
  background: rgba(29, 33, 41, 0.85);
  border-radius: 2px;
  padding: 3px 0 12px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  &.position-bottom {
    top: 100%; left: 50%; transform: translateX(-50%);
  }
  &.position-top {
    top: auto; bottom: 100%; left: 50%; transform: translateX(-50%);
  }
  &.position-left {
    right: 100%; top: 50%; transform: translateY(-50%);
    flex-direction: row; width: 109px; height: 27px;
  }
  &.position-right {
    left: 100%; top: 50%; transform: translateY(-50%);
    flex-direction: row; width: 109px; height: 27px;
  }
  .volume-slider-text {
    font-size: 14px;
    line-height: 22px;
    color: #fff;
    text-align: center;
    margin-bottom: 14px;
    
  }

  .el-slider {
    width: 3px;
    flex: 1;

    ::v-deep(.el-slider__runway) {
      height: 100%;
      background-color: #fff;
      display: flex;
      justify-content: center;
      width: 3px;
      margin: 0;
      padding: 0;

      .el-slider__bar {
        background-color: #0AAF60;
        width: 3px;
        height: 100%;
      }

      .el-slider__button-wrapper {
        width: 12px;
        height: 12px;
        left: 50%;
        transform: translateX(-50%);

        .el-slider__button {
          width: 100%;
          height: 100%;
          background-color: #fff;
          border: none;
        }
      }
    }
  }

  .volume-slider-label {
    font-size: 12px;
    line-height: 24px;
    color: #000;
    text-align: center;
    margin-top: 4px;
  }
}
</style>
