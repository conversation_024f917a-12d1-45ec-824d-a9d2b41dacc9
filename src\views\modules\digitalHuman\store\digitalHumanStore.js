import { defineStore } from 'pinia';
// 建议安装nanoid来生成唯一的ID: npm install nanoid
// 如果没有nanoid，可以使用简单的时间戳+随机数方案
const generateId = () => {
	return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 在JS中，我们不使用interface，但期望的事件对象结构如下：
// event = {
//   id: string,
//   type: 'SPEAK' | 'SHOW_TEXT' | 'CHANGE_BACKGROUND' | 'SUBTITLE',
//   startTime: number,
//   duration: number,
//   payload: object
// }

export const useDigitalHumanStore = defineStore('digitalHumanEditor', {
	// 禁用持久化，避免保存测试数据
	persist: false,
	state: () => ({
		totalDuration: 0, // 视频总时长，单位：秒（默认为0，避免显示虚假时长）
		currentTime: 0,    // 当前播放头时间，初始位置设为0秒
		timelineEvents: [],
		selectedEventId: null,
		isPlaying: false,    // 播放状态
		animationFrameId: null,  // 动画帧ID
		playStartTime: null,     // 播放开始的系统时间
		playStartPosition: null, // 播放开始时的播放位置
		
		// 新增字幕相关状态
		subtitleUrl: '',           // 字幕文件URL
		ttsAudioUrl: '',          // TTS音频文件URL
		subtitleData: [],         // 解析后的字幕数据 [{text, startTime, endTime}]
		currentSubtitle: '',      // 当前显示的字幕文本
		isSubtitleLoaded: false,  // 字幕是否已加载

		// 🎵 音频播放相关状态
		audioElement: null,       // HTML5 Audio元素
		isAudioLoaded: false,     // 音频是否已加载
		audioError: null,         // 音频加载错误信息

		// 🎯 新增：完整作品数据管理
		originalWorkData: null,   // 存储getDigitalWork接口返回的完整数据
		workDataLoading: false,   // 作品数据加载状态
		workDataError: null,      // 作品数据加载错误信息
	}),

	getters: {
		selectedEvent(state) {
			if (!state.selectedEventId) return null;
			return state.timelineEvents.find(e => e.id === state.selectedEventId) || null;
		},
		
		// 获取当前时间应该显示的字幕
		activeSubtitle(state) {
			// 如果没有加载字幕数据，返回手动设置的字幕文本
			if (!state.isSubtitleLoaded || state.subtitleData.length === 0) {
				return state.currentSubtitle;
			}

			// 查找当前时间范围内的字幕（使用严格的时间匹配）
			const currentSubtitleItem = state.subtitleData.find(
				item => state.currentTime >= item.startTime && state.currentTime < item.endTime
			);



			// 只返回匹配的字幕文本，如果没有匹配则返回空字符串
			return currentSubtitleItem ? currentSubtitleItem.text : '';
		}
	},

	actions: {
		addTimelineEvent(eventData) {
			const newEvent = {
				id: generateId(),
				...eventData,
			};
			this.timelineEvents.push(newEvent);
			this.selectEvent(newEvent.id); // 添加后自动选中
		},

		updateEvent(updatedEvent) {
			const index = this.timelineEvents.findIndex(e => e.id === updatedEvent.id);
			if (index !== -1) {
				this.timelineEvents[index] = updatedEvent;
			}
		},

		selectEvent(eventId) {
			this.selectedEventId = eventId;
		},

		setCurrentTime(time) {
			if (time < 0) time = 0;
			if (time > this.totalDuration) time = this.totalDuration;
			console.log('⏰ setCurrentTime调用:', {
				设置时间: time,
				总时长: this.totalDuration,
				当前时间: this.currentTime
			});
			this.currentTime = time;
		},
		
		// 新增字幕管理方法
		setSubtitleUrl(url) {
			this.subtitleUrl = url;
			this.isSubtitleLoaded = false;
			// 清空之前的字幕数据
			this.subtitleData = [];
			this.currentSubtitle = '';
		},
		
		setTtsAudioUrl(url) {
			this.ttsAudioUrl = url;
			this.loadAudio(url);
		},

		// 🎵 音频加载和控制方法
		loadAudio(url) {
			if (!url) {
				console.warn('音频URL为空，无法加载');
				return;
			}

			// 清理之前的音频元素
			if (this.audioElement) {
				this.audioElement.pause();
				this.audioElement.src = '';
				this.audioElement = null;
			}

					// 创建新的音频元素
		this.audioElement = new Audio();
		this.audioElement.crossOrigin = 'anonymous'; // 处理CORS问题
		this.audioElement.preload = 'auto'; // 预加载音频
		
		// 🔧 修复音频播放问题的关键设置
		this.audioElement.volume = 1.0; // 确保音量为最大
		this.audioElement.muted = false; // 确保不是静音状态
		this.audioElement.loop = false; // 不循环播放
		
		this.isAudioLoaded = false;
		this.audioError = null;

			console.log('🎵 创建音频元素，URL:', url);

			// 设置音频事件监听器
			this.audioElement.addEventListener('loadeddata', () => {
				console.log('📡 loadeddata事件触发');
			});

			this.audioElement.addEventListener('canplay', () => {
				this.isAudioLoaded = true;
				console.log('✅ 音频可以播放:', url);

				// 更新总时长（如果音频时长更准确）
				if (this.audioElement.duration && this.audioElement.duration > 0) {
					this.totalDuration = Math.ceil(this.audioElement.duration);
					console.log('✅ 音频总时长已更新:', this.totalDuration, '秒');
				}
			});

			this.audioElement.addEventListener('loadstart', () => {
				console.log('📡 开始加载音频...');
			});

			this.audioElement.addEventListener('progress', () => {
				console.log('📡 音频加载进度更新...');
			});

			this.audioElement.addEventListener('error', (e) => {
				const error = e.target.error;
				let errorMessage = '音频加载失败';

				if (error) {
					switch(error.code) {
						case error.MEDIA_ERR_ABORTED:
							errorMessage = '音频加载被中止';
							break;
						case error.MEDIA_ERR_NETWORK:
							errorMessage = '网络错误，无法加载音频';
							break;
						case error.MEDIA_ERR_DECODE:
							errorMessage = '音频解码失败';
							break;
						case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
							errorMessage = '音频格式不支持或URL无效';
							break;
						default:
							errorMessage = '未知音频错误';
					}
				}

				this.audioError = errorMessage;
				this.isAudioLoaded = false;
				// 🔧 音频加载失败时，重置时长为0，避免显示虚假时长
				this.totalDuration = 0;
				console.error('❌ 音频加载失败:', errorMessage, e);
				console.error('❌ 音频URL:', url);
				console.log('🔧 已重置时长为0，因为音频无效');
			});

			this.audioElement.addEventListener('timeupdate', () => {
				// 如果音频正在播放，同步时间轴
				if (this.isPlaying && this.audioElement && !this.audioElement.paused) {
					this.currentTime = this.audioElement.currentTime;
				}
			});

			this.audioElement.addEventListener('ended', () => {
				// 🔧 音频播放结束，优化重置逻辑
				console.log('🎵 音频播放结束');

				// 先确保进度条显示100%
				this.currentTime = this.totalDuration;

				// 延迟重置，让用户看到完整的播放进度
				setTimeout(() => {
					this.pause();
					this.currentTime = 0;
					console.log('🎵 音频播放完成，已重置到起始位置');
				}, 500); // 500ms延迟，给用户足够时间看到完成状态
			});

			// 开始加载音频
			this.audioElement.src = url;
			this.audioElement.load();
			console.log('🎵 开始加载音频:', url);
		},
		
		setSubtitleData(subtitleArray) {
			this.subtitleData = subtitleArray;
			this.isSubtitleLoaded = true;
			
			// 清除现有的字幕事件
			this.timelineEvents = this.timelineEvents.filter(event => event.type !== 'SUBTITLE');
			
			// 为每个字幕片段创建时间轴事件
			subtitleArray.forEach(subtitle => {
				this.addTimelineEvent({
					type: 'SUBTITLE',
					startTime: subtitle.startTime,
					duration: subtitle.endTime - subtitle.startTime,
					payload: {
						text: subtitle.text
					}
				});
			});
		},
		
		// 设置当前字幕文本（用于手动控制）
		setCurrentSubtitle(text) {
			// 确保文本是干净的，移除多余的空白和重复内容
			const cleanText = text ? text.trim() : '';
			this.currentSubtitle = cleanText;
		},

		// 清除所有字幕数据
		clearSubtitleData() {
			this.subtitleData = [];
			this.currentSubtitle = '';
			this.isSubtitleLoaded = false;
			this.subtitleUrl = '';
			this.ttsAudioUrl = '';

			// 🎵 清理音频资源
			this.clearAudio();

			// 清除字幕相关的时间轴事件
			this.timelineEvents = this.timelineEvents.filter(event => event.type !== 'SUBTITLE');
		},

		// 🔄 模式切换专用重置方法
		resetForModeSwitch() {
			// 停止播放
			this.pause();

			// 重置播放时间到初始位置
			this.currentTime = 0;
			this.totalDuration = 0;

			// 清除所有字幕和音频数据
			this.clearSubtitleData();

			// 清除所有时间轴事件
			this.timelineEvents = [];
			this.selectedEventId = null;

			// 重置播放状态
			this.isPlaying = false;
			this.playStartTime = null;
			this.playStartPosition = null;

			// 清理动画帧
			if (this.animationFrameId) {
				cancelAnimationFrame(this.animationFrameId);
				this.animationFrameId = null;
			}
		},
		
		// 加载假数据进行测试
		loadMockSubtitleData() {
			const mockData = [
				{ text: '欢迎来到数字人配音系统', startTime: 0, endTime: 3 },
				{ text: '这里可以实现智能语音合成', startTime: 3, endTime: 6 },
				{ text: '支持多种音色和语言', startTime: 6, endTime: 9 },
				{ text: '让您的内容更加生动有趣', startTime: 9, endTime: 12 },
				{ text: '现在开始体验吧！', startTime: 12, endTime: 15 }
			];
			
			this.setSubtitleData(mockData);
			// 不再设置默认的15秒时长，让用户看到真实的时长状态
			console.log('已加载假字幕数据:', mockData);
		},

		// 🎵 播放控制方法（增强版，支持音频同步）
		play() {
			if (this.isPlaying) return;

			console.log('🎵 开始播放，当前时间:', this.currentTime);
			console.log('🔍 音频状态检查:');
			console.log('  - audioElement存在:', !!this.audioElement);
			console.log('  - isAudioLoaded:', this.isAudioLoaded);
			console.log('  - ttsAudioUrl:', this.ttsAudioUrl);
			console.log('  - audioError:', this.audioError);

			this.isPlaying = true;

					// 如果有音频且已加载，播放音频
		if (this.audioElement && this.isAudioLoaded) {
			console.log('🎵 尝试播放音频...');
			console.log('🔧 音频状态详情:', {
				'音量': this.audioElement.volume,
				'静音': this.audioElement.muted,
				'时长': this.audioElement.duration,
				'网络状态': this.audioElement.networkState,
				'就绪状态': this.audioElement.readyState,
				'当前URL': this.audioElement.src.substring(0, 100) + '...'
			});
			
			try {
				// 🔧 确保音频设置正确
				this.audioElement.volume = 1.0;
				this.audioElement.muted = false;
				
				// 设置音频播放位置
				this.audioElement.currentTime = this.currentTime;

				// 播放音频
				const playPromise = this.audioElement.play();
				if (playPromise !== undefined) {
					playPromise.then(() => {
						console.log('✅ 音频开始播放');
						console.log('📊 播放状态:', {
							'当前时间': this.audioElement.currentTime,
							'音量': this.audioElement.volume,
							'静音': this.audioElement.muted,
							'暂停状态': this.audioElement.paused
						});
					}).catch(error => {
						console.error('❌ 音频播放失败:', error.name, error.message);
						console.error('🔍 失败原因分析:');
						console.error('  - NotAllowedError: 浏览器阻止自动播放，需要用户交互');
						console.error('  - NotSupportedError: 音频格式不支持');
						console.error('  - AbortError: 播放被中止');
						
						// 音频播放失败，回退到时间轴模式
						this.startTimelinePlayback();
					});
				}
			} catch (error) {
				console.error('❌ 音频播放异常:', error);
				// 音频播放异常，回退到时间轴模式
				this.startTimelinePlayback();
			}
			} else {
				// 没有音频或音频未加载，使用时间轴模式
				console.log('⏰ 使用时间轴播放模式，原因:');
				if (!this.audioElement) console.log('  - 没有音频元素');
				if (!this.isAudioLoaded) console.log('  - 音频未加载完成');
				this.startTimelinePlayback();
			}
		},



		// 启动时间轴播放模式（无音频时的回退方案）
		startTimelinePlayback() {
			this.playStartTime = performance.now(); // 记录播放开始的系统时间
			this.playStartPosition = this.currentTime; // 记录播放开始时的位置

			// 启动RAF播放循环
			const playLoop = () => {
				if (!this.isPlaying) return;

				const now = performance.now();
				const elapsed = (now - this.playStartTime) / 1000; // 转换为秒
				const newTime = this.playStartPosition + elapsed;

				// 🔧 优化播放结束判断：增加容差避免提前结束，确保进度条能到达终点
				const tolerance = 0.05; // 50毫秒容差
				if (newTime >= this.totalDuration - tolerance) {
					// 先设置为总时长，确保进度条显示100%
					this.currentTime = this.totalDuration;

					// 延迟重置，让用户看到完整的进度
					setTimeout(() => {
						this.pause();
						this.currentTime = 0;
						console.log('🎵 时间轴播放完成，已重置');
					}, 300); // 300ms延迟重置
					return;
				}

				this.currentTime = newTime;
				this.animationFrameId = requestAnimationFrame(playLoop);
			};

			this.animationFrameId = requestAnimationFrame(playLoop);
		},

		pause() {
			console.log('⏸️ 暂停播放');

			this.isPlaying = false;

			// 暂停音频
			if (this.audioElement && !this.audioElement.paused) {
				this.audioElement.pause();
				console.log('✅ 音频已暂停');
			}

			// 清理时间轴播放循环
			if (this.animationFrameId) {
				cancelAnimationFrame(this.animationFrameId);
				this.animationFrameId = null;
			}
			this.playStartTime = null;
			this.playStartPosition = null;
		},

		stop() {
			console.log('⏹️ 停止播放');

			this.pause();
			this.currentTime = 0; // 停止时重置到0秒

			// 重置音频位置
			if (this.audioElement) {
				this.audioElement.currentTime = 0;
			}
		},

		// 🎵 切换播放/暂停状态（增强版）
		togglePlay() {
			console.log('🎮 切换播放状态，当前状态:', this.isPlaying ? '播放中' : '暂停');

			if (this.isPlaying) {
				this.pause();
			} else {
				this.play();
			}
		},

		// 🎵 清理音频资源
		clearAudio() {
			if (this.audioElement) {
				this.audioElement.pause();
				this.audioElement.src = '';
				this.audioElement = null;
			}
			this.isAudioLoaded = false;
			this.audioError = null;
			console.log('🧹 音频资源已清理');
		},

		// 🔧 调试方法：手动测试音频播放
		debugAudioPlay() {
			console.log('🔧 === 音频调试信息 ===');
			console.log('audioElement:', this.audioElement);
			console.log('isAudioLoaded:', this.isAudioLoaded);
			console.log('ttsAudioUrl:', this.ttsAudioUrl);
			console.log('audioError:', this.audioError);
			
			if (this.audioElement) {
				console.log('音频详细状态:', {
					'src': this.audioElement.src,
					'duration': this.audioElement.duration,
					'currentTime': this.audioElement.currentTime,
					'volume': this.audioElement.volume,
					'muted': this.audioElement.muted,
					'paused': this.audioElement.paused,
					'ended': this.audioElement.ended,
					'readyState': this.audioElement.readyState,
					'networkState': this.audioElement.networkState
				});
				
				// 尝试手动播放
				console.log('🎵 尝试手动播放音频...');
				this.audioElement.volume = 1.0;
				this.audioElement.muted = false;
				this.audioElement.currentTime = 0;
				
				const playPromise = this.audioElement.play();
				if (playPromise) {
					playPromise.then(() => {
						console.log('✅ 手动播放成功！');
					}).catch(error => {
						console.error('❌ 手动播放失败:', error);
					});
				}
			} else {
				console.log('❌ 没有音频元素');
			}
		},

		// 🎯 新增：完整作品数据管理 actions
		/**
		 * 加载数字人作品数据
		 * @param {number} workId - 作品ID
		 * @returns {Promise<Object|null>} 返回作品数据或null
		 */
		async loadWorkData(workId) {
			if (!workId) {
				console.warn('⚠️ 作品ID为空，无法加载作品数据');
				this.originalWorkData = null;
				this.workDataError = null;
				return null;
			}

			// 防止重复加载同一个作品
			if (this.workDataLoading) {
				console.warn('⚠️ 作品数据正在加载中，请勿重复请求');
				return this.originalWorkData;
			}

			try {
				this.workDataLoading = true;
				this.workDataError = null;

				console.log('📡 开始加载作品数据，ID:', workId);

				// 动态导入getDigitalWork接口
				const { getDigitalWork } = await import('@/api/digitalHuman');
				
				// 调用接口获取作品详情数据
				const workData = await getDigitalWork({ id: parseInt(workId) });

				if (!workData) {
					throw new Error('获取作品数据失败，服务器返回空数据');
				}

				// 💾 保存完整的原始工作数据
				this.originalWorkData = workData;
				this.workDataError = null;

				console.log('✅ 作品数据加载成功:', {
					作品ID: workId,
					数据字段: Object.keys(workData),
					audioJson: workData.audioJson ? '✅' : '❌',
					bgJson: workData.bgJson ? '✅' : '❌',
					commonJson: workData.commonJson ? '✅' : '❌',
					personJson: workData.personJson ? '✅' : '❌',
					subtitleConfigJson: workData.subtitleConfigJson ? '✅' : '❌',
					title: workData.title || '无标题'
				});

				return workData;

			} catch (error) {
				console.error('❌ 加载作品数据失败:', error);
				this.workDataError = error.message || '加载作品数据失败';
				this.originalWorkData = null;
				return null;
			} finally {
				this.workDataLoading = false;
			}
		},

		/**
		 * 获取完整的作品数据
		 * @returns {Object|null} 返回完整的作品数据或null
		 */
		getOriginalWorkData() {
			return this.originalWorkData;
		},

		/**
		 * 清空作品数据
		 */
		clearWorkData() {
			this.originalWorkData = null;
			this.workDataError = null;
			this.workDataLoading = false;
			console.log('🧹 作品数据已清空');
		},

		/**
		 * 获取指定字段的数据
		 * @param {string} fieldName - 字段名称，如'audioJson', 'bgJson'等
		 * @returns {any} 返回指定字段的数据
		 */
		getWorkDataField(fieldName) {
			if (!this.originalWorkData) {
				console.warn(`⚠️ 原始作品数据为空，无法获取字段: ${fieldName}`);
				return null;
			}

			if (!this.originalWorkData.hasOwnProperty(fieldName)) {
				console.warn(`⚠️ 原始作品数据中不存在字段: ${fieldName}`);
				return null;
			}

			return this.originalWorkData[fieldName];
		},

		/**
		 * 检查作品数据是否已加载
		 * @returns {boolean} 返回是否已加载作品数据
		 */
		hasWorkData() {
			return this.originalWorkData !== null;
		}
	},
});

// 🔧 开发环境下暴露调试方法到全局
if (typeof window !== 'undefined') {
	window.debugDigitalHumanAudio = () => {
		const store = useDigitalHumanStore();
		store.debugAudioPlay();
	};
} 