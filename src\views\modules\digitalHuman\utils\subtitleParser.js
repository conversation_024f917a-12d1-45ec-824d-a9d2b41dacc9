/**
 * 字幕解析工具
 * 功能：解析SRT字幕文件，处理字幕时间和文本
 */

/**
 * 解析SRT格式的字幕内容
 * @param {string} srtContent - SRT文件内容
 * @returns {Array} - 解析后的字幕数组 [{text, startTime, endTime}]
 */
export const parseSrtContent = (srtContent) => {
    if (!srtContent) return [];

    try {
        // 将SRT内容按块分割（空行分隔）
        const blocks = srtContent.trim().split(/\r?\n\r?\n/);

        const subtitles = [];

        blocks.forEach((block, index) => {
            const lines = block.split(/\r?\n/);
            // SRT格式：1. 序号 2. 时间范围 3+. 文本内容（可能有多行）
            if (lines.length >= 3) {
                try {
                    // 解析时间范围：00:00:01,500 --> 00:00:04,400
                    const timeRange = lines[1];
                    const timeMatch = timeRange.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
                    
                    if (timeMatch) {
                        const startTime = timeStringToSeconds(timeMatch[1]);
                        const endTime = timeStringToSeconds(timeMatch[2]);
                        
                        // 提取文本内容（跳过序号和时间行）
                        const text = lines.slice(2)
                            .join(' ')
                            .replace(/<[^>]*>/g, '') // 移除HTML标签
                            .trim();
                        
                        if (text) {
                            subtitles.push({
                                text,
                                startTime,
                                endTime,
                                duration: endTime - startTime
                            });
                        }
                    }
                } catch (error) {
                    // 解析失败，跳过该块
                }
            }
        });

        return subtitles;
    } catch (error) {
        return [];
    }
};

/**
 * 将时间字符串转换为秒数
 * @param {string} timeString - 时间字符串，格式：00:00:01,500
 * @returns {number} - 转换后的秒数
 */
export const timeStringToSeconds = (timeString) => {
    try {
        // 处理格式：00:00:01,500
        const [timePart, millisecondsPart] = timeString.split(',');
        const [hours, minutes, seconds] = timePart.split(':').map(Number);
        const milliseconds = parseInt(millisecondsPart);
        
        return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
    } catch (error) {
        console.error('时间字符串转换失败:', timeString, error);
        return 0;
    }
};

/**
 * 从URL获取并解析SRT字幕文件
 * @param {string} srtUrl - SRT文件URL
 * @returns {Promise<Array>} - 解析后的字幕数组
 */
export const fetchAndParseSrtContent = async (srtUrl) => {
    if (!srtUrl) {
        return [];
    }

    try {
        
        // 确保使用HTTPS
        const httpsUrl = srtUrl.startsWith('http:') ? srtUrl.replace('http:', 'https:') : srtUrl;
        
        // 发送请求获取SRT文件内容
        const response = await fetch(httpsUrl, {
            method: 'GET',
            mode: 'cors',
            headers: {
                'Content-Type': 'text/plain',
            }
        });

        if (!response.ok) {
            throw new Error(`获取SRT文件失败: ${response.status} ${response.statusText}`);
        }

        // 获取文本内容
        const srtContent = await response.text();

        // 解析SRT内容
        const subtitleData = parseSrtContent(srtContent);

        return subtitleData;
    } catch (error) {
        return [];
    }
};

/**
 * 生成假字幕数据用于测试
 * @param {number} duration - 总时长（秒）
 * @returns {Array} - 假字幕数据数组
 */
export const generateMockSubtitleData = (duration = 30) => {
    const mockTexts = [
        '欢迎来到数字人配音系统',
        '这里可以实现智能语音合成',
        '支持多种音色和语言选择',
        '让您的内容更加生动有趣',
        '字幕与音频完美同步播放',
        '现在开始体验这项技术吧！'
    ];
    
    const subtitles = [];
    const segmentDuration = duration / mockTexts.length;
    
    mockTexts.forEach((text, index) => {
        subtitles.push({
            text,
            startTime: index * segmentDuration,
            endTime: (index + 1) * segmentDuration,
            duration: segmentDuration
        });
    });
    
    console.log('生成假字幕数据:', subtitles);
    return subtitles;
};

/**
 * 验证字幕数据格式
 * @param {Array} subtitleData - 字幕数据数组
 * @returns {boolean} - 验证结果
 */
export const validateSubtitleData = (subtitleData) => {
    if (!Array.isArray(subtitleData)) {
        return false;
    }
    
    for (let i = 0; i < subtitleData.length; i++) {
        const subtitle = subtitleData[i];
        
        if (!subtitle.text || typeof subtitle.text !== 'string') {
            return false;
        }
        
        if (typeof subtitle.startTime !== 'number' || subtitle.startTime < 0) {
            return false;
        }
        
        if (typeof subtitle.endTime !== 'number' || subtitle.endTime <= subtitle.startTime) {
            return false;
        }
    }
    
    return true;
}; 