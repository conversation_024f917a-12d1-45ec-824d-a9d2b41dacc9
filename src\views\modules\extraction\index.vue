<template>
    <div class="extraction">
        <div class="cont">
            <div class="extraction_head">
                <h2>文案提取</h2>
                <div class="vertical-line">|</div>
                <div class="tabs" :class="{ disabled: isDisabled }">
                    <span @click="tabsClick(index)" :class="{ tabsActive: activeIndex == index }"
                        v-for="(item, index) in tabs" :key="index">{{ item }}</span>
                </div>
            </div>
            <template v-if="!activeIndex">
                <!-- 上传格式 -->
                <div class="sharing">
                    <div class="sharing_caption">
                        <span>*</span>
                        <span>上传格式：</span>
                    </div>
                    <div class="card">
                        <div @click="cardClick(index, card.type)" 
                             :class="{
                            cardStyleOne: cardIndex == index,
                            cardStyleTwo: cardIndex != index,
                                cardDisabled: isDisabled
                             }" 
                             v-for="(card, index) in cardList" :key="index">
                            <div class="container">
                                <div class="inner-oval"></div>
                            </div>
                            <span class="text">{{ card.name }}</span>
                        </div>
                    </div>
                </div>

                <!-- 视频 -->
                <div class="Video">
                    <!-- 视频链接 -->
                    <div class="video_link" v-if="typeIndex === 0">
                        <div class="video_link_one">
                            <div class="video_hade">
                                <span>* </span>
                                <span>{{ videoName }}</span>
                            </div>
                            <!-- 这是视频链接 -->
                            <div class="child">
                                <CommonInput class="parent-wrapper" :placeholder="placeholder" v-model="videoUrl"
                                    upperText="上传视频" @submit="handleParse" @clear="handleClear" />
                                <!-- 提取文件 -->
                                <el-button class="btn" @click="videoClick('链接')" :disabled="videoLinkisParsing"
                                    :loading="videoLinkisParsing">
                                    {{
                                        videoLinkisParsing
                                            ? "提取中..."
                                            : "提取文案"
                                    }}
                                </el-button>
                                <transition>
                                    <p v-show="isParsing" class="parsing-tip">
                                        <span class="loading-dots"></span>
                                        文案提取中，请稍等…
                                    </p>
                                </transition>
                            </div>
                        </div>
                        <!-- 视频链接生成结果 -->
                        <div class="result" v-if="uploadComplete">
                            <div class="result_count">
                                <div class="result_head">
                                    <span>* </span>
                                    <span>生成结果：</span>
                                </div>
                                <div class="checkmark">
                                    <div class="checkmark-bold"></div>
                                    <div class="text">
                                        <span>文案提取成功</span>
                                    </div>
                                </div>
                            </div>
                            <div class="TextOnOffToggle">
                                <span>平台不做存储,请下载完成后在离开界面</span>
                            </div>

                            <div class="broadcast">
                                <div class="vidio_count">
                                    <video width="600" height="400" controls class="video-player">
                                        <source :src="videoSrc" type="video/mp4" />
                                    </video>
                                </div>
                                <div class="container">
                                    <!-- 可滚动内容区域 -->
                                    <div class="scroll-content">
                                        <div class="text-content">
                                            <p>
                                                {{ extractedText }}
                                            </p>
                                        </div>
                                    </div>

                                    <!-- 底部固定操作栏 -->
                                    <div class="fixed-footer">
                                        <div class="footer-wrapper">
                                            <span class="word-count">共提取<span class="number">{{ extractedText.length
                                                    }}</span>个字</span>
                                            <div class="action-buttons">
                                                <el-button class="btn" @click="SaveClick">保存到我的空间</el-button>
                                                <el-button :disabled="isCopying ||
                                                    !extractedText.length
                                                    " @click="handleCopy" class="btn copy">{{
                                                        isCopying
                                                            ? "复制中..."
                                                            : "复制"
                                                    }}</el-button>

                                                <el-button @click="handleDownload" class="btn download">下载</el-button>
                                                <el-button class="voice" @click="dub">一键配音</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频文件 -->
                    <div class="video_toolkit" v-if="typeIndex === 1">
                        <div class="video_toolkit_one">
                            <div class="video_hade">
                                <span>* </span>
                                <span>{{ videoName }}</span>
                            </div>
                            <div class="child">
                                <AudioUpload ref="vedioUploadRef" @upload-success="handleSuccess"
                                    @upload-error="handleError" type="extraction" :id="id" :externalFile="uploadedFile" 
                                    :skip-space-check="true" />

                                <!-- 提取文件 -->
                                <el-button class="btn" @click="videoClick('文件')"
                                    :disabled="videoToolkitisParsing || !uploadedFile || isUploading"
                                    :loading="videoToolkitisParsing">
                                    {{ videoToolkitisParsing ? "提取中..." : "提取文件" }}
                                </el-button>
                                <transition>
                                    <p v-show="isParsing" class="parsing-tip">
                                        <span class="loading-dots"></span>
                                        文案提取中，请稍等…
                                    </p>
                                </transition>
                            </div>
                        </div>
                        <!-- 视频文件生成结果 -->
                        <div class="result" v-if="uploadComplete">
                            <div class="result_count">
                                <div class="result_head">
                                    <span>* </span>
                                    <span>生成结果：</span>
                                </div>
                                <div class="checkmark">
                                    <div class="checkmark-bold"></div>
                                    <div class="text">
                                        <span>文案提取成功</span>
                                    </div>
                                </div>
                            </div>
                            <div class="TextOnOffToggle">
                                <span>平台不做存储,请下载完成后在离开界面</span>
                            </div>

                            <div class="broadcast">
                                <div class="vidio_count">
                                    <video width="600" height="400" controls class="video-player">
                                        <source :src="videoSrc" type="video/mp4" />
                                    </video>
                                </div>
                                <div class="container">
                                    <!-- 可滚动内容区域 -->
                                    <div class="scroll-content">
                                        <div class="text-content">
                                            <p>
                                                {{ extractedText }}
                                            </p>
                                        </div>
                                    </div>

                                    <!-- 底部固定操作栏 -->
                                    <div class="fixed-footer">
                                        <div class="footer-wrapper">
                                            <span class="word-count">共提取<span class="number">{{ extractedText.length
                                            }}</span>个字</span>
                                            <div class="action-buttons">
                                                <el-button class="btn" @click="SaveClick">保存到我的空间</el-button>
                                                <el-button :disabled="isCopying ||
                                                    !extractedText.length
                                                    " @click="handleCopy" class="btn copy">{{
                                                        isCopying
                                                            ? "复制中..."
                                                            : "复制"
                                                    }}</el-button>

                                                <el-button @click="handleDownload" class="btn download">下载</el-button>
                                                <el-button class="voice" @click="dub">一键配音</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="audio">
                    <div class="audio_one">
                        <div class="audio_hade">
                            <span>* </span>
                            <span>音频文件：</span>
                        </div>
                        <div class="child">
                            <AudioUpload accept-type="audio" accept-types="audio/mpeg,audio/wav" upperText="上传音频"
                                lowerTextAfter="MP3/wav格式的文件" @upload-success="handleSuccess"
                                @upload-error="handleError" type="extraction" :id="id" 
                                :skip-space-check="true" />
                            <el-button class="btn" @click="videoClick('音频')"
                                :disabled="isParsing || !uploadedFile || isUploading" :loading="isParsing">
                                {{ isParsing ? "提取中..." : "提取文件" }}
                            </el-button>
                            <transition>
                                <p v-show="isParsing" class="parsing-tip">
                                    <span class="loading-dots"></span>
                                    文案提取中，请稍等…
                                </p>
                            </transition>
                        </div>
                    </div>
                    <!-- 生成结果 -->
                    <div class="result" v-if="uploadComplete">
                        <div class="result_count">
                            <div class="result_head">
                                <span>* </span>
                                <span>生成结果：</span>
                            </div>
                            <div class="checkmark">
                                <div class="checkmark-bold"></div>
                                <div class="text">
                                    <span>文案提取成功</span>
                                </div>
                            </div>
                        </div>
                        <div class="TextOnOffToggle">
                            <span>平台不做存储,请下载完成后在离开界面</span>
                        </div>

                        <div class="broadcast">
                            <div class="container">
                                <!-- 可滚动内容区域 -->
                                <div class="scroll-content">
                                    <div class="text-content">
                                        <p>
                                            {{ extractedText }}
                                        </p>
                                    </div>
                                </div>

                                <!-- 底部固定操作栏 -->
                                <div class="fixed-footer">
                                    <div class="footer-wrapper">
                                        <span class="word-count">共提取<span class="number">{{ extractedText.length
                                        }}</span>个字</span>
                                        <div class="action-buttons">
                                            <el-button class="btn" @click="SaveClick">保存到我的空间</el-button>
                                            <el-button :disabled="isCopying ||
                                                !extractedText.length
                                                " @click="handleCopy" class="btn copy">{{
                                                    isCopying
                                                        ? "复制中..."
                                                        : "复制"
                                                }}</el-button>

                                            <el-button @click="handleDownload" class="btn download">下载</el-button>
                                            <el-button class="voice" @click="dub">一键配音</el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <div v-show="isShow && !activeIndex" class="room">
                <AudioUpload ref="childRef" :externalFile="uploadedFile" @upload-success="handleSuccess"
                    @upload-error="handleError" :before-trigger="showConfirmDialog" type="extraction" :id="id" 
                    :skip-space-check="true" />
                <!-- 提取文件 - 添加!uploadedFile条件使按钮在没有文件时禁用 -->
                <el-button class="room_btn" @click="videoClick('空间')" :disabled="isParsing || !uploadedFile" :loading="isParsing">
                    {{ isParsing ? "提取中..." : "提取文件" }}
                </el-button>
                <transition>
                    <p v-show="isParsing" class="parsing-tip">
                        <span class="loading-dots"></span>
                        文案提取中，请稍等…
                    </p>
                </transition>
                <!-- 生成结果 -->
                <div class="result" v-if="uploadComplete">
                    <div class="result_count">
                        <div class="result_head">
                            <span>* </span>
                            <span>生成结果：</span>
                        </div>
                        <div class="checkmark">
                            <div class="checkmark-bold"></div>
                            <div class="text">
                                <span>文案提取成功</span>
                            </div>
                        </div>
                    </div>
                    <div class="TextOnOffToggle">
                        <span>平台不做存储,请下载完成后在离开界面</span>
                    </div>

                    <div class="broadcast">
                        <div class="vidio_count">
                            <video width="600" height="400" controls class="video-player">
                                <source :src="videoSrc" type="video/mp4" />
                            </video>
                        </div>
                        <div class="container">
                            <!-- 可滚动内容区域 -->
                            <div class="scroll-content">
                                <div class="text-content">
                                    <p>
                                        {{ extractedText }}
                                    </p>
                                </div>
                            </div>

                            <!-- 底部固定操作栏 -->
                            <div class="fixed-footer">
                                <div class="footer-wrapper">
                                    <span class="word-count">共提取<span class="number">{{ extractedText.length
                                    }}</span>个字</span>
                                    <div class="action-buttons">
                                        <el-button class="btn" @click="SaveClick">保存到我的空间</el-button>
                                        <el-button :disabled="isCopying ||
                                            !extractedText.length
                                            " @click="handleCopy" class="btn copy">{{
                                                isCopying ? "复制中..." : "复制"
                                            }}</el-button>

                                        <el-button @click="handleDownload" class="btn download">下载</el-button>
                                        <el-button class="voice" @click="dub">一键配音</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部展示 -->
        <FooterChild v-if="uploadComplete" :show-recommend="true" :show-watermark-button="true" :show-extraction-button="false"
            :video-url="videoInfo.videoUrl" />

        <!-- 我的空间 -->
        <AlbumDialog ref="albumDialogRef" v-model="showDialog" @cancel="handleCancel" @confirm="handleVideoSelect" />

        <!-- dialog弹窗 -->
        <SubtitleDialog v-model="modelValue" :material-info="materialInfo" :sourcePage="'ai-tool'" @confirm="handleSave" @save-success="handleSaveSuccess" @save-error="handleSaveError" />

        <!-- 添加会员限制弹窗 -->
        <AlertDialog v-model:visible="showLimitDialog" type="warning" title="会员功能" message="非会员每日只能使用15次，请开通会员使用"
            confirm-button-text="开通会员" cancel-button-text="我知道了" :show-cancel-button="true" :custom-confirm-class="true"
            :custom-cancel-class="true" :show-fee-explanation="false" @confirm="handleOpenMember"
            @cancel="handleCloseLimitDialog" />
    </div>
</template>

<!-- 在 Tabs.vue 中添加颜色切换逻辑 -->
<script setup>
import { ref, computed, getCurrentInstance, onMounted } from "vue";
import CommonInput from "../../../components/CommonInput/index.vue";
import AudioUpload from "../../../components/AudioUpload/index.vue";
import AlbumDialog from "../../../components/AlbumDialog/index.vue";
import FooterChild from "../../../components/FooterChild/index.vue";
import SubtitleDialog from "../../../components/SubtitleDialog/index.vue";
import AlertDialog from "@/views/components/AlertDialog.vue";
import { ElMessage } from "element-plus";
import { extractFile, crawlTextByVideoPage } from "@/api/dubbing";
import { useRouter, useRoute } from 'vue-router'; // 引入路由和路由实例
import { useAIDubbingStore } from '@/stores/modules/AIDubbing.js'
import { saveFullMaterial } from "@/api/myMaterial";
import { useUmeng } from '@/utils/umeng/hook'

// 使用try-catch初始化Pinia store，防止错误导致组件崩溃
let previewStore = null;
try {
    import('@/stores/previewStore').then(module => {
        const { usePreviewStore } = module;
        previewStore = usePreviewStore();
        console.log('previewStore成功初始化');
    }).catch(error => {
        console.error('动态导入previewStore失败:', error);
    });
} catch (error) {
    console.error('初始化previewStore失败:', error);
}

let useAIDubbing = useAIDubbingStore()
// 添加获取实例
const { proxy } = getCurrentInstance();
// 获取路由实例
const router = useRouter();
const route = useRoute();
const umeng = useUmeng();

// 添加引用到AlbumDialog组件
const albumDialogRef = ref(null);

// 工具函数：多次decode确保OSS链接可用
function decodeOSSUrl(url) {
    try {
        url = decodeURIComponent(url);
        if (url.includes('%')) url = decodeURIComponent(url);
    } catch {}
    return url;
}

// 添加onMounted初始化
onMounted(() => {
    // 初始化视频名称和卡片索引
    videoName.value = "视频文件："; // 默认为视频文件
    cardIndex.value = 1; // 默认选中第二张卡片（视频文件）
    typeIndex.value = 1; // 默认选中第二个类型（视频文件）
    
    // 检查路由参数中是否有视频URL
    let urlParam = route.query.url;
    if (urlParam) {
        urlParam = decodeOSSUrl(urlParam);
        // 只设置上传文件相关值，不设置视频链接
        uploadedFile.value = {
            url: urlParam,
            name: "从其他页面传入的视频"
        };
        // 设置视频源
        videoSrc.value = urlParam;
        // 提示消息
        ElMessage.success("已自动填充视频，可以直接点击提取文案");
    }
});

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};
let dub = () => {
    // 检查是否有文字内容
    if (!extractedText.value || (typeof extractedText.value === 'string' && !extractedText.value.trim())) {
        // 如果没有文字内容，显示提示
        ElMessage.warning('请输入文字');
        return;
    }

    // 有文字内容，继续执行原有逻辑
    console.log(extractedText.value, useAIDubbing, 777);

    useAIDubbing.setExtraction(extractedText.value)
    router.push({ path: '/AIDubbing', query: { extraction: true } });
}
// 判断用户是否已登录
const checkUserLogin = () => {
    // 从本地存储获取user信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;

    try {
        const userData = JSON.parse(userStorage);
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== '';
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
};

const albums = ref([]);
const tabs = ref(["视频文案", "音频文案"]);
const activeIndex = ref(0);
const cardIndex = ref(1);
const typeIndex = ref(1);
const placeholder = ref("请输入视频链接");
const showDialog = ref(false);
const isParsing = ref(false);
const showResult = ref(false);
// const videoToolkitGenerates = ref(false);
const isShow = ref(false);
const videoUrl = ref("");
const childRef = ref(null); // 子组件引用
const modelValue = ref(false);
const vedioUploadRef = ref(null); // 音频上传组件引用
const isCopying = ref(false);
const uploadedFile = ref(null); // 添加存储上传文件信息的响应式变量
const uploadComplete = ref(false); // 控制生成结果显示
const isUploading = ref(false); // 添加上传状态控制
const videoSrc = ref('');
const acceptType = ref('video');
const id = ref(getUserId()); // 使用动态获取的userId

const extractedText = ref(
    Array(50).fill("文字内容文字内容文字内容文字内容") // 初始示例数据
);

// 添加默认选定文件夹
const defaultSelected = ref([]);

// 添加一个计算属性用于处理videoInfo
const videoInfo = computed(() => {
    return {
        video: videoSrc.value || '',
        videoUrl: videoSrc.value || '',
        title: (uploadedFile.value?.name || `提取文案_${new Date().toISOString().slice(0, 10)}`),
        thumbnail: (uploadedFile.value?.thumbnailPath || ''),
        coverUrl: (uploadedFile.value?.thumbnailPath || ''),
        text: (extractedText.value || '')
    }
});

// 添加一个计算属性用于素材类型
const materialType = computed(() => {
    // 确保activeIndex有值
    return activeIndex.value === 0 ? 'video' : 'audio';
});

// 添加一个计算属性来判断是否应该禁用标签切换
const isDisabled = computed(() => {
    return isParsing.value || videoLinkisParsing.value || videoToolkitisParsing.value;
});

// 添加处理保存的函数
const handleSave = async (params) => {
    const required = ['userId', 'materialName', 'materialType', 'tagId', 'textContent'];
    for (const key of required) {
        if (!params[key]) {
            ElMessage.error(`${key}为必填项`);
            return;
        }
    }
    try {
        await saveFullMaterial(params);
        ElMessage.success("保存成功");
        modelValue.value = false;
    } catch (error) {
        ElMessage.error("保存失败");
        console.error("保存失败:", error);
    }
};

// 复制方法
const handleCopy = async () => {
    if (isCopying.value) return;
    isCopying.value = true;
    try {
        // 拼接所有段落文本
        // const textToCopy = extractedText.value.join("\n");

        // 空内容检查
        if (!extractedText.value.trim()) {
            ElMessage.warning("没有可复制的内容");
            return;
        }

        // 现代浏览器复制方案
        if (navigator.clipboard) {
            await navigator.clipboard.writeText(extractedText.value);
        } else {
            // 兼容旧版浏览器的复制方案
            const textarea = document.createElement("textarea");
            textarea.value = extractedText.value;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand("copy");
            document.body.removeChild(textarea);
        }

        ElMessage.success("文案已复制到剪贴板");
    } catch (err) {
        console.error("复制操作失败:", err);
        ElMessage.error("复制失败，请手动选择文本");
    } finally {
        setTimeout(() => {
            isCopying.value = false;
        }, 1000);
    }
};

// 视频格式动态展示
const videoName = ref("视频文件：");

// 清空
const handleClear = () => {
    videoUrl.value = "";
    isParsing.value = false;
    showResult.value = false;
};

// 上传格式
const cardList = ref([
    {
        name: "视频链接",
        type: 1,
    },
    {
        name: "视频文件",
        type: 2,
    },
    {
        name: "我的空间",
        type: 3,
    },
]);

// tabs方法
const tabsClick = (index) => {
    // 检查是否有正在进行的提取任务，如果有则禁止切换
    if (isParsing.value || videoLinkisParsing.value || videoToolkitisParsing.value) {
        ElMessage.warning('文案提取中，请等待处理完成');
        return;
    }
    
    activeIndex.value = index;
    // 清空相关状态
    uploadedFile.value = null;
    videoSrc.value = '';
    extractedText.value = '';
    uploadComplete.value = false;
    isParsing.value = false;
    videoToolkitisParsing.value = false;
    videoLinkisParsing.value = false;
};

// 上传格式
const cardClick = (index, type) => {
    // 检查是否有正在进行的提取任务，如果有则禁止切换
    if (isParsing.value || videoLinkisParsing.value || videoToolkitisParsing.value) {
        ElMessage.warning('文案提取中，请等待处理完成');
        return;
    }
    
    cardIndex.value = index;
    typeIndex.value = index;

    if (type == 1) {
        // 清空上传的文件
        uploadedFile.value = null;
        videoName.value = "视频链接：";
        isShow.value = false;
        uploadComplete.value = false;
    }
    if (type == 2) {
        // 清空上传的文件
        uploadedFile.value = null;
        videoName.value = "视频文件：";
        isShow.value = false;
        childRef.value && childRef.value.currentFile && (childRef.value.currentFile.value = false);
        uploadComplete.value = false;
    }
    if (type == 3) {
        console.log(childRef.value, 111);
        
        // 清空上传的文件
        uploadedFile.value = null;
        
        // 检查用户登录状态
        if (!checkUserLogin()) {
            // 用户未登录，弹出登录弹窗
            proxy.$modal.open('组合式标题');
            return;
        }
        
        // 在显示弹窗前先预加载数据
        // 注意：已移除AlbumDialog组件中watch监听器里的loadAlbums调用
        // 现在采用单一数据加载点，只在这里调用loadAlbums
        try {
            console.log('预加载我的空间数据');
            if (albumDialogRef.value) {
                albumDialogRef.value.loadAlbums();
                console.log('预加载请求已发送');
            } else {
                console.warn('AlbumDialog组件尚未挂载，无法预加载');
                // 如果组件尚未挂载，稍后再显示弹窗
                setTimeout(() => {
                    if (albumDialogRef.value) {
                        albumDialogRef.value.loadAlbums();
                        console.log('延迟预加载请求已发送');
                    }
                }, 100);
            }
        } catch (error) {
            console.error('预加载数据失败:', error);
        }
        
        // 显示弹窗
        uploadComplete.value = false;
        showDialog.value = true;
    }
};

// 提取文件

// 视频链接控制
const videoLinkisParsing = ref(false);
// 视频文件控制
const videoToolkitisParsing = ref(false);
const videoClick = async (type) => {
    // 添加埋点代码
    umeng.trackEvent(
      '文案提取', 
      '点击提取文案', 
      `${type}提取`, 
      ''
    )
    
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }

            // 针对不同类型的特殊处理
        if (type === '链接') {
            // 视频链接类型特殊检查
            if (!videoUrl.value || !videoUrl.value.trim()) {
                ElMessage({
                    message: '请输入视频链接',
                    type: 'warning',
                });
                return;
            }

            // 确保设置了uploadedFile
            if (!uploadedFile.value || !uploadedFile.value.url) {
                // 自动设置uploadedFile
                uploadedFile.value = {
                    name: `视频链接_${new Date().toISOString().slice(0, 10)}`,
                    url: videoUrl.value
                };
            }

        videoLinkisParsing.value = true;
        uploadComplete.value = false; // 开始处理时重置状态

        try {
            isParsing.value = true;

            // 调用视频链接专用API
            const response = await crawlTextByVideoPage({
                url: videoUrl.value,
                userId: getUserId(),
            });

            // 检查会员限制状态码 - 已注释，跳过会员限制检查
            // if (response && response.status_code === 308) {
            //     showLimitDialog.value = true;
            //     isParsing.value = false;
            //     videoLinkisParsing.value = false;
            //     return;
            // }

            if (response && response.status_code === 200) {
                ElMessage.success('文件处理成功');
                extractedText.value = response.content.result.txt;
                uploadComplete.value = true; // 处理成功后显示结果

                // 设置视频源
                videoSrc.value = response.content.result.video;
            } else {
                throw new Error(response.message || '处理失败');
            }
        } catch (error) {
            console.error('处理失败:', error);

            // 检查错误响应中是否有会员限制状态码 - 已注释，跳过会员限制检查
            // if (error.response && error.response.data &&
            //     error.response.data.status_code === 308) {
            //     showLimitDialog.value = true;
            // } else {
                ElMessage.error(`处理失败: ${error.message || '未知错误'}`);
            // }

            uploadComplete.value = false; // 处理失败时不显示结果
        } finally {
            isParsing.value = false;
            videoLinkisParsing.value = false;
        }

        return; // 提前返回，避免执行后续的通用代码
    } else {
        // 对于非链接类型，保持原有检查逻辑
        // 检查是否有上传文件
        if (!uploadedFile.value) {
            ElMessage.warning('请上传视频文件');
            return;
        }

        if (!uploadedFile.value?.url) {
            ElMessage.warning('请上传视频文件');
            return;
        }

        if (type === '文件') {
            videoToolkitisParsing.value = true;
        } else {
            videoLinkisParsing.value = true;
        }
    }

    uploadComplete.value = false; // 开始处理时重置状态

    try {
        isParsing.value = true;

        const response = await extractFile({
            url: uploadedFile.value.url,
            userId: getUserId(),
        });

        // 检查会员限制状态码 - 已注释，跳过会员限制检查
        // if (response && response.status_code === 308) {
        //     showLimitDialog.value = true;
        //     isParsing.value = false;
        //     videoToolkitisParsing.value = false;
        //     videoLinkisParsing.value = false;
        //     return;
        // }

        if (response && response.status_code === 200) {
            ElMessage.success('文件处理成功');
            extractedText.value = response.content.result.txt;
            uploadComplete.value = true; // 处理成功后显示结果
            console.log(response.content.result.video, 111);

            // 设置视频源
            videoSrc.value = response.content.result.video;
        } else {
            throw new Error(response.message || '处理失败');
        }

    } catch (error) {
        console.error('处理失败:', error);

        // 检查错误响应中是否有会员限制状态码 - 已注释，跳过会员限制检查
        // if (error.response && error.response.data &&
        //     error.response.data.status_code === 308) {
        //     showLimitDialog.value = true;
        // } else {
            ElMessage.error(`处理失败: ${error.message || '未知错误'}`);
        // }

        uploadComplete.value = false; // 处理失败时不显示结果
    } finally {
        isParsing.value = false;
        videoToolkitisParsing.value = false;
        videoLinkisParsing.value = false;
    }
};

// 修改处理上传成功的方法
const handleSuccess = (fileInfo) => {
    uploadedFile.value = fileInfo;
    isUploading.value = false;
    // 只在视频标签页时设置视频源
    if (!activeIndex.value) {
        videoSrc.value = fileInfo.url;
    }
    ElMessage.success('文件上传成功');
};

// 修改处理上传错误的方法
const handleError = (error) => {
    isUploading.value = false; // 上传失败也要重置状态
    uploadedFile.value = null;
    ElMessage.error('文件上传失败');
};

// 音频文件提取方法
const audioClick = () => { };

// 确定按钮操作
const handleVideoSelect = (selectedVideo) => {
    console.log('选中的视频/音频:', selectedVideo);
    
    if (!selectedVideo) {
        ElMessage.warning('未选择任何视频/音频');
        return;
    }
    
    try {
        if (selectedVideo && selectedVideo.url) {
            // 更新上传组件的显示
            uploadedFile.value = {
                name: selectedVideo.name || '未命名视频',
                url: selectedVideo.url,
                thumbnailPath: selectedVideo.thumbnailPath || '',
                materialType: selectedVideo.materialType || 'video'
            };
            
            // 如果是视频类型，设置视频源
            if (selectedVideo.materialType === 'video') {
                videoSrc.value = selectedVideo.url;
            }
            
            // 关闭选择对话框
            isShow.value = true;
            ElMessage.success('已选择: ' + selectedVideo.name);
        } else {
            throw new Error('选中的视频/音频缺少URL');
        }
    } catch (error) {
        console.error('处理选择失败:', error);
        ElMessage.error('选择处理失败: ' + error.message);
    }
};

// 取消按钮操作
const handleCancel = () => {
    try {
        console.log('取消选择');
        isShow.value = true;
        
        // 如果childRef存在且有currentFile属性，则清空它
        if (childRef.value && childRef.value.currentFile) {
            childRef.value.currentFile = null;
        } else {
            console.log('childRef不可用或没有currentFile属性');
        }
    } catch (error) {
        console.error('取消操作出错:', error);
    }
};

// 在我的空间中，组件需要阻断默认的上传，需要打开dialog上传
const showConfirmDialog = () => {
    showDialog.value = true;
};

// 更新SaveClick方法
const SaveClick = async () => {
    // 检查是否有提取出的文本
    if (!extractedText.value || !extractedText.value.trim()) {
        ElMessage.warning('请先提取文案');
        return;
    }
    const textContent = extractedText.value.trim();
    materialInfo.value = {
        userId: getUserId(),
        materialName: textContent.slice(0, 10),
        materialType: 'text',
        textContent,
        duration: textContent.length, // 字符数
        // tagId由子组件回传
    };
    modelValue.value = true;
};

// 下载字幕方法
const handleDownload = () => {
    // 拼接所有文本内容
    const textContent = extractedText.value;

    if (!textContent.trim()) {
        ElMessage.warning("没有可下载的内容");
        return;
    }

    try {
        // 创建Blob对象
        const blob = new Blob([textContent], { type: "text/plain" });

        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;

        // 生成更友好的文件名
        let fileName = "";

        // 1. 尝试使用上传文件的原始名称
        if (uploadedFile.value && uploadedFile.value.name) {
            // 提取文件名（不含扩展名）
            const originalName = uploadedFile.value.name.replace(/\.[^/.]+$/, "");
            fileName = `${originalName}_文案提取`;
        }
        // 2. 尝试从提取的文本中获取名称（使用前20个字符作为文件名）
        else if (textContent && textContent.trim()) {
            // 获取文本前20个字符
            let textPreview = textContent.trim().substring(0, 20);
            // 移除换行符等特殊字符
            textPreview = textPreview.replace(/[\r\n\t]/g, " ").trim();
            fileName = `文案_${textPreview}`;
        }
        // 3. 最后的回退选项
        else {
            const date = new Date().toLocaleDateString().replace(/[\/\\]/g, "-");
            fileName = `文案提取_${date}`;
        }

        // 清理文件名中的非法字符
        fileName = fileName.replace(/[\\/:*?"<>|]/g, "_");

        // 设置下载文件名
        link.download = `${fileName}.txt`;

        console.log("设置下载文件名:", link.download);

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理资源
        URL.revokeObjectURL(url);
        document.body.removeChild(link);

        ElMessage.success("文件下载成功");
    } catch (error) {
        console.error("下载失败:", error);
        ElMessage.error("文件下载失败");
    }
};

// 可以添加一个计算属性来控制视频源
const currentVideoSrc = computed(() => {
    if (!activeIndex.value && uploadedFile.value) {
        return uploadedFile.value.url;
    }
    return '';
});

const showLimitDialog = ref(false);
const handleOpenMember = () => {
    // 关闭弹窗
    showLimitDialog.value = false;

    // 导航到会员页面
    try {
        // 判断是否在layout布局内
        if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
            // 如果在layout布局内，使用内部路由导航
            router.push({ name: 'membership-nav' });
        } else {
            // 否则通过URL跳转
            window.location.href = '/membership';
        }
        ElMessage.success("正在跳转到会员页面");
    } catch (error) {
        console.error("导航到会员页面失败:", error);
        ElMessage.error("导航到会员页面失败，请手动前往会员中心");
    }
};

const handleCloseLimitDialog = () => {
    // 关闭会员限制弹窗
    showLimitDialog.value = false;
};

// 检查会员权限状态码 - 仅检查308状态码
const checkMemberLimit = (statusCode) => {
    return statusCode === 308;
};

// 更新handleParse方法
const handleParse = () => {
    // 检查URL是否为空
    if (!videoUrl.value || !videoUrl.value.trim()) {
        ElMessage({
            message: '请输入视频链接',
            type: 'warning',
        });
        return;
    }

    // 检查URL格式是否大致合理
    if (!videoUrl.value.startsWith('http')) {
        ElMessage({
            message: '请输入有效的视频链接',
            type: 'warning',
        });
        return;
    }

    // 设置uploadedFile为链接
    uploadedFile.value = {
        name: `视频链接_${new Date().toISOString().slice(0, 10)}`,
        url: videoUrl.value
    };

    // 设置视频源为当前链接
    videoSrc.value = videoUrl.value;

    ElMessage.success('链接已解析，可以点击提取文案');
};

const materialInfo = ref({});
const handleSaveSuccess = () => {
    ElMessage.success("保存成功");
};
const handleSaveError = (error) => {
    ElMessage.error("保存失败");
    console.error("保存失败:", error);
};
</script>

<style lang="scss" scoped>
.extraction {
    .cont {
        margin-left: 20px;
        margin-bottom: 100px;
    }

    .extraction_head {
        display: flex;
        align-items: center;

        .tabs {
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;

            span {
                margin-left: 20px;
                transition: all 0.3s ease;
                
                /* 添加悬停效果 */
                &:hover {
                    color: #18ad25;
                }
            }

            .tabsActive {
                color: #18ad25;
            }
            
            /* 添加禁用样式 */
            &.disabled {
                opacity: 0.6;
                
                span {
                    cursor: not-allowed;
                    
                    &:hover {
                        color: inherit;  /* 禁用悬停色彩变化 */
                    }
                }
            }
        }

        .vertical-line {
            width: 3px; // 宽度调节点（默认3px→5px）
            height: 25px; // 高度调节点（默认50px→撑满全屏）
            background: linear-gradient(to bottom,
                    #000 100%,
                    // 顶部实色
                    transparent // 底部渐隐（可选效果）
                );
            margin-left: 20px; // 水平居中
        }
    }

    .sharing {
        margin-top: 10px;
        display: flex;
        align-items: center;

        .sharing_caption {}

        .card {
            display: flex;
            align-items: center;
            cursor: pointer;

            .cardStyleOne {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 106px;
                height: 40px;
                text-align: center;
                line-height: 40px;
                margin-left: 14px;
                border-radius: 4px;
                color: #0AAF60;
                background-color: #FFFFFF;
                border: 1px solid #0AAF60;

                .text {
                    margin-left: 5px;
                }

                .container {
                    width: 20px;
                    height: 20px;
                    background: #4caf50;
                    /* 绿色背景 */
                    border-radius: 50%;
                    /* 圆形 */
                    position: relative;
                }

                .inner-oval {
                    width: 10px;
                    height: 10px;
                    background: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    /* 椭圆效果 */
                }

                &:nth-child(2),
                &:nth-child(3) {
                    margin-left: 20px;
                }
            }

            .cardStyleTwo {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 106px;
                height: 40px;
                border-radius: 4px;
                text-align: center;
                line-height: 40px;
                margin-left: 14px;
                border: 1px solid #D9DCE1;
                background-color: transparent;

                .text {
                    margin-left: 5px;
                }

                .container {
                    width: 20px;
                    height: 20px;
                    background: white;
                    /* 绿色背景 */
                    border-radius: 50%;
                    /* 圆形 */
                    position: relative;
                }

                .inner-oval {
                    width: 20px;
                    height: 20px;
                    border: 1px solid #eeeef0;
                    background: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    /* 椭圆效果 */
                }

                &:nth-child(2),
                &:nth-child(3) {
                    margin-left: 20px;
                }
            }
            
            .cardDisabled {
                opacity: 0.6;
                cursor: not-allowed;
                
                &:hover {
                    border-color: #eeeef0 !important;
                }
                
                .container, .inner-oval {
                    opacity: 0.6;
                }
            }
        }
    }

    .Video {
        display: flex;
        margin-top: 30px;

        // 视频链接样式
        .video_link,
        .video_toolkit {

            .video_link_one,
            .video_toolkit_one {
                display: flex;
            }

            .child {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                // width: 47%;
                margin-left: 10px;

                :deep(.el-input__wrapper) {
                    border: none !important;
                    box-shadow: none !important;

                    // 去除hover状态边框
                    &:hover {
                        box-shadow: none !important;
                    }

                    // 去除聚焦状态边框
                    &.is-focus {
                        box-shadow: none !important;
                    }
                }

                :deep(.image-container) {
                    width: 640px;
                    height: 160px;
                    border: 1px solid #D9DCE1;
                    border-radius: 4px;
                    background-color: #ffffff;
                }

                :deep(.upload-card) {
                    width: 640px !important;
                    height: 160px !important;
                    background-color: #FFFFFF !important;
                }

                :deep(.video-uploader) {
                    width: 640px;
                    height: 160px;
                }

                :deep(.el-upload) {
                    width: 640px;
                    height: 160px;
                }

                .parent-wrapper {
                    width: 800px;
                }

                .btn {
                    width: 112px;
                    height: 40px;
                    cursor: pointer;
                    margin-top: 20px;
                    color: #fff;
                    text-align: center;
                    border-radius: 4px;
                    line-height: 30px;
                    background-color: #0AAF60;

                    &:hover {
                        border: 1px solid #0AAF60;
                    }

                    // 添加禁用状态样式
                    &:disabled {
                        background-color: #0AAF60;
                        border-color: #0AAF60;
                        color: #fff;
                        cursor: not-allowed;
                    }
                }

                .parsing-tip {
                    margin-top: 10px;
                    color: #909399;
                    font-size: 14px;
                }
            }
        }

        .video_toolkit {
            .video_toolkit_one {
                .child {
                    :deep(.btn) {
                        width: 112px;
                        height: 40px;
                        cursor: pointer;
                        margin-top: 20px;
                        color: #fff;
                        text-align: center;
                        border-radius: 4px;
                        line-height: 30px;
                        background-color: #0AAF60;

                        &:hover {
                            border: 1px solid #0AAF60;
                        }

                        // 增加选择器优先级并使用 !important
                        &.is-disabled,
                        &:disabled {
                            background-color: #0AAF60 !important;
                            border-color: #0AAF60 !important;
                            color: #fff !important;
                            cursor: not-allowed !important;
                        }
                    }
                }
            }
        }
    }

    // 结果的样式
    .result {
        margin-top: 60px;

        .result_count {
            display: flex;
            align-items: center;

            .checkmark {
                display: flex;
                align-items: center;

                .checkmark-bold {
                    background: #0AAF60;
                    width: 23px;
                    height: 23px;
                    border-radius: 50%;
                    position: relative;
                    // left: 1px;
                }

                .checkmark-bold::after {
                    content: "";
                    position: absolute;
                    left: 22%;
                    /* 调整定位补偿线宽变化 */
                    top: 25%;
                    width: 14px;
                    height: 9px;
                    border: 4px solid #fff;
                    /* 线宽从4px增加到6px */
                    border-top: none;
                    border-right: none;
                    transform: rotate(-45deg);
                    box-sizing: border-box;
                    /* 保持尺寸稳定 */
                }

                .text {
                    margin: 0 0 0 6px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #0AAF60;
                }
            }
        }

        .TextOnOffToggle {
            color: #0AAF60;
            font-size: 12px;
            margin: 8px 0 0 109px;
        }

        .broadcast {
            display: flex;
            align-items: center;
            width: 1200px;
            margin: 20px 0 0 80px;

            // border: 1px solid red;
            .vidio_count {
                display: flex;
                align-items: center;
                background: #000; // 或者其他你喜欢的颜色

                .vidio {
                    .video-player {
                        min-height: 400px;
                        background: #000; // 或者其他你喜欢的颜色
                        object-fit: contain; // 保持视频比例
                    }
                }
            }

            .container {
                margin-left: 40px;
                height: 400px;
                display: flex;
                flex-direction: column;
                background: #fff;
                border: 1px solid #DCDCDC;

                .scroll-content {
                    flex: 1;
                    overflow: auto;
                    padding: 20px;

                    .text-content {
                        p {
                            line-height: 1.8;
                            color: #666;
                            margin: 0 0 1em;
                        }
                    }
                }

                .fixed-footer {
                    background: #fff;
                    padding: 16px 0;
                    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
                    position: sticky;
                    bottom: 0;

                    .footer-wrapper {
                        max-width: 1200px;
                        // margin: 0 auto;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0 20px;
                    }

                    .word-count {
                        color: #666;
                        font-size: 12px;
                        padding-right: 10px;

                        .number {
                            color: #0AAF60;
                        }
                    }

                    .action-buttons {
                        display: flex;
                        align-items: center;

                        .btn {
                            width: 121px;
                            height: 37px;
                            border-color: #0AAF60;
                            background-color: #0AAF60;
                            color: white;

                            &:hover {
                                color: #000;
                                background-color: #f0faf1;
                                border-color: #0AAF60;
                                transform: translateY(-2px);
                            }
                        }

                        .copy,
                        .download {
                            width: 89px;
                            height: 37px;
                        }

                        .voice {
                            width: 89px;
                            height: 37px;
                            background: #0AAF60;
                            border-color: #0AAF60;
                            color: white;

                            &:hover {
                                transform: translateY(-2px);
                            }
                        }
                    }
                }

                ::-webkit-scrollbar {
                    width: 6px;
                    background: #f5f5f5;
                }

                ::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                }
            }
        }
    }

    // 音频文件
    .audio {
        display: flex;
        flex-direction: column;

        .audio_one {
            display: flex;
        }

        .child {
            margin-left: 20px;

            .btn {
                width: 112px;
                height: 40px;
                margin-top: 20px;
                color: white;
                text-align: center;
                line-height: 30px;
                border-radius: 4px;
                background-color: #0AAF60;
            }
        }

        .result {
            margin-top: 30px;

            .result_count {
                display: flex;
                align-items: center;

                .checkmark {
                    display: flex;
                    align-items: center;

                    .checkmark-bold {
                        background: #0AAF60;
                        width: 23px;
                        height: 23px;
                        border-radius: 50%;
                        position: relative;
                        left: 20px;
                    }

                    .checkmark-bold::after {
                        content: "";
                        position: absolute;
                        left: 22%;
                        /* 调整定位补偿线宽变化 */
                        top: 25%;
                        width: 14px;
                        height: 9px;
                        border: 4px solid #fff;
                        /* 线宽从4px增加到6px */
                        border-top: none;
                        border-right: none;
                        transform: rotate(-45deg);
                        box-sizing: border-box;
                        /* 保持尺寸稳定 */
                    }

                    .text {
                        margin: 2px 0 0 25px;
                        font-size: 16px;
                        color: #0AAF60;
                    }
                }
            }

            .TextOnOffToggle {
                color: #0AAF60;
                margin: 20px 0 0 100px;
            }

            .broadcast {
                display: flex;
                align-items: center;
                width: 1200px;
                margin: 20px 0 0 59px;

                // border: 1px solid red;
                .vidio_count {
                    display: flex;
                    align-items: center;

                    .vidio {
                        .video-player {
                            min-height: 400px;
                            background: #f7f7f7; // 或者其他你喜欢的颜色
                            object-fit: contain; // 保持视频比例
                        }
                    }
                }

                .container {
                    width: 50%;
                    margin-left: 40px;
                    height: 400px;
                    display: flex;
                    flex-direction: column;
                    background: #fff;
                    border: 1px solid #666;

                    .scroll-content {
                        flex: 1;
                        overflow: auto;
                        padding: 20px;

                        .text-content {
                            p {
                                line-height: 1.8;
                                color: #666;
                                margin: 0 0 1em;
                            }
                        }
                    }

                    .fixed-footer {
                        background: #fff;
                        padding: 16px 0;
                        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
                        position: sticky;
                        bottom: 0;

                        .footer-wrapper {
                            max-width: 1200px;
                            // margin: 0 auto;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 0 20px;
                        }

                        .word-count {
                            color: #666;
                            font-size: 12px;
                            padding-right: 10px;

                            .number {
                                color: #0AAF60;
                            }
                        }

                        .action-buttons {
                            display: flex;
                            // gap: 12px;

                            .btn {
                                // width: 80px;
                                border-color: #0AAF60;
                                background-color: #0AAF60;
                                color: white;

                                &:hover {
                                    color: #000;
                                    background-color: #f0faf1;
                                    border-color: #0AAF60;
                                    transform: translateY(-2px);
                                }
                            }

                            .copy,
                            .download {
                                width: 80px;
                                // height: 40px;
                            }

                            .voice {
                                background: #0AAF60;
                                border-color: #0AAF60;
                                color: white;

                                &:hover {
                                    transform: translateY(-2px);
                                }
                            }
                        }
                    }

                    ::-webkit-scrollbar {
                        width: 6px;
                        background: #f5f5f5;
                    }

                    ::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;
                    }
                }
            }
        }
    }

    // 我的空间
    .room {
        .room_btn {
            width: 112px;
            height: 40px;
            margin-top: 20px;
            color: white;
            text-align: center;
            line-height: 30px;
            border-radius: 4px;
            background-color: #0AAF60;
        }

    }
}

// 更新呼吸效果动画样式
.parsing-tip {
    margin-top: 8px;
    color: #666666;
    font-size: 14px;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: breathing 1.5s ease-in-out infinite;

    .loading-dots {
        display: inline-block;
        position: relative;
        width: 16px;
        height: 16px;

        &::before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #0AAF60;
            animation: dots-bounce 1s infinite ease-in-out;
        }

        &::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #0AAF60;
            animation: dots-bounce 1s infinite ease-in-out;
            animation-delay: 0.5s;
            opacity: 0;
        }
    }
}

@keyframes breathing {
    0% {
        opacity: 0.4;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.4;
    }
}

@keyframes dots-bounce {

    0%,
    100% {
        transform: scale(0);
        opacity: 0.5;
    }

    50% {
        transform: scale(1);
        opacity: 1;
    }
}

// 自定义添加 Element Plus 消息样式
:deep(.el-message) {
    min-width: 180px;
    padding: 10px 15px;
    border-radius: 4px;

    &.el-message--warning {
        background-color: #fdf6ec;
        border-color: #faecd8;

        .el-message__content {
            color: #e6a23c;
            font-size: 14px;
            line-height: 1;
        }

        .el-icon {
            color: #e6a23c;
            font-size: 16px;
            margin-right: 8px;
        }
    }
}
</style>