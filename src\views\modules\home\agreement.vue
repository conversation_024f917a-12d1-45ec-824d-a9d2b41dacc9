<template>
    <div class="agreement"  ref="agreementRef">
        <Headbar />
        <div class="agreement_contaier">
        <div class="agreement_content">
            <div class="extraction_head">
                <h2>平台政策</h2>
                <div class="vertical-line">|</div>
                <div class="tabs">
                    <span @click="handleClick(item.value)" :class="{ tabsActive: activeName == item.value }"
                        v-for="(item, index) in tabs" :key="index">{{ item.label }}</span>
                </div>
                <el-button type="primary" @click="back">返回<el-icon><Right/></el-icon></el-button>
            </div>
            <userAgreement v-if="activeName=='user'"></userAgreement>
            <privacyAgreement v-else></privacyAgreement>
       
        </div>
             <el-backtop :bottom="100"  target=".agreement_contaier">
                <el-icon><Top/></el-icon>
                返回顶部
            </el-backtop>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted,watch,nextTick } from 'vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import privacyAgreement from "./privacyAgreement.vue"
import userAgreement from "./userAgreement.vue"
import { useRoute,useRouter  } from 'vue-router';
let route = useRoute();
let router = useRouter()
let activeName = ref('user')
let  agreementRef = ref(null)
let tabs = reactive([
    {
        label: '用户协议',
        value: 'user'
    }, {
        label: '隐私协议',
        value: 'privacy'
    }
])
let handleClick = (data) => {
    console.log(data);
    activeName.value = data
}
let back=()=>{
    router.back()
}
watch(route, (newRoute, oldRoute) => {
    if(route.query.type){
        activeName.value = route.query.type
    }else{
        activeName.value =tabs[0].value
    }
});
onMounted(() => {
    if(route.query.type){
        activeName.value = route.query.type
    }else{
        activeName.value =tabs[0].value
    }
    nextTick(() => {
        let body = document.body;
        body.classList.remove('el-popup-parent--hidden');
    });
    
})
</script>
<style lang="scss" scoped>
.agreement {
    display: flex;
    flex-direction: column;

    overflow-x: clip; /* 使用clip代替auto，防止下拉显示空白区域 */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    min-height: 100vh; /* 确保至少填满视口高度 */
    display: flex;
    flex-direction: column;
    .extraction_head {
        display: flex;
        align-items: center;

        h2 {
            margin-right: 20px;
        }

        .tabs {
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;

            span {
                margin-left: 20px;
            }

            .tabsActive {
                color: #18ad25;
            }
        }
        button{
            background-color: #18ad25;
            color: #fff;
            font-size: 14px;
            border: none;
            margin-left: auto;
            .el-icon{
                margin-left: 4px;
            }
        }
    }
    .agreement_contaier{
     overflow-y: scroll;
    margin-top:50px;
    .agreement_content {
        padding-top:50px;
        width: 1162px;
        margin: 0 auto;
       
    }
    }
}
.el-backtop{
    width: 2em;
    height: fit-content;
    padding:10px 20px;
    border-radius: 5px;
    box-sizing: border-box;
    background-color: #18ad25;
    color: #fff;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    .el-icon{
        margin-bottom: 3px;
    }
}
</style>
