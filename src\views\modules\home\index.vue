<template>
	<Container class="home-container">
		<!-- 添加首页头部图片 -->
		<div class="header-banner">
			<img :src="shouye1" alt="配音帮手" class="header-banner-img" />
		</div>

		<!-- 添加应用入口标题 -->
		<div class="entrance-title">音频创作</div>

		<!-- 修改为卡片式布局，保持一致的交互和路由 -->
		<!-- 顶部主要功能卡片区域 -->
		<div class="feature-section">
			<div class="feature-row">
				<!-- AI配音卡片 -->
				<div class="feature-card ai-voice-card first-row-card" @click="jump_page(1)"
					:style="{ backgroundImage: `url(${ai1})`, backgroundSize: '747px 210px', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }">
					<div class="card-content-container">
						<div class="card-left">
							<h3 class="card-title ai-voice-title">AI 配音</h3>
							<p class="card-description ai-voice-description">媲美真人的AI 配音，200 多个精品AI 音色，20 多个精细调节功能，13 个配音场景，短视频配音的最佳选择</p>
							<button class="start-button" @click.stop="jump_page(1)">开始创作</button>
						</div>
					</div>
				</div>

				<!-- AI商配卡片 -->
				<div class="feature-card ai-commercial-card first-row-card" @click="jump_page(3)"
					:style="{ backgroundImage: `url(${ai2})`, backgroundSize: '747px 210px', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }">
					<div class="card-content-container">
						<div class="card-left">
							<h3 class="card-title ai-commercial-title">AI 商配</h3>
							<p class="card-description commercial-description">99% 真人音色，100% 真人授权，广告商业片配音首选。</p>
							<button class="start-button commercial-button" @click.stop="jump_page(3)">开始创作</button>
						</div>
					</div>
				</div>
			</div>

			<!-- 添加视频创作标题 -->
			<div class="entrance-title">视频创作</div>
			
			<div class="feature-row">
				<!-- 一键成片卡片 -->
				<div class="feature-card one-click-card" @click="jump_page(0)"
					:style="{ backgroundImage: `url(${ai3})`, backgroundSize: '488px 154px', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }">
					<div class="card-content-container">
						<div class="card-left">
							<h3 class="card-title one-click-title">一键成片</h3>
							<p class="card-description one-click-desc">智能剪辑，AI 一键成片，提升视频内容创作效率</p>
						</div>
					</div>
				</div>

				<!-- 专业云剪卡片 -->
				<div class="feature-card cloud-edit-card" @click="jump_page(2)"
					:style="{ backgroundImage: `url(${ai4})`, backgroundSize: '488px 154px', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }">
					<div class="card-content-container">
						<div class="card-left">
							<h3 class="card-title cloud-edit-title">专业云剪</h3>
							<p class="card-description cloud-edit-desc">智能剪辑，AI 一键成片，提升视频内容创作效率</p>
						</div>
					</div>
				</div>

				<!-- 新增第三个卡片 -->
				<div class="feature-card new-card" @click="jump_page(10)"
					:style="{ backgroundImage: `url(${ai5})`, backgroundSize: '488px 154px', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }">
					<div class="card-content-container">
						<div class="card-left">
							<h3 class="card-title digital-human-title">数字人</h3>
							<p class="card-description digital-human-desc">提供多轨视频、音频、特效、字幕轨道等可视化<br>编辑，实现专业级视频剪辑</p>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- AI工具区域 -->
		<div class="entrance-title">AI工具</div>
		<div class="tools-container">
			<!-- 一键解析视频卡片 -->
			<div class="tool-card new-tool-card" @click="jump_page(5)">
				<div class="tool-icon-container">
					<img :src="li1" alt="一键解析视频" class="new-tool-icon" />
				</div>
				<div class="tool-text-content">
					<div class="tool-main-title">一键解析视频</div>
					<div class="tool-sub-desc">输入视频链接一键解析</div>
				</div>
			</div>

			<!-- 视频去水印字幕卡片 -->
			<div class="tool-card new-tool-card" @click="jump_page(6)">
				<div class="tool-icon-container">
					<img :src="li2" alt="视频去水印字幕" class="new-tool-icon" />
				</div>
				<div class="tool-text-content">
					<div class="tool-main-title">视频去水印字幕</div>
					<div class="tool-sub-desc">智能消除视频中的水印和字幕</div>
				</div>
			</div>

			<!-- 文案提取卡片 -->
			<div class="tool-card new-tool-card" @click="jump_page(7)">
				<div class="tool-icon-container">
					<img :src="li3" alt="文案提取" class="new-tool-icon" />
				</div>
				<div class="tool-text-content">
					<div class="tool-main-title">文案提取</div>
					<div class="tool-sub-desc">输入视频链接一键提取文案</div>
				</div>
			</div>

			<!-- 文案创作卡片 -->
			<div class="tool-card new-tool-card" @click="jump_page(8)">
				<div class="tool-icon-container">
					<img :src="li4" alt="文案创作" class="new-tool-icon" />
				</div>
				<div class="tool-text-content">
					<div class="tool-main-title">文案创作</div>
					<div class="tool-sub-desc">多样化的视频文案模板创作</div>
				</div>
			</div>

			<!-- 声音克隆卡片 -->
			<div class="tool-card new-tool-card voice-clone-card" @click="jump_page(9)">
				<!-- 热门标签 -->
				<img :src="remen" alt="热门" class="hot-badge" />
				<div class="tool-icon-container">
					<img :src="li5" alt="声音克隆" class="new-tool-icon" />
				</div>
				<div class="tool-text-content">
					<div class="tool-main-title">声音克隆</div>
					<div class="tool-sub-desc">快速克隆自己的专属音色</div>
				</div>
			</div>
		</div>
	</Container>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { reactive, ref, getCurrentInstance, onMounted, watch } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { usePreviewStore } from '@/stores/previewStore' // 导入预览store
import { useMusicStore } from '@/stores/modules/musicStore' // 导入音乐store
import { ElMessage } from 'element-plus'
// 获取当前实例，用于访问全局属性
const { proxy } = getCurrentInstance()

// 初始化store实例
const previewStore = usePreviewStore()
const musicStore = useMusicStore()

// 检查用户是否登录
const checkUserLogin = () => {
	const userStorage = localStorage.getItem('user')
	if (!userStorage) return false

	try {
		const userData = JSON.parse(userStorage)
		// 检查token是否存在且不为空
		return userData && userData.token && userData.token.trim() !== ''
	} catch (e) {
		console.error('解析用户数据失败:', e)
		return false
	}
}

//引入静态图片文件
import homeIndex1 from '@/assets/images/index_images/home_index1.png'
import homeIndex2 from '@/assets/images/index_images/home_index2.png'
import homeIndex3 from '@/assets/images/index_images/home_index3.png'
import homeIndex4 from '@/assets/images/index_images/home_index4.png'
import homeIndex5 from '@/assets/images/index_images/home_index5.png'
import homeIndex6 from '@/assets/images/index_images/home_index6.png'
import homeIndex7 from '@/assets/images/index_images/home_index7.png'
import homeIndex8 from '@/assets/images/index_images/home_index8.png'
import homeIndex9 from '@/assets/images/index_images/home_index9.png'
import homeIndex10 from '@/assets/images/index_images/home_index10.png'
// 引入首页头部图片
import shouye1 from '@/assets/img/shouye1.png'
// 引入新的功能卡片图片
import shouye2 from '@/assets/img/shouye2.png'
import shouye3 from '@/assets/img/shouye3.png'
import shouye4 from '@/assets/img/shouye4.png'
import shouye5 from '@/assets/img/shouye5.png'
// 引入头像图片（已不再使用）
// 引入AI配音卡片背景图片
import ai1 from '@/assets/img/ai1.png'
import ai2 from '@/assets/img/ai2.png'
import ai3 from '@/assets/img/ai3.png'
import ai4 from '@/assets/img/ai4.png'
import ai5 from '@/assets/img/ai5.png'

// 引入AI工具区域的新图片
import Link1 from '@/assets/img/Link1.png'
import Link2 from '@/assets/img/Link2.png'
import Link3 from '@/assets/img/Link3.png'
import Link4 from '@/assets/img/Link4.png'

import icon1 from '@/assets/img/icon1.png'
import icon2 from '@/assets/img/icon2.png'
import icon3 from '@/assets/img/icon3.png'
import icon4 from '@/assets/img/icon4.png'
import kelong from '@/assets/img/kelong.png'
import remen from '@/assets/img/remen.png'
import li1 from '@/assets/img/li1.png'
import li2 from '@/assets/img/li2.png'
import li3 from '@/assets/img/li3.png'
import li4 from '@/assets/img/li4.png'
import li5 from '@/assets/img/li5.png'
import { wxStatus, userInfo, showUserBenefits } from '@/api/login.js'
import { useloginStore } from '@/stores/login'
const loginStore = useloginStore()
const imageArr1 = reactive([
	{ url: shouye2, name: '开始创作' },
	{ url: shouye3, name: '开始配音' },
])
const imageArr2 = reactive([
	{ url: shouye4, name: '开始剪辑' },
	{ url: shouye5, name: '开始配音' },
])
const imageArr3 = reactive([
	{ url: Link1 },
	{ url: Link2 },
	{ url: Link3 },
	{ url: Link4 },
])
// 首页跳转页面统一用这个
const router = useRouter()
let route = useRoute()
const jump_page = (index) => {
	console.log(index)

	switch (index) {
		case 0:
			// 清空pinia预览数据
			console.log('清空Pinia预览数据')

			// 清空预览标题
			previewStore.setTitle('')
			// 清空预览内容
			previewStore.setContent('')

			// 清空选中角色
			previewStore.setRole(null)
			// 清空视频列表和选中的视频ID
			previewStore.setVideoList([])
			previewStore.setSelectedVideoIds([])
			// 清空已提取内容和时间线标记
			previewStore.clearExtractedContent()
			previewStore.setIsTimelineContent(false)
			// 清空当前视频URL和时间戳
			previewStore.setCurrentVideoUrl('')
			previewStore.setVideoTimestamps([])

			// 清空音乐背景
			musicStore.clearAll()
			// 弹窗提示即将发布，使用element吐司
			// ElMessage.warning('即将发布')

			// 跳转到一键成片页面
			router.replace({ name: 'Editor', query: { refresh: 'true' } })
			break
		case 1:
			// 跳转到ai配音页面
			router.replace({ name: 'AIDubbing' })
			break
		case 2:
			// 跳转到外部云剪网站，使用新页面打开
			let token = ""
			try {
				const userStr = localStorage.getItem('user')
				if (userStr) {
					const userObj = JSON.parse(userStr)
					token = userObj.token || ""
				}
			} catch (error) {
				console.error('获取token失败:', error)
			}
			// 使用window.open打开新页面，而不是在当前页面跳转
			window.open(`https://yunjian.peiyinbangshou.com/App?token=${token}`, '_blank')
			break
		case 3:
			// 跳转到商业配音页面
			router.replace({ name: 'commercialDubbing' })
			break
		case 4:
			// // 跳转到智能加字幕
			// router.replace({name:'captions'})
			break
		case 5:
			// 跳转到解析视频
			router.replace({ name: 'ParsingVideo' })
			break
		case 6:
			// 跳转视频去水印
			router.replace({ name: 'watermark' })
			break
		case 7:
			// 文案提取
			router.replace({ name: 'Extraction' })
			break
		case 8:
			// 一键成片（文案创作）
			// 检查用户是否已登录
			if (!checkUserLogin()) {
				// 用户未登录，调用全局登录弹窗方法
				proxy.$modal.open('组合式标题')
				return
			}
			// 已登录，正常跳转
			router.replace({ name: 'compose' })
			break
		case 9:
			// 声音克隆
			router.replace({ name: 'voiceClone' })
			break
		case 10:
			// 跳转到数字人中转页面
			if (!checkUserLogin()) {
				// 用户未登录，调用全局登录弹窗方法
				proxy.$modal.open('组合式标题')
				return
			}
			router.replace({ name: 'DigitalHumanTransition' })
			break
	}
}

// 这里可以添加你的逻辑代码
onMounted(() => {
	const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
	if (isSafari) {
		const el = document.querySelector('.home-container');
		if (el) el.style.minHeight = '100vh';
	}
});
//获取微信登录信息
let getWxStatus=async(state)=>{
    try {
      // 调用后端接口查询支付状态
      let data = await wxStatus({state});
      if (data.token) {
          data.token&&loginStore.setToken(data.token)
          let user_data=await userInfo({userId:loginStore.userId})
          console.log(user_data,'user_data');
          
          user_data.userId&&loginStore.setUserId(user_data.userId)
          user_data&&loginStore.setLoginData(user_data)
          user_data&&loginStore.setUserInfo(user_data)
          let res=await showUserBenefits({userId:user_data.userId})
		  if(res.code!=0){
			  ElMessage.error(res.msg) 
			  return 
		  }
		  let member_data=res?.data?.content
          member_data.result&&loginStore.setMemberInfo(member_data.result)
          setTimeout(()=>{
			router.replace({ path: route.path });
          },200)
     // 退出函数
      } 
  } catch (error) {
      console.error('微信登录失败:', error);
  }
}
//监听微信登陆跳转
watch(() => route.query, (newValue, oldValue) => {
	console.log(newValue, 'newValue');

	if (newValue.state) {
		getWxStatus(newValue.state)
	}
	// 在这里可以执行其他逻辑
}, { immediate: true });

</script>



<style scoped lang="scss">
.home-container {
	width: 100%;
	margin: 0 auto;
	padding: 0 20px;
	box-sizing: border-box;
	max-width: 1546px;
	overflow-x: hidden;
	/* 防止水平滚动条 */
	// min-height: 100vh;
}

/* 头部图片样式 */
.header-banner {
	margin-bottom: 15px;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	width: 100%;
	// height: 180px;

	.header-banner-img {
		width: 100%;
		height: 100%;
		display: block;
		border-radius: 10px;
		object-fit: cover;
	}
}

/* 主要功能区域样式 */
.feature-section {
	margin-bottom: 15px;
	width: 100%;
}

.feature-row {
	display: flex;
	gap: 20px;
	margin-bottom: 20px;
	justify-content: center;
	flex-wrap: nowrap;
}

.feature-section .feature-row:last-child {
	margin-bottom: 53px;
}

.feature-card {
	width: 750px;
	height: 154px;
	background-color: #fff;
	border-radius: 10px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	display: flex;
	position: relative;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1px solid transparent;
	padding: 0;
	
	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		border-color: #e0e3e9;
	}

.card-content {
	flex: 1;
	padding: 20px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	z-index: 2;
}

.card-content-container {
	display: flex;
	width: 100%;
	height: 100%;
	gap: 32px;
}

.card-left {
	flex: 1;
	padding-left: 28px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	position: relative;
}

.card-title {
	font-size: 24px;
	font-weight: 600;
	color: #000000;
	margin-top: 0;
}

.card-description {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	word-wrap: break-word;
	white-space: normal;
}

.ai-voice-title {
	margin-bottom: 0;
}

.ai-commercial-title {
	margin-bottom: 0;
}

.ai-voice-card .card-left,
.ai-commercial-card .card-left {
	justify-content: flex-start;
	padding-top: 32px;
}

.ai-voice-description {
	max-width: 350px !important;
	white-space: normal !important;
	word-wrap: break-word !important;
	overflow-wrap: break-word !important;
}


.commercial-button {
	margin-top: 30px !important;
}



.start-button {
	width: 112px;
	height: 35px;
	margin-top: 20px;
	background-color: #0AAF60;
	color: #FFFFFF;
	border: none;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		opacity: 0.9;
	}
}

.card-image-container {
	width: 100%;
	height: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
}

.card-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	/* 修改为contain确保图片完整显示 */
	display: block;
}
}

.first-row-card {
	height: 210px;
}

/* 特定卡片背景色 */
.ai-voice-card {
	/* 背景图片通过:style动态绑定 */
}

.ai-commercial-card {
	/* 背景图片通过:style动态绑定 */
}

.one-click-card {
	/* 背景图片通过:style动态绑定 */
}

.cloud-edit-card {
	/* 背景图片通过:style动态绑定 */
}

.new-card {
	/* 背景图片通过:style动态绑定 */
}

/* 区域标题样式 */
.section-title {
	font-size: 20px;
	font-weight: 500;
	color: #333;
	margin: 15px 0;
}

/* AI工具区域样式 */
.tools-container {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: space-between;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}

.tool-card {
	border-radius: 10px;
	display: flex;
	align-items: center;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	cursor: pointer;
	border: 1px solid transparent;
	overflow: hidden;
	width: 289.2px;
	height: 100px;

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		border-color: #e0e3e9;
	}
}

.entrance-title {
	display: block;
	font-size: 20px;
	font-weight: 600;
	color: #333;
	margin-bottom: 10px;
	border: none;
	background-color: transparent;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
	text-align: left;
}

.one-click-title,
.cloud-edit-title,
.digital-human-title {
	font-size: 24px;
	color: #000000;
}

.one-click-card .card-title {
	margin-bottom: 13px;
}

.cloud-edit-card .card-title,
.new-card .card-title {
	margin-bottom: 13px;
}

.digital-human-title {
	margin-bottom: 0px !important;
}

.one-click-desc {
	color: #8F8F8F;
}

.cloud-edit-desc {
	color: #353D49;
}

.digital-human-desc {
	color: #666;
	line-height: 1.5;
}

.new-tool-card {
	background-color: #fff;
	border-radius: 10px;
	padding: 20px;
	display: flex;
	align-items: center;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	cursor: pointer;
	border: 1px solid transparent;
	overflow: hidden;
	width: 289.2px;
	height: 100px;
	gap: 16px;
	flex-shrink: 0;

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		border-color: #e0e3e9;
	}

	.tool-icon-container {
		width: 48px;
		height: 48px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.new-tool-icon {
		width: 100%;
		height: 100%;
		object-fit: contain;
		display: block;
	}

	.tool-text-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8px;
	}

	.tool-main-title {
		font-size: 18px;
		color: #3C3C3C;
		font-weight: 600;
		line-height: 1.2;
	}

	.tool-sub-desc {
		font-size: 14px;
		color: #8F8F8F;
		line-height: 1.4;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

/* 声音克隆卡片特殊样式 */
.voice-clone-card {
	position: relative;
	overflow: visible;
}

/* 热门标签样式 */
.hot-badge {
	position: absolute;
	top: -10px;
	right: -5px;
	width: 67px;
	height: 27px;
	z-index: 999;
	animation: pulse 2s infinite;
}

/* 脉冲动画 */
@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}

	50% {
		transform: scale(1.1);
		opacity: 0.8;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}
</style>
