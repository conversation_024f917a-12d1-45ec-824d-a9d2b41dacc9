<template>
    <div class="income-container">
        <!-- 操作区域 - 移到顶部 -->
        <div class="operation-bar">
            <div class="right-actions">
                <el-button type="primary" class="custom-withdraw-btn" @click="handleWithdrawal">提现</el-button>
            </div>
        </div>

        <!-- 顶部卡片区域 - 收益总览 -->
        <div class="income-overview">
            <div class="overview-card" v-for="(card, index) in overviewCards" :key="index">
                <div class="card-amount">{{ card.amount }}</div>
                <div class="card-title">{{ card.title }}</div>
            </div>
        </div>
        
        
        <!-- 收益明细表格 -->
        <div class="income-details">
            <el-table :data="incomeRecords" stripe style="width: 100%" header-align="center" height="450px"
                v-loading="state.isLoading">
                <el-table-column v-for="column in tableColumns" :key="column.prop" :prop="column.prop"
                    :label="column.label" :width="column.width" :align="column.align">
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="state.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="state.pageSize"
                    layout="prev, pager, next, jumper" :total="state.totalRecords">
                </el-pagination>
            </div>
        </div>

        <!-- 提现弹窗 - 显示实际二维码 -->
        <el-dialog title="扫码提现" v-model="withdrawalDialogVisible" width="420px" class="withd_rawal_dialog" :show-close="false">
            <template #header>
                <div class="withd_rawal_dialog_title">
                    <div class="withd_rawal_dialog_title_text">
                        扫码提现
                    </div>
                    <img class="withd_rawal_dialog_close" src="@/assets/img/withd_rawal_dialog_close.svg" @click="withdrawalDialogVisible=false" alt="">
                </div>
              
            </template>
             
            <template #default>
            <div class="qrcode-container">
                <div class="qrcode-wrapper">
                    <span>扫码加客服提现</span>
                    <!-- 方法1: 使用public文件夹中的图片 (推荐) -->
                    <img src="../../../assets/img/erweima.png" alt="提现二维码" class="qrcode-image" />
                </div>
                <div class="qrcode-tips">
                    <p>扫码添加客服提现</p>
                    <p>当前可提现金额: <span class="available-amount">{{ state.balance }} 元</span></p>
                </div>
            </div>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeWithdrawalDialog">关闭</el-button>
                </span>
            </template>
        </el-dialog>
        
        <!-- 系统公告弹窗 -->
        <SystemNoticeDialog 
            :visible="showSystemNotice" 
            @close="handleCloseSystemNotice" 
        />
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useIncome } from './useIncome.js'
import SystemNoticeDialog from '@/components/SystemNoticeDialog/index.vue'

// localStorage 存储键名常量
const INCOME_NOTICE_VIEWED_KEY = 'income_system_notice_viewed'

// 使用收益组合式函数
const {
    state,
    overviewCards,
    tableColumns,
    incomeRecords,
    withdrawalDialogVisible,
    handleWithdrawal,
    handleSizeChange,
    handleCurrentChange,
    initIncomeData,
    closeWithdrawalDialog
} = useIncome()

// 系统公告弹窗显示状态
const showSystemNotice = ref(false)

// 检查是否已查看过系统公告
const hasViewedSystemNotice = () => {
    return localStorage.getItem(INCOME_NOTICE_VIEWED_KEY) === 'true'
}

// 记录已查看系统公告
const markSystemNoticeAsViewed = () => {
    localStorage.setItem(INCOME_NOTICE_VIEWED_KEY, 'true')
}

// 关闭系统公告弹窗
const handleCloseSystemNotice = () => {
    showSystemNotice.value = false
    // 记录用户已查看过系统公告
    markSystemNoticeAsViewed()
}

// ===== Preprocessing (前置处理) =====
// 页面初始化
onMounted(() => {
    // 初始化数据
    initIncomeData()
    
    // 检查是否首次访问，如果是首次则显示系统公告弹窗
    if (!hasViewedSystemNotice()) {
        setTimeout(() => {
            showSystemNotice.value = true
        }, 500) // 延迟500ms显示，确保页面渲染完成
    }
})
</script>




<style lang="scss" scoped>
.income-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 60px);

    .operation-bar {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 30px; // 增加下方间距

        .right-actions {
            display: flex;
            gap: 10px;

            .custom-withdraw-btn {
                background-color: #09AF5E;
                border-color: #09AF5E;
                color: white;

                &:hover,
                &:focus {
                    background-color: #0bbe6a;
                    border-color: #0bbe6a;
                }

                &:active {
                    background-color: #08a055;
                    border-color: #08a055;
                }
            }
        }
    }

    .income-overview {
        display: flex;
        gap: 20px;
        margin-bottom: 40px; // 增加下方间距
        margin-top: 20px; // 增加与上方操作栏的距离
        background-color: #e8f7ee;
        padding: 30px;
        border-radius: 8px;

        .overview-card {
            flex: 1;
            text-align: center;
            padding: 20px;

            .card-title {
                font-size: 14px;
                color: #606266;
                margin-top: 10px;
            }

            .card-amount {
                font-size: 24px;
                font-weight: bold;
                color: #303133;
            }
        }
    }

    .income-details {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        padding: 20px;

        // 表格样式优化
        :deep(.el-table) {
            border-radius: 4px;
            overflow: hidden;

            .el-table__header th {
                background-color: #f5f7fa;
                font-weight: bold;
            }

            .el-table__row {
                cursor: pointer;
                transition: background-color 0.2s;

                &:hover {
                    background-color: #f0f9eb;
                }
            }
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }
    }

    .qrcode-container {
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 18px 21px;
            border: 0.5px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            margin-bottom: 10px;
            span{
                font-size: 16px;
                line-height: 22px;
                letter-spacing: -0.01px;
                color: #000000;
                margin-bottom: 10px;
            }
            .qrcode-image {
                width: 124px;
                height: 124px;
            }
        }

        .qrcode-tips {
            text-align: center;

            p {
                font-size: 14px;
                line-height: 22px;
                letter-spacing: -0.01px;
                margin: 8px 0;
                color: #606266;
                margin: 0;
            }

            .available-amount {
                font-weight: bold;
                color: #0AAF60;
            }
        }
    }
}
::v-deep(.withd_rawal_dialog){
    padding: 0px 16px 16px;
    background: #FFFFFF;
    border-radius: 4px;
    .el-dialog__header{
        padding: 0;
        margin-bottom: 16px;
        .withd_rawal_dialog_title{
            display: flex;
            align-items:center;
            padding: 9px 0px;
            .withd_rawal_dialog_title_text{
                font-size: 16px;
                line-height: 22px;
                color: #1D2129;
            }
            .withd_rawal_dialog_close{
                margin-left: auto;
                width: 20px;
                height: 20px;
                cursor: pointer;
            }
        }
    }
    .el-dialog__body{
         .qrcode-wrapper{
            display: flex;
            flex-direction: column;
            align-items: center;
            .qrcode-img{
                width: 200px;
                height: 200px;
            }
         }
    }
   
    .el-dialog__footer{
        display: flex;
        justify-content: flex-end;
        button{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 32px;
            background: #F2F3F5;
            border-radius: 2px;
            border: none;
            font-size: 14px;
            line-height: 22px;
            color: #4E5969;
        }
        
    }
   
}
</style>