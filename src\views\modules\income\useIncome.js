import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { getVoiceActorIncome } from '@/api/income'
import { getCurrentInstance } from 'vue'
import { useloginStore } from '@/stores/login'
const loginStore = useloginStore() // 获取 loginStore 实例
/**
 * 用户登录检查函数
 * 从localStorage获取用户信息并检查是否登录
 * @returns {boolean} 是否已登录
 */
function checkUserLogin() {
	const userStorage = localStorage.getItem('user');
	if (userStorage) {
		try {
			const userData = JSON.parse(userStorage);
			// 检查token是否为null或空
			return userData && userData.token && userData.token.trim() !== '';
		} catch (e) {
			console.error('解析用户数据失败', e);
			return false;
		}
	}
	return false;
}

/**
 * 收益管理相关的组合式函数
 * 封装了收益页面相关的状态和方法
 */
export function useIncome() {
	const { proxy } = getCurrentInstance()
	
	// 页面状态管理
	const state = reactive({
		isLoading: false,
		currentPage: 1,
		pageSize: 10,
		totalRecords: 0,
		balance: '0' // 余额
	})

	// 卡片数据
	const overviewCards = ref([
		{ title: '余额 (元)', amount: '0' },
		{ title: '订单数', amount: '0' },
		{ title: '消耗数', amount: '0' }
	])

	// 表格配置
	const tableColumns = ref([
		{ prop: 'index', label: '序号', width: '80', align: 'center' },
		{ prop: 'voice', label: '选用音色', width: '180', align: 'center' },
		{ prop: 'duration', label: '合成时间 (字符)', width: '180', align: 'center' },
		{ prop: 'income', label: '个人收益（分）', width: '180', align: 'center' },
		{ prop: 'orderNo', label: '订单编号', align: 'center' },
		{ prop: 'orderTime', label: '订单时间', width: '180', align: 'center' }
	])

	// 收益记录数据
	const incomeRecords = ref([])

	// 提现弹窗状态
	const withdrawalDialogVisible = ref(false)

	/**
	 * 获取收益记录数据
	 * 直接从API获取当前页的数据
	 */
	const fetchIncomeRecords = async () => {
		try {
			// 检查用户是否登录
			if (!checkUserLogin()) {
				proxy.$modal.open('组合式标题');
				return;
			}
			
			state.isLoading = true;
			
			// 直接获取当前页数据
			const res = await getVoiceActorIncome({
				user_id: loginStore.userId,
				page: state.currentPage,
				page_size: state.pageSize
			});
			
			if (res && res.result) {
				// 更新总记录数和概览数据
				state.totalRecords = res.result.total || 0;
				state.balance = res.result.cost || '0';
				
				// 更新概览卡片数据
				overviewCards.value = [
					{ title: '余额 (元)', amount: res.result.cost || '0' },
					{ title: '订单数', amount: res.result.total || '0' },
					{ title: '消耗数', amount: res.result.word_total || '0' }
				];
				
				// 处理表格数据
				if (res.result.traces) {
					// 将数据映射为表格所需格式
					incomeRecords.value = res.result.traces.map((item, index) => ({
						index: (state.currentPage - 1) * state.pageSize + index + 1,
						voice: item.voice_name || '未知音色',
						duration: item.word_count || '0',
						income: item.cost ? (Number(item.cost) * 100).toFixed(3) : '0',
						orderNo: item.trace_id || '无',
						orderTime: item.day || '无'
					}));
				} else {
					incomeRecords.value = [];
				}
			}
			
			console.log('收益记录获取成功:', incomeRecords.value);
		} catch (error) {
			console.error('获取收益数据失败:', error);
			ElMessage.error('获取收益数据失败，请稍后重试');
			incomeRecords.value = [];
		} finally {
			state.isLoading = false;
		}
	}

	/**
	 * 提现操作
	 */
	const handleWithdrawal = () => {
		// 检查用户是否登录
		if (!checkUserLogin()) {
			proxy.$modal.open('组合式标题');
			return;
		}
		
		withdrawalDialogVisible.value = true
	}

	/**
	 * 关闭提现弹窗
	 */
	const closeWithdrawalDialog = () => {
		withdrawalDialogVisible.value = false
	}

	/**
	 * 分页大小变化处理
	 */
	const handleSizeChange = (val) => {
		// 检查用户是否登录
		if (!checkUserLogin()) {
			proxy.$modal.open('组合式标题');
			return;
		}
		
		state.pageSize = val;
		state.currentPage = 1; // 重置为第一页
		
		// 重新获取数据
		fetchIncomeRecords();
	}

	/**
	 * 当前页变化处理
	 */
	const handleCurrentChange = (val) => {
		// 检查用户是否登录
		if (!checkUserLogin()) {
			proxy.$modal.open('组合式标题');
			return;
		}
		
		state.currentPage = val;
		
		// 直接获取新页面数据
		fetchIncomeRecords();
	}

	/**
	 * 初始化收益页面数据
	 */
	const initIncomeData = async () => {
		// 设置初始页为第1页并获取数据
		state.currentPage = 1;
		await fetchIncomeRecords();
	}

	return {
		// 状态
		state,
		overviewCards,
		tableColumns,
		incomeRecords,
		withdrawalDialogVisible,

		// 方法
		fetchIncomeRecords,
		handleWithdrawal,
		closeWithdrawalDialog,
		handleSizeChange,
		handleCurrentChange,
		initIncomeData
	}
} 