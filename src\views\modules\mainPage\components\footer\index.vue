<template>
    <div class="main_footer">
        <div class="main_footer_contaner">
            <div class="main_footer_top">
                <div class="main_footer_top_nav">
                    <div class="main_footer_top_nav_item">
                        <h4>配音帮手&服务</h4>
                        <ul>
                            <li>老师挑选</li>
                            <li>分类挑选</li>
                            <li>配乐FAQ</li>
                        </ul>
                    </div>
                    <div class="main_footer_top_nav_item">
                        <h4>妙音&内容</h4>
                        <ul>
                            <li>新片场</li>
                            <li>优酷视频展示FAQ</li>
                            <li>妙音知识</li>
                        </ul>
                    </div>
                    <div class="main_footer_top_nav_item">
                        <h4>妙音&频道</h4>
                        <ul>
                            <li>妙音微博</li>
                            <li>黑马配音</li>
                            <li>妙音FM</li>
                        </ul>
                    </div>
                </div>
                <div class="main_footer_top_contact">
                    <h4>服务热线</h4>
                    <span class="main_footer_top_phone">15376662695</span>
                    <span>传真：0531-86068008</span>
                    <span>地址：济南市历下区三庆枫润大厦A座21层妙音传媒</span>
                    <span>配音员招聘：张老师 132105316</span>
                </div>
            </div>
            <div class="main_footer_bottom">
                <span class="main_footer_bottom_copyright">
                    2024妙音AI.All rights reserved.
                </span>
                <div class="main_footer_bottom_qrcode">
                    <div class="main_footer_bottom_qrcode_img">
                        <img src="" alt="">
                    </div>
                    <div class="main_footer_bottom_qrcode_img">
                        <img src="" alt="">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
</script>
<style lang="scss" scoped>
.main_footer{
    background-color:#1A2331;
    .main_footer_contaner{
        width: 1414px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        padding: 116px 0 109px;
        .main_footer_top{
            display: flex;
            padding-bottom: 70px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.32);
            margin-bottom: 44px;
            .main_footer_top_nav{
                display: flex;
                align-items: flex-start;
                .main_footer_top_nav_item{
                    h4{
                        margin: 0;
                        font-weight: bold;
                        font-size: 16px;
                        color: rgba(255, 255, 255, 0.87);
                        padding-bottom: 18px;
                        position: relative;
                        margin-bottom: 31px;
                        &::after{
                            content: '';
                            width: 51px;
                            height: 1px;
                            background: #FFFFFF;
                            position: absolute;
                            left: 0;
                            bottom: 0;
                        }
                    }
                    ul{
                        li{
                            font-size: 14px;
                            color: rgba(255, 255, 255, 0.65);
                            cursor: pointer;
                            margin-bottom: 23px;
                            line-height: 14px;
                            &:last-child{
                                margin-bottom: 0;
                            }
                        }
                    }
                    &:first-child{
                        margin-right: 84px;
                    }
                    &:nth-child(2){
                        margin-right: 50px;
                    }

                }
            }
            .main_footer_top_contact{
                margin-left: auto;
                margin-right: 65px;
                width: 330px;
                display: flex;
                flex-direction: column;
                h4{
                    margin: 0;
                    font-weight: bold;
                    font-size: 16px;
                    color: rgba(255, 255, 255, 0.87);
                    padding-bottom: 18px;
                    position: relative;
                    margin-bottom: 24px;
                    &::after{
                        content: '';
                        width: 51px;
                        height: 1px;
                        background: #FFFFFF;
                        position: absolute;
                        left: 0;
                        bottom: 0;
                    }
            }
                span{
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 21px;
                    color: rgba(255, 255, 255, 0.65);
                    &.main_footer_top_phone{
                        font-weight: bold;
                        font-size: 25px;
                        margin-bottom: 26px;
                        line-height: 18px;
                    }
                    &:last-child{
                        margin-bottom: 0;
                    }
                }
                
            }
        }
        .main_footer_bottom{
            height: 107px;
            display: flex;
            align-items: center;
            .main_footer_bottom_copyright{
                font-size: 16px;
                color: #FFFFFF;
            }
            .main_footer_bottom_qrcode{
                margin-left: auto;
                display: flex;
                align-items: center;
                .main_footer_bottom_qrcode_img{
                    width: 107px;
                    height: 107px;
                    margin-right: 31px;
                    width: 107px;
                    height: 107px;
                    background: #CCCCCC;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
                
            }
        }

    }
}
</style>