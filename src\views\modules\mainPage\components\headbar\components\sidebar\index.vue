<script setup>
import Subfield from './components/subfield/index.vue'
// import Subfield from './components/subfield/index.vue'
// const route = useRoute()
// const menuStore = useMenuStore()
// const themeStore = useThemeStore()
// const menuLayoutMode = computed(() => themeStore.layout.menuLayoutMode)
//
// const { active } = storeToRefs(menuStore)
// 判断路由菜单样式以哪种模式从呈现出来
// const component = computed(() => {
//   let result = ''
//   switch (menuLayoutMode.value) {
//     case 1:
//       result = Classic
//       break
//     case 2:
//       result = Subfield
//       break
//   }
//
//   return result
// })
// console.log('888',component.value)
/**
 * @description: 路由变化事件
 * @param {*}
 * @return {*}
 * @author: gumingchen
 */
// const routeHandle = argRoute => {
//   const name = argRoute.name
//   console.log('000000',name)
//   active.value = name
// }
//
// watchEffect(() => {
//   routeHandle(route)
// })
</script>

<template>
  <component class="margin_l-35" :is="Subfield" />
</template>

<style lang="scss" scoped>

</style>
