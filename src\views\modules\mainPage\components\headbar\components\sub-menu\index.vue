<script setup>
import SubTtem from './index.vue'
import { useRouter } from 'vue-router'
import { MenuType } from '@/common/enums'
import { getCurrentInstance, } from 'vue'
import { useUmeng } from '@/utils/umeng/hook' // 导入埋点

const { proxy } = getCurrentInstance();
// 初始化埋点
const umeng = useUmeng()

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})
// console.log('pppppp',props.data)
const router = useRouter()
/**
 * 菜单点击事件
 * @param name 路由名称
 */
const checkUserLogin = () => {
  // 从本地存储获取user信息
  const userStorage = localStorage.getItem('user');
  if (!userStorage) return false;

  try {
    const userData = JSON.parse(userStorage);
    // 检查token是否为null或空
    return userData && userData.token && userData.token.trim() !== '';
  } catch (e) {
    console.error('解析用户数据失败:', e);
    return false;
  }
};
const clickHandle = (e) => {
  console.log(props.data, 'clickHandle');

  // 添加埋点
  umeng.trackEvent(
    '页面导航',
    '点击导航菜单',
    `${props.data.titleName || props.data.name || '未知菜单'}`,
    ''
  )

  const isMySpaceRelated = props.data.name && (
    props.data.name === 'myWorks'
    // ||
    // props.data.name === 'membership'//会员计划
  );
  if (isMySpaceRelated && !checkUserLogin()) {
    proxy.$modal.open('组合式标题');
    return;
  }
  switch (props.data.type) {
    case MenuType.URL: // 外链
      window.open(props.data.url)
      break
    case MenuType.ROUTER: // 路由
    case MenuType.MENU: // 菜单
    case MenuType.IFRAME: // iframe
      router.replace({ name: props.data.name }) // 改为replace，与工作台保持一致，确保每次点击都刷新
      break
  }
}
</script>

<template>
  <el-menu-item-group title="Group One" v-if="data.type === 5" :index="data.name || data.id + ''">
    <template #title>
      <!--      <Iconfont :name="data.icon" class="padding_r-5" v-if="data.icon" />-->
      <span>{{ data.titleName }}33</span>
    </template>
    <SubTtem v-for="item in data.children" :key="item.id" :data="item" />
  </el-menu-item-group>
  <el-sub-menu v-else-if="data.children && data.children.length > 0" :index="data.name || data.id + ''">
    <template #title>
      <!--      <Iconfont :name="data.icon" class="padding_r-5" v-if="data.icon" />-->
      <span>{{ data.titleName }}1122</span>
    </template>
    <SubTtem v-for="item in data.children" :key="item.id" :data="item" />
  </el-sub-menu>
  <el-menu-item v-else :index="data.name || data.id + ''" @click="clickHandle" class="hhhh">
    <!--    <Iconfont :name="data.icon" class="padding_r-5" v-if="data.icon" />-->
    <!--    <el-icon size="15" color="#409EFC">-->
    <!--      <Iconfont :name="data.icon" class="padding_r-5" v-if="data.icon" />-->
    <!--    </el-icon>-->
    <template #title>
      <span>{{ data.titleName }}</span>
      <img class="discountImg" v-if="data.titleName == '会员计划'" src="@/assets/img/discount.png" alt="" />
    </template>
  </el-menu-item>
</template>

<style scoped lang="scss">
::v-deep(.el-sub-menu__title) {
  background-color: #fff;
}

::v-deep(.el-sub-menu__title):hover {
  background-color: #fff !important;
}

.hhhh {
  height: fit-content !important;

  //background-color: #fff!important;
  span {
    line-height: 18px !important;
    height: 18px !important;
  }

}

.el-menu-item:hover {
  background-color: #fff !important;
}

.el-menu--popup {
  border: none;
  border-radius: var(--el-border-radius-small);
  box-shadow: var(--el-box-shadow-light);
  min-width: 200px;
  padding: 5px 0;
  z-index: 100;
}

.el-menu {
  height: fit-content !important;
}

.sidebar-classic-container[data-v-363c1793] .el-menu--horizontal>.el-menu-item:nth-child(5) {
  padding: 0 0 0 0 !important;
}

.discountImg {
  position: relative;
  top: -10px;
}
</style>
