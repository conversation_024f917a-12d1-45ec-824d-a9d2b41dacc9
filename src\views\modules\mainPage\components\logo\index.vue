<script setup>
import { useRouter,useRoute } from 'vue-router'

import tobuDark from '@/assets/img/tobu_dark.png'
import tobu from '@/assets/img/tobu.png'
const router = useRouter()
const route = useRoute()
// const props = defineProps({
//   collapse: {
//     type: Boolean,
//     default: () => true
//   }
// })

// const adminerStore = useAdminerStore()
//
// const { tenant } = storeToRefs(adminerStore)
let to_home=()=>{
  router.push('/home')
}
</script>

<template>
  <div class="logo-container flex flex_j_c-center cursor-pointer" @click="to_home">
    <img :src="route.meta.header_module == 'dark'?tobuDark:tobu" alt="logo"  srcset="" class="logo_image  height-40" :style="{width:route.meta.header_module == 'dark'?'108px':'auto'}">
<!--    v-if="tenant.name || tenant.logo">-->
<!--    <transition name="el-fade-in" mode="out-in">-->
<!--      <div class="flex flex-item_f-1 flex_j_c-center flex_a_i-center" v-if="!collapse">-->
<!--        <el-image class="width-34 height-34" :src="tenant.logo" />-->
<!--        <div class="margin_l-10 ellipse">-->
<!--          {{ tenant.name }}-->
<!--        </div>-->
<!--      </div>-->
<!--      <div class="flex flex-item_f-1 flex_j_c-center flex_a_i-center" v-else>-->
<!--        <el-image class="width-34 height-34" :src="tenant.logo" v-if="tenant.logo" />-->
<!--        <div class="margin_l-10 ellipse" v-else>-->
<!--          {{ tenant.name }}-->
<!--        </div>-->
<!--      </div>-->
<!--    </transition>-->
  </div>
</template>

<style lang="scss" scoped>
.logo-container {
  //height: var(--gl-headbar-height);
  //line-height: var(--gl-headbar-height);
  // height:var(--gl-headbar-height);
  // line-height:var(--gl-headbar-height);
  // background-color: #fff;
  //background-color: #006eff;
  transition: width 0.4s;
  display: flex;
  align-items: center;
  justify-content: center;
  .logo_image{
    border: none;
  }
}
</style>
