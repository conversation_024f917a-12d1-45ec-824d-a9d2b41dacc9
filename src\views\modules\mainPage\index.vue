<script setup>
import Headbar from './components/headbar/index.vue'

import Sidebar from "@/views/layout/components/sidebar/index.vue";
import { useRoute } from 'vue-router';
const route = useRoute()
console.log('路由信息啊',route.meta);
</script>

<template>
	<div class="layout-container height-full flex overflow-auto" :style="{ paddingTop: route.meta.fixedHeader ? '60px' : '0' }">
		<div class="navigation-container flex-item_f-1 flex flex_d-column overflow-auto">
			<Headbar />
			<router-view v-slot="{ Component }">
				<transition name="el-fade-in">
					<component :is="Component" class="flex-item_f-1" />
				</transition>
			</router-view>
		</div>

	</div>
</template>
<style scoped lang="scss"></style>