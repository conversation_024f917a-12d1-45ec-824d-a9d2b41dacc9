<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="480px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :show-close="true"
    top="30vh"
    class="project-dialog"
  >
    <div class="input-container">
      <el-input 
        v-model="form.name" 
        placeholder="最多50个字符" 
        clearable 
        maxlength="50"
        show-word-limit
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="handleClose">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新建专辑'
  }
});

const emit = defineEmits(['update:visible', 'created']);

// 计算属性：对话框标题
const dialogTitle = computed(() => props.title);

// 表单数据
const form = ref({
  name: ''
});

const dialogVisible = ref(false);

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 关闭对话框
const handleClose = () => {
  form.value.name = '';
  emit('update:visible', false);
};

// 提交表单
const submitForm = () => {
  if (!form.value.name.trim()) {
    ElMessage.warning('请输入专辑名称');
    return;
  }
  
  // 创建项目成功
  emit('created', {
    name: form.value.name,
    id: Date.now(), // 使用时间戳作为临时ID
    updateTime: new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '.')
  });
  
  ElMessage.success('专辑创建成功');
  handleClose();
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  background-color: #eef5ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .el-dialog__header {
    margin-right: 0;
    padding: 16px 20px;
    text-align: center;
    border-bottom: none;
    position: relative;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
    
    .el-dialog__headerbtn {
      position: absolute;
      top: 16px;
      right: 20px;
    }
  }
  
  .el-dialog__body {
    padding: 0 20px 40px;
  }
  
  .el-dialog__footer {
    padding: 0 20px 20px;
    border-top: none;
  }
}

.input-container {
  margin-top: 16px;
  
  .el-input {
    --el-input-border-color: #dcdfe6;
    --el-input-hover-border-color: #c0c4cc;
    --el-input-focus-border-color: #409eff;
    
    .el-input__wrapper {
      background-color: white;
      border-radius: 4px;
      padding: 0 12px;
      box-shadow: none;
      border: 1px solid var(--el-input-border-color);
      
      &:hover {
        border-color: var(--el-input-hover-border-color);
      }
      
      &.is-focus {
        border-color: var(--el-input-focus-border-color);
      }
    }
    
    .el-input__inner {
      height: 40px;
      line-height: 40px;
    }
    
    .el-input__count {
      color: #909399;
      font-size: 12px;
      bottom: -20px;
      right: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  
  .cancel-btn, .confirm-btn {
    min-width: 80px;
    height: 36px;
    font-size: 14px;
    padding: 0 16px;
    border-radius: 4px;
  }
  
  .cancel-btn {
    --el-button-text-color: #606266;
    --el-button-bg-color: #f5f7fa;
    --el-button-border-color: #dcdfe6;
    --el-button-hover-text-color: #606266;
    --el-button-hover-bg-color: #ebeef5;
    --el-button-hover-border-color: #dcdfe6;
  }
  
  .confirm-btn {
    --el-button-bg-color: #0AAF60;
    --el-button-border-color: #0AAF60;
    --el-button-hover-bg-color: #09a058;
    --el-button-hover-border-color: #09a058;
  }
}
</style> 