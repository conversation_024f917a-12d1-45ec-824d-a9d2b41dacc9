<template>
	<div class="custom-table-container" :class="{ 'centered': centered }">
		<el-table :data="tableData" style="width: 100%" :cell-class-name="getCellClass" v-loading="loading" :element-loading-text="loadingText" element-loading-background="rgba(255, 255, 255, 0.8)" @row-click="handleRowClick" :empty-text="emptyText || '暂无数据'" v-bind="maxHeight ? { maxHeight } : {}"> 
			<el-table-column v-if="showSelection" type="selection" width="55" align="center" />
			
			<!-- 添加单选列 - 修复对齐问题 -->
			<el-table-column v-if="showRadio" width="55" align="center">
				<template #default="scope">
					<div class="radio-container">
						<el-radio 
							v-model="selectedRow" 
							:label="scope.$index"
							@change="handleRadioChange(scope.row)"
						>&nbsp;</el-radio>
					</div>
				</template>
			</el-table-column>

			<template v-for="column in columns" :key="column.prop">
				<el-table-column 
					v-if="column.prop === 'title'" 
					:label="column.label" 
					:prop="column.prop"
					:width="column.width || ''" 
					:min-width="column.minWidth || ''"
					:align="column.align || 'left'"
					:header-align="'center'"
				>
					<template #default="scope">
						<div class="title-cell">
							<!-- 图标/图片部分 -->
							<div v-if="scope.row.icon || scope.row.type === '音频' || scope.row.type === '文案' || scope.row.type === '文本'" class="item-icon">
								<!-- 视频播放按钮覆盖层 -->
								<div 
									class="video-play-overlay"
									v-if="scope.row.type === '视频'"
									@click.stop="$emit('titleClick', scope.row)"
								>
									<el-icon class="play-icon"><VideoPlay /></el-icon>
								</div>
								
								<!-- 音频播放按钮覆盖层 -->
								<div 
									class="audio-play-overlay"
									v-if="scope.row.type === '音频'"
									@click.stop="$emit('audioClick', scope.row)"
								>
									<el-icon class="play-icon"><Headset /></el-icon>
								</div>
								
								<!-- 添加文本预览按钮覆盖层 -->
								<div 
									class="text-preview-overlay"
									v-if="scope.row.type === '文本'"
									@click.stop="$emit('textClick', scope.row)"
								>
									<el-icon class="preview-icon"><View /></el-icon>
								</div>
								
								<!-- 音频类型默认使用音频图标 -->
								<img 
									v-if="scope.row.type === '音频'" 
									:src="audioIconPath" 
									alt="音频"
									class="audio-icon" 
								/>
								<!-- 文案类型默认使用文案图标 -->
								<img 
									v-else-if="scope.row.type === '文案'" 
									:src="wenanIconPath" 
									alt="文案"
									class="wenan-icon" 
								/>
								<!-- 文本类型显示Element Plus的Document图标 -->
								<el-icon v-else-if="scope.row.type === '文本'" class="text-icon"><Document /></el-icon>
								<!-- 图片类型图标 -->
								<img 
									v-else-if="scope.row.icon && scope.row.icon.type === 'image'" 
									:src="scope.row.icon.src" 
									alt="" 
								/>
								<!-- 颜色块类型图标 -->
								<div 
									v-else-if="scope.row.icon && scope.row.icon.type === 'color'" 
									class="color-icon"
									:style="{ backgroundColor: scope.row.icon.color }"
								>
									{{ scope.row.icon.text }}
								</div>
							</div>
							<!-- 标题文本 -->
							<span class="title-text">
								{{ scope.row.title }}
							</span>
						</div>
					</template>
				</el-table-column>
				
				<el-table-column 
					v-else-if="column.type === 'select'"
					:label="column.label" 
					:prop="column.prop"
					:width="column.width || ''" 
					:min-width="column.minWidth || ''"
					:align="column.align || 'center'"
					:header-align="column.align || 'center'"
				>
					<template #header>
						<div class="filter-header">
							<el-dropdown 
								ref="dropdown"
								trigger="click"
								:teleported="true"
								@command="(value) => handleFilterSelect(column.prop, value)"
							>
								<div class="dropdown-trigger" @click="triggerWorkTypeLoad(column)">
									<span>{{ column.label }}</span>
									<i class="arrow-icon el-icon"><ArrowDown /></i>
								</div>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item 
											v-for="opt in column.options" 
											:key="opt.value" 
											:command="opt.value"
										>
											{{ opt.label }}
										</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
					</template>
				</el-table-column>
				
				<el-table-column 
					v-else
					:label="column.label" 
					:prop="column.prop"
					:width="column.width || ''" 
					:min-width="column.minWidth || ''"
					:align="column.align || 'center'"
					:header-align="column.align || 'center'"
				>
					<!-- 添加字数/时长列的特殊格式化逻辑 -->
					<template #default="scope">
						<template v-if="column.prop === 'duration'">
							<span v-if="scope.row.type === '一键成片'">
								{{ formatVideoTime(scope.row.duration) }}
							</span>
							<span v-else-if="scope.row.type === 'AI配音' || scope.row.type === 'AI商配'">
								{{ scope.row.duration }}字
							</span>
							<span v-else>
								{{ scope.row.duration }}
							</span>
						</template>
						<template v-else>
							{{ scope.row[column.prop] }}
						</template>
					</template>
				</el-table-column>
			</template>

			<el-table-column v-if="showActions" label="操作" width="180" fixed="right">
				<template #default="scope">
					<el-button-group>
						<template v-for="(action, index) in actions" :key="index">
							<!-- 下载按钮特殊处理：根据enableFormatSelection决定是否使用Popover -->
							<template v-if="action.type === 'download'">
								<!-- 启用格式选择的下载按钮 -->
								<el-popover
									v-if="enableFormatSelection"
									placement="right"
									:width="90"
									trigger="hover"
									:disabled="!isAudioDownloadable(scope.row)"
									popper-class="download-format-popover"
								>
									<template #reference>
										<el-button
											type="primary" 
											link
											:disabled="!isDownloadable(scope.row) || isDownloading"
											@click.stop="handleDirectDownload(scope.row)"
										>
											<el-icon>
												<Download />
											</el-icon>
										</el-button>
									</template>
									<template #default>
										<div class="download-format-selector">
											<div class="format-options">
												<div 
													class="format-option"
													@click="handleFormatDownload('mp3', scope.row)"
												>
													<span>MP3格式</span>
												</div>
												<div 
													class="format-option"
													@click="handleFormatDownload('wav', scope.row)"
												>
													<span>WAV格式</span>
												</div>
											</div>
										</div>
									</template>
								</el-popover>
								<!-- 普通下载按钮（我的素材等页面使用） -->
								<el-button 
									v-else
									type="primary" 
									link
									@click="handleAction(action.type, scope.row)"
								>
									<el-icon>
										<component :is="action.icon" />
									</el-icon>
								</el-button>
							</template>
							<!-- 其他操作按钮保持原有逻辑 -->
							<el-button 
								v-else
								type="primary" 
								link
								@click="handleAction(action.type, scope.row)"
							>
								<el-icon>
									<component :is="action.icon" />
								</el-icon>
							</el-button>
						</template>
					</el-button-group>
				</template>
			</el-table-column>
		</el-table>

		<!-- 修改分页容器样式，保持固定高度 -->
		<div class="pagination-container" v-if="showPagination && tableData && tableData.length > 0">
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:page-sizes="pageSizes"
				:layout="paginationLayout"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
		<!-- 添加一个占位元素，当分页器隐藏时保持高度一致 -->
		<div class="pagination-placeholder" v-else-if="showPagination"></div>

		<!-- 添加删除确认对话框 -->
		<DeleteConfirmDialog
			v-model:visible="deleteDialogVisible"
			:item-title="deleteItemTitle"
			@cancel="handleDeleteCancel"
			@confirm="handleDeleteConfirm"
		/>
	</div>
</template>

<script setup>
import { defineProps, defineEmits, ref, nextTick, computed, watch } from 'vue'
import { ArrowDown, VideoPlay, Headset, Document, View, Download } from '@element-plus/icons-vue'
import { ElMessage, ElLoading } from 'element-plus'
// 引入删除确认对话框组件
import DeleteConfirmDialog from './DeleteConfirmDialog.vue'
// 导入音频和文案图标
import audioIcon from '@/assets/img/ms.png'
import wenanIcon from '@/assets/img/wenanIcon.png'
// 导入路由和store
import { useRouter } from 'vue-router'
import { useSoundStore } from '@/stores/modules/soundStore'
// 导入MP3转WAV接口
import { extractWavByMp3 } from '@/api/mySpace'

// 音频图标 - 使用导入的资源
const audioIconPath = ref(audioIcon)
// 文案图标 - 使用导入的资源
const wenanIconPath = ref(wenanIcon)

const props = defineProps({
	tableData: {
		type: Array,
		required: true
	},
	columns: {
		type: Array,
		required: true
	},
	showSelection: {
		type: Boolean,
		default: true
	},
	showRadio: {
		type: Boolean,
		default: false
	},
	showActions: {
		type: Boolean,
		default: true
	},
	actions: {
		type: Array,
		default: () => []
	},
	centered: {
		type: Boolean,
		default: false
	},
	// 分页相关属性
	showPagination: {
		type: Boolean,
		default: true
	},
	total: {
		type: Number,
		default: 0
	},
	defaultPageSize: {
		type: Number,
		default: 10
	},
	defaultCurrentPage: {
		type: Number,
		default: 1
	},
	pageSizes: {
		type: Array,
		default: () => [10, 20, 50, 100]
	},
	paginationLayout: {
		type: String,
		default: 'total, sizes, prev, pager, next, jumper'
	},
	// 添加加载状态属性
	loading: {
		type: Boolean,
		default: false
	},
	loadingText: {
		type: String,
		default: '加载中...'
	},
	emptyText: {
		type: String,
		default: null
	},
	maxHeight: {
		type: [String, Number],
		default: null
	},
	// 添加下载格式选择功能开关
	enableFormatSelection: {
		type: Boolean,
		default: false
	}
})

const emit = defineEmits([
	'action', 
	'filter', 
	'radioChange', 
	'delete', 
	'page-change', 
	'size-change',
	'titleClick',
	'audioClick',
	'textClick',
	'cell-click',
	'row-click',
	'download-success',
	'download-error'
])

const filterValues = ref({})
const filterDropdowns = ref({})
const activeFilter = ref('')
const selectedRow = ref(null)

// 添加删除对话框相关的状态
const deleteDialogVisible = ref(false)
const deleteItemTitle = ref('')
const currentDeleteItem = ref(null)

// 分页相关状态
const currentPage = ref(props.defaultCurrentPage)
const pageSize = ref(props.defaultPageSize)

// 添加下载相关状态
const globalLoadingInstance = ref(null) // 全局loading实例
const isDownloading = ref(false) // 全局下载状态标识

// 添加路由和store实例
const router = useRouter()
const soundStore = useSoundStore()

// 监听props中的defaultCurrentPage和defaultPageSize变化
watch(() => props.defaultCurrentPage, (newVal) => {
	console.log('CustomTable - defaultCurrentPage 变化:', newVal, typeof newVal)
	if (newVal !== undefined && newVal !== null) {
		// 确保是数字类型
		currentPage.value = parseInt(newVal, 10) || 1
		console.log('CustomTable - 更新currentPage为:', currentPage.value)
	}
}, { immediate: true }) // 添加immediate参数确保组件初始化时执行一次

watch(() => props.defaultPageSize, (newVal) => {
	if (newVal !== undefined && newVal !== null) {
		pageSize.value = newVal
	}
})

// 获取用户信息
const getUserInfo = () => {
	try {
		const userStr = localStorage.getItem('user')
		if (userStr) {
			return JSON.parse(userStr)
		}
	} catch (error) {
		console.error('获取用户信息失败:', error)
	}
	return null
}

// 跳转到AI配音页面的通用方法
const jumpToAIDubbing = (row = null) => {
	const userInfo = getUserInfo()
	console.log('jumpToAIDubbing',row)
	if (userInfo) {
		// 判断是否为克隆项目（source === 2）
		if (row && row.source === 2) {
			console.log('检测到克隆项目，使用克隆跳转逻辑', row)
			
			// 构造克隆音色数据，参考克隆列表的数据结构
			const cloneVoiceData = {
				id: row.vedioId || row.voiceId, // 音色ID
				platformNickname: row.voicePerson || row.voiceName || '我的克隆音色', // 音色名称
				avatarUrl: row.vedioPersonImg || row.avatarUrl || '', // 头像
				audioUrl: row.ossAudioUrl || row.audioSrc, // 音频URL
				// 其他可能需要的字段
				voiceName: row.voicePerson || row.voiceName || '我的克隆音色'
			}
			
			// 设置克隆数据到soundStore
			soundStore.setCloneData(cloneVoiceData)
			
			// 跳转到AI配音页面，传递克隆参数
			router.push({
				name: 'AIDubbing',
				query: {
					clone: true,
					voiceId: cloneVoiceData.id
				}
			})
		} else {
			// 普通项目的跳转逻辑（原有逻辑）
			console.log('普通项目跳转AI配音')
			soundStore.setChooseData(userInfo.user || userInfo)
			router.push({ name: 'AIDubbing', query: { choose: true } })
		}
	} else {
		console.error('用户信息不存在，无法跳转到AI配音')
	}
}

// 跳转到商业配音页面的通用方法
const jumpToCommercialDubbing = () => {
	const userInfo = getUserInfo()
	if (userInfo) {
		// 设置soundStore数据
		soundStore.setChooseData(userInfo.user || userInfo)
		// 跳转到商业配音页面，使用路由名称
		router.push({ name: 'commercialDubbing', query: { choose: true } })
	} else {
		console.error('用户信息不存在，无法跳转到商业配音')
	}
}

// 判断是否需要显示格式选择（仅AI配音和AI商配类型且有音频文件）
const isAudioDownloadable = (row) => {
	// 首先检查是否为AI配音或AI商配类型
	if (row.type === 'AI配音' || row.type === 'AI商配') {
		// 然后检查是否有实际的音频链接（包含ossVedioKey字段）
		return !!(row.audioSrc || row.ossAudioUrl || row.ossVedioKey)
	}
	return false
}

// 判断是否可以下载（所有可下载的类型）
const isDownloadable = (row) => {
	// AI配音和AI商配类型（有音频文件）
	if (row.type === 'AI配音' || row.type === 'AI商配') {
		return !!(row.audioSrc || row.ossAudioUrl || row.ossVedioKey)
	}
	// 一键成片类型（有视频文件）
	if (row.type === '一键成片') {
		return !!row.ossVedioKey
	}
	// 其他类型默认可下载
	return true
}

// 处理直接下载（点击下载按钮时的默认行为）
const handleDirectDownload = (row) => {
	console.log('handleDirectDownload 被调用', row)
	
	// 如果启用了格式选择功能且是AI配音/AI商配类型，直接点击不执行下载，只有悬停选择才下载
	if (props.enableFormatSelection && isAudioDownloadable(row)) {
		console.log('AI配音/AI商配类型且格式选择功能已启用，直接点击不执行下载')
		return
	}
	
	// 如果是AI配音/AI商配类型，默认下载MP3格式
	if (isAudioDownloadable(row)) {
		console.log('检测到AI配音/AI商配类型，开始下载MP3格式')
		handleFormatDownload('mp3', row)
	} else {
		console.log('非AI配音类型（如一键成片），调用原有下载逻辑')
		// 特殊处理一键成片的下载
		if (row.type === '一键成片' && row.ossVedioKey) {
			console.log('检测到一键成片类型，直接下载视频文件')
			try {
				const filename = `${row.title || '视频文件'}.mp4`
				downloadFile(row.ossVedioKey, filename)
				ElMessage.success('视频下载开始')
			} catch (error) {
				console.error('一键成片下载失败:', error)
				ElMessage.error('视频下载失败，请重试')
			}
		} else {
			// 其他非AI配音类型，调用原有的下载逻辑
			emit('action', { type: 'download', row })
		}
	}
}

// 处理格式下载选择
const handleFormatDownload = async (format, row) => {
	console.log('handleFormatDownload 被调用', format, row)
	
	// 防止重复点击
	if (isDownloading.value) {
		console.log('已有下载任务在进行中，忽略重复点击')
		return
	}
	
	try {
		// 设置全局下载状态
		isDownloading.value = true
		console.log('开始下载任务:', format, row.title)
		
		let audioUrl = row.audioSrc || row.ossAudioUrl || row.ossVedioKey
		let suffix = 'mp3'
		
		console.log('音频URL:', audioUrl)
		
		if (!audioUrl) {
			ElMessage.warning('没有可下载的音频文件')
			emit('download-error', { format, row, error: '没有可下载的音频文件' })
			return
		}
		
		// 如果选择WAV格式，需要转换
		if (format === 'wav') {
			try {
				const userInfo = getUserInfo()
				const userId = userInfo?.userId || userInfo?.user?.userId || '11'
				
				// 显示全局loading
				globalLoadingInstance.value = ElLoading.service({
					lock: true,
					text: '正在转换为WAV格式，请稍候...',
					background: 'rgba(0, 0, 0, 0.7)'
				})
				
				const data = await extractWavByMp3({ userId, audioUrl })
				if (data.status_code === 200) {
					audioUrl = data.content.result || ''
					console.log('audioUrl',audioUrl)
					suffix = 'wav'
					if (!audioUrl) {
						throw new Error('转换失败，未获得有效的WAV文件URL')
					}
				} else {
					throw new Error(data.msg || '转换失败')
				}
			} catch (error) {
				console.error('WAV转换失败:', error)
				// 关闭全局loading
				if (globalLoadingInstance.value) {
					globalLoadingInstance.value.close()
					globalLoadingInstance.value = null
				}
				ElMessage.error(`WAV转换失败: ${error.message || '请重试'}`)
				emit('download-error', { format, row, error: error.message })
				return
			}
		}
		
		// 执行下载
		await downloadFile(audioUrl, `${row.title || '音频文件'}.${suffix}`)
		
		// 关闭全局loading
		if (globalLoadingInstance.value) {
			globalLoadingInstance.value.close()
			globalLoadingInstance.value = null
		}
		
		ElMessage.success(`${format.toUpperCase()}格式下载完成`)
		emit('download-success', { format, row, filename: `${row.title || '音频文件'}.${suffix}` })
		
	} catch (error) {
		console.error('下载失败:', error)
		// 关闭全局loading
		if (globalLoadingInstance.value) {
			globalLoadingInstance.value.close()
			globalLoadingInstance.value = null
		}
		ElMessage.error(`下载失败: ${error.message || '请重试'}`)
		emit('download-error', { format, row, error: error.message })
	} finally {
		// 清除全局下载状态
		isDownloading.value = false
		console.log('下载任务结束')
	}
}

// 下载文件的通用方法
const downloadFile = async (url, filename) => {
	try {
		const response = await fetch(url)
		if (!response.ok) throw new Error('网络请求失败')
		
		const blob = await response.blob()
		const downloadUrl = URL.createObjectURL(blob)
		
		const link = document.createElement('a')
		link.href = downloadUrl
		link.download = filename.replace(/[\/\\:*?"<>|]/g, '_') // 过滤非法字符
		link.style.display = 'none'
		
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
		
		// 释放URL对象
		setTimeout(() => {
			URL.revokeObjectURL(downloadUrl)
		}, 1000)
	} catch (error) {
		throw new Error(`文件下载失败: ${error.message}`)
	}
}

const handleAction = (type, row) => {
	console.log('handleAction被调用', type, row)
	
	// 如果是跳转到AI配音，特殊处理
	if (type === 'aiDubbing' || type === 'ai-dubbing') {
		jumpToAIDubbing(row) // 传递row参数以便判断source
		return
	}
	
	// 如果是跳转到商业配音，特殊处理  
	if (type === 'commercialDubbing' || type === 'commercial-dubbing') {
		jumpToCommercialDubbing()
		return
	}
	
	// 其他操作直接发送action事件，将处理逻辑交给父组件
	emit('action', { type, row })
}

const handleFilterSelect = (prop, value) => {
	filterValues.value[prop] = value
	console.log('filterValues',filterValues.value)
	emit('filter', { prop, value })
	
	// 特殊处理工作类型列的筛选操作
	// 如果是工作类型列，也同时触发cell-click事件
	if (prop === 'type') {
		console.log('触发工作类型筛选，同时加载工作类型数据')
		emit('cell-click', { column: prop, value })
	}
}

const handleRadioChange = (row) => {
	emit('radioChange', row)
}

// 可以保留这些函数供父组件通过ref调用，但不在内部直接使用
const handleDeleteCancel = () => {
	deleteDialogVisible.value = false
	currentDeleteItem.value = null
}

const handleDeleteConfirm = () => {
	if (currentDeleteItem.value) {
		emit('delete', currentDeleteItem.value)
	}
	currentDeleteItem.value = null
}

// 处理页码变更
const handleCurrentChange = (page) => {
	// 确保page是数字
	const pageNum = parseInt(page, 10) || 1
	console.log('CustomTable - 页码变更:', pageNum, typeof pageNum)
	
	// 创建一个过滤值的副本，以便修改
	const filters = {...filterValues.value} || {}
	console.log('filters',filters)
	// 如果工作类型选择全部的话或者filters.type不存在，type就不需要传
	if (!filters || filters.type === undefined || filters.type === '') {
		// 确保filters存在
		if (filters && 'type' in filters) {
			delete filters.type
		}
	}
	
	// 如果没有任何过滤条件，只传递页码
	if (!filters || Object.keys(filters).length === 0) {
		emit('page-change', pageNum)
	} else {
		emit('page-change', pageNum, filters)
	}
}

// 处理页大小变更
const handleSizeChange = (size) => {
	// 创建一个过滤值的副本，以便修改
	const filters = {...filterValues.value} || {}
	
	// 如果工作类型选择全部的话或者filters.type不存在，type就不需要传
	if (!filters || filters.type === undefined || filters.type === '') {
		// 确保filters存在
		if (filters && 'type' in filters) {
			delete filters.type
		}
	}
	
	// 如果没有任何过滤条件，只传递页大小
	if (!filters || Object.keys(filters).length === 0) {
		emit('size-change', size)
	} else {
		emit('size-change', size, filters)
	}
}

// 修改获取单元格样式类的函数
const getCellClass = ({ column, columnIndex }) => {
	if (column.property === 'title') {
		return 'title-column';
	}
	
	// 单选框列和第一列
	if (columnIndex === 0 || columnIndex === 1) {
		return '';
	}
	
	// 操作列
	if (columnIndex === props.columns.length + (props.showSelection ? 1 : 0) + (props.showRadio ? 1 : 0)) {
		return 'operation-column is-center';
	}
	
	// 为工作类型和素材类型列添加特殊类名
	if (column.property === 'workType' || column.property === 'type' || 
		column.property === 'materialType' || column.property === 'category') {
		return 'is-center clickable-cell type-column';
	}
	
	// 其他所有列都居中
	return 'is-center';
}


const triggerWorkTypeLoad = (column) => {
	console.log('触发工作类型表头点击事件')
	// 避免重复调用API，只在第一次点击或必要时触发
	emit('cell-click', { column: column.prop })
}

// 添加行点击处理函数
const handleRowClick = (row, column, event) => {
	// 防止点击操作列或选择列时触发行点击事件
	if (column.type === 'selection' || column.label === '操作') {
		return
	}
	emit('row-click', row)
}

// 格式化视频时长为 HH:MM:SS 格式
const formatVideoTime = (duration) => {
	// 如果是无效值，直接返回
	if (!duration || duration === '--') return duration
	
	// 如果已经是格式化的字符串（包含冒号），需要解析
	if (typeof duration === 'string' && duration.includes(':')) {
		// 解析格式化的时间字符串
		const parts = duration.split(':')
		let hours = 0, minutes = 0, seconds = 0
		
		if (parts.length === 3) {
			// HH:MM:SS 格式
			hours = parseInt(parts[0], 10)
			minutes = parseInt(parts[1], 10)
			seconds = parseInt(parts[2], 10)
		} else if (parts.length === 2) {
			// MM:SS 格式
			minutes = parseInt(parts[0], 10)
			seconds = parseInt(parts[1], 10)
		}
		
		// 转换为 X小时Y分Z秒 格式
		return formatTimeText(hours, minutes, seconds)
	}
	
	// 尝试将输入转换为数字
	let totalSeconds = 0
	try {
		totalSeconds = Number(duration)
	} catch(e) {
		// 如果无法转换为数字，返回原始值
		return duration
	}
	
	if (isNaN(totalSeconds)) return duration
	
	// 将秒数转换为时分秒
	const hours = Math.floor(totalSeconds / 3600)
	const minutes = Math.floor((totalSeconds % 3600) / 60)
	const seconds = Math.floor(totalSeconds % 60)
	
	// 转换为 X小时Y分Z秒 格式
	return formatTimeText(hours, minutes, seconds)
}

// 辅助函数：将时分秒转换为文字格式
const formatTimeText = (hours, minutes, seconds) => {
	let result = ''
	
	if (hours > 0) {
		result += `${hours}小时`
	}
	
	if (minutes > 0) {
		result += `${minutes}分`
	}
	
	if (seconds > 0 || (hours === 0 && minutes === 0)) {
		result += `${seconds}秒`
	}
	
	return result
}
</script>

<style lang="scss" scoped>
.custom-table-container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	
	&.centered {
		margin: 0 auto;
		max-width: 1200px;
	}

	:deep(.el-table) {
		flex: unset;
		height: auto;
		max-height: unset;
		min-height: unset;
		width: 100% !important;
		overflow: auto;
		--el-table-border-color: #EBEEF5;
		--el-table-header-bg-color: #F5F7FA;

		// 修改单元格样式，确保所有单元格内容垂直居中
		th.el-table__cell,
		td.el-table__cell {
			.cell {
				display: flex;
				align-items: center;
				padding: 0 12px;
				box-sizing: border-box;
				height: 100%;
				line-height: normal;
			}
			
			// 居中样式
			&.is-center {
				text-align: center !important;
				
				.cell {
					justify-content: center !important;
					align-items: center !important;
				}
			}
		}
		
		// 标题行的特殊样式
		th.el-table__cell {
			font-weight: bold;
			color: #20252A;
			height: 50px;
			text-align: center !important; // 确保所有表头居中对齐
			
			.cell {
				justify-content: center !important; // 确保所有表头内容居中
			}
		}
		
		// 表头样式：确保与数据单元格一致的对齐方式
		th.el-table__cell {
			&[class*="title-column"] {
				// 移除左对齐设置，使表头居中
				// text-align: left !important;
				
				.cell {
					// justify-content: flex-start !important;
					// padding-left: 16px;
				}
			}
			
			&[class*="type-column"] {
				text-align: center !important;
				
				.cell {
					justify-content: center !important;
				}
			}
		}
		
		// 确保所有常规单元格居中显示（水平和垂直）
		td.el-table__cell {
			text-align: center !important;
			vertical-align: middle !important;
			.cell {
				justify-content: center !important;
				align-items: center !important;
				padding: 0 8px;
				min-height: 30px;
			}
		}
		
		// 标题列的数据单元格左对齐
		td.el-table__cell.title-column {
			text-align: left !important;
			
			.cell {
				justify-content: flex-start !important;
				padding-left: 16px;
			}
		}
		
		// 优化过滤器表头样式
		.filter-header {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		
		// 下拉触发器样式 - 优化点击区域
		.dropdown-trigger {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			padding: 4px 8px;
			border-radius: 4px;
			font-size: 14px;
			
			&:hover {
				background-color: rgba(0, 0, 0, 0.02);
			}
			
			span {
				margin-right: 4px;
				// 确保文字可点击
				pointer-events: auto; 
			}
			
			.arrow-icon {
				font-size: 12px;
				display: inline-block;
				width: 12px;
				height: 12px;
				line-height: 12px;
				text-align: center;
				transition: transform 0.3s;
				pointer-events: auto;
				vertical-align: middle;
				transform: translateY(-2px);
			}
		}
		
		// 下拉菜单激活状态
		:deep(.el-dropdown.is-active) {
			.arrow-icon {
				transform: rotate(180deg);
			}
		}

		// 修复下拉菜单样式
		:deep(.el-dropdown-menu) {
			padding: 4px 0;
			min-width: 120px;
			border-radius: 4px;
			
			.el-dropdown-menu__item {
				line-height: 34px;
				font-size: 14px;
				padding: 0 16px;
				
				&:hover {
					background-color: #f5f7fa;
					color: #0AAF60;
				}
				
				&.is-active {
					color: #0AAF60;
					background-color: #f0f9eb;
				}
			}
		}
		
		// 自定义单选按钮样式
		.el-radio {
			margin-right: 0;
			
			.el-radio__label {
				padding-left: 0;
				font-size: 0;
			}
			
			// 调整单选按钮的圆圈样式
			.el-radio__inner {
				width: 16px;
				height: 16px;
				border: 1px solid #DCDFE6;
				
				&::after {
					width: 10px;
					height: 10px;
					background-color: #FFFFFF;
				}
			}
			
			// 选中状态样式
			&.is-checked {
				.el-radio__inner {
					background-color: #0AAF60;
					border-color: #0AAF60;
				}
			}
			
			// 悬停状态样式
			&:hover:not(.is-disabled) {
				.el-radio__inner {
					border-color: #0AAF60;
				}
			}
		}
		
		// 自定义标题单元格样式
		.title-cell {
			display: flex;
			align-items: center;
			gap: 12px;
			justify-content: flex-start !important;
			width: 100%;
			
			.item-icon {
				width: 40px;
				height: 40px;
				flex-shrink: 0;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 4px;
				overflow: hidden;
				background-color: transparent !important;
				box-shadow: none !important;
				
				// 音频图标特殊样式
				.audio-icon {
					width: 100% !important;
					height: 100% !important;
					object-fit: contain !important;
					border-radius: 4px;
					background-color: #f5f7fa;
					display: block !important;
				}
				
				// 文案图标样式（与音频图标样式相同）
				.wenan-icon {
					width: 100% !important;
					height: 100% !important;
					object-fit: contain !important;
					border-radius: 4px;
					background-color: #f5f7fa;
					display: block !important;
				}
				
				// 视频播放按钮覆盖层 - 修复对齐问题
				.video-play-overlay {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.4);
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 2px;
					cursor: pointer;
					transition: all 0.2s;
					z-index: 2; // 确保在顶层
					opacity: 0; // 默认隐藏
					
					&:hover {
						background-color: rgba(0, 0, 0, 0.6);
					}
					
					.play-icon {
						color: white;
						font-size: 20px;
						filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
					}
				}
				
				// 音频播放按钮覆盖层
				.audio-play-overlay {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.4);
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 2px;
					cursor: pointer;
					transition: all 0.2s;
					z-index: 2; // 确保在顶层
					opacity: 0; // 默认隐藏
					
					&:hover {
						background-color: rgba(0, 0, 0, 0.6);
					}
					
					.play-icon {
						color: white;
						font-size: 20px;
						filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
					}
				}
				
				// 添加文本预览按钮覆盖层
				.text-preview-overlay {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.4);
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 2px;
					cursor: pointer;
					transition: all 0.2s;
					z-index: 2; // 确保在顶层
					opacity: 0; // 默认隐藏
					
					&:hover {
						background-color: rgba(0, 0, 0, 0.6);
					}
					
					.preview-icon {
						color: white;
						font-size: 20px;
						filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
					}
				}
				
				// 鼠标悬停时显示播放按钮覆盖层
				&:hover {
					.video-play-overlay,
					.audio-play-overlay,
					.text-preview-overlay {
						opacity: 1;
					}
				}
				
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					border-radius: 2px;
					z-index: 1;
				}
				
				.color-icon {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 18px;
					border-radius: 2px;
					z-index: 1;
				}
			}
			
			.title-text {
				font-size: 14px;
				color: #333;
				flex: 1;
				// text-align: left;
				padding-left: 4px;
			}
		}
		
		// 单元格与表头对齐的重要修复
		th.el-table__cell,
		td.el-table__cell {
			// 统一边距处理
			.cell {
				padding-left: 12px;
				padding-right: 12px;
			}
		}
		
		// 操作按钮组样式
		.el-button-group {
			display: inline-flex;
			justify-content: center;
			align-items: center;
			
			// 默认隐藏操作按钮
			.el-button {
				opacity: 0;
				transition: opacity 0.2s;
				padding: 4px 8px;
				
				.el-icon {
					color: #909399;
				}
			}
			
			// "更多"按钮始终显示
			.el-button:last-child {
				opacity: 1;
			}
		}
		
		// 当鼠标悬停在行上时显示所有操作按钮
		.el-table__row:hover .el-button-group .el-button {
			opacity: 1;
		}
		
		// 单选框列需要特殊处理
		th.el-table__cell:nth-child(1),
		td.el-table__cell:nth-child(1) {
			text-align: center !important;
			
			.cell {
				text-align: center !important;
				justify-content: center !important;
				
			}
		}
		
		// 添加可点击单元格的样式
		td.el-table__cell.clickable-cell {
			.cell {
				cursor: pointer;
			}
		}
		
		// 工作类型列的居中对齐
		td.el-table__cell.type-column {
			text-align: center !important;
			
			.cell {
				justify-content: center !important;
			}
		}
	}

	// 确保表格容器结构正确
	:deep(.el-table__inner-wrapper) {
		height: 100%;
		width: 100%;
	}
	
	:deep(.el-table__header-wrapper),
	:deep(.el-table__body-wrapper) {
		width: 100% !important;
	}
	
	:deep(.el-table__header),
	:deep(.el-table__body) {
		width: 100% !important;
	}
	
	// 确保表格内容区域能够横向滚动但隐藏滚动条
	:deep(.el-table__body-wrapper) {
		overflow-x: auto !important;
		
		// 隐藏滚动条但保留滚动功能
		&::-webkit-scrollbar {
			width: 0;
			height: 0;
		}
		
		// 针对Firefox
		scrollbar-width: none;
		
		// 针对IE/Edge
		-ms-overflow-style: none;
	}
	
	// 分页容器样式
	.pagination-container {
		display: flex;
		justify-content: center;
		margin-top: 20px;
		padding: 0 10px;
		height: 32px;
	}

	:deep(.el-pagination) {
		--el-pagination-button-color: #606266;
		--el-pagination-button-bg-color: #ffffff;
		--el-pagination-button-size: 32px;
		--el-pagination-button-margin: 0 5px;
		--el-pagination-button-disabled-color: #c0c4cc;
		--el-pagination-button-disabled-bg-color: #ffffff;
		
		// 主题色
		--el-pagination-hover-color: #0AAF60;
		--el-pagination-color: #0AAF60;
		
		.el-pagination__total,
		.el-pagination__sizes,
		.el-pagination__jump {
			margin-right: 16px;
		}
		
		.el-input__inner {
			height: 28px;
		}
		
		button {
			min-width: 32px;
			height: 32px;
		}
		
		.el-pagination__editor {
			margin: 0 8px;
		}

		// 强化当前页码的高亮样式
		.el-pager li.is-active {
			background-color: #0AAF60 !important;
			color: #ffffff !important;
			font-weight: bold;
		}

		// 确保数字按钮样式正确
		.el-pager li {
			min-width: 32px;
			height: 32px;
			line-height: 32px;
			font-size: 14px;
			margin: 0 3px;
			border-radius: 4px;
			
			&:hover {
				color: #0AAF60;
			}
		}
	}

	// 全局隐藏表格中的所有滚动条
	:deep(.el-table), 
	:deep(.el-table__body-wrapper),
	:deep(.el-table__header-wrapper),
	:deep(.el-table__fixed-right),
	:deep(.el-table__fixed-body-wrapper),
	:deep(.el-table__fixed-header-wrapper) {
		// 隐藏滚动条但保留滚动功能
		&::-webkit-scrollbar {
			width: 0 !important;
			height: 0 !important;
			display: none !important;
		}
		
		// 针对Firefox
		scrollbar-width: none !important;
		
		// 针对IE/Edge
		-ms-overflow-style: none !important;
		
		// 添加overflow属性以确保内容仍可滚动
		overflow-x: auto !important;
		overflow-y: auto !important;
	}
	
	// 确保所有表格区域都能正确隐藏滚动条
	:deep(.el-scrollbar__wrap),
	:deep(.el-scrollbar__view) {
		// 隐藏Element Plus自带的滚动条
		&::-webkit-scrollbar {
			width: 0 !important;
			height: 0 !important;
			display: none !important;
		}
		
		scrollbar-width: none !important;
		-ms-overflow-style: none !important;
	}

	// 分页占位符样式，保持与分页容器相同高度
	.pagination-placeholder {
		height: 52px;
	}

	// 添加单选框容器样式用于居中对齐
	.radio-container {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
	}

	:deep(.el-table__row) {
		height: 50px !important;
		cursor: pointer;
	}
	:deep(.el-table__cell) {
		padding-top: 6px !important;
		padding-bottom: 6px !important;
	}
	:deep(.cell) {
		line-height: 20px !important;
		font-size: 13px !important;
	}
}

.title-text {
	font-size: 14px;
	color: #333;
}

.text-icon {
	font-size: 28px !important;
	color: #909399 !important;
	box-shadow: none !important;
	filter: none !important;
}

// 下载格式选择器Popover样式
:deep(.download-format-popover) {
	padding: 0 !important;
	border: 1px solid #e4e7ed !important;
	border-radius: 6px !important;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
	
	.el-popover__content {
		padding: 0 !important;
		margin: 0 !important;
	}
}

// 下载格式选择器样式
.download-format-selector {
	display: flex;
	flex-direction: column;
	
	.format-options {
		.format-option {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10px 12px;
			cursor: pointer;
			transition: all 0.2s;
			font-size: 14px;
			color: #1D2129;
			
			&:first-child {
				border-radius: 6px 6px 0 0;
			}
			
			&:last-child {
				border-radius: 0 0 6px 6px;
			}
			
			&:only-child {
				border-radius: 6px;
			}
			
			&:not(:last-child) {
				border-bottom: 1px solid #f0f0f0;
			}
			
			&:hover {
				background-color: #f5f7fa;
				color: #0AAF60;
			}
			
			span {
				font-weight: 500;
				white-space: nowrap;
			}
		}
	}
}


</style>