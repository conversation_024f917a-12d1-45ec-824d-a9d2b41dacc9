<template>
	<el-dialog v-model="dialogVisible" title="删除" width="500px" :close-on-click-modal="false" :show-close="false"
		class="delete-confirm-dialog">
		<div class="dialog-content">
			<p class="delete-message">是否删除：《{{ itemTitle }}》？</p>
			<p class="delete-warning" v-if="showWarning">删除{{ itemTypeText }}后，该{{ itemTypeText }}下的内容会被清空，是否继续？</p>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleCancel">取消</el-button>
				<el-button type="primary" @click="handleConfirm" class="delete-btn">删除</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	itemTitle: {
		type: String,
		default: ''
	},
	fileName: {
		type: String,
		default: ''
	},
	itemType: {
		type: String,
		default: 'project',
		validator: (value) => ['project', 'album'].includes(value)
	},
	showWarning: {
		type: Boolean,
		default: true
	}
});

const itemTypeText = computed(() => {
	return props.itemType === 'album' ? '专辑' : '项目';
});

const itemTitle = computed(() => {
	return props.itemTitle || props.fileName || '';
});

const emit = defineEmits(['cancel', 'confirm', 'update:visible']);

const dialogVisible = ref(props.visible);

watch(() => props.visible, (newValue) => {
	dialogVisible.value = newValue;
});

watch(dialogVisible, (newValue) => {
	emit('update:visible', newValue);
});

const handleCancel = () => {
	dialogVisible.value = false;
	emit('cancel');
};

const handleConfirm = () => {
	dialogVisible.value = false;
	emit('confirm');
};
</script>

<style lang="scss" scoped>
.delete-confirm-dialog {
	:deep(.el-dialog__header) {
		padding: 20px;
		margin: 0;
		border-bottom: 1px solid #EBEEF5;
		text-align: center;
	}

	:deep(.el-dialog__title) {
		font-size: 16px;
		font-weight: 600;
		color: #333;
	}

	:deep(.el-dialog__body) {
		padding: 30px 20px;
	}

	.dialog-content {
		display: flex;
		flex-direction: column;
		align-items: center;

		.delete-message {
			font-size: 14px;
			color: #333;
			text-align: center;
			margin: 0 0 10px 0;
		}
		
		.delete-warning {
			font-size: 14px;
			color: #f56c6c;
			text-align: center;
			margin: 0;
		}
	}

	:deep(.el-dialog__footer) {
		padding: 10px 20px 20px;
		border-top: none;
	}
	
	.dialog-footer {
		display: flex;
		justify-content: center;
		gap: 15px;
		
		.el-button {
			min-width: 100px;
		}
		
		.delete-btn {
			background-color: #0AAF60;
			border-color: #0AAF60;
			
			&:hover {
				background-color: #09a058;
				border-color: #09a058;
			}
		}
	}
}
</style>