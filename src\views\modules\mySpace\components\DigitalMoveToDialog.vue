<template>
    <el-dialog v-model="dialogVisible" width="500px" :before-close="handleClose" destroy-on-close
        custom-class="move-dialog">
        <template #header>
            <div class="custom-dialog-header">
                <span>移动到项目</span>
            </div>
        </template>
        <div class="move-dialog-container">
            <div class="move-dialog-content">
                <el-radio-group v-model="selectedProjectId">
                    <div v-for="item in filteredProjectList" :key="item.id" class="project-checkbox">
                        <el-radio :value="item.id">{{ item.name }}</el-radio>
                    </div>
                </el-radio-group>
            </div>
            <div class="dialog-footer">
                <el-button type="primary" :disabled="!selectedProjectId" @click="handleConfirm">确定</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue'
import { useloginStore } from '@/stores/login'
import { ElMessage } from 'element-plus'
import { moveDigitalHumanWorks } from '@/api/mySpace.js'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    selectedItem: {
        type: Object,
        default: () => ({})
    },
    projectList: {
        type: Array,
        default: () => []
    }
})
const emit = defineEmits(['update:visible', 'confirm'])
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}
const dialogVisible = ref(false)
const selectedProjectId = ref(null)

const filteredProjectList = computed(() => {
    return props.projectList.filter(item => item.id != 'all')
})

watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
        selectedProjectId.value = props.selectedItem.albumId
    }
})
watch(dialogVisible, (newVal) => {
    emit('update:visible', newVal)
})

const handleClose = () => {
    dialogVisible.value = false
}
const handleConfirm = async () => {
    if (selectedProjectId.value != props.selectedItem.albumId) {
        const choosedFile = props.projectList.filter(item => item.id == selectedProjectId.value)
        const data = await moveDigitalHumanWorks({ id: props.selectedItem.id, albumId: selectedProjectId.value, userId: getUserId() })
        ElMessage.success(`已移至${choosedFile[0].name}文件夹`)
        emit('confirm')
        dialogVisible.value = false
    } else {
        dialogVisible.value = false
    }
}
</script>

<style lang="scss">
/* 全局样式，没有 scoped */
.move-dialog .el-dialog__header {
    text-align: center !important;
}
</style>

<style lang="scss" scoped>
.custom-dialog-header {
    width: 100%;
    text-align: center;
    padding: 15px 20px;
    box-sizing: border-box;

    span {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
    }
}

:deep(.move-dialog) {
    .el-dialog__body {
        padding: 20px;
    }
}

.move-dialog-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.move-dialog-content {
    border: 1px dashed #C0C4CC;
    border-radius: 2px;
    background-color: #F8FAFC;
    padding: 20px 15px;
    min-height: 250px;

    .el-radio-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .project-checkbox {
            margin-right: 0;
            font-size: 14px;
            text-align: left;

            :deep(.el-radio) {
                width: 100%;
                margin-right: 0;
                display: flex;
                justify-content: flex-start;

                .el-radio__input {
                    .el-radio__inner {
                        border-color: #DCDFE6;

                        &:hover {
                            border-color: #0AAF60;
                        }
                    }

                    &.is-checked {
                        .el-radio__inner {
                            background-color: #0AAF60;
                            border-color: #0AAF60;
                        }
                    }
                }

                .el-radio__label {
                    color: #606266;
                    padding-left: 8px;
                    text-align: left;
                }
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;

    .el-button--primary {
        width: 80px;
        background-color: #0AAF60;
        border-color: #0AAF60;

        &:hover,
        &:focus {
            background-color: #09a058;
            border-color: #09a058;
        }

        &:active {
            background-color: #088f4d;
            border-color: #088f4d;
        }

        &.is-disabled {
            background-color: #a0cfba;
            border-color: #a0cfba;
        }
    }
}
</style>
