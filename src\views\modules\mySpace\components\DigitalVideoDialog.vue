<template>
    <el-dialog v-model="dialogVisible" :title="title" width="1068px" height="797px" :before-close="handleClose"
        :destroy-on-close="true" align-center>
        <div style="text-align: center;">
            <video v-if="screenWidth == '1080'" ref="mediaPlayer" class="videoPlayer" controls :src="mediaSource"
                width="350px" height="620px"></video>
            <video v-if="screenWidth == '1920'" ref="mediaPlayer" class="videoPlayer" controls :src="mediaSource"
                width="100%" height="100%"></video>
        </div>
        <div class="line"></div>
        <div class="content">
            <div class="cue">
                <img src="@/assets/img/notice.png" />
                <span>该内容由AI深度合成，请确认内容真实性</span>
            </div>
            <div class="downVideo" @click="downVideo">下载视频</div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    mediaSource: {
        type: String,
        required: true
    },
    screenWidth: {
        type: String,
        default: ''
    }
});
const emit = defineEmits(['update:visible', 'close', 'down']);

const dialogVisible = ref(props.visible);
const mediaPlayer = ref(null);
const mediaSource = computed(() => props.mediaSource)
const screenWidth = computed(() => props.screenWidth)

watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal;
});
watch(dialogVisible, (newVal) => {
    emit('update:visible', newVal);
});

const handleClose = () => {
    dialogVisible.value = false;
    emit('close');
};
const downVideo = () => {
    emit('down', { url: props.mediaSource, filename: props.title })
}

</script>

<style lang="scss" scoped>
.line {
    width: 1100px;
    border-bottom: 1px solid #DEDEDF;
    position: absolute;
    top: 50px;
    left: -16px;
}

.content {
    display: flex;
    justify-content: space-between;
    margin: 30px 0 0px 0;
    align-items: center;

    .cue {
        display: flex;
        align-items: center;

        span {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            margin-left: 5px;
        }
    }

    .downVideo {
        width: 96px;
        height: 40px;
        border-radius: 4px;
        background: #0AAF60;
        text-align: center;
        line-height: 40px;
        font-size: 14px;
        color: #FFFFFF;
        cursor: pointer;
    }
}
</style>
<style lang="scss">
.el-dialog__title {
    font-size: 16px !important;
    color: #1D2129 !important;
    font-weight: 500;
}

.el-dialog__headerbtn .el-dialog__close {
    color: #8C8C8C;
    font-size: 18px;
}
</style>