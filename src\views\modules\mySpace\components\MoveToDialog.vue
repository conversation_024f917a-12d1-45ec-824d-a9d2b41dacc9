<template>
    <el-dialog v-model="dialogVisible" width="500px" :before-close="handleClose" destroy-on-close
        custom-class="move-dialog">
        <template #header>
            <div class="custom-dialog-header">
                <span>{{ mode === 'project' ? '移动到项目' : '移动到专辑' }}</span>
            </div>
        </template>

        <div class="move-dialog-container">
            <div class="move-dialog-content">
                <el-radio-group v-model="selectedProjectId">
                    <div v-for="item in filteredProjectList" :key="item.id" class="project-checkbox">
                        <el-radio :label="item.id">{{ item.name }}</el-radio>
                    </div>
                </el-radio-group>
            </div>

            <div class="dialog-footer">
                <el-button type="primary" :disabled="!selectedProjectId" @click="handleConfirm">确定</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { changeAlbum } from '@/api/mySpace.js' // 导入 changeAlbum API
import { updateMaterialAlbum } from '@/api/myMaterial.js' // 导入素材移动到专辑的API

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    selectedItem: {
        type: Object,
        default: () => ({})
    },
    projectList: {
        type: Array,
        default: () => []
    },
    mode: {
        type: String,
        default: 'project', // 'project' 或 'album'
        validator: (value) => ['project', 'album'].includes(value)
    }
})

const emit = defineEmits(['update:visible', 'confirm', 'close'])

const dialogVisible = ref(false)
const selectedProjectId = ref(null)

// 过滤掉id为'all'的项目，只显示从接口获取的真实项目
const filteredProjectList = computed(() => {
    return props.projectList.filter(item => item.id !== 'all');
})

// 监听 visible 属性变化
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
    // 重置选择
    if (newVal) {
        selectedProjectId.value = null
        // 如果有当前项目，则默认选择当前项目
        if (props.selectedItem && props.selectedItem.currentProject) {
            const currentProject = props.projectList.find(
                p => p.name === props.selectedItem.currentProject
            )
            if (currentProject) {
                selectedProjectId.value = currentProject.id
            }
        }
    }
})

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
    emit('update:visible', newVal)
})

// 处理关闭对话框
const handleClose = () => {
    dialogVisible.value = false
}

// 修改处理确认的方法
const handleConfirm = async () => {
    if (selectedProjectId.value) {
        try {
            // 获取选中的项目
            const targetProjectId = selectedProjectId.value
            const selectedProject = props.projectList.find(p => p.id === targetProjectId)

            if (selectedProject && props.selectedItem) {
                let response;
                let needRefreshMaterial = false;
                
                if (props.mode === 'album') {
                    // 素材移动到专辑时使用updateMaterialAlbum API
                    const params = {
                        materialId: props.selectedItem.id,  // 素材ID
                        tagId: selectedProject.id          // 目标专辑ID
                    }
                    
                    console.log('发送的移动素材参数:', params)
                    // 调用素材移动API
                    response = await updateMaterialAlbum(params)
                    console.log('移动素材结果:', response)
                    // 标记需要刷新素材列表
                    needRefreshMaterial = true;
                } else {
                    // 作品移动到项目时使用changeAlbum API
                    const params = {
                        id: props.selectedItem.id,     // 作品ID
                        userId: '11',                  // 固定的用户ID，从creatorId改为userId
                        albumId: selectedProject.id    // 目标专辑/项目ID
                    }
                    
                    console.log('发送的移动作品参数:', params)
                    // 调用API
                    response = await changeAlbum(params)
                    console.log('移动作品结果:', response)
                }

                // 成功后关闭对话框
                dialogVisible.value = false

                // 显示成功消息
                // ElMessage.success('移动成功')

                // 发送成功信号给父组件，包含更多详细信息
                emit('confirm', {
                    itemId: props.selectedItem.id,
                    targetProjectId: selectedProjectId.value,
                    success: true,
                    needRefreshMaterial: needRefreshMaterial // 添加刷新素材列表的标志
                })
            }
        } catch (error) {
            console.error('移动失败:', error)
            ElMessage.error('移动失败，请重试')
        }
    }
}
</script>

<style lang="scss">
/* 全局样式，没有 scoped */
.move-dialog .el-dialog__header {
    text-align: center !important;
}
</style>

<style lang="scss" scoped>
.custom-dialog-header {
    width: 100%;
    text-align: center;
    padding: 15px 20px;
    box-sizing: border-box;

    span {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
    }
}

:deep(.move-dialog) {
    .el-dialog__body {
        padding: 20px;
    }
}

.move-dialog-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.move-dialog-content {
    border: 1px dashed #C0C4CC;
    border-radius: 2px;
    background-color: #F8FAFC;
    padding: 20px 15px;
    min-height: 250px;

    .el-radio-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .project-checkbox {
            margin-right: 0;
            font-size: 14px;
            text-align: left;

            :deep(.el-radio) {
                width: 100%;
                margin-right: 0;
                display: flex;
                justify-content: flex-start;

                .el-radio__input {
                    .el-radio__inner {
                        border-color: #DCDFE6;

                        &:hover {
                            border-color: #0AAF60;
                        }
                    }

                    &.is-checked {
                        .el-radio__inner {
                            background-color: #0AAF60;
                            border-color: #0AAF60;
                        }
                    }
                }

                .el-radio__label {
                    color: #606266;
                    padding-left: 8px;
                    text-align: left;
                }
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;

    .el-button--primary {
        width: 80px;
        background-color: #0AAF60;
        border-color: #0AAF60;

        &:hover,
        &:focus {
            background-color: #09a058;
            border-color: #09a058;
        }

        &:active {
            background-color: #088f4d;
            border-color: #088f4d;
        }

        &.is-disabled {
            background-color: #a0cfba;
            border-color: #a0cfba;
        }
    }
}
</style>
