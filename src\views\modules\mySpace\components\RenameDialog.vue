<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="null" 
    width="460px" 
    :show-close="false" 
    :close-on-click-modal="false"
    top="30vh"
    class="rename-dialog"
  >
    <div class="dialog-content">
      <div class="dialog-title">{{ title }}</div>
      <div class="input-wrapper">
        <el-input v-model="inputName" placeholder="请输入名称"></el-input>
      </div>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="handleClose">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="confirmRename">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '修改名称'
  },
  currentName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

const dialogVisible = ref(false);
const inputName = ref('');

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    inputName.value = props.currentName;
  }
});

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};

// 确认重命名
const confirmRename = () => {
  if (inputName.value.trim()) {
    emit('confirm', inputName.value);
    handleClose();
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  background-color: #eef5ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .el-dialog__header {
    display: none;
  }
  
  .el-dialog__body {
    padding: 0;
  }
}

.dialog-content {
  position: relative;
  padding: 24px;
  
  .dialog-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
  }
  
  .input-wrapper {
    margin-bottom: 24px;
    
    :deep(.el-input__wrapper) {
      background-color: #fff;
      border-radius: 4px;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    
    .cancel-btn, .confirm-btn {
      min-width: 80px;
      height: 36px;
      font-size: 14px;
      padding: 0 24px;
      border-radius: 4px;
    }
    
    .cancel-btn {
      --el-button-text-color: #606266;
      --el-button-bg-color: #f5f7fa;
      --el-button-border-color: #dcdfe6;
      --el-button-hover-text-color: #606266;
      --el-button-hover-bg-color: #ebeef5;
      --el-button-hover-border-color: #dcdfe6;
    }
    
    .confirm-btn {
      --el-button-bg-color: #0AAF60;
      --el-button-border-color: #0AAF60;
      --el-button-hover-bg-color: #09a058;
      --el-button-hover-border-color: #09a058;
    }
  }
}
</style> 