<template>
    <el-dialog title="设置" v-model="dialogVisible" width="600px" :before-close="handleClose" destroy-on-close>
        <div class="settings-dialog-container">
            <!-- 标题 -->
            <div class="settings-item">
                <div class="settings-label">标题</div>
                <el-input v-model="formData.title" placeholder="如何命名" class="settings-input" />
            </div>

            <!-- 描述 -->
            <div class="settings-item">
                <div class="settings-label">描述</div>
                <el-input v-model="formData.description" type="textarea" :rows="5" placeholder="添加描述" resize="none"
                    class="settings-textarea" />
            </div>

            <!-- 缩略图设置（仅在素材模式下显示） -->
            <div v-if="isMaterialMode" class="settings-item">
                <div class="settings-label clickable" @click="handleChangeThumbnail">
                    <span>更改缩略图</span>
                </div>
                <div class="thumbnail-container">
                    <div class="thumbnail-preview" @click="handleChangeThumbnail">
                        <img v-if="formData.thumbnail" :src="formData.thumbnail" alt="缩略图预览" />
                        <div v-else class="no-thumbnail">
                            <el-icon>
                                <Picture />
                            </el-icon>
                        </div>
                    </div>
                    <input ref="fileInput" type="file" accept="image/*" style="display: none"
                        @change="onFileSelected" />
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSave" :style="buttonStyle">保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import { InfoFilled, Picture, Upload } from '@element-plus/icons-vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    itemData: {
        type: Object,
        default: () => ({
            id: '',
            title: '',
            description: '',
            thumbnail: ''
        })
    },
    mode: {
        type: String,
        default: 'work', // 'work' 或 'material'
        validator: (value) => ['work', 'material'].includes(value)
    },
    buttonColor: {
        type: String,
        default: ''
    }
});

// 根据buttonColor属性计算按钮样式
const buttonStyle = computed(() => {
    if (props.buttonColor) {
        return {
            backgroundColor: props.buttonColor,
            borderColor: props.buttonColor
        };
    }
    return {};
});

const emit = defineEmits(['update:visible', 'save']);

// 计算是否为素材模式
const isMaterialMode = computed(() => props.mode === 'material');

// 对话框可见状态
const dialogVisible = ref(false);

// 表单数据
const formData = ref({
    id: '',
    title: '',
    description: '',
    thumbnail: ''
});

// 文件选择器引用
const fileInput = ref(null);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
        // 初始化表单数据
        formData.value = {
            id: props.itemData?.id || '',
            title: props.itemData?.title || '',
            description: props.itemData?.description || '',
            thumbnail: props.itemData?.thumbnail || ''
        };
    }
});

// 监听对话框状态变化
watch(() => dialogVisible.value, (newVal) => {
    emit('update:visible', newVal);
});

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false;
};

// 保存设置
const handleSave = () => {
    emit('save', { ...formData.value });
    dialogVisible.value = false;
};

// 触发文件选择器
const handleChangeThumbnail = () => {
    fileInput.value.click();
};

// 处理文件选择
const onFileSelected = (event) => {
    const file = event.target.files[0];
    if (file) {
        // 创建文件预览URL
        const reader = new FileReader();
        reader.onload = (e) => {
            formData.value.thumbnail = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    // 重置文件输入，以便可以选择相同的文件
    event.target.value = null;
};
</script>

<style lang="scss" scoped>
.settings-dialog-container {
    padding: 0 10px;
}

.settings-item {
    margin-bottom: 24px;
}

.settings-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    &.clickable {
        cursor: pointer;

        &:hover {
            color: #0AAF60;

            .thumbnail-info-icon {
                color: #0AAF60;
            }
        }
    }

    .thumbnail-info-icon {
        margin-left: 6px;
        font-size: 14px;
        color: #909399;
    }
}

.settings-input,
.settings-textarea {
    width: 100%;
}

.thumbnail-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.thumbnail-preview {
    width: 150px;
    height: 100px;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: #F5F7FA;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
        border-color: #0AAF60;
    }

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-thumbnail {
        color: #C0C4CC;
        font-size: 24px;
    }
}

.change-thumbnail-btn {
    display: none;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
}
</style>