<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (path) => {
  router.push(path)
}
</script>

<template>
  <div class="side-navigation">
    <div class="nav-item" @click="navigateTo('/mySpace/myWorks')">我的作品</div>
    <div class="nav-item" @click="navigateTo('/mySpace/myMaterials')">我的素材</div>
    <div class="nav-item" @click="navigateTo('/mySpace/myCustomizations')">我的定制</div>
  </div>
</template> 