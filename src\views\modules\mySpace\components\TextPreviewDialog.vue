<template>
	<el-dialog 
		v-model="dialogVisible" 
		width="50%"
		class="text-preview-dialog" 
		:before-close="handleClose"
		:destroy-on-close="true">
		<div class="title-input">
			<input 
				type="text" 
				v-model="localTitle" 
				placeholder="请输入标题"
				@input="updateTitle" 
				class="title-field" />
		</div>
		<div class="text-container">
			<div class="text-content">
				<textarea 
					v-model="localContent" 
					@input="updateContent" 
					placeholder="请输入内容"
					class="content-textarea"></textarea>
			</div>
		</div>
		<div class="dialog-footer">
			<div class="word-count">{{ currentCount }}</div>
			<div class="buttons">
				<el-button class="ai-voice-btn" @click="handleAiVoice">AI配音</el-button>
				<el-button type="primary" class="save-btn" @click="handleSave">保存</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	title: {
		type: String,
		default: ''
	},
	content: {
		type: String,
		default: ''
	},
	maxLength: {
		type: Number,
		default: 2000
	}
});

const emit = defineEmits(['update:visible', 'update:title', 'update:content', 'close', 'save', 'aiVoice']);

const dialogVisible = ref(props.visible);
const localTitle = ref(props.title);
const localContent = ref(props.content);

// 计算当前字数
const currentCount = computed(() => localContent.value.length);

// 恢复visible属性的监听，确保外部控制可以正常工作
watch(() => props.visible, (newVal) => {
	dialogVisible.value = newVal;
	if (newVal) {
		// 当对话框显示时，更新本地状态
		localTitle.value = props.title;
		localContent.value = props.content;
	}
});

// 监听对话框状态变化，通知父组件
watch(dialogVisible, (newVal) => {
	emit('update:visible', newVal);
	if (!newVal) {
		emit('close');
	}
});

// 处理标题更新
const updateTitle = () => {
	emit('update:title', localTitle.value);
};

// 处理内容更新
const updateContent = () => {
	// 如果超出字数限制，截断文本
	if (localContent.value.length > props.maxLength) {
		localContent.value = localContent.value.substring(0, props.maxLength);
	}
	emit('update:content', localContent.value);
};

// 处理关闭对话框
const handleClose = () => {
	dialogVisible.value = false;
};

// 处理保存按钮点击
const handleSave = () => {
	emit('save', { title: localTitle.value, content: localContent.value });
	dialogVisible.value = false;
};

// 处理AI配音按钮点击
const handleAiVoice = () => {
	emit('aiVoice', { title: localTitle.value, content: localContent.value });
};
</script>

<style lang="scss" scoped>
.text-preview-dialog {
	:deep(.el-dialog) {
		display: flex;
		flex-direction: column;
		height: 70vh; 
		margin: 15vh auto 0;
		max-height: 700px;
		background-color: #fff;
		border-radius: 8px;
	}
	
	:deep(.el-dialog__header) {
		display: none; // 隐藏默认的对话框标题区域
	}

	:deep(.el-dialog__body) {
		padding: 0;
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
	
	:deep(.el-dialog__headerbtn) {
		position: absolute;
		top: 16px;
		right: 16px;
		z-index: 10;
	}

	.title-input {
		padding: 20px 20px 0;
		width: 100%;
		border-bottom: 1px solid #eee;
		
		.title-field {
			width: 100%;
			font-size: 18px;
			font-weight: bold;
			border: none;
			outline: none;
			padding: 10px 0;
			margin-bottom: 10px;
			
			&::placeholder {
				color: #999;
			}
		}
	}

	.text-container {
		flex: 1;
		overflow-y: auto;
		padding: 0;
		position: relative;
		
		&::-webkit-scrollbar {
			width: 6px;
		}
		
		&::-webkit-scrollbar-thumb {
			background-color: #ccc;
			border-radius: 3px;
			
			&:hover {
				background-color: #aaa;
			}
		}
		
		&::-webkit-scrollbar-track {
			background-color: #f5f5f5;
		}
		
		scrollbar-width: thin;
		scrollbar-color: #ccc #f5f5f5;
	}

	.text-content {
		position: relative;
		height: 100%;
		width: 100%;
		
		.content-textarea {
			width: 100%;
			height: 100%;
			min-height: 500px;
			border: none;
			outline: none;
			resize: none;
			padding: 20px;
			font-size: 15px;
			line-height: 1.8;
			color: #333;
			background-color: #fff;
			
			&::placeholder {
				color: #999;
			}
		}
	}
	
	.dialog-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15px 20px;
		border-top: 1px solid #eee;
		
		.word-count {
			color: #666;
			font-size: 14px;
		}
		
		.buttons {
			display: flex;
			gap: 10px;
			
			.ai-voice-btn {
				width: 88px;
				height: 32px;
				background-color: #fff;
				border: 1px solid #0AAF60;
				color: #0AAF60;
			}
			
			.save-btn {
				width: 88px;
				height: 32px;
				background-color: #0AAF60;
				border-color: #0AAF60;
				color: #fff;
				
				&:hover {
					background-color: #09a058;
					border-color: #09a058;
				}
			}
		}
	}
}
</style> 