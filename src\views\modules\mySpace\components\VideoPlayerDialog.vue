<template>
	<el-dialog v-model="dialogVisible" :title="mode === 'audio' ? '音频名称' : (title || '媒体预览')" width="70%"
		class="media-player-dialog" :class="{ 'audio-player-dialog': mode === 'audio' }" :before-close="handleClose"
		:destroy-on-close="true">
		<!-- 音频播放器 -->
		<div v-if="mode === 'audio'" class="audio-container">
			<div class="audio-player">
				<div class="audio-info">
					<div class="audio-icon">
						<img v-if="audioIcon" :src="audioIcon" alt="音频封面" class="custom-thumbnail">
						<div v-else class="default-audio-icon" :style="{ backgroundColor: '#36B678' }">
							<img :src="maskIcon" alt="音频" class="audio-mask">
							<span class="audio-text">♪</span>
						</div>
					</div>
					<div class="audio-title">{{ title }}</div>
					<!-- <div class="download-btn" @click="handleDownload">
						<el-icon>
							<Download />
						</el-icon>
						下载
					</div> -->
				</div>
				<div class="audio-controls">
					<div class="time-display">{{ formatTime(currentTime) }}</div>
					<div class="progress-bar" ref="progressBarRef" @click="onSliderClick">
						<!-- 自定义滑动条 -->
						<div class="slider-track">
							<div class="slider-fill" :style="{ width: `${(sliderValue / duration) * 100}%` }"></div>
							<div 
								class="slider-thumb" 
								:style="{ left: `${(sliderValue / duration) * 100}%` }"
								@mousedown="startDrag"
							></div>
						</div>
					</div>
					<div class="time-display">{{ formatTime(duration) }}</div>
				</div>
				<div class="control-buttons">
					<el-button class="control-btn play-btn" @click="togglePlay">
						<el-icon v-if="!isPlaying">
							<VideoPlay />
						</el-icon>
						<el-icon v-else>
							<VideoPause />
						</el-icon>
					</el-button>
				</div>
			</div>
		</div>

		<!-- 视频播放器 -->
		<div v-else class="video-container">
			<video ref="mediaPlayer" class="video-player" autoplay :src="mediaSource"
				@timeupdate="handleTimeUpdate" @loadedmetadata="handleMetadataLoaded" @play="isPlaying = true"
				@pause="isPlaying = false" @ended="handleMediaEnded"></video>
			
			<!-- 视频播放器控制界面 -->
			<div class="video-controls">
				<div class="video-progress">
					<div class="time-display">{{ formatTime(currentTime) }}</div>
					<div class="progress-bar" ref="videoProgressBarRef" @click="onSliderClick">
						<!-- 自定义滑动条 -->
						<div class="slider-track">
							<div class="slider-fill" :style="{ width: `${(sliderValue / duration) * 100}%` }"></div>
							<div 
								class="slider-thumb" 
								:style="{ left: `${(sliderValue / duration) * 100}%` }"
								@mousedown="startDrag"
							></div>
						</div>
					</div>
					<div class="time-display">{{ formatTime(duration) }}</div>
				</div>
				<div class="control-buttons">
					<el-button class="control-btn play-btn" @click="togglePlay">
						<el-icon v-if="!isPlaying">
							<VideoPlay />
						</el-icon>
						<el-icon v-else>
							<VideoPause />
						</el-icon>
					</el-button>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import { Download, DArrowLeft, DArrowRight, VideoPlay, VideoPause, Back, Right } from '@element-plus/icons-vue';
// 直接导入图片
import audioIcon from '@/assets/img/ms.png'

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	title: {
		type: String,
		default: '媒体预览'
	},
	mediaSource: {
		type: String,
		required: true
	},
	mediaThumbnail: {
		type: String,
		default: ''
	},
	mode: {
		type: String,
		default: 'video', // 'video' 或 'audio'
		validator: (value) => ['video', 'audio'].includes(value)
	},
	autoPlay: {
		type: Boolean,
		default: false
	}
});

const emit = defineEmits(['update:visible', 'close', 'download']);

const dialogVisible = ref(props.visible);
const mediaPlayer = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const sliderValue = ref(0);
const audioElement = ref(null);
// 添加拖动状态变量
const isSliding = ref(false);
// 参考获取进度条DOM元素
const progressBarRef = ref(null);
const videoProgressBarRef = ref(null);
// 初始点击位置和滑动进度条宽度
const startClientX = ref(0);
const clientX = ref(0);

// 计算属性获取媒体源
const mediaSource = computed(() => props.mediaSource);

// 计算属性获取默认音频封面
const defaultAudioCover = computed(() => {
	return new URL('@/assets/img/default-audio.png', import.meta.url).href;
});

// 添加计算属性获取默认遮罩图标
const defaultMaskIcon = computed(() => {
	return new URL('@/assets/img/Mask.png', import.meta.url).href;
});

// 监听 mediaThumbnail 属性变化
watch(() => props.mediaThumbnail, (newVal) => {
	console.log('VideoPlayerDialog: mediaThumbnail 已更新:', newVal);
});

// 创建音频元素（仅在音频模式下使用）
onMounted(() => {
	console.log('VideoPlayerDialog: 组件已挂载, mediaThumbnail =', props.mediaThumbnail);
	
	if (props.mode === 'audio') {
		audioElement.value = new Audio(props.mediaSource);
		console.log('音频元素已初始化:', props.mediaSource);
		// 添加事件监听
		audioElement.value.addEventListener('timeupdate', handleTimeUpdate);
		audioElement.value.addEventListener('loadedmetadata', handleMetadataLoaded);
		audioElement.value.addEventListener('play', () => isPlaying.value = true);
		audioElement.value.addEventListener('pause', () => isPlaying.value = false);
		audioElement.value.addEventListener('ended', handleMediaEnded);

		// 加载音频
		audioElement.value.load();
	}
	
	// 监听全局的鼠标事件，用于处理拖动过程和结束
	window.addEventListener('mousemove', onDrag);
	window.addEventListener('mouseup', stopDrag);
});

// 组件销毁前清理音频元素和事件监听
onBeforeUnmount(() => {
	if (audioElement.value) {
		audioElement.value.pause();
		audioElement.value.src = '';
		audioElement.value.removeEventListener('timeupdate', handleTimeUpdate);
		audioElement.value.removeEventListener('loadedmetadata', handleMetadataLoaded);
	}
	
	// 移除全局事件监听
	window.removeEventListener('mousemove', onDrag);
	window.removeEventListener('mouseup', stopDrag);
});

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
	dialogVisible.value = newVal;

	// 当对话框打开时，确保音频元素已初始化
	if (newVal && props.mode === 'audio') {
		// 如果音频元素不存在或音频源已改变，则重新创建音频元素
		if (!audioElement.value || audioElement.value.src !== props.mediaSource) {
			// 移除旧的音频元素（如果存在）
			if (audioElement.value) {
				audioElement.value.pause();
				audioElement.value.removeEventListener('timeupdate', handleTimeUpdate);
				audioElement.value.removeEventListener('loadedmetadata', handleMetadataLoaded);
				audioElement.value.removeEventListener('play', () => isPlaying.value = true);
				audioElement.value.removeEventListener('pause', () => isPlaying.value = false);
				audioElement.value.removeEventListener('ended', handleMediaEnded);
			}
			
			// 创建新的音频元素
			audioElement.value = new Audio(props.mediaSource);
			
			// 添加事件监听
			audioElement.value.addEventListener('timeupdate', handleTimeUpdate);
			audioElement.value.addEventListener('loadedmetadata', handleMetadataLoaded);
			audioElement.value.addEventListener('play', () => isPlaying.value = true);
			audioElement.value.addEventListener('pause', () => isPlaying.value = false);
			audioElement.value.addEventListener('ended', handleMediaEnded);
			
			// 加载音频
			audioElement.value.load();
			
			console.log('音频元素已初始化:', props.mediaSource);
			
			// 如果设置了自动播放，则在音频加载好后自动播放
			if (props.autoPlay) {
				audioElement.value.oncanplay = () => {
					togglePlay();
					audioElement.value.oncanplay = null;
				};
			}
		} else if (props.autoPlay && !isPlaying.value) {
			// 如果音频元素已存在且设置了自动播放，直接播放
			togglePlay();
		}
	}
});

// 监听对话框状态变化
watch(dialogVisible, (newVal) => {
	emit('update:visible', newVal);

	if (!newVal) {
		// 关闭时暂停媒体播放
		if (props.mode === 'video' && mediaPlayer.value) {
			mediaPlayer.value.pause();
		} else if (props.mode === 'audio' && audioElement.value) {
			audioElement.value.pause();
		}
	}
});

// 监听媒体源变化
watch(() => props.mediaSource, (newSource) => {
	if (props.mode === 'audio' && audioElement.value) {
		// 重置音频元素
		audioElement.value.pause();
		audioElement.value.src = newSource;
		audioElement.value.load();
		currentTime.value = 0;
		sliderValue.value = 0;
		isPlaying.value = false;
		console.log('音频源已更新:', newSource);
	}
});

// 格式化时间
const formatTime = (seconds) => {
	if (!seconds || isNaN(seconds)) return '00:00';

	const mins = Math.floor(seconds / 60);
	const secs = Math.floor(seconds % 60);
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 处理进度条点击
const onSliderClick = (event) => {
	if (!duration.value) return;
	
	// 计算点击位置相对于进度条的百分比
	const progressBar = props.mode === 'audio' ? progressBarRef.value : videoProgressBarRef.value;
	if (!progressBar) return;
	
	const rect = progressBar.getBoundingClientRect();
	const offsetX = event.clientX - rect.left;
	const percentage = Math.min(Math.max(offsetX / rect.width, 0), 1);
	
	// 更新播放时间和滑块位置
	updateCurrentTime(percentage * duration.value);
};

// 开始拖动
const startDrag = (event) => {
	if (!duration.value) return;
	
	isSliding.value = true;
	
	const progressBar = props.mode === 'audio' ? progressBarRef.value : videoProgressBarRef.value;
	if (!progressBar) return;
	
	// 记录初始值用于计算拖动距离
	startClientX.value = event.clientX;
	
	// 防止选中文本
	event.preventDefault();
};

// 拖动过程
const onDrag = (event) => {
	if (!isSliding.value || !duration.value) return;
	
	const progressBar = props.mode === 'audio' ? progressBarRef.value : videoProgressBarRef.value;
	if (!progressBar) return;
	
	const rect = progressBar.getBoundingClientRect();
	
	// 计算新的位置百分比
	const offsetX = event.clientX - rect.left;
	const percentage = Math.min(Math.max(offsetX / rect.width, 0), 1);
	
	// 更新时间和滑块位置
	updateCurrentTime(percentage * duration.value);
};

// 停止拖动
const stopDrag = () => {
	if (isSliding.value) {
		isSliding.value = false;
	}
};

// 更新当前时间和滑块位置
const updateCurrentTime = (time) => {
	// 确保时间在有效范围内
	const newTime = Math.min(Math.max(time, 0), duration.value);
	
	// 更新状态
	sliderValue.value = newTime;
	currentTime.value = newTime;
	
	// 设置媒体元素的当前时间
	if (props.mode === 'video' && mediaPlayer.value) {
		mediaPlayer.value.currentTime = newTime;
	} else if (props.mode === 'audio' && audioElement.value) {
		audioElement.value.currentTime = newTime;
	}
};

// 处理时间更新
const handleTimeUpdate = (event) => {
	// 只在非拖动状态下更新滑块位置
	if (!isSliding.value) {
		if (props.mode === 'video') {
			currentTime.value = mediaPlayer.value.currentTime;
			sliderValue.value = currentTime.value;
		} else if (props.mode === 'audio') {
			currentTime.value = audioElement.value.currentTime;
			sliderValue.value = currentTime.value;
		}
	}
};

// 处理元数据加载完成
const handleMetadataLoaded = (event) => {
	if (props.mode === 'video') {
		duration.value = mediaPlayer.value.duration;
	} else if (props.mode === 'audio') {
		duration.value = audioElement.value.duration;
	}
};

// 切换播放/暂停
const togglePlay = () => {
	if (props.mode === 'video' && mediaPlayer.value) {
		if (isPlaying.value) {
			mediaPlayer.value.pause();
		} else {
			mediaPlayer.value.play().catch(err => {
				console.error('视频播放失败:', err);
				isPlaying.value = false;
			});
		}
	} else if (props.mode === 'audio' && audioElement.value) {
		if (isPlaying.value) {
			audioElement.value.pause();
		} else {
			// 检查音频是否已加载
			if (audioElement.value.readyState >= 2) { // HAVE_CURRENT_DATA
				audioElement.value.play().catch(err => {
					console.error('音频播放失败:', err);
					isPlaying.value = false;
				});
			} else {
				// 如果音频未加载，重新设置源并尝试加载
				console.log('正在加载音频...');
				audioElement.value.src = props.mediaSource;
				audioElement.value.load();
				audioElement.value.oncanplay = () => {
					audioElement.value.play().catch(err => {
						console.error('音频播放失败:', err);
						isPlaying.value = false;
					});
					audioElement.value.oncanplay = null;
				};
			}
		}
	}

	// 只有在确实开始播放时才更新状态
	if (!isPlaying.value) {
		isPlaying.value = true;
	} else {
		isPlaying.value = false;
	}
};

// 处理下载按钮
const handleDownload = () => {
	// 只发送下载事件，让父组件决定如何处理下载
	emit('download', {
		url: props.mediaSource,
		title: props.title,
		type: props.mode
	});
};

// 关闭对话框
const handleClose = () => {
	dialogVisible.value = false;
	emit('close');
};

// 媒体播放结束处理
const handleMediaEnded = () => {
	isPlaying.value = false;
	// 可以在这里添加媒体播放结束后的逻辑
};
</script>

<style lang="scss" scoped>
.media-player-dialog {
	:deep(.el-dialog__body) {
		padding: 10px;
	}

	&.audio-player-dialog {
		:deep(.el-dialog__body) {
			padding: 20px;
		}

		:deep(.el-dialog__header) {
			margin-right: 0;
			padding-left: 20px;
			padding-right: 20px;
			border-bottom: 1px solid #EBEEF5;
		}

		:deep(.el-dialog__title) {
			font-weight: bold;
			font-size: 16px;
		}
	}
}

.video-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: #000;
	position: relative;

	.video-player {
		width: 100%;
		max-height: 70vh;
		object-fit: contain;
	}
	
	.video-controls {
		width: 100%;
		padding: 10px 20px;
		background-color: rgba(0, 0, 0, 0.7);
		position: absolute;
		bottom: 0;
		left: 0;
		z-index: 10;
		
		.video-progress {
			display: flex;
			align-items: center;
			position: relative;
			margin-bottom: 10px;
			
			.time-display {
				font-size: 14px;
				color: #ffffff;
				width: 40px;
				text-align: center;
			}
			
			.progress-bar {
				flex: 1;
				margin: 0 10px;
				position: relative;
				cursor: pointer;
				
				/* 自定义滑动条样式 */
				.slider-track {
					position: relative;
					height: 6px;
					background-color: rgba(255, 255, 255, 0.3);
					border-radius: 3px;
					
					.slider-fill {
						position: absolute;
						height: 100%;
						background-color: #0AAF60;
						border-radius: 3px;
						left: 0;
						top: 0;
					}
					
					.slider-thumb {
						position: absolute;
						width: 16px;
						height: 16px;
						background-color: white;
						border-radius: 50%;
						border: 2px solid #0AAF60;
						top: 50%;
						transform: translate(-50%, -50%);
						cursor: pointer;
						z-index: 15; /* 确保滑块在最上层 */
						box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
						
						&:hover {
							transform: translate(-50%, -50%) scale(1.1);
						}
					}
				}
			}
		}
		
		.control-buttons {
			display: flex;
			justify-content: center;
			gap: 8px;
			
			.control-btn {
				--el-button-bg-color: #0AAF60;
				--el-button-border-color: #0AAF60;
				--el-button-text-color: #FFFFFF;
				--el-button-hover-bg-color: #0AAF60;
				--el-button-hover-border-color: #0AAF60;
				
				width: 40px;
				height: 40px;
				padding: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 50%;
				transition: none !important;
				
				&:hover, &:focus, &:active {
					background-color: #0AAF60 !important;
					border-color: #0AAF60 !important;
					color: #FFFFFF !important;
					opacity: 1 !important;
					box-shadow: none !important;
					transform: none !important;
				}
				
				.el-icon {
					font-size: 14px;
				}
				
				&.play-btn {
					width: 40px;
					height: 40px;
					
					.el-icon {
						font-size: 28px;
					}
				}
			}
		}
	}
}

.audio-container {
	width: 100%;
	padding: 0 20px;

	.audio-player {
		display: flex;
		flex-direction: column;
		width: 100%;

		.audio-info {
			display: flex;
			align-items: center;
			margin-bottom: 20px;

			.audio-icon {
				width: 80px;
				height: 80px;
				border-radius: 50%;
				overflow: hidden;
				margin-right: 20px;
				flex-shrink: 0;
				
				.custom-thumbnail {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
				
				.default-audio-icon {
					width: 100%;
					height: 100%;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					
					.audio-mask {
						position: absolute;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
					
					.audio-text {
						position: relative;
						z-index: 1;
						color: white;
						font-size: 24px;
					}
				}
			}

			.audio-title {
				flex: 1;
				font-size: 16px;
				font-weight: 500;
				color: #333;
			}

			.download-btn {
				display: flex;
				align-items: center;
				color: #0AAF60;
				cursor: pointer;
				transition: opacity 0.2s;

				&:hover {
					opacity: 0.8;
				}

				.el-icon {
					margin-right: 4px;
				}
			}
		}

		.audio-controls {
			display: flex;
			align-items: center;
			position: relative;

			.time-display {
				font-size: 14px;
				color: #606266;
				width: 40px;
				text-align: center;
			}

			.progress-bar {
				flex: 1;
				margin: 0 10px;
				position: relative;
				cursor: pointer;

				/* 自定义滑动条样式 */
				.slider-track {
					position: relative;
					height: 6px;
					background-color: #E4E7ED;
					border-radius: 3px;
					
					.slider-fill {
						position: absolute;
						height: 100%;
						background-color: #0AAF60;
						border-radius: 3px;
						left: 0;
						top: 0;
					}
					
					.slider-thumb {
						position: absolute;
						width: 16px;
						height: 16px;
						background-color: white;
						border-radius: 50%;
						border: 2px solid #0AAF60;
						top: 50%;
						transform: translate(-50%, -50%);
						cursor: pointer;
						z-index: 5; /* 确保滑块在最上层 */
						box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
						
						&:hover {
							transform: translate(-50%, -50%) scale(1.1);
						}
					}
				}
			}
		}

		.control-buttons {
			display: flex;
			justify-content: center;
			gap: 8px;

			.control-btn {
				--el-button-bg-color: #0AAF60;
				--el-button-border-color: #0AAF60;
				--el-button-text-color: #FFFFFF;
				--el-button-hover-bg-color: #0AAF60;
				--el-button-hover-border-color: #0AAF60;
				
				width: 40px;
				height: 40px;
				padding: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 50%;
				transition: none !important;
				
				&:hover, &:focus, &:active {
					background-color: #0AAF60 !important;
					border-color: #0AAF60 !important;
					color: #FFFFFF !important;
					opacity: 1 !important;
					box-shadow: none !important;
					transform: none !important;
				}

				.el-icon {
					font-size: 14px;
				}

				&.play-btn {
					width: 40px;
					height: 40px;

					.el-icon {
						font-size: 28px;
					}
				}
			}
		}
	}
}
</style>