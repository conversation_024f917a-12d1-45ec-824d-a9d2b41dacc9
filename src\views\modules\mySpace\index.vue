<template>
  <div class="my-space-container">
    <!-- 顶部导航 -->
    <div class="nav-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="我的作品" name="works"></el-tab-pane>
        <el-tab-pane label="我的素材" name="materials"></el-tab-pane>
        <el-tab-pane label="我的定制" name="customizations"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 路由视图 -->
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const activeTab = ref('')

// 监听路由变化更新标签页
watch(() => route.path, (newPath) => {
  if (newPath.includes('/myWorks')) {
    activeTab.value = 'works'
  } else if (newPath.includes('/myMaterials')) {
    activeTab.value = 'materials'
  } else if (newPath.includes('/myCustomizations')) {
    activeTab.value = 'customizations'
  }
}, { immediate: true })

// 切换标签页时更新路由
const handleTabChange = (tab) => {
  if (tab === 'works') {
    router.push('/mySpace/myWorks')
  } else if (tab === 'materials') {
    router.push('/mySpace/myMaterials')
  } else if (tab === 'customizations') {
    router.push('/mySpace/myCustomizations')
  }
}
</script>

<style lang="scss" scoped>
.my-space-container {
  padding: 20px;
  background-color: #fff;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .nav-tabs {
    margin-bottom: 20px;
    padding: 0 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        height: 0;
      }
    }

    :deep(.el-tabs__nav) {
      border: none;
    }

    :deep(.el-tabs__item) {
      font-size: 18px;
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      color: #333;

      &.is-active {
        font-weight: 500;
        color: #078f4d;
      }

      &:hover {
        color: #078f4d;
      }
    }

    :deep(.el-tabs__active-bar) {
      background-color: #078f4d;
    }
  }
}
</style> 