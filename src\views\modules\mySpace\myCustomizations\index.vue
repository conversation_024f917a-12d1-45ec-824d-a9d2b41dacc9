<template>
	<div class="my-customizations-container">
		<div class="content">
			<!-- 暂无数据展示 -->
			<div class="empty-state">
				<div class="empty-icon">
					<i class="iconfont icon-empty-box"></i>
				</div>
				<h2 class="empty-title">暂无定制内容</h2>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'

// 获取 router 实例
const router = useRouter()

// activeTab 默认值设为 'custom'
const activeTab = ref('custom')

// 添加 watch 监听 activeTab 变化
watch(activeTab, (newValue) => {
	if (newValue === 'works') {
		router.push('/myWorks')
	} else if (newValue === 'materials') {
		router.push('/myMaterials')
	} else if (newValue === 'custom') {
		router.push('/myCustomizations')
	}
})

</script>

<style lang="scss" scoped>
.my-customizations-container {
	padding: 20px;
	min-height: calc(100vh - 150px);

	h1 {
		font-size: 24px;
		color: #333;
		margin-bottom: 20px;
	}

	.content {
		margin-top: 20px;
		background-color: #fff;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		min-height: 500px;
		display: flex;
		justify-content: center;
		align-items: center;

		.empty-state {
			text-align: center;
			padding: 40px 20px;
			max-width: 500px;

			.empty-icon {
				font-size: 80px;
				color: #e0e0e0;
				margin-bottom: 20px;

				i {
					font-size: inherit;
				}
			}

			.empty-title {
				font-size: 22px;
				color: #333;
				margin-bottom: 15px;
				font-weight: 500;
			}

			.empty-desc {
				font-size: 16px;
				color: #909399;
				margin-bottom: 30px;
				line-height: 1.6;
			}

			.create-btn {
				padding: 12px 30px;
				font-size: 16px;
				border-radius: 4px;
				transition: all 0.3s;

				&:hover {
					transform: translateY(-2px);
					box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
				}
			}
		}
	}
}
</style>