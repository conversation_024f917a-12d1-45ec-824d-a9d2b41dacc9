<template>
	<div class="my-materials-container">
		<!-- 专辑区域 -->
		<div class="albums-section">
			<div class="section-header">
				<span class="section-title">专辑</span> 
				<el-button type="primary" class="add-btn" plain @click="showCreateAlbumDialog">
					<el-icon>
						<Plus />
					</el-icon>新建专辑
				</el-button>
			</div>
			<div class="album-cards">
				<el-card v-for="album in albums"  :key="album.id" class="album-card"
					:class="{ 'active-album': currentAlbumId === album.id }" 
					@click="handleAlbumClick(album)"
					style="transition: all 0.2s ease;">
					<div class="album-icon">
						<el-icon>
							<Folder />
						</el-icon>
					</div>
					<div class="album-info">
						<div class="album-name">{{ album.name }}</div>
						<div class="album-date">{{ album.updateTime }} 更新</div>
					</div>
					<el-dropdown v-if="album.name && album.name !== '默认专辑'" @command="command => handleAlbumAction(command, album)" @click.stop>
						<img src="@/assets/img/三个点.png" class="more-icon" alt="更多" />
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="rename">
									<el-icon>
										<EditPen />
									</el-icon>重命名
								</el-dropdown-item>
								<el-dropdown-item command="delete">
									<el-icon>
										<Delete />
									</el-icon>删除
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</el-card>
			</div>
		</div>

		<!-- 素材区域 -->
		<div class="materials-section">
			<div class="section-header">
				<span class="section-title">素材</span>

				<el-button type="primary" plain @click="handleUploadClick">
					<el-icon>
						<Plus />
					</el-icon>去上传
				</el-button>
				<span v-if="currentAlbumName" class="current-item-label">(当前素材：{{ currentAlbumName }})</span>
				<el-checkbox class="view-all-checkbox" v-model="showAllMaterials" @change="handleShowAllMaterialsChange">查看全部</el-checkbox>
			</div>
			<CustomTable :tableData="materialsList" :columns="materialsColumns" :actions="tableActions"
				:showSelection="false" :showRadio="true" @action="handleTableAction" @radioChange="handleRadioChange"
				@titleClick="handleTitleClick" @audioClick="handleAudioClick" @textClick="handleTextClick" :total="totalItems"
				:default-current-page="currentPage"
				@page-change="handlePageChange" @size-change="handleSizeChange" @filter="handleTypeFilter" />
		</div>

		<!-- 创建专辑对话框 -->
		<CreateProjectDialog v-model:visible="createDialogVisible" title="新建专辑" @created="handleAlbumCreated" />

		<!-- 删除确认对话框 -->
		<DeleteConfirmDialog v-model:visible="deleteDialogVisible"
			:file-name="currentDeleteItem.name || currentDeleteItem.title" 
			:file-type="deleteFileType"
			:item-type="deleteFileType === 'album' ? 'album' : 'album'"
			:show-warning="deleteFileType === 'album'"
			@confirm="handleDeleteConfirm" />

		<!-- 重命名对话框 -->
		<RenameDialog v-model:visible="renameDialogVisible" title="修改专辑名称"
			:current-name="currentRenameItem.name || currentRenameItem.title" @confirm="handleRenameConfirm" />

		<!-- 设置对话框 -->
		<SettingsDialog v-model:visible="settingsDialogVisible" :itemData="currentSettingsItem" mode="material"
			@save="handleSettingsSave" />

		<!-- 移动到专辑对话框 -->
		<MoveToDialog v-model:visible="moveToAlbumDialogVisible" :selected-item="currentMoveItem" :project-list="albums"
			mode="album" @confirm="handleMoveConfirm" />

		<!-- 通用媒体播放对话框 -->
		<VideoPlayerDialog v-model:visible="mediaPlayerDialogVisible" :title="currentMediaTitle"
			:mediaSource="currentMediaSrc" :mediaThumbnail="currentMediaThumbnail" :mode="mediaPlayerMode"
			:autoPlay="true"
			@close="handleMediaDialogClose" @download="handleMediaDownload" />

		<!-- 文本预览对话框 -->
		<TextPreviewDialog 
			v-model:visible="textPreviewDialogVisible" 
			v-model:title="currentTextTitle"
			v-model:content="currentTextContent"
			@close="handleTextDialogClose"
			@save="handleTextSave"
			@aiVoice="handleAiVoice" />

		<!-- 上传进度对话框 -->
		<el-dialog v-model="isUploading" title="文件上传中" width="30%" :close-on-click-modal="false" :show-close="false">
			<div class="upload-progress">
				<div class="file-info">
					<span>{{ uploadFile.name }}</span>&nbsp;&nbsp;&nbsp;
					<span>{{ Math.floor(uploadFile.loaded / 1024) }} KB / {{ Math.floor(uploadFile.size / 1024) }}
						KB</span>
				</div>
				<el-progress :percentage="uploadFile.percent" :format="percentFormat" color="#0AAF60" />
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelUpload">取消上传</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 上传权限对话框 -->
		<AlertDialog v-model:visible="uploadPermissionDialogVisible" title="上传权限检查"
			:message="uploadPermissionMessage" @confirm="handleUploadPermissionConfirm" />

		<!-- 空间不足对话框 -->
		<AlertDialog v-model:visible="insufficientSpaceDialogVisible" type="warning" 
			:title="insufficientSpaceTitle" :sub-title="insufficientSpaceSubTitle" :message="insufficientSpaceMessage" show-cancel-button
			cancel-button-text="暂不购买" confirm-button-text="立即购买"
			:show-fee-explanation="false" :custom-confirm-class="true"
			@confirm="handleBuySpace" @cancel="handleCancelBuy" />
	</div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Folder, MoreFilled, Setting, Download, Delete, More, EditPen } from '@element-plus/icons-vue'
import { getMyMaterialList, updateAlbumName, deleteMaterial, getUserAlbum, deleteMaterialList, createAlbum, updateMaterialInfo } from '@/api/myMaterial'
import { dubbing, callbackOss } from '@/api/dubbing'
import { checkUploadPermission } from '@/api/upload' // 导入空间检查接口
import CustomTable from '../components/CustomTable.vue'
import CreateProjectDialog from '../components/CreateProjectDialog.vue'
import DeleteConfirmDialog from '../components/DeleteConfirmDialog.vue'
import RenameDialog from '../components/RenameDialog.vue'
import SettingsDialog from '../components/SettingsDialog.vue'
import MoveToDialog from '../components/MoveToDialog.vue'
import VideoPlayerDialog from '../components/VideoPlayerDialog.vue'
import TextPreviewDialog from '../components/TextPreviewDialog.vue'
import AlertDialog from '@/views/components/AlertDialog.vue' // 导入AlertDialog组件
import { ElMessage } from 'element-plus'
import { useloginStore } from '@/stores/login'
import { useAIDubbingStore } from '@/stores/modules/AIDubbing.js'
const router = useRouter()
const loginStore = useloginStore() // 获取 loginStore 实例
let useAIDubbing = useAIDubbingStore()
// 添加 getUserId 函数，与其他组件保持一致
const getUserId = () => {
	return loginStore.userId || ''
}

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)

const activeTab = ref('materials')
const createDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const currentDeleteItem = ref({})
const deleteFileType = ref('album')
const renameDialogVisible = ref(false)
const currentRenameItem = ref({})
const settingsDialogVisible = ref(false)
const currentSettingsItem = ref({})
const moveToAlbumDialogVisible = ref(false)
const currentMoveItem = ref(null)
const mediaPlayerDialogVisible = ref(false)
const currentMediaSrc = ref('')
const currentMediaTitle = ref('')
const currentMediaThumbnail = ref('')
const mediaPlayerMode = ref('video')
const currentAlbumId = ref(null)
const currentAlbumName = ref('全部素材')
const currentMaterialType = ref('')
const isUploading = ref(false)
const uploadFile = ref({
	name: '',
	size: 0,
	loaded: 0,
	percent: 0,
	type: ''
})
const uploadRequest = ref(null)
const fileInputRef = ref(null)

// 文本预览相关状态
const textPreviewDialogVisible = ref(false)
const currentTextTitle = ref('')
const currentTextContent = ref('')
const currentTextMaterialId = ref('')

// 添加音频图标常量声明
import audioIcon from '@/assets/img/ms.png';
const audioIconPath = audioIcon;

// 示例数据
const albums = ref([
	// { id: 1, name: '专辑名称', updateTime: '2025.02.16' },
	// { id: 2, name: '默认专辑', updateTime: '2025.01.18' }
])

const materialsList = ref([
	// 这里原来的示例数据将被实际API数据替换
])

// 添加处理文本点击的函数
const handleTextClick = (row) => {
	console.log('Text clicked:', row)
	if (row.type === '文本' && row.textContent) {
		currentTextTitle.value = row.title || '文本预览'
		currentTextContent.value = row.textContent
		currentTextMaterialId.value = row.id
		textPreviewDialogVisible.value = true
	} else if (row.type === '文本' && row.description) {
		// 备选：如果textContent不存在但description存在，使用description
		currentTextTitle.value = row.title || '文本预览'
		currentTextContent.value = row.description
		currentTextMaterialId.value = row.id
		textPreviewDialogVisible.value = true
	} else {
		ElMessage.warning('无法预览，文本内容为空')
	}
}

// 处理文本对话框关闭
const handleTextDialogClose = () => {
	textPreviewDialogVisible.value = false
}

const materialsColumns = ref([
	{ label: '标题', prop: 'title', align: 'center' },
	{
		label: '素材类型',
		prop: 'type',
		align: 'center',
		type: 'select',
		options: [
			{ label: '全部', value: '' },
			{ label: '音频', value: 'audio' },
			{ label: '视频', value: 'video' },
			{ label: '文本', value: 'text' },
			{ label: '图片', value: 'background' }
		]
	},
	{ label: '字数/时长', prop: 'duration', align: 'center' },
	{ label: '所属专辑', prop: 'album', align: 'center' },
	// { label: '发音人', prop: 'speaker', align: 'center' },
	// { label: '最近打开', prop: 'lastOpen', align: 'center' },
	{ label: '大小', prop: 'size', align: 'center' }
])

const tableActions = ref([
	{ type: 'setting', icon: markRaw(Setting) },
	{ type: 'add', icon: markRaw(Plus) },
	{ type: 'download', icon: markRaw(Download) },
	{ type: 'delete', icon: markRaw(Delete) },
	{ type: 'more', icon: markRaw(More) }
])

const handleTableAction = ({ type, row }) => {
	console.log('Table action:', type, row)

	if (type === 'setting') {
		// 打开设置对话框
		currentSettingsItem.value = {
			id: row.id, // 确保ID也被传递
			title: row.title,
			description: row.description || '', // 已经是UI层面的description字段，不需要修改
			thumbnail: (row.icon && row.icon.type === 'image') ? row.icon.src : ''
		}
		console.log('打开设置对话框，传递数据:', currentSettingsItem.value);
		settingsDialogVisible.value = true
	} else if (type === 'delete') {
		// 处理删除操作
		showDeleteDialog(row, 'material')
	} else if (type === 'add') {
		// 处理移动操作 - 打开移动到专辑对话框
		currentMoveItem.value = {
			id: row.id,
			title: row.title,
			currentProject: row.album || '无'
		}
		moveToAlbumDialogVisible.value = true
	} else if (type === 'download') {
		// 处理下载操作
		if (row.type === '音频' && row.audioSrc) {
			// 音频下载
			handleMediaDownload({
				type: 'audio',
				title: row.title || '音频文件',
				url: row.audioSrc
			})
		} else if (row.type === '视频' && row.videoSrc) {
			// 视频下载
			handleMediaDownload({
				type: 'video',
				title: row.title || '视频文件',
				url: row.videoSrc
			})
		} else if (row.type === '文本') {
			// 文本下载
			if (row.textContent) {
				handleMediaDownload({
					type: 'text',
					title: row.title || '文本文件',
					content: row.textContent
				})
			} else if (row.description) {
				// 备选：如果textContent不存在但description存在，使用description
				handleMediaDownload({
					type: 'text',
					title: row.title || '文本文件',
					content: row.description
				})
			} else {
				ElMessage.warning('没有可下载的文本内容')
			}
		} else {
			ElMessage.warning('没有可下载的媒体文件')
		}
	} else if (type === 'more') {
		// 处理更多操作
		// ...
	} else if (type === 'preview') {
		// 处理视频预览操作
		if (row.type === '视频' && row.videoSrc) {
			openMediaPlayer(row, 'video')
		}
	}
}

const handleRadioChange = (row) => {
	console.log('Selected material:', row)
	// 如果选中的是视频，可以显示预览按钮或其他选项
}

// 显示创建专辑对话框
const showCreateAlbumDialog = () => {
	createDialogVisible.value = true
}

// 处理专辑创建成功
const handleAlbumCreated = async (newAlbum) => {
	try {
		// 调用创建专辑API
		const response = await createAlbum({
			userId: getUserId(),
			tagName: newAlbum.name || ""
		});

		// ElMessage.success('专辑创建成功');
		// 刷新专辑列表
		fetchAlbums();
	} catch (error) {
		console.error('创建专辑失败:', error);
		ElMessage.error('创建专辑失败，请重试');
	}
}

// 显示删除确认对话框
const showDeleteDialog = (item, type) => {
	currentDeleteItem.value = item
	deleteFileType.value = type // 'album' 或 'material'
	deleteDialogVisible.value = true
}

// 显示重命名对话框
const showRenameDialog = (item) => {
	currentRenameItem.value = item
	renameDialogVisible.value = true
}

// 处理专辑下拉菜单点击事件
const handleAlbumAction = (action, album) => {
	if (action === 'delete') {
		showDeleteDialog(album, 'album')
	} else if (action === 'rename') {
		showRenameDialog(album)
	}
}

// 确认删除的处理方法
const handleDeleteConfirm = async () => {
	console.log('删除专辑', deleteFileType.value)
	try {
		if (deleteFileType.value === 'album') {
			// 删除专辑逻辑
			const tagId = currentDeleteItem.value
			if (!tagId || !tagId.id) {
				ElMessage.warning('专辑信息不完整，无法删除')
				return
			}

			// 调用删除专辑API
			const response = await deleteMaterial({
				userId: getUserId(),
				tagId: tagId.id
			})

			ElMessage.success('专辑删除成功')
			// 从专辑列表中移除
			const index = albums.value.findIndex(a => a.id === tagId.id)
			if (index !== -1) {
				albums.value.splice(index, 1)
			}
			// 如果当前选中的是被删除的专辑，重新加载所有素材
			if (currentAlbumId.value === tagId.id) {
				currentAlbumId.value = 'all'
				currentAlbumName.value = '全部素材'
				loadMaterialsList()
			}
		} else if (deleteFileType.value === 'material') {
			// 删除素材逻辑
			const material = currentDeleteItem.value
			if (!material || !material.id) {
				ElMessage.warning('素材信息不完整，无法删除')
				return
			}

			// 调用删除素材API
			const response = await deleteMaterialList({
				userId: getUserId(),
				materialId: material.id
			})

			if (response) {
				ElMessage.success('素材删除成功')
				// 从素材列表中移除
				const index = materialsList.value.findIndex(m => m.id === material.id)
				if (index !== -1) {
					materialsList.value.splice(index, 1)
				}
                
                // 重新加载素材列表，确保数据同步
                // 根据当前选中的专辑决定加载方式
                if (currentAlbumId.value) {
                    loadMaterialsListByAlbum(currentAlbumId.value)
                } else {
                    loadMaterialsList()
                }
			} else {
				ElMessage.error(response?.message || '素材删除失败')
			}
		}
	} catch (error) {
		console.error('删除操作失败:', error)
		ElMessage.error('删除操作失败')
	} finally {
		// 关闭确认对话框
		deleteDialogVisible.value = false
		currentDeleteItem.value = {}
	}
}

// 添加重命名确认处理函数
const handleRenameConfirm = async (newName) => {
	try {
		const item = currentRenameItem.value
		if (item.id) {
			// 调用重命名专辑API
			const response = await updateAlbumName({
				userId: getUserId(),
				tagId: item.id,
				tagName: newName
			})

			ElMessage.success('专辑重命名成功')
		}
	} catch (error) {
		console.error('重命名专辑失败:', error)
		ElMessage.error('重命名专辑失败')
	} finally {
		// 关闭重命名对话框
		renameDialogVisible.value = false
		currentRenameItem.value = {}
	}
}

// 处理设置保存
const handleSettingsSave = async (updatedData) => {
	try {
		// 更新素材数据
		const index = materialsList.value.findIndex(item => item.title === currentSettingsItem.value.title)
		if (index !== -1) {
			const material = materialsList.value[index]
			
			// 调用API更新素材信息，修改参数格式为id、comment、title
			const response = await updateMaterialInfo({
				materialId: material.id,
				comment: updatedData.description,
				materialName: updatedData.title
			})
			
			// 更新本地数据
			materialsList.value[index].title = updatedData.title
			materialsList.value[index].description = updatedData.description

			// 更新缩略图（如果有的话）
			if (updatedData.thumbnail) {
				materialsList.value[index].icon = {
					type: 'image',
					src: updatedData.thumbnail
				}
			}
			
			ElMessage.success('素材信息已更新')
		} else {
			ElMessage.warning('未找到要更新的素材')
		}
	} catch (error) {
		console.error('更新素材信息失败:', error)
		ElMessage.error('更新素材信息失败')
	} finally {
		// 关闭设置对话框
		settingsDialogVisible.value = false
	}
}

// 处理专辑点击事件
const handleAlbumClick = (album) => {
	console.log('Album clicked:', album, 'Current ID:', currentAlbumId.value)

	// 取消查看全部单选
	showAllMaterials.value = false;
	// 更新选中专辑ID
	currentAlbumId.value = album.id
	// 更新当前专辑名称
	currentAlbumName.value = album.name
	
	// 加载带筛选条件的素材列表
	loadFilteredMaterials()
}

// 处理页码变更事件
const handlePageChange = (page) => {
	console.log('页码变更:', page, typeof page)
	// 确保页码是数字类型
	currentPage.value = parseInt(page, 10) || 1
	// 使用统一的加载方法
	loadFilteredMaterials()
}

// 处理每页条数变更事件
const handleSizeChange = (size) => {
	console.log('每页条数变更:', size)
	pageSize.value = size
	// 切换每页条数时重置为第一页
	currentPage.value = 1
	// 使用统一的加载方法
	loadFilteredMaterials()
}

// 修改现有的loadMaterialsList函数，默认加载全部素材
const loadMaterialsList = async () => {
	currentAlbumId.value = 'all'
	currentAlbumName.value = '全部素材'
	currentMaterialType.value = ''
	loadFilteredMaterials()
}

// 新增加载特定专辑素材的函数
const loadMaterialsListByAlbum = async (tagId) => {
	try {
		// 调用API获取素材列表，调整参数结构
		const params = {
			userId: getUserId(),
			pageParam: {
				pageNum: currentPage.value,
				pageSize: pageSize.value
			}
		}
		
		// 只有当tagId不是'all'时才添加tagIds参数
		if (tagId && tagId !== 'all') {
			params.tagIds = tagId
		} else {
			// 如果是查看全部素材，更新显示名称
			currentAlbumName.value = '全部素材'
		}
		
		const response = await getMyMaterialList(params)

		// 检查API返回是否成功
		if (response) {
			console.log('获取专辑素材列表', response)
			// 将API返回的数据映射到所需的表格格式
			materialsList.value = response.list.map(item => ({
				id: item.materialId,
				title: item.materialName || '未命名',
				type: getTypeFromMaterial(item),
				duration: (item.materialType === 'audio' || item.materialType === 'video') ? formatDuration(item.duration) : item.duration,
				album: item.tagName || '--',
				speaker: item.speakerName || '--',
				lastOpen: formatDate(item.updateTime),
				size: formatFileSize(item.fileSize),
				icon: getIconForMaterial(item),
				audioSrc: item.materialType === 'audio' ? item.storagePath : null,
				videoSrc: item.materialType === 'video' ? item.storagePath : null,
				description: item.comment || '',
				textContent: item.textContent || '',
				thumbnailPath: item.thumbnailPath || null // 添加缩略图路径
			}))
			totalItems.value = response.total
		} else {
			ElMessage.warning('获取专辑素材列表失败')
		}
	} catch (error) {
		console.error('加载专辑素材列表出错:', error)
		ElMessage.error('加载专辑素材列表出错')
	}
}

// 获取素材类型的辅助函数
const getTypeFromMaterial = (material) => {
	switch (material.materialType) {
		case 'audio': return '音频'
		case 'video': return '视频'
		case 'text': return '文本'
		case 'background': return '图片'
		// 其他类型统一归类
		case 'bgm': return '音频'  // 背景音乐归为音频
		case 'effect': return '音频'  // 特效音归为音频
		case 'image': return '文本'   // 头像归为文本
		case 'other': return '文本'   // 其他归为文本
		case 'driver': return '音频'
		default: return '文本'
	}
}

// 格式化持续时间的辅助函数
const formatDuration = (seconds) => {
	if (!seconds) return '--'
	const mins = Math.floor(seconds / 60)
	const secs = Math.floor(seconds % 60)
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 格式化日期的辅助函数
const formatDate = (timestamp) => {
	// if (!timestamp) return '--'
	// const date = new Date(timestamp)
	return timestamp
}

// 格式化文件大小的辅助函数
const formatFileSize = (bytes) => {
	if (!bytes) return '--'
	const kb = bytes / 1024
	if (kb < 1024) {
		return `${kb.toFixed(2)} KB`
	}
	const mb = kb / 1024
	return `${mb.toFixed(2)} MB`
}

// 根据素材类型获取图标信息
const getIconForMaterial = (material) => {
	// 如果是视频类型且有缩略图，则使用缩略图
	if (material.materialType === 'video' && material.thumbnailPath) {
		return {
			type: 'image',
			src: material.thumbnailPath
		}
	}
	// 以下是原有逻辑，作为后备选项
	if (material.materialType === 'audio' && material.thumbnailPath) {
		return {
			type: 'image',
			src: material.thumbnailPath
		}
	}
	if (material.materialType === 'tts') {
		return {
			type: 'color',
			color: '#FF6D41',
			text: 'T'
		}
	}
	if (material.materialType === 'background' && material.storagePath) {
		return {
			type: 'image',
			src: material.storagePath
		}
	}
	return {
		type: 'color',
		color: '#909399',
		text: '?'
	}
}

// 处理移动确认的函数
const handleMoveConfirm = async (data) => {
	if (data.success) {
		ElMessage.success('素材已成功移动到新专辑')
		// 重新加载素材列表
		loadMaterialsList()
	}
}

// 添加处理音频点击的函数
const handleAudioClick = (row) => {
	console.log('Audio clicked:', row)
	if (row.type === '音频' && row.audioSrc) {
		openMediaPlayer(row, 'audio')
	}
}

// 修改视频点击处理函数
const handleTitleClick = (row) => {
	console.log('Video clicked:', row)
	
	// 保存标题到previewStore
	if (row.title) {
		import('@/stores/previewStore').then(module => {
			const { usePreviewStore } = module;
			const previewStore = usePreviewStore();
			
			// 保存标题到previewStore
			previewStore.setTitle(row.title);
			console.log('已保存标题到previewStore:', row.title);
		}).catch(error => {
			console.error('保存标题到previewStore失败:', error);
		});
	}
	
	if (row.type === '视频' && row.videoSrc) {
		openMediaPlayer(row, 'video')
	}
}

// 更新媒体播放器打开函数
const openMediaPlayer = (mediaData, mode) => {
	currentMediaTitle.value = mediaData.title || (mode === 'audio' ? '音频预览' : '视频预览')
	currentMediaSrc.value = mode === 'audio' ? mediaData.audioSrc : mediaData.videoSrc
	console.log('更新媒体播放器打开函数', currentMediaSrc.value)
	// 优先使用缩略图路径，如果没有再检查icon里是否有图片
	if (mediaData.thumbnailPath) {
		currentMediaThumbnail.value = mediaData.thumbnailPath
		console.log('使用 thumbnailPath 作为封面:', currentMediaThumbnail.value)
	} else if (mediaData.icon?.type === 'image') {
		currentMediaThumbnail.value = mediaData.icon.src
		console.log('使用 icon.src 作为封面:', currentMediaThumbnail.value)
	} else if (mode === 'audio') {
		// 使用绝对路径的音频图标
		currentMediaThumbnail.value = audioIcon
		console.log('使用默认音频图标作为封面:', currentMediaThumbnail.value)
	} else {
		currentMediaThumbnail.value = ''
		console.log('没有设置封面')
	}

	mediaPlayerMode.value = mode
	mediaPlayerDialogVisible.value = true
	
	// 打印最终传递给组件的值
	console.log('传递给 VideoPlayerDialog 的属性:', {
		title: currentMediaTitle.value,
		mode: mediaPlayerMode.value,
		mediaThumbnail: currentMediaThumbnail.value
	})
}

// 更新关闭对话框的处理函数
const handleMediaDialogClose = () => {
	mediaPlayerDialogVisible.value = false
}

// 修改处理媒体下载的函数，实现直接下载到本地
const handleMediaDownload = async (data) => {
	try {
		// ElMessage.success(`开始下载${data.type === 'audio' ? '音频' : '视频'}：${data.title}`);

		if (data.type === 'text') {
			// 处理文本下载 - 创建和下载一个txt文件
			const blob = new Blob([data.content || ''], { type: 'text/plain;charset=utf-8' });
			const url = URL.createObjectURL(blob);
			
			const a = document.createElement('a');
			a.href = url;
			// 确保文件名包含.txt扩展名
			let fileName = data.title || '文本文件';
			if (!fileName.toLowerCase().endsWith('.txt')) {
				fileName += '.txt';
			}
			
			a.download = fileName;
			document.body.appendChild(a);
			a.click();
			
			// 清理
			document.body.removeChild(a);
			URL.revokeObjectURL(url);
			
			return;
		}

		// 直接使用URL创建一个a标签进行下载
		const a = document.createElement('a');
		a.href = data.url;
		// 确保文件名包含正确的扩展名
		const extension = data.type === 'audio' ? '.mp3' : '.mp4';
		let fileName = data.title || `${data.type === 'audio' ? '音频' : '视频'}-${new Date().getTime()}`;
		
		// 如果文件名没有包含扩展名，添加扩展名
		if (!fileName.toLowerCase().endsWith('.mp3') && !fileName.toLowerCase().endsWith('.mp4')) {
			fileName += extension;
		}
		
		a.download = fileName;
		// 设置target为_blank，避免浏览器只导航而不下载
		a.target = '_blank';
		a.rel = 'noopener noreferrer';
		document.body.appendChild(a);
		a.click();
		
		// 清理
		document.body.removeChild(a);
		
		// ElMessage.success(`下载请求已发送：${fileName}`);
	} catch (error) {
		console.error('Download failed:', error);
		ElMessage.error('下载失败，请重试');
	}
};

// 处理上传按钮点击事件
const handleUploadClick = () => {
	// 如果存在旧的文件输入框，先从DOM中移除
	if (fileInputRef.value) {
		document.body.removeChild(fileInputRef.value);
		fileInputRef.value = null;
	}

	// 每次都创建一个新的文件输入框，确保触发change事件
	const input = document.createElement('input');
	input.type = 'file';
	input.accept = 'audio/*,video/*,image/*,.txt,.doc,.docx,.pdf';
	input.style.display = 'none';
	input.onchange = (e) => handleFileChange(e);
	document.body.appendChild(input);
	fileInputRef.value = input;

	// 触发文件选择
	fileInputRef.value.click();
}

// 处理文件选择变更
const handleFileChange = async (event) => {
	const file = event.target.files[0]
	if (!file) return

	try {
		// 验证文件类型
		if (!file.name.toLowerCase().endsWith('.wav') &&
			!file.name.toLowerCase().endsWith('.mp3') &&
			!file.name.toLowerCase().endsWith('.mp4')) {
			ElMessage.error('请上传视频或音频文件')
			return
		}

		// 检查文件大小（这里设置最大1GB）
		const maxSize = 1024 * 1024 * 1024 // 1GB
		if (file.size > maxSize) {
			ElMessage.error('文件大小不能超过1GB')
			return
		}

		// 获取媒体文件时长
		let duration = 0
		try {
			duration = await getMediaDuration(file)
			console.log('获取到媒体时长:', duration)
		} catch (error) {
			console.error('获取媒体时长失败:', error)
			// 失败不阻止上传，但记录日志
		}

		// 空间权限检查
		try {
			const fileSizeMB = Math.ceil(file.size / 1024 / 1024); // 转换为MB并向上取整
			const userId = getUserId();
			const response = await checkUploadPermission({
				userId: userId,
				feat: "空间",
				need: fileSizeMB
			});
			console.log(response, 'response');
			
			// 检查返回结果
			if (response && response.content && response.content.result === false) {
				// 显示空间不足对话框
				insufficientSpaceDialogVisible.value = true;
				return;
			}
		} catch (error) {
			console.error('检查上传权限失败:', error);
			ElMessage.error('检查上传权限失败，请稍后重试');
			return;
		}

		isUploading.value = true

		// 设置上传文件信息
		uploadFile.value = {
			name: file.name,
			size: file.size,
			loaded: 0,
			percent: 0,
			type: file.type
		}

		// 获取文件扩展名确定文件类型
		const fileExtension = file.name.split('.').pop().toLowerCase()
		const fileType = fileExtension === 'mp4' ? 'mp4' : 'mp3'

		// 调用 dubbing API 获取 OSS 上传凭证
		const response = await dubbing({ userId: getUserId(), fileType })

		// 去掉文件名的后缀
		const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.')

		const formData = new FormData()
		// 添加 OSS 需要的参数
		formData.append('OSSAccessKeyId', response.accessKeyId)
		formData.append('policy', response.policy)
		formData.append('signature', response.signature)
		formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`)
		formData.append('file', file)

		// 使用 XHR 上传文件，以便能够跟踪进度
		const xhr = new XMLHttpRequest()

		// 保存上传请求引用，以便能够取消
		uploadRequest.value = xhr

		// 设置进度监听
		xhr.upload.onprogress = (e) => {
			if (e.lengthComputable) {
				uploadFile.value.percent = Math.round(e.loaded / e.total * 100)
				uploadFile.value.loaded = e.loaded
			}
		}

		// 上传完成后的处理
		xhr.onload = async () => {
			try {
				if (xhr.status >= 200 && xhr.status < 300) {
					// 判断文件类型
					const materialType = fileExtension === 'mp4' ? 'video' : 'audio'
					const userId = getUserId()
					
					// 获取当前选中的专辑ID
					let tagId = null
					let tagName = null
					
					if (currentAlbumId.value && currentAlbumId.value !== 'all') {
						// 如果已选中专辑，则使用选中的专辑ID
						tagId = currentAlbumId.value
						// 根据ID查找对应的专辑名称
						const selectedAlbum = albums.value.find(album => album.id === tagId)
						tagName = selectedAlbum ? selectedAlbum.name : ''
					} else {
						// 如果没有选中专辑或选中"全部素材"
						// 查找名为"默认专辑"的专辑，或者使用第一个专辑作为默认
						const defaultAlbum = albums.value.find(album => album.name === '默认专辑')
						if (defaultAlbum) {
							tagId = defaultAlbum.id
							tagName = defaultAlbum.name
						} else if (albums.value.length > 0) {
							// 如果没有名为"默认专辑"的专辑，使用第一个专辑
							tagId = albums.value[0].id
							tagName = albums.value[0].name
						} 
					}

					// 调用 callbackOss 接口，添加duration参数
					const callbackResponse = await callbackOss({
						userId: userId,
						materialName: fileNameWithoutExt,
						ossPath: response.key.replace(/[^\/]+$/, '') + file.name,
						fileSize: String(file.size),
						fileExtension: fileExtension,
						tagNames: tagName, // 使用专辑名称
						materialType: materialType,
						isPrivate: '1',
						storage_path: `/material/${userId}/${file.name}`,
						tagId: tagId, // 使用专辑ID
						duration: duration ? String(Math.round(duration)) : '0' // 添加媒体时长（取整）
					})

					// 更新文件信息
					uploadFile.value = {
						...uploadFile.value,
						name: callbackResponse.filename || file.name,
						url: callbackResponse.url,
						percent: 100,
						loaded: file.size
					}

					ElMessage.success('文件上传成功')

					// 上传成功后重新加载当前选中专辑的素材列表
					if (currentAlbumId.value) {
						loadFilteredMaterials() // 使用当前的筛选条件重新加载
					} else {
						loadMaterialsList() // 加载全部素材
					}
				} else {
					throw new Error(xhr.statusText || '上传失败')
				}
			} catch (error) {
				console.error('处理错误:', error)
				ElMessage.error(error.message || '文件处理失败')
			} finally {
				// 确保在所有处理完成后重置状态
				resetUploadState()
			}
		}

		// 错误处理
		xhr.onerror = (error) => {
			console.error('上传错误:', error)
			ElMessage.error('文件上传失败')
			resetUploadState()
		}

		// 中断处理
		xhr.onabort = () => {
			ElMessage.warning('上传已取消')
			resetUploadState()
		}

		// 开始上传
		xhr.open('POST', response.host, true)
		xhr.send(formData)
	} catch (error) {
		console.error('上传失败:', error)
		ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
		resetUploadState()
	}

	// 清空文件选择，以便于下次选择同一文件时能触发change事件
	if (fileInputRef.value) {
		fileInputRef.value.value = ''
	}
}

// 添加获取媒体时长的辅助函数
const getMediaDuration = (file) => {
	return new Promise((resolve, reject) => {
		// 根据文件类型创建对应的媒体元素
		const isVideo = file.type.includes('video') || file.name.toLowerCase().endsWith('.mp4')
		const media = isVideo ? document.createElement('video') : document.createElement('audio')
		
		// 创建临时URL
		const objectUrl = URL.createObjectURL(file)
		media.src = objectUrl
		
		// 监听元数据加载事件
		media.addEventListener('loadedmetadata', () => {
			// 获取媒体时长（秒）
			const durationSeconds = media.duration
			// 释放临时URL
			URL.revokeObjectURL(objectUrl)
			resolve(durationSeconds)
		})
		
		// 监听错误事件
		media.addEventListener('error', (e) => {
			URL.revokeObjectURL(objectUrl)
			reject(new Error('获取媒体时长失败'))
		})
		
		// 设置超时，防止某些媒体文件无法加载元数据
		setTimeout(() => {
			URL.revokeObjectURL(objectUrl)
			reject(new Error('获取媒体时长超时'))
		}, 5000) // 5秒超时
	})
}

// 重置上传状态
const resetUploadState = () => {
	isUploading.value = false
	uploadFile.value = {
		name: '',
		size: 0,
		loaded: 0,
		percent: 0,
		type: ''
	}
	uploadRequest.value = null
    
    // 确保在重置状态时，也清空文件输入元素的值
    if (fileInputRef.value) {
        fileInputRef.value.value = ''
    }
}

// 取消上传
const cancelUpload = () => {
	if (uploadRequest.value) {
		uploadRequest.value.abort()
		uploadRequest.value = null
	}
	resetUploadState()
}

// 添加进度格式化函数
const percentFormat = (percent) => {
	return percent === 100 ? '上传完成' : `${percent}%`
}

// 添加 watch 监听 activeTab 变化
watch(activeTab, (newValue) => {
	if (newValue === 'works') {
		router.push('/myWorks')
	} else if (newValue === 'materials') {
		router.push('/myMaterials')
	} else if (newValue === 'custom') {
		router.push('/myCustomizations')
	}
})

// 新增加载用户专辑列表的函数
const loadUserAlbums = async () => {
	try {
		const response = await getUserAlbum({ userId: getUserId() })
		if (response && response.length > 0) {
			albums.value = response.map(item => ({
				id: item.tagId,
				name: item.tagName || '未命名专辑',
				updateTime: formatDate(item.createdAt) || '未知时间'
			}))
			// 移除自动选中专辑的逻辑，默认使用"查看全部"
		} else {
			albums.value = []
			// 没有专辑时，仍然保持全部素材状态
		}
	} catch (error) {
		console.error('加载用户专辑列表出错:', error)
		albums.value = []
	}
}

// 定义fetchAlbums函数，用于刷新专辑列表
const fetchAlbums = () => {
	// 调用加载用户专辑列表的函数
	loadUserAlbums();
}

// 在组件挂载时加载数据
onMounted(() => {
	// 默认加载全部素材
	currentAlbumId.value = 'all'
	currentAlbumName.value = '全部专辑'
	loadMaterialsList() // 直接加载全部素材
	
	// 仍然加载专辑列表，但不自动选中
	loadUserAlbums() 
})

// 添加格式化日期时间的函数，确保带有前导0的时分秒（与myWorks一致）
const formatDateTime = (date) => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 修改处理素材类型过滤的方法
const handleTypeFilter = (value) => {
	console.log('素材类型过滤:', value, 'typeof:', typeof value)
	
	// 检查是否选择了"全部"选项
	if (value === '' || (typeof value === 'object' && value !== null && value.value === '')) {
		// 选择了"全部"，将materialType设为null，确保不传此参数
		currentMaterialType.value = null
		console.log('选择了全部类型，materialType设为null')
	} else if (typeof value === 'object' && value !== null && value.value) {
		// 选择了具体类型，从对象中提取value属性
		currentMaterialType.value = value.value
		console.log('从对象中提取的materialType值:', value.value)
		
		// 将bgm和effect都映射到audio类型
		if (value.value === 'bgm' || value.value === 'effect') {
			currentMaterialType.value = 'audio'
			console.log('将bgm/effect映射到audio类型')
		}
		
		// 将image和other都映射到text类型
		if (value.value === 'image' || value.value === 'other') {
			currentMaterialType.value = 'text'
			console.log('将image/other映射到text类型')
		}
	} else {
		// 不是对象则直接使用
		currentMaterialType.value = value
	}
	
	// 选择素材类型时，重置为第一页（确保是数字类型）
	currentPage.value = 1
	console.log('素材类型选择已重置页码为第一页:', currentPage.value, typeof currentPage.value)
	
	// 等待DOM更新后，触发加载函数
	nextTick(() => {
		// 重新加载素材列表，带上筛选条件
		loadFilteredMaterials()
	})
}

// 修改统一的加载素材方法
const loadFilteredMaterials = async () => {
	try {
		console.log('开始加载筛选素材，类型:', currentMaterialType.value, '当前页码:', currentPage.value, typeof currentPage.value)
		
		// 强制确保页码是数字类型
		const pageNum = parseInt(currentPage.value, 10) || 1
		const pageSizeNum = parseInt(pageSize.value, 10) || 10
		
		console.log('转换后的页码参数 - 页码:', pageNum, '每页条数:', pageSizeNum)
		
		const params = {
			userId: getUserId(),
			pageParam: {
				pageNum: pageNum,
				pageSize: pageSizeNum
			}
		}

		// 只有当选择了特定专辑时才添加tagIds
		if (currentAlbumId.value && currentAlbumId.value !== 'all') {
			params.tagIds = currentAlbumId.value
		}

		// 只有当选择了特定素材类型时才添加materialType
		if (currentMaterialType.value) {
			// 确保materialType是字符串
			params.materialType = String(currentMaterialType.value)
			console.log('添加materialType参数:', params.materialType)
		} else {
			console.log('未添加materialType参数，使用全部类型')
		}

		console.log('素材查询最终参数:', JSON.stringify(params))
		const response = await getMyMaterialList(params)
		
		if (response) {
			// 将API返回的数据映射到所需的表格格式
			materialsList.value = response.list.map(item => ({
				id: item.materialId,
				title: item.materialName || '未命名',
				type: getTypeFromMaterial(item),
				duration: (item.materialType === 'audio' || item.materialType === 'video' || item.materialType === 'driver') ? formatDuration(item.duration) : item.duration,
				album: item.tagName || '--',
				speaker: item.speakerName || '--',
				lastOpen: formatDate(item.updateTime),
				size: formatFileSize(item.fileSize),
				icon: getIconForMaterial(item),
				audioSrc: item.materialType === 'audio' ? item.storagePath : null,
				videoSrc: item.materialType === 'video' ? item.storagePath : null,
				description: item.comment || '',
				textContent: item.textContent || '',
				thumbnailPath: item.thumbnailPath || null // 添加缩略图路径
			}))
			totalItems.value = response.total
			console.log('素材列表加载完成，总条数:', response.total, '当前页:', pageNum)
		} else {
			ElMessage.warning('获取素材列表失败')
		}
	} catch (error) {
		console.error('加载素材列表出错:', error)
		ElMessage.error('加载素材列表出错')
	}
}

// 新增：查看全部复选框状态默认为true
const showAllMaterials = ref(true)
// 添加一个用于记录上次请求状态的变量
const lastAllMaterialsRequestTime = ref(0)

// 处理查看全部复选框切换
const handleShowAllMaterialsChange = async (checked) => {
	try {
		if (checked) {
			// 重置相关状态
			currentAlbumId.value = 'all'
			currentAlbumName.value = '全部专辑'
			currentPage.value = 1
			
			// 记录本次请求时间
			const currentTime = Date.now()
			lastAllMaterialsRequestTime.value = currentTime
			
			// 强制触发素材列表接口
			console.log('查看全部选中，触发素材列表请求')
			await loadMaterialsList()
		} else if (currentAlbumId.value && currentAlbumId.value !== 'all') {
			await loadMaterialsListByAlbum(currentAlbumId.value)
		}
	} catch (error) {
		console.error('切换查看全部素材失败:', error)
	}
}

// 添加处理文本保存的函数
const handleTextSave = async (data) => {
	try {
		console.log('文本保存:', data);
		// 使用ID查找当前正在编辑的素材
		const index = materialsList.value.findIndex(item => item.id === currentTextMaterialId.value);
		if (index !== -1) {
			const material = materialsList.value[index];
			
			// 调用API更新素材信息
			const response = await updateMaterialInfo({
				materialId: material.id,
				comment: data.content, // 使用content作为描述/评论
				materialName: data.title // 更新标题
			});
			
			// 更新本地数据
			materialsList.value[index].title = data.title;
			materialsList.value[index].description = data.content;
			materialsList.value[index].textContent = data.content;
			
			// 更新当前标题，保持一致性
			currentTextTitle.value = data.title;
			
			ElMessage.success('文本内容已更新');
		} else {
			ElMessage.warning('未找到要更新的素材');
		}
	} catch (error) {
		console.error('更新文本内容失败:', error);
		ElMessage.error('更新文本内容失败');
	}
}

// 处理AI配音按钮点击
const handleAiVoice = (data) => {
	// 关闭对话框
	textPreviewDialogVisible.value = false;
	useAIDubbing.setExtraction(data.content )
	router.push({ path: '/AIDubbing', query: { extraction: true } });
}

// 新增：上传权限对话框相关状态
const uploadPermissionDialogVisible = ref(false)
const uploadPermissionMessage = ref('')

// 新增：空间不足对话框相关变量
const insufficientSpaceDialogVisible = ref(false);
const insufficientSpaceTitle = ref('');
const insufficientSpaceSubTitle = ref('您的个人空间容量已不足');
const insufficientSpaceMessage = ref('如需使用请购买空间额度！');

// 处理购买空间
const handleBuySpace = () => {
  insufficientSpaceDialogVisible.value = false;
  // 在新标签页打开购买空间页面
  const route = router.resolve({ name: 'membership', query: { nav: 'space' } });
  window.open(route.href, '_blank');
}

// 处理取消购买
const handleCancelBuy = () => {
    insufficientSpaceDialogVisible.value = false;
}

// 处理上传权限确认
const handleUploadPermissionConfirm = () => {
	// 实现上传权限检查逻辑
	// 这里可以根据实际情况进行权限检查
	uploadPermissionDialogVisible.value = false
	ElMessage.success('上传权限检查通过')
}

</script>

<style lang="scss" scoped>
.my-materials-container {
	padding: 20px;
	background-color: #fff;
	height: 100%;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	overflow: hidden; /* 添加overflow:hidden防止整体滚动 */

	.albums-section,
	.materials-section {
		padding: 0 20px;
	}
	
	.albums-section {
		flex-shrink: 0; /* 防止专辑区域被压缩 */
		margin-bottom: 15px; /* 减少专辑区域和素材区域间的间距 */
		/* 确保专辑区域有固定高度 */
		height: auto;
		overflow: visible hidden; /* 防止垂直方向溢出 */
	}

	.current-item-label {
		font-size: 14px;
		color: #909399;
		font-weight: normal;
		margin-left: 8px;
		line-height: 32px;
	}

	.album-cards {
		display: flex;
		gap: 12px; /* 减小间距从16px到12px，使布局更紧凑 */
		// margin-bottom: 15px; /* 减少底部边距 */
		padding: 0;
		flex-wrap: wrap;
		/* 调整高度为三行专辑卡片 */
		max-height: 270px; /* 增加高度，能够容纳三行卡片才显示滚动条 */
		overflow-y: auto; /* 保留垂直滚动条 */
		width: 100%; /* 确保宽度占满 */
		/* 美化滚动条 */
		&::-webkit-scrollbar {
			width: 6px;
		}
		&::-webkit-scrollbar-thumb {
			background-color: #C0C4CC;
			border-radius: 3px;
		}
		&::-webkit-scrollbar-track {
			background-color: #F5F7FA;
		}

		.album-card {
			width: 240px;
			cursor: pointer;
			border: 1px solid #EBEEF5;
			position: relative;
			/* 添加固定高度确保每行高度一致 */
			// height: 80px;
			margin-bottom: 8px; /* 保持卡片底部间距 */
			box-sizing: border-box;
			transition: all 0.2s ease;

			&:hover {
				box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
				border: 1px solid #DCDFE6;
			}
			
			/* 为"全部素材"专辑添加特殊样式 */
			&.all-materials-album {
				.album-icon {
					color: #0AAF60;
					font-size: 26px;
				}
				
				.album-name {
					color: #0AAF60;
					font-weight: 600;
				}
			}

			&.active-album {
				background-color: rgba(10, 175, 96, 0.05);

				&::after {
					content: '';
					position: absolute;
					top: -1px;
					left: -1px;
					right: -1px;
					bottom: -1px;
					border: 2px solid #0AAF60;
					pointer-events: none;
					z-index: 1;
				}
			}

			:deep(.el-card__body) {
				display: flex;
				align-items: center;
				padding: 16px;
				height: 100%; /* 确保内容填充整个卡片高度 */
				box-sizing: border-box;
			}

			.album-icon {
				margin-right: 12px;
				font-size: 24px;
				color: #0AAF60;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.album-info {
				flex: 1;

				.album-name {
					font-size: 14px;
					margin-bottom: 4px;
				}

				.album-date {
					font-size: 12px;
					color: #909399;
				}
			}

			.more-icon {
				cursor: pointer;
				padding: 4px;
				width: 20px;
				height: 20px;
				outline: none;
				border: none;

				&:hover,
				&:focus {
					outline: none;
					border: none;
				}
			}
		}
	}

	.section-header {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 10px; /* 减少标题区域的底部边距 */
		padding: 0;
		gap: 12px;
		// height: 32px;

		.section-title {
			font-size: 18px;
			font-weight: bold;
			color: #20252A;
			line-height: 32px;
			display: inline-block;
			vertical-align: middle;
		}

		.el-button {
			--el-button-border-color: #0AAF60;
			--el-button-text-color: #0AAF60;
			--el-button-bg-color: #FFFFFF;
			width: 93px;
			height: 30px;
			padding: 0;
			line-height: 28px;

			&:hover {
				background-color: #FFFFFF;
				border-color: #0AAF60;
				color: #0AAF60;
			}

			.el-icon {
				color: #0AAF60;
			}
		}
	}

	.materials-section {
		flex: 1;
		overflow-y: auto; /* 修改为auto，允许内部滚动 */
		display: flex;
		flex-direction: column;
		min-height: 0;
		max-height: calc(100% - 140px); /* 调整计算值，更准确地适应专辑区域 */
	}

	.view-all-checkbox {
		margin: 0;
		font-size: 14px;
		// 默认文字颜色为橙色
		:deep(.el-checkbox__label) {
			color: #606266;
		}
		// 仅修改勾选框本体选中颜色
		:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
			border-color: #09A058;
			background-color: #09A058;
		}
	}

	:deep(.el-dialog__body) {
		overflow: hidden !important;
	}
}

.materials-section{
	flex: 1;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	min-height: 0;
	max-height: calc(100% - 150px); /* 限制作品区域最大高度，给项目区域留出空间 */
	
	/* 确保CustomTable能够在works-section中正确适应高度 */
	:deep(.custom-table-container) {
		height: 100%;
		display: flex;
		flex-direction: column;
		
		.el-table {
			flex: 1;
			min-height: 0;
			overflow-y: auto; /* 确保表格内容可滚动 */
		}
		
		.pagination-container {
			margin-top: 10px; /* 减少分页器上边距 */
			flex-shrink: 0;
		}
	}

	// 查看全部复选框选中样式
	.section-header {
		.view-all-checkbox {
			:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
				border-color: #09A058;
				background-color: #09A058;
			}
		}
	}
}
</style>