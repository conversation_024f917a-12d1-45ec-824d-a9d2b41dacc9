<template>
	<div class="my-works-container">
		<!-- 项目区域 -->
		<div class="projects-section">
			<div class="section-header">
				<span class="section-title">项目</span>
				<span class="verticalLine"></span>
				<el-button type="primary" class="add-btn" plain @click="showCreateProjectDialog">
					<el-icon>
						<Plus />
					</el-icon>新建项目
				</el-button>
				<el-checkbox v-model="showAllWorks" @change="handleShowAllWorksChange"
					class="view-all-checkbox">查看全部</el-checkbox>
			</div>
			<div class="project-cards">
				<!-- 使用v-if确保projects数组已初始化 -->
				<template v-if="projects && projects.length > 0">
					<el-card v-for="project in projects" :key="project.id" class="project-card"
						:class="{ 'active-project': selectedProjectId === project.id }"
						@click="handleProjectClick(project)">
						<div class="project-icon">
							<img src="@/assets/img/file.png" />
						</div>
						<div class="project-info">
							<!-- 显示项目名称，动态绑定project对象的name属性 -->
							<div class="project-name">{{ project.name || '未命名项目' }}</div>
							<!-- 显示项目最后更新时间，动态绑定project对象的updateTime属性，并添加"更新"文本 -->
							<div class="project-date">{{ project.updateTime || '--' }} 更新</div>
						</div>
						<!-- 对"默认项目"隐藏操作按钮 -->
						<el-dropdown v-if="project.name && project.name !== '默认项目'"
							@command="command => handleProjectAction(command, project)">
							<img src="@/assets/img/三个点.png" class="more-icon" alt="更多" />
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item command="rename">
										<el-icon>
											<EditPen />
										</el-icon>重命名
									</el-dropdown-item>
									<el-dropdown-item command="delete">
										<el-icon>
											<Delete />
										</el-icon>删除
									</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</el-card>
				</template>
			</div>
		</div>

		<!-- 作品区域 -->
		<div class="works-section">
			<div class="section-header">
				<span class="section-subtitle">作品</span>
				<div class="tabBox">
					<span :class="{active:workTab=='mine'}" @click="changeWorkType('mine')">我的作品</span>
					<span :class="{active:workTab=='digital'}" @click="changeWorkType('digital')">数字人作品</span>
				</div>
			</div>
			<div v-if="workTab=='mine'" style="height: 93%;">
				<CustomTable :tableData="worksList" :columns="worksColumns" :actions="tableActions" :showSelection="false"
				:showRadio="false" :total="totalCount" :defaultCurrentPage="currentPage" :defaultPageSize="pageSize"
				:loading="isLoading" :enableFormatSelection="true" @action="handleTableAction"
				@page-change="handlePageChange" @size-change="handleSizeChange" @cell-click="handleCellClick"
				@filter="handleFilter" @row-click="handleRowClick" />
			</div>
			<div v-if="workTab=='digital'" style="height: 100%;">
				<DigitalHumanWorks :selectedFileId="selectedProjectId" :workTab="workTab" />
			</div>
		</div>

		<!-- 新建项目对话框 -->
		<CreateProjectDialog v-model:visible="createProjectDialogVisible" title="新建项目"
			@created="handleProjectCreated" />

		<!-- 删除确认对话框 -->
		<DeleteConfirmDialog v-model:visible="deleteDialogVisible" :item-title="deleteItemTitle"
			:item-type="deleteFileType === 'project' ? 'project' : 'project'"
			:show-warning="deleteFileType === 'project'" @cancel="handleDeleteCancel" @confirm="handleDeleteConfirm" />

		<!-- 重命名对话框 -->
		<RenameDialog v-model:visible="renameDialogVisible" title="修改作品名称" :current-name="currentRenameItem.name"
			@confirm="handleRenameConfirm" />

		<!-- 设置对话框 -->
		<SettingsDialog v-model:visible="settingsDialogVisible" :itemData="currentSettingsItem" mode="work"
			buttonColor="#0AAF60" @save="handleSettingsSave" />

		<!-- 移动到项目对话框 -->
		<MoveToDialog v-model:visible="moveToProjectDialogVisible" :selected-item="currentMoveItem"
			:project-list="projects" mode="project" @confirm="handleMoveConfirm" />
	</div>
</template>

<script setup>
import { ref, onMounted, markRaw } from 'vue'
import { Plus, Folder, MoreFilled, Setting, Delete, More, EditPen, Download } from '@element-plus/icons-vue'
import CustomTable from '../components/CustomTable.vue'
import DigitalHumanWorks from './components/DigitalHumanWorks.vue' //我的作品-数字人作品
import CreateProjectDialog from '../components/CreateProjectDialog.vue'
import DeleteConfirmDialog from '../components/DeleteConfirmDialog.vue'
import RenameDialog from '../components/RenameDialog.vue'
import SettingsDialog from '../components/SettingsDialog.vue'
import MoveToDialog from '../components/MoveToDialog.vue'
import { getByAlbum, getAlbumsByCreator, createAlbum, deleteAlbum, updateAlbum, changeAlbum, deleteWork, getWorkTypes, updateWork } from '@/api/mySpace.js'
import { useRouter, useRoute } from 'vue-router'
import { useloginStore } from '@/stores/login'
import { useSoundStore } from '@/stores/modules/soundStore'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const currentTypeFilter = ref('')
const loginStore = useloginStore()
const soundStore = useSoundStore()

const createProjectDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const renameDialogVisible = ref(false)
const settingsDialogVisible = ref(false)
const currentDeleteItem = ref({})
const deleteFileType = ref('project')
const currentRenameItem = ref({})
const currentSettingsItem = ref({})
const deleteItemTitle = ref('')

// 新增分页相关状态
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 将示例数据改为空数组
const projects = ref([])

// 初始化worksList为空数组
const worksList = ref([])

const worksColumns = ref([
	{ label: '标题', prop: 'title', align: 'center' },
	{
		label: '工作类型',
		prop: 'type',
		align: 'center',
		type: 'select',
		options: [
			// { label: '全部', value: '' },
			// { label: '一键成片', value: '0' },
			// { label: 'AI配音', value: '1' },
			// { label: '专业云剪', value: '2' },
			// { label: 'AI商配', value: '3' }
		]
	},
	{ label: '字数/时长', prop: 'duration', align: 'center' },
	{ label: '所属项目', prop: 'project', align: 'center' },
	{ label: '最近打开', prop: 'updatedAt', align: 'center' },
	{ label: '发音人', prop: 'speaker', align: 'center' }
])

const tableActions = ref([
	{ type: 'setting', icon: markRaw(Setting) },
	{ type: 'add', icon: markRaw(Plus) },
	{ type: 'download', icon: markRaw(Download) },
	{ type: 'delete', icon: markRaw(Delete) },
	{ type: 'more', icon: markRaw(More) }
])

const isLoading = ref(false)

// 添加移动到项目对话框的状态变量
const moveToProjectDialogVisible = ref(false)
const currentMoveItem = ref(null)

// 新增查看全部复选框状态
const showAllWorks = ref(true)

// 添加一个用于记录上次请求状态的变量
const lastAllWorksRequestTime = ref(0)

// 作品tab切换
const workTab = ref('mine')
const changeWorkType = (val) => {
	workTab.value = val
}

// 接收数字人页面传递过来的参数
const pageType = route.query.type
if(pageType && pageType == 'shuziren'){
	workTab.value = 'digital'
}

// 我的作品页面专用音频下载功能
const handleAudioDownload = async (row) => {
	try {
		console.log('下载作品音频:', row)

		// 从作品数据中获取音频URL
		const audioUrl = row.ossAudioUrl

		if (!audioUrl) {
			ElMessage({
				message: '该作品没有可下载的音频文件',
				type: 'warning'
			})
			return
		}

		// 生成文件名
		const filename = `${sanitizeFilename(row.title)}.mp3`

		// 使用fetch下载音频文件
		const link = document.createElement('a')

		try {
			const response = await fetch(audioUrl)
			if (!response.ok) {
				throw new Error('网络请求失败')
			}

			const blob = await response.blob()
			const url = URL.createObjectURL(blob)

			link.href = url
			link.download = filename
			link.style.display = 'none'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)

			// 释放URL对象，避免内存泄漏
			setTimeout(() => {
				URL.revokeObjectURL(url)
			}, 1000)

			ElMessage({
				message: `作品 "${row.title}" 音频下载完成`,
				type: 'success'
			})
		} catch (fetchError) {
			console.error('音频下载失败:', fetchError)
			// 如果fetch失败，尝试直接链接下载
			link.href = audioUrl
			link.download = filename
			link.style.display = 'none'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)

			ElMessage({
				message: `作品 "${row.title}" 音频下载请求已发送`,
				type: 'success'
			})
		}
	} catch (error) {
		console.error('音频下载失败:', error)
		ElMessage({
			message: `音频下载失败: ${error.message || '未知错误'}`,
			type: 'error'
		})
	}
}

// 我的作品页面专用视频下载功能
const handleVideoDownload = async (row) => {
	try {
		console.log('下载作品视频:', row)

		// 从作品数据中获取视频URL，优先使用ossVideoUrl，备选videoUrl
		const videoUrl = row.ossVideoUrl || row.videoUrl

		if (!videoUrl) {
			ElMessage({
				message: '该作品没有可下载的视频文件',
				type: 'warning'
			})
			return
		}

		// 生成文件名
		const filename = `${sanitizeFilename(row.title)}.mp4`

		// 使用fetch下载视频文件
		const link = document.createElement('a')

		try {
			const response = await fetch(videoUrl)
			if (!response.ok) {
				throw new Error('网络请求失败')
			}

			const blob = await response.blob()
			const url = URL.createObjectURL(blob)

			link.href = url
			link.download = filename
			link.style.display = 'none'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)

			// 释放URL对象，避免内存泄漏
			setTimeout(() => {
				URL.revokeObjectURL(url)
			}, 1000)

			ElMessage({
				message: `作品 "${row.title}" 视频下载完成`,
				type: 'success'
			})
		} catch (fetchError) {
			console.error('视频下载失败:', fetchError)
			// 如果fetch失败，尝试直接链接下载
			link.href = videoUrl
			link.download = filename
			link.style.display = 'none'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)

			ElMessage({
				message: `作品 "${row.title}" 视频下载请求已发送`,
				type: 'success'
			})
		}
	} catch (error) {
		console.error('视频下载失败:', error)
		ElMessage({
			message: `视频下载失败: ${error.message || '未知错误'}`,
			type: 'error'
		})
	}
}



// 清理文件名中的非法字符
const sanitizeFilename = (filename) => {
	if (!filename) return '未命名文件'
	return filename.replace(/[\/\\:*?"<>|]/g, '_').substring(0, 100) // 限制长度
}



// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}

const handleTableAction = async ({ type, row }) => {
	console.log('Table action:', type, row)

	if (type === 'setting') {
		// 直接使用表格数据中的comment字段初始化设置对话框
		currentSettingsItem.value = {
			id: row.id,
			title: row.title,
			description: row.comment || '',
			thumbnail: row.thumbnail || ''
		}
		settingsDialogVisible.value = true
	} else if (type === 'download') {
		// 处理下载操作
		try {
			// 检查作品是否有有效的ID
			if (!row.id) {
				throw new Error('作品ID无效，无法下载')
			}

			// 根据作品类型判断下载内容
			if (row.type === 'AI配音' || row.type === 'AI商配') {
				// AI配音和AI商配类型，下载音频文件
				if (row.ossAudioUrl) {
					await handleAudioDownload(row)
				} else {
					ElMessage({
						message: '该作品没有可下载的音频文件',
						type: 'warning'
					})
				}
			} else if (row.type === '一键成片') {
				// 一键成片类型，下载视频文件
				if (row.ossVideoUrl || row.videoUrl) {
					await handleVideoDownload(row)
				} else {
					ElMessage({
						message: '该作品没有可下载的视频文件',
						type: 'warning'
					})
				}
			} else {
				// 其他类型暂不支持下载
				ElMessage({
					message: '该作品类型暂不支持下载',
					type: 'warning'
				})
			}
		} catch (error) {
			console.error('下载失败:', error)
			ElMessage({
				message: `下载失败: ${error.message || '未知错误'}`,
				type: 'error'
			})
		}
	} else if (type === 'delete') {
		// 显示删除确认对话框，为作品删除设置特定标题
		deleteItemTitle.value = `${row.title || '此作品'}`
		currentDeleteItem.value = row
		deleteFileType.value = 'work'
		deleteDialogVisible.value = true
	} else if (type === 'add') {
		// 处理移动操作 - 打开移动到项目对话框
		currentMoveItem.value = {
			id: row.id,
			title: row.title,
			currentProject: row.project || selectedProjectName.value || '无'
		}
		moveToProjectDialogVisible.value = true
	} else if (type === 'more') {
		// 处理更多操作
		// ...
	}
}

// 处理设置保存
const handleSettingsSave = async (updatedData) => {
	console.log('保存设置:', updatedData)

	try {
		// 调用updateWork接口更新作品
		const updateParams = {
			id: updatedData.id,
			title: updatedData.title,
			comment: updatedData.description // 使用用户在UI中输入的描述
		}

		console.log('更新作品参数:', updateParams)
		const updateResponse = await updateWork(updateParams)
		console.log('更新作品结果:', updateResponse)

		// 从返回结果中获取最新comment
		const latestComment = updateResponse && updateResponse.comment ? updateResponse.comment : updatedData.description

		// 更新本地数据
		const index = worksList.value.findIndex(item => item.id === updatedData.id)
		if (index !== -1) {
			worksList.value[index].title = updatedData.title
			worksList.value[index].comment = latestComment // 使用返回结果中的comment
		}

		// 显示成功提示
		ElMessage({
			message: '作品更新成功',
			type: 'success'
		})
	} catch (error) {
		console.error('更新作品失败:', error)
		ElMessage({
			message: '更新作品失败',
			type: 'error'
		})
	}
}

// 显示创建项目对话框
const showCreateProjectDialog = () => {
	createProjectDialogVisible.value = true
}

// 处理项目创建成功
const handleProjectCreated = async (projectData) => {
	try {
		console.log('收到的项目数据:', projectData)

		// 从嵌套结构中提取项目名称
		let projectName

		// 检查不同可能的数据结构
		if (typeof projectData === 'string') {
			// 如果直接是字符串
			projectName = projectData
		} else if (projectData && projectData.name && typeof projectData.name === 'object') {
			// 如果是 {name: {name: "项目名"}} 格式
			projectName = projectData.name.name
		} else if (projectData && projectData.name && typeof projectData.name === 'string') {
			// 如果是 {name: "项目名"} 格式
			projectName = projectData.name
		} else {
			console.error('无法识别的项目名称格式', projectData)
			return
		}

		// 调用创建项目API
		const params = {
			name: projectName,
			userId: getUserId()
		}

		console.log('发送的创建项目参数:', params)
		const response = await createAlbum(params)
		console.log('项目创建结果:', response)

		// 创建成功后，重新加载项目列表以获取最新数据
		loadProjectsList()
	} catch (error) {
		console.error('创建项目失败:', error)
	}
}

// 显示删除确认对话框
const showDeleteDialog = (item, type) => {
	currentDeleteItem.value = item
	deleteFileType.value = type // 'project' 或 'work'

	// 如果没有在调用处设置标题，则在这里设置默认标题
	if (!deleteItemTitle.value) {
		if (type === 'project') {
			deleteItemTitle.value = `${item.name || '此项目'}`
		} else if (type === 'work') {
			deleteItemTitle.value = `${item.title || '此作品'}`
		}
	}

	deleteDialogVisible.value = true
}

// 显示重命名对话框
const showRenameDialog = (item) => {
	currentRenameItem.value = item
	renameDialogVisible.value = true
}

// 修改项目下拉菜单点击事件
const handleProjectAction = (action, project) => {
	// 阻止对"全部作品"项目执行操作
	if (project.id === 'all') {
		ElMessage({
			message: '默认项目不可操作',
			type: 'warning'
		})
		return
	}

	if (action === 'delete') {
		// 为项目删除设置特定标题
		deleteItemTitle.value = `${project.name || '此项目'}`
		showDeleteDialog(project, 'project')
	} else if (action === 'rename') {
		showRenameDialog(project)
	}
}

// 修改确认删除的处理方法
const handleDeleteConfirm = async () => {
	if (deleteFileType.value === 'project') {
		try {
			// 调用删除项目API
			const params = {
				id: currentDeleteItem.value.id
			}

			console.log('删除项目参数:', params)
			const response = await deleteAlbum(params)
			console.log('删除项目结果:', response)

			// 删除成功后重新加载项目列表
			loadProjectsList()
			loadWorksListAfterDelete()
		} catch (error) {
			console.error('删除项目失败:', error)
			// 可以在这里添加错误提示
		}
	} else if (deleteFileType.value === 'work') {
		try {
			// 调用删除作品API
			const params = {
				id: currentDeleteItem.value.id
			}

			console.log('删除作品参数:', params)
			const response = await deleteWork(params)
			console.log('删除作品结果:', response)

			// 删除成功后重新加载作品列表，但不传递albumId参数
			await loadWorksListAfterDelete()
		} catch (error) {
			console.error('删除作品失败:', error)
			// 可以在这里添加错误提示
		}
	}
}

// 删除作品后加载作品列表的专用函数
const loadWorksListAfterDelete = async () => {
	try {
		isLoading.value = true

		const params = {
			work: {
				userId: getUserId()
				// 不传递albumId参数
			},
			pageParam: {
				pageNum: currentPage.value,
				pageSize: pageSize.value
			}
		}

		const response = await getByAlbum(params)
		console.log('删除作品后的作品列表数据:', response)

		// 如果API返回成功且有数据，更新作品列表
		if (response) {
			// 保存总记录数
			totalCount.value = response.total || 0

			if (response.list) {
				// 处理type字段，映射为对应的工作类型名称
				worksList.value = response.list.map(item => {
					// 根据type字段获取工作类型名称
					let workType = '未知'
					if (item.type !== undefined) {
						// 使用默认映射
						switch (String(item.type)) {
							case '0': workType = '一键成片'; break;
							case '1': workType = 'AI配音'; break;
							case '2': workType = '专业云剪'; break;
							case '3': workType = 'AI商配'; break;
							default: workType = '未知';
						}
					}

					return {
						...item,
						type: workType,
						typeId: item.type, // 保存原始的类型ID，用于筛选
						albumId: item.albumId, // 保留albumId字段
						duration: item.contentLength || '--',
						title: item.title || '--',
						project: item.albumName || '--',
						lastOpen: item.lastOpenTime || '--',
						speaker: item.voicePerson || '--',
						ossAudioUrl: item.ossAudioUrl || '', // 保留音频下载链接
						ossVideoUrl: item.ossVideoUrl || '', // 保留视频下载链接
						videoUrl: item.videoUrl || '', // 备选视频下载链接
						copywriting: item.copywriting || ''
					}
				}) || []
			} else {
				worksList.value = []
			}
		}
	} catch (error) {
		console.error('加载作品列表失败:', error)
		worksList.value = [] // 出错时清空列表
	} finally {
		isLoading.value = false
	}
}

// 添加取消删除的处理方法
const handleDeleteCancel = () => {
	// 关闭删除对话框并重置状态
	deleteDialogVisible.value = false
	deleteItemTitle.value = ''
	currentDeleteItem.value = {}
}

// 修改重命名确认处理函数
const handleRenameConfirm = async (newName) => {
	try {
		const item = currentRenameItem.value
		if (item && item.id) {
			// 调用重命名API
			const params = {
				id: item.id,
				name: newName,
				userId: getUserId()
			}

			console.log('发送的重命名参数:', params)
			const response = await updateAlbum(params)
			console.log('重命名结果:', response)

			// 重命名成功后刷新项目列表
			await loadProjectsList()

			// 如果重命名的是当前选中的项目，也更新选中项目的名称显示
			if (selectedProjectId.value === item.id) {
				selectedProjectName.value = newName
			}
		}
	} catch (error) {
		console.error('重命名项目失败:', error)
		// 可以在这里添加错误提示
	}
}

// 加载作品列表数据
const loadWorksList = async (type) => {
	try {
		isLoading.value = true
		const params = {
			work: { userId: getUserId() },
			pageParam: { pageNum: currentPage.value, pageSize: pageSize.value }
		}
		// 如果type不为空，则传递type参数
		if (type) {
			params.work.type = type
		}
		// 如图选中全部的话，type
		const response = await getByAlbum(params)


		// 如果API返回成功且有数据，更新作品列表
		if (response) {
			// 保存总记录数
			totalCount.value = response.total || 0

			if (response.list) {
				// 处理type字段，映射为对应的工作类型名称
				worksList.value = response.list.map(item => {
					// 根据type字段获取工作类型名称
					// 根据下拉菜单中的选项查找匹配的类型名称
					let workType = '未知'
					if (item.type !== undefined) {
						// 查找worksColumns中工作类型列
						const typeColumn = worksColumns.value.find(col => col.prop === 'type')
						if (typeColumn && typeColumn.options && typeColumn.options.length > 1) {
							// 从选项中查找匹配的类型
							const matchOption = typeColumn.options.find(opt => opt.value === String(item.type))
							if (matchOption) {
								workType = matchOption.label
							} else {
								// 使用默认映射
								switch (String(item.type)) {
									case '0': workType = '一键成片'; break;
									case '1': workType = 'AI配音'; break;
									case '2': workType = '专业云剪'; break;
									case '3': workType = 'AI商配'; break;
									default: workType = '未知';
								}
							}
						} else {
							// 使用默认映射
							switch (String(item.type)) {
								case '0': workType = '一键成片'; break;
								case '1': workType = 'AI配音'; break;
								case '2': workType = '专业云剪'; break;
								case '3': workType = 'AI商配'; break;
								default: workType = '未知';
							}
						}
					}

					// 判断类型，audio/video 格式化时长，否则直接显示
					const isAudioOrVideo = item.materialType === 'audio' || item.materialType === 'video';
					return {
						...item,
						type: workType,
						typeId: item.type,
						albumId: item.albumId,
						duration: isAudioOrVideo ? formatDuration(item.contentLength) : item.contentLength,
						title: item.title || '--',
						project: item.albumName || '--',
						lastOpen: item.lastOpenTime || '--',
						speaker: item.voicePerson || '--',
						copywriting: item.copywriting || ''
					}
				}) || []
			}
		}
	} catch (error) {
		console.error('加载作品列表失败:', error)
	} finally {
		isLoading.value = false
	}
}

// 添加选中项目状态
const selectedProjectId = ref(null)
const selectedProjectName = ref('')

// 处理项目点击事件
const handleProjectClick = async (project) => {
	try {
		// 取消查看全部单选
		showAllWorks.value = false;
		// 更新选中项目状态
		selectedProjectId.value = project.id
		selectedProjectName.value = project.name

		// 重置分页
		currentPage.value = 1

		// 如果是"全部作品"项目，加载所有作品
		if (project.id === 'all') {
			await loadWorksList()
		} else {
			// 否则调用API获取所选项目的作品
			await loadWorksByProject(project.id)
		}
	} catch (error) {
		console.error('加载项目作品失败:', error)
	}
}

// 加载指定项目的作品列表
const loadWorksByProject = async (projectId) => {
	try {
		isLoading.value = true

		const params = {
			work: {
				userId: getUserId(),
				albumId: projectId,
			},
			pageParam: {
				pageNum: currentPage.value,
				pageSize: pageSize.value
			}
		}
		// 判断一下如果currentTypeFilter?.value为空，则不传递type参数
		if (currentTypeFilter?.value) {
			params.work.type = currentTypeFilter?.value
		}

		const response = await getByAlbum(params)
		console.log('项目作品数据:', response)

		// 如果API返回成功，更新作品列表
		if (response) {
			// 保存总记录数
			totalCount.value = response.total || 0

			if (response.list) {
				// 处理返回的作品数据
				worksList.value = response.list.map(item => {
					// 根据type字段获取工作类型名称
					let workType = '未知'
					if (item.type !== undefined) {
						// 查找worksColumns中工作类型列
						const typeColumn = worksColumns.value.find(col => col.prop === 'type')
						if (typeColumn && typeColumn.options && typeColumn.options.length > 1) {
							// 从选项中查找匹配的类型
							const matchOption = typeColumn.options.find(opt => opt.value === String(item.type))
							if (matchOption) {
								workType = matchOption.label
							} else {
								// 使用默认映射
								switch (String(item.type)) {
									case '0': workType = '一键成片'; break;
									case '1': workType = 'AI配音'; break;
									case '2': workType = '专业云剪'; break;
									case '3': workType = 'AI商配'; break;
									default: workType = '未知';
								}
							}
						} else {
							// 使用默认映射
							switch (String(item.type)) {
								case '0': workType = '一键成片'; break;
								case '1': workType = 'AI配音'; break;
								case '2': workType = '专业云剪'; break;
								case '3': workType = 'AI商配'; break;
								default: workType = '未知';
							}
						}
					}

					// 判断类型，audio/video 格式化时长，否则直接显示
					const isAudioOrVideo = item.materialType === 'audio' || item.materialType === 'video';
					return {
						...item,
						type: workType,
						typeId: item.type,
						albumId: item.albumId || projectId, // 确保保留albumId字段，如果API没有返回，则使用当前项目ID
						duration: isAudioOrVideo ? formatDuration(item.contentLength) : item.contentLength,
						title: item.title || '--',
						project: item.albumName || selectedProjectName.value,
						lastOpen: item.lastOpenTime || '--',
						speaker: item.voicePerson || '--',
						ossAudioUrl: item.ossAudioUrl || '', // 保留音频下载链接
						ossVideoUrl: item.ossVideoUrl || '', // 保留视频下载链接
						videoUrl: item.videoUrl || '', // 备选视频下载链接
						copywriting: item.copywriting || ''
					}
				}) || []
			} else {
				// 如果没有作品数据，显示空列表
				worksList.value = []
			}
		}
	} catch (error) {
		console.error('加载项目作品失败:', error)
		worksList.value = [] // 出错时清空列表
	} finally {
		isLoading.value = false
	}
}

// 修改处理页码变更函数
const handlePageChange = (page, filterValues) => {
	currentPage.value = page
	// 判断一下如果filterValues.value为空，则不传递filterValues.value
	if (filterValues?.type) {
		currentTypeFilter.value = filterValues.type
	}
	// 如果选中项目，则加载指定项目的作品
	if (selectedProjectId.value) {
		loadWorksByProject(selectedProjectId.value)
	} else {
		// 如果filterValues.type为空，则不传递type参数
		loadWorksList(filterValues?.type)
	}
}

// 修改处理页大小变更函数
const handleSizeChange = (size, filterValues) => {
	pageSize.value = size
	currentPage.value = 1
	// 判断一下如果filterValues.value为空，则不传递filterValues.value
	if (filterValues?.type) {
		currentTypeFilter.value = filterValues.type
	}
	// 如果选中项目，则加载指定项目的作品
	if (selectedProjectId.value) {
		loadWorksByProject(selectedProjectId.value)
	} else {
		// 如果filterValues.type为空，则不传递type参数
		loadWorksList(filterValues?.type)
	}
}

// 加载项目列表数据
const loadProjectsList = async () => {
	try {
		const params = {
			userId: getUserId()
		}
		const response = await getAlbumsByCreator(params)
		console.log('项目列表数据:', response)

		// 只保留接口返回的项目
		if (response && response.length > 0) {
			projects.value = response.map(item => ({
				id: item.id,
				name: item.name || '未命名项目',
				updateTime: item.updateTime || '----'
			}))
			// 不再自动选中第一个项目
		} else {
			projects.value = []
		}
	} catch (error) {
		console.error('加载项目列表失败:', error)
		projects.value = []
	}
}

// 格式化日期时间，确保带有前导0的时分秒
const formatDateTime = (date) => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');

	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 修改处理移动确认的函数
const handleMoveConfirm = async (data) => {
	if (data.success) {
		// 移动成功后，重新加载当前项目的作品
		if (selectedProjectId.value) {
			await loadWorksByProject(selectedProjectId.value)
		} else {
			// 如果没有选中项目，则加载所有作品
			await loadWorksList()
		}
	}
}

// 添加处理单元格点击事件的方法
const handleCellClick = async (data) => {
	// 只处理工作类型相关事件
	if (data.column === 'type') {
		try {
			// 避免重复加载
			if (isLoading.value) return

			// 显示加载状态
			isLoading.value = true

			// 调用获取工作类型接口
			const params = { userId: getUserId() } // 根据实际需求传递参数
			const response = await getWorkTypes(params)
			console.log('工作类型数据:', response)

			// 处理API返回的特定格式数据：{0:"一键成片", 1:"AI配音", 2:"专业云剪", 3:"AI商配"}
			if (response) {
				// 构建选项数组，始终保留"全部"选项
				const typeOptions = [
					{ label: '全部', value: '' }
				]

				// 将API返回的工作类型数据添加到选项中
				// 这里处理的是格式为 {0:"一键成片", 1:"AI配音"...} 的数据
				Object.entries(response).forEach(([key, value]) => {
					// 使用字符串类型的key
					const stringKey = String(key)

					typeOptions.push({
						label: value, // 类型名称
						value: stringKey // 类型ID，确保是字符串类型
					})

					console.log(`添加工作类型选项: ${value}, 值: ${stringKey}, 类型: ${typeof stringKey}`)
				})

				// 更新工作类型列的选项
				const typeColumnIndex = worksColumns.value.findIndex(col => col.prop === 'type')
				if (typeColumnIndex !== -1) {
					// 判断选项是否有变化后再更新
					const currentOptions = worksColumns.value[typeColumnIndex].options || []
					const hasChanged = typeOptions.length !== currentOptions.length ||
						typeOptions.some((opt, idx) =>
							idx >= currentOptions.length || opt.label !== currentOptions[idx].label
						)

					if (hasChanged) {
						console.log('更新工作类型选项:', typeOptions)
						worksColumns.value[typeColumnIndex].options = typeOptions
					}
				}
			}
		} catch (error) {
			console.error('获取工作类型失败:', error)
		} finally {
			// 延迟关闭加载状态，避免闪烁
			setTimeout(() => {
				isLoading.value = false
			}, 300)
		}
	}
}

// 添加filter事件监听和处理方法
const handleFilter = async ({ prop, value }) => {
	console.log('筛选事件:', prop, value, typeof value)

	// 处理工作类型筛选
	if (prop === 'type') {
		// 重置到第一页
		currentPage.value = 1

		// 根据筛选条件重新加载数据
		if (selectedProjectId.value === 'all') {
			// 如果选择的是全部作品项目，加载全部作品并应用筛选
			await loadWorksListWithFilter(value)
		} else if (selectedProjectId.value) {
			// 如果有选中的项目，重新加载该项目的作品
			await loadWorksByProjectWithFilter(selectedProjectId.value, value)
		} else {
			// 否则加载全部作品并应用筛选
			await loadWorksListWithFilter(value)
		}
	}
}

// 修改loadWorksListWithFilter函数，确保保留albumId字段
const loadWorksListWithFilter = async (typeFilter) => {
	try {
		isLoading.value = true

		const params = {
			work: {
				userId: getUserId()
			},
			pageParam: {
				pageNum: currentPage.value,
				pageSize: pageSize.value
			}
		}
		console.log('typeFilter', typeFilter)
		// 添加类型筛选条件
		if (typeFilter !== undefined && typeFilter !== '') {
			// 确保typeFilter为0时转为字符串"0"
			params.work.type = typeFilter === '0' ? "0" : String(typeFilter)
		}

		const response = await getByAlbum(params)
		console.log('筛选后的作品列表数据:', response)

		// 处理响应数据，与loadWorksList方法相同的逻辑
		if (response) {
			totalCount.value = response.total || 0

			if (response.list) {
				// 使用相同的数据处理逻辑
				worksList.value = response.list.map(item => {
					// 根据type字段获取工作类型名称
					let workType = '未知'
					if (item.type !== undefined) {
						// 查找worksColumns中工作类型列
						const typeColumn = worksColumns.value.find(col => col.prop === 'type')
						if (typeColumn && typeColumn.options && typeColumn.options.length > 1) {
							// 从选项中查找匹配的类型
							const matchOption = typeColumn.options.find(opt => opt.value === String(item.type))
							if (matchOption) {
								workType = matchOption.label
							} else {
								// 使用默认映射
								switch (String(item.type)) {
									case '0': workType = '一键成片'; break;
									case '1': workType = 'AI配音'; break;
									case '2': workType = '专业云剪'; break;
									case '3': workType = 'AI商配'; break;
									default: workType = '未知';
								}
							}
						} else {
							// 使用默认映射
							switch (String(item.type)) {
								case '0': workType = '一键成片'; break;
								case '1': workType = 'AI配音'; break;
								case '2': workType = '专业云剪'; break;
								case '3': workType = 'AI商配'; break;
								default: workType = '未知';
							}
						}
					}

					return {
						...item,
						type: workType,
						typeId: item.type,
						albumId: item.albumId, // 确保保留albumId字段
						duration: item.contentLength || '--',
						title: item.title || '--',
						project: item.albumName || '--',
						lastOpen: item.lastOpenTime || '--',
						speaker: item.voicePerson || '--',
						ossAudioUrl: item.ossAudioUrl || '', // 保留音频下载链接
						ossVideoUrl: item.ossVideoUrl || '', // 保留视频下载链接
						videoUrl: item.videoUrl || '', // 备选视频下载链接
						copywriting: item.copywriting || ''
					}
				}) || []
			} else {
				worksList.value = []
			}
		}
	} catch (error) {
		console.error('加载筛选作品列表失败:', error)
	} finally {
		isLoading.value = false
	}
}

// 修改loadWorksByProjectWithFilter函数，确保保留albumId字段
const loadWorksByProjectWithFilter = async (projectId, typeFilter) => {
	try {
		isLoading.value = true

		const params = {
			work: {
				userId: getUserId(),
				albumId: projectId
			},
			pageParam: {
				pageNum: currentPage.value,
				pageSize: pageSize.value
			}
		}
		console.log('typeFilter', typeFilter)
		// 添加类型筛选条件
		if (typeFilter !== undefined && typeFilter !== '') {
			// 确保typeFilter为0时转为字符串"0"
			params.work.type = typeFilter === '0' ? "0" : String(typeFilter)
		}

		const response = await getByAlbum(params)
		console.log('筛选后的项目作品数据:', response)

		// 处理响应数据，与loadWorksByProject方法相同的逻辑
		if (response) {
			totalCount.value = response.total || 0

			if (response.list) {
				worksList.value = response.list.map(item => {
					// 根据type字段获取工作类型名称
					let workType = '未知'
					if (item.type !== undefined) {
						// 查找worksColumns中工作类型列
						const typeColumn = worksColumns.value.find(col => col.prop === 'type')
						if (typeColumn && typeColumn.options && typeColumn.options.length > 1) {
							// 从选项中查找匹配的类型
							const matchOption = typeColumn.options.find(opt => opt.value === String(item.type))
							if (matchOption) {
								workType = matchOption.label
							} else {
								// 使用默认映射
								switch (String(item.type)) {
									case '0': workType = '一键成片'; break;
									case '1': workType = 'AI配音'; break;
									case '2': workType = '专业云剪'; break;
									case '3': workType = 'AI商配'; break;
									default: workType = '未知';
								}
							}
						} else {
							// 使用默认映射
							switch (String(item.type)) {
								case '0': workType = '一键成片'; break;
								case '1': workType = 'AI配音'; break;
								case '2': workType = '专业云剪'; break;
								case '3': workType = 'AI商配'; break;
								default: workType = '未知';
							}
						}
					}

					return {
						...item,
						type: workType,
						typeId: item.type,
						albumId: item.albumId || projectId, // 确保保留albumId字段，如果API没有返回，则使用当前项目ID
						duration: item.contentLength || '--',
						title: item.title || '--',
						project: item.albumName || selectedProjectName.value,
						lastOpen: item.lastOpenTime || '--',
						speaker: item.voicePerson || '--',
						ossAudioUrl: item.ossAudioUrl || '', // 保留音频下载链接
						ossVideoUrl: item.ossVideoUrl || '', // 保留视频下载链接
						videoUrl: item.videoUrl || '', // 备选视频下载链接
						copywriting: item.copywriting || ''
					}
				}) || []
			} else {
				worksList.value = []
			}
		}
	} catch (error) {
		console.error('加载筛选项目作品失败:', error)
		worksList.value = []
	} finally {
		isLoading.value = false
	}
}

// 添加行点击处理函数
const handleRowClick = (row) => {
	console.log('Row clicked:', row)

	// 检查row对象中是否有copywriting字段，如果有则保存到previewStore
	if (row) {
		// 导入previewStore
		import('@/stores/previewStore').then(module => {
			const { usePreviewStore } = module;
			const previewStore = usePreviewStore();

			// 将title字段保存到previewStore
			if (row.title && row.title !== '--') {
				previewStore.setTitle(row.title);
				console.log('已保存标题到previewStore:', row.title);
			}

			// 将copywriting字段值保存到previewStore中
			if (row.copywriting) {
				previewStore.setContent(row.copywriting);
				console.log('已保存文案内容到previewStore:', row.copywriting);
			}
		}).catch(error => {
			console.error('保存数据到previewStore失败:', error);
		});
	}

	// 确保row有typeId属性，这是原始的类型ID
	if (!row || !row.typeId) {
		console.warn('无法获取作品类型，无法进行跳转')
		return
	}

	// 确保row有albumId属性
	if (!row.albumId) {
		console.warn('无法获取作品所属项目ID，无法进行跳转')
		return
	}

	// 根据作品类型跳转到不同路由
	const typeId = String(row.typeId)
	const albumId = row.id
	// 获取行数据中的id字段，用于传递给预览区域
	const id = row.id || ''
	// 从 API 返回的原始数据中获取 projectId
	const projectId = row.projectId || ''

	console.log('跳转参数:', { typeId, albumId, projectId, id, row })

	// 根据类型ID跳转到对应路由
	switch (typeId) {
		case '0': // 一键成片
			router.push({
				name: 'VideoEditing',
				query: { albumId, id, activeTool: 2 }
			})
			break
		case '1': // AI配音
			// 从行数据中获取voicePerson信息并设置soundStore数据
			console.log(row, 'row数据:', row)

			// 判断是否为克隆项目（source === 2）
			if (row.source === 2) {
				console.log('检测到克隆项目，使用克隆跳转逻辑', row)

				// 构造克隆音色数据，参考克隆列表的数据结构
				const cloneVoiceData = {
					id: row.vedioId || row.voiceId, // 音色ID
					platformNickname: row.voicePerson || row.voiceName || '我的克隆音色', // 音色名称
					avatarUrl: row.vedioPersonImg || row.avatarUrl || '', // 头像
					audioUrl: row.ossAudioUrl || row.audioSrc, // 音频URL
					// 其他可能需要的字段
					voiceName: row.voicePerson || row.voiceName || '我的克隆音色'
				}

				console.log('设置克隆数据到soundStore:', cloneVoiceData)
				soundStore.setCloneData(cloneVoiceData)

				// 跳转到AI配音页面，传递克隆参数
				router.push({
					name: 'AIDubbing',
					query: {
						clone: true,
						voiceId: cloneVoiceData.id,
						albumId,
						projectId
					}
				})
			} else {
				// 普通项目的跳转逻辑（原有逻辑）
				console.log('普通项目跳转AI配音')
				if (row.voicePerson || row.speaker) {
					// 将vedioId重命名为id，其他字段保持不变
					const { vedioId, ...otherFields } = row
					const voiceData = {
						...otherFields, // 先保留其他所有字段
						id: vedioId // 将vedioId的值赋给id（放在后面确保覆盖原来的id）
					}
					console.log('设置soundStore数据:', voiceData)
					soundStore.setChooseData(voiceData)
				}
				router.push({
					name: 'AIDubbing',
					query: { albumId, projectId, choose: true }
				})
			}
			break
		case '3': // AI商配
			// 从行数据中获取voicePerson信息并设置soundStore数据
			console.log(row, 'AI商配row数据:', row)
			if (row.voicePerson || row.speaker) {
				// 将vedioId重命名为id，其他字段保持不变
				const { vedioId, ...otherFields } = row
				const voiceData = {
					...otherFields, // 先保留其他所有字段
					id: vedioId // 将vedioId的值赋给id（放在后面确保覆盖原来的id）
				}
				console.log('设置soundStore数据(AI商配):', voiceData)
				soundStore.setChooseData(voiceData)
			}
			router.push({
				name: 'commercialDubbing',
				query: { batchId: row.batchId, status: row.status, choose: true }
			})
			break
		case '2': // 专业云剪
			// 从localStorage获取token
			let token = ""
			try {
				const userStr = localStorage.getItem('user')
				if (userStr) {
					const userObj = JSON.parse(userStr)
					token = userObj.token || ""
				}
			} catch (error) {
				console.error('获取token失败:', error)
			}
			// 确保使用 API 返回的 projectId
			window.location.href = `https://yunjian.peiyinbangshou.com/App?token=${token}&projectId=${projectId}`
			break
		default:
			console.warn(`未知的工作类型: ${typeId}，无法进行跳转`)
	}
}

// 修改处理查看全部复选框切换
const handleShowAllWorksChange = async (checked) => {
	// 每次选中时都触发作品列表API请求
	if (checked) {
		// 重置相关状态
		selectedProjectId.value = null;
		selectedProjectName.value = '全部项目';
		currentPage.value = 1;

		// 记录本次请求时间
		const currentTime = Date.now()
		lastAllWorksRequestTime.value = currentTime

		// 强制触发作品列表接口
		console.log('查看全部选中，触发作品列表请求')
		await loadWorksList();
	}
	// 取消选中时不做任何数据请求
}

// 格式化持续时间的辅助函数
const formatDuration = (seconds) => {
	if (!seconds) return '--';
	const mins = Math.floor(seconds / 60);
	const secs = Math.floor(seconds % 60);
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 在组件挂载时加载数据
onMounted(() => {
	// 设置选中全部
	selectedProjectId.value = null
	selectedProjectName.value = '全部项目'

	loadWorksList()
	loadProjectsList()
	// 初始加载工作类型数据
	handleCellClick({ column: 'type' })
})
</script>

<style lang="scss" scoped>
.my-works-container {
	padding: 20px;
	background-color: #fff;
	height: 100%;
	/* 保持100%高度 */
	max-height: 100%;
	/* 添加最大高度限制 */
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.projects-section,
	.works-section {
		padding: 0 20px;
	}

	.projects-section {
		flex-shrink: 0;
		/* 防止项目区域被压缩 */
		margin-bottom: 0px;
		/* 增加项目区域和作品区域间的间距 */
		/* 确保项目区域有固定高度 */
		height: auto;
		overflow: visible;

		// 新建项目按钮颜色
		.section-header {
			.el-button.add-btn {
				width: 82px;
				height: 26px;
				font-size: 14px;
				margin: 0 30px 0 0;
				--el-button-border-color: #09A058;
				--el-button-text-color: #09A058;
				--el-button-bg-color: #FFFFFF;
			}

			.el-button.add-btn .el-icon {
				color: #09A058;
			}

			.el-button.add-btn:hover {
				border-color: #09A058;
				color: #09A058;
			}
		}
	}

	.project-cards {
		display: flex;
		gap: 16px;
		margin-bottom: 15px;
		margin-top: 30px;
		/* 减少底部边距 */
		padding: 0;
		flex-wrap: wrap;
		/* 新增样式：限制高度为两行项目卡片 */
		max-height: 200px;
		/* 每个卡片高度约80px + 上下间距16px = 96px，两行约176px */
		overflow-y: auto;
		/* 添加垂直滚动条 */
		width: 100%;

		/* 确保宽度占满 */
		/* 美化滚动条 */
		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #C0C4CC;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #F5F7FA;
		}

		.project-card {
			width: 240px;
			height: 61px;
			border-radius: 5px;
			cursor: pointer;
			border: 1px solid rgba(99, 114, 130, 0.16);
			box-sizing: border-box;
			transition: all 0.2s ease;
			position: relative;
			/* 添加固定高度确保每行高度一致 */
			height: 80px;
			margin-bottom: 8px;

			&:hover {
				box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
				border: 1px solid #DCDFE6;
			}

			/* 为"全部作品"项目添加特殊样式 */
			&.all-works-project {

				.project-name {
					color: #0AAF60;
					font-weight: 600;
				}
			}

			:deep(.el-card__body) {
				display: flex;
				// align-items: center;
				padding: 16px;
				height: 100%;
				/* 确保内容填充整个卡片高度 */
				box-sizing: border-box;
			}

			.project-icon {
				margin: 3px 12px 0 0;
			}

			.project-info {
				flex: 1;

				.project-name {
					font-size: 14px;
					color: #1A1918;
					margin-bottom: 4px;
				}

				.project-date {
					font-size: 12px;
					color: #B4B4B4;
				}
			}

			.more-icon {
				cursor: pointer;
				padding: 4px;
				width: 20px;
				height: 20px;
				outline: none;
				border: none;

				&:hover,
				&:focus {
					outline: none;
					border: none;
				}
			}

			// 修改选中项目的样式 - 使用伪元素而不是改变边框宽度
			&.active-project {
				background-color: rgba(10, 175, 96, 0.05);
				border-color: #0AAF60;

				// 使用伪元素添加边框效果而不是改变原始边框宽度
				// &::after {
				// 	content: '';
				// 	position: absolute;
				// 	top: -1px;
				// 	left: -1px;
				// 	right: -1px;
				// 	bottom: -1px;
				// 	border: 2px solid #0AAF60;
				// 	pointer-events: none;
				// 	z-index: 1;
				// }
			}
		}
	}

	.section-header {
		margin-bottom: 10px;
		display: flex;
		align-items: center;
		height: 32px;

		.section-title {
			margin-right: 8px;
			font-size: 24px;
			color: #001126;
			line-height: 32px;
			display: inline-block;
			vertical-align: middle;
		}
		
		.section-subtitle{
			margin-right: 40px;
			font-size: 16px;
			color: #001126;
			font-weight: 500;
		}

		.tabBox{
			display: flex;
			align-items: center;
			font-size: 14px;
			color: #353D49;

			span{
				margin: 0 25px 0 0;
				cursor: pointer;
			}

			span.active{
				color: #0AAF60;
			}

		}

		.verticalLine {
			width: 1px;
			height: 16px;
			background: #DDE1EA;
			margin: 0 20px 0 12px;
		}

		.selected-project-info {
			margin-right: 8px;
			font-size: 14px;
			color: #606266;
		}

		.el-button {
			--el-button-border-color: #09A058;
			--el-button-text-color: #09A058;
			--el-button-bg-color: #FFFFFF;
			width: 93px;
			height: 30px;
			padding: 0;
			line-height: 28px;

			&:hover {
				background-color: #FFFFFF;
				border-color: #09A058;
				color: #09A058;
			}

			.el-icon {
				color: #09A058;
			}
		}

		.view-all-checkbox {
			margin: 0;
			font-size: 14px;

			// 默认文字颜色为橙色
			:deep(.el-checkbox__label) {
				color: #353D49;
				font-size: 14px;
			}

			// 选中时只改变复选框本体
			:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
				border-color: #09A058;
				background-color: #09A058;
			}
		}
	}

	.works-section {
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		min-height: 0;
		max-height: calc(100% - 140px);
		/* 限制作品区域最大高度，给项目区域留出空间 */

		/* 确保CustomTable能够在works-section中正确适应高度 */
		:deep(.custom-table-container) {
			height: 100%;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				min-height: 0;
				overflow-y: auto; 
			}

			.pagination-container {
				margin-top: 10px;
				/* 减少分页器上边距 */
				flex-shrink: 0;
			}
		}

		// 查看全部复选框选中样式
		.section-header {
			.view-all-checkbox {
				:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
					border-color: #09A058;
					background-color: #09A058;
				}
			}
		}
	}
}
</style>