<template>
    <div class="parser-container">
        <div class="cont">
            <!-- 标题 -->
            <h2 class="parser-title">一键解析视频</h2>

            <!-- 输入框组件 -->
            <div class="sharing" :class="{ 'is-parsing': isParsing }">
                <div class="sharing_caption">
                    <span>*</span>
                    <span>视频链接：</span>
                </div>
                <div class="child">
                    <CommonInput class="parent-wrapper" :placeholder="placeholder" v-model="videoUrl"
                        @parse="handleParse" @clear="handleClear" :disabled="isParsing" />
                </div>
            </div>

            <!-- 解析按钮及状态 -->
            <div class="action-section">
                <el-button :disabled="isParsing" :loading="isParsing" type="primary" @click="handleParse">
                    {{ isParsing ? "解析中..." : "解析链接" }}
                </el-button>
                <transition name="el-fade-in">
                    <p v-show="isParsing" class="parsing-tip">
                        <span class="loading-dots"></span>
                        解析中，请耐心等待
                    </p>
                </transition>
            </div>


            <!-- 生成结果 -->
            <div v-show="isParsingResult" class="consists">
                <ConsistsChild :videoInfo="videoInfo" @handleCopy="handleCopyTitle" @download="handleDownload" />
            </div>
        </div>

        <!-- 底部更多推荐 -->
        <FooterChild :show-cloud-button="false" :show-recommend="true" :show-watermark-button="true" :show-extraction-button="false" :video-url="videoInfo.videoUrl" :video-title="videoInfo.title || videoInfo.name" />

        <!-- 预览层 -->
        <!-- 原有模板不变，新增预览层 -->
        <div v-if="showPreview" class="preview-overlay" @click.self="closePreview">
            <div class="preview-content">
                <!-- 视频预览 -->
                <video v-if="previewType === 'video'" ref="previewVideo" controls autoplay class="preview-media">
                    <source :src="previewUrl" type="video/mp4" />
                </video>

                <!-- 封面预览 -->
                <img v-else :src="previewUrl" alt="全屏预览" class="preview-media" />

                <el-button class="close-btn" type="danger" circle @click="closePreview">
                    <el-icon>
                        <Close />
                    </el-icon>
                </el-button>
            </div>
        </div>

        <!-- 添加会员限制弹窗 -->
        <AlertDialog 
            v-model:visible="showLimitDialog"
            type="warning"
            title="会员功能"
            message="非会员每日只能使用15次，请开通会员使用"
            confirm-button-text="开通会员"
            cancel-button-text="我知道了"
            :show-cancel-button="true"
            :custom-confirm-class="true"
            :custom-cancel-class="true"
            :show-fee-explanation="false"
            @confirm="handleOpenMember"
            @cancel="handleCloseLimitDialog"
        />
    </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { ElButton, ElMessage } from "element-plus";
import { VideoCamera, Close } from "@element-plus/icons-vue";
import CommonInput from "../../../components/CommonInput/index.vue";
import ConsistsChild from "../../../components/ConsistsChild/index.vue";
import FooterChild from "../../../components/FooterChild/index.vue";
import { parsingVideo } from "@/api/parsingVideo";
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import { useRouter } from 'vue-router'; // 引入路由
import { usePreviewStore } from '@/stores/previewStore'; // 导入预览store
// 引入友盟埋点Hook和模块
import { useUmeng } from '@/utils/umeng/hook';
import { createParsingAnalytics } from '@/utils/umeng/modules/parsing';

const { proxy } = getCurrentInstance();
const videoUrl = ref("");
const isParsing = ref(false);
const placeholder = ref("请输入短视频链接");

// 获取路由实例
const router = useRouter();
// 添加会员限制相关状态
const showLimitDialog = ref(false);

// 初始化友盟埋点
const umeng = useUmeng();
const parsingAnalytics = createParsingAnalytics(umeng);

// 页面加载时记录PV
onMounted(() => {
  parsingAnalytics.trackParsingPageView();
});

// 新增：会员限制处理方法
const handleOpenMember = () => {
    // 关闭弹窗
    showLimitDialog.value = false;
    
    // 导航到会员页面
    try {
        // 判断是否在layout布局内
        if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
            // 如果在layout布局内，使用内部路由导航
            router.push({ name: 'membership-nav' });
        } else {
            // 否则通过URL跳转
            window.location.href = '/membership';
        }
        ElMessage.success("正在跳转到会员页面");
    } catch (error) {
        console.error("导航到会员页面失败:", error);
        ElMessage.error("导航到会员页面失败，请手动前往会员中心");
    }
};

const handleCloseLimitDialog = () => {
    // 关闭会员限制弹窗
    showLimitDialog.value = false;
};

// 新增状态
const showPreview = ref(false);
const previewType = ref("video"); // video/cover
const previewUrl = ref("");
const previewVideo = ref(null);


const isParsingResult = ref(false);

// 添加视频信息响应式对象
const videoInfo = ref({
    name: "已成功提取视频内容",
    title: "",
    duration: "",
    size: "",
    videoUrl: '',
    coverUrl: "videoUrl",
});

const previewStore = usePreviewStore(); // 初始化预览store

// 预览处理
const handlePreview = (type, url) => {
    previewType.value = type;
    previewUrl.value = url
    showPreview.value = true;

    // 自动全屏（需要用户手势）
    if (type === "video" && previewVideo.value) {
        try {
            previewVideo.value.requestFullscreen();
        } catch (e) {
            console.error("全屏失败:", e);
        }
    }
};

// 关闭预览
const closePreview = () => {
    showPreview.value = false;
    if (document.fullscreenElement) {
        document.exitFullscreen();
    }
};

// 清空
const handleClear = () => {
    videoUrl.value = "";
    isParsing.value = false;
};

// 判断用户是否已登录
const checkUserLogin = () => {
    // 从本地存储获取user信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;
    
    try {
        const userData = JSON.parse(userStorage);
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== '';
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
};

// 检查会员权限状态码
// const checkMemberLimit = (statusCode) => {
//     // 如果状态码是301、307或310，表示会员限制
//     if (statusCode === 307) {
//         showLimitDialog.value = true;
//         return true; // 返回true表示存在会员限制
//     }
//     return false; // 返回false表示没有会员限制
// };

// 修改解析函数
const handleParse = async () => {
    // 埋点：点击解析按钮
    parsingAnalytics.trackParseButtonClick();
    
    // 如果没有提供视频URL，显示提示并返回
    if (!videoUrl.value.trim()) {
        ElMessage.warning("请输入视频链接");
        return;
    }
    
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }
    
    // 开始新的解析前，先隐藏之前的结果
    isParsingResult.value = false;
    isParsing.value = true;

    try {
        // 清空之前的视频信息
        videoInfo.value = {
            name: "已成功提取视频内容",
            title: "",
            duration: "",
            size: "",
            videoUrl: "",
            coverUrl: "",
        };

        // 调用解析API
        const res = await parsingVideo({ url: videoUrl.value });
        
        // 检查会员限制状态码
        // if (checkMemberLimit(res.status_code)) {
        //     isParsing.value = false;
        //     return;
        // }
        
        // 更新视频信息
        videoInfo.value = {
            name: "已成功提取视频内容",
            title: res.content.result.title,
            videoUrl: res.content.result.video,
            coverUrl: res.content.result.thumbnail,
        };
        
        // 更新预览URL
        previewUrl.value = res.content.result.thumbnail;
        
        // 更新状态
        isParsing.value = false;
        isParsingResult.value = true;
        
        // 将视频信息保存到Pinia store中
        previewStore.setParsedVideoInfo(res.content.result);
        console.log('视频信息已保存到Pinia store:', res.content.result);
        
        ElMessage.success("解析成功");
    } catch (error) {
        console.error("视频解析失败:", error);
        
        // 检查错误是否包含状态码
        // if (error && error.response) {
        //     checkMemberLimit(error.response.status_code);
        // } else {
            ElMessage.error("解析失败，请检查链接是否有效");
        // }
        
        isParsing.value = false;
        // 清空Pinia中的视频信息
        previewStore.clearParsedVideoInfo();
    }
};

// 修改复制函数
const handleCopyTitle = async (videoInfoData) => {
    // 埋点：复制标题
    parsingAnalytics.trackCopyTitle();
    
    // 获取要复制的文本
    const textToCopy = videoInfoData.title || videoInfo.value.title;
    if (!textToCopy) {
        ElMessage.warning("暂无可复制的标题");
        return;
    }

    // 先尝试使用现代剪贴板API
    try {
        console.log("正在尝试复制文本:", textToCopy);
        await navigator.clipboard.writeText(textToCopy);
        ElMessage.success("标题已复制到剪贴板");
        return; // 如果成功，直接返回
    } catch (err) {
        console.warn("使用现代剪贴板API失败:", err);
        // 继续尝试备用方法
    }

    // 备用方法：使用document.execCommand
    try {
        // 创建临时文本区域
        const textArea = document.createElement("textarea");
        textArea.value = textToCopy;
        
        // 设置样式使元素不可见
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        // 执行复制命令
        const successful = document.execCommand('copy');
        
        // 移除临时元素
        document.body.removeChild(textArea);
        
        if (successful) {
            ElMessage.success("标题已复制到剪贴板");
        } else {
            throw new Error("execCommand返回失败");
        }
    } catch (err) {
        console.error("所有复制方法都失败:", err);
        ElMessage.error("复制失败，请手动选中文字复制");
    }
};

// 新增下载处理函数
const handleDownload = async (type, videoInfo) => {
    // 埋点：下载视频或封面
    if (type === "video") {
        parsingAnalytics.trackDownloadVideo();
    } else {
        parsingAnalytics.trackDownloadCover();
    }
    
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }
    
    try {
        // 先检查下载权限(可以调用解析接口再次验证用户权限)
        // const checkPermission = await parsingVideo({ 
        //     url: videoUrl.value,
        //     checkPermission: true // 添加一个标识，表示只是检查权限
        // });
        
        // 检查会员限制状态码
        // if (checkMemberLimit(checkPermission.status_code)) {
        //     return;
        // }
        
        // 创建隐藏的下载链接
        let url = type === "video" ? videoInfo.videoUrl : videoInfo.coverUrl;
        
        // 检查URL是否存在
        if (!url) {
            ElMessage.warning(`无法下载，${type === "video" ? "视频" : "封面"}链接不存在`);
            return;
        }
        
        const a = document.createElement("a");
        a.href = url;
        a.download = `${videoInfo.title || "未命名文件"}.${type === "video" ? "mp4" : "jpg"}`;

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        ElMessage.success("开始下载");
    } catch (error) {
        console.error("下载失败:", error);
        
        // 检查错误是否包含状态码
        // if (error && error.response) {
        //     checkMemberLimit(error.response.status_code);
        // } else {
            ElMessage.error("下载失败，请重试");
        // }
    }
};
</script>

<style lang="scss" scoped>
.parser-container {
    .cont {
        margin-left: 20px;

        // 更新呼吸效果动画样式
        .parsing-tip {
            margin-top: 8px;
            color: #666666;
            font-size: 14px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: breathing 1.5s ease-in-out infinite;

            .loading-dots {
                display: inline-block;
                position: relative;
                width: 16px;
                height: 16px;
                top: -2px;
                &::before {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #18ad25;
                    animation: dots-bounce 1s infinite ease-in-out;
                }

                &::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #18ad25;
                    animation: dots-bounce 1s infinite ease-in-out;
                    animation-delay: 0.5s;
                    opacity: 0;
                }
            }
        }

        @keyframes breathing {
            0% {
                opacity: 0.4;
            }

            50% {
                opacity: 1;
            }

            100% {
                opacity: 0.4;
            }
        }

        @keyframes dots-bounce {

            0%,
            100% {
                transform: scale(0);
                opacity: 0.5;
            }

            50% {
                transform: scale(1);
                opacity: 1;
            }
        }
    }

    .sharing {
        display: flex;
        margin-top: 20px;
        position: relative;

        &.is-parsing {
            .child {
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.7);
                    z-index: 1;
                }

                animation: parsing-pulse 1.5s infinite;
            }
        }

        .child {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 37%;

            :deep(.el-input__wrapper) {
                border: none !important;
                box-shadow: none !important;

                // 去除hover状态边框
                &:hover {
                    box-shadow: none !important;
                }

                // 去除聚焦状态边框
                &.is-focus {
                    box-shadow: none !important;
                }
            }

            :deep(.image-container) {
                height: 150px;
                border: 1px solid #eeeef0;
                background-color: #fff;
            }

            :deep(.action-buttons) {
                bottom: -107px;
            }

            .btn {
                width: 100px;
                height: 30px;
                cursor: pointer;
                margin-top: 20px;
                color: #fff;
                text-align: center;
                border-radius: 5px;
                line-height: 30px;
                background-color: #18ad25;
            }
        }

        .parser-title {
            font-weight: 600;
            color: #333;
        }
    }

    .action-section {
        margin: 20px 0 0 75px;

        .el-button {
            width: 140px;
            height: 40px;
            font-size: 14px;
            transition: all 0.3s;
            background-color: #18ad25;
            border: 1px solid #18ad25;

            &:disabled {
                opacity: 0.7;
                cursor: not-allowed;
            }
        }

        .parsing-tip {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
            animation: pulse 1.5s infinite;
        }
    }

    .consists {
        width: 65%;
    }



    // .bottom {
    //     margin-top: 70px;
    //     .recommend-container {
    //         height: 70px;
    //         display: flex;
    //         align-items: center;
    //         // gap: 16px;
    //         // padding: 12px;
    //         background: #fdfdfd;
    //         border-radius: 6px;

    //         .recommend-title {
    //             padding-left: 10px;
    //             font-size: 14px;
    //             color: #606266;
    //             white-space: nowrap;
    //         }

    //         .button-group {
    //             margin-left: 18px;
    //             display: flex;
    //             gap: 8px;

    //             .recommend-btn {
    //                 padding: 8px 16px;
    //                 border-radius: 4px;
    //                 border: 1px solid #dcdcdc;
    //                 background: white;
    //                 transition: all 0.2s;

    //                 &:hover {
    //                     border: 1px solid #dcdcdc;
    //                     box-shadow: 0 2px 8px rgba(32, 160, 255, 0.1);
    //                 }

    //                 .btn-content {
    //                     display: flex;
    //                     align-items: center;
    //                     font-size: 14px;
    //                     color: #303133;

    //                     .arrow {
    //                         margin-left: 8px;
    //                         font-weight: 600;
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    
}


@keyframes pulse {
    0% {
        opacity: 0.9;
    }

    50% {
        opacity: 0.6;
    }

    100% {
        opacity: 0.9;
    }
}

@keyframes parsing-pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}
</style>