<template>
  <div class="allRealVoice">
    <Header :fixedHeader="true" />
    <div class="allReal_voice_contanier">
      <filterCriteria
        ref="filter_criteria_ref"
        @choose_nav="choose_nav"
      />
      <PopularLiveStreamers ref="popular_live_streamers_ref" />
    </div>
    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick,onDeactivated, onActivated } from 'vue'
import Header from "@/views/modules/mainPage/components/headbar/index.vue"
import filterCriteria from './components/allRealVoice/filterCriteria.vue'
import PopularLiveStreamers from "./components/index/popularLivesStreamers.vue"
import Footer from '@/views/modules/H4Home/components/footer.vue'
import { getAllForReal } from "@/api/realVoice.js"

const filter_criteria_ref = ref(null)
const popular_live_streamers_ref = ref(null)

const data = ref([]) // 原始列表数据
const filterCriteriaObj = reactive({
  voicePriceType: '全部',
  voiceLevel: '全部',
  gender: '全部',
  language: '全部',
  recommendTags: '全部'
})

const layers = ['voicePriceType', 'voiceLevel', 'gender', 'language', 'recommendTags']
const fieldNames = {
  voicePriceType: "筛选",
  voiceLevel: "级别",
  gender: "音色",
  language: "语言",
  recommendTags: "分类"
}

// 字符串分割成标签数组
const splitTags = (str) => {
  if (!str) return []
  return str.split(/[、，,]/).map(t => t.trim()).filter(Boolean)
}

// 根据当前筛选条件过滤数据
const filterDataByCriteria = (dataArr, criteria) => {
  return dataArr.filter(item => {
    return layers.every(field => {
      const selected = criteria[field]
      if (selected === '全部') return true
      const tags = splitTags(item[field])
      return tags.includes(selected)
    })
  })
}

// 根据数据和当前筛选条件，生成导航数据（筛选条件选项），用于赋值给子组件的 filter_criteria
const generateNavData = (dataArr, criteria) => {
  console.log(dataArr, criteria,'generateNavData');
  
  const navData = {}
  // 先过滤数据，满足前面层级的筛选条件
  for (let i = 0; i < layers.length; i++) {
    const field = layers[i]
    // 过滤数据满足前面层级条件
    const filtered = dataArr.filter(item => {
      for (let j = 0; j < i; j++) {
        const preField = layers[j]
        const selected = criteria[preField]
        if (selected !== '全部') {
          const tags = splitTags(item[preField])
          if (!tags.includes(selected)) return false
        }
      }
      return true
    })

    // 收集当前层级所有标签
    const tagSet = new Set()
    filtered.forEach(item => {
      splitTags(item[field]).forEach(tag => tagSet.add(tag))
    })

    // 生成选项数组，包含“全部”
    const arr = [{ key: '全部', label: '全部' }]
    tagSet.forEach(tag => {
      arr.push({ key: tag, label: tag })
    })

    navData[field] = {
      name: fieldNames[field],
      arr
    }
  }
  return navData
}

// 初始化数据和导航
const initDataAndNav = async () => {
  popular_live_streamers_ref.value.list_loading=true
  const res = await getAllForReal({ tts: 6 })
console.log(res,'res');

  if(Array.isArray(res.data)){
    data.value = res.data || []

  }else{
     data.value = res || []
  }
 
  // 先生成导航数据
  const navData = generateNavData(data.value, filterCriteriaObj)

  // 通过ref调用子组件方法，赋值filter_criteria
  await nextTick()
  if (filter_criteria_ref.value && filter_criteria_ref.value.setFilterCriteria) {
    filter_criteria_ref.value.setFilterCriteria(navData)
  }

  // 初始化列表
  if (popular_live_streamers_ref.value && popular_live_streamers_ref.value.init) {
    popular_live_streamers_ref.value.init(data.value)
  }
}

// 处理子组件点击筛选项事件
const choose_nav = async ({ layer, key }) => {
  popular_live_streamers_ref.value.close_aduio()
  if (!layers.includes(layer)) return
  filterCriteriaObj[layer] = key

  // 过滤数据
  const filtered = filterDataByCriteria(data.value, filterCriteriaObj)

  // 重新生成导航数据
  const navData = generateNavData(data.value, filterCriteriaObj)

  // 赋值给子组件filter_criteria，实现联动更新
  if (filter_criteria_ref.value && filter_criteria_ref.value.setFilterCriteria) {
    filter_criteria_ref.value.setFilterCriteria(navData)
  }

  // 刷新列表
  if (popular_live_streamers_ref.value && popular_live_streamers_ref.value.init) {
    popular_live_streamers_ref.value.init(filtered)
  }
}

onMounted(() => {
  filter_criteria_ref.value.loading=true
  initDataAndNav()
})
onDeactivated(()=>{
 
  
  popular_live_streamers_ref.value.close_aduio()
})
</script>

<style scoped lang="scss">
.allRealVoice {
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  overflow-x: clip;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  min-height: 100vh;
  .allReal_voice_contanier {
    padding-top: 60px;
    background-color: #fff;
  }
}
</style>
