<template>
    <div class="filterCriteria" v-loading="loading">
        <div class="filterCriteria_contanier">
            <div class="filterCriteria_filter" v-for="(item,index) in Object.keys(filter_criteria)" :key="index">
                <!-- <span class="filterCriteria_filter_title">{{ filter_criteria[item].name }}</span> -->
                <span class="filterCriteria_filter_item" :class="current_tag[item]==item1.key?'current':''" @click="select_tag(item,item1,index)" v-for="(item1,index1) in  filter_criteria[item].arr" :key="index1">{{ item1.label }}</span>
            </div>
        </div>
    </div>
</template>
<script setup>
import {reactive,ref,defineExpose,onMounted,defineEmits} from 'vue'
let emit=defineEmits('choose_nav')
let filter_criteria=ref({
    voicePriceType: {
        name: "筛选",
        arr: [

           
        ]
    },
    voiceLevel: {
        name: "级别",
        arr: [

           
        ]
    },
    gender: {
        name: "音色",
        arr: [

           
        ]
    },
    language: {
        name: "语言",
        arr: [

        ]
    },
    recommendTags: {
        name: "分类",
        arr: [
          
        ]
    },
   
})
let loading=ref(false)
// 父组件通过ref调用此方法，赋值导航数据
let setFilterCriteria = (navData) => {
    console.log(navData,'navData');
  // 清空旧数据
  Object.keys(filter_criteria.value).forEach(key => delete filter_criteria.value[key])
  // 赋值新数据
  Object.assign(filter_criteria.value, navData)

  // 初始化current_tag.value，保持选中状态，如果之前没选过，默认选“全部”
  Object.keys(navData).forEach(field => {
    if (!current_tag.value[field]) {
        current_tag.value[field] = '全部'
    } else {
      // 如果当前选中值不在新选项中，重置为全部
      const keys = navData[field].arr.map(item => item.key)
      if (!keys.includes(current_tag.value[field])) {
        current_tag.value[field] = '全部'
      }
    }
  })
  loading.value=false
}
let current_tag=ref({})
let select_tag=(item,item1,index)=>{
    current_tag.value[item]=item1.key
    emit('choose_nav',{
        layer:item,
        key:item1.key
    })
}
defineExpose({
    current_tag,
    filter_criteria,
    setFilterCriteria,
    loading
})
onMounted(()=>{
    Object.keys(filter_criteria.value).map((item)=>{
        filter_criteria.value[item].arr.unshift({
            key: '全部',
            label: '全部'
        })
        current_tag.value[item]=filter_criteria.value[item].arr[0].key
    }) 
})
</script>
<style lang="scss" scoped>
.filterCriteria{
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    .filterCriteria_contanier{
        width: 1400px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        padding:51px 0;
        padding-bottom: 30px;
        .filterCriteria_filter{
            display: flex;
            align-items: center;
            margin-bottom: px;
            flex-wrap: wrap;
            .filterCriteria_filter_title{
                margin-right: 24px;
                font-size: 14px;
                line-height: 21px;
                color: #F5F5F5;
            }
            .filterCriteria_filter_item{
                font-size: 14px;
                height: 22px;
                line-height: 21px;
                color: #000;
                background-color: transparent;
                padding: 0;
                cursor: pointer;
                display: flex;
                align-items: center;
                margin-right: 24px;
                margin-bottom: 15px;
                &.current{
                    color: #fff;
                    background: #0AAF60;
                    border-radius: 4px;
                    padding: 0 6px;
                }
            }
            &:last-child{
                margin-bottom: 0;
            }
        }
    }
}
</style>