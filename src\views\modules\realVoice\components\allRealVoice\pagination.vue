<template>
<div class="allRealVoice_pagination">
    <div class="pagination_contanier">
        <el-pagination
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalItems"
        layout="prev, pager, next"
        :prev-text="'上一页'"
        :next-text="'下一页'"
        />
    </div>
</div>
</template>
<script setup>
import {reactive,ref,defineExpose} from 'vue'
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页显示的条目数
const totalItems = ref(100); // 总条目数

const handleCurrentChange = (page) => {
    currentPage.value = page;
    // 在这里可以添加逻辑来获取新页的数据
    console.log(`当前页: ${page}`);
};

defineExpose({

})
</script>
<style lang="scss" scoped>
.allRealVoice_pagination{
    
    .pagination_contanier{
        width: 1228px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 169px;
        .el-pagination{
           width: fit-content;
           :deep(.btn-prev),:deep(.btn-next){
                width: 68px;
                height: 33px;
                background: rgba(24, 173, 37, 0.1);
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                span{
                    font-size: 14px;
                    color: #181818;
                }
           }
           :deep(.btn-prev){
                margin-right: 8px;
           }
           :deep(.el-pager){
            .number{
                width: 34px;
                height: 33px;
                background: rgba(24, 173, 37, 0.1);
                border-radius: 4px;
                font-size: 14px;
                color: #181818;
                margin-right: 7px;
                &.is-active{
                    background: #18AD25;
                    color: #fff;
                }
            }
           }
          
        }
    }

}
</style>