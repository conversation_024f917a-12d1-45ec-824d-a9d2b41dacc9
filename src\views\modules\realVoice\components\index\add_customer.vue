<template>
   <div v-if="dialogVisible" class="add_customer">
   <img src="@/assets/images/realVoice/add_customer_close.svg" class="add_customer_close" @click="close" alt="">
   <div class="add_customer_ercode">
    <img src="@/assets/images/realVoice/add_customer_ercode.png" alt="">
   </div>
  </div>
</template>

<script setup>
import { ref, defineExpose, } from 'vue';
let dialogVisible = ref(false);
let close=()=>{
    dialogVisible.value = false;
}
defineExpose({
    dialogVisible,
    close
});
</script>

<style lang="scss">
.add_customer{
    width: 462px;
    height: 448px;
    background-image: url('@/assets/images/realVoice/add_customer_bg.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: cover;
    position: fixed;
    top: 200px;
    right: 0;
    z-index:2;
    .add_customer_close{
        position: absolute;
        top: 0;
        right: 16px;
        z-index: 3;
        cursor: pointer;
        width: 40px;
        height: 40px;
    }
    .add_customer_ercode{
        width: 149px;
        height: 150px;
        padding: 10px;
        box-sizing: border-box;
        position: absolute;
        top: 150px;
        left: 36px;
        border-radius: 12px;
        background-color: #fff;
        z-index: 4;
        img{
            width: 100%;
            height: 100%;
        }
    }
}
</style>
