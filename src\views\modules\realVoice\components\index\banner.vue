<template>
    <div class="real_video_banner">
      <swiper :modules="[Autoplay, Pagination, Navigation]"  class="mySwiper" :navigation="false" :loop="false"   v-bind="swiperOptions">
        <swiper-slide v-for="(banner, index) in props.banners" :key="index">
          <div class="real_video_banner_item" :style="{ backgroundImage: `url(${banner.image})` }">
            <div class="real_video_banner_item_content">
              <!-- <h2>{{ banner.title }}</h2>
              <p>{{ banner.description }}</p>
              <span>{{ banner.remark }}</span> -->
              <!-- <div class="real_video_banner_item_btns">
                <button class="real_video_banner_item_btns_item"  @click="freeSound(banner.title)">免费试音</button>
                <button class="real_video_banner_item_btns_item"  @click="learnMore(banner.title)">了解更多</button>
              </div> -->
              <img :src="banner.btn.image" class="free_sound" alt="" @click="freeSound(banner.title,index)" :style="{left:banner.btn.left+'px',top:banner.btn.top+'px',width:banner.btn.width+'px',height:banner.btn.height+'px'}">
            </div>
          </div>
        </swiper-slide>
        <!-- 只有多个时才显示自定义导航按钮 -->
        <template v-if="!isSingle">
          <div class="custom-swiper-button-prev"></div>
          <div class="custom-swiper-button-next"></div>
        </template>
      </swiper>
    </div>
  </template>
  
  <script setup>
  import { ref,computed,defineExpose,defineEmits,getCurrentInstance } from 'vue';
  import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import 'swiper/swiper-bundle.css';
  import banner from '@/assets/images/realVoice/banner.png'
  import { useUmeng } from '@/utils/umeng/hook'; // 导入友盟埋点
  import { useloginStore } from '@/stores/login'
  const { proxy } = getCurrentInstance();
  let loginStore = useloginStore() 
  // 初始化埋点
  const umeng = useUmeng();

  const props = defineProps({
    banners: Array,
  });
  let emits=defineEmits(['freeSound','learnMore'])
  const handleBannerClick = (title) => {
  };
  const freeSound=(title,index)=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let numStr = String(index + 1).padStart(2, '0')
    let eventLabel = `Banner${numStr}-${title || '未知标题'}`

    // 添加埋点代码
    umeng.trackEvent(
      '真人配音', 
      '点击免费试音', 
      eventLabel, 
      ''
    );
    
    emits('freeSound')
  }
  const learnMore=(title)=>{
    // 添加埋点代码
    umeng.trackEvent(
      '真人配音', 
      '点击了解更多', 
      `Banner-${title || '未知标题'}`, 
      ''
    );
    
    emits('learnMore')
  }
  defineExpose({

  })
  
  const isSingle = computed(() => (props.banners.length<=1 ? true : false));
  console.log(isSingle.value,'isSingle');
  
const swiperOptions = computed(() => {
  if (isSingle.value) {
    return {
      loop: false,
      autoplay: false,
      pagination: false, // 隐藏指示器
      navigation: false, // 不显示导航按钮
      allowTouchMove: false,
    };
  } else {
    return {
      loop: true,
      autoplay: {
        delay: 5000,
        disableOnInteraction: false,
      },
      pagination: false, // 隐藏指示器
      navigation: {
        nextEl: '.custom-swiper-button-next',
        prevEl: '.custom-swiper-button-prev',
      },
      allowTouchMove: true,
    };
  }
});
  </script>
  
  <style lang="scss" scoped>
  .real_video_banner{
    padding-top: 24px;
  .real_video_banner_item {

    height: 510px; /* 设置 Banner 高度 */
    background-size: cover;
    background-position: center;
    display: flex;
    // padding-top: 112px;
    align-items: center;
    justify-content: flex-start;
    color: white;
  
  }
  
  .real_video_banner_item_content {
    .free_sound{
      position: absolute;
      z-index: 2;
      cursor: pointer;
    }
    // h2{
    //     font-size: 60px;
    //     line-height: 84px;
    //     margin: 0;
    //     letter-spacing: 0.005em;
    //     text-shadow: 0px 4.89173px 34.2421px rgba(255, 255, 255, 0.33);
    //     font-weight: 500;

    // }
    // p{
    //     font-size: 42px;
    //     line-height: 59px;
    //     margin: 0;
    //     margin-bottom: 24px;
    //     letter-spacing: 0.055em;
    //     font-family: 'PingFang SC';
    //     font-style: normal;
    // }
    // span{
    //     font-size: 19px;
    //     line-height: 27px;
    //     margin-bottom: 41px;
    //     display: block;


    // }
    // .real_video_banner_item_btns{
    //   display: flex;
    //   align-items: center;
    //   button{
    //     width: 127px;
    //     height: 46px;
    //     background: #0AAF60;
    //     margin-right: 23px;
    //     cursor: pointer;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     font-size: 16px;
    //     line-height: 22px;
    //     /* identical to box height */
    //     text-transform: uppercase;
    //     border: none;
    //     color: #FFFFFF;
    //     box-sizing: border-box;
    //     border-radius: 8px;
    //     &:last-child{
    //       border: 1px solid #0AAF60;
    //       color: #0AAF60;
    //       margin-right: 0; 
    //       background-color: transparent;
    //     }
    //   }
    //     // button{
    //     //     margin-right: 21px;
    //     //     &:last-child{
    //     //         margin-right: 0;
    //     //     }
    //     // }
    // }
  }
  .mySwiper{
    width: 1800px;
    margin: 0 auto;
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    // ::v-deep(.swiper-pagination){
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   bottom: 17px;
    //   .swiper-pagination-bullet{
    //     background-color: #ffffff;
    //     border: none;
    //     cursor: pointer;
    //     display: block;
    //     height: 2px;
    //     margin: 0;
    //     opacity: .48;
    //     outline: none;
    //     padding: 0;
    //     transition: var(--el-transition-duration);
    //     width: 30px;
    //     margin: 0 4px;
    //   }
    //   .swiper-pagination-bullet-active{
    //     opacity: 1;
    //   }
    //    }
    .custom-swiper-button-prev,
    .custom-swiper-button-next {
      width: 46px;
      height: 46px;
      background-color: rgba(255,255,255,0.1);
      border-radius: 50%;
      backdrop-filter: blur(20px);
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      z-index: 2;
    }

    .custom-swiper-button-prev {
      left: 44px;
      background-image: url('@/assets/images/realVoice/realVoice_banner_pre.svg');
      background-repeat: no-repeat;
      background-position: center;
    }

    .custom-swiper-button-next {
      right: 44px;
      background-image: url('@/assets/images/realVoice/realVoice_banner_next.svg');
      background-repeat: no-repeat;
      background-position: center;
    }
  }

}
  </style>
  