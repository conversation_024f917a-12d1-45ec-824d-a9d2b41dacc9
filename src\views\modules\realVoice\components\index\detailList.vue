<template>
    <div class="real_voice_detail_list">
        <div class="real_voice_detail_list_title">
            <div class="real_voice_detail_list_title_label">
                声音列表
            </div>
            <!-- <div class="real_voice_detail_list_tags">
                <span class="real_voice_detail_list_tags_value">
                    <span v-for="(item, index) in voice_list.tags" :key="index" :class="current_tag == item.value ? 'current' : ''"
                        @click="change_tag(item)">{{ item.label }}</span>
                </span>
            </div> -->
        </div>
        

        <div class="real_voice_detail_list_tags_content" v-loading="loading">
            <template v-if="voice_list.list.length>0">
            <div class="real_voice_detail_list_tags_item" v-for="(item, index) in voice_list.list" :key="index" >
                <div class="real_voice_detail_list_tags_item_imgs" :class="item.isPlaying?'real_voice_detail_list_tags_item_imgs_play':''">
                    <img :src="item.avator" alt="" :style="imgStyle(index)">
                    <div class="real_voice_detail_list_tags_item_control_imgs" @click="video_play(index)">
                        <img src="@/assets/images/realVoice/real_voice_detail_play.png" v-if="item.isPlaying" alt="">
                        <img src="@/assets/images/realVoice/real_voice_detail_pause.svg" v-else alt="">
                    </div>
                    <canvas :ref="el => getImgRef(el, index)"></canvas>
                </div>
                <div class="real_voice_detail_list_tags_item_video">
                    <div class="real_voice_detail_list_tags_item_video_info">
                        <span class="real_voice_detail_list_tags_item_video_name">{{ item.title }}</span>
                        <!-- <div class="real_voice_detail_list_tags_item_video_operate">
                            <img src="@/assets/images/realVoice/real_voice_detail_list_share.svg" @click="share(item)" alt="">
                            <img src="@/assets/images/realVoice/real_voice_detail_list_download.svg" @click="download(item)" alt="">
                            <img src="@/assets/images/realVoice/real_voice_detail_list_collect.svg"  @click="collect(item)" alt="">
                        </div> -->
                    </div>
                    
                    <div class="real_voice_detail_list_tags_item_video_audio_picture">
                        <img src="@/assets/images/realVoice/real_voice_detail_list.png" alt="">
                        <!-- <canvas :ref="el => getRef(el, index)"></canvas> -->
                        <!-- <span class="real_voice_detail_list_tags_item_video_duration play" v-if="item.isPlaying">{{
                            item.currentTime }}</span> -->
                        <span class="real_voice_detail_list_tags_item_video_duration duration">{{ item.duration
                            }}</span>
                    </div>

                </div>
               
            </div>
        </template>
        <template v-else>
            <div class="real_voice_detail_list_no_data">
                <span>该主播暂无相关配音，敬请期待！</span>
            </div>
        </template>
        </div>
    </div>
</template>
<script setup>
import { reactive, ref, defineExpose, onMounted, onUnmounted, nextTick, getCurrentInstance, onActivated,onDeactivated } from 'vue'
import avator from '@/assets/images/realVoice/real_voice_detail_avator.png'
import audioExample from '@/assets/images/realVoice/audio.mp3'
import { ElMessage } from 'element-plus'
import { isArguments } from 'lodash'
import audioExample1 from '@/assets/images/realVoice/audio1.mp3'
import { post, get } from '@/api/index'
//   let audioExample2=get('/audio1/material/11/%E5%BF%92%E7%AC%A8%20%282%29%20%282%29.mp3')
let audioExample2='https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/11/%E5%BF%92%E7%AC%A8%20%282%29%20%282%29.mp3'

let voice_list = reactive({
    tags: [
        { label: '走心旁白', value: '1' },
        { label: '宣传片', value: '2' },
        { label: 'TVC广告', value: '3' },
    ],
    list: [

    ]
})
let loading=ref(false)
let current_tag = ref(2)
let itemRefs = ref({});
let getRef = (el, index) => {
    if (el) {
        itemRefs.value[index] = el;
    } else {
        // 元素被卸载时，清除对应的 ref
        delete itemRefs.value[index];
    }
};
let itemImgRefs = ref({});
let getImgRef = (el, index) => {
    if (el) {
        itemImgRefs.value[index] = el;
    } else {
        // 元素被卸载时，清除对应的 ref
        delete itemImgRefs.value[index];
    }
};
//设置头像旋转角度
let imgStyle = (index) => {
    return {
        transform: `rotate(${voice_list.list[index].lastRotation}deg)`,
        transition: `${voice_list.list[index].isPlaying}` ? 'none' : 'transform 1.5s ease-in-out', // 暂停时添加过渡效果
        transformOrigin: 'center center'
    };
}
let formatDuration = (seconds) => {
    let totalSeconds = Math.floor(seconds); // 取整
    let hours = Math.floor(totalSeconds / 3600); // 计算小时
    let minutes = Math.floor((totalSeconds % 3600) / 60); // 计算分钟
    let remainingSeconds = totalSeconds % 60; // 计算剩余秒数

    // 格式化为两位数
    let formattedHours = String(hours).padStart(2, '0');
    let formattedMinutes = String(minutes).padStart(2, '0');
    let formattedSeconds = String(remainingSeconds).padStart(2, '0');

    // 根据小时数决定返回格式
    if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`; // hh:mm:ss
    } else {
        return `${formattedMinutes}:${formattedSeconds}`; // mm:ss
    }
    console.log('formatDuration');
    
}
let change_tag = (data) => {
    current_tag.value = data.value
}
let video_play = (index) => {
    // 先暂停任何正在播放的音频
    voice_list.list.forEach((item, i) => {
        if (i !== index && item.isPlaying) {
            item.isPlaying = false
            // 停止其他正在播放的音频
            if (item.audioElement) {
                item.audioElement.pause()
            }
        }
    })

    let item = voice_list.list[index]
    // 切换当前音频的播放状态
    item.isPlaying = !item.isPlaying

    // 实际播放/暂停音频
    if (!item.audioElement) {
        // 如果还没有创建音频元素，则创建一个
        try {
            let audioUrl = item.url || 'http://example.com/default-audio.mp3' // 提供一个默认URL用于测试
            // console.log(item, 'audioUrl');

            item.audioElement = new Audio(audioUrl)
            item.audioElement.crossOrigin = "anonymous"; 
            // item.audioElement.crossOrigin = 'anonymous'
            item.audioElement.volume = item.volume / 100
            item.currentTime=`${formatDuration(0)}`; 
            // 监听播放结束事件，自动重置状态
            item.audioElement.addEventListener('ended', () => {
                item.isPlaying = false
            })

            // 监听音频加载错误
            item.audioElement.addEventListener('error', (e) => {
                // console.error('音频加载错误:', e)
                // ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`)
                item.isPlaying = false
            })
        } catch (err) {
            // console.error('创建音频元素失败:', err)
            ElMessage.error(`无法播放 "${item.title}"`)
            item.isPlaying = false
            return
        }
    }

    if (item.isPlaying) {
        // 尝试播放音频
        try {
            let playPromise = item.audioElement.play()
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    // 播放成功
                    // ElMessage.success(`正在播放: ${item.title}`)
                }).catch(err => {
                    // console.error('播放失败:', err)
                    ElMessage.error(`播放失败: ${item.title}`)
                    item.isPlaying = false
                })
            }
        } catch (err) {
            // console.error('播放音频时发生错误:', err)
            ElMessage.error(`无法播放 "${item.title}"`)
            item.isPlaying = false
        }
    } else {
        item.audioElement.pause()
        // ElMessage.info(`已暂停: ${item.title}`)
    }
}
//绘制音频波形图
let originalDraw = () => {
    console.log('originalDraw');
    
    // 创建 Web Audio API 上下文
    let audioCtx = new (window.AudioContext || window.webkitAudioContext)();
     let the_num=0
    voice_list.list.map((item, index) => {



        nextTick(() => {
            let staticCanvas = itemRefs.value[index]
            // 设置 Canvas 尺寸
            staticCanvas.width = staticCanvas.offsetWidth;
            staticCanvas.height = staticCanvas.offsetHeight;
            // console.log(staticCanvas.width,'staticCanvas');
            let realTimeCanvas = itemImgRefs.value[index]
            let realTimeCtx = realTimeCanvas.getContext('2d');
            realTimeCtx.imageSmoothingEnabled = true;
            realTimeCtx.fillStyle = 'rgba(0,0,0,0)';
            realTimeCtx.fillRect(0, 0, realTimeCanvas.width, realTimeCanvas.height);

            let waveformData = []; // 存储静态波形数据
            loadAudioAndGenerateWaveform(item.url);
            // 加载音频并生成静态波形数据
            async function loadAudioAndGenerateWaveform(url) {
                console.log(index,url,'index');
                try {
                    let response = await fetch(url);
         
                    
                    // let response = await get(url)
                    console.log(response,'response');
                    let arrayBuffer = await response.arrayBuffer();
                    console.log(arrayBuffer,'arrayBuffer');
                    let audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);
                    console.log(audioBuffer,'audioBuffer');
                    item.audioCtx = audioCtx
                   
                    // 获取音频的原始 PCM 数据
                    let pcmData = audioBuffer.getChannelData(0); // 取第一个声道
                    the_num++
                    if(the_num==voice_list.list.length){
                        loading.value=false
                    }
                    console.log(pcmData,voice_list.list,'pcmData');
                    // 增加柱状图密度：设置柱子数量为 200（之前是 50）
                    let barCount = 400; // 柱子数量
                    let step = Math.floor(pcmData.length / barCount); // 采样步长

                    waveformData = [];
                    for (let i = 0; i < barCount; i++) {
                        let sum = 0;
                        for (let j = 0; j < step; j++) {
                            sum += Math.abs(pcmData[i * step + j] || 0);
                        }
                        let avg = sum / step;
                        // 增加高低对比：对振幅值进行放大（例如平方或指数变换）
                        waveformData.push(Math.pow(avg, 0.5)); // 使用 0.5 次方放大高低对比
                    }


                    // 初始绘制静态波形
                    drawStaticWaveformWithProgress(0, item.originDuration || 0, index, waveformData);
                } catch (error) {
                    // console.error('加载音频失败:', error);
                }
            }
            let source, analyser, bufferLength, dataArray; // 用于实时波形
            if (!item.source) {
                item.source = audioCtx.createMediaElementSource(item.audioElement);
                item.source.connect(audioCtx.destination);
                source = item.source

                // 实时波形图
                item.audioElement.addEventListener('play', () => {
                    // console.log(audioCtx,'audioCtx');
                    item.isPlaying = true
                    startRotation(index);

                    if (audioCtx.state === 'suspended') {
                        audioCtx.resume();
                    }

                    // 创建音频源

                    analyser = audioCtx.createAnalyser();

                    // 连接音频源到分析器，再连接到输出
                    source.connect(analyser);
                    analyser.connect(audioCtx.destination);

                    // 设置分析器的 FFT 大小
                    analyser.fftSize = 2048;
                    bufferLength = analyser.frequencyBinCount;
                    dataArray = new Uint8Array(bufferLength);

                    // console.log(index,111);

                    // 开始绘制实时波形
                    drawRealTimeWaveform(analyser, dataArray, index, bufferLength);
                });

                // 暂停时断开连接
                item.audioElement.addEventListener('pause', () => {
                    item.isPlaying = false
                    item.lastRotation = (item.lastRotation) % 360
                    if (source) {
                        source.disconnect();
                    }
                });
            }
            // 更新进度并重绘静态波形
            item.audioElement.addEventListener('timeupdate', () => {
                // console.log(item.audioElement.currentTime);
                
                let currentTime = item.audioElement.currentTime;
                let duration = item.audioElement.duration || 0;
                item.currentTime = `${formatDuration(currentTime)}`;
                // progressElement.textContent = `${formatTime(currentTime)} / ${formatTime(duration)}`;
                // console.log(index,'index1');
                requestAnimationFrame(()=>drawStaticWaveformWithProgress(currentTime, duration, index, waveformData));// 根据进度重绘静态波形
                 
            });
            // 点击静态波形跳转
            staticCanvas.addEventListener('click', (event) => {
                let rect = staticCanvas.getBoundingClientRect();
                let x = event.clientX - rect.left;
                let percentage = x / staticCanvas.width;


                item.audioElement.currentTime = percentage * item.audioElement.duration;
                // console.log(item.audioElement,item.audioElement.currentTime,percentage,666);
            });
        })
    })
}
//设置图片旋转
let startRotation = (index) => {
    // 这里可以使用定时器来模拟旋转
    let rotateInterval = setInterval(() => {
        if (voice_list.list[index].isPlaying) {
            voice_list.list[index].lastRotation = (voice_list.list[index].lastRotation + 2) % 360; // 每次增加1度
        } else {
            clearInterval(rotateInterval); // 停止旋转
        }
    }, 100); // 每100毫秒更新一次
}
// 绘制静态波形图并根据进度高亮
let drawStaticWaveformWithProgress = (currentTime, duration, index, waveformData) => {
    let staticCanvas = itemRefs.value[index];
    let staticCtx = staticCanvas.getContext('2d');

    // 清空画布
    staticCtx.fillStyle = 'rgba(255, 255, 255, 1)';
    // console.log(staticCanvas.width, staticCanvas.height,'画布尺寸');
    
    staticCtx.fillRect(0, 0, staticCanvas.width, staticCanvas.height);
    staticCtx.imageSmoothingEnabled = true;

    // 计算柱子宽度和间距
    let barCount = Math.min(waveformData.length, 300);
    let barWidth = staticCanvas.width / (barCount) / 3; // 调整间距，增加密度
    let barGap = barWidth * 2; // 柱子间距
    let maxHeight = staticCanvas.height * 1; // 最大柱子高度

    // 计算当前进度对应的柱子数量
    let progressPercentage = duration ? currentTime / duration : 0;
    let highlightedBars = Math.floor(barCount * progressPercentage);

    // 绘制柱状图
    for (let i = 0; i < barCount; i++) {
        let targetHeight = waveformData[i] * maxHeight; // 目标高度
        let currentHeight = Math.min(targetHeight, maxHeight); // 当前高度，初始为目标高度

        // 根据进度设置颜色：已播放部分高亮，未播放部分暗淡
   
        // console.log(highlightedBars,'highlightedBars');
        
        if (i <= highlightedBars&&highlightedBars>0) {
            staticCtx.fillStyle = '#ff3651'; // 高亮颜色（已播放）
        } else {
            staticCtx.fillStyle = '#25252a'; // 暗淡颜色（未播放）
        }

        // 逐渐增加柱子的高度
        let x = i * (barWidth + barGap);
        let y = staticCanvas.height - currentHeight;

        // 绘制柱子
        staticCtx.fillRect(x, y, barWidth, currentHeight);
    }
};
let timeOut;
//播放时渲染波形图
let drawRealTimeWaveform = (analyser, dataArray, index, bufferLength) => {
    clearTimeout(timeOut); // 清除之前的定时器
    let previousHeights = new Array(20).fill(0); // 保存上一次的柱子高度
    let drawInterval = 150; // 设置绘制间隔（毫秒）
    let lastDrawTime = 0; // 上一次绘制的时间
    let currentTime = performance.now();
    if (currentTime - lastDrawTime < drawInterval) {
        timeOut = setTimeout(() => drawRealTimeWaveform(analyser, dataArray, index, bufferLength), drawInterval);
        return; // 如果未到达绘制间隔，直接返回
    }
    lastDrawTime = currentTime; // 更新上一次绘制时间

    let realTimeCanvas = itemImgRefs.value[index];
    let realTimeCtx = realTimeCanvas.getContext('2d');
    realTimeCtx.imageSmoothingEnabled = true;

    // 清空画布
    realTimeCtx.clearRect(0, 0, realTimeCanvas.width, realTimeCanvas.height);

    // 设置不透明背景
    realTimeCtx.fillStyle = 'rgba(0, 0, 0, 0)';
    realTimeCtx.fillRect(0, 0, realTimeCanvas.width, realTimeCanvas.height);

    // 获取音频数据
    analyser.getByteTimeDomainData(dataArray);

    // 实时波形柱状图设置
    let barCount = 20; // 实时波形柱子数量
    let barWidth = realTimeCanvas.width / (barCount * 2); // 调整柱子宽度
    let barGap = barWidth; // 柱子间距
    let maxHeight = realTimeCanvas.height * 0.8; // 最大高度

    let x = 0;
    let step = Math.floor(bufferLength / barCount);

    for (let i = 0; i < barCount; i++) {
        let sum = 0;
        for (let j = 0; j < step; j++) {
            sum += Math.abs(dataArray[i * step + j] - 128); // 中心化数据
        }
        let avg = sum / step / 128; // 归一化
        let targetHeight = Math.pow(avg, 0.5) * 0.35 * maxHeight; // 目标高度

        // 使用插值计算新的高度
        let newHeight = previousHeights[i] + (targetHeight - previousHeights[i]) * 1.5; // 1.5为插值因子，调整平滑程度

        // 绘制柱子
        realTimeCtx.fillStyle = '#fff';
        realTimeCtx.fillRect(x, realTimeCanvas.height - newHeight, barWidth, newHeight);

        // 更新上一次的高度
        previousHeights[i] = newHeight;

        x += barWidth + barGap; // 更新x坐标
    }

    // 请求下一帧
    timeOut = setTimeout(() => drawRealTimeWaveform(analyser, dataArray, index, bufferLength), drawInterval);
};
let share=(data)=>{

}
let download=(data)=>{
    // 创建一个链接元素
    let link = document.createElement('a');
    link.href = data.url; // 设置链接的 href 为音频文件的 URL
    link.download = data.title; // 设置下载时的文件名

    // 触发下载
    document.body.appendChild(link); // 将链接添加到文档中
    link.click(); // 模拟点击链接
    document.body.removeChild(link); // 下载后移除链接
}
let collect=(data)=>{

}
let encodeUrl=(url)=>{
    try {
      const httpsUrl = url.replace(/^http:/, 'https:');
      const urlObj = new URL(httpsUrl);

      // 先解码路径，避免重复编码
      const decodedPath = decodeURI(urlObj.pathname);

      // 对路径每个段处理
      const encodedPath = decodedPath
        .split('/')
        .map(segment => {
          // 对 + 编码成 %2B
          // 对中文和特殊字符用 encodeURIComponent
          // 但保留已有的 %xx 不变
          return segment
            .split(/(%[0-9A-Fa-f]{2})/) // 分割出已有编码
            .map(part => {
              if (/^%[0-9A-Fa-f]{2}$/.test(part)) {
                // 已编码部分，保持不变
                return part;
              } else {
                // 未编码部分，编码 + 和特殊字符
                return encodeURIComponent(part).replace(/\+/g, '%2B');
              }
            })
            .join('');
        })
        .join('/');

      return `${urlObj.protocol}//${urlObj.host}${encodedPath}${urlObj.search}${urlObj.hash}`;
    } catch (e) {
      console.error('URL 编码失败:', e);
      return url;
    }
  }
  let hasReloaded =ref(false) ;
//初始化音频视频
let initList = async (data) => {
    hasReloaded.value=false
    if(!data) return
    voice_list.list=[]
    voice_list.tags.unshift({
        label: '全部分类',
        value: ''
    })
    // let urls = [audioExample, audioExample1,audioExample2];

    if (!voice_list.list) {
        voice_list.list = [];
    }
    let loadedCount = 0;
    loading.value=true
    // for (const url of urls) {
    for (const data1 of data) {


        // 创建音频对象
        let audio = new Audio(encodeUrl(data1.url));
        // audio.preload = 'none';  // 关闭预加载
        // audio.crossOrigin = "anonymous"; 
        console.log(audio,data1.url,777);
  
        // let audio = new Audio(url);
        // 当音频元数据加载完成时获取播放时长
        audio.addEventListener('loadedmetadata', () => {
     console.log(audio,'loadedmetadata');
            let lastDotIndex =data1.name.lastIndexOf('.')
            voice_list.list.push({
                duration: formatDuration(audio.duration),
                avator: avator,
                // title: url.split('/').pop(), // 获取文件名
                // url: url,
                title: lastDotIndex === -1 ? data1.name : data1.name.substring(0, lastDotIndex), // 获取文件名
                url: data1.url,
                isHovered: false,
                isPlaying: false,
                volume: 100,
                audioElement: audio,
                originDuration: audio.duration,
                lastRotation: 0
            });
     
            
            loadedCount++; // 增加已加载计数
            console.log(loadedCount , data.length,444);
            loading.value=false
            // if (loadedCount === data.length) {
                // originalDraw(); // 所有音频加载完成后执行 originalDraw
            // }
               audio.addEventListener('timeupdate', () => {
                    const item = voice_list.list.find(v => v.audioElement === audio);
                    if (item) {
                        item.currentTime = `${formatDuration(audio.currentTime)}`;
                    }
                });
            
        });
        audio.addEventListener('error', (e) => {
             loadedCount++
            console.error('音频加载失败',hasReloaded.value,loadedCount,data.length, e);
            if(!hasReloaded.value&&loadedCount==data.length){
                  if (!localStorage.getItem('hasReloaded')) {
                    localStorage.setItem('hasReloaded', 'true');
                    setTimeout(()=>{
                        window.location.reload();
                    },500)
                   
                }
                // window.location.reload();
                console.log('刷新页面');
                
          
              
            }
            // window.location.reload()
            // 这里可以做错误提示或重试逻辑
        });
    }



}
onMounted(() => {
    initList()
})
onDeactivated(() => {
    voice_list.list.forEach(item => {
		if (item.audioElement) {
			item.audioElement.pause();
			item.audioElement.src = '';
			item.audioElement = null;
		}
	});
})
onUnmounted(()=>{
    voice_list.list.forEach(item => {
		if (item.audioElement) {
			item.audioElement.pause();
			item.audioElement.src = '';
			item.audioElement = null;
		}
	});
})
defineExpose({
    initList
})
</script>
<style lang="scss" scoped>
.real_voice_detail_list {
    width: 100%;
    box-sizing: border-box;

    .real_voice_detail_list_title {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .real_voice_detail_list_title_label{
            margin-right: 24px;
            font-size: 16px;
            line-height: 24px;
            color: #1F2937;
            display: inline-block;
        }
        .real_voice_detail_list_tags {
        display: flex;
        align-items: center;
        .real_voice_detail_list_tags_label {
            margin-right: 24px;
            font-size: 16px;
            line-height: 24px;
            color: #1F2937;
        }

        .real_voice_detail_list_tags_value {
            display: flex;
            align-items: center;
            cursor: pointer;

            span {
                height: 20px;
                background: #fff;
                border-radius: 2px;
                margin-right: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 10px;
                font-size: 11px;
                line-height: 20px;
                color: #9CA3AF;
                &.current {
                    color: #0AAF60;
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }

    }
        
    }

    

    .real_voice_detail_list_tags_content {
        display: flex;
        flex-direction: column;

        .real_voice_detail_list_tags_item {
            width: 100%;
            box-sizing: border-box;
            background: #FFFFFF;
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 6px;
            padding: 14px 42px 24px 17px;
            display: flex;
            align-items: center;
            margin-bottom: 16px;



            .real_voice_detail_list_tags_item_imgs {
                width: 54px;
                height: 54px;
                background: #000000;
                border-radius: 50%;
                box-sizing: border-box;
                position: relative;
                overflow: hidden;
                box-shadow: 0 0 1px rgba(0,0,0,0.9); /* 缓和边缘 */
                img {
                    width: 100%;
                    height: 100%;

                    &.play {
                        animation: rotate 10s linear infinite;
                    }
                }

                .real_voice_detail_list_tags_item_control_imgs {
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 10;
                    cursor: pointer;
                    width: 15px;
                    height: 16px;
                    img {
                        width: 100%;
                        height: 16px;
                    }
                   
                   
                }

                &::after {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    left: 0;
                    background-color: rgba(0, 0, 0, 0);
                    z-index: 2;
                }

                canvas {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    bottom: 0px;
                    left: 0px;
                    z-index: 9;
                    pointer-events: none;
                    z-index: 4;
                    background-color: rgba(0, 0, 0, 0);
                    cursor: pointer;
                }
                &.real_voice_detail_list_tags_item_imgs_play{
                            border: 1px solid #0AAF60;
                }
            }

            .real_voice_detail_list_tags_item_video {
                display: flex;
                flex-direction: column;
                flex: 1;
                margin-left: 14px;
                .real_voice_detail_list_tags_item_video_info{
                    display: flex;
                    align-items: center;
                    .real_voice_detail_list_tags_item_video_name {
                        font-size: 16px;
                        color: #1F2937;
                        line-height: 24px;
                        word-break: break-all;
                    }
                    .real_voice_detail_list_tags_item_video_operate {
                        display: flex;
                        align-items: center;
                        margin-left: auto;

                        img {
                            width: 14px;
                            height: 14px;
                            margin-right: 18px;
                            cursor: pointer;

                            &:last-child {
                                margin-right: 0;
                            }
                        }
                    }
                }
                

                .real_voice_detail_list_tags_item_video_audio_picture {
                    position: relative;
                    width: 100%;
                    height: 40px;
                    img{
                        width: 100%;
                        height: 24px;
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        border: none;
                    }
                    // canvas {
                    //     width: 100%;
                    //     height: 100%;
                    //     background-color: transparent;
                    //     cursor: pointer;
                    // }

                    .real_voice_detail_list_tags_item_video_duration {
                        position: absolute;
                        pointer-events: none;
                        padding: 0 10px;
                        background-color: rgba(0, 0, 0, 0.65);
                        font-size: 12px;
                        color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        line-height: 21px;
                        border-radius: 2px;
                        &.play {
                            left: 0;
                            bottom: 0;
                        }

                        &.duration {
                            right: 0;
                            bottom: 0;
                        }
                    }
                }

            }

            

            &:last-child {
                margin-bottom: 0;
            }
           
        }
        .real_voice_detail_list_no_data{
            width: 100%;
            display: flex;
            justify-content: center;
            margin: 76px 0 95px 0;
            span{
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                line-height: 20px
            }
        }
    }

}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>