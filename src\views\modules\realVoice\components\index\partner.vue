<template>
<div class="partner">
    <div class="partner_contanier">
        <div class="partner_title">
            <h3>合作伙伴</h3>
            <span>正与众多客户一起创造更多价值</span>
        </div>
        <div class="partner_list" :class="imageList.length > 8 ? 'partner_list_center' : ''">
            <div class="partner_item" v-for="item in imageList" :key="item" >
                <img :src="item" alt="">
            </div>
        </div>
    </div>
</div>
</template>
<script setup>
const images = import.meta.glob('@/assets/images/realVoice/partner_*.png', { eager: true, as: 'url' });

// 生成图片数组，注意路径键名格式
const imageList = Array.from({ length: 38 }, (_, i) => {
  const key = `/src/assets/images/realVoice/partner_${i + 1}.png`;
  return images[key];
});
</script>
<style lang="scss" scoped>
.partner{
    margin-bottom: 203px;
    .partner_contanier{
        width: 1400px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        .partner_title{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 70px;
            h3{
                font-size: 36px;
                color: #222;
                line-height: 50px;
                align-self: center;
                margin-bottom: 20px;
                font-weight: normal;
            }
            span{
                font-size: 18px;
                line-height: 25px;
                text-align: center;
                color: #333333;
            }
            
        }
        .partner_list{
            display: flex;
            flex-wrap: wrap;
            // max-height: 318px;
            .partner_item{
                width: 154px;
                height: 78px;
                background: #fff;
                margin-right: 24px;
                margin-bottom: 24px;
                &:nth-child(8n){
                    margin-right: 0;
                }
                &:last-child{
                    margin-right: 0;
                }
            }
            &.partner_list_center{
                justify-content: center;
            }
        }
        .partner_btn{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 50px;
            background: #fff;
            border-radius: 25px;
            margin-top: 50px;
            span{
                font-size: 16px;
            }
        }
    }
   
}
</style>