<template>

<div class="popular_live_streamers" :style="{'padding-bottom':route.name=='realVoice'?'256px':'26px','padding-top':route.name=='realVoice'?'98px':'69px'}">
    <div class="popular_live_streamers_contanier" >
        <div class="popular_live_streamers_title" v-if="route.name=='realVoice'">热门真人主播</div>
        <div  class="popular_live_streamers_list" v-loading="list_loading">
            <div class="popular_live_streamers_item"  @click.stop="toDetail(item)" v-for="item in list" :key="item.id" >
              <!-- {{ item.voicePriceType }}---{{ item.voiceLevel }}---{{ item.gender }}---{{ item.language }}--{{ item.recommendTags }} -->
                <div class="special_tage" v-if="item.voicePriceType">
                    特价
                </div>
                <div class="popular_live_streamers_item_profile">
                    <div class="popular_live_streamers_item_profile_img">
                        <img class="" :src="item.avatarUrl" alt="">
                    </div>
                    <div class="popular_live_streamers_item_profile_text">
                        <div class="popular_live_streamers_item_profile_text_top">
                            <span class="popular_live_streamers_item_profile_name">{{ item.platformNickname }}</span>
                            <span class="popular_live_streamers_item_profile_job">{{item.feature}}</span>
                        </div>
                        <div class="popular_live_streamers_item_profile_text_deal">
                          <img src="@/assets/images/realVoice/popular_live_streamers_item_profile_text_deal.svg" alt="">
                          成交：{{item.memo}}单
                        </div>
                        <div class="popular_live_streamers_item_tags">
                            <template v-for="(item1,index1) in item.recommendTags.split('、')">
                               <button v-if="item1!=''"   :key="index1">{{item1}}</button>
                            </template>
                           
                        </div>
                    </div>
                   
                 
                </div>
                <div class="popular_live_streamers_item_audio_list">
         
                  <template  v-if="item.audioInfos && item.audioInfos.length > 0">
                      <div class="popular_live_streamers_item_audio_item" 
                          @click.stop="video_play(item, index1)" 
                          v-for="(audioInfo, index1) in item.audioInfos.slice(0, 3)" 
                          :key="index1" v-loading="audioInfo.loading">
                          <span class="popular_live_streamers_item_audio_item_title">{{audioInfo.name}}</span>
                          <div class="popular_live_streamers_item_audio_item_imgs">
                          <img src="@/assets/images/realVoice/play.svg" v-if="audioInfo.isPlaying" alt="">
                          <img src="@/assets/images/realVoice/pause.svg" v-else alt="">
                          </div>
                      </div>
                    </template>
                </div>

                <div class="popular_live_streamers_item_price">
                    <span class="popular_live_streamers_item_word">
                        <span class="popular_live_streamers_item_price_symbol">¥</span><span class="popular_live_streamers_item_price_bold">{{item.adPrice}}</span>
                        <div class="popular_live_streamers_item_rule_boxs">
                            <b class="popular_live_streamers_item_rule">百字</b><b class="popular_live_streamers_item_rule">¥{{item.specialPrice}}起配</b>
                        </div>
                         <!-- <div class="popular_live_streamers_item_rule_boxs">
                          <b class="popular_live_streamers_item_rule">元/分钟</b>
                         </div> -->
                       
                    </span>
                   
                    <button class="popular_live_streamers_item_contact"  @click.stop="contactClick">联系TA</button>
                </div>
            </div>
        </div>
        <button class="popular_live_streamers_more" v-if="route.name=='realVoice'"  @click="view_more">
            查看更多配音师<img src="@/assets/images/realVoice/view_dub_more.svg" alt="">
        </button>
        <contact ref="contact_ref" ></contact>
    </div>
</div>

</template>
<script setup>
import {reactive,ref,defineExpose,onMounted,onUnmounted,onDeactivated,getCurrentInstance  } from 'vue'
import { useRouter,useRoute } from 'vue-router'
import contact from './contact.vue'
import { ElMessage } from 'element-plus'
import { useUmeng } from '@/utils/umeng/hook' // 导入友盟埋点

import { useRealStore } from '@/stores/modules/realVoice.js' 
import { useloginStore } from '@/stores/login'
const { proxy } = getCurrentInstance();
let loginStore = useloginStore() 
let router = useRouter()
let route = useRoute()
// 初始化埋点
const umeng = useUmeng()

let view_more=()=>{
    // 添加埋点代码
    umeng.trackEvent(
      '真人配音', 
      '点击查看更多配音师', 
      '', 
      ''
    )
    
    router.push('/allRealVoice')
}
let list_loading=ref(false)
let contact_ref=ref(null)
console.log(router,'router');
let contactClick=()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    // 添加埋点代码
    umeng.trackEvent(
      '真人配音', 
      '点击联系TA', 
      '主播卡片', 
      ''
    )
    
    console.log(contact_ref.value);
    
    contact_ref.value.dialogVisible=true
}
let audioItems = ref([])
let list=ref([])
let realStore=useRealStore()
// let video_play=(index)=>{

    
//     // 先暂停任何正在播放的音频
//     audioItems.value.forEach((item, i) => {
//         if (i !== index && item.isPlaying) {
//             item.isPlaying = false
//             // 停止其他正在播放的音频
//             if (item.audioElement) {
//                 item.audioElement.pause()
//             }
//         }
//     })

//     let item = audioItems.value[index]
//     console.log(item, 'item');

//     // 切换当前音频的播放状态
//     item.isPlaying = !item.isPlaying

//     // 实际播放/暂停音频
//     if (!item.audioElement) {
//         // 如果还没有创建音频元素，则创建一个
//         try {
//             let audioUrl = item.url || 'http://example.com/default-audio.mp3' // 提供一个默认URL用于测试
//             console.log(item, 'audioUrl');
            
//             item.audioElement = new Audio(audioUrl)
     
            
//             item.audioElement.volume = item.volume / 100

//             // 监听播放结束事件，自动重置状态
//             item.audioElement.addEventListener('ended', () => {
//                 item.isPlaying = false
//             })

//             // 监听音频加载错误
//             item.audioElement.addEventListener('error', (e) => {
//                 console.error('音频加载错误:', e)
//                 // ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`)
//                 item.isPlaying = false
//             })
//         } catch (err) {
//             console.error('创建音频元素失败:', err)
//             ElMessage.error(`无法播放 "${item.name}"`)
//             item.isPlaying = false
//             return
//         }
//     }

//     if (item.isPlaying) {
//         // 尝试播放音频
//         try {
//             let playPromise = item.audioElement.play()

//             if (playPromise !== undefined) {
//                 playPromise.then(() => {
//                     // 播放成功
//                     ElMessage.success(`正在播放: ${item.name}`)
//                 }).catch(err => {
//                     console.error('播放失败:', err)
//                     ElMessage.error(`播放失败: ${item.name}`)
//                     item.isPlaying = false
//                 })
//             }
//         } catch (err) {
//             console.error('播放音频时发生错误:', err)
//             ElMessage.error(`无法播放 "${item.name}"`)
//             item.isPlaying = false
//         }
//     } else {
//         item.audioElement.pause()
//         ElMessage.info(`已暂停: ${item.name}`)
//     }
// }
// 全局暂停所有音频
const pauseAllAudio = () => {
  if (!list.value || list.value.length === 0) return;

  list.value.forEach(item => {
    if (item.audioInfos && item.audioInfos.length > 0) {
      item.audioInfos.forEach(audioInfo => {
        if (audioInfo.isPlaying && audioInfo.audioElement) {
          audioInfo.audioElement.pause();
          audioInfo.isPlaying = false;
        }
      });
    }
  });
};


let video_play = (item, index) => {
  if (!item.audioInfos || item.audioInfos.length === 0) return;

  let currentAudio = item.audioInfos[index];

  // if (!currentAudio.isPlayable) {
  //   ElMessage.error(`音频 "${currentAudio.name}" 不可播放`);
  //   return;
  // }

  // 如果当前音频正在播放，则暂停
  if (currentAudio.isPlaying) {
    currentAudio.audioElement.pause();
    currentAudio.isPlaying = false;
    return;
  }

  // 否则，先暂停其他音频
  pauseAllAudio();

  // 添加埋点代码
  umeng.trackEvent(
    '真人配音', 
    '点击播放音频', 
    `${item.platformNickname}-${currentAudio.name || '未命名音频'}`, 
    ''
  );

  currentAudio.isPlaying = true;

  if (!currentAudio.audioElement) {
    try {
      currentAudio.audioElement = new Audio(currentAudio.url);
      currentAudio.audioElement.volume = currentAudio.volume / 100;
      // 监听加载成功事件，设置可播放标志
      currentAudio.audioElement.addEventListener('canplay', () => {
        currentAudio.isPlayable = true;
      });
      currentAudio.audioElement.addEventListener('ended', () => {
        currentAudio.isPlaying = false;
      });

      currentAudio.audioElement.addEventListener('error', (e) => {
        console.error('音频加载错误:', e);
        // ElMessage.error(`音频 "${currentAudio.name}" 加载失败`);
        currentAudio.isPlaying = false;
      });
    } catch (err) {
      console.error('创建音频元素失败:', err);
      ElMessage.error(`无法播放 "${currentAudio.name}"`);
      currentAudio.isPlaying = false;
      return;
    }
  }

  try {
    let playPromise = currentAudio.audioElement.play();
    if (playPromise !== undefined) {
      playPromise.catch(err => {
        console.error('播放失败:', err);
        ElMessage.error(`播放失败: ${currentAudio.name}`);
        currentAudio.isPlaying = false;
      });
    }
  } catch (err) {
    console.error('播放音频时发生错误:', err);
    ElMessage.error(`无法播放 "${currentAudio.name}"`);
    currentAudio.isPlaying = false;
  }
};
let formatDuration=(seconds)=>{
    let totalSeconds = Math.floor(seconds); // 取整
    let hours = Math.floor(totalSeconds / 3600); // 计算小时
    let minutes = Math.floor((totalSeconds % 3600) / 60); // 计算分钟
    let remainingSeconds = totalSeconds % 60; // 计算剩余秒数

    // 格式化为两位数
    let formattedHours = String(hours).padStart(2, '0');
    let formattedMinutes = String(minutes).padStart(2, '0');
    let formattedSeconds = String(remainingSeconds).padStart(2, '0');

    // 根据小时数决定返回格式
    if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`; // hh:mm:ss
    } else {
        return `${formattedMinutes}:${formattedSeconds}`; // mm:ss
    }
}
let toDetail=(data)=>{
    // 添加埋点代码
    umeng.trackEvent(
      '真人配音', 
      '点击进入配音师详情', 
      `${data.platformNickname || '未知配音师'}`, 
      ''
    )
    
    realStore.setDetailData(data)
    router.push('/realVoiceDetail')
}
let globalAudio = new Audio();
let encodeUrl=(url)=>{
  try {
      const httpsUrl = url.replace(/^http:/, 'https:');
      console.log(url,httpsUrl,'httpsUrl');
      
      const urlObj = new URL(httpsUrl);

      // 先解码路径，避免重复编码
      const decodedPath = decodeURI(urlObj.pathname);

      // 对路径每个段处理
      const encodedPath = decodedPath
        .split('/')
        .map(segment => {
          // 对 + 编码成 %2B
          // 对中文和特殊字符用 encodeURIComponent
          // 但保留已有的 %xx 不变
          return segment
            .split(/(%[0-9A-Fa-f]{2})/) // 分割出已有编码
            .map(part => {
              if (/^%[0-9A-Fa-f]{2}$/.test(part)) {
                // 已编码部分，保持不变
                return part;
              } else {
                // 未编码部分，编码 + 和特殊字符
                return encodeURIComponent(part).replace(/\+/g, '%2B');
              }
            })
            .join('');
        })
        .join('/');

      return `${urlObj.protocol}//${urlObj.host}${encodedPath}${urlObj.search}${urlObj.hash}`;
    } catch (e) {
      console.error('URL 编码失败:', e);
      return url;
    }
  }
  
// let init = async (data) => {
//   list_loading.value = true;
//   data = data.filter(item => item.audioUrl);
//   if (route.name == 'realVoice') {
//     data = data.slice(0, 12);
//   }
//   list.value = data.map(item => reactive({ ...item, audioInfos: [] }));
//   list_loading.value = false;
//   const itemPromises = list.value.map(item => {
//     if (!item.audioUrl) return Promise.resolve();

//     const audioUrls = item.audioUrl.split(/[、,]/).map(s => s.trim()).filter(s => s);

//     const promises = audioUrls.map((url, index) => new Promise((resolve) => {
//       item.audioInfos[index] = {
//         duration: 0,
//         name: getAudioName(item.voiceName, url.split('/').pop()),
//         url: encodeUrl(url),
//         isPlayable: false,
//         isHovered: false,
//         isPlaying: false,
//         lastRotation: 0,
//         volume: 100,
//         audio_finished: false,
//         audioElement: null,
//         loading: true
//       };

//       const audio = new Audio(encodeUrl(url));

//       function onCanPlay() {
//         Object.assign(item.audioInfos[index], {
//           duration: formatDuration(audio.duration),
//           isPlayable: true,
//           audio_finished: true,
//           audioElement: audio,
//           loading: false
//         });
//         cleanup();
//         resolve();
//       }

//       function onError() {
//         Object.assign(item.audioInfos[index], {
//           isPlayable: false,
//           audio_finished: false,
//           audioElement: null,
//           loading: false
//         });
//         cleanup();
//         resolve();
//       }

//       function cleanup() {
//         audio.removeEventListener('canplay', onCanPlay);
//         audio.removeEventListener('error', onError);
//       }

//       audio.addEventListener('canplay', onCanPlay);
//       audio.addEventListener('error', onError);

//       audio.load();
//     }));

//     return Promise.all(promises).then(() => {
//       console.log(`item ${item.id} 的所有音频加载完成`);
//     });
//   });

//   await Promise.all(itemPromises);


// };
let audioElements = []; // 保存所有 Audio 对象
let abortController = null; // 全局 AbortController
let init = async (data) => {
  // // 先取消之前的加载
  // if (abortController) {
  //   abortController.abort();
  // }
  // abortController = new AbortController();
  // const { signal } = abortController;

  list_loading.value = true;
console.log(data,'data');

  data = data.filter(item => item.audioUrl);
  if (route.name === 'realVoice') {
    data = data.slice(0, 12);
  }

  list.value = data.map(item => reactive({ ...item, audioInfos: [] }));

  list_loading.value = false;

  // 清理之前的音频资源
  audioElements.forEach(audio => {
    audio.pause();
    audio.src = '';
  });
  audioElements.length = 0;

  const itemPromises = list.value.map(item => {
    if (!item.audioUrl) return Promise.resolve();

    const audioUrls = item.audioUrl.split(/[,]/).map(s => s.trim()).filter(s => s);
console.log(item.audioUrl,'audioUrl');

    const promises = audioUrls.map((url, index) => new Promise((resolve) => {
      item.audioInfos[index] = {
        duration: 0,
        name: getAudioName(item.voiceName, url.split('/').pop()),
        url: encodeUrl(url),
        isPlayable: false,
        isHovered: false,
        isPlaying: false,
        lastRotation: 0,
        volume: 100,
        audio_finished: false,
        audioElement: null,
        loading: false
      };

      // const audio = new Audio(encodeUrl(url));
      // audioElements.push(audio);

      // function cleanup() {
      //   audio.removeEventListener('canplay', onCanPlay);
      //   audio.removeEventListener('error', onError);
      //   signal.removeEventListener('abort', onAbort);
      // }

      // function onCanPlay() {
      //   if (signal.aborted) {
      //     cleanup();
      //     resolve();
      //     return;
      //   }
      //   Object.assign(item.audioInfos[index], {
      //     duration: formatDuration(audio.duration),
      //     isPlayable: true,
      //     audio_finished: true,
      //     audioElement: audio,
      //     loading: false
      //   });
      //   cleanup();
      //   resolve();
      // }

      // function onError() {
      //   if (signal.aborted) {
      //     cleanup();
      //     resolve();
      //     return;
      //   }
      //   Object.assign(item.audioInfos[index], {
      //     isPlayable: false,
      //     audio_finished: false,
      //     audioElement: null,
      //     loading: false
      //   });
      //   cleanup();
      //   resolve();
      // }

      // function onAbort() {
      //   audio.pause();
      //   audio.src = '';
      //   Object.assign(item.audioInfos[index], {
      //     isPlayable: false,
      //     audio_finished: false,
      //     audioElement: null,
      //     loading: false
      //   });
      //   cleanup();
      //   resolve();
      // }

      // audio.addEventListener('canplay', onCanPlay);
      // audio.addEventListener('error', onError);
      // signal.addEventListener('abort', onAbort);

      // audio.load();
    }));

    return Promise.all(promises).then(() => {
      if (!signal.aborted) {
        console.log(`item ${item.id} 的所有音频加载完成`);
      }
    });
  });

  await Promise.all(itemPromises);
};

  

let getAudioName=(voiceName, url)=>{
  const lowerUrl = url.toLowerCase();
  const mp3Index = lowerUrl.indexOf('.mp3');
  const startStr = voiceName + '+';
  const startIndex = url.indexOf(startStr);
  if (startIndex === -1) {
    // 找不到 voiceName+，取从开头到 .mp3 之前的内容
    return url.substring(0, mp3Index);
  }

  const endIndex = lowerUrl.indexOf('.mp3', startIndex);
  if (endIndex === -1) {
    return null; // 没有找到 .mp3 结尾
  }

  // 截取 voiceName+ 后面到 .mp3 之前的内容
  return url.substring(startIndex + startStr.length, endIndex);
}
let close_aduio = () => {
  if (list.value.length > 0) {
    list.value.forEach(item => {
      if (item.audioInfos && item.audioInfos.length > 0) {
        item.audioInfos.forEach(audioInfo => {
          if (audioInfo.audioElement) {
            audioInfo.audioElement.pause();
            audioInfo.audioElement.currentTime = 0; // 重置播放进度
            audioInfo.audioElement.src = ''; // 释放资源
            audioInfo.audioElement = null;
          }
        });
      }
    });
  }
}
defineExpose({
  init,
  close_aduio,
  list_loading
})
onUnmounted(() => {

  
  if (abortController) {
    abortController.abort();
  }
  audioElements.forEach(audio => {
    audio.pause();
    audio.src = '';
  });
  audioElements.length = 0;

  // 清理 list 中的 audioElement 引用，避免内存泄漏
  list.value.forEach(item => {
    item.audioInfos.forEach(info => {
      if (info.audioElement) {
        info.audioElement.pause();
        info.audioElement.src = '';
        info.audioElement = null;
      }
    });
  });
});
</script>
<style lang="scss" scoped>
.popular_live_streamers{
    padding-top: 116px;
    padding-bottom: 256px;
    background-color: rgba(249,250,251,1);
    .popular_live_streamers_contanier{
        width: 1400px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
    .popular_live_streamers_title{
        font-weight: 500;
        font-size: 36px;
        line-height: 50px;
        color: #222;
        text-indent: 33px;
        margin-bottom: 80px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .popular_live_streamers_list{
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 28px;
    .popular_live_streamers_item{
            width: 324px;
            height: 408px;
            background: #FFFFFF;
            box-shadow: 0px 0px 33px 0px rgba(9,2,4,0.1);
            padding:20px;
            padding-bottom: 24px;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            margin-right: 34px;
            margin-bottom: 52px;
            .popular_live_streamers_item_profile{
                display: flex;
                margin-bottom: 10px;
                .popular_live_streamers_item_profile_img{
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    margin-right: 12px;
                    padding: 2px 1px 0;
                    box-sizing: border-box;
                    img{
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        border: none;
                    }
                }
                .popular_live_streamers_item_profile_text{
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    flex: 1;
                    .popular_live_streamers_item_profile_text_top{
                        margin-bottom: 9px;
                        display: flex;
                        align-items: center;
                        .popular_live_streamers_item_profile_name{
                            font-size: 18px;
                            line-height: 26px;
                            color: #000;
                            margin-right: 7px;
                            font-weight: 500;
                        }
                        .popular_live_streamers_item_profile_job{
                            font-size: 12px;
                            color: #409EFF;
                            line-height: 20px;
                            box-sizing: border-box;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding: 0px 8px;
                            height: 20px;
                            background: #ECF5FF;
                            border: 1px solid #D9ECFF;
                            border-radius: 2px;
                        }
                    }
                      .popular_live_streamers_item_profile_text_deal{
                        font-size: 12px;
                        line-height: 17px;
                        color: #9CA3AF;
                        display: flex;
                        align-items: center;
                        margin-bottom: 9px;
                        img{
                          margin-right: 3px;
                          width: 11px;
                          height: 13px;
                        }
                      }
                        .popular_live_streamers_item_works_box{
                            display: flex;
                            align-items: center;
                            font-size: 12px;
                            color: #9CA3AF;
                            margin-bottom: 9px;
                            line-height: 17px;
                            .popular_live_streamers_item_works{
                                margin-right: 21px;
                                &:last-child{
                                    margin-right: 0;
                                }
                            }
                        }
                        .popular_live_streamers_item_tags{
                            display: flex;
                            align-items: center;
                            margin-bottom: 12px;
                            flex-wrap: wrap;
                            max-height: 58px;
                            button{
                                background: #F3F4F6;
                                border-radius: 2px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.6);
                                margin-right: 4px;
                                border: none;
                                padding: 2px 8px;
                                line-height: 20px;
                                box-sizing: border-box;
                                margin-bottom: 5px;
                                &:last-child{
                                    margin-right: 0;
                                }
                            }
                        }
                   
                    
                }
            }
            
            .popular_live_streamers_item_audio_list{
                display: flex;
                flex-direction: column;
                margin-bottom: 26px;
                height: 192px;
                .popular_live_streamers_item_audio_item{
                    display: flex;
                    align-items: center;
                    width: 100%;
                    height: 48px;
                    border: 1px solid #F5F6F8;
                    padding: 12px;
                    padding-left: 8px;
                    box-sizing: border-box;
                    justify-content: space-between;
                    margin-bottom: 8px;
                    cursor: pointer;
                    .popular_live_streamers_item_audio_item_title{
                        font-size: 14px;
                        color:rgba(53, 61, 73,0.8);
                        line-height: 20px;

                    }
                    .popular_live_streamers_item_audio_item_imgs{
                        width: 24px;
                        height: 24px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    &:last-child{
                        margin-bottom: 0;
                    }
                }
            }
            .popular_live_streamers_item_price{
                display: flex;
                align-items:baseline;
                font-weight: 300;
                font-size: 12px;
                color: #7F7F7F;
                position: absolute;
                bottom: 24px;
                width: 284px;
                .popular_live_streamers_item_word{
                    margin-right: 14px;
                    display: flex;
                    align-items: flex-end;
                    .popular_live_streamers_item_rule_boxs{
                        height: 32px;
                        display: flex;
                        align-items: center;
                        .popular_live_streamers_item_rule{
                            line-height: 15px;
                            font-size: 14px;
                            letter-spacing: -0.02em;
                            color: #626263;
                            padding: 0 11px;
                            border-right: 1px solid #626263;
                            &:first-child{
                                padding-left: 0;
                            }
                            &:last-child{
                                padding-right: 0;
                                border-right: none;
                            }
                        }
                    }
                    
                }
                .popular_live_streamers_item_price_bold{
                    font-weight: bold;
                    font-size: 24px;
                    line-height: 38px;
                    color: #FF4311;
                    letter-spacing: 0.14px;
                    margin-right: 7px;
                }
                .popular_live_streamers_item_price_symbol{
                    font-size: 16px;
                    line-height: 38px;
                    color: #FF4311;
                    margin-right: 2px;
                    letter-spacing: 0.14px;
                }

                .popular_live_streamers_item_contact{
                    width: 73px;
                    height: 28px;
                    border-radius: 3px;
                    border: 1px solid #0AAF60;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    color: #0AAF60;
                    background-color: transparent;
                    margin-left: auto;
                    cursor: pointer;
                }
            }
            &:nth-child(4n){
                margin-right: 0;
            }
            .special_tage{
                position: absolute;
                right: 0;
                top: 0;
                padding: 2px 12px;
                font-size: 14px;
                line-height: 22px;
                color: #fff;
                background-color: #EB6B2D;
                border-radius: 0 0 0 8px;
                z-index: 1;
                display: flex;
                align-items: center;
                img{
                    width: 21px;
                    height: 21px;
                    margin-right: 3px;
                }
          
            }
       
        }
    }
    .popular_live_streamers_more{
        width: 178px;
        height: 49px;
        border-radius: 4px;
        font-size: 16px;
        color:#fff;
        font-weight: bold;
        background-color: #40BB15;
        border: none;
        align-self: center;
        cursor: pointer;
        padding: 11px 18px;
        display: flex;
        align-items: center;
        font-size: 16px;
        letter-spacing: 0.14px;
        img{
            margin-left: auto;
            width: 19px;
            height: 19px;
        }

    }
}
}
</style>