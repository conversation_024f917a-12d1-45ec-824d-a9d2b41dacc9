<template>
    <div class="serveProcess">
        <div class="serveProcess_contanier">
            <div class="serveProcess_title">
                配音服务流程
            </div>
            <div class="serveProcess_list">
                <div class="serveProcess_item" v-for="(item,index) in list" :key="index">
                    <div class="serveProcess_item_detail">
                        <div class="serveProcess_item_imgs">
                            <img :src="item.img" alt="">
                        </div>
                        <span class="serveProcess_item_title">{{ item.title }}</span>
                        <span class="serveProcess_item_describe">{{ item.describe }}</span>
                    </div>
                </div>
                
            </div>

        </div>
    </div>
</template>
<script setup>
import { reactive,ref,defineExpose } from 'vue';
import serveProcess1 from '@/assets/images/realVoice/serveProcess_1.svg'
import serveProcess2 from '@/assets/images/realVoice/serveProcess_2.svg'
import serveProcess3 from '@/assets/images/realVoice/serveProcess_3.svg'
import serveProcess4 from '@/assets/images/realVoice/serveProcess_4.svg'
import serveProcess5 from '@/assets/images/realVoice/serveProcess_5.svg'
import serveProcess6 from '@/assets/images/realVoice/serveProcess_6.svg'
let list=reactive([ {
        title:'选择配音师',
        describe:'按场景、风格、语言选择配音师',
        img:serveProcess1
    },{
        title:'免费样音',
        describe:'免费试音、确认风格匹配度',
        img:serveProcess2
    },{
        title:'确认小样',
        describe:'确认语速、情感、语气等细节',
        img:serveProcess3
    },{
        title:'客户付款',
        describe:'安全付款、平台担保交易',
        img:serveProcess4
    },{
        title:'交付成品',
        describe:'提供多种格式、高品质音频文件',
        img:serveProcess5
    },{
        title:'售后服务',
        describe:'免费修改、确保满意为止',
        img:serveProcess6
    },])
defineExpose({
    list
})
</script>
<style lang="scss" scoped>
.serveProcess{
    margin-bottom: 239px;
    .serveProcess_contanier{
        width: 1400px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        .serveProcess_title{
            font-size: 36px;
            color: #222222;
            line-height: 50px;
            margin-bottom: 64px;
            align-self: center;
        }
        .serveProcess_list{
            display: flex;
  
            .serveProcess_item{
                width: 208px;
                height: 266px;
                display: flex;
                justify-content: center;
                margin-right: 30px;
                background-color: #fff;
                padding: 60px 13px 0;
                box-sizing: border-box;
                .serveProcess_item_detail{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .serveProcess_item_imgs{
                        width: 90px;
                        height: 90px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 15px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .serveProcess_item_title{
                        font-weight: bold;
                        font-size: 19px;
                        line-height: 23px;
                        color: #333333;
                        margin-bottom: 3px;
                    }
                    .serveProcess_item_describe{
                        font-size: 13px;
                        color: #333;
                        line-height: 25px;
                        text-align: center;
                        display: -webkit-box;               
                        -webkit-box-orient: vertical;       
                        -webkit-line-clamp:2;              
                        overflow: hidden;                    
                        text-overflow: ellipsis;             
                        max-height: 50px;  
                        word-break: break-all;  
                        width: 182px; 

                    }
                   
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
    }
}
</style>