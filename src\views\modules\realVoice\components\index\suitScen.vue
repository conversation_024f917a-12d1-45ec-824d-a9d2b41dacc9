<template>
<div class="suitScen">
    <div class="suitScen_contanier">
        <div class="suitScen_title">
            适用场景
        </div>
        <div class="suitScen_list">
            <div class="suitScen_item" v-for="(item,index) in list" :key="index">
                <div class="suitScen_item_imgs">
                    <img :src="item.img" alt="">
                </div>
                <div class="suitScen_item_title">
                    {{item.title}}
                </div>
                <div class="suitScen_item_describe">
                    {{ item.describe }}
                </div>
            </div>
        </div>
    </div>
</div>
</template>
<script setup>
import { reactive,ref,defineExpose } from 'vue';
import suitScen1 from '@/assets/images/realVoice/suit_scen_1.png'
import suitScen2 from '@/assets/images/realVoice/suit_scen_2.png'
import suitScen3 from '@/assets/images/realVoice/suit_scen_3.png'
import suitScen4 from '@/assets/images/realVoice/suit_scen_4.png'
let list=reactive([
    {
        title:'影视配音',
        describe:'电影、电视剧、纪录片、广告片等 专业影视作品配音',
        img:suitScen1
    },
    {
        title:'游戏配音',
        describe:'游戏角色、剧情、宣传片等专业游戏配音服务',
        img:suitScen2
    },
    {
        title:'企业配音',
        describe:'企业宣传片、产品介绍、培训视频等商业配音',
        img:suitScen3
    },
    {
        title:'教育配音',
        describe:'在线课程、教育视频、有声读物等教育内容配音',
        img:suitScen4
    },
])
defineExpose({
    list
})
</script>
<style lang="scss" scoped>
.suitScen{
    margin-bottom: 192px;
    .suitScen_contanier{
        width: 1400px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        .suitScen_title{
            font-weight: 500;
            font-size: 36px;
            color: #222222;
            line-height: 50px;
            margin-bottom: 52px;
            align-self: center;

        }
        .suitScen_list{
            display: flex;
            align-items: center;
            .suitScen_item{
                width: 311px;
                height: 401px;
                border-radius: 27px;
                box-sizing: border-box;
                margin-right: 52px;
                display: flex;
                flex-direction: column;
                align-items: center;
                .suitScen_item_imgs{
                    width: 100%;
                    height: 241px;
                    border-radius: 12px;
                    overflow: hidden;
                    margin-bottom: 32px;
                    border-radius: 8px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .suitScen_item_title{
                    font-weight: bold;
                    font-size: 20px;
                    color: #333;
                    line-height: 28px;
                    margin-bottom: 8px;

                }
                .suitScen_item_describe{
                    font-size: 16px;
                    color: rgba(0, 0, 0, 0.45);
                    line-height: 26px;
                    text-align: center;
                    display: -webkit-box;               
                    -webkit-box-orient: vertical;       
                    -webkit-line-clamp:2; 
                    word-break: break-all;               
                    overflow: hidden;                    
                    text-overflow: ellipsis;             
                    max-height: 52px; 
                    padding: 0 26px;
                    width: 100%;
                    box-sizing: border-box;   
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
    }
}
</style>