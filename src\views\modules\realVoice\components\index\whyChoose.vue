<template>
    <div class="whyChoose">
        <div class="whyChoose_contanier">
            <div class="whyChoose_title">
                为什么选择配音帮手
            </div>
            <div class="whyChoose_detail">
                <div class="whyChoose_detail_item" v-for="(item,index) in list" :key="index">
                    <div class="whyChoose_detail_item_title">
                        {{ item.title }}
                    </div>
                    <div class="whyChoose_detail_item_describe">
                        {{ item.describe }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {reactive,ref,defineExpose} from 'vue'
import whyChoose1 from '@/assets/images/realVoice/why_choose1.png'
import whyChoose2 from '@/assets/images/realVoice/why_choose2.png'
import whyChoose3 from '@/assets/images/realVoice/why_choose3.png'
import whyChoose4 from '@/assets/images/realVoice/why_choose4.png'
let list=reactive([
    {
        img:whyChoose1,
        title:'专业配音品质',
        describe:'超过1000位专业配音师，覆盖各类配音风格和语言。'
    },{
        img:whyChoose2,
        title:'极速配音服务',
        describe:'5分钟快速生成，24小时内交付满足紧急需求。'
    },{
        img:whyChoose3,
        title:'版权安全保障',
        describe:'签署正规合同，提供完整版权确保作品安全。'
    },{
        img:whyChoose4,
        title:'贴心售后服务',
        describe:'专业客服团队，提供7*24小时在线支持。'
    },
])
defineExpose({
    list
})
</script>
<style lang="scss" scoped>
$bg-images: (
  'why_choose1.png',
  'why_choose2.png',
  'why_choose3.png',
  'why_choose4.png'
);
.whyChoose{
    background-image: url('@/assets/images/realVoice/why_choose.png');
    background-position: 0 0;
    background-size:cover;
    background-repeat: no-repeat;
    padding: 100px 0 101px;
    .whyChoose_contanier{
        width: 1420px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        .whyChoose_title{
            font-size: 36px;
            line-height: 50px;
            color: #222;
            margin-bottom: 60px;
            align-self: center;
        }
        .whyChoose_detail{
            width: 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .whyChoose_detail_item{
                width: 329px;
                height: 400px;
                display: flex;
                flex-direction: column;
                align-items:flex-start;
                margin-right: 28px;
                padding: 40px;
                padding-bottom: 0;
                @for $i from 1 through length($bg-images) {
                    &:nth-child(#{$i}) {
                    background-image: url('@/assets/images/realVoice/#{nth($bg-images, $i)}');
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    }
                }
                .whyChoose_detail_item_title{
                    font-size: 26px;
                    color: #fff;
                    line-height: 36px;
                    margin-bottom: 24px;
                }
                .whyChoose_detail_item_describe{
                    font-size: 14px;
                    color: #fff;
                    line-height: 24px;
                    display: -webkit-box;               
                    -webkit-box-orient: vertical;       
                    -webkit-line-clamp:2;              
                    overflow: hidden;                    
                    text-overflow: ellipsis;             
                    word-break: break-all;  
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
    }
}
</style>