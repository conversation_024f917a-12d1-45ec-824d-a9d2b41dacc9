<template>
<div class="real_voice_detail">
    <Header :fixedHeader="true"></Header>
   
    <div class="real_voice_detail_contanier">
      <div class="real_voice_detail_content">
        <!-- <div class="real_voice_detail_tools">
            <div class="real_voice_detail_tools_item">
              <div class="real_voice_detail_tools_item_img">
                <img :src="operate.collect?collectIcon:uncollectIcon" @click="change_collect"  alt="">
              </div>
              收藏
            </div>
            <div class="real_voice_detail_tools_item">
              <div class="real_voice_detail_tools_item_img">
                <img src="@/assets/images/realVoice/real_voice_detail_share.svg" alt="">
              </div>
              分享
            </div>
        </div> -->

        <el-breadcrumb>
          <el-breadcrumb-item :to="{ path: '/' }">
          工作台
        </el-breadcrumb-item>
        <template #separator>
          <img src="@/assets/images/realVoice/real_voice_detail_arrow.png" alt="Separator" style="width: 16px; height: 16px; margin: 0 5px;">
        </template>
        <el-breadcrumb-item :to="{ path: '/allRealVoice' }">
          真人配音列表
        </el-breadcrumb-item>
      </el-breadcrumb>
      <div class="real_voice_detail_profile">
        <div class="real_voice_detail_profile_title">
            <span class="real_voice_detail_profile_great">擅长类型：{{userInfo.recommendTags}}</span>
            <div class="real_voice_detail_profile_measure">
              <div class="real_voice_detail_profile_measure_item">
                <span><span class="real_voice_detail_profile_measure_price">¥<b>{{ userInfo.adPrice }}</b></span>/100字</span>
              </div>
              <div class="real_voice_detail_profile_measure_item">
                <span><span class="real_voice_detail_profile_measure_price">¥<b>{{ userInfo.specialPrice }}</b></span>/起配</span>
              </div>
            </div>
        </div>
        <div class="real_voice_detail_profile_info">
          <div class="real_voice_detail_profile_info_img">
            <img :src="userInfo.avatarUrl" alt="">
          </div>
          <div class="real_voice_detail_profile_info_text">
            <div class="real_voice_detail_profile_info_text_top">
              <span class="real_voice_detail_profile_info_text_name">{{userInfo.platformNickname }}</span>
              <span class="real_voice_detail_profile_info_text_mark">{{userInfo.voiceLevel}}</span>
              <div class="real_voice_detail_profile_info_text_works">
                <!-- <span>作品：9</span> -->
                <span>成交：{{ userInfo.memo }}</span>
              </div>
            </div>
           
          </div>
          <button class="real_voice_detail_profile_info_payment" @click="order_now">立即下单</button>
          <div class="real_voice_detail_profile_info_text_describe">
              {{ userInfo.memoText }}
            </div>
        
        </div>
      </div>
      <detailList ref="detail_list_ref"></detailList>
    </div>
    </div>
   <contact ref="contact_ref"></contact>
</div>
</template>
<script setup>
   import Header from "@/views/modules/mainPage/components/headbar/index.vue"
   import {reactive,ref,defineExpose,onMounted,onUnmounted,onActivated,getCurrentInstance } from 'vue'
   import contact from './components/index/contact.vue'
   import detailList from './components/index/detailList.vue'
   import uncollectIcon from "@/assets/images/realVoice/real_voice_detail_uncollect.svg"
   import collectIcon from "@/assets/images/realVoice/real_voice_detail_collect.png"
   import { ElMessage } from 'element-plus'
   import { useRealStore } from '@/stores/modules/realVoice.js' 
   import { useloginStore } from '@/stores/login'
   const { proxy } = getCurrentInstance();
   let loginStore = useloginStore() 
   let realStore=useRealStore()
   let contact_ref=ref(null)
   let detail_list_ref=ref(null)
   let operate=reactive({
    collect:false
   })
   let userInfo=ref({})
   
   let change_collect=()=>{
    operate.collect=!operate.collect
    if(operate.collect){
      ElMessage.success(`已收藏`)
    }else{
      ElMessage.success(`未收藏`)
    }
   }
   let order_now=()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    contact_ref.value.dialogVisible=true
   }

   onMounted(()=>{
    userInfo.value=realStore.detail
    console.log(userInfo.value,'userInfo');
    
    detail_list_ref.value.initList( userInfo.value.audioInfos)
     if (!localStorage.getItem('hasReloaded')) {
      localStorage.setItem('hasReloaded', 'true');
      window.location.reload();
    }

   })
</script>
<style lang="scss">
body{
  background-color: rgb(247,247,247);
}
.real_voice_detail{
  width: 1400px;
  margin: 0 auto;
  .real_voice_detail_contanier{
    width: 1200px;
    margin: 0 auto;
    padding-top:60px;
    
    .real_voice_detail_content{
      position: relative;
 
  .real_voice_detail_tools{
    width: 56px;
    height: 124px;
    position: absolute;
    padding: 8px 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    left: -88px;
    top:63px;
    background-color: #fff;
    z-index: 2;
    .real_voice_detail_tools_item{
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: rgba(32, 37, 42, 0.3);
      font-size: 12px;
      justify-content: center;
      line-height: 24px;
      cursor: pointer;
      padding: 12px 0;
      position: relative;
      .real_voice_detail_tools_item_img{
        width: 18px;
        height: 18px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
      }
      &::after{
        content: '';
        width: 16px;
        height: 1px;
        background-color: rgba(156, 163, 175, 0.45);
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
      }
      &:first-child{
        padding-top: 0;
      }
      &:last-child{
        padding-bottom: 0;
        &::after{
          background-color: transparent;
        }
      }
    }
  }
    .el-breadcrumb{
      margin-top: 30px;
      margin-bottom: 42px;
      .el-breadcrumb__item{
        font-size: 14px;
        line-height: 21px;
        color: #7E8C8D;
        display: flex;
        align-items: baseline;
        height: 21px;
        .el-breadcrumb__inner{
          &.is-link{
            font-weight: normal;
            color: #7E8C8D;
            cursor: pointer;
          }
        }
        .el-breadcrumb__separator{
          font-size: 9px;
        }
      }
    }
    .real_voice_detail_profile{
      background-color: #fff;
      padding: 16px 24px;
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 45px;
      .real_voice_detail_profile_title{
        display: flex;
        align-items: center;
        margin-bottom: 3px;
        .real_voice_detail_profile_great{
          font-weight: bold;
          font-size: 24px;
          color: #1F2937;
          line-height: 27px;
          margin-top: 5px;
          margin-bottom: 5px;
        }
        .real_voice_detail_profile_measure{
          margin-left: auto;
          display: flex;
          align-items: center;
          .real_voice_detail_profile_measure_item{
            font-size: 12px;
            color: #6B7280;
            line-height: 28px;
            margin-right: 15px;
            &>span{
              display: flex;
              align-items: center;
              color: #6B7280;
              font-weight: 500;
               line-height: 16px;
              .real_voice_detail_profile_measure_price{
                align-items: baseline;
                font-size: 13px;
                color: #EB6B2D;
                display: inline-block;
                line-height: 16px;
                b{
                  font-weight: bold;
                  font-size: 18px;
                  line-height: 16px;
                  display: inline-block;
                }
              }
            }
            
            &:last-child{
              margin-right: 0;
            }
          }
          
        }
      }
      .real_voice_detail_profile_info{
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        .real_voice_detail_profile_info_img{
          width: 60px;
          height: 60px;
          background: #ECF5FF;
          border-radius: 50%;
          margin-right: 12px;
          overflow: hidden;
          margin-top: 9px;
          padding: 2px 1px 0;
          img{
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .real_voice_detail_profile_info_text{
          margin-top: 9px;
          .real_voice_detail_profile_info_text_top{
            display: flex;
            align-items: center;
            margin-top: 3px;
            flex-wrap: wrap;
            .real_voice_detail_profile_info_text_name{
              line-height: 26px;
              font-weight: bold;
              font-size: 18px;
              color: #1F2937;
              margin-right: 4px;
              display: inline-block;
              margin-bottom: 5px;
              
            }
            .real_voice_detail_profile_info_text_mark{
                margin-top: 3px;
                margin-bottom: 8px;
                line-height: 20px;
                font-size: 12px;
                color:#409EFF;
                display: inline-block;
                background: #ECF5FF;
                border: 1px solid #D9ECFF;
                border-radius: 2px;
                padding: 0px 8px;
                height: 20px; 
                box-sizing: border-box;
            }
            .real_voice_detail_profile_info_text_works{
              width: 100%;
              display: flex;
              align-items: center;
              span{
                color:#9CA3AF;
                font-size: 14px;
                line-height: 22px;
                display: inline-block;
                margin-right: 16px;
              }
            }
          }
    
        }
        .real_voice_detail_profile_info_text_describe{
            font-size: 12px;
            color:#6B7280;
            line-height: 16px;
            width: 100%;
            margin-top: 12px;
          }
        .real_voice_detail_profile_info_payment{
          margin-left: auto;
          width: 104px;
          height: 40px;
          border-radius: 4px;
          border: none;
          background: #0AAF60;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          color: #fff;
          cursor: pointer;
          margin-top: 3px;
        }
      }
      
    }
    
  }
  }
}
</style>