<template>
  <div class="realVoice">
    <Headbar  :fixedHeader="true"/>
    <div class="real_voice_contaier">
      <banner :banners="banners" @freeSound="free_sound" @learnMore="learn_more" ></banner>
      <PopularLiveStreamers ref="popular_live_streamers_ref"></PopularLiveStreamers>
      <suitScen ref="suit_scen_ref"></suitScen>
      <serveProcess ref="serve_process_ref"></serveProcess>
      <partner ref="partner_ref"></partner>
      <whyChoose ref="why_choose_ref"></whyChoose>
      <Footer></Footer>
      <addCustomer ref="add_customer_ref"></addCustomer>
      <freeTrialSound ref="free_trial_sound_ref" ></freeTrialSound>
    </div>
    
    </div>
    <contact ref="contact_ref" ></contact>
   
</template>
  
  <script setup>
  import { ref,onActivated,onMounted,onDeactivated } from 'vue';
  import banner from './components/index/banner.vue';
  // import bannerImg from '@/assets/images/realVoice/banner.jpg'
  import bannerImg1 from '@/assets/images/realVoice/banner1.jpg'
  import bannerImg2 from '@/assets/images/realVoice/banner2.jpg'
  import bannerImg3 from '@/assets/images/realVoice/banner3.jpg'
  import bannerBtnImg from '@/assets/images/realVoice/banner_btn.png'
  import bannerBtnImg1 from '@/assets/images/realVoice/banner_btn1.png'
  import bannerBtnImg2 from '@/assets/images/realVoice/banner_btn2.png'
  import bannerBtnImg3 from '@/assets/images/realVoice/banner_btn3.png'
  import PopularLiveStreamers from "./components/index/popularLivesStreamers.vue"
  import suitScen from "./components/index/suitScen.vue"
  import serveProcess from "./components/index/serveProcess.vue"
  import partner from "./components/index/partner.vue"
  import whyChoose from "./components/index/whyChoose.vue"
  import Footer from '@/views/modules/H4Home/components/footer.vue'
  import { useRouter,useRoute } from 'vue-router'
  import contact from './components/index/contact.vue'
  import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
  import {getAllForReal} from "@/api/realVoice.js"
  import freeTrialSound from './components/index/free_trial_sound.vue'
  import addCustomer from './components/index/add_customer.vue'
  let router = useRouter()
  let route = useRoute()
  let contact_ref=ref(null)
  let add_customer_ref=ref(null)
  let free_trial_sound_ref=ref(null)
  const banners = ref([
    // {
    //   title: '专业配音服务',
    //   btn:{
    //     left:1240,
    //     top:352,
    //     width:331,
    //     height:121,
    //     image:bannerBtnImg
    //   },
    //   image: bannerImg,
    // },
 {
      title: '专业配音服务',
      btn:{
        left:723,
        top:362,
        width:354,
        height:114,
        image:bannerBtnImg1
      },
      image: bannerImg1,
    },
    {
      title: '专业配音服务',
      btn:{
        left:393,
        top:353,
        width:350,
        height:123,
        image:bannerBtnImg2
      },
      image: bannerImg2,
    },
    {
      title: '专业配音服务',
      btn:{
        left:361,
        top:367,
        width:297,
        height:111,
        image:bannerBtnImg3
      },
      image: bannerImg3,
    },
    // {
    //   title: '专业配音服务',
    //   description: '为您的作品注入灵魂之声',
    //   remark: '5分钟极速配音 · 专业配音师 · 高品质服务',
    //   image: bannerImg,
    // },
    // {
    //   title: '专业配音服务',
    //   description: '为您的作品注入灵魂之声',
    //   remark: '5分钟极速配音 · 专业配音师 · 高品质服务',
    //   image: bannerImg,
    // },
  ]);
  let popular_live_streamers_ref=ref(null)
  let suit_scen_ref=ref(null)
  let serve_process_ref=ref(null)
  let partner_ref=ref(null)
  let why_choose_ref=ref(null)
  let free_sound=()=>{
     if(add_customer_ref.value){
      add_customer_ref.value.close()
    }
    free_trial_sound_ref.value.dialogVisible = true;
      // 同步写入localStorage，标记当天已展示
  let key = 'freeTrialSoundLastShowDate';
  let today = new Date().toISOString().slice(0, 10);
  localStorage.setItem(key, today);
  }
  let learn_more=()=>{
    router.push('/allRealVoice')
  }
  let getList=async()=>{
    console.log('getList');
    
    let key = 'freeTrialSoundLastShowDate';
    let today = new Date().toISOString().slice(0, 10);
    if (localStorage.getItem(key) !== today) {
      console.log('弹出');
      
      add_customer_ref.value.dialogVisible = true;
      localStorage.setItem(key, today);
    }
      popular_live_streamers_ref.value.list_loading=true
      let result=await getAllForReal({tts:6})
      // if(route.name=='realVoice'){
      //     list.value=result.slice(0,12)
      // }else{
      //     list.value=result
      // }
      console.log(result,'result');
      if (popular_live_streamers_ref.value && popular_live_streamers_ref.value.init) {
        console.log('传入值');
        
          popular_live_streamers_ref.value.init(result.data?result.data:result)
      }
  }
  onMounted(() => {
    getList()

});
onDeactivated(()=>{
  popular_live_streamers_ref.value.close_aduio()
})
  </script>
  
  <style lang="scss" scoped>
.realVoice{
  padding-top: 64px;
  width: 100%;
  box-sizing: border-box;
  background-color: rgb(249,250,251);
  padding-bottom: 0;
  overflow-y: scroll;
  overflow-x: clip; /* 使用clip代替auto，防止下拉显示空白区域 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  min-height: 100vh; /* 确保至少填满视口高度 */
  display: flex;
  flex-direction: column;
  ::v-deep(.headbar-container){
    border:none;
  }
  .real_voice_contaier{
    position: relative;
  }
}
  </style>
  <style>
  html,body{
      overflow-y: hidden;
  }
  </style>
  