<template>
    <div class="soundStore_filter_tag">

        <div class="soundStore_filter_tag_type">
            <span v-for="(tag, index) in tags" :key="index" @click="change_title(index)"
                :class="tag.type == current_type ? 'current' : ''">{{ tag.title }}</span>
        </div>
        <div class="soundStore_filter_tag_type_list" v-loading="tag_loading"
            v-if="getCurrentTypeList(current_type) && getCurrentTypeList(current_type)['condition']">
            <div class="soundStore_filter_tag_type_list_content" >
                <div v-for="(tag, index) in Object.keys(getCurrentTypeList(current_type)['condition'])" :key='index' style="width: 100%;display: flex;position: relative;">
                    <div class="soundStore_filter_tag_type_list_tyepe"  :ref="el => getRef(el, index)"
                        v-if="getCurrentTypeList(current_type)['condition'][tag].arr.length > 0"
                        :style="{ 'width': showMore[tag] ? '1180px' : '100%' }">
                        <span v-for="(item, index1) in displayedItems[tag]" :key='index1'
                            :class="current_tag[tag] == item.key ? 'current' : ''" @click="select_tag(tag, item, index)"
                            class="soundStore_filter_tag_type_list_item">
                            {{ item.label }}
                        </span>
                    </div>
                    <span @click="displayedItemsMore(index, tag)"   class="soundStore_filter_tag_type_list_more"
                            v-if="overflowStatus[index]">
                            <button>
                                更多
                                <el-icon v-if="showMore[tag]"><ArrowUpBold/></el-icon>
                                <el-icon v-else><ArrowDownBold/></el-icon>
                            </button>
                            <!-- <template v-if="showMore[tag]">收起</template>
                            <template v-else>更多</template> -->
                        </span>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { reactive, ref, onMounted, defineExpose, nextTick, toRefs, defineEmits } from 'vue'
import Index from '../index.vue';
let tags = ref([
    {
        type: '4',
        title: 'AI配音',
        value: '4',
        condition: {
            grade: {
                name: "领域",
                arr: [

                    // {
                    //     key: '影视',
                    //     label: '影视'
                    // }, {
                    //     key: '小说',
                    //     label: '小说'
                    // }, {
                    //     key: '情感',
                    //     label: '情感'
                    // }, {
                    //     key: 'rap',
                    //     label: 'rap'
                    // }, {
                    //     key: '纪录片',
                    //     label: '纪录片'
                    // }, {
                    //     key: '广告',
                    //     label: '广告'
                    // },
                    // {
                    //     key: '娱乐',
                    //     label: '娱乐'
                    // },
                    // {
                    //     key: '宣传片',
                    //     label: '宣传片'
                    // },
                    // {
                    //     key: '快板',
                    //     label: '快板'
                    // },
                    // {
                    //     key: '朗诵',
                    //     label: '朗诵'
                    // },
                    // {
                    //     key: '直播',
                    //     label: '直播'
                    // },
                    // {
                    //     key: '书单',
                    //     label: '书单'
                    // },
                    // {
                    //     key: '助理',
                    //     label: '助理'
                    // },
                    // {
                    //     key: '游戏',
                    //     label: '游戏'
                    // },
                    // {
                    //     key: '动漫',
                    //     label: '动漫'
                    // },
                    // {
                    //     key: '美食',
                    //     label: '美食'
                    // },
                    // {
                    //     key: '方言',
                    //     label: '方言'
                    // },
                ]
            },
            gender: {
                name: "语言",
                arr: [

                    // {
                    //     key: '普通话',
                    //     label: '普通话'
                    // }, {
                    //     key: '台湾腔',
                    //     label: '台湾腔'
                    // }, {
                    //     key: '粤语',
                    //     label: '粤语'
                    // }, {
                    //     key: '四川话',
                    //     label: '四川话'
                    // }, {
                    //     key: '湖南话',
                    //     label: '湖南话'
                    // }, {
                    //     key: '浙普',
                    //     label: '浙普'
                    // },
                    // {
                    //     key: '东北话',
                    //     label: '东北话'
                    // },
                    // {
                    //     key: '英文',
                    //     label: '英文'
                    // },
                    // {
                    //     key: '北京话',
                    //     label: '北京话'
                    // },
                    // {
                    //     key: '天津话',
                    //     label: '天津话'
                    // },
                    // {
                    //     key: '河南话',
                    //     label: '河南话'
                    // },
                    // {
                    //     key: '日语',
                    //     label: '日语'
                    // },
                    // {
                    //     key: '港台腔',
                    //     label: '港台腔'
                    // },
                    // {
                    //     key: '湖北话',
                    //     label: '湖北话'
                    // },
                    // {
                    //     key: '说唱腔',
                    //     label: '说唱腔'
                    // },
                ]
            },
            sceneCategory: {
                name: "年龄",
                arr: [

                    // {
                    //     key: '儿童',
                    //     label: '儿童'
                    // }, {
                    //     key: '少年、少女',
                    //     label: '少年、少女'
                    // }, {
                    //     key: '青年',
                    //     label: '青年'
                    // }, {
                    //     key: '壮年',
                    //     label: '壮年'
                    // }, {
                    //     key: '中年',
                    //     label: '中年'
                    // }, {
                    //     key: '老年',
                    //     label: '老年'
                    // },
                ]
            },
            recommendTags: {
                name: "性别",
                arr: [
                    // {
                    //     key: '男声',
                    //     label: '男声'
                    // }, {
                    //     key: '女生',
                    //     label: '女生'
                    // }
                ]
            },
            style: {
                name: "风格",
                arr: [
                    // {
                    //     key: '儒雅',
                    //     label: '儒雅'
                    // }, {
                    //     key: '惊讶',
                    //     label: '惊讶'
                    // },
                    // {
                    //     key: '伤感',
                    //     label: '伤感'
                    // }, {
                    //     key: '生气',
                    //     label: '生气'
                    // },
                    // {
                    //     key: '激情',
                    //     label: '激情'
                    // }, {
                    //     key: '甜美',
                    //     label: '甜美'
                    // },
                    // {
                    //     key: '淳朴',
                    //     label: '淳朴'
                    // }, {
                    //     key: '严谨',
                    //     label: '严谨'
                    // },
                    // {
                    //     key: '客服',
                    //     label: '客服'
                    // }, {
                    //     key: '欢快',
                    //     label: '欢快'
                    // },
                    // {
                    //     key: '大气',
                    //     label: '大气'
                    // }, {
                    //     key: '导游',
                    //     label: '导游'
                    // }, {
                    //     key: '读书',
                    //     label: '读书'
                    // }, {
                    //     key: '恐怖',
                    //     label: '恐怖'
                    // },
                    // {
                    //     key: '搞笑',
                    //     label: '搞笑'
                    // }, {
                    //     key: '温情',
                    //     label: '温情'
                    // },
                ]
            },
            expenses: {
                name: "资费",
                arr: [
                    // {
                    //     key: 'VIP',
                    //     label: 'VIP'
                    // }, {
                    //     key: 'SVIP',
                    //     label: 'SVIP'
                    // }
                ]
            },
        },


    }, {
        type: '5',
        title: 'AI商配',
        condition: {
            grade: {
                name: "领域",
                arr: [

                    // {
                    //     key: '影视',
                    //     label: '影视'
                    // }, {
                    //     key: '小说',
                    //     label: '小说'
                    // }, {
                    //     key: '情感',
                    //     label: '情感'
                    // }, {
                    //     key: 'rap',
                    //     label: 'rap'
                    // }, {
                    //     key: '纪录片',
                    //     label: '纪录片'
                    // }, {
                    //     key: '广告',
                    //     label: '广告'
                    // },
                    // {
                    //     key: '娱乐',
                    //     label: '娱乐'
                    // },
                    // {
                    //     key: '宣传片',
                    //     label: '宣传片'
                    // },
                    // {
                    //     key: '快板',
                    //     label: '快板'
                    // },
                    // {
                    //     key: '朗诵',
                    //     label: '朗诵'
                    // },
                    // {
                    //     key: '直播',
                    //     label: '直播'
                    // },
                    // {
                    //     key: '书单',
                    //     label: '书单'
                    // },
                    // {
                    //     key: '助理',
                    //     label: '助理'
                    // },
                    // {
                    //     key: '游戏',
                    //     label: '游戏'
                    // },
                    // {
                    //     key: '动漫',
                    //     label: '动漫'
                    // },
                    // {
                    //     key: '美食',
                    //     label: '美食'
                    // },
                    // {
                    //     key: '方言',
                    //     label: '方言'
                    // },
                ]
            },
            gender: {
                name: "语言",
                arr: [

                    // {
                    //     key: '普通话',
                    //     label: '普通话'
                    // }, {
                    //     key: '台湾腔',
                    //     label: '台湾腔'
                    // }, {
                    //     key: '粤语',
                    //     label: '粤语'
                    // }, {
                    //     key: '四川话',
                    //     label: '四川话'
                    // }, {
                    //     key: '湖南话',
                    //     label: '湖南话'
                    // }, {
                    //     key: '浙普',
                    //     label: '浙普'
                    // },
                    // {
                    //     key: '东北话',
                    //     label: '东北话'
                    // },
                    // {
                    //     key: '英文',
                    //     label: '英文'
                    // },
                    // {
                    //     key: '北京话',
                    //     label: '北京话'
                    // },
                    // {
                    //     key: '天津话',
                    //     label: '天津话'
                    // },
                    // {
                    //     key: '河南话',
                    //     label: '河南话'
                    // },
                    // {
                    //     key: '日语',
                    //     label: '日语'
                    // },
                    // {
                    //     key: '港台腔',
                    //     label: '港台腔'
                    // },
                    // {
                    //     key: '湖北话',
                    //     label: '湖北话'
                    // },
                    // {
                    //     key: '说唱腔',
                    //     label: '说唱腔'
                    // },
                ]
            },
            sceneCategory: {
                name: "年龄",
                arr: [

                    // {
                    //     key: '儿童',
                    //     label: '儿童'
                    // }, {
                    //     key: '少年、少女',
                    //     label: '少年、少女'
                    // }, {
                    //     key: '青年',
                    //     label: '青年'
                    // }, {
                    //     key: '壮年',
                    //     label: '壮年'
                    // }, {
                    //     key: '中年',
                    //     label: '中年'
                    // }, {
                    //     key: '老年',
                    //     label: '老年'
                    // },
                ]
            },
            recommendTags: {
                name: "性别",
                arr: [
                    // {
                    //     key: '男声',
                    //     label: '男声'
                    // }, {
                    //     key: '女生',
                    //     label: '女生'
                    // }
                ]
            },
            style: {
                name: "风格",
                arr: [
                    // {
                    //     key: '儒雅',
                    //     label: '儒雅'
                    // }, {
                    //     key: '惊讶',
                    //     label: '惊讶'
                    // },
                    // {
                    //     key: '伤感',
                    //     label: '伤感'
                    // }, {
                    //     key: '生气',
                    //     label: '生气'
                    // },
                    // {
                    //     key: '激情',
                    //     label: '激情'
                    // }, {
                    //     key: '甜美',
                    //     label: '甜美'
                    // },
                    // {
                    //     key: '淳朴',
                    //     label: '淳朴'
                    // }, {
                    //     key: '严谨',
                    //     label: '严谨'
                    // },
                    // {
                    //     key: '客服',
                    //     label: '客服'
                    // }, {
                    //     key: '欢快',
                    //     label: '欢快'
                    // },
                    // {
                    //     key: '大气',
                    //     label: '大气'
                    // }, {
                    //     key: '导游',
                    //     label: '导游'
                    // }, {
                    //     key: '读书',
                    //     label: '读书'
                    // }, {
                    //     key: '恐怖',
                    //     label: '恐怖'
                    // },
                    // {
                    //     key: '搞笑',
                    //     label: '搞笑'
                    // }, {
                    //     key: '温情',
                    //     label: '温情'
                    // },
                ]
            },
            expenses: {
                name: "资费",
                arr: [
                    // {
                    //     key: 'VIP',
                    //     label: 'VIP'
                    // }, {
                    //     key: 'SVIP',
                    //     label: 'SVIP'
                    // }
                ]
            },
        }
    }
])
let tagRefs = ref({});
let overflowStatus = ref({}); // 用于存储每个标签的溢出状态
let displayedItems = ref({}); // 存储每个标签组的显示项
let sceneMetadata = ref([])
let uniqueArray = ref([])//原始所有子列
let lineArray = ref([])//只展示一行子列
let emit = defineEmits(['changeNav', 'changesubNav'])
let getRef = (el, index) => {
    if (el) {
        tagRefs.value[index] = el;
    } else {
        // 元素被卸载时，清除对应的 ref
        delete tagRefs.value[index];
    }
}
let showMore = reactive({

})
let tag_loading = ref(true)
let setTags = (tag, index) => {
    if(selectLastIndex.value!=index){
        overflowStatus.value[index] = false
    }else{
        // showMore[tag]=false
        return
    }
    let displayedItemsList = getCurrentTypeList(current_type.value)['condition'][tag].arr;
    nextTick(() => {
        lineArray.value[tag] = []
        let the_data = checkOverflow(displayedItemsList, index)
        lineArray.value[tag] = the_data
        displayedItems.value[tag] = the_data
    });
};
let checkOverflow = (displayedItemsList, index) => {
    const parentWidth = tagRefs.value[index].clientWidth; // 获取父元素宽度
    let totalWidth = 0;

    // 计算标题的宽度
    const title = tagRefs.value[index].querySelector('.soundStore_filter_tag_type_list_title');
    if (title) {
        totalWidth += title.offsetWidth + parseFloat(window.getComputedStyle(title).marginRight);
    }

    // calculateTotalWidth(displayedItemsList,index)
   

    // 计算所有子元素的总宽度
    if(index==2&&current_tag.value['sceneCategory']=='全部'){
        
        // console.log(displayedItemsList,999)
    //     displayedItemsList=uniqueArray.value
    }
  
    
    displayedItemsList.forEach((item1, index1) => {

setTimeout(()=>{
        const child = tagRefs.value[index].children[index1];
        // console.log(child,'child');
        
        if (child) {
            totalWidth += child.offsetWidth + parseFloat(window.getComputedStyle(child).marginRight);
        }
        // if(index==2){
        //     console.log(totalWidth , parentWidth,displayedItemsList,333);
        // }
       
        
        if (totalWidth > parentWidth) {
        showMore[Object.keys(getCurrentTypeList(current_type.value)['condition'])[index]] = false
        overflowStatus.value[index] = true
        // 隐藏超出的子元素，只显示前几个
        while (totalWidth > parentWidth && displayedItemsList.length > 1) {
            displayedItemsList.pop();
            totalWidth = 0; // 重新计算总宽度
            // 重新计算标题的宽度
            if (title) {
                totalWidth += title.offsetWidth + parseFloat(window.getComputedStyle(title).marginRight);
            }
            // 重新计算所有子元素的总宽度
            displayedItemsList.forEach((item1, index1) => {
                const child = tagRefs.value[index].children[index1 + 1]; // +1 因为第一个是标题
                if (child) {
                    totalWidth += child.offsetWidth + parseFloat(window.getComputedStyle(child).marginRight);
                }
            });
        }
    }
    },200)
    });
   
    // 判断是否溢出
   
    return displayedItemsList;




};
let calculateTotalWidth = (displayedItemsList,index) => {
    return new Promise((resolve) => {
    let totalWidth=0,num=0
    displayedItemsList.forEach((item1, index1) => {

setTimeout(()=>{


        const child = tagRefs.value[index].children[index1];
// console.log(child.offsetWidth,'child77');

        if (index == 2) {

        }
        if (child) {
            totalWidth += child.offsetWidth + parseFloat(window.getComputedStyle(child).marginRight);
        }
        return totalWidth
        num++
        if(num==displayedItemsList.length){
            resolve(totalWidth)
           
        }
    },200)
    });
})

}
let current_type = ref("")
let getCurrentTypeList = (type) => {
    let result = tags.value.filter((item) => {
        return item.type == type
    })
    return result[0]
}
let selectLastIndex=ref(0)
let current_tag = ref({})
let select_tag = (type, tag, index) => {
    // console.log(type, tag, index,'select_tag');
    selectLastIndex.value=index
    let index1 = tags.value.findIndex((item) => item.type == current_type.value)
    let data = Object.keys(tags.value[index1].condition)
    let index2 = data.findIndex(item => item == type)
    // if (index == 1) {
    //     if (tag.label == '全部') {
    //         if (current_tag.value[type] != tag.key) {
    //             current_tag.value[data[index2 + 1]] = '全部'
    //         }
    //         tags.value[index1].condition[data[index2 + 1]].arr =JSON.parse(JSON.stringify(uniqueArray.value)) 
    //     } else {
    //         let data1 = sceneMetadata.value.findIndex((item) => item.scene_category == tag.label)


    //         tags.value[index1].condition[data[index2 + 1]].arr = sceneMetadata.value[data1].recommend_tags.map((item) => {
    //             return {
    //                 key: item.recommend_tags,
    //                 label: item.recommend_tags,
    //             };
    //         })
    //         tags.value[index1].condition[data[index2 + 1]].arr.unshift({
    //             key: '全部',
    //             label: '全部'
    //         })
    //     }
    // }
    current_tag.value[type] = tag.key
    let nav_data = {}
    Object.keys(current_tag.value).map((item) => {
        if (current_tag.value[item] != '全部') {
            nav_data[item] = current_tag.value[item]
        }
    })

    emit("changesubNav", nav_data)
    index == 1 && updateDom()



}
let init = () => {

    // current_tag.value = {}
    // current_type.value = tags.value[0].type
    tags.value.map((item) => {
        Object.keys(item.condition).map((item1) => {
            // console.log(item.condition[item1],item1,666);
            const exists = item.condition[item1].arr.some(item => item.key === '全部');
            if (item.condition[item1].arr.length > 0&&!exists) {
          
                item.condition[item1].arr.unshift({
                    key: '全部',
                    label: '全部'
                })
                // item.condition[item1].arr.push({
                //     key: '更多',
                //     label: '更多'
                // })
                current_tag.value[item1] = item.condition[item1].arr[0].key
            }


        })
    })
    updateDom()

}
let updateDom = () => {
    nextTick(() => {
        Object.keys(getCurrentTypeList(current_type.value)['condition']).forEach((tag, index) => {
           
            
            if (getCurrentTypeList(current_type.value)['condition'][tag].arr.length > 0) {
                setTags(tag, index)
            }
        });
        tag_loading.value = false
    })
}
let change_title = (index) => {
    current_type.value = tags.value[index].type
    Object.keys(current_tag.value).map((item) => {
        current_tag.value[item] = '全部'
    })

    emit('changeNav', tags.value[index].type)
}
let publicdisplayedItemsMore = (index, tag) => {
    // 显示所有子元素
    if (!showMore[tag]) {
        let data = JSON.parse(JSON.stringify(uniqueArray.value[tag]))
        displayedItems.value[tag] = data.arr;
    } else {
        displayedItems.value[tag] = lineArray.value[tag]
    }
}
let displayedItemsMore = (index, tag) => {
    publicdisplayedItemsMore(index, tag)
    showMore[tag] = !showMore[tag]
}
defineExpose({
    tags,
    current_type,
    current_tag,
    init,
    updateDom,
    sceneMetadata,
    uniqueArray,
    tag_loading,
    change_title,
    selectLastIndex
})

onMounted(() => {
    init()

})
</script>
<style lang="scss" scoped>
.soundStore_filter_tag {
    padding: 18px 19px 2px 13px;
    background: linear-gradient(0deg, #E7F7EA 0%, rgba(231, 247, 234, 0) 100%);
    border-radius: 8px;
    box-sizing: border-box;
    margin-bottom: 31px;
    width: 100%;

    .soundStore_filter_tag_type {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        span {
            font-size: 18px;
            color: #271E17;
            margin-right: 32px;
            cursor: pointer;

            &.current {
                color: #18AD25;
            }

            &:last-child {
                margin-right: 0;
            }
        }
    }

    .soundStore_filter_tag_type_list {
        width: 100%;
        overflow: hidden;

        // .soundStore_filter_tag_type_list_content{
        //     width: 1152px;
        // }
        .soundStore_filter_tag_type_list_tyepe {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            /* 不换行 */
            // overflow: hidden;

            /* 隐藏超出部分 */
            .soundStore_filter_tag_type_list_title {
                font-size: 14px;
                color: #271E17;
                margin-right: 22px;
                display: inline-block;
                white-space: nowrap;
                cursor: pointer;
            }

            .soundStore_filter_tag_type_list_item {
                margin-right: 55px;
                font-size: 14px;
                color: #271E17;
                display: inline-block;
                white-space: nowrap;
                cursor: pointer;
                line-height: 43px;

                &.current {
                    font-size: 14px;
                    color: #18AD25;
                }

                &:last-child {
                    margin-right: 0;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
        .soundStore_filter_tag_type_list_more{
            width: 60px;
            height: 43px;
            line-height: 43px;
            display: inline-block;
            position: absolute;
            right: 0;
            top: 0;
            color: #596472;
            text-align: center;
            flex-shrink: 0;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            button{
                background-color: transparent;
                padding: 0 8px;
                width: 100%;
                height: 30px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                cursor: pointer;
                justify-content: center;
                border: none;
                border-radius: 4px;
                .el-icon{
                    margin-left: 2px;
                }
            }
            
            &:hover{
                button{
                    background: rgba(10, 175, 96, .1);
                    color: #18AD25;
                }
            }
        }

    }
}
</style>
