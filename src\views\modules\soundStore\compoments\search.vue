<template>
    <div class="soundStore_search">
        <div class="soundStore_search_input_box">
            <el-select
                v-model="searchQuery"
                clearable
                filterable
                remote
                reserve-keyword
                allow-create 
                placeholder="请输入搜索内容"
                :remote-method="remoteMethod"
                :loading="loading"
                ref="select"
                class="soundStore_select"
            >
                <el-option
                    v-for="item in options"
                    :key="item.platformNickname"
                    :label="item.platformNickname"
                    :value="item.platformNickname"
                />
            </el-select>
            <el-input
                v-model="searchQuery" 
                @input="handleInputChange"
                clearable
                placeholder="请输入搜索内容"
                class="soundStore_search_input"
            />
        </div>
        
        <el-button type="primary" @click="handleSearch">
            <img src="@/assets/images/soundStore/search.png" alt="">声音搜索
        </el-button>
    </div>
</template>

<script setup>
import { reactive, ref, defineEmits } from 'vue';

let searchQuery = ref('');
let options = ref([]);
let loading = ref(false);
let list = ref([]);
let select = ref(null);
let searchKeys = reactive({
    platformNickname: ''
});
let emit = defineEmits(['search_list']);

// 搜索处理函数
let handleSearch = () => {
    emit('search_list', search_method(list.value, searchQuery.value));
};

// 远程搜索方法
let remoteMethod = (query) => {
    if (query) {
        loading.value = true;
        // 模拟异步请求
        setTimeout(() => {
            loading.value = false;
            options.value = search_method(list.value, query);
        }, 200);
    } else {
        options.value = [];
    }
};

// 输入框内容变化处理函数
let handleInputChange = (e) => {
    searchQuery.value = e; // 更新搜索查询
    select.value.remoteMethod(searchQuery.value); // 调用远程方法
    select.value.toggleMenu(); // 显示下拉菜单
};

// 模糊搜索方法
let search_method = (data, query) => {
     if (!query) return data;

  const lowerQuery = query.toLowerCase();

  // 精准匹配
  const exactMatches = data.filter(item =>
    Object.keys(searchKeys).some(key =>
      item[key] && item[key].toLowerCase() === lowerQuery
    )
  );

  // 模糊匹配但排除精准匹配的项
  const fuzzyMatches = data.filter(item =>
    Object.keys(searchKeys).some(key =>
      item[key] && item[key].toLowerCase().includes(lowerQuery)
    ) && !exactMatches.includes(item)
  );

  return [...exactMatches, ...fuzzyMatches];
};
defineExpose({
    searchQuery,
    list
})



</script>
<style lang="scss" scoped>
.soundStore_search{
    width: 822px;
    height: 54px;
    background: #FFFFFF;
    box-shadow: 0px 0px 16px 0px rgba(6,79,13,0.1);
    border-radius: 27px;
    margin-bottom: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 7px;
    overflow: hidden;
    .el-input{
        width: 712px;
        ::v-deep(.el-input__wrapper){
            box-shadow: none;
        }

    }
    .el-button {
        width: 104px;
        height: 40px;
        background: #18AD25;
        border-radius: 20px;
        padding: 12px;
        padding-right: 10px;
        display: flex;
        align-items: center;
        border: none;
        ::v-deep(span){
            display: flex;
            width: 100%;
            font-size: 14px;
            color:#fff;
            img{
                width: 15px;
                height: 15px;
                margin-right: 7px;
            }
        }
        
    }
    .soundStore_search_input_box{
    flex: 1;
    position: relative;
    :deep(.soundStore_search_input){
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        z-index: 1;
    }

}
}
:deep(.soundStore_select){
    border: none; /* 隐藏边框 */
    box-shadow: none; /* 去掉阴影 */
    .el-select__wrapper {
        border: none; /* 隐藏输入框边框 */
        box-shadow: none; /* 去掉阴影 */
    }
    // .el-select__suffix{
    //     display: none;
    // }
    :deep(.el-select-dropdown) {
        background-color: red;
  background-color: #fff; /* 下拉框背景色 */
  border: 1px solid #ccc; /* 下拉框边框 */
  border-radius: 4px; /* 下拉框圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 下拉框阴影 */
}

:deep(.el-option) {
    background-color: red;
  padding: 10px; /* 选项内边距 */
  color: #333; /* 选项字体颜色 */
}



}
</style>
    