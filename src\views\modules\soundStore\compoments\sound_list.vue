<template>
    <div class="sound_list" v-loading="list_loading">
        <template v-if="list.length>0">
        <!-- <div class="sound_list_item" :class="item.package?'package':'single'" v-for="(item,index) in list" :key="index" @click="go_sound_store_detail"> -->
        <div class="sound_list_item"  :class="{package: item.package,single: !item.package, play: item.isPlaying,pause: !item.isPlaying}"  v-for="(item,index) in list" :key="index" @click="go_sound_store_detail(item)">
            <!-- <div class="sound_list_item_imgs" :class="!item.package&&(item.isPlaying?'play':'pause')" @click.stop="play_sound(index)"></div> -->
            <div class="sound_list_item_imgs" :class="[item.isPlaying ? 'play' : 'pause',item.price>=500 ? 'special' : '']" @click.stop="play_sound(index,item)">
                    <template v-if="item.package">
                        
                        <div class="sound_list_item_no_imgs">
                               
                            <span class="sound_list_item_no_img" v-for="(item1,index1) in (item.data.slice(0,9))" :key="index1">
                                 <img :src="item1.avatarUrl" alt="" >
                                 <span v-if="index1==8">+{{ item.data.length-9 }}</span>
                            </span>
                        </div>
                        <div class="sound_list_item_imgs_mask"></div>
                    </template>
                    <template v-else>
                        <div class="sound_list_languages">
                            <!-- <span class="sound_list_languages_order">NO: <template v-if="index+1<10">0</template>{{index+1}}</span> -->
                            <span class="sound_list_languages_label">{{ item.languageSkills }}</span>
                        </div>
                        <div class="sound_list_imgs">
                            <img :src="item.avatarUrl" alt="" :style="imgStyle(index)">
                            <div class="sound_list_item_imgs_mask"></div>
                        </div>
                        <div class="sound_list_collect">
                            <img @click.stop="toggle_collect(item,index)" :src="collectSrc(item.bookmark)" alt="">
                        </div>
                    </template>
                  
            </div>
            <div class="sound_list_item_detail">
                <div class="sound_list_item_detail_title">
                  
                    <div class="sound_list_item_detail_title_label">{{item.platformNickname}}</div>
                    <img src="@/assets/images/soundStore/AI.png" alt="" class="sound_list_item_detail_title_tag"/>
                    <img src="@/assets/images/soundStore/detail_package_item_ultimate_perfection.svg" v-if="item.voiceType=='至臻'" class="detail_package_item_ultimate_perfection" alt="">
                </div>
                <!-- <div class="sound_list_item_detail_style_role">
                    <template v-if="item.style_num>0">{{item.style_num}}种风格</template><template v-if="item.style_num>0&&item.role_num>0">,</template><template v-if="item.role_num>0">{{item.role_num}}种角色</template>
                </div> -->
                <div class="sound_list_item_detail_area" v-if="!item.package">
                    擅长领域：
                  <!-- <template v-if="item.package">

                  </template>
                  <template v-else> -->
                    <template v-if="item.sceneCategory&&item.sceneCategory!=''">
                        <template v-for="(item1,index1) in item.sceneCategory.split('、')">
                            
                            {{ item1 }}
                            <template v-if="index1!=item.sceneCategory.split('、').length-1||item.recommendTags.length>0">/</template>
                        </template>
                    </template>
                    <template v-if="item.recommendTags&&item.recommendTags!=''">
                        <template v-for="(item1,index1) in item.recommendTags.split('、')">
                            {{ item1 }}
                            <template v-if="index1<item.recommendTags.split('、').length-1">/</template>
                        </template>
                    </template>
                <!-- </template> -->
                </div>
                <div class="sound_list_item_detail_sign" v-if="item.membershipGrade=='SVIP'||item.membershipGrade=='VIP'">
                   <img :src="imageSrc(item.membershipGrade)" alt=""> {{item.membershipGrade }}
                </div>
            </div>
        </div>
    </template>
    <template v-else>
        <div class="sound_list_no_data">
            <span>更多优质好声音，正在加入音色商店，敬请期待！</span>
        </div>
    </template>
        <detailDialog ref="detail_dialog_ref" @go_package="go_package"></detailDialog>
        <detailPackageDialog ref="detail_package_dialog_ref"></detailPackageDialog>
        <soundThaliDialog ref="sound_thali_dialog_ref"></soundThaliDialog>
        <soundMemberThaliDialog ref="sound_member_thali_dialog_ref"></soundMemberThaliDialog>
    </div>
</template>
<script setup>
import {ref,reactive,defineExpose,onMounted,onUnmounted,getCurrentInstance,onActivated,onDeactivated, nextTick,watch} from "vue"
import SVIPImage from '@/assets/images/soundStore/SVIP.png';
import VIPImage from '@/assets/images/account/avatar_sign.png';
import unCollectImg from '@/assets/images/soundStore/uncollect.png';
import collectImg  from '@/assets/images/soundStore/collect.png';
import detailDialog  from '../detail_dialog.vue';
import detailPackageDialog  from '../detail_package_dialog.vue';
import soundThaliDialog  from '../sound_thali_dialog.vue';
import soundMemberThaliDialog  from '../sound_member_thali_dialog.vue';
import avator from '@/assets/images/realVoice/real_voice_detail_avator.png'
import { bookmarkToggle } from '@/api/soundStore.js'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router';
import { useSoundStore } from '@/stores/modules/soundStore.js'
import { useloginStore } from '@/stores/login'
const { proxy } = getCurrentInstance();
let loginStore = useloginStore() 
let soundStore = useSoundStore()
let route = useRoute();
let list=ref([])
let detail_dialog_ref=ref(null)
let sound_thali_dialog_ref=ref(null)
let sound_member_thali_dialog_ref=ref(null)
let detail_package_dialog_ref=ref(null)
let list_loading=ref(true)
let cancelToken=ref(null)
let tts=ref('')
let all_data=ref([])
//音频控制按钮
let play_sound = (index, data) => {  
  if (data.package) {
    go_sound_store_detail(data);
    return;
  }

  if (!('isPlayable' in data) || !data.isPlayable) {
    ElMessage.warning(`尚未加载完，请稍后再试`);
    return;
  }

  // 先暂停其他正在播放的音频，并重置它们的播放进度
  list.value.forEach((item, i) => {
    if (i !== index && item.isPlaying) {
      item.isPlaying = false;
      if (item.audioElement) {
        item.audioElement.pause();
        item.audioElement.currentTime = 0; // 重置进度
      }
    }
  });

  let item = list.value[index];

  // 切换当前音频的播放状态
  item.isPlaying = !item.isPlaying;

  // 创建音频元素（如果不存在）
  if (!item.audioElement) {
    try {
      let audioUrl = item.url || 'http://example.com/default-audio.mp3';
      item.audioElement = new Audio(audioUrl);
      item.audioElement.volume = item.volume / 100;

      // 播放结束事件，重置播放状态和进度
      item.audioElement.addEventListener('ended', () => {
        item.isPlaying = false;
        item.audioElement.currentTime = 0;
      });

      // 加载错误事件
      item.audioElement.addEventListener('error', (e) => {
        item.isPlaying = false;
        // ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`);
      });

      item.audioElement.load();
    } catch (err) {
      ElMessage.error(`暂时无法播放`);
      item.isPlaying = false;
      return;
    }
  }

  if (item.isPlaying) {
    // 播放音频，从暂停位置继续
    try {
      let playPromise = item.audioElement.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
        //   ElMessage.success(`正在播放: ${item.name}`);
        }).catch(err => {
          ElMessage.error(`播放失败`);
          item.isPlaying = false;
        });
      }
    } catch (err) {
      ElMessage.error(`暂时无法播放`);
      item.isPlaying = false;
    }
  } else {
    // 暂停音频，保留当前播放进度，不重置 currentTime
    if (item.audioElement) {
      item.audioElement.pause();
      // 不重置 currentTime，保持暂停位置
    }
    item.lastRotation = (item.lastRotation) % 360;
    // ElMessage.info(`已暂停: ${item.name}`);
  }
};
// let play_sound = (index, data) => {  
//     if (data.package) {
//         go_sound_store_detail(data);
//         return;
//     }
//     // console.log(data.isPlayable,'isPlayable');
    
//     if (!('isPlayable' in data) || !data.isPlayable) {
//         ElMessage.warning(`${data.name ? data.name : data.voiceName} 尚未加载完，请稍后再试`);
//         return;
//     }

//     // 先暂停任何正在播放的音频
//     list.value.forEach((item, i) => {
//         if (i !== index && item.isPlaying) {
//             item.isPlaying = false;
//             // 停止其他正在播放的音频
//             if (item.audioElement) {
//                 item.audioElement.pause();
//             }
//         }
//     });

//     let item = list.value[index];

//     // 切换当前音频的播放状态
//     item.isPlaying = !item.isPlaying;

//     // 实际播放/暂停音频
//     if (!item.audioElement) {
//         // 如果还没有创建音频元素，则创建一个
//         try {
//             let audioUrl = item.url || 'http://example.com/default-audio.mp3'; // 提供一个默认URL用于测试
//             item.audioElement = new Audio(audioUrl);
//             item.audioElement.volume = item.volume / 100;

//             // 监听播放结束事件，自动重置状态
//             item.audioElement.addEventListener('ended', () => {
//                 item.isPlaying = false;
//                 item.audioElement.currentTime = 0; // 重置播放时间
//             });

//             // 监听音频加载错误
//             item.audioElement.addEventListener('error', (e) => {
//                 // console.error('音频加载错误:', e);
//                 item.isPlaying = false;
//                 // ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`);
//             });

//             // 预加载音频
//             item.audioElement.load();
//         } catch (err) {
//             // console.error('创建音频元素失败:', err);
//             ElMessage.error(`无法播放 "${item.name}"`);
//             item.isPlaying = false;
//             return;
//         }
//     }

//     if (item.isPlaying) {
//         // 尝试播放音频
//         try {
//             let playPromise = item.audioElement.play();
//             // startRotation(index);
//             if (playPromise !== undefined) {
//                 playPromise.then(() => {
//                     // 播放成功
//                     ElMessage.success(`正在播放: ${item.name}`);
//                 }).catch(err => {
//                     // console.error('播放失败:', err);
//                     ElMessage.error(`播放失败: ${item.name}`);
//                     item.isPlaying = false;
//                 });
//             }
//         } catch (err) {
//             // console.error('播放音频时发生错误:', err);
//             ElMessage.error(`无法播放 "${item.name}"`);
//             item.isPlaying = false;
//         }
//     } else {
//         // 暂停音频
//         if (item.audioElement) {
//             item.audioElement.pause();
//             item.audioElement.currentTime = 0; // 重置播放时间
//         }
//         item.lastRotation = (item.lastRotation) % 360;
//         ElMessage.info(`已暂停: ${item.name}`);
//     }
// };
// let play_sound = (index, data) => {  
//     if (data.package) {
//         go_sound_store_detail(data);
//         return;
//     }
//     console.log(data.isPlayable, 'isPlayable');
    
//     if (!('isPlayable' in data) || !data.isPlayable) {
//         ElMessage.warning(`${data.name ? data.name : data.voiceName} 尚未加载完，请稍后再试`);
//         return;
//     }

//     // 先暂停任何正在播放的音频
//     list.value.forEach((item, i) => {
//         if (i !== index && item.isPlaying) {
//             item.isPlaying = false;
//             // 停止其他正在播放的音频
//             if (item.audioElement) {
//                 item.audioElement.pause();
//             }
//         }
//     });

//     let item = list.value[index];

//     // 切换当前音频的播放状态
//     item.isPlaying = !item.isPlaying;

//     // 如果音频元素不存在，则创建一个
//     if (!item.audioElement) {
//         try {
//             let audioUrl = item.url || 'http://example.com/default-audio.mp3'; // 提供一个默认URL用于测试
//             item.audioElement = new Audio(audioUrl);
//             item.audioElement.volume = item.volume / 100;

//             // 监听播放结束事件，自动重置状态
//             item.audioElement.addEventListener('ended', () => {
//                 item.isPlaying = false;
//                 item.audioElement.currentTime = 0; // 重置播放时间
//             });

//             // 监听音频加载错误
//             item.audioElement.addEventListener('error', (e) => {
//                 console.error('音频加载错误:', e);
//                 item.isPlaying = false;
//                 ElMessage.error(`音频 "${item.name}" 加载失败，请检查URL是否有效`);
//             });

//             // 监听音频可以播放事件
//             item.audioElement.addEventListener('canplay', () => {
//                 if (item.isPlaying) {
//                     item.audioElement.play().then(() => {
//                         ElMessage.success(`正在播放: ${item.name}`);
//                     }).catch(err => {
//                         console.error('播放失败:', err);
//                         ElMessage.error(`播放失败: ${item.name}`);
//                         item.isPlaying = false;
//                     });
//                 }
//             });

//             // 预加载音频
//             item.audioElement.load();
//         } catch (err) {
//             console.error('创建音频元素失败:', err);
//             ElMessage.error(`无法播放 "${item.name}"`);
//             item.isPlaying = false;
//             return;
//         }
//     }

//     // 如果当前音频正在播放，则暂停
//     if (!item.isPlaying) {
//         if (item.audioElement) {
//             item.audioElement.pause();
//             item.audioElement.currentTime = 0; // 重置播放时间
//         }
//         item.lastRotation = (item.lastRotation) % 360;
//         ElMessage.info(`已暂停: ${item.name}`);
//     }
// };

//设置头像旋转角度
let imgStyle = (index) => {
    // return {
    //     transform: `rotate(${list.value[index].lastRotation}deg)`,
    //     transition: `${list.value[index].isPlaying}` ? 'none' : 'transform 1.5s ease-in-out', // 暂停时添加过渡效果
    //     transformOrigin: 'center center'
    // };
}
let startRotation=(index)=>{
    // 这里可以使用定时器来模拟旋转
    let rotateInterval = setInterval(() => {
        if (list.value[index].isPlaying) {
            list.value[index].lastRotation = (list.value[index].lastRotation + 2) % 360; // 每次增加1度
        } else {
            clearInterval(rotateInterval); // 停止旋转
        }
    }, 100); // 每100毫秒更新一次
}
let imageSrc =(sign)=>{
    return sign == 'SVIP' ? SVIPImage : VIPImage;
} 
let collectSrc =(collect)=>{
    return collect==1 ? collectImg  : unCollectImg ;
} 
let toggle_collect=async(item,index)=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let status='',tip='',result=0
    if(list.value[index].bookmark==0){
        result=1
        status='success'
        tip='收藏成功'
    }else{
        result=0
        status='error'
        tip='取消收藏'
    }
    try {
        let data=await bookmarkToggle({voiceId:item.id,type:result,tts:tts.value,userId:loginStore.userId})
        if(data.code==1){
            ElMessage({ message:data.msg , type: 'error' });
            return
        }
        list.value[index].bookmark=result
        ElMessage[status](tip);
    }catch (error) {
        // console.log(error,'error');
        ElMessage({ message:'数据加载失败，请刷新页面重试' , type: 'error' });
        
    }
   
}
let go_sound_store_detail=(item)=>{
    console.log(item,'item');
    
    close_aduio()


    if(item.package){
        detail_package_dialog_ref.value.package_info=item
        detail_package_dialog_ref.value.package_info.list=item.data
        
        // 延迟打开弹窗，确保DOM更新
        setTimeout(() => {
            detail_package_dialog_ref.value.dialogDetailVisible=true
        }, 50)
    }else{
        // 先暂停任何正在播放的音频
        list.value.forEach((item, i) => {
            if (item.isPlaying) {
                item.isPlaying = false
                // 停止其他正在播放的音频
                if (item.audioElement) {
                    item.audioElement.pause()
                }
            }
        })
        let item_data=JSON.parse(JSON.stringify(item))
        delete item_data.audioElement
        detail_dialog_ref.value.userInfo.user={}
        detail_dialog_ref.value.userInfo.user=item_data
        
        // 延迟打开弹窗，确保DOM更新
        setTimeout(() => {
            detail_dialog_ref.value.dialogDetailVisible=true
            detail_dialog_ref.value.init_audio(item_data)
        }, 50)
    }
}
let formatDuration=(seconds)=>{
    let totalSeconds = Math.floor(seconds); // 取整
    let hours = Math.floor(totalSeconds / 3600); // 计算小时
    let minutes = Math.floor((totalSeconds % 3600) / 60); // 计算分钟
    let remainingSeconds = totalSeconds % 60; // 计算剩余秒数

    // 格式化为两位数
    let formattedHours = String(hours).padStart(2, '0');
    let formattedMinutes = String(minutes).padStart(2, '0');
    let formattedSeconds = String(remainingSeconds).padStart(2, '0');

    // 根据小时数决定返回格式
    if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`; // hh:mm:ss
    } else {
        return `${formattedMinutes}:${formattedSeconds}`; // mm:ss
    }
}
let currentPromise = null; // 用于跟踪当前的 Promise

// let init = (data) => {
//     // console.log('切换1');
  
//     // 如果当前有 Promise 正在进行，取消它
//     if (currentPromise) {
//         currentPromise.cancel(); // 取消之前的 Promise
//     }
    
//     list.value = data;
 
// // console.log(list.value);

//    if (route.query.buy && Object.keys(soundStore.buy_sound).length>0 ) {
//         if(soundStore.buy_sound.type=='single'){
//             let index=list.value.findIndex((item)=>item.voiceName==soundStore.buy_sound.voice_id)
//             go_sound_store_detail(list.value[index])
//             detail_dialog_ref.value.buy_sound()
//         }else{
//             let index=list.value.findIndex((item)=>item.packageType=="进阶版")
//             go_sound_store_detail(list.value[index])
//             detail_package_dialog_ref.value.buy_sound()
//         }
//     }
//     list_loading.value = false;

//     // 创建一个数组来存储所有的 Promise
//     const promises = data.map((item) => {
//         return new Promise((resolve, reject) => {
//             if (item.audioUrl) {
//                 let audio = new Audio(item.audioUrl);
//                 audio.addEventListener('loadedmetadata', () => {
//                     if (currentPromise.isCanceled) return; // 检查是否被取消
//                     item.duration = formatDuration(audio.duration);
//                     item.name = item.voiceName; // 获取文件名
//                     item.url = item.audioUrl;
//                     item.isHovered = false;
//                     item.isPlaying = false;
//                     item.lastRotation = 0;
//                     item.volume = 100;
//                     item.aduio_finished = true;
//                     resolve();
//                 });
//                 audio.addEventListener('canplay', () => {
//                     // console.log(`音频 "${item.name}" 可以正常播放`);
//                     item.isPlayable = true; // 设置一个标志，表示音频可以播放
//                 });
//                 audio.addEventListener('error', () => {
//                     if (currentPromise.isCanceled) return; // 检查是否被取消
//                     resolve(); // 解析 Promise，避免阻塞
//                 });
//                 audio.load();
//             } else {
//                 resolve(); // 如果没有 audioUrl，直接解析 Promise
//             }
//         });
//     });
//     // console.log(promises,'promises');
//     // 将当前 Promise 存储到 currentPromise 中
//     currentPromise = Promise.all(promises).then(() => {
//         if (currentPromise.isCanceled) return; // 检查是否被取消
//         // console.log('all');
        
//         // list.value = data; // 更新 list.value
//         // console.log(list.value, 222);
//     }).catch((error) => {
//         // console.error('处理音频元数据时出错:', error);
//     });

//     // 实现 cancel 方法
//     currentPromise.cancel = function() {
//         this.isCanceled = true; // 设置取消标志
//     };
// };
let waitForData = () => {
  return new Promise((resolve) => {
    let stop
    stop = watch(all_data, (newVal) => {
      if (newVal && newVal.length > 0) {
        // stop()  // 停止监听
        resolve(newVal)
      }
    }, { immediate: true })
  })
}
let go_package = async (data) => {
     const dataList = await waitForData()
    let index = dataList.findIndex((item) => item.platformNickname == data);
    
      go_sound_store_detail(dataList[index]);
      detail_package_dialog_ref.value.buy_sound();
}
let globalAudio = new Audio();
// 定义函数，传入情绪，返回对应链接
let getAudioUrl=(data,emotion)=>{
  if (!emotion || typeof emotion !== 'string') {
    console.warn('传入的情绪无效:', emotion);
    return null;
  }
  const trimmedEmotion = emotion.trim();
  return data[trimmedEmotion] || null;
}
let init = async(data) => {
    console.log(data, 'data');
    
    if(data.length == 0||!data) {
        list.value=[]
        list_loading.value = false
        return
    };
  if (currentPromise) {
    currentPromise.cancel();
  }

  
  if(tts.value==4){
    data.map((item)=>{
        let audioMap = JSON.parse(item.audioUrl);
		item.audioUrl=getAudioUrl(audioMap,'neutral')
    })
  }
  console.log(tts.value,data,'tts');
  list.value = data;


  if (route.query.buy && Object.keys(soundStore.buy_sound).length > 0) {
    if (soundStore.buy_sound.type == 'single') {
      let index = list.value.findIndex((item) => item.voiceName == soundStore.buy_sound.voice_id);
      console.log(soundStore.buy_sound.voice_id,index, 'index');
      go_sound_store_detail(list.value[index]);
      await nextTick()
      console.log(detail_dialog_ref.value, 'detail_dialog_ref.value');
      
       detail_dialog_ref.value&&detail_dialog_ref.value.buy_sound();
    } else {
      go_package('进阶版套餐包')
    }
    soundStore.setbuySound({})
  }
  list_loading.value = false;



  let thisPromise;

  thisPromise = (async () => {
    for (const item of data) {
      if (thisPromise && thisPromise.isCanceled) break;

      if (item.audioUrl) {
        await new Promise((resolve) => {
          globalAudio.src = item.audioUrl;

          function onLoadedMetadata() {
            if (thisPromise && thisPromise.isCanceled) {
              cleanup();
              resolve();
              return;
            }
            item.duration = formatDuration(globalAudio.duration);
            item.name = item.voiceName;
            item.url = item.audioUrl;
            item.isHovered = false;
            item.isPlaying = false;
            item.lastRotation = 0;
            item.volume = 100;
            item.aduio_finished = true;
            // 不在这里 resolve，等待 canplay 事件
            console.log(item, item.isPlayable, 'loadedmetadata');
          }

          function onCanPlay() {
            item.isPlayable = true;
            console.log(item, item.isPlayable, 'canplay');
            cleanup();
            resolve(); // 在这里 resolve，确保 isPlayable 已赋值
          }

          function onError() {
            item.isPlayable = false; // 标记不可播放
            cleanup();
            resolve();
          }

          function cleanup() {
            globalAudio.removeEventListener('loadedmetadata', onLoadedMetadata);
            globalAudio.removeEventListener('canplay', onCanPlay);
            globalAudio.removeEventListener('error', onError);
          }

          globalAudio.addEventListener('loadedmetadata', onLoadedMetadata);
          globalAudio.addEventListener('canplay', onCanPlay);
          globalAudio.addEventListener('error', onError);

          globalAudio.load();
        });
      } else {
        continue;
      }
    }
  })();

  currentPromise = thisPromise;

  currentPromise.cancel = function () {
    this.isCanceled = true;
    globalAudio.pause();
    globalAudio.src = '';
  };
};



// let init=(data)=>{
//     list.value=data
//     if(route.query.package&&soundStore.open_sound_pack!=''){
//         openPackage()
//     }
//     list_loading.value=false
//     // let url = 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/11/%E5%BF%92%E7%AC%A8%20%282%29%20%282%29.mp3';
    
//     // 确保 list.value 是一个数组
//     // if (!list.value) {
//     //     list.value = [];
//     // }
//    // 创建一个数组来存储所有的 Promise
//    const promises = data.map((item) => {
//         return new Promise((resolve) => {
//             if(item.audioUrl){
//                 let audio = new Audio(item.audioUrl);
//                 audio.addEventListener('loadedmetadata', () => {
//                     item.duration = formatDuration(audio.duration);
//                     item.name = item.voiceName; // 获取文件名
//                     item.url = item.audioUrl;
//                     item.isHovered = false;
//                     item.isPlaying = false;
//                     item.lastRotation = 0;
//                     item.volume = 100;
//                     item.aduio_finished=true
//                     // 解析 Promise，表示该音频的元数据已加载
//                     resolve();
//                 });
//                 // 处理音频加载错误的情况
//                 audio.addEventListener('error', () => {
//                     // console.error(`Failed to load audio: ${item.audioUrl}`);
//                     resolve(); // 解析 Promise，避免阻塞
//                 });
//                 // 开始加载音频
//                 audio.load();
//             }
//         });
//     });

//     // 等待所有的 Promise 完成
//     Promise.all(promises).then(() => {
//         list.value=data
//         console.log(list.value,222);
//     });
//     // sound_list_ref.value.list=
// }
//从ai商配返回音色商店打开的弹窗
let openPackage=()=>{
   
   
    let index=list.value.findIndex((item)=>item.platformNickname==soundStore.open_sound_pack)
    console.log(list.value,index,'openPackage');

    go_sound_store_detail(list.value[index])
    soundStore.open_sound_pack=''
}
let close_aduio=()=>{
    if (currentPromise) {
        currentPromise.cancel();
    }
    if (globalAudio) {
        globalAudio.pause();
        globalAudio.src = '';
    }
    if(list.value.length>0){
        // 停止所有正在播放的音频并释放资源
        list.value.forEach(item => {
            if (item.audioElement) {
                item.audioElement.pause();
                item.audioElement.src = '';
                item.audioElement = null;
            }
        });
    }
    
}
let openActivePackage=()=>{
    if (route.query.package && soundStore.open_sound_pack != '') {
        openPackage();
        //ai工具跳转购买
    }
}
onMounted(()=>{
    openActivePackage()
})
onDeactivated(()=>{
    close_aduio()
})
defineExpose({
    list,
    init,
    list_loading,
    cancelToken,
    close_aduio,
    tts,
    all_data,
    go_sound_store_detail
})
onUnmounted(() => {

	close_aduio()
});
</script>
<style lang="scss" scoped>
.sound_list{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;
    .sound_list_item{
        width: 167px;
        margin-right: 27px;
        display: flex;
        flex-direction: column;
        margin-bottom: 41px;
        cursor: pointer;
        position: relative;
        .sound_list_item_imgs{
            width: 100%;
            height: 171px;
            background: linear-gradient(0deg, #D0DAE0 0%, #E2F0EF 100%);
            border-radius: 8px;
            padding: 11px;
            box-sizing: border-box;
            margin-bottom: 21px;
            position: relative;
            overflow: hidden;
            .sound_list_item_imgs_mask{
                width: 100%;
                height: 100%;
                background-color:rgba(0, 0, 0, 0.3);
                position: absolute;
                left: 0;
                top: 0;
                display: none;
            }
            .sound_list_item_no_imgs{
                display: flex;
                flex-wrap: wrap;
                img{
                    width: 100%;
                    height: 100%;
                }
                .sound_list_item_no_img{
                    width: 43px;
                    height: 43px;
                    background: #FFFFFF;
                    border-radius: 2px;
                    margin-right: 8px;
                    margin-bottom: 7px;
                    &:nth-child(3n){
                        margin-right: 0;
                    }
                    &:nth-last-child(-n+3){
                        margin-bottom: 0;
                    }
                    &:nth-child(9){
                        position: relative;
                        overflow: hidden;
                        &::after{
                            content: '';
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            top: 0;
                            left: 0;
                            background-color: rgba(0, 0, 0, 0.5);
                            z-index: 1;
                        }
                        span{
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            z-index: 2;
                            color: #fff;
                            font-size: 12px;
                        }
                    }
                }
               
            }
            .sound_list_languages{
                position: absolute;
                top: 7px;
                left: 0;
                width: 100%;
                padding: 0;
                padding-left: 9px;
                padding-right: 7px;
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #7C7C7C;
            }
            .sound_list_imgs{
                position: absolute;
                top: 16px;
                left: 16px;
                width: 139px;
                height: 139px;
                background: #FFFFFF;
                border-radius: 50%;
                img{
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                }
            }
            .sound_list_collect{
                position: absolute;
                right: 8px;
                bottom: 9px;
                width: 17px;
                height: 16px;
                cursor: pointer;
            }
            &.special{
                background: linear-gradient(326.38deg, #EFD7B6 27.01%, #F2E8D4 77.45%), linear-gradient(180deg, #F5E5C0 0%, #EDDCBF 28.85%, #EDCF80 100%);
            }
        }
        .sound_list_item_detail{
            display: flex;
            flex-direction: column;
            .sound_list_item_detail_title{
                display: flex;
                margin-bottom: 12px;
                .sound_list_item_detail_title_label{
                    max-width: 7em;
                    font-size: 18px;
                    line-height: 18px;
                    color: #271E17;
                    margin-right: 8px;
                }
                img{
                    width: 31px;
                    height: 17px;
                    margin-right: 8px;
                    &.detail_package_item_ultimate_perfection{
                        width: 40px;
                        height: 17px;
                    }  
                }
               
            }
            .sound_list_item_detail_style_role{
                font-size: 14px;
                line-height: 14px;
                color: #7C7C7C;
                margin-bottom: 12px;
            }
            .sound_list_item_detail_area{
                font-size: 14px;
                color: #271E17;
                line-height: 21px;
                display: -webkit-box;               
                -webkit-box-orient: vertical;       
                -webkit-line-clamp: 2;             
                overflow: hidden;                   
                text-overflow: ellipsis;   
                height:42px;        
                max-height:42px ;                   
                word-break: break-all;  
                margin-bottom: 18px;          
            }
            .sound_list_item_detail_sign{
                height: 24px;
                background: #E9F0FE;
                border-radius: 4px;
                display: flex;
                justify-content: center;
                font-size: 14px;
                color: #271E17;
                padding: 4px 7px 5px  6px ;
                width: fit-content;
                align-items: center;
                img{
                    height: 14px;
                    width: 18px;
                    margin-right: 3px;
                }
            }
           
        }
        
        &.package{
            &:hover{
                .sound_list_item_imgs_mask{
                    display: block;
                }
                .sound_list_item_imgs{
                    &::before{
                            content: '';
                            position: absolute;
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            background-color: rgba(0, 0, 0, 0.3);
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            z-index: 3;
                        }
                        &::after{
                            content: '';
                            position: absolute;
                            width: 22px;
                            height: 14px;
                            background-image: url('@/assets/images/soundStore/sound_list_view.png');
                            background-size: cover; 
                            background-position: 0 0;
                            background-repeat: no-repeat;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            z-index: 3;
                    }
                    
                }
            }
            
        }
        &.single{
            &:hover,&.play{
                .sound_list_item_imgs_mask{
                    display: block;
                    border-radius: 50%;
                }
                .sound_list_item_imgs{
                    &::before{
                            content: '';
                            position: absolute;
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            background-color: rgba(0, 0, 0, 0.3);
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            z-index: 3;
                        }
                        &::after{
                            content: '';
                            position: absolute;
                            
                           
                            background-size: cover; 
                            background-position: 0 0;
                            background-repeat: no-repeat;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            z-index: 3;
                        }
                        &.play{
                            &::after{
                                width: 13px;
                                height: 19px;
                                background-image: url('@/assets/images/soundStore/sound_list_play.png');
                            }
                        }
                        &.pause{
                            &::after{
                                width: 17px;
                                height: 19px;
                                background-image: url('@/assets/images/soundStore/sound_list_pause.png');
                            }
                        }
                        
                    
                }
            }
        }
        &:nth-child(6n){
            margin-right: 0;
        }
    }
    .sound_list_no_data{
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 76px 0 95px 0;
        span{
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px
        }
    }
}
.el-loading-mask{
    background-color: transparent;
}
</style>