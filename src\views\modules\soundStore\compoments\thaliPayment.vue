<template>
    <div class="thali_payment">
                    <div class="thali_payment_title">
                        <div class="thali_payment_title_price" :class="prop.currentCom=='sound_thali'?'needWeight':''">
                            原价<span>¥{{current.price}}</span>
                            <!-- <i>-</i>官方优惠<span class="official">¥1000</span><i>-</i> -->
                        </div>
                        <!-- <button class="thali_payment_title_coupon">
                            <img src="@/assets/images/soundStore/thali_payment_title_coupon.png" class="thali_payment_title_coupon_img" alt="">
                            <span>暂无可用优惠券</span>
                            <img src="@/assets/images/soundStore/thali_payment_title_coupon_more.png" class="thali_payment_title_coupon_more" alt="">
                        </button> -->
                    </div>
                    <div class="thali_payment_content">
                        <div class="thali_payment_content_payMethod">
                            <div class="thali_payment_content_payMethod_item" :class="current.paymethod=='weixin'?'current':''"  @click="choose_paymethod('weixin')">
                                <img src="@/assets/images/soundStore/thali_type_weixin.png" alt="" width="20px" style="margin-right: 9px;">
                                微信支付
                            </div>
                            <div class="thali_payment_content_payMethod_item" :class="current.paymethod=='alipay'?'current':''" @click="choose_paymethod('alipay')">
                                <img src="@/assets/images/soundStore/thali_type_alipay.png" alt="" width="16px" style="margin-right: 6px;">
                                支付宝支付
                            </div>
                        </div>
                        <div class="thali_payment_content_qrcode">
                           <QRCode :value="current.qrCode" :size="130" />
                        </div>
                       
                        <div class="thali_payment_content_explain">
                            <div class="thali_payment_content_explain_title" :class="prop.currentCom=='sound_thali'?'sound_thali':''">
                                <img src="@/assets/images/soundStore/thali_type_weixin_pay.png" alt=""><template v-if="current.paymethod=='weixin'">微信</template><template v-else>支付宝</template>扫码支付，支付
                                <template v-if="prop.currentCom=='sound_thali'">
                                    <span>{{current.price}}元</span>
                                </template>
                                <template v-else>
                                    <span>{{current.price}}</span>元
                                </template>
                               
                            </div>
                            <div class="thali_payment_content_explain_texts" v-if="prop.currentCom=='sound_thali'">
                                1.【账号绑定】仅限平台会员使用，不可单独开通；<br/>
                                2.【有效期】自购买日起生效，到期自动终止；<br/>
                                3.【字数限制】含定额合成字符，实时扣减，余额不足时暂停服务；<br/>
                                4.【叠加规则】重复购买则有效期与字数累计叠加（有效期从末次购买计算）；<br/>
                                5.【协议确认】支付即视为同意<span @click="go_user_agreement">《用户协议》</span>和<span  @click="go_privacy_agreement">《隐私协议》</span>；<br/>
                                6.【退款政策】即时生效数字商品，依据法规不支持无理由退款。
                            </div>
                            <div class="thali_payment_content_explain_texts" v-else>
                                1.【字数配额】年享240万字符基础合成额度，额度用尽后服务自动暂停；<br/>
                                2.【叠加规则】重复购买则有效期与字数累计叠加（有效期从末次购买计算）；<br/>
                                3.【服务范围】本套餐不含至臻音色及真人配音服务，如需专业配音需另行购买独立服务包；<br/>
                                4.【协议确认】支付即视为同意<span @click="go_user_agreement">《用户协议》</span>和<span  @click="go_privacy_agreement">《隐私协议》</span>；<br/>
                                5.【退款政策】即时生效数字商品，依据法规不支持无理由退款。
                            </div>
                        </div>
                    </div>
                </div>
</template>
<script setup>
import { ref, defineExpose, reactive,defineProps } from 'vue';
import { useRouter } from 'vue-router'
import QRCode from 'qrcode.vue';
let router = useRouter()
let prop=defineProps({
    currentCom: {
        type: String,
        default: ''
    },
})

let current=ref({
    paymethod:'weixin',
    qrCode:'',
    price:''
})

let go_user_agreement=()=>{
    router.push({ path: '/agreement', query: { type: 'user' } })
}
let go_privacy_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'privacy' } })
}
let choose_paymethod=(paymethod)=>{
    current.value.paymethod=paymethod
}
defineExpose({
    current,
})
</script>
<style lang="scss" scoped>
.thali_payment{
    width: 100%;
    height: 262px;
    border-radius: 11px;
    border: 1px solid #E0E7F4;
    .thali_payment_title{
        padding: 19px 0 16px 25px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-bottom: 1px dashed rgba(224, 231, 244, 1);
        height: 66px;
        .thali_payment_title_price{
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #20252A;
            line-height: 15px;
            span{
                margin-left: 9px;
                font-size: 20px;
                color: #20252A;
                margin-right: 8px;
                &.official{
                    margin-right: 0;
                }
            }
            i{
                margin-right: 8px;
                font-size: 16px;
                color: #20252A;
                &:last-child{
                    margin-left: 8px;
                    margin-right: 9px;
                }
            }
            &.needWeight{
                span{
                    font-weight: bold;
                }
            }
        }
        .thali_payment_title_coupon{
            display: flex;
            align-items: center;
            padding: 5px 12px 6px 11px;
            border-radius: 4px;
            border: 1px solid #C7D1E2;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            background-color: transparent;
            .thali_payment_title_coupon_img{
                width: 19px;
                height: 14px;
                margin-right: 6px;
            }
            span{
                margin-right: 7px;
                font-size: 16px;
                color: #6B7D90;
                display: inline-block;
                line-height: 17px;
            }
            .thali_payment_title_coupon_more{
                width: 7px;
                height: 12px;
            }
        }
    }
    .thali_payment_content{
        display: flex;
        .thali_payment_content_payMethod{
            display: flex;
            flex-direction: column;
            width: 154px;
            margin-right: 24px;
            .thali_payment_content_payMethod_item{
                border: none;
                background-color: transparent;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: #20252A;
                height: 93px;
                cursor: pointer;
                img{
                    height: 16px;

                }
                &:first-child{
                    border-radius: 0px 0px 11px 0px;
                }
                &:nth-child(2){
                    border-radius: 0px 11px 0px 11px;
                }
                &.current{
                    height: 103px;
                    background: #F5F8FD;
                   
                }
            }
        }
        .thali_payment_content_qrcode{
            margin-top: 37px;
            width: 130px;
            height: 130px;
            margin-right: 24px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .thali_payment_content_explain{
            margin-top: 25px;
            flex: 1;
            .thali_payment_content_explain_title{
                display: flex;
                font-size: 16px;
                color: #20252A;
                line-height: 15px;
                align-items: center;
                margin-bottom: 12px;
                img{
                    width: 18px;
                    height: 16px;
                    margin-right: 8px;
                }
                span{
                    font-weight: bold;
                    font-size: 24px;
                    color: #FF2F2F;
                    line-height: 23px;
                    margin-left: 9px;
                    margin-right: 9px;
                }
                &.sound_thali{
                    margin-right: 0;
                }
            }
            .thali_payment_content_explain_texts{
                word-break: break-all;
                font-size: 12px;
                color: rgba(32, 37, 42, 0.87);
                line-height: 20px;
                letter-spacing: -0.01em;
            
                span{
                    color: #18AD25;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>