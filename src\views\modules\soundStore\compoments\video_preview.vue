<template>
    <el-dialog v-model="dialogVisible" :title="currentPreviewTitle" width="800px" :before-close="handleClose">
        <div class="video-preview-container">
            <video 
                v-if="videoUrl" 
                ref="videoPlayer"
                class="video-player" 
                controls
                :src="videoUrl"
            >
                您的浏览器不支持 HTML5 视频。
            </video>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    videoUrl: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:visible'])

const dialogVisible = ref(props.visible)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})

// 监听对话框状态变化
watch(dialogVisible, (newVal) => {
    emit('update:visible', newVal)
})

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.video-preview-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .video-player {
        width: 100%;
        max-height: 60vh;
        object-fit: contain;
    }
}

:deep(.el-dialog) {
    border-radius: 8px;
    
    .el-dialog__header {
        margin: 0;
        padding: 20px;
        border-bottom: 1px solid #e4e7ed;
    }
    
    .el-dialog__body {
        padding: 20px;
    }
}
</style> 