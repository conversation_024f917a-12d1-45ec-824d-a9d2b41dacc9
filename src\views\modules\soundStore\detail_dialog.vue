<template>
    <el-dialog v-model="dialogDetailVisible" class="go_sound_store_detail" width="888px" :show-close="false" append-to-body :style="{
  transformOrigin: 'top center',
  transform: `scale(${rate})`
}">
        <template #header>
            <div class="go_sound_store_detail_header" >
                <div class="go_sound_store_detail_header_imgs" @click.stop="play_sound(userInfo.user)" :class="{ play: userInfo.user.isPlaying,pause: !userInfo.user.isPlaying}" >
                    <img :src="userInfo.user.avatarUrl" alt="">
                    <div class="sound_detail_item_imgs_mask"></div>
                </div>
                <div class="go_sound_store_detail_header_detail">
                    <div class="go_sound_store_detail_header_detail_name_box">
                        <div class="go_sound_store_detail_header_detail_name">
                            {{ userInfo.user.platformNickname }}
                        </div>
                       
                            <img :src="get_detail_sign(userInfo.user.membershipGrade)"   class="go_sound_store_detail_header_detail_sign" alt="">
                     
                       
                            <!-- <img src="@/assets/images/soundStore/detail_collect.png" @click="toggle_collect" v-if="userInfo.user.bookmark==1" class="go_sound_store_detail_header_detail_collect" alt="">
                            <img src="@/assets/images/soundStore/detail_uncollect.png" width="14px" height="13px" @click="toggle_collect"  v-else  class="go_sound_store_detail_header_detail_collect" alt=""> -->
                       
                    </div>
                    <div class="go_sound_store_detail_header_tags">
                        <div class="go_sound_store_detail_header_tag" v-for="(item,index) in userInfo.user.sceneCategory.split('、')" :key="index">
                            {{ item }}
                        </div>
                    </div>
                    <div class="go_sound_store_detail_header_describe">
                        <template v-if="tts==4">
                            <span v-for="(item,index) in userInfo.user.filterArr" :key="index" class="go_sound_store_detail_header_describe_item" >
                                <img :src="item.url" alt="">
                                <span>{{item.label}}</span> 
                            </span>
                        </template>
                         <span v-for="(item,index) in userInfo.user.emotionTags.split('、')" :key="index" v-else>
                            {{item}}&nbsp;<template v-if="userInfo.user.emotionTags.split('、').length>1&&index!=userInfo.user.emotionTags.split('、').length-1">|&nbsp;</template> 
                        </span>
                        
                    </div>
                </div>
                <div class="go_sound_store_detail_close">

                    <img src="@/assets/images/soundStore/go_sound_store_detail_close.svg" @click="close" alt="">
                </div>
            </div>
            <div class="go_sound_store_detail_describe" v-if="userInfo.user.demoText">
                {{ userInfo.user.demoText }}
            </div>
           <div class="go_sound_store_detail_no_have" v-if="loginStore.token&&userInfo.user.isBuy==0">
                <template v-if="tts==4">
                    <template v-if="loginStore.token&&loginStore.userInfo&&loginStore.memberInfo.level.level==0">
                        *你还未拥有此声音，此声音属于{{ userInfo.user.membershipGrade }}声音，请开通会员  。
                    </template>
                    <template v-else>
                        *你还未拥有此声音，此声音属于{{userInfo.user.membershipGrade}}声音，请成为 <span class="exclusive">「{{userInfo.user.membershipGrade}}全场声音会员」</span> 或成为会员后点击购买按钮单独购买此声音。
                    </template>
                </template>
                <template v-else>
                    您还未拥有此声音，此声音属于商配套餐包<template v-if="userInfo.user.price>=500">进阶版</template><template v-else>基础版</template>声音，请<template v-if="loginStore.memberInfo.level.level!=0">点击下方购买套餐包按钮</template><template v-else>成为会员后</template>，购买<template v-if="userInfo.user.price>=500">进阶版</template><template v-else>基础版</template>套餐包。
                </template>
            </div>
          
            <div class="go_sound_store_detail_btns">
                <button  @click="become_member" v-if="loginStore.token&&tts==4&&loginStore.memberInfo.level.level==0">
                     成为会员
                </button>
                <button @click="use_sound" v-if="loginStore.token&&userInfo.user.isBuy==1">用TA配音</button>
                <button @click="buy_sound" v-if="loginStore.token&&(tts==4||tts==5)&&userInfo.user.isBuy==0&&loginStore.memberInfo.level.level!=0" >
                    <template v-if="tts==4">
                        购买声音
                    </template>
                    <template v-else>
                        购买套餐包
                    </template>
                </button>
                <button @click="upgrade_member" v-if="loginStore.token&&(loginStore.memberInfo.level.level==1&&userInfo.user.membershipGrade=='SVIP')">升级会员</button>
                <!-- 普通只有 <button>成为会员</button> -->
                 <!-- 同级只有用TA配音 -->
            </div>
        </template>
        <template #default>
            <!-- <div class="premium_sample_sound">
                <div class="premium_sample_sound_title"><img src="@/assets/images/soundStore/premium_sample_sound.png" alt="">精品样音</div>
                <div class="premium_sample_sound_list">
                    <div class="premium_sample_sound_item"  @click="preview_video">
                        <div class="premium_sample_sound_item_imgs">
                            <img src="" alt="">
                        </div>
                        <div class="premium_sample_sound_item_detail">
                            <span class="premium_sample_sound_item_title">作品名称</span>
                            <div class="premium_sample_sound_item_views">
                                <img src="@/assets/images/soundStore/detail_view.png" alt="">
                                1.5w
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div> -->
        </template>
    </el-dialog>
    <VideoPreview
        v-model:visible="previewDialogVisible"
        :video-url="currentPreviewUrl"
        :title="currentPreviewTitle"
    />
    <soundThaliDialog ref="sound_thali_dialog_ref"></soundThaliDialog>
    <soundMemberThaliDialog ref="sound_member_thali_dialog_ref"></soundMemberThaliDialog>
    <contact ref="contact_ref"></contact>
</template>

<script setup>
import { ref, defineExpose, reactive,inject,onMounted,watch,getCurrentInstance,defineEmits } from 'vue';
import { useRouter } from 'vue-router'
import VideoPreview from "./compoments/video_preview.vue"
import soundThaliDialog from "./sound_thali_dialog.vue"
// import soundMemberThaliDialog from "./sound_member_thali_dialog.vue"
import contact from "@/views/modules/realVoice/components/index/contact.vue"
import vipImg from '@/assets/images/account/avatar_sign.png';
import sVIPImage from '@/assets/images/soundStore/SVIP.png';
import expireImage from '@/assets/images/account/expire.svg'
import { bookmarkToggle } from '@/api/soundStore.js'
import { ElMessage } from 'element-plus'
import { useSoundStore } from '@/stores/modules/soundStore.js' 
import { getAll } from '@/api/soundStore.js'
import { useloginStore } from '@/stores/login'
import insert_emotion from '@/assets/images/aiImages/insert_emotion_1.svg'
import insert_emotion1 from '@/assets/images/aiImages/insert_emotion_2.svg'
import insert_emotion2 from '@/assets/images/aiImages/insert_emotion_3.svg'
import insert_emotion3 from '@/assets/images/aiImages/insert_emotion_4.svg'
import insert_emotion4 from '@/assets/images/aiImages/insert_emotion_5.svg'
import insert_emotion5 from '@/assets/images/aiImages/insert_emotion_6.svg'
import insert_emotion6 from '@/assets/images/aiImages/insert_emotion_7.svg'
const { proxy } = getCurrentInstance();
let loginStore = useloginStore() 
let soundStore = useSoundStore()
let router = useRouter()
let emotionList=ref([
{
  id:0,
   label:'通用',
  value:"neutral",
  url:insert_emotion6
},
{
  id:1,
  label:'高兴',
  value:'happy',
  url:insert_emotion
},{
  id:2,
  label:'悲伤',
  value:'sad',
  url:insert_emotion1
},{
  id:3,
  label:'害怕',
  value:'fearful',
  url:insert_emotion2
},{
  id:4,
  label:'愤怒',
  value:'angry',
  url:insert_emotion3
},{
  id:5,
  label:'厌恶',
  value:'disgusted',
  url:insert_emotion4
},{
  id:6,
  label:'惊讶',
  value:"surprised",
  url:insert_emotion5
}])
let tts=inject('tts')
let emit=defineEmits(['go_package'])
let rate=ref(window.innerWidth/1920)
let userInfo=reactive({
    user:{
        platformNickname:'奇喵紫',
        membershipGrade:'svip',
        bookmark:true,
        tags:['情感','书单','商务','影视','资讯'],
        describes:['成熟知性','亲切温和','年轻时尚'],
        detail_describe:'今天我要向大家推荐几本值得一读的好书。首先是《活着》这本书，它讲述了一个普通农民的生命历程，通过对生命的反思和思考，让我们更加珍惜生命，关注人性的美好。'
    }
})
let membershipGradeImg=reactive({
    SVIP:sVIPImage,
    VIP:vipImg,
    expire:expireImage
})
let previewDialogVisible=ref(false)
let currentPreviewUrl=ref('')
let currentPreviewTitle=ref('')
let dialogDetailVisible = ref(false);
let preview_video=()=>{
    currentPreviewTitle.value='视频名称'
    currentPreviewUrl.value='http://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/11/123.mp4?Expires=1743059765&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=k31e351SVuXEqbAZf3LQwX%2F6k84%3D'
    previewDialogVisible.value=true
}
let contact_ref=ref(null)
let close_aduio=()=>{
    if(userInfo.user.audioElement){
        userInfo.user.isPlaying = false
        userInfo.user.audioElement.pause();
        userInfo.user.audioElement.currentTime = 0;
    }
}
let close=()=>{
    close_aduio()
    dialogDetailVisible.value=false
}
let use_sound=()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        close_aduio()
        return
    }
   
    soundStore.setChooseData(userInfo.user)
    if(userInfo.user.voiceType=="至臻"||userInfo.user.voiceType=="SFT"){
        router.push({ path: '/commercialDubbing',query:{choose:true} })
    }else{
        router.push({ path: '/AIDubbing' ,query:{choose:true}})
    }
    close_aduio()
    dialogDetailVisible.value=false
}
let sound_thali_dialog_ref=ref(null)
let buy_sound=async()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    if(tts.value==4){
         let thail_data=await getAll({membershipGrade:'SVIP',tts:tts.value, userId:loginStore.userId,voiceId:userInfo.user.id })
        console.log(thail_data,'thail_data');
        
        let data=sound_thali_dialog_ref.value.thali_list
        data[0].price=thail_data[0].year
        data[1].price=thail_data[0].halfYear
        data[2].price=thail_data[0].month
        sound_thali_dialog_ref.value.info=thail_data
        sound_thali_dialog_ref.value.dialogVisible=true
    }else{
        become_member()
    }
   
}
let sound_member_thali_dialog_ref=ref(null)
let upgrade_member=()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        close_aduio()
        return
    }
    // router.push('/membership')
    let url = `${window.location.origin}/membership`;
	window.open(url, '_blank');
    close_aduio()
}
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
let get_detail_sign=(membershipGrade)=>{
    let sign=membershipGrade
    // if(expire.value){
    //     sign='expire'
    // }
  return   membershipGradeImg[sign]
}
let toggle_collect=async()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let status='',tip='',result=0
    if(userInfo.user.bookmark==0){
        result=1
        status='success'
        tip='收藏成功'
    }else{
        result=0
        status='error'
        tip='取消收藏'
    }
   
    try {
        let data=await bookmarkToggle({voiceId:userInfo.user.id,type:result,tts:tts.value,userId:loginStore.userId})
        if(data.code==1){
            ElMessage({ message:data.msg , type: 'error' });
            return
        }
        userInfo.user.bookmark=result
        ElMessage[status](tip);
    }catch (error) {
        console.log(error,'error');
        ElMessage({ message:'数据加载失败，请刷新页面重试' , type: 'error' });
        
    }
}
let formatDuration=(seconds)=>{
    let totalSeconds = Math.floor(seconds); // 取整
    let hours = Math.floor(totalSeconds / 3600); // 计算小时
    let minutes = Math.floor((totalSeconds % 3600) / 60); // 计算分钟
    let remainingSeconds = totalSeconds % 60; // 计算剩余秒数

    // 格式化为两位数
    let formattedHours = String(hours).padStart(2, '0');
    let formattedMinutes = String(minutes).padStart(2, '0');
    let formattedSeconds = String(remainingSeconds).padStart(2, '0');

    // 根据小时数决定返回格式
    if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`; // hh:mm:ss
    } else {
        return `${formattedMinutes}:${formattedSeconds}`; // mm:ss
    }
}
let init_audio = (item) => {
    if(item.emotion){
    //   filteredEmotionList(data.emotion)
      item.filterArr=filteredEmotionList(item.emotion)
    }
    // let emotionSettingStr ='[\"happy\", \"sad!\", \"angy\", \"fearful\", \"disgusted\", \"surprised\", \"neutral\"]'

    if (item.audioUrl) {
        // 检查是否已经存在 audioElement

        if (!item.audioElement) {
            let audioUrl=item.audioUrl
            //  if(tts.value==4){
            //     audioUrl=item.audioUrl['neutral']
            //  }
            item.audioElement = new Audio(audioUrl);
            console.log(item,item.volume,666);
            if( !item.volume){
                 item.volume=100
            }
            item.audioElement.volume = item.volume / 100; // 设置音量

            item.audioElement.addEventListener('loadedmetadata', () => {
                item.duration = formatDuration(item.audioElement.duration);
                item.name = item.voiceName; // 获取文件名
                item.url = audioUrl;
                item.isHovered = false;
                item.isPlaying = false;
                item.lastRotation = 0;
                item.aduio_finished = true;
            });

            item.audioElement.addEventListener('error', () => {
                console.error('音频加载错误');
                item.isPlaying = false; // 更新状态
            });

            item.audioElement.load(); // 加载音频
        } else {
            // 如果 audioElement 已经存在，确保它的 URL 是最新的
            if (item.audioElement.src !== audioUrl) {
                item.audioElement.src = audioUrl;
                item.audioElement.load(); // 重新加载音频
            }
        }
    }
};

let become_member=()=>{
    console.log(444);
    
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        close_aduio()
        return
    }
    if(tts.value==4){
        let url = `${window.location.origin}/membership`;
        window.open(url, '_blank');
    }else{
        let data=""
        if(userInfo.user.price>=500){
           data='进阶版套餐包'
        }else{
           data='基础版套餐包'
        }
        emit('go_package',data)
    }
    close_aduio()
}
//音频控制按钮
let play_sound=(data)=>{  
    if (!('aduio_finished' in data) || !data.aduio_finished) {
    ElMessage.warning(`${data.name ? data.name : data.voiceName} 尚未加载完，请稍后再试`);
    return;
    }
// 切换播放状态
data.isPlaying = !data.isPlaying;

// 实际播放/暂停音频
if (!data.audioElement) {
    // 如果还没有创建音频元素，则创建一个
    try {
        let audioUrl = data.url || 'http://example.com/default-audio.mp3'; // 提供一个默认URL用于测试
        data.audioElement = new Audio(audioUrl);
        console.log(data.audioElement, 999);
        
        data.audioElement.volume = data.volume / 100;

        // 监听播放结束事件，自动重置状态
        data.audioElement.addEventListener('ended', () => {
            data.isPlaying = false;
        });

        // 监听音频加载错误
        data.audioElement.addEventListener('error', (e) => {
            console.error('音频加载错误:', e);
            data.isPlaying = false;
            ElMessage.error(`音频 "${data.name}" 加载失败，请检查URL是否有效`);
        });
    } catch (err) {
        console.error('创建音频元素失败:', err);
        ElMessage.error(`无法播放 "${data.name}"`);
        data.isPlaying = false;
        return;
    }
}
        // 播放或暂停音频
    if (data.isPlaying) {
        // 尝试播放音频
        try {
            let playPromise = data.audioElement.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    // 播放成功
                    ElMessage.success(`正在播放: ${data.name}`);
                }).catch(err => {
                    console.error('播放失败:', err);
                    ElMessage.error(`播放失败: ${data.name}`);
                    data.isPlaying = false; // 播放失败，更新状态
                });
            }
        } catch (err) {
            console.error('播放音频时发生错误:', err);
            ElMessage.error(`无法播放 "${data.name}"`);
            data.isPlaying = false; // 播放失败，更新状态
        }
    } else {
        // 暂停音频
        data.audioElement.pause();
        data.lastRotation = (data.lastRotation) % 360; // 更新旋转状态
        ElMessage.info(`已暂停: ${data.name}`);
    }



}
let expire=ref(false)
watch(dialogDetailVisible, (newValue, oldValue) => {
   if(!newValue){
    if(userInfo.user.audioElement){
        userInfo.user.isPlaying = false
        userInfo.user.audioElement.pause();
        userInfo.user.audioElement.currentTime = 0;
        expire.value=isExpired(loginStore.userInfo?.expireTime ||'')
    }
   
   }
});
let  cleanEmotion=(str)=>{
  // 去除非字母字符，转小写
  return str.replace(/[^a-zA-Z]/g, '').toLowerCase()
}
// 解析字符串
let parseEmotionSetting=(str)=>{
  const matches = str.match(/"([^"]*)"/g)
  if (!matches) return []
  return matches.map(s => cleanEmotion(s.slice(1, -1)))
}
// 表情过滤
let filteredEmotionList = (emotionSettingStr) => {
  const parsedEmotions = parseEmotionSetting(emotionSettingStr)
console.log(parsedEmotions,'parsedEmotions');

  // 先找默认项
  const defaultItem = emotionList.value.find(item => item.value === '')

  // 过滤其他项
  const filteredOthers = emotionList.value.filter(item => {
    if (item.value === '') return false // 默认项先排除，避免重复
    const cleanVal = cleanEmotion(item.value)
    return parsedEmotions.includes(cleanVal)
  })

  // 返回默认项 + 过滤项
  return defaultItem ? [defaultItem, ...filteredOthers] : filteredOthers
}
defineExpose({
    dialogDetailVisible,
    userInfo,
    init_audio,
    buy_sound
});
</script>

<style lang="scss">
.go_sound_store_detail {
    padding:52px 32px 51px;
    box-sizing: border-box;
    background: linear-gradient(180deg, #DFFFDF 0%, #FFFFFF 30.57%);
    border-radius: 4px;

    .el-dialog__header{
        padding-bottom: 0;
    }
    .go_sound_store_detail_header{
        display: flex;
        margin-bottom: 44px;
        
        .go_sound_store_detail_header_imgs{
            width: 110px;
            height: 110px;
            background-color: #18AD25;
            border-radius: 50%;
            margin-right: 27px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            img{
                width: 100%;
                height: 100%;
            }
            .sound_detail_item_imgs_mask{
                width: 100%;
                height: 100%;
                background-color:rgba(0, 0, 0, 0.3);
                position: absolute;
                left: 0;
                top: 0;
                display: none;
            }
            &::before{
                content: '';
                position: absolute;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.3);
                left: 50%;
                top: 50%;
                transform: translate(-50%,-50%);
                z-index: 3;
                display: none;
            }
            &::after{
                content: '';
                position: absolute;
                
                
                background-size: cover; 
                background-position: 0 0;
                background-repeat: no-repeat;
                left: 50%;
                top: 50%;
                transform: translate(-50%,-50%);
                z-index: 3;
            }
            &:hover{
                .sound_detail_item_imgs_mask{
                    display: block;
                    border-radius: 50%;
                }
                &::before{
                    display: block;
                }
                &.play{
                    &::after{
                        width: 13px;
                        height: 19px;
                        background-image: url('@/assets/images/soundStore/sound_list_play.png');
                    }
                }
                &.pause{
                    &::after{
                        width: 17px;
                        height: 19px;
                        background-image: url('@/assets/images/soundStore/sound_list_pause.png');
                    }
                }
            }
            &.play{
                &::before{
                    display: block;
                }
                &::after{
                        width: 13px;
                        height: 19px;
                        background-image: url('@/assets/images/soundStore/sound_list_play.png');
                    }
                    .sound_detail_item_imgs_mask{
                        display: block;
                    }
            }
        }
        .go_sound_store_detail_header_detail{
            .go_sound_store_detail_header_detail_name_box{
                margin-top: 10px;
                position: relative;
                margin-bottom: 9px;
                display: flex;
                align-items: center;
                height: 26px;
                .go_sound_store_detail_header_detail_name{
                    font-size: 18px;
                    line-height: 26px;
                    height: 21px;
                    color: #001126;
                    margin-right: 3px;
               
                    line-height: 20px;   
                }
                .go_sound_store_detail_header_detail_sign{
                    width: 18px;
                    height: 13px;
                    margin-right: 10px;
                }
                .go_sound_store_detail_header_detail_collect{
                    width: 16px;
                    height: 14px;
                    cursor: pointer;
                }
            }
        }
        .go_sound_store_detail_header_tags{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            .go_sound_store_detail_header_tag{
                position: relative;
                font-size: 14px;
                line-height: 22px;
                height: 22px;
                color: #5D708F;
                padding:0 6px;
                &::after{
                    content:'';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1px;
                    height: 14px;
                    background-color: #5D708F;
                    z-index: 1;
                }
                &:first-child{
                    padding-left: 0;
                }
                &:last-child{
                    padding-right: 0;
                    &::after{
                        background-color: transparent;
                    }
                }
            }
        }
        .go_sound_store_detail_header_describe{
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #7c7c7c;
            .go_sound_store_detail_header_describe_item{
                padding: 0 10px;
                border: 1px solid #EFEFF1;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                height: 28px;
                border-radius: 100px;
                margin-right: 9px;

                img{
                    width: 21px;
                    margin-right: 6px;
                }
                span{
                    font-size: 14px;
                    line-height: 18px;
                    color: #000000;
                }
            }
        }
        .go_sound_store_detail_close{
            position: absolute;
            top: 16px;
            right: 32px;
            z-index: 1;
            cursor: pointer;
            line-height: 0;
            img{
                width: 20px;
                height: 20px;
            }
        }
    }
    .go_sound_store_detail_describe{
        font-size: 14px;
        color: #202328;
        // text-indent: 2em;
        line-height: 24px;
        margin-bottom: 84px;
    }
    .go_sound_store_detail_no_have{
        font-size: 12px;
        color: rgba(0,0,0,0.45);
        line-height: 22px;
        margin-bottom: 6px;
        .exclusive{
            color: #0AAF60;
        }
    }
    .go_sound_store_detail_btns{
        display: flex;
        align-items: center;
        // margin-bottom: 35px;
        button{
            width: 160px;
            height: 42px;
            border-radius: 4px;
            background-color: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            border: 1px solid #18AD25;
            color:#18AD25 ;
            margin-right: 11px;
            cursor: pointer;
            &:first-child{
                background-color: #18AD25;
                color: #fff;
            }
        }
    }
    .premium_sample_sound{
        display: flex;
        flex-direction: column;
        .premium_sample_sound_title{
            display: flex;
            align-items: center;
            font-size: 16px;
            line-height: 17px;
            color: #271E17;
            margin-bottom: 17px;
            img{
                width: 16px;
                height: 15px;
                margin-right: 7px;
            }
            
        }
        .premium_sample_sound_list{
            display: flex;
            .premium_sample_sound_item{
                width: 199px;
                margin-right: 12px;
                cursor: pointer;
                .premium_sample_sound_item_imgs{
                    width: 100%;
                    height: 113px;
                    background: #C8CAC8;
                    border-radius: 8px;
                    margin-bottom: 7px;
                    overflow: hidden;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .premium_sample_sound_item_detail{
                    display: flex;
                    align-items: center;
                    .premium_sample_sound_item_title{
                        font-size: 12px;
                        line-height: 12px;
                        color: #7C7C7C;
                    }
                    .premium_sample_sound_item_views{
                        margin-left: auto;
                        display: flex;
                        align-items: center;
                        font-size: 12px;
                        color: #7C7C7C;
                        line-height:12px;
                        img{
                            width: 11px;
                            height: 9px;
                            margin-right: 4px;
                        }
                    }
                }
                
                &:last-child{
                    margin-right: 0;
                }
            }
        }
       
    }
}
</style>
