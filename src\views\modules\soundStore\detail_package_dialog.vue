<template>
    <el-dialog v-model="dialogDetailVisible"   ref="dialogRef" class="detail_package_dialog" width="888px" append-to-body :style="{transformOrigin: 'top center',transform: `scale(${rate})`,margin:`${marginValue} auto` }">
        <template #header>
           <img src="@/assets/images/soundStore/detail_package_close.png" class="detail_package_close" @click="close" alt="">
           <div class="detail_package_header">
                <div class="detail_package_header_avator">
                    <div class="detail_package_header_avator_imgs">
                        <div class="detail_package_header_avator_item" v-for="(item,index) in (package_info.list.slice(0,9))" :key="index">
                            <img :src="item.avatarUrl" alt="">
                            <span v-if="index==8">+{{package_info.list.length-9}}</span>
                        </div>
                    </div>
                    
                </div>
                <div class="detail_package_header_info">
                    <div class="detail_package_header_info_text">
                        <span class="detail_package_header_info_name">{{package_info.platformNickname}}</span>
                        <!-- <button class="detail_package_header_info_thali_btn" v-if="package_info.isThali">基础套餐包</button> -->
                        <img src="@/assets/images/soundStore/detail_package_header_info_ai.svg" v-if="package_info.isAi" alt="">
                    </div>
                    <template v-if="!soundStore.showPackage">
                    <div class="detail_package_header_info_tip">
                        使用额度：{{package_info.volume}}
                    </div>
                    <div class="detail_package_header_info_price">
                        <div class="detail_package_header_info_price_label">
                            总价：
                        </div>
                        <div class="detail_package_header_info_price_value">
                            <i>{{parseFloat(((package_info.discountPrice&&package_info.discountPrice>0)?package_info.discountPrice:package_info.totalPrice))}}</i>
                            <span class="line" v-if="package_info.discountPrice&&package_info.discountPrice>0">
                                {{parseFloat(package_info.totalPrice)}}
                            </span>
                        </div>
                    </div>
                    </template>
                </div>
           </div>
        </template>
        <template #default>
            <el-scrollbar ref="scrollbar" style="height: 446px;" always>
            <div class="detail_package_list">
                <div class="detail_package_item" v-for="(item,index) in package_info.list" :key="index">
                    <div class="detail_package_item_top" :class="item.price>=500?'special':''">
                        <div class="detail_package_item_top_text">
                            <div class="detail_package_item_top_language">
                                {{ item.languageSkills }}
                            </div>
                            <!-- <div class="detail_package_item_top_collect">
                                <img :src="getCollectImg(item.bookmark)" @click="chage_collect(index,item)" alt="">
                            </div> -->
                        </div>
                        <div class="detail_package_item_top_image">
                           <img :src="item.avatarUrl" alt="">
                        </div>
                    </div>
                    <div class="detail_package_item_content">
                        <div class="detail_package_item_content_top">
                            <!-- <span>{{ item.voiceName }}</span> -->
                            <span v-if="item.platformNickname&&item.platformNickname!=''">{{ item.platformNickname }}</span>
                            <img  src="@/assets/images/soundStore/detail_package_item_ai.svg" alt="">
                            <img class="detail_package_item_ultimate_perfection"  src="@/assets/images/soundStore/detail_package_item_ultimate_perfection.svg" alt="">
                        </div>
                       
                        <span class="detail_package_item_already_owned" v-if="item.owned">
                            <img src="@/assets/images/soundStore/detail_package_item_already_owned.svg" alt="">
                            已拥有
                        </span>
                    </div>
                </div>
            </div>
            </el-scrollbar>
            <!-- <div class="detail_package_tip">
                你还未拥有<span>「至臻音色包」</span>的声音包，请点击下方购买按钮购买此声音包
            </div> -->
            <div class="detail_package_btns">
                <button @click="use_sound"  v-if="!soundStore.showPackage">用TA配音</button>
                <button @click="buy_sound" v-loading="buy_loading">购买声音</button>
            </div>
        </template>
    </el-dialog>
    <packageBuyDialog ref="package_buy_dialog_ref"></packageBuyDialog>
</template>

<script setup>
import { ref, defineExpose, reactive,getCurrentInstance,watch,nextTick } from 'vue';
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSoundStore } from '@/stores/modules/soundStore.js' 
import detailPackageUncollect from "@/assets/images/soundStore/detail_package_uncollect.svg"
import detailPackageCollect from "@/assets/images/soundStore/detail_package_collect.svg"
import avatarUrl from "@/assets/images/soundStore/detail_package_item_avator.png"
import packageBuyDialog from "./package_buy_dialog.vue"
import { productQuery,packageByCode,bookmarkToggle} from '@/api/soundStore.js'
import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
import { useloginStore } from '@/stores/login'
const { proxy } = getCurrentInstance();
let loginStore = useloginStore() 
let soundStore = useSoundStore()
let router = useRouter()
let dialogDetailVisible=ref(false)
let package_info=ref({
    name:' 男团声音包',
    isThali:true,
    isAi:true,
    limit:1000,
    character:2000,
    time:50,
    price:598,
    list:[
    
    ]
})
let marginValue=ref(0)
let rate=ref(window.innerWidth/1920)
let buy_loading=ref(false)
let getCollectImg=(bookmark)=>{
    if(bookmark==1){
        return detailPackageCollect
    }else{
        return detailPackageUncollect
    }
}
let chage_collect=async(index,item)=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let status='',tip='',result=0
    if(package_info.value.list[index].bookmark==0){
        result=1
        status='success'
        tip='收藏成功'
    }else{
        result=0
        status='error'
        tip='取消收藏'
    }
    
    try {
        let data=await bookmarkToggle({voiceId:item.id,type:result,tts:5,userId:loginStore.userId})
        if(data.code==1){
            ElMessage({ message:data.msg , type: 'error' });
            return
        }
        package_info.value.list[index].bookmark=result
        ElMessage[status](tip);
    }catch (error) {
        console.log(error,'error');
        ElMessage({ message:'数据加载失败，请刷新页面重试' , type: 'error' });
        
    }
}
let use_sound=()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    console.log(package_info.value.data,'package_info.value.data');
    
    soundStore.setPackChooseData(package_info.value.data)
    router.push({ path: '/commercialDubbing',query:{package:true} })
    dialogDetailVisible.value=false
}
let package_buy_dialog_ref=ref(null)
let buy_sound=async()=>{
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    try {
        buy_loading.value=true
        
        let data=await productQuery({ type:package_info.value.type})
       
        package_buy_dialog_ref.value.pack_info=data[0]
        // qrcode
        // let data1=await packageByCode({ paymentType:'PURCHASE',planId:data[0].id,quantity:1})
        // package_buy_dialog_ref.value.pack_info.qrcode=data1.resp_data.counter_url
        // package_buy_dialog_ref.value.pack_info.totalPrice=get_price(data1.resp_data.total_amount,100)
        // package_buy_dialog_ref.value.order_params=data1
        package_buy_dialog_ref.value.dialogDetailVisible=true
        console.log(package_info.value,'package_info.value');
        data[0].priceList.map((item)=>{
            if(data[0].type!='进阶版套餐包'){
                item.info=`${ package_info.value.list.length}款至臻音色（专业质）`
            }else{
                item.info=`${ package_info.value.list.length}款至臻音色 (专业级+央视级)`
            }
            
        })
        console.log(data,'data');
        
         package_buy_dialog_ref.value.init_price_list(data[0].type!='进阶版套餐包'?'base':'advanced',data[0].priceList)
        // package_buy_dialog_ref.value.pack_info.qrcode=data1.resp_data.counter_url
        buy_loading.value=false
    } catch (error) {
        console.log(error);
        buy_loading.value=false
        if(error.response){
            
            ElMessage({ message:error.response.data , type: 'error' });
           
        }
    }
   
   
}



let get_price=(a,b)=>{
    return accDiv(a,b)
} 
let close=()=>{
    buy_loading.value=false
    soundStore.setOpenSoundPack('')
    dialogDetailVisible.value=false
}
let dialogRef = ref(null);
let  updateMargin=()=>{
    const dialogEl = document.querySelector('.detail_package_dialog');
  if (!dialogEl) {
    console.warn('通过类名未找到弹窗 DOM');
    return;
  }
  const rect = dialogEl.getBoundingClientRect();
  const dialogHeight = rect.height;
  const screenHeight = window.innerHeight;
  const marginTop = (screenHeight - dialogHeight) / 2;
  const marginValuePx = marginTop > 0 ? marginTop : 0;
  marginValue.value = `${marginValuePx}px auto`;
}
defineExpose({
    dialogDetailVisible,
    package_info,
    buy_sound
});
watch(dialogDetailVisible, async (newVal) => {
  if (newVal) {
    await nextTick();
    updateMargin();
  }else{
     soundStore.setShowPackage(null)
  }
});
</script>

<style lang="scss">
.el-overlay{
    .el-overlay-dialog{
        overflow: hidden;
    }
}

.detail_package_dialog {
    padding:52px 32px 53px;
    box-sizing: border-box;
    background: linear-gradient(180deg, #DFFFDF 0%, #FFFFFF 30.57%);
    border-radius: 4px;
    .el-dialog__header{
        padding-bottom: 0;
        position: relative;
        .el-dialog__headerbtn{
           display: none;
        }
        .detail_package_close{
            position: absolute;
            top: -34px;
            right: 1px;
            width: 14px;
            height: 14px;
            z-index: 1;
            cursor: pointer;
        }
        .detail_package_header{
            display: flex;
            width: 100%;
            box-sizing: border-box;
            .detail_package_header_avator{
                width: 139px;
                height: 139px;
                background: linear-gradient(180deg, #596472 0%, #263446 100%);
                border-radius: 8px;
                padding: 7px 10px 10px 7px;
                // display: flex;
                // flex-wrap: wrap;
                margin-right: 27px;
                .detail_package_header_avator_imgs{
                    display: flex;
                    flex-wrap: wrap;
                .detail_package_header_avator_item{
                    width: 36px;
                    height: 36px;
                    background: #FFFFFF;
                    border-radius: 4px;
                    overflow: hidden;
                    margin-right: 7px;
                    margin-bottom: 7px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                    &:nth-child(3n){
                        margin-right: 0;
                    }
                    &:nth-last-child(-n+3){
                        margin-bottom: 0;
                    }
                    &:nth-child(9){
                        position: relative;
                        overflow: hidden;
                        &::after{
                            content: '';
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            top: 0;
                            left: 0;
                            background-color: rgba(0, 0, 0, 0.5);
                            z-index: 1;
                        }
                        span{
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%,-50%);
                            z-index: 2;
                            color: #fff;
                            font-size: 12px;
                        }
                    }
                }
            }
            }
            .detail_package_header_info{
                display: flex;
                flex-direction: column;
                .detail_package_header_info_text{
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    .detail_package_header_info_name{
                        font-size: 18px;
                        line-height: 26px;
                        color: #001126;
                        margin-right: 15px;
                    }
                    .detail_package_header_info_thali_btn{
                        margin-right: 3px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 82px;
                        height: 22px;
                        background: #E9F0FE;
                        border-radius: 4px;
                        font-size: 12px;
                        color: #3563C0;
                        border: none;
                    }
                    img{
                        width: 37px;
                        height: 20px;
                    }
                }
                .detail_package_header_info_tip{
                    font-size: 14px;
                    line-height: 22px;
                    color: #5D708F;
                    margin-bottom: 49px;

                }
                .detail_package_header_info_price{
                    display: flex;
                    align-items: baseline;
                    font-size: 14px;
                    color: #001126;
                    .detail_package_header_info_price_value{
                        display: flex;
                        align-items: baseline;
                        i{
                            font-style: italic;
                            font-size: 32px;
                            line-height: 22px;
                            color: #FF3B30;
                            margin-right: 3px;
                        }
                        .line{
                            font-size: 16px;
                            // color: #5D708F;
                            margin-left: 10px;
                            text-decoration: line-through;
                        }
                    }
                }
            }
        }
       
    }
    .el-dialog__body{
        overflow: hidden;
            padding-top: 16px;
            .detail_package_list{
                display: flex;
                flex-wrap: wrap;
                width: 100%;
                padding: 24px 20px;
                background: #F4F4F4;
                padding-bottom: 0;
                .detail_package_item{
                    margin-bottom: 24px;
                    margin-right: 22px;
                    display: flex;
                    flex-direction: column;
                    width: 139px;
                    .detail_package_item_top{
                        width: 139px;
                        height: 139px;
                        background: linear-gradient(180deg, #F0FFF1 0%, #DAE7D4 100%);
                        border-radius: 8px;
                        margin-bottom: 4px;
                        padding: 6px 7px 9px 8px;
                        box-sizing: border-box;
                        display: flex;
                        flex-direction: column;
                        position: relative;
                        .detail_package_item_top_text{
                            display: flex;
                            align-items: center;
                            .detail_package_item_top_language{
                                font-size: 12px;
                                line-height: 22px;
                                color: #888888;
                            }
                            .detail_package_item_top_collect{
                                margin-left: auto;
                                cursor: pointer;
                            }
                        }
                        .detail_package_item_top_image{
                            width: 116px;
                            height: 116px;
                            border-radius: 68px;
                            overflow: hidden;
                            position: absolute;
                            top: 12px;
                            left: 50%;
                            transform:translateX(-50%) ;
                            img{
                                width: 100%;
                                height: 100%;
                            }
                        }
                        &:nth-child(5n){
                            margin-right: 0;
                        }
                        &.special{
                            background: linear-gradient(326.38deg, #EFD7B6 27.01%, #F2E8D4 77.45%), linear-gradient(180deg, #F5E5C0 0%, #EDDCBF 28.85%, #EDCF80 100%);
                        }
                    }
                    .detail_package_item_content{
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                        box-sizing: border-box;
                        .detail_package_item_content_top{
                            display: flex;
                            align-items: center;
                            margin-bottom: 6px;
                            flex-wrap: wrap;
                            span{
                                font-size: 18px;
                                line-height: 26px;
                                color: #000000;
                                margin-right: 8px;
                            }
                            img{
                                width: 30px;
                                height: 15px;
                                margin-right: 4px;
                            }
                            .detail_package_item_ultimate_perfection{
                                width: 37px;
                                height: 15px;
                            }
                        }
                        .detail_package_item_already_owned{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding: 3px 4px;
                            background: #E4EDFF;
                            border-radius: 2px;
                            box-sizing: border-box;
                            width: fit-content;
                            font-size: 9px;
                            color: #353D49;
                            line-height: 12px;
                            img{
                                width: 10px;
                                height: 9px;
                                margin-right: 4px;
                            }

                       }
                    }
                   &:nth-child(5n){
                    margin-right: 0;
                   }
                }
            }
            .detail_package_tip{
                font-size: 14px;
                line-height: 22px;
                display: flex;
                align-items: center;
                color: #000000;
                margin-bottom: 40px;
                margin-top: 16px;
                span{
                    font-size: 14px;
                    color: #0AAF60;
                }
            }
            .detail_package_btns{
                display: flex;
                align-items: center;
                padding-top: 40px;
                button{
                    margin-right: 8px;
                    box-sizing: border-box;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 160px;
                    height: 42px;
                    border: 1px solid #0AAF60;
                    border-radius: 4px;
                    color: #0AAF60;
                    font-size: 16px;
                    background-color: transparent;
                    cursor: pointer;
                    &:first-child{
                        border: none;
                        background-color: #0AAF60;
                        color:#fff;
                    }
                }
            }
        }
}
</style>
