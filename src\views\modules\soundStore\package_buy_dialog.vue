<template>
    <el-dialog v-model="dialogDetailVisible" class="package_buy_dialog" width="888px"  :close-on-click-modal="false"  :style="{transformOrigin: 'top center',transform: `scale(${rate})`,margin:`${marginValue} auto` }"  append-to-body>
        <template #header>
            <payCloseDialog :top="-147" @close="close_dialog" ref="pay_close_dialog_ref"></payCloseDialog>
            <img src="@/assets/images/soundStore/detail_package_close.png" class="package_buy_close" @click="close"
                alt="">
            <div class="package_buy_header">
                
                <div class="package_buy_header_avator">
                    <img :src="loginStore.userInfo.avatar&&loginStore.userInfo.avatar!=''?loginStore.userInfo.avatar:avator" alt="">
                    <div class="package_buy_header_avator_sign">
                        <img :src="get_sign(loginStore.userInfo.benefitLevel)" alt="">
                    </div>
                </div>
                <span class="package_buy_header_name">{{ pack_info.type }}</span>
                <img :src="get_symbol_img(pack_info.type!='进阶版套餐包'?'base':'advanced')" class="package_buy_header_symbol" alt="">
            </div>
        </template>
        <template #default>
            <div class="package_buy_profile" :class="pack_info.type!='进阶版套餐包'?'base':''">
                <!-- <div class="package_buy_profile_symbel">
                    <span class="package_buy_profile_symbel_type">{{ pack_info.type }}</span>
                    <p class="package_buy_profile_symbel_describe">{{ pack_info.volume }}</p>
                </div> -->
               
                <!-- <div class="package_buy_profile_content">
                    <span><template v-if="pack_info.type!='进阶版套餐包'">23款至臻音色（高品质）</template><template v-else>27款至臻音色（高品质+超高品质）</template></span>
                    <div><div class="original"><span>￥</span>{{parseFloat(((pack_info.discountPrice&&pack_info.discountPrice>0)?pack_info.discountPrice:pack_info.totalPrice))}}</div> 
                        <template v-if="pack_info.discountPrice&&pack_info.discountPrice>0">
                        <span class="line">￥{{parseFloat(pack_info.totalPrice)}}</span><img src="@/assets/images/soundStore/package_buy_discount.svg" alt="">
                        </template>
                    </div>   
                </div> -->
                <div class="package_buy_profile_content">
                   
                    <div class="package_buy_profile_content_item" v-for="(item,index) in get_list(pack_info.type!='进阶版套餐包'?'base':'advanced').list" :class="item.id==current_thali?'current':''" :key="index" @click="choose_thali(item)">
                        <img :src="get_img(get_list(pack_info.type !== '进阶版套餐包' ? 'base' : 'advanced')['type'],item.type)" class="package_buy_profile_content_item_symbel" alt="" />
                        <div class="package_buy_profile_content_item_content">
                            <!-- <span><template v-if="pack_info.type!='进阶版套餐包'">23款至臻音色（高品质）</template><template v-else>27款至臻音色（高品质+超高品质）</template></span> -->
                            <span class="package_buy_profile_content_item_content_info">{{item.info}}</span>
                            <span class="package_buy_profile_content_item_content_character">{{item.volume}}</span>
                            <div class="package_buy_profile_content_item_content_price_discount">
                                <span class="package_buy_profile_content_item_content_price"><span class="package_buy_profile_content_item_content_price_symbol">￥</span>{{parseFloat(((item.discountPrice&&item.discountPrice>0)?item.discountPrice:item.price))}}</span>
                                <div class="package_buy_profile_content_item_content_discount" v-if="item.discountPrice&&item.discountPrice<item.price">
                                    <img :src="getDiscountImg(pack_info.type !== '进阶版套餐包' ? 'base' : 'advanced')" alt="">
                                    <span>￥{{parseFloat(item.price)}}</span>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                   
                </div>
                <!-- <img :src="get_profile_img(pack_info.type)" alt=""> -->
            </div>
            <div class="package_buy_content">
                <div class="package_buy_describe">
                    <h5> 购买说明</h5>
                    <p>
                        1.<b>【账号绑定】</b>不限平台会员身份使用，可单独开通；<br />
                        2.<b>【有效期】</b>自购买日起生效，到期自动终止；<br />
                        3.<b>【字数限制】</b>含定额合成字符，实时扣减，余额不足时暂停服务；<br />
                        4.<b>【叠加规则】</b>重复购买则有效期与字数累计叠加（有效期从末次购买计算）；<br />
                        5.<b>【协议确认】</b>支付即视为同意<span @click="go_user_agreement">《用户协议》</span> 和 <span
                        @click="go_privacy_agreement">《隐私协议》</span> ；<br />
                        6.<b>【退款政策】</b>即时生效数字商品，依据法规不支持无理由退款。
                    </p>
                </div>
                <div class="package_buy_paymethod">
                    <ul>
                        <li :class="current_paymethod == 'alipay' ? 'current' : ''" @click="paymethod('alipay')">支付宝支付</li>
                        <li :class="current_paymethod == 'weixin' ? 'current' : ''" @click="paymethod('weixin')">微信支付</li>
                    </ul>
                    <!-- <img :src="pack_info.qrcode" class="package_buy_paymethod_img" alt=""> -->
                    <QRCode :value="pack_info.qrcode" :size="140" />
                    <span class="package_buy_paymethod_tip">
                        <template v-if="current_paymethod == 'weixin'">微信</template><template v-else>支付宝</template>扫码支付<i>¥</i>
                        <span class="package_buy_paymethod_tip_price">
                    
                            {{
                            parseFloat(
                            (current_thali_obj.discountPrice && (current_thali_obj.discountPrice > 0))
                                ? current_thali_obj.discountPrice
                                : current_thali_obj.price
                            )
                        }}</span>                    
                    </span>
                    <button class="package_buy_finish_pay" @click="finish_pay"
                        v-if="pack_info.finish_pay == 'success'">已完成付款</button>
                </div>
            </div>
        </template>
    </el-dialog>
    <payStatusDialog ref="pay_status_dialog_ref" @status="order_status"></payStatusDialog>
</template>
<script setup>
import { ref, defineExpose, reactive,watch,defineEmits,nextTick } from 'vue';
import avator from "@/assets/images/soundStore/package_buy_header_avator.png"
import sign from "@/assets/images/soundStore/package_buy_header_avator_sign.png"
import packageBuyProfileBase from "@/assets/images/soundStore/package_buy_profile_base.png"
import packageBuyProfileAdvanced from "@/assets/images/soundStore/package_buy_profile_advanced.png"
import qrCode from "@/assets/images/account/member_qrcode.png"
import {queryOrder}  from '@/api/soundStore.js'
import QRCode from 'qrcode.vue';
import { useRouter } from 'vue-router'
import payStatusDialog from "@/components/payDialog/pay_status_dialog.vue"
import { useloginStore } from '@/stores/login'
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
import svip from '@/assets/images/account/avatar_svip_sign.svg'
import vip from '@/assets/images/account/avatar_sign.png'
import expireImage from '@/assets/images/account/expire.svg'
import { productQuery,packageByCode} from '@/api/soundStore.js'
import packageAdvancedSymbol from '@/assets/images/soundStore/package_buy_header_advanced_symbol.svg'
import packageBaseSymbol from '@/assets/images/soundStore/package_buy_header_base_symbol.svg'
import packageBuyProfileDiscount from '@/assets/images/soundStore/package_buy_profile_content_item_content_discount.svg' 
import packageBuyProfileDiscountAttain from '@/assets/images/soundStore/package_buy_profile_content_item_content_discount_attain.svg'
import payCloseDialog from "@/components/payDialog/pay_close_dialog.vue"
let loginStore = useloginStore()
const { fetchUserBenefits } = useUserBenefits()
let router = useRouter()
let rate=ref(window.innerWidth/1920)
let dialogDetailVisible = ref(false)
let close=()=>{
    pay_close_dialog_ref.value.pay_close_show=true
}
let close_dialog=()=>{
    dialogDetailVisible.value=false
}
let pay_status_dialog_ref=ref(null)
let order_params=ref({})
let pack_info = ref({
    platformNickname: '至臻音色包',
    avator: avator,
    sign: sign,
    packageType: "base",
    describe: '23款至臻音色（高品质）',
    price: 598,
    finish_pay:'success',
    qrcode:qrCode,
})
let discountImages={
    base:packageBuyProfileDiscount,
    advanced:packageBuyProfileDiscountAttain
}
let getDiscountImg=(type)=>{
    return discountImages[type];
}
let pay_close_dialog_ref=ref(null)
//套餐包数据
let package_buy_profile_list=ref([
    {
        type:'base',
        list:[]
    },{
        type:'advanced',
        list:[]
    }
])
let init_price_list=(type,list)=>{
   let index= package_buy_profile_list.value.findIndex(item=>item.type==type)
   package_buy_profile_list.value[index].list=list
   choose_thali(package_buy_profile_list.value[index].list[0])
//    update_code(list[0])
}
const images = import.meta.glob('@/assets/images/soundStore/*.svg', { eager: true });

let get_img = (type, index) => {
  const fileName = `/src/assets/images/soundStore/package_buy_profile_${type}_${index}.svg`;
   console.log(images,fileName,'images');
//   // 注意路径要和 keys 一致
  return images[fileName].default;
}
let get_list=(type)=>{
    return package_buy_profile_list.value.filter(item=>item.type==type)[0]
}
let marginValue=ref(0)
let  updateMargin=()=>{
    const dialogEl = document.querySelector('.package_buy_dialog');
  if (!dialogEl) {
    console.warn('通过类名未找到弹窗 DOM');
    return;
  }
  const rect = dialogEl.getBoundingClientRect();
  const dialogHeight = rect.height;
  const screenHeight = window.innerHeight;
  const marginTop = (screenHeight - dialogHeight) / 2;
  const marginValuePx = marginTop > 0 ? marginTop : 0;
  marginValue.value = `${marginValuePx}px auto`;
}
let get_profile_img = (type) => {
    if (type == "基础版") {
        return packageBuyProfileBase
    } else {
        return packageBuyProfileAdvanced
    }
}
let current_paymethod=ref('alipay')
let paymethod=(method)=>{
    current_paymethod.value=method
}
let go_user_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'user' } })
}
let go_privacy_agreement=()=>{
  router.push({ path: '/agreement', query: { type: 'privacy' } })
}
let finish_pay=()=>{
    dialogDetailVisible.value=false
}
let order_status = async (status) => {
    console.log(status,'order_status');

    pack_info.value.finish_pay=status

    // 如果支付成功，刷新用户权益信息
    if (status === 'success') {
        console.log('声音包支付成功，开始刷新用户权益信息')
        try {
            await fetchUserBenefits()
            console.log('✅ 声音包支付成功，用户权益信息刷新完成')
        } catch (error) {
            console.error('❌ 声音包支付成功，但权益信息刷新失败:', error)
        }
    }
}
let pollingInterval = ref(null); // 轮询定时器
let checkPaymentStatus = async () => {
    try {
     
        // 调用后端接口查询支付状态
        let data = await queryOrder({outOrderNo:order_params.value.resp_data.out_order_no});
       
        console.log(data.resp_data.order_status, 5555666);
        // 假设返回的状态字段为 status
        // pack_info.finish_pay = data.code; 
        // 
        // 如果支付成功或者失败或者过期，停止轮询
        if (data.resp_data.order_status == 2||data.resp_data.order_status == 3||data.resp_data.order_status == 4) {
            clearInterval(pollingInterval.value);
            if(data.resp_data.order_status == 4){
                update_code()
                console.log("已过期，重新获取验证码");
            } else {
                if(data.resp_data.order_status == 2){
                    pack_info.finish_pay='success'
                    // await notify(order_params.value) 
                }else{
                    pack_info.finish_pay='fail' 
                }
                pay_status_dialog_ref.value.status = pack_info.finish_pay; // 更新支付状态
                pay_status_dialog_ref.value.dialogVisible = true
            }
            return; // 退出函数
        } 
    } catch (error) {
        console.error('查询支付状态失败:', error);
    } 
};
let startPolling = () => {
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value); // 清除已有的定时器
    }
    pollingInterval.value = setInterval(checkPaymentStatus, 3000);
    // pollingStartTime.value = Date.now()
};
let expire=ref(false)
let get_sign=(status)=>{
    console.log(status,'get_sign');
    
    let result=''
    switch (status) {
        case 0:
            result='' 
            break;
        case 1:
            result=vip
            break;
        case 2:
            result=svip
            break;
        default:
            break;
    }
   
    if(expire.value){
        result=expireImage
    }
    return result
}
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
watch(dialogDetailVisible, async(newValue, oldValue) => {
   if(!newValue){
      current_paymethod=ref('alipay')
      clearInterval(pollingInterval.value)
     
   }else{
    await nextTick();
    updateMargin();
    startPolling()
    expire.value=isExpired(loginStore.userInfo?.expireTime ||'')
   }
});  
let get_symbol_img=(type)=>{
    let result=""
    switch (type) {
        case 'base':
            result=packageBaseSymbol
            break;
        case 'advanced':
            result=packageAdvancedSymbol
            break;
        default:
            break;
    }
    return result
}
let get_pay_status=(status)=>{
    switch (status) {
        case 0:
            return '待支付'
            break;
        case 1:
            return '已支付'
            break;
}
}
let current_thali=ref(0)
let current_thali_obj=ref({})
//选择套餐
let choose_thali=(data)=>{
    current_thali.value=data.id
    current_thali_obj.value=data
    update_code()
    startPolling()
}
let update_code=async()=>{
    return new Promise((resolve, reject) => {
    packageByCode({ paymentType: 'PURCHASE', planId: pack_info.value.id,userId:loginStore.userId,priceId:current_thali.value,quantity: 1 })
      .then(data1 => {
        pack_info.value.qrcode = data1.resp_data.counter_url;
        // package_buy_dialog_ref.value.pack_info.totalPrice = get_price(data1.resp_data.total_amount, 100);
        order_params.value = data1;
        resolve(true);
      })
      .catch(err => {
        reject(err);
      });
  });
}
defineExpose({
    dialogDetailVisible,
    pack_info,
    package_buy_profile_list,
    init_price_list
})
</script>
<style lang="scss">
.package_buy_dialog {
    padding: 52px 27px 53px;
    box-sizing: border-box;
    background: linear-gradient(180deg, #DFFFDF 0%, #FFFFFF 30.57%);
    border-radius: 4px;
    position: relative;
    overflow: visible;
    .el-dialog__header {
        padding-bottom: 0;
        position: relative;

        .el-dialog__headerbtn {
            display: none;
        }

        .package_buy_close {
            position: absolute;
            top: -34px;
            right: 1px;
            width: 14px;
            height: 14px;
            z-index: 1;
            cursor: pointer;
        }

        .package_buy_header {
            display: flex;
            align-items: center;
            margin-bottom: 48px;

            .package_buy_header_avator {
                width: 48px;
                height: 48px;
                position: relative;
                margin-right: 16px;

                img {
                    width: 100%;
                    height: 100%;
                }

                .package_buy_header_avator_sign {
                    bottom: 1px;
                    right: -2px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 14px;
                    height: 14px;
                    background: #EEEEEE;
                    border-radius: 7px;
                    position: absolute;

                    img {
                        width: 10px;
                        height: 10px;
                    }
                }
            }

            .package_buy_header_name {
                font-size: 24px;
                line-height: 22px;
                color: rgba(0, 0, 0, 0.85);
                margin-right: 9px;
            }
            .package_buy_header_symbol{
                height: 20px;
            }
        }


    }
    .el-dialog__body{
        overflow: revert
    }
    .package_buy_profile {
        position: relative;
        width: 100%;
        margin-bottom: 27px;
        color: #5D1A64;

        .package_buy_profile_content{
            width: 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .package_buy_profile_content_item{
                width: 269px;
                height: 190px;
                padding-top: 43px;
                background: linear-gradient(192.2deg, #FDF7FF -26.74%, #E6E7FF 79.62%), linear-gradient(192.2deg, #FDF7FF -26.74%, #DAE1FF 79.62%);
                border: 1px solid #FFFFFF;
                box-shadow: 0px 4px 1px rgba(255, 233, 177, 0.1);
                border-radius: 12px;
                margin-right: 13px;
                box-sizing: border-box;
                // transform: matrix(-1, 0, 0, 1, 0, 0);
                position: relative;
                cursor: pointer;
                box-shadow: 0px 4px 1px rgba(255, 233, 177, 0.1);

                .package_buy_profile_content_item_symbel{
                    width: 71px;
                    height: 32px;
                    position: absolute;
                    top: -10px;
                    left: 0;
                    z-index: 1;
                }
                .package_buy_profile_content_item_content{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .package_buy_profile_content_item_content_info{
                        font-size: 16px;
                        line-height: 20px;
                        height: 25px;
                        margin-bottom: 12px;
                    }
                    .package_buy_profile_content_item_content_character{
                        font-family: 'DingTalk JinBuTi';
                        font-style: italic;
                        font-weight: 400;
                        font-size: 20px;
                        line-height: 20px;
                        margin-bottom: 12px;
                    }
                    .package_buy_profile_content_item_content_price_discount{
                        display: flex;
                        align-items: center;
                        .package_buy_profile_content_item_content_price{
                            font-family: 'DingTalk JinBuTi';
                            font-style: italic;
                            font-weight: 400;
                            font-size: 32px;
                            line-height: 60px;
                            height: 60px;
                            font-weight: bold;
                        }
                        .package_buy_profile_content_item_content_discount{
                            margin-left: 6px;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            img{
                                width: 64px;
                                height: 23px;
                                margin-bottom: 3px;
                            }
                            span{
                                font-size: 20px;
                                line-height: 24px;
                                font-style: italic;
                                text-decoration-line: line-through;
                                color: #5D1A64;
                            }
                        }
                    }
                    
                }
                &:last-child{
                    margin-right: 0;
                }
                &.current{
                    border: 2px solid #B7B7FF;
                    .package_buy_profile_content_item_symbel{
                        left: -3px;
                    }
                   
                }
            }
        }
        // .package_buy_profile_content{
        //     width: 100%;
        //     background: linear-gradient(192.2deg, #FDF7FF -26.74%, #E6E7FF 79.62%), linear-gradient(272.27deg, #F7E9D7 2.1%, #FFE9B0 92.25%);
        //     border-radius: 12px;
        //     padding: 44px 0 46px;
        //     box-sizing: border-box;
        //     display: flex;
        //     flex-direction: column;
        //     align-items: center;
        //     span{
        //         font-size: 16px;
        //         line-height: 20px;
        //         color: #5D1A64;
        //         height: 40px;
        //     }
        //     div{
        //         font-style: italic;
                
        //         color: #5D1A64;
        //         height: 60px;
        //         margin: 0;
        //         display: flex;
        //         align-items: center;
        //         .original{
        //             font-size: 50px;
        //             height: 60px;
        //             display: flex;
        //             align-items:center;
        //             span{
        //                 font-size: 32px;
        //                 display: inline-block;
        //                 align-items: flex-end;
        //                 height: 32px;
        //             }
        //         }
        //         span{
        //             height: 60px;
        //             font-size: 32px;
        //             align-self: flex-end;
        //             display: inline-block;
        //             &.line{
        //                 margin-left: 12px;
        //                 text-decoration: line-through;
        //                 line-height: 40px;
        //                 height: 40px;
        //                 align-self:center;
        //             }
        //         }
        //         img{
        //             margin-left: 12px;
        //             width: 92px;
        //             height: 32px;
        //         }
        //     }
        // }
        &.base{
            color: #54300A;
            .package_buy_profile_content{
                .package_buy_profile_content_item{
                    box-shadow: 0px 4px 4px rgba(196, 179, 255, 0.1), 0px 4px 4px rgba(196, 179, 255, 0.1);
                    background: linear-gradient(272.27deg, #F7E9D7 2.1%, #FFE9B0 92.25%);
                    &.current{
                        border-color: #D8B14E;
                    }
                    .package_buy_profile_content_item_content {
                        .package_buy_profile_content_item_content_price_discount{
                            .package_buy_profile_content_item_content_discount{
                                span{
                                    color:#54300A
                                }
                            }
                        }
                    } 
                               
       
                }
            //     background: linear-gradient(90.27deg, #F7E9D7 2.1%, #FFDF8D 92.25%);
            //     span{
            //         color:#54300A;
            //     }
            //     p{
            //         color:#54300A;
            //     }                
            }

        }
    }

    .package_buy_content {
        display: flex;
        padding-top: 43px;
        width: 100%;

        .package_buy_describe {
            display: flex;
            flex-direction: column;
            height: 193px;

            h5 {
                margin: 0;
                font-size: 16px;
                line-height: 22px;
                letter-spacing: -0.02em;
                margin-bottom: 18px;
                color: #353D49;
            }

            p {
                margin: 0;
                font-size: 14px;
                line-height: 32px;
                color: #353D49;
                b{
                    // color: #1890FF;
                }
                span{
                    cursor: pointer;
                }
            }
        }

        .package_buy_paymethod {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 216px;
            margin-left: auto;
            margin-right: 24px;
            height: 280px;

            ul {
                display: flex;
                align-items: center;
                margin-bottom: 11px;

                li {
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 75px;
                    height: 26px;
                    border: 1px solid #D3D3D2;
                    border-radius: 2px;
                    color: #000000;
                    font-size: 12px;
                    margin-right: 4px;
                    position: relative;
                    cursor: pointer;

                    &:last-child {
                        margin-right: 0;
                    }

                    &.current {
                        border: 1px solid #0AAF60;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 0;
                            height: 0;
                            border-left: 17px solid transparent;
                            border-right: 17px solid #0AAF60;
                            border-bottom: 17px solid transparent;

                        }

                        &::after {
                            content: '';
                            position: absolute;
                            top: 0px;
                            right: 0px;
                            width: 17px;
                            height: 17px;
                            background-image: url('@/assets/images/account/buy_describe_paymethod_current.png');
                            background-color: transparent;
                            background-repeat: no-repeat;
                            background-size: 9px 6px;
                            background-position: 8px 2px;
                            z-index: 4;
                        }

                    }
                }
            }

            .package_buy_paymethod_img {
                width: 156px;
                height: 156px;
               
            }

            .package_buy_paymethod_tip {
                display: flex;
                line-height: 22px;
                align-items: baseline;
                font-size: 12px;
                margin-bottom: 18px;
                color: #000000;
                margin-top: 11px;
                i {
                    margin-left: 16px;
                    margin-right: 2px;
                }

                .package_buy_paymethod_tip_price {
                    font-size: 24px;
                    color: #FF3B30;
                    font-style: italic;
                }

            }

            .package_buy_finish_pay {
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                width: 165px;
                height: 32px;
                background: #0AAF60;
                border-radius: 100px;
                border: none;
                font-size: 12px;
                color: #FFFFFF;
                cursor: pointer;
            }
        }
    }
}
</style>