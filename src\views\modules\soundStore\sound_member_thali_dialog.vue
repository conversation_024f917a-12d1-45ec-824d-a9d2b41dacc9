<template>
    <el-dialog v-model="dialogVisible" class="sound_member_thali" width="897px" :style="{transformOrigin: 'top right',transform: `scale(${rate})`}">
        <template #header>
            <div class="sound_member_thali_header">
                <div class="sound_member_thali_header_info">
                    <div class="sound_member_thali_header_info_avatar">
                        <img :src="user.avatar" class="sound_member_thali_header_info_avatar_img" alt="">
                        <img :src="get_detail_sign(user.membershipGrade)" class="sound_member_thali_header_info_avatar_sign" alt="">
                    </div>
                    <div class="sound_member_thali_header_info_text">
                        
                        <span class="sound_member_thali_header_info_text_account">配音帮手账号 {{user.account}} <template v-if="user.expire">你的【会员】已过期</template></span>
                        <span class="sound_member_thali_header_info_text_date">有效期至：{{user.date}}</span>
                    </div>
                </div>
                <img src="@/assets/images/soundStore/sound_member_thali_close.png" @click="close" class="sound_member_thali_close" alt="">
            </div>
        </template>
        <template #default>
            <div class="sound_member_thali_container">
                <div class="sound_member_thali_container_title">
                    全场声音会员套餐<span class="sound_member_thali_container_title_describe">(349款VIP+472款SVIP声音使用权益)</span>
                </div>
                <div class="sound_member_thali_container_list">
                    <div class="sound_member_thali_container_item" v-for="(item,index) in member_list" :class="item.id==current?'current':''" @click="change_member(item.id)" :key="index">
                        <div class="sound_member_thali_container_item_recommend" v-if="item.cost">
                           性价比最高
                        </div>
                        <div class="sound_member_thali_container_item_content">
                            <div class="sound_member_thali_container_item_time">
                                {{ item.time }}
                            </div>
                            <div class="sound_member_thali_container_item_month">
                                约 {{ item.month }}元/月
                            </div>
                            <div class="sound_member_thali_container_item_price">
                                ¥{{ item.price }} <div class="sound_member_thali_container_item_underline">¥{{ item.underline }}</div>
                            
                            </div>
                           
                        </div>
                    </div>
                    
                    
                </div>
                <thaliPayment ref="thali_payment" :currentCom="'sound_member_thali'"></thaliPayment>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, defineExpose, reactive,watch } from 'vue';
import thaliPayment from './compoments/thaliPayment.vue'
import vipImg from "@/assets/images/soundStore/detail_vip.png"
import sVIPImage from '@/assets/images/soundStore/SVIP.png';
import expireImage from '@/assets/images/account/expire.svg'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
let rate=ref(window.innerWidth/1920)
let dialogVisible=ref(false)
let close=()=>{
    dialogVisible.value=false
}
let user=reactive({
    avatar:loginStore.userInfo&&loginStore.userInfo.avatar||'',
    membershipGrade:'',
    account:'***********',
    expire:false,
    date:'2025.01.01 18:25:44'
})
let isExpired=(expireTime)=>{
    const expireISO = expireTime.replace(' ', 'T');
    const expireDate = new Date(expireISO);
    const now = new Date();

    if (isNaN(expireDate.getTime())) {
        console.warn('无效的时间格式:', expireTime);
        return false;
    }

    return now.getTime() > expireDate.getTime();
}
let get_membershipGrade=(sign)=>{
  let result=''
  switch (sign) {
    case 0:
      result=''
      break;
    case 1:
      result='VIP'
      break;
    case 2:
      result='SVIP'
      break;
    default:
      break;
  }
  return result
}
let member_list=reactive([
    {
        id:1,
        time : '12个月(VIP+SVIP声音)',
        month:'83',
        price:'999',
        underline:'1999',
        cost:true
    },
    {
        id:2,
        time : '6个月(VIP+SVIP声音)',
        month:'100',
        price:'599',
        underline:'1499',
        cost:false
    },{
        id:3,
        time : '1个月(VIP+SVIP声音）',
        month:'199',
        price:'199',
        underline:'599',
        cost:false
    }
])
let current=ref(1)
let change_member=(id)=>{
    current.value=id
}
let membershipGradeImg=reactive({
    SVIP:sVIPImage,
    VIP:vipImg,
    expire:expireImage
})
let get_detail_sign=(membershipGrade)=>{
    let sign=membershipGrade
    if(user.expire){
        sign='expire'
    }
    
  return   membershipGradeImg[sign]
}

watch(dialogVisible, (newValue, oldValue) => {
    if(newValue){
        user.membershipGrade=get_membershipGrade(loginStore.userInfo.benefitLevel)
        user.date = loginStore.userInfo.expireTime ||''
        user.expire=isExpired(user.date+' 23:59:59')
    }
}, { deep: true,immediate:true });
defineExpose({
    dialogVisible,
    member_list
});
</script>

<style lang="scss" >
.sound_member_thali {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 0;
    overflow: hidden;
    .el-dialog__header{
        background: linear-gradient(-90deg, rgba(75, 230, 207, 0.4) 0%, rgba(212, 248, 174, 0.4) 100%);
        padding: 0;
        .el-dialog__headerbtn{
            display: none;
        }
        .sound_member_thali_header{
            width: 100%;
            display: flex;
            align-items: center;
            position: relative;
            background-image: url('@/assets/images/soundStore/sound_member_thali_header_bg.png');
            background-size: 100% 296px;
            background-position: 0 0;
            padding: 41px 37px 37px 36px;
            width: 100%;
            box-sizing: border-box;
            .sound_member_thali_header_info{
              display: flex;
              align-items: center;
              .sound_member_thali_header_info_avatar{
                width: 63px;
                height: 63px;
                background: #E5E5E5;
                border-radius: 50%;
                position: relative;
                margin-right: 20px;
                .sound_member_thali_header_info_avatar_img{
                    width: 100%;
                    height: 100%;
                }
                .sound_member_thali_header_info_avatar_sign{
                    position: absolute;
                    top: 45px;
                    left: 44px;
                    width: 25px;
                    height: 20px;
                }

              }
              .sound_member_thali_header_info_text{
                display: flex;
                flex-direction: column;
                .sound_member_thali_header_info_text_account{
                    margin-bottom: 16px;
                    font-weight: bold;
                    font-size: 18px;
                    line-height: 18px;
                    display: inline-block;
                }
                .sound_member_thali_header_info_text_date{
                    font-weight: bold;
                    font-size: 14px;
                    color: #20252A;
                    line-height: 12px;
                    display: inline-block;
                }
              }
            }
            .sound_member_thali_close{
                width: 17px;
                height: 17px;
                position: absolute;
                top: 31px;
                right: 37px;
                cursor: pointer;
            }
        }
    }
    .el-dialog__body{
        padding: 39px 33px 34px; 
        width: 100%;
        box-sizing: border-box;
        .sound_member_thali_container{
            .sound_member_thali_container_title{
                font-weight: bold;
                font-size: 20px;
                color: #20252A;
                line-height: 20px;
                display: flex;
                align-items: baseline;
                margin-bottom: 25px;
                .sound_member_thali_container_title_describe{
                    margin-left: 4px;
                    font-size: 14px;
                    color: rgba(32, 37, 42, 0.65);
                    line-height: 14px;
                    font-weight: normal;
                }
            }
            .sound_member_thali_container_list{
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                margin-bottom: 30px;
                .sound_member_thali_container_item{
                    margin-right: 26px;
                    padding: 31px 0 34px 24px;
                    width: 259px;
                    border-radius: 12px;
                    border: 1px solid #C7D1E2;
                    box-sizing: border-box;
                    cursor: pointer;
                    .sound_member_thali_container_item_recommend{
                        position: absolute;
                        top: -12px;
                        left: 0;
                        z-index: 1;
                        width: 84px;
                        height: 25px;
                        border-radius: 13px 13px 13px 0px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-left: -1px;
                        font-size: 12px;
                        background: #18AD25;
                        color:#fff;
                    }
                    .sound_member_thali_container_item_content{
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                        box-sizing: border-box;
                        
                        .sound_member_thali_container_item_time{
                            font-weight: bold;
                            font-size: 18px;
                            color: #20252A;
                            line-height: 18px;
                            margin-bottom: 9px;
                        }
                        .sound_member_thali_container_item_month{
                            font-size: 14px;
                            color: rgba(32, 37, 42, 0.65);
                            line-height: 13px;
                            margin-bottom: 25px;
                        }
                        .sound_member_thali_container_item_price{
                            display: flex;
                            align-items: baseline;
                            font-weight: bold;
                            font-size: 28px;
                            color: #FF2F2F;
                            line-height: 21px;
                            .sound_member_thali_container_item_underline{
                                margin-left: 8px;
                                color: rgba(32, 37, 42, 0.65);
                                line-height: 11px;
                                font-size: 14px;
                                text-decoration: line-through;
                                font-weight: normal;
                            }
                        }
                        
                    }
                    &.current{
                        border: 1px solid #18AD25;
                        background-color: rgba(24, 173, 37,0.1);
                        position: relative;
                       
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
           
        }
    }
    
    
}
</style>
