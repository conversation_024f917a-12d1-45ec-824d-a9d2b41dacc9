<template>
    <el-dialog v-model="dialogVisible" class="sound_thali" width="800px"  :style="{transformOrigin: 'top right',transform: `scale(${rate})`}"  :close-on-click-modal="false" append-to-body>
        <template #header>
            <payCloseDialog :top="-95" @close="close_dialog" ref="pay_close_dialog_ref"></payCloseDialog>
            <div class="sound_thali_header">
                <span class="sound_thali_header_title">「配音帮手」声音套餐选择</span>
                <img src="@/assets/images/soundStore/sound_thali_close.png" @click="close" class="sound_thali_close" alt="">
            </div>
        </template>
        <template #default>
            <div class="sound_thali_container">
                <div class="thali_type_list">
                    <div class="thali_type_item" v-for="(item,index) in thali_list" :class="item.param==current?'current':''" :key="index" @click="change_thali(item.param)">
                        <div class="thali_type_item_recommend" v-if="item.isRecommend">
                            <img src="@/assets/images/soundStore/thali_type_item_recommend.png" alt="">推荐
                        </div>
                        <div class="thali_type_item_content">
                            <div class="thali_type_item_time">
                                {{ item.time }}
                            </div>
                            <!-- <div class="thali_type_item_day">
                                {{ item.day }}元/天
                            </div> -->
                            <div class="thali_type_item_price">
                                ¥{{ item.price }}
                            </div>
                            <!-- <div class="thali_type_item_underline">
                                ¥{{ item.underline }}
                            </div> -->
                        </div>
                    </div>
                </div>
                <thaliPayment ref="thali_payment" :currentCom="'sound_thali'"></thaliPayment>
            </div>
        </template>
    </el-dialog>
    <payStatusDialog ref="pay_status_dialog_ref" @status="order_status"></payStatusDialog>
</template>

<script setup>
import { ref, defineExpose, reactive,watch,onBeforeUnmount } from 'vue';
import thaliPayment from './compoments/thaliPayment.vue'
import {packageByCode,queryOrder}  from '@/api/soundStore.js'
import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
import payStatusDialog from "@/components/payDialog/pay_status_dialog.vue"
import { useloginStore } from '@/stores/login'
import payCloseDialog from "@/components/payDialog/pay_close_dialog.vue"
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
let loginStore = useloginStore()
const { fetchUserBenefits } = useUserBenefits()
let dialogVisible=ref(false)
let thali_payment=ref(null)
let rate=ref(window.innerWidth/1920)
let pay_close_dialog_ref=ref(null)
let close=()=>{
    pay_close_dialog_ref.value.pay_close_show=true
}
let close_dialog=()=>{
    dialogVisible.value=false
}
let close_tip_submit=()=>{

}
let close_tip_cancel=()=>{

}
let thali_list=reactive([
    {
        id:1,
        time : '12个月',
        day:'1.37',
        price:'499',
        underline:'899',
        isRecommend:true,
        param:'year'
    },
    {
        id:2,
        time : '6个月',
        day:'1.39',
        price:'299',
        underline:'499',
        isRecommend:false,
        param:'half_year'
    },{
        id:3,
        time : '1个月',
        day:'2.19',
        price:'68',
        underline:'99',
        isRecommend:false,
        param:'month'
    },
])
let info=ref({})
let current=ref('year')
let change_thali=(param)=>{
    current.value=param
    getData()
}
let user=reactive({
    finish_pay:'fail'
})
let pay_status_dialog_ref=ref(null)
let pollingInterval = ref(null); // 轮询定时器
let order_params=ref({})
let getData=async()=>{
    console.log(info.value,666);
    
   let data=await packageByCode({paymentType: "VOICE",userId:loginStore.userId,cycle:current.value,planId: info.value[0].id,quantity:1})
   console.log(data.resp_data.counter_url,'data');
   order_params.value=data
   thali_payment.value.current=Object.assign(thali_payment.value.current,data.resp_data)
   thali_payment.value.current.qrCode=data.resp_data.counter_url
   thali_payment.value.current.price=get_price(data.resp_data.total_amount,100)
   startPolling()
   console.log(thali_payment.value.current,999);
}
let startPolling = () => {
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value); // 清除已有的定时器
    }
    pollingInterval.value = setInterval(checkPaymentStatus, 3000);
    // pollingStartTime.value = Date.now()
};
let checkPaymentStatus = async () => {
    try {
     
        // 调用后端接口查询支付状态
        let data = await queryOrder({outOrderNo:order_params.value.resp_data.out_order_no});
       
        console.log(data.resp_data.order_status, pollingInterval.value, 5555666);
        // 假设返回的状态字段为 status
        // user.finish_pay = data.code; 
        // 
        // 如果支付成功或者失败或者过期，停止轮询
        if (data.resp_data.order_status == 2||data.resp_data.order_status == 3||data.resp_data.order_status == 4) {
            clearInterval(pollingInterval.value);
            if(data.resp_data.order_status == 4){
                getData()
                console.log("已过期，重新获取验证码");
            } else {
                if(data.resp_data.order_status == 2){
                    user.finish_pay='success'
                    // await notify(order_params.value) 
                }else{
                    user.finish_pay='fail' 
                }
                pay_status_dialog_ref.value.status = user.finish_pay; // 更新支付状态
                pay_status_dialog_ref.value.dialogVisible = true
            }
            return; // 退出函数
        } 
    } catch (error) {
        console.error('查询支付状态失败:', error);
    } 
};
let order_status = async (status) => {
    user.finish_pay=status

    // 如果支付成功，刷新用户权益信息并调用全局支付成功弹窗方法
    if (status === 'success') {
        console.log('声音套餐支付成功，开始刷新用户权益信息')

        // 刷新用户权益信息
        try {
            await fetchUserBenefits()
            console.log('✅ 声音套餐支付成功，用户权益信息刷新完成')
        } catch (error) {
            console.error('❌ 声音套餐支付成功，但权益信息刷新失败:', error)
        }

        // 调用全局支付成功弹窗方法
        setTimeout(() => {
            if (typeof window !== 'undefined' && window.showVoiceClonePaymentSuccess) {
                window.showVoiceClonePaymentSuccess()
            } else {
                console.log('全局方法不存在，声音套餐支付成功但无法显示弹窗')
            }
        }, 1000) // 延迟1秒，让支付成功消息先显示
    }
}
let get_price=(a,b)=>{
   
    
    return accDiv(a,b)
} 
defineExpose({
    dialogVisible,
    thali_list,
    info
});
watch(dialogVisible, (newValue, oldValue) => {
   if(newValue){
        getData()
        
   }else{
        current.value='year'
        thali_payment.value.current.paymethod='weixin'
        clearInterval(pollingInterval.value);
   }
});
onBeforeUnmount(()=>{
    clearInterval(pollingInterval.value);
})
</script>

<style lang="scss" >
.sound_thali {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 0;
    overflow: visible;
    .el-dialog__header{
        background: linear-gradient(-90deg, rgba(75, 230, 207, 0.4) 0%, rgba(212, 248, 174, 0.4) 100%);
        padding: 0;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        .el-dialog__headerbtn{
            display: none;
        }
        .sound_thali_header{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .sound_thali_header_title{
                font-size: 20px;
                line-height: 65px;
                color: #20252A;
                display: inline-block;
            }
            .sound_thali_close{
                width: 54px;
                height: 54px;
                position: absolute;
                top: 0;
                right: 0;
                cursor: pointer;
            }
        }
    }
    .el-dialog__body{
        padding: 28px 25px;
        width: 100%;
        box-sizing: border-box;
        .sound_thali_container{
            padding-top: 16px;
            .thali_type_list{
                display: flex;
                align-items: flex-end;
                margin-bottom: 23px;
                flex-wrap: wrap;
                .thali_type_item{
                    margin-right: 16px;
                    width: 175px;
                    height: 205px;
                    background: #FFFFFF;
                    border-radius: 8px;
                    border: 1px solid #E0E7F4;
                    display: flex;
                    flex-direction: column;
                    position: relative;
                    padding-top: 46px;
                    padding-left: 17px;
                    cursor: pointer;
                    .thali_type_item_recommend{
                        position: absolute;
                        top: -16px;
                        left: 0;
                        z-index: 1;
                        width: 73px;
                        height: 32px;
                        border-radius: 16px 16px 16px 0px;
                        font-size: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-left: -1px;
                        background: #17C256;
                        color:#fff;
                    }
                    .thali_type_item_content{
                        display: flex;
                        flex-direction: column;
                        .thali_type_item_time{
                            font-weight: bold;
                            font-size: 18px;
                            color: #20252A;
                            line-height: 18px;
                            margin-bottom: 13px;
                        }
                        .thali_type_item_day{
                            font-size: 14px;
                            color: rgba(32, 37, 42, 0.65);
                            line-height: 13px;
                            margin-bottom: 16px;
                        }
                        .thali_type_item_price{
                            font-weight: bold;
                            font-size: 18px;
                            color: #FF2F2F;
                            line-height: 14px;
                            margin-bottom: 16px;
                        }
                        .thali_type_item_underline{
                            text-decoration: line-through;
                            font-size: 14px;
                            color: rgba(32, 37, 42, 0.65);
                            line-height: 11px;
                        }
                    }
                    &.current{
                        border: 1px solid #17C256;
                        background-color: rgba(23, 194, 86, 0.1);
                        position: relative;
                     
                    }
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
            
        }
    }
    
    
}
</style>
