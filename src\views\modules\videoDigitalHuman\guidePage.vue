<template>
    <div class="guidePage">
        <header class="top-header">
			<Headbar ref="headbarRef" />
		</header>
        <div class="content">
            <div class="first">
                <p class="title">只需几步即可创作你的专属数字人</p>
                <div class="videoBox">
                    <video src="@/assets/video/people_basics_introduce.mp4" width="100%" height="100%" controls autoplay></video>
                </div>
                <div class="create">立即创建</div>
                <p class="cue">高质量数字人创建指南</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'


</script>

<style scoped lang="scss">
.guidePage { 
    width: 100%;
    height: 100%;
    .content{
        width: 100%;
        height: calc(100% - 64px);
        background: #F1F2F4;
        margin-top: 64px;
        padding-top: 100px;
        .first{
            width: 755px;
            height: 622px;
            background: #FFFFFF;
            margin: 0 auto;
            padding-top: 10px;
            .title{
                font-size: 22px;
                font-weight: 500;
                color: #000000;
                text-align: center;
                margin-bottom: 30px;
            }
            .videoBox{
                width: 90%;
                margin: 20px auto 10px;
            }
            .create{
                width: 118px;
                height: 36px;
                text-align: center;
                line-height: 36px;
                border-radius: 2px;
                background: #0AAF60;
                font-size: 14px;
                color: #FFFFFF;
                margin: 0 auto;
                cursor: pointer;
            }
            .cue{
                font-size: 14px;
                color: #353D49;
                text-align: center;
                margin-top: 30px;
            }
        }
    }
}   
</style>