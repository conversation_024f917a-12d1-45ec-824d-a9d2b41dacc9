<template>
    <div class="clone-result-container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-nav">
            <span class="nav-item" @click="showConfirmDialog">声音克隆</span>
            <span class="nav-separator">/</span>
            <span class="nav-item current">快速复刻</span>
        </div>

        <!-- 新增内容区域 -->
        <div class="content-wrapper">
            <div class="content-box">
                <div class="success-container">
                    <img src="@/assets/img/Union.png" alt="成功图标" class="success-icon" />
                    <p class="success-text">已生成您的克隆声音</p>
                </div>

                <!-- 添加文本预览区域 -->
                <div class="text-preview-container">
                    <div class="text-preview-box">
                        <div class="audio-icon" @click="togglePlay">
                            <img :src="isPlaying ? playingIcon : playIcon" :alt="isPlaying ? '播放中' : '播放'" />
                        </div>
                        <div class="preview-text">{{ currentText }}</div>
                        <!-- <div class="change-text-btn" @click="changePreviewText">
                            <img src="@/assets/img/Switch_(切换).png" alt="切换图标" class="switch-icon" />
                            <span>更换文案</span>
                        </div> -->
                    </div>
                </div>

                <!-- 添加头像上传区域 -->
                <div class="avatar-upload-container">
                    <div class="avatar-wrapper">
                        <span class="avatar-label">头像</span>
                        <el-upload
                            class="avatar-uploader"
                            action="#"
                            :show-file-list="false"
                            :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload"
                            :http-request="customUpload"
                        >
                            <template v-if="displayAvatarUrl">
                                <img :src="displayAvatarUrl" class="avatar" />
                                <div class="avatar-delete-icon" @click.stop="handleRemoveAvatar">
                                    <el-icon><Delete /></el-icon>
                                </div>
                            </template>
                            <div v-else class="avatar-placeholder">
                                <el-icon class="avatar-icon"><Plus /></el-icon>
                            </div>
                        </el-upload>
                    </div>
                    <div class="upload-tip">只能上传jpg/png文件，且不超过500kb</div>
                </div>
                
                <!-- 添加昵称输入框区域 -->
                <div class="nickname-input-container">
                    <div class="nickname-wrapper">
                        <span class="nickname-label">昵称</span>
                        <el-input
                            v-model="nickname"
                            class="nickname-input"
                            placeholder="请输入昵称"
                        />
                    </div>
                </div>

                <!-- 添加按钮区域 -->
                <div class="buttons-container">
                    <div class="buttons-wrapper">
                        <button class="back-button" @click="showConfirmDialog">返回</button>
                        <button class="activate-button" @click="showActivateDialog">激活</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 确认弹窗 -->
        <div class="confirm-dialog" v-if="confirmDialogVisible">
            <div class="confirm-dialog-mask"></div>
            <div class="confirm-dialog-container">
                <div class="confirm-dialog-close" @click="cancelBack">
                    <el-icon><Close /></el-icon>
                </div>
                <div class="confirm-dialog-content">
                    <div class="confirm-dialog-icon">
                        <img src="@/assets/img/fanhui.png" alt="返回图标" />
                    </div>
                    <div class="confirm-dialog-title">克隆声音未保存</div>
                    <div class="confirm-dialog-text">
                        <span>当前克隆的音色未保存，返回后</span>
                        <span class="second-line">需重新生成，确认要返回吗</span>
                    </div>
                    <div class="confirm-dialog-buttons">
                        <button class="cancel-button" @click="cancelBack">取消</button>
                        <button class="confirm-button" @click="confirmBack">确认</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 激活弹窗 -->
        <div class="activate-dialog" v-if="activateDialogVisible">
            <div class="confirm-dialog-mask"></div>
            <div class="activate-dialog-container">
                <!-- 添加头部图片 -->
                <div class="activate-dialog-header">
                    <img src="@/assets/img/service-header.png" alt="声音克隆服务" class="header-image" />
                    <!-- 将关闭按钮移动到图片上 -->
                    <div class="activate-dialog-close" @click="showWarningDialog">
                        <el-icon><Close /></el-icon>
                    </div>
                    <!-- 添加金额显示 -->
                    <div class="price-display">¥{{ paymentPrice || 38 }}</div>
                </div>
                <!-- 弹窗内容 -->
                <div class="activate-dialog-content">
                    <!-- 服务内容 -->
                    <div class="service-info-section">
                        <div class="section-title">服务内容</div>
                        <div class="service-items">
                            <div class="service-item">
                                <div class="service-icon">
                                    <img src="@/assets/img/checkbox-circle-line.png" alt="选中" class="check-icon" />
                                </div>
                                <div class="service-text">支持克隆 1个音色</div>
                            </div>
                            <div class="service-item">
                                <div class="service-icon">
                                    <img src="@/assets/img/checkbox-circle-line.png" alt="选中" class="check-icon" />
                                </div>
                                <div class="service-text">会员期间无限使用</div>
                            </div>
                            <div class="service-item">
                                <div class="service-icon">
                                    <img src="@/assets/img/checkbox-circle-line.png" alt="选中" class="check-icon" />
                                </div>
                                <div class="service-text">专属技术支持服务</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 用一个总的div包裹支付方式部分 -->
                    <div class="payment-wrapper">
                        <!-- 支付方式标题 -->
                        <div class="payment-section">
                            <div class="section-title">支付方式</div>
                        </div>
                        
                        <!-- 二维码和支付方式说明作为一个整体 -->
                        <div class="qrcode-section">
                            <div class="qrcode-container">
                                <!-- 使用动态生成的二维码 -->
                                <template v-if="paymentQrCode">
                                    <QRCode :value="paymentQrCode" :size="155" />
                                </template>
                                <img v-else src="@/assets/img/qrcode.png" alt="支付二维码" class="qrcode-image" />
                            </div>
                            <div class="payment-methods">
                                <span>支持</span>
                                <img src="@/assets/img/wechat.png" alt="微信支付" class="payment-icon" />
                                <img src="@/assets/img/alipay.png" alt="支付宝" class="payment-icon" />
                                <span>扫码支付</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部说明 -->
                    <div class="payment-notice">
                        支付即视为同意 <router-link to="/agreement?type=user" class="agreement-link" target="_blank">《用户协议》</router-link> 如遇到支付问题请在线反馈
                    </div>
                    
                    <!-- 底部安全提示 -->
                    <div class="security-info">
                        <el-icon class="shield-icon"><Lock /></el-icon>
                        支付安全由银联提供技术支持
                    </div>
                </div>
            </div>
        </div>

        <!-- 警告弹窗 -->
        <div class="warning-dialog" v-if="warningDialogVisible">
            <div class="warning-dialog-container">
                <div class="warning-content">
                    <div class="warning-icon">
                        <img src="@/assets/img/warning.png" alt="警告图标" />
                    </div>
                    <div class="warning-text">
                        <div class="warning-title">关闭提示</div>
                        <div class="warning-message">付款过程中关闭此页面可能导致音色生成失败，是否确认关闭？</div>
                    </div>
                    <div class="warning-buttons">
                        <button class="warning-cancel-button" @click="closeWarningDialog">取消</button>
                        <button class="warning-confirm-button" @click="confirmCloseActivateDialog">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 支付状态弹窗 -->
    <payStatusDialog ref="payStatusDialogRef" @status="onPaymentStatusChange"></payStatusDialog>

    <!-- 支付成功弹窗 - 只显示图片 -->
    <div
        v-if="paymentSuccessVisible"
        class="payment-success-overlay"
    >
        <div class="payment-success-image-container">
            <!-- 关闭按钮 -->
            <div
                class="close-button"
                @click="closePaymentSuccessDialog"
            >
                <img :src="shanchuImg" alt="关闭" class="close-icon" /> 
            </div>
            <img
                :src="chenggongImg"
                alt="支付成功"
                class="success-image-only"
                @click.stop
            />
            <!-- 透明的开心收下按钮点击区域 -->
            <div
                class="confirm-button-overlay"
                @click="closePaymentSuccessDialog"
                title="开心收下"
            ></div>
        </div>
    </div>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted, onUnmounted } from 'vue'
import { defineProps, defineEmits } from 'vue'
import { Plus, Delete, Close, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dubbing, callbackOss } from '@/api/dubbing'
import QRCode from 'qrcode.vue'
import payStatusDialog from '@/components/payDialog/pay_status_dialog.vue'

// 导入支付相关API
import { ordersCreate, queryOrder,  userInfo } from '@/api/account.js'
import { useloginStore } from '@/stores/login'

// 导入克隆次数相关API
import { getCloneTimesByUserId, deductCloneVoice } from '@/api/voiceOver.js'

// 导入友盟埋点Hook和声音克隆模块
import { useUmeng } from '@/utils/umeng/hook'
import { createVoiceCloneAnalytics } from '@/utils/umeng/modules/voiceClone'

// 导入用户权益数据刷新 hook
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'

// 导入支付成功弹窗所需的图片
import chenggongImg from '@/assets/img/chenggong.png'
import shanchuImg from '@/assets/img/shanchju.png'

// 获取登录状态
const loginStore = useloginStore()

// 初始化友盟埋点
const umeng = useUmeng()
const voiceCloneAnalytics = createVoiceCloneAnalytics(umeng)

// 用户权益数据刷新功能
const { fetchUserBenefits } = useUserBenefits()

// 支付成功弹窗状态
const paymentSuccessVisible = ref(false)

// 保存支付前的用户状态
const initialFristBuyCloneState = ref(null)
const hasRecordedInitialState = ref(false)

// 定义事件
const emit = defineEmits(['back', 'goToList', 'goToHome'])

// 定义接收的属性
const props = defineProps({
    // 这里可以定义需要接收的数据
    cloneData: {
        type: Object,
        default: () => ({})
    }
})

// 获取用户ID的函数
const getUserId = () => {
    // 优先从loginStore获取userId，如果没有再从localStorage获取
    const userIdFromStore = loginStore.userInfo?.userId || loginStore.userInfo?.id
    const userIdFromLocal = localStorage.getItem('userId')
    
    console.log('获取用户ID调试信息:', {
        loginStore: loginStore.userInfo,
        userIdFromStore,
        userIdFromLocal
    })
    
    return userIdFromStore || userIdFromLocal || ''
}

// 确认弹窗相关
const confirmDialogVisible = ref(false)

// 显示确认弹窗
const showConfirmDialog = () => {
    confirmDialogVisible.value = true
}

// 取消返回
const cancelBack = () => {
    confirmDialogVisible.value = false
}

// 确认返回
const confirmBack = () => {
    confirmDialogVisible.value = false
    console.log('确认返回，通知父组件跳转到声音克隆首页并刷新数据')
    

    
    // 通知父组件跳转到声音克隆首页，并传递需要刷新数据的标识
    emit('goToHome', { refreshData: true })
}

// 激活弹窗相关
const activateDialogVisible = ref(false)

// 添加警告弹窗状态
const warningDialogVisible = ref(false)

// 支付相关状态
const paymentData = ref(null)
const pollingInterval = ref(null)
const pollingCount = ref(0)          // 轮询计数器
const maxPollingCount = ref(200)     // 最大轮询次数（200次 * 3秒 = 10分钟）
const paymentStatus = ref('pending') // pending, success, fail
const paymentQrCode = ref('')
const paymentPrice = ref(0)

// 支付状态弹窗相关
const payStatusDialogRef = ref(null)

// 获取支付二维码
const getPaymentQrCode = async () => {
    try {
        // 调试信息：检查传入的数据
        console.log('准备创建订单，调试信息:', {
            nickname: nickname.value,
            avatarUrl: avatarUrl.value,
            cloneData: props.cloneData,
            currentText: currentText.value,
            userId: getUserId()
        })
        
        // 基础支付参数
        const baseParams = {
            paymentType: 'CLONE',
            quantity: 1,
            planId: "",
            userId: getUserId(),
        };

        // 新增克隆参数（使用用户输入的实际数据）
        const cloneParams = {
            cloneId: props.cloneData.id || props.cloneData.cloneId || "2",  // 使用上一页面传递的克隆ID
            voiceName: nickname.value || "我的克隆音色", 
            avatarUrl: avatarUrl.value || "",
            audioUrl: props.cloneData.audioUrl || "",  // 用户上传或录制的音频URL
            demoText: currentText.value,
            
        };

        // 合并参数
        const requestData = { ...baseParams, ...cloneParams };
        
        console.log('最终发送的请求数据:', requestData);
        
        // 调用支付创建接口
        const data = await ordersCreate(requestData);
        
        // 保存支付数据
        paymentData.value = data
        paymentQrCode.value = data.resp_data.counter_url
        paymentPrice.value = data.resp_data.total_amount / 100  // 假设金额需要除以100
        
        // 添加日志确认out_order_no字段
        console.log('订单创建成功，out_order_no:', data.resp_data.out_order_no)
        console.log('完整的支付数据:', data)
        
        return true
    } catch (error) {
        console.error('获取支付二维码失败:', error)
        ElMessage.error('获取支付信息失败，请稍后重试')
        return false
    }
}

// 开始轮询支付状态
const startPolling = () => {
    // 清除可能存在的定时器
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value)
    }
    
    // 重置轮询计数器
    pollingCount.value = 0
    
    // 设置新的定时器，每3秒检查一次
    pollingInterval.value = setInterval(checkPaymentStatus, 3000)
}

// 检查支付状态
const checkPaymentStatus = async () => {
    try {
        // 首先检查激活弹窗是否还在显示，如果不在显示则停止轮询
        if (!activateDialogVisible.value) {
            console.log('激活弹窗已关闭，停止支付状态轮询')
            if (pollingInterval.value) {
                clearInterval(pollingInterval.value)
                pollingInterval.value = null
            }
            return
        }
        
        // 增加轮询计数器
        pollingCount.value++
        
        // 检查是否超过最大轮询次数
        if (pollingCount.value > maxPollingCount.value) {
            // 清除轮询定时器
            clearInterval(pollingInterval.value)
            pollingInterval.value = null
            
            ElMessage.warning('支付检测超时，请检查支付状态或重新激活')
            console.log('支付轮询超时，已停止检测')
            return
        }
        
        if (!paymentData.value || !paymentData.value.resp_data) {
            console.log('支付数据不存在，停止轮询')
            return
        }
        
        // 调用查询订单状态接口
        const queryOrderNo = paymentData.value.resp_data.out_order_no
        console.log('开始查询订单状态，outOrderNo:', queryOrderNo)
        
        const data = await queryOrder({
            outOrderNo: queryOrderNo
        })
        
        console.log('查询订单状态返回:', data)
        
        // 检查订单状态
        // 2=支付成功，3=支付失败，4=已过期
        if (data.resp_data.order_status === "2"|| 
            data.resp_data.order_status === "3" || 
            data.resp_data.order_status === "4") {
            
            // 清除轮询定时器
            clearInterval(pollingInterval.value)
            pollingInterval.value = null
            
            if (data.resp_data.order_status === "2") {
                // 支付成功处理
                paymentStatus.value = 'success'
                
                try {
                    // 通知后端支付成功
                    // await notify(paymentData.value)
                    
                    console.log('支付成功，准备显示成功弹窗')
                    
                    // 友盟埋点：记录激活成功（付费）
                    try {
                        voiceCloneAnalytics.trackVoiceCloneActivateSuccess(
                            true, // 付费激活
                            nickname.value,
                            paymentPrice.value
                        )
                    } catch (umengError) {
                        console.error('友盟埋点失败:', umengError)
                    }
                    
                    // 关闭激活弹窗
                    closeActivateDialog()
                    
                    // 显示支付成功弹窗
                    if (payStatusDialogRef.value) {
                        payStatusDialogRef.value.status = 'success'
                        payStatusDialogRef.value.dialogVisible = true
                    }
                    
                    // 更新用户信息会在payStatusDialog关闭时自动调用
                    // await updateUserInfo()
                    
                } catch (notifyError) {
                    console.error('通知后端支付成功失败:', notifyError)
                    
                    // 即使通知失败，也要显示成功消息，因为支付已经成功
                    closeActivateDialog()
                    
                    // 仍然显示支付成功弹窗
                    if (payStatusDialogRef.value) {
                        payStatusDialogRef.value.status = 'success'
                        payStatusDialogRef.value.dialogVisible = true
                    }
                }
                
            } else if (data.resp_data.order_status === "4") {
                // 订单过期，重新获取二维码
                console.log('订单已过期，重新获取支付二维码')
                try {
                    await getPaymentQrCode()
                    // 重新开始轮询
                    startPolling()
                } catch (error) {
                    console.error('重新获取支付二维码失败:', error)
                    ElMessage.error('支付二维码已过期，请重新激活')
                    closeActivateDialog()
                }
                
            } else if (data.resp_data.order_status === "3") {
                // 支付失败
                paymentStatus.value = 'fail'
                ElMessage.error('支付失败，请稍后重试')
                
                // 可以选择关闭弹窗或保持打开让用户重新支付
                // closeActivateDialog()
            }
        }
        
        // 如果订单状态是其他值（比如1=待支付），继续轮询
        
    } catch (error) {
        console.error('查询支付状态失败:', error)
        // 网络错误不应该停止轮询，继续等待下次查询
    }
}

// 更新用户信息
const updateUserInfo = async () => {
    try {
        const userData = await userInfo({userId:loginStore.userId})
        loginStore.setUserInfo(userData)
    } catch (error) {
        console.error('更新用户信息失败:', error)
    }
}

// 显示激活弹窗
const showActivateDialog = async () => {
    console.log('尝试显示激活弹窗')

    // 在激活开始前记录用户的初始状态
    await recordInitialUserState()

    // 检查昵称是否已填写
    if (!nickname.value || nickname.value.trim() === '') {
        ElMessage.warning('请填写昵称')
        return
    }

    // 检查用户登录状态
    if (!loginStore.token || loginStore.token === '') {
        // 未登录，提示用户登录
        ElMessage.warning('请先登录')
        console.log('用户未登录，无法激活')
        // 可以添加跳转到登录页面的逻辑
        return
    }
    
    // 首先检查用户的克隆次数
    console.log('检查用户克隆次数')
    try {
        const cloneTimesResponse = await getCloneTimesByUserId({
            userId: getUserId(),
            type: 1
        })
        
        console.log('克隆次数查询结果:', cloneTimesResponse)
        
        const availableNum = cloneTimesResponse.availableNum || 0
        
        if (availableNum > 0) {
            // 有可用次数，直接扣除并跳转
            console.log(`用户有 ${availableNum} 次可用克隆次数，执行扣除操作`)
            
            try {
                // 调用扣除接口，参数与create接口相同
                const deductParams = {
                    cloneId: props.cloneData.id || props.cloneData.cloneId || "2",
                    voiceName: nickname.value || "我的克隆音色", 
                    avatarUrl: avatarUrl.value || "",
                    audioUrl: props.cloneData.audioUrl || "",
                    demoText: currentText.value,
                    userId: getUserId(),
                    paymentType: 'CLONE',
                }
                
                console.log('扣除克隆次数请求参数:', deductParams)
                
                const deductResponse = await deductCloneVoice(deductParams)
                
                console.log('克隆次数扣除成功:', deductResponse)
                
                // 友盟埋点：记录激活成功（免费）
                try {
                    voiceCloneAnalytics.trackVoiceCloneActivateSuccess(
                        false, // 免费激活
                        nickname.value
                    )
                } catch (umengError) {
                    console.error('友盟埋点失败:', umengError)
                }
                
                ElMessage.success('声音克隆激活成功！')
                
                // 延迟一下再跳转，让用户看到成功消息
                setTimeout(() => {
                    console.log('扣除成功，跳转到克隆列表页')
                    emit('goToList')
                }, 1500)
                
                return
                
            } catch (deductError) {
                console.error('扣除克隆次数失败:', deductError)
                ElMessage.error('激活失败，请稍后重试')
                return
            }
        } else {
            // 没有可用次数，走支付流程
            console.log('用户没有可用克隆次数，进入支付流程')
        }
        
    } catch (error) {
        console.error('查询克隆次数失败:', error)
        ElMessage.error('查询克隆次数失败，请稍后重试')
        return
    }
    
    // 走支付流程（原有逻辑）
    // 清理之前可能存在的轮询状态
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value)
        pollingInterval.value = null
    }
    
    // 重置支付状态
    paymentStatus.value = 'pending'
    pollingCount.value = 0
    
    console.log('开始获取支付二维码')
    // 获取支付二维码
    const success = await getPaymentQrCode()
    
    if (success) {
        console.log('支付二维码获取成功，显示激活弹窗')
        

        
        // 显示激活弹窗
        activateDialogVisible.value = true
        
        console.log('开始支付状态轮询')
        // 开始轮询支付状态
        startPolling()
    } else {
        console.log('支付二维码获取失败，不显示弹窗')
    }
}

// 关闭激活弹窗
const closeActivateDialog = () => {
    console.log('关闭激活弹窗，清理支付轮询状态')
    
    // 关闭弹窗
    activateDialogVisible.value = false
    
    // 停止轮询
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value)
        pollingInterval.value = null
        console.log('支付状态轮询已停止')
    }
    
    // 重置轮询计数器
    pollingCount.value = 0
    
    // 重置支付状态
    paymentStatus.value = 'pending'
}

// 添加显示警告弹窗的方法
const showWarningDialog = () => {
    warningDialogVisible.value = true
}

// 添加关闭警告弹窗的方法
const closeWarningDialog = () => {
    warningDialogVisible.value = false
}

// 添加确认关闭的方法
const confirmCloseActivateDialog = () => {
    // 关闭警告弹窗
    warningDialogVisible.value = false
    // 执行原来的关闭激活弹窗逻辑
    closeActivateDialog()
}

// 格式化文本，超过50个字截断并添加省略号
const formatText = (text) => {
    if (text.length > 50) {
        return text.substring(0, 50) + '...'
    }
    return text
}

// 当前显示的文本 - 优先使用传入的demoText，否则使用默认文本
const defaultText = "你好，欢迎使用我们的音色克隆服务！在这里，你可以轻松定制属于自己的专属音色，让声音更具个性与魅力。"
const currentText = ref(formatText(props.cloneData?.demoText || defaultText))



// 导入图片资源
import playIcon from '@/assets/img/bofang.png'
import playingIcon from '@/assets/img/BUTU.png'

// 播放状态管理
const isPlaying = ref(false)
const audioElement = ref(null)

// 切换播放状态
const togglePlay = () => {
    const audioUrl = props.cloneData?.audioUrl
    
    if (!audioUrl) {
        ElMessage.warning('音频地址不存在')
        return
    }
    
    // 验证音频URL格式
    if (!audioUrl.startsWith('http')) {
        ElMessage.error('音频地址格式无效')
        console.error('无效的音频URL:', audioUrl)
        return
    }
    
    if (isPlaying.value) {
        // 当前正在播放，点击暂停
        pauseAudio()
    } else {
        // 当前未播放，检查是否有已存在的音频实例
        if (audioElement.value && audioElement.value.src === audioUrl) {
            // 有相同的音频实例，从暂停位置恢复播放
            resumeAudio()
        } else {
            // 没有音频实例或音频源不同，重新开始播放
            playAudio(audioUrl)
        }
    }
}

// 播放音频
const playAudio = (audioUrl) => {
    try {
        console.log('开始播放音频:', audioUrl)
        

        
        // 如果已有音频实例，先完全停止
        if (audioElement.value) {
            console.log('清理现有音频实例')
            audioElement.value.pause()
            audioElement.value = null
        }
        
        // 创建新的音频实例
        audioElement.value = new Audio(audioUrl)
        
        // 设置音频属性
        audioElement.value.preload = 'auto'
        audioElement.value.volume = 1.0  // 确保音量是最大
        audioElement.value.crossOrigin = 'anonymous'  // 处理跨域音频
        
        // 添加事件监听
        audioElement.value.addEventListener('loadstart', () => {
            console.log('开始加载音频:', audioUrl)
        })
        
        audioElement.value.addEventListener('loadeddata', () => {
            console.log('音频数据加载完成')
        })
        
        audioElement.value.addEventListener('canplay', () => {
            console.log('音频可以播放，音量:', audioElement.value.volume)
        })
        
        audioElement.value.addEventListener('canplaythrough', () => {
            console.log('音频可以流畅播放')
        })
        
        audioElement.value.addEventListener('play', () => {
            isPlaying.value = true
            console.log('音频开始播放，当前时间:', audioElement.value.currentTime)
        })
        
        audioElement.value.addEventListener('playing', () => {
            console.log('音频正在播放中')
        })
        
        audioElement.value.addEventListener('pause', () => {
            isPlaying.value = false
            console.log('音频暂停')
        })
        
        audioElement.value.addEventListener('ended', () => {
            isPlaying.value = false
            audioElement.value = null
            console.log('音频播放结束')
        })
        
        audioElement.value.addEventListener('error', (e) => {
            isPlaying.value = false
            audioElement.value = null
            console.error('音频播放错误:', e)
            console.error('错误详情:', {
                error: e.target.error,
                networkState: e.target.networkState,
                readyState: e.target.readyState,
                src: e.target.src
            })
            ElMessage.error('音频播放失败: ' + (e.target.error?.message || '未知错误'))
        })
        
        audioElement.value.addEventListener('stalled', () => {
            console.warn('音频加载停滞')
        })
        
        audioElement.value.addEventListener('waiting', () => {
            console.log('音频缓冲中')
        })
        
        // 开始播放
        const playPromise = audioElement.value.play()
        
        if (playPromise !== undefined) {
            playPromise
                .then(() => {
                    console.log('音频播放成功启动')
                })
                .catch(error => {
                    console.error('播放失败:', error)
                    
                    // 处理常见的播放错误
                    if (error.name === 'NotAllowedError') {
                        ElMessage.error('浏览器阻止了自动播放，请手动点击播放')
                    } else if (error.name === 'NotSupportedError') {
                        ElMessage.error('不支持该音频格式')
                    } else if (error.name === 'AbortError') {
                        ElMessage.error('音频加载被中断')
                    } else {
                        ElMessage.error('音频播放失败: ' + error.message)
                    }
                    
                    isPlaying.value = false
                    audioElement.value = null
                })
        }
        
    } catch (error) {
        console.error('创建音频实例失败:', error)
        ElMessage.error('音频播放失败: ' + error.message)
        isPlaying.value = false
    }
}

// 暂停音频播放（保留播放位置）
const pauseAudio = () => {
    if (audioElement.value) {
        audioElement.value.pause()
        console.log('音频暂停，当前播放位置:', audioElement.value.currentTime)
    }
    isPlaying.value = false
}

// 恢复音频播放（从暂停位置继续）
const resumeAudio = () => {
    if (audioElement.value) {
        console.log('从位置恢复播放:', audioElement.value.currentTime)
        audioElement.value.play().then(() => {
            isPlaying.value = true
            console.log('音频恢复播放成功')
        }).catch(error => {
            console.error('恢复播放失败:', error)
            ElMessage.error('恢复播放失败: ' + error.message)
        })
    }
}

// 完全停止音频播放（重置到开头并清理资源）
const stopAudio = () => {
    if (audioElement.value) {
        audioElement.value.pause()
        audioElement.value.currentTime = 0
        audioElement.value = null
        console.log('音频完全停止并清理')
    }
    isPlaying.value = false
}





// 头像上传相关
const avatarUrl = ref('') // 存储storage_path，用于传给后端
const displayAvatarUrl = ref('') // 存储显示用的URL
const isUploading = ref(false)
const nickname = ref('')

const handleAvatarSuccess = (res, file) => {
    displayAvatarUrl.value = URL.createObjectURL(file.raw)
}

// 添加删除头像处理函数
const handleRemoveAvatar = () => {
    avatarUrl.value = ''
    displayAvatarUrl.value = ''
    ElMessage.success('头像已删除')
}

const beforeAvatarUpload = (file) => {
    const isJPG = file.type === 'image/jpeg'
    const isPNG = file.type === 'image/png'
    const isLt500K = file.size / 1024 < 500

    if (!isJPG && !isPNG) {
        ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
        return false
    }
    
    if (!isLt500K) {
        ElMessage.error('上传头像图片大小不能超过 500KB!')
        return false
    }
    
    return true
}

const customUpload = async (options) => {
    const { file, onSuccess, onError, onProgress } = options
    isUploading.value = true
    
    try {
        // 获取文件扩展名确定文件类型
        const fileExtension = file.name.split('.').pop().toLowerCase()
        const fileType = fileExtension === 'jpg' ? 'jpg' : 'png'
        
        // 调用 dubbing API 获取 OSS 上传凭证
        const response = await dubbing({ userId: getUserId(), fileType })
        
        // 去掉文件名的后缀
        const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.')
        
        // 准备上传表单数据
        const formData = new FormData()
        formData.append('OSSAccessKeyId', response.accessKeyId)
        formData.append('policy', response.policy)
        formData.append('signature', response.signature)
        formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`)
        formData.append('file', file)
        
        // 使用 XHR 上传文件
        const xhr = new XMLHttpRequest()
        
        // 进度事件
        xhr.upload.onprogress = (e) => {
            if (e.total > 0) {
                const percent = Math.floor((e.loaded / e.total) * 100)
                onProgress({ percent })
            }
        }
        
        // 上传完成处理
        xhr.onload = async () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    // 构建文件URL（用于显示）
                    const fileUrl = `${response.host}/${response.key.replace(/[^\/]+$/, '')}${file.name}`
                    
                    // 调用 callbackOss 接口
                    const callbackResponse = await callbackOss({
                        userId: getUserId(),
                        materialName: fileNameWithoutExt,
                        ossPath: response.key.replace(/[^\/]+$/, '') + file.name,
                        fileSize: String(file.size),
                        fileExtension: fileExtension,
                        tagNames: '1',
                        materialType: 'image',
                        isPrivate: '1',
                        storage_path: `/material/${getUserId()}/${file.name}`,
                        temporary:'1',
                    })
                    
                    ElMessage.success('头像上传成功！')
                    
                    // 更新显示用的URL
                    displayAvatarUrl.value = fileUrl
                    
                    // 更新avatarUrl为完整的URL（用于传给后端）
                    let storagePath
                    if (callbackResponse && callbackResponse.storage_path) {
                        storagePath = callbackResponse.storage_path
                    } else {
                        // 如果callback没有返回storage_path，使用传入的storage_path
                        storagePath = `/material/${getUserId()}/${file.name}`
                    }
                    
                    // 拼接域名形成完整的URL
                    avatarUrl.value = `${response.host}${storagePath}`
                    
                    console.log('头像上传完成:', {
                        displayUrl: displayAvatarUrl.value,
                        storagePath: avatarUrl.value,
                        callbackResponse
                    })
                    
                    // 调用成功回调
                    onSuccess(callbackResponse)
                    
                } catch (callbackError) {
                    console.error('Callback接口调用失败:', callbackError)
                    ElMessage.error('文件上传成功，但保存失败')
                    onError(callbackError)
                }
            } else {
                const error = new Error(xhr.statusText || '上传失败')
                ElMessage.error('头像上传失败')
                onError(error)
            }
            
            isUploading.value = false
        }
        
        // 上传错误处理
        xhr.onerror = (error) => {
            console.error('上传错误:', error)
            ElMessage.error('头像上传失败')
            onError(error)
            isUploading.value = false
        }
        
        // 发送上传请求
        xhr.open('POST', response.host, true)
        xhr.send(formData)
        
    } catch (error) {
        console.error('上传失败:', error)
        ElMessage.error('上传失败: ' + error.message)
        onError(error)
        isUploading.value = false
    }
}

// 页面可见性监听
const handleVisibilityChange = () => {
    if (document.hidden && pollingInterval.value) {
        console.log('页面不可见，暂停支付轮询')
        clearInterval(pollingInterval.value)
        pollingInterval.value = null
    } else if (!document.hidden && activateDialogVisible.value && paymentData.value) {
        console.log('页面重新可见，恢复支付轮询')
        startPolling()
    }
}

// 组件挂载时添加页面可见性监听
onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 输出调试信息
    console.log('CloneResult组件接收到的数据:', props.cloneData)
    console.log('显示的文本:', currentText.value)
    console.log('音频URL:', props.cloneData?.audioUrl)

    // 输出支付成功弹窗相关的调试信息
    console.log('=== 支付成功弹窗初始化信息 ===')
    console.log('当前用户会员信息:', loginStore.memberInfo)
    console.log('当前 frist_buy_clone 值:', loginStore.memberInfo?.frist_buy_clone)
    console.log('是否已看过弹窗:', localStorage.getItem('hasSeenVoiceClonePaymentSuccess'))
    console.log('=== 初始化信息输出完成 ===')
})

// 组件卸载时移除监听器
onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// 组件卸载前清除定时器和音频资源
onBeforeUnmount(() => {
    console.log('组件即将卸载，清理支付轮询和音频资源')
    if (pollingInterval.value) {
        clearInterval(pollingInterval.value)
        pollingInterval.value = null
    }

    // 清理音频资源
    if (audioElement.value) {
        audioElement.value.pause()
        audioElement.value = null
    }

    // 清理初始状态记录
    initialFristBuyCloneState.value = null
    hasRecordedInitialState.value = false
    console.log('已清理初始状态记录')
})

// 记录支付前的初始状态
const recordInitialUserState = async () => {
    if (hasRecordedInitialState.value) {
        console.log('已记录过初始状态，跳过')
        return
    }

    console.log('=== 开始记录支付前初始状态 ===')

    try {
        // 在记录初始状态前，先刷新用户权益数据以确保获取最新状态
        console.log('🔄 刷新用户权益数据以获取最新状态...')
        await fetchUserBenefits()
        console.log('✅ 用户权益数据刷新完成')
    } catch (error) {
        console.error('❌ 刷新用户权益数据失败:', error)
        // 即使刷新失败，也继续记录当前状态
    }

    const memberInfo = loginStore.memberInfo
    console.log('当前用户会员信息:', memberInfo)
    console.log('当前 frist_buy_clone 值:', memberInfo?.frist_buy_clone)

    if (memberInfo) {
        initialFristBuyCloneState.value = memberInfo.frist_buy_clone
        hasRecordedInitialState.value = true
        console.log('✅ 已记录支付前 frist_buy_clone 状态:', initialFristBuyCloneState.value)
        console.log('状态类型:', typeof initialFristBuyCloneState.value)
        console.log('是否为 null:', initialFristBuyCloneState.value === null)
        console.log('是否为 undefined:', initialFristBuyCloneState.value === undefined)
        console.log('是否为空字符串:', initialFristBuyCloneState.value === '')
    } else {
        console.log('❌ memberInfo 不存在，记录初始状态为 null')
        initialFristBuyCloneState.value = null
        hasRecordedInitialState.value = true
    }
    console.log('=== 初始状态记录完成 ===')
}

// 检查是否应该显示支付成功弹窗（基于支付前保存的初始状态判断）
const checkShouldShowPaymentSuccessDialog = () => {
    console.log('=== 支付成功弹窗检查开始 ===')
    
    // 检查用户是否已经看过弹窗，避免重复显示
    const hasSeenPaymentSuccessDialog = localStorage.getItem('hasSeenVoiceClonePaymentSuccess')
    console.log('检查是否已看过弹窗:', hasSeenPaymentSuccessDialog)
    
    if (hasSeenPaymentSuccessDialog === 'true') {
        console.log('❌ 用户已看过支付成功弹窗，不再显示')
        console.log('=== 支付成功弹窗检查结束：已看过 ===')
        return false
    }
    
    // 获取支付前保存的初始状态
    const initialState = initialFristBuyCloneState.value
    console.log('支付前保存的初始 frist_buy_clone 状态:', initialState)
    console.log('初始状态类型:', typeof initialState)
    console.log('初始状态是否为 null:', initialState === null)
    console.log('初始状态是否为 undefined:', initialState === undefined)
    console.log('初始状态是否为空字符串:', initialState === '')
    
    // 获取当前最新的会员信息（用于日志对比）
    const memberInfo = loginStore.memberInfo
    console.log('当前会员信息:', memberInfo)
    console.log('支付后最新的 frist_buy_clone 值:', memberInfo?.frist_buy_clone)
    
    // 基于支付前的初始状态判断：
    // 1. 如果支付前 frist_buy_clone 为 null/undefined/空字符串，说明是首次购买，显示弹窗
    // 2. 如果支付前 frist_buy_clone 有值，说明用户之前已购买过，但本次支付成功仍应显示弹窗
    // 3. 只有用户在当前会话中已经看过弹窗（localStorage 标记），才不显示
    
    if (initialState === null || initialState === undefined || initialState === '') {
        console.log('✅ 支付前为首次购买状态，显示支付成功弹窗')
        console.log('=== 支付成功弹窗检查结束：首次购买，显示弹窗 ===')
        return true
    } else {
        console.log('✅ 支付前已有克隆服务，但本次支付成功仍显示弹窗')
        console.log('=== 支付成功弹窗检查结束：再次购买，显示弹窗 ===')
        return true
    }
}

// 关闭支付成功弹窗
const closePaymentSuccessDialog = () => {
    paymentSuccessVisible.value = false

    // 标记用户已看过支付成功弹窗，确保不会重复显示
    localStorage.setItem('hasSeenVoiceClonePaymentSuccess', 'true')
    console.log('支付成功弹窗已关闭，已标记用户看过弹窗')

    // 弹窗关闭后跳转到克隆页面
    console.log('支付成功弹窗关闭，跳转到声音克隆页面')
    emit('goToList')
}

// 显示支付成功弹窗的方法
const showPaymentSuccessDialog = async () => {
    try {
        console.log('🎉 === 开始显示支付成功弹窗 ===')

        // 刷新用户权益数据，确保获取最新的接口数据
        console.log('🔄 支付成功后刷新用户权益数据...')
        await fetchUserBenefits()
        console.log('✅ 支付成功后用户权益数据刷新完成')
        console.log('接口返回的最新 frist_buy_clone 值:', loginStore.memberInfo?.frist_buy_clone)

        // 基于支付前保存的初始状态检查是否应该显示弹窗
        const shouldShow = checkShouldShowPaymentSuccessDialog()
        console.log('基于支付前初始状态的弹窗检查结果:', shouldShow)

        if (shouldShow) {
            // 基于支付前初始状态，应该显示支付成功弹窗
            console.log('🎊 支付成功，显示支付成功弹窗')
            paymentSuccessVisible.value = true
            console.log('弹窗状态已设置为:', paymentSuccessVisible.value)
        } else {
            // 用户已看过弹窗，直接跳转到克隆页面
            console.log('🔄 用户已看过弹窗，直接跳转到声音克隆页面')
            emit('goToList')
        }
        console.log('🎉 === 显示支付成功弹窗结束 ===')
    } catch (error) {
        console.error('❌ 显示支付成功弹窗失败:', error)
        // 即使出错，也跳转到克隆页面
        console.log('出错后跳转到声音克隆页面')
        emit('goToList')
    }
}

// 处理支付状态变化
const onPaymentStatusChange = (status) => {
    console.log('支付状态变化:', status)
    if (status === 'success') {
        // 支付成功后的处理
        ElMessage.success('支付成功，声音克隆服务已激活！')

        // 延迟一下再显示支付成功弹窗
        setTimeout(() => {
            console.log('支付成功，显示支付成功弹窗')
            showPaymentSuccessDialog()
        }, 1500)
    }
}
</script>

<style scoped lang="scss">
.clone-result-container {
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
    display: flex;
    height: 54px;
    font-size: 14px;
    padding: 20px 20px 0;
}

.nav-item {
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
        color: #409EFF;
    }

    &.current {
        color: #333;
        font-weight: 500;
        cursor: default;

        &:hover {
            color: #333;
        }
    }
}

.nav-separator {
    margin: 0 8px;
    color: #999;
}

/* 内容区域样式 */
.content-wrapper {
    flex: 1;
    display: flex;
    justify-content: center;
}

.content-box {
    width: 1554px;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 50px; /* 添加底部内边距确保有足够空间 */
}

/* 成功提示样式 */
.success-container {
    margin-top: 63px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.success-icon {
    width: 60px;
    height: 60px;
}

.success-text {
    margin-top: 20px;
    font-size: 16px;
    color: #333;
    font-weight: 400;
}

/* 文本预览区域样式 */
.text-preview-container {
    margin-top: 69px;
    display: flex;
    justify-content: center;
    padding: 0 20px;
    width: 100%;
}

.text-preview-box {
    width: 100%;
    color: #000000;
    max-width: 1100px;
    border: 1px solid #EAEAEA;
    border-radius: 6px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    background-color: #FFFFFF;
}

.audio-icon {
    margin-right: 9px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
        transform: scale(1.1);
    }
}

.audio-icon img {
    width: 36px;
    height: 36px;
    display: block;
}

.preview-text {
    flex: 1;
    font-size: 16px;
    color: #333;
    font-weight: 500;
    line-height: 1.5;
}

.change-text-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #1890FF;
    font-size: 14px;
}

.switch-icon {
    width: 15px;
    height: 15px;
    margin-right: 5px;
}



/* 头像上传区域样式 */
.avatar-upload-container {
    margin-top: 25px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 1100px;
}

.avatar-wrapper {
    display: flex;
    align-items: flex-start;
}

.avatar-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
}

.avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;
    margin-left: 5px;
    &:hover {
        border-color: #1890FF;
        
        .avatar-delete-icon {
            opacity: 1;
        }
    }
}

.avatar-uploader .avatar {
    width: 148px;
    height: 148px;
    display: block;
}

.avatar-placeholder {
    width: 148px;
    height: 148px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    color: #8c939d;
    background-color: #FFFFFF;
}

.avatar-icon {
    font-size: 28px;
}

.avatar-delete-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    cursor: pointer;
    color: #fff;
    transition: all 0.3s;
    opacity: 0;
    
    .el-icon {
        font-size: 20px;
    }
    
    &:hover {
        background-color: rgba(0, 0, 0, 0.7);
    }
}

.upload-tip {
    margin-top: 6px;
    margin-left: 32px;
    font-size: 12px;
    color: #606266;
}

/* 昵称输入框区域样式 */
.nickname-input-container {
    margin-top: 50px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 1100px;
}

.nickname-wrapper {
    display: flex;
    align-items: center;
}

.nickname-label {
    font-size: 14px;
    color: #333;
    margin-right: 5px;
}

.nickname-input {
    width: 420px;
    height: 32px;
}

/* 按钮区域样式 */
.buttons-container {
    margin-top: 68px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 1100px;
}

.buttons-wrapper {
    display: flex;
    align-items: center;
    margin-left: 32px; /* 对齐上传和input边框 */
}

.back-button {
    width: 60px;
    height: 32px;
    background-color: #F2F3F5;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
    color: #333;
}

.activate-button {
    width: 90px;
    height: 32px;
    background-color: #0AAF60;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: #FFFFFF;
}

/* 确认弹窗样式 */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.confirm-dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.confirm-dialog-container {
    position: relative;
    width: 380px;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10000;
}

.confirm-dialog-close {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    color: #909399;
}

.confirm-dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.confirm-dialog-icon {
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
}

.confirm-dialog-icon img {
    width: 49px;
    height: 45px;
}

.confirm-dialog-title {
    font-size: 16px;
    color: #000000;
    font-weight: 500;
    margin-bottom: 10px;
    text-align: center;
}

.confirm-dialog-text {
    font-size: 12px;
    color: #606266;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.confirm-dialog-text .second-line {
    margin-top: 5px;
}

.confirm-dialog-buttons {
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}

.cancel-button {
    width: 96px;
    height: 40px;
    background-color: #0AAF60;
    border: none;
    border-radius: 4px;
    color: #FFFFFF;
    cursor: pointer;
    font-size: 14px;
}

.confirm-button {
    width: 96px;
    height: 40px;
    background-color: #D3D3D2;
    border: none;
    border-radius: 4px;
    color: #FFFFFF;
    cursor: pointer;
    font-size: 14px;
}

/* 激活弹窗样式 */
.activate-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.activate-dialog .confirm-dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.activate-dialog-container {
    position: relative;
    width: 464px;
    min-height: 600px; /* 增加最小高度 */
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 0; /* 移除内边距以使图片能完全贴合容器 */
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10000;
    overflow: hidden; /* 确保圆角效果 */
}

/* 添加头部样式 */
.activate-dialog-header {
    position: relative;
    width: 100%;
    height: 161px; /* 设置头部高度为161px */
}

.header-image {
    width: 441px; /* 设置宽度为441px */
    height: 161px; /* 设置高度为161px */
    display: block;
    margin: 12px auto;
    object-fit: cover; /* 确保图片覆盖整个区域 */
}

.activate-dialog-close {
    position: absolute;
    top: 19px;
    right: 14px;
    cursor: pointer;
    color: #00000073;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    font-size: 20px;
}

.activate-dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    padding: 12px; /* 添加12px内边距 */
}

/* 服务内容部分 */
.service-info-section {
    width: 100%;
    margin-bottom: 20px;
    margin-top: 24px; /* 添加上边距，与图片底部12px边距一起形成36px间距 */
    margin-left: 12px;
}

.section-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px; /* 减少标题下方的间距，从12px改为8px */
}

.service-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.service-item {
    display: flex;
    align-items: center;
}

.service-icon {
    margin-right: 8px;
    display: flex;
    align-items: center;
}

.check-icon {
    width: 16px;
    height: 16px;
    display: block;
}

.service-text {
    font-size: 14px;
    color: #333;
}

/* 支付方式整体包装 */
.payment-wrapper {
    display: flex;
    width: 100%;
    border-radius: 4px;
    padding: 12px;
    box-sizing: border-box;
}

/* 支付方式部分 */
.payment-section {
    margin-bottom: 8px; /* 减少与下面二维码区域的间距 */
}

/* 二维码部分作为一个整体 */
.qrcode-section {
    margin-left: 77px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.qrcode-container {
    display: flex;
    justify-content: center;
    margin-top: 0;
    margin-bottom: 15px;
}

.qrcode-image {
    width: 155px; /* 调整二维码尺寸 */
    height: 155px;
}

.payment-methods {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
}

.payment-icon {
    width: 13px;
    height: 13px;
    margin: 0 5px;
}

.payment-price {
    margin-left: 5px;
    font-size: 16px;
    color: #FF3B30;
    font-weight: bold;
}

/* 底部说明 */
.payment-notice {
    font-size: 12px;
    color: #606266;
    margin-bottom: 15px;
    width: 100%;
    padding-left: 12px;
    text-align: left;
    box-sizing: border-box;
}

.agreement-link {
    color: #1890FF;
    text-decoration: none;
}

/* 底部安全提示 */
.security-info {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #909399;
}

.shield-icon {
    margin-right: 5px;
    font-size: 14px;
}

/* 价格显示样式 */
.price-display {
    position: absolute;
    bottom: 12px;
    left: 32px;
    font-size: 38px;
    color: #FFFFFF;
    font-family: 'Manrope', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    line-height: 1;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 警告弹窗样式 */
.warning-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start; /* 改为顶部对齐 */
    padding-top: 80px; /* 添加顶部间距，让警告弹窗显示在支付弹窗上方 */
    z-index: 10001; /* 比激活弹窗的z-index更高 */
    pointer-events: none; /* 防止点击外侧区域 */
}

.warning-dialog-container {
    position: relative;
    width: 720px;
    height: 76px;
    background-color: #FAECD8;
    border: 1px solid #FD9D26;
    border-radius: 4px;
    display: flex;
    align-items: center;
    z-index: 10002;
    pointer-events: auto; /* 允许弹窗内容接收点击事件 */
}

.warning-content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 17px 0 16px; /* 左边距16px，右边距17px */
}

.warning-icon {
    display: flex;
    align-items: center;
    margin-right: 3px; /* img右侧3px处有文字 */
}

.warning-icon img {
    width: 32px; /* 修改图标大小为32px */
    height: 32px; /* 修改图标大小为32px */
    display: block;
}

.warning-text {
    flex: 1;
    display: flex;
    flex-direction: column; /* 确保垂直排列 */
    justify-content: center;
    gap: 5px; /* 添加10px的间距 */
}

.warning-title {
    font-size: 14px;
    font-weight: 600; /* 修改字重为600 */
    color: #353D49; /* 修改颜色为#353D49 */
    font-family: 'PingFang SC', sans-serif; /* 添加字体族 */
    line-height: 1.2;
    margin-bottom: 0px;
}

.warning-message {
    font-size: 14px; /* 修改字体大小为14px */
    color: #666;
    font-weight: 400; /* 修改字重为400 */
    font-family: 'PingFang SC', sans-serif; /* 添加字体族 */
    line-height: 1.2;
}

.warning-buttons {
    display: flex;
    gap: 8px; /* 两个按钮之间的间距 */
}

.warning-cancel-button {
    width: 68px;
    height: 32px;
    background-color: #FD9D26;
    border: none;
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 14px;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.warning-cancel-button:hover {
    opacity: 0.8;
}

.warning-confirm-button {
    width: 68px;
    height: 32px;
    background-color: #FAECD8;
    border: 1px solid #FD9D26;
    border-radius: 4px;
    color: #FD9D26;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.warning-confirm-button:hover {
    background-color: #F5E1C8;
}

/* 支付成功弹窗样式 */
.payment-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.payment-success-image-container {
    position: relative;
    display: inline-block;
}

.success-image-only {
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    height: auto;
}

.close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
    z-index: 10000;
}

.close-icon {
    width: 24px;
    height: 24px;
}

.confirm-button-overlay {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 50px;
    background-color: transparent;
    cursor: pointer;
    z-index: 10000;
}
</style>