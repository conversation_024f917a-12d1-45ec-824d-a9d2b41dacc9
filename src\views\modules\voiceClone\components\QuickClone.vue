<template>
    <div class="quick-clone-container">
        <!-- 如果当前是初始状态，显示上传/录制界面 -->
        <template v-if="currentView === 'initial'">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-nav">
                <span class="nav-item" @click="handleBack">声音克隆</span>
                <span class="nav-separator">/</span>
                <span class="nav-item current">快速复刻</span>
            </div>

            <!-- 功能卡片区域 -->
            <div class="function-cards-container">
                <div class="centered-content">
                    <!-- 这里可以添加具体内容 -->
                    <div class="content-item">
                        <!-- 左侧内容区域 -->
                        <div class="card-content">
                            <div class="card-icon">
                                <img src="@/assets/img/Group1.png" alt="上传文件" />
                            </div>
                            <h3 class="card-title">上传音频文件</h3>
                            <p class="card-description">从本地文件中选择音频</p>
                            <p class="card-description sub-description">推荐上传5-30秒高质量无杂音音频文件</p>
                            <button class="card-button" @click="handleUpload">立即上传</button>
                        </div>
                    </div>
                    <div class="content-item">
                        <!-- 右侧内容区域 -->
                        <div class="card-content">
                            <div class="card-icon">
                                <img src="@/assets/img/Group.png" alt="录制音频" />
                            </div>
                            <h3 class="card-title">录制音频</h3>
                            <p class="card-description">根据例句或可由录音进行克隆</p>
                            <button class="card-button" @click="handleRecord">立即录制</button>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 如果当前是结果状态，显示结果页面 -->
        <template v-else-if="currentView === 'result'">
            <CloneResult :clone-data="cloneResultData" @goToHome="handleGoToHome" @goToList="handleGoToList" />
        </template>

        <!-- 录制音频页面 - 使用组件 -->
        <template v-else-if="currentView === 'recording'">
            <RecordingView @back="handleBack" @recordingComplete="handleRecordingComplete" />
        </template>

        <!-- 音频上传弹窗 -->
        <el-dialog v-model="uploadModalVisible" :width="420" :show-close="true" :close-on-click-modal="false"
            class="upload-audio-dialog" align-center>
            <template #header>
                <div class="dialog-header">
                    <!-- 空的头部，只显示背景和关闭按钮 -->
                </div>
            </template>

            <div class="upload-modal-content">
                <!-- 动态音频波形 -->
                <div class="audio-wave-container">
                    <img src="@/assets/img/Group_1321314519.png" alt="音频波形" class="wave-image" />
                </div>

                <!-- 标题文字 -->
                <div class="dialog-title-section">
                    <h3 class="dialog-title">10秒音频，即刻克隆</h3>
                </div>

                <!-- 说明文字 -->
                <div class="upload-description">
                    <el-scrollbar max-height="400px">
                        <p>使用本功能前，您须承诺并保证：<br><br>

素材来源合法性： 素材来源严格遵循现行法律法规、规章制度及主管机关监管要求，符合社会公德与善良风俗，且声音、形象、作品等内容非经非法或不正当途径获得。<br><br>

权利无瑕疵： 您系素材的合法权利人，或已就素材中所有内容（包括但不限于著作权、专利权、商标权、人格权、个人信息权益等）取得充分、有效的授权，不存在任何权利瑕疵或侵权情形。<br><br>

遵守平台规范： 严格遵守配音帮手平台《配音帮手 AI 功能使用规范》等相关协议及规则，确保素材内容不包含扰乱平台秩序、规避技术审核的信息，亦不包含任何违反公序良俗的内容。<br><br>

用途合法正当： 承诺不利用本功能实施任何违法行为。<br><br>

您确认并同意： 上述承诺真实有效。无论违反承诺的行为是否导致实际损失（无论对象）或引发投诉、争议，您均须独立承担由此产生的一切法律后果。<br>
如因此导致配音帮手平台承担任何法律责任或遭受损失（包括但不限于行政处罚、对第三方的赔偿/和解费用、处理投诉/争议的合理诉讼费、律师费、证据保全费等），您应负责全额赔偿配音帮手平台。
                    </p>
                    </el-scrollbar>
                </div>

                <!-- 确认同意单选框 -->
                <div class="agreement-checkbox">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" v-model="agreedToTerms" class="checkbox-input" />
                        <span class="checkbox-mark"></span>
                        <span class="checkbox-text">我确认并同意</span>
                    </label>
                </div>

                <!-- 操作按钮 -->
                <div class="modal-actions">
                    <button class="upload-confirm-btn" @click="handleConfirmUpload"
                        :disabled="!agreedToTerms || isUploading">
                        {{ isUploading ? '正在上传...' : '去克隆' }}
                    </button>
                </div>
            </div>
        </el-dialog>

        <!-- 生成进度弹窗 -->
        <el-dialog v-model="progressDialogVisible" :width="420" :show-close="true" :close-on-click-modal="false"
            class="progress-dialog" align-center>
            <template #header>
                <div class="dialog-header">
                    <!-- 空的头部，只显示背景和关闭按钮 -->
                </div>
            </template>

            <div class="progress-modal-content">
                <!-- 动态音频波形 -->
                <div class="progress-wave-container">
                    <div class="dynamic-wave">
                        <div class="wave-bar" style="animation-delay: 0s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.2s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.4s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.6s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.8s;"></div>
                        <div class="wave-bar" style="animation-delay: 1s;"></div>
                    </div>
                </div>

                <!-- 进度信息 -->
                <div class="progress-info-section">
                    <h3 class="progress-title">生成音色中...{{ Math.round(currentProgress) }}%</h3>
                    <p class="progress-description">正在生成你的专属克隆音色</p>
                </div>

                <!-- 取消按钮 -->
                <!-- <div class="progress-actions">
                    <button class="cancel-btn" @click="handleCancelGeneration">
                        取消
                    </button>
                </div> -->
            </div>
        </el-dialog>

        <!-- 隐藏的文件上传输入框 -->
        <input ref="fileInputRef" type="file" accept="audio/*,.mp3,.wav" style="display: none"
            @change="handleFileSelect" />

        <!-- 空间不足对话框 -->
        <AlertDialog v-model:visible="insufficientSpaceDialogVisible" type="warning" title=""
            :sub-title="insufficientSpaceSubTitle" :message="insufficientSpaceMessage" show-cancel-button
            cancel-button-text="暂不购买" confirm-button-text="立即购买" :show-fee-explanation="false"
            :custom-confirm-class="true" @confirm="handleBuySpace" @cancel="handleCancelBuy" />

        <!-- 会员不足对话框 -->
        <AlertDialog v-model:visible="memberInsufficientDialogVisible" type="warning" title=""
            :sub-title="memberInsufficientSubTitle" :message="memberInsufficientMessage" show-cancel-button
            cancel-button-text="我知道了" confirm-button-text="开通会员" :show-fee-explanation="false"
            :custom-confirm-class="true" @confirm="handleBuyMembership" @cancel="handleCancelBuyMembership" />
    </div>
</template>

<script setup>
import { ref, defineEmits, getCurrentInstance, onUnmounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { dubbing, callbackOss } from '@/api/dubbing'
import { cloneVoice } from '@/api/voiceClone'
import { checkUploadPermission } from '@/api/upload'
import AlertDialog from '@/views/components/AlertDialog.vue'
import CloneResult from './CloneResult.vue'
import RecordingView from './RecordingView.vue'
import { useRouter } from 'vue-router'
import { useloginStore } from '@/stores/login'

// 定义事件发射器
const emit = defineEmits(['back', 'upload', 'record', 'upload-success', 'start-recording'])

// 获取当前组件实例和路由器
const { proxy } = getCurrentInstance()
const router = useRouter()
const loginStore = useloginStore() // 添加 loginStore

// 视图状态控制
const currentView = ref('initial') // initial | result | recording
const cloneResultData = ref({}) // 克隆结果数据
const cloneApiResponse = ref(null) // 克隆接口返回的数据

// 弹窗状态管理
const uploadModalVisible = ref(false)
const agreedToTerms = ref(false) // 同意条款状态
const isUploading = ref(false) // 上传状态
const selectedFile = ref(null) // 选中的文件
const modalSource = ref('') // 标记弹窗来源：'upload' 或 'record'

// 生成进度弹窗状态管理
const progressDialogVisible = ref(false) // 进度弹窗显示状态
const currentProgress = ref(0) // 当前进度百分比
const progressTimer = ref(null) // 进度定时器
const isGenerating = ref(false) // 是否正在生成

// 空间不足对话框相关变量
const insufficientSpaceDialogVisible = ref(false)
const insufficientSpaceSubTitle = ref('您的个人空间容量已不足')
const insufficientSpaceMessage = ref('如需使用请购买空间额度！')

// 会员不足对话框相关变量
const memberInsufficientDialogVisible = ref(false)
const memberInsufficientSubTitle = ref('您还未开通会员')
const memberInsufficientMessage = ref('请开通会员使用')

// 文件输入框引用
const fileInputRef = ref(null)

// 获取用户ID的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || ''
    } catch (error) {
        console.error('获取userId失败:', error)
        return ''
    }
}

// 用户登录检查函数
const checkUserLogin = () => {
    const userStorage = localStorage.getItem('user')
    if (!userStorage) return false

    try {
        const userData = JSON.parse(userStorage)
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== ''
    } catch (e) {
        console.error('解析用户数据失败:', e)
        return false
    }
}

// 返回上一页
const handleBack = () => {
    emit('back')
}

// 处理上传音频
const handleUpload = () => {
    console.log('开始上传音频文件')

    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题')
        return
    }

    // 重置复选框状态
    agreedToTerms.value = false

    modalSource.value = 'upload' // 设置弹窗来源为上传
    uploadModalVisible.value = true // 显示上传弹窗
    emit('upload')
}

// 处理录制音频
const handleRecord = () => {
    console.log('开始录制音频')

    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题')
        return
    }

    // 重置复选框状态
    agreedToTerms.value = false

    modalSource.value = 'record' // 设置弹窗来源为录制
    uploadModalVisible.value = true // 显示上传弹窗
    emit('record')
}

// 处理确认上传或录制
const handleConfirmUpload = async () => {
    if (!agreedToTerms.value) {
        ElMessage.warning('请先同意相关条款')
        return
    }

    // 根据弹窗来源执行不同操作
    if (modalSource.value === 'upload') {
        // // 先清空文件输入框的值，确保 change 事件能够触发
        // if (fileInputRef.value) {
        //     fileInputRef.value.value = ''
        // }
        // 触发文件选择
        fileInputRef.value?.click()
    } else if (modalSource.value === 'record') {
        // 关闭弹窗
        uploadModalVisible.value = false

        // 切换到录制页面
        currentView.value = 'recording'

        console.log('跳转到录制音频页面')

        // 通知父组件开始录制流程
        emit('start-recording')
    }
}

// 处理文件选择
const handleFileSelect = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    // console.log('选择的文件:', file.name) // 调试信息

    try {
        // 验证文件名是否包含特殊符号
        // console.log('验证文件名:', file.name, '验证结果:', validateFileName(file.name)) // 调试信息
        // if (!validateFileName(file.name)) {
        //     ElMessage.error('请确保文件名中不含特殊符号，否则将导致上传失败')
        //     event.target.value = ''
        //     return
        // }

        // 检查文件类型
        const validAudioTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3']
        const validExtensions = ['.mp3', '.wav']
        const fileExtension = file.name.toLowerCase()
        const isValidType = validAudioTypes.includes(file.type) ||
            validExtensions.some(ext => fileExtension.endsWith(ext))

        if (!isValidType) {
            ElMessage.error('请上传有效的音频文件（MP3, WAV）')
            event.target.value = ''
            // selectedFile.value = null // 清理选中文件状态
            return
        }

        // 检查文件大小（最大20MB）
        const maxSize = 20 * 1024 * 1024 // 20MB
        if (file.size > maxSize) {
            ElMessage.error('文件大小不能超过20MB')
            event.target.value = ''
            // selectedFile.value = null // 清理选中文件状态
            return
        }

        // 检查音频时长
        // try {
        //     const durationInfo = await getAudioDuration(file)
        //     const durationInSeconds = durationInfo.durationInSeconds

        //     if (durationInSeconds > 30) {
        //         ElMessage.error('音频时长不能超过30秒')
        //         event.target.value = ''
        //         return
        //     }

        //     console.log('音频时长合格:', durationInSeconds, '秒')
        // } catch (durationError) {
        //     console.error('获取音频时长失败:', durationError)
        //     ElMessage.error('无法获取音频时长，请确保上传有效的音频文件')
        //     event.target.value = ''
        //     return
        // }

        selectedFile.value = file

        // 显示全局 loading
        const loadingInstance = ElLoading.service({
            lock: true,
            text: '权限检查中...',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
            // 注释掉会员级别检查，移除对非会员用户的限制
            const memberLevel = loginStore?.memberInfo?.level?.level || 0
            if (memberLevel === 0) {
                // 关闭 loading
                loadingInstance.close()
                // // 清理文件输入状态
                // event.target.value = ''
                // selectedFile.value = null
                // 显示会员不足对话框
                memberInsufficientDialogVisible.value = true
                return
            }

            // 空间权限检查
            const fileSizeMB = Math.ceil(file.size / 1024 / 1024) // 转换为MB并向上取整
            const userId = getUserId()
            const response = await checkUploadPermission({
                userId: userId,
                feat: "空间",
                need: fileSizeMB
            })

            // 关闭 loading
            loadingInstance.close()

            // 检查返回结果
            if (response && response.content && response.content.result === false) {
                // // 清理文件输入状态
                // event.target.value = ''
                // selectedFile.value = null
                // 显示空间不足对话框
                insufficientSpaceDialogVisible.value = true
                return
            }

            // 开始上传
            await performUpload(file)

        } catch (error) {
            loadingInstance.close()
            console.error('权限检查失败:', error)
            ElMessage.error('权限检查失败，请稍后重试')
            event.target.value = ''
            // selectedFile.value = null // 清理选中文件状态
        }

    } catch (error) {
        console.error('文件处理失败:', error)
        ElMessage.error('文件处理失败: ' + error.message)
        event.target.value = ''
        // selectedFile.value = null // 清理选中文件状态
    }
}

// 验证文件名是否包含特殊符号
// const validateFileName = (fileName) => {
//     // 只检测真正不允许的特殊符号，允许中文、逗号、点号、短横线、下划线
//     const specialCharsRegex = /[\s!@#$%^&*()+=\[\]{}|\\:";'<>?/`~]/
//     return !specialCharsRegex.test(fileName)
// }

// 获取音频时长的辅助函数
const getAudioDuration = (file) => {
    return new Promise((resolve, reject) => {
        const audio = document.createElement('audio')
        const url = URL.createObjectURL(file)

        audio.addEventListener('loadedmetadata', () => {
            const durationInSeconds = Math.round(audio.duration)
            const minutes = Math.floor(durationInSeconds / 60)
            const seconds = durationInSeconds % 60
            const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`

            URL.revokeObjectURL(url) // 清理URL对象
            resolve({
                durationInSeconds,
                formattedDuration
            })
        })

        audio.addEventListener('error', (error) => {
            URL.revokeObjectURL(url) // 清理URL对象
            reject(error)
        })

        audio.src = url
    })
}

// 开始生成进度与克隆请求
const startProgressSimulation = async (audioUrl, sourceType) => {
    // 显示进度对话框，初始进度设为1%
    isGenerating.value = true
    currentProgress.value = 1
    progressDialogVisible.value = true

    // 标记API是否已完成
    let apiCompleted = false

    // 开始模拟进度增长，最高到90%
    progressTimer.value = setInterval(() => {
        // 如果API已完成，清除定时器
        if (apiCompleted) {
            clearInterval(progressTimer.value)
            progressTimer.value = null
            return
        }

        // 根据当前进度计算增长速率（进度越高，增长越慢）
        let increment = 0
        if (currentProgress.value < 30) {
            increment = 1  // 初始阶段快速增长
        } else if (currentProgress.value < 60) {
            increment = 0.7  // 中期减缓
        } else if (currentProgress.value < 85) {
            increment = 0.5  // 后期更慢
        } else {
            increment = 0.2  // 接近90%时极慢
        }

        // 增加进度，但不超过90%
        currentProgress.value = Math.min(90, currentProgress.value + increment)
    }, 200)  // 每200ms更新一次

    try {
        // 异步调用克隆接口
        console.log('开始调用克隆接口，音频URL:', audioUrl, '类型:', sourceType)
        const response = await cloneVoice({
            userId: getUserId(),
            audioUrl,
            cloneType: 1  // 固定传1
        })
        console.log('克隆请求成功:', response)
        // 
        if (response.status_code == 400) {
            ElMessage.error(response.content.error)
            // 清除定时器
            if (progressTimer.value) {
                clearInterval(progressTimer.value)
                progressTimer.value = null
            }

            // 出错时关闭进度弹窗
            progressDialogVisible.value = false
            isGenerating.value = false
            currentProgress.value = 0
            return
        }
        // 保存克隆接口返回的数据
        cloneApiResponse.value = response

        // 标记API已完成
        apiCompleted = true

        // 清除定时器
        if (progressTimer.value) {
            clearInterval(progressTimer.value)
            progressTimer.value = null
        }

        // 克隆请求成功，将进度直接设为100%
        currentProgress.value = 100

        // 延迟关闭弹窗，让用户看到100%完成状态
        setTimeout(() => {
            handleGenerationComplete()
        }, 800)

        return response
    } catch (error) {
        // console.error('克隆请求失败:', error)
        // ElMessage.error('音色克隆请求失败')

        // 标记API已完成
        apiCompleted = true

        // 清除定时器
        if (progressTimer.value) {
            clearInterval(progressTimer.value)
            progressTimer.value = null
        }

        // 出错时关闭进度弹窗
        progressDialogVisible.value = false
        isGenerating.value = false
        currentProgress.value = 0

        throw error
    }
}

// 处理生成完成
const handleGenerationComplete = () => {
    progressDialogVisible.value = false
    isGenerating.value = false
    currentProgress.value = 0

    ElMessage.success('音色克隆完成！')
    console.log('音色克隆生成完成')

    // 准备克隆结果数据
    cloneResultData.value = {
        // 设置克隆结果的相关数据，如音色ID、名称等
        // 使用克隆接口返回的真实ID
        id: cloneApiResponse.value?.id || cloneApiResponse.value?.data?.id || new Date().getTime(),
        cloneId: cloneApiResponse.value?.id || cloneApiResponse.value?.data?.id,
        name: selectedFile.value ? selectedFile.value.name.split('.')[0] : '新音色',
        date: new Date().toLocaleString(),
        audioUrl: cloneApiResponse.value?.audioUrl || cloneApiResponse.value?.data?.audioUrl,
        // 其他需要传递的数据
        response: cloneApiResponse.value // 保存完整的接口响应
    }

    // 切换到结果视图
    currentView.value = 'result'
}

// 处理取消生成
const handleCancelGeneration = () => {
    // 清除定时器
    if (progressTimer.value) {
        clearInterval(progressTimer.value)
        progressTimer.value = null
    }

    // 关闭弹窗并重置状态
    progressDialogVisible.value = false
    isGenerating.value = false
    currentProgress.value = 0

    ElMessage.info('已取消音色生成')
    console.log('用户取消音色生成')
}

// 执行上传操作
const performUpload = async (file) => {
    isUploading.value = true

    try {
        // 获取文件扩展名确定文件类型
        const fileExtension = file.name.split('.').pop().toLowerCase()
        const fileType = fileExtension === 'mp3' ? 'mp3' : 'wav'

        // 调用 dubbing API 获取 OSS 上传凭证
        const response = await dubbing({ userId: getUserId(), fileType })

        // 去掉文件名的后缀
        const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.')

        // 准备上传表单数据
        const formData = new FormData()
        formData.append('OSSAccessKeyId', response.accessKeyId)
        formData.append('policy', response.policy)
        formData.append('signature', response.signature)
        formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`)
        formData.append('file', file)

        // 使用 XHR 上传文件
        const xhr = new XMLHttpRequest()

        // 上传完成处理
        xhr.onload = async () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    // 构建文件URL
                    const fileUrl = `${response.host}/${response.key.replace(/[^\/]+$/, '')}${file.name}`

                    // 获取音频时长
                    let durationInSeconds = 0
                    try {
                        const durationInfo = await getAudioDuration(file)
                        durationInSeconds = durationInfo.durationInSeconds
                        console.log('成功获取音频时长:', durationInSeconds, '秒')
                    } catch (durationError) {
                        console.error('获取音频时长失败，使用默认值0:', durationError)
                    }

                    // 调用 callbackOss 接口
                    const callbackResponse = await callbackOss({
                        userId: getUserId(),
                        materialName: fileNameWithoutExt,
                        ossPath: response.key.replace(/[^\/]+$/, '') + file.name,
                        fileSize: String(file.size),
                        fileExtension: fileExtension,
                        tagNames: '1',
                        materialType: 'audio',
                        isPrivate: '1',
                        storage_path: `/material/${getUserId()}/${file.name}`,
                        duration: durationInSeconds,
                        temporary: '1',
                        music_classification: '',
                    })

                    // ElMessage.success('音频上传成功！')
                    console.log('上传成功，文件URL:', fileUrl)
                    console.log('Callback响应:', callbackResponse)

                    // 关闭上传弹窗
                    uploadModalVisible.value = false

                    // 重置上传状态
                    selectedFile.value = null
                    agreedToTerms.value = false
                    isUploading.value = false

                    // 开始显示生成进度弹窗并调用克隆接口
                    await startProgressSimulation(callbackResponse.url || fileUrl, 'upload')

                    // 触发自定义事件，将文件信息传递给父组件
                    emit('upload-success', {
                        file: file,
                        url: fileUrl,
                        name: callbackResponse.filename || file.name,
                        size: file.size,
                        type: file.type,
                        duration: durationInSeconds,
                        callbackResponse: callbackResponse
                    })

                } catch (callbackError) {
                    // console.log(callbackError,'callbackErrorcallbackErrorcallbackError')
                    // console.error('Callback接口调用失败:', callbackError)
                    // ElMessage.error('文件上传成功，但保存失败')
                    isUploading.value = false
                }
            } else {
                throw new Error(xhr.statusText || '上传失败')
            }
        }

        // 上传错误处理
        xhr.onerror = (error) => {
            console.error('上传错误:', error)
            ElMessage.error('文件上传失败')
            isUploading.value = false
        }

        // 发送上传请求
        xhr.open('POST', response.host, true)
        xhr.send(formData)

        // 清空文件输入
        if (fileInputRef.value) {
            fileInputRef.value.value = ''
        }

    } catch (error) {
        console.error('上传失败:', error)
        ElMessage.error('上传失败: ' + error.message)
        isUploading.value = false

        // 清空文件输入
        if (fileInputRef.value) {
            fileInputRef.value.value = ''
        }
    }
}

// 处理购买空间
const handleBuySpace = () => {
    insufficientSpaceDialogVisible.value = false
    // 在当前页面打开购买空间页面
    router.push({ name: 'membership', query: { nav: 'space' } })
}

// 处理取消购买
const handleCancelBuy = () => {
    insufficientSpaceDialogVisible.value = false
}

// 处理购买会员
const handleBuyMembership = () => {
    memberInsufficientDialogVisible.value = false
    // 在当前页面打开会员页面
    router.push({ name: 'membership' })
}

// 处理取消购买会员
const handleCancelBuyMembership = () => {
    memberInsufficientDialogVisible.value = false
}

// 处理返回首页
const handleGoToHome = () => {
    console.log('返回首页，通知父组件切换到主页面')
    // 通过emit向上传递，让index.vue处理返回首页
    emit('back')
}

// 处理返回列表
const handleGoToList = () => {
    console.log('返回列表，通知父组件切换到主页面')
    // 通过emit向上传递，让index.vue处理返回首页（列表就在首页）
    emit('back')
}

// 处理录制完成事件
const handleRecordingComplete = async (recordingData) => {
    console.log('接收到录制完成事件:', recordingData)

    // 如果录制已经完成了生成流程，直接跳转到结果页面
    if (recordingData.generationComplete) {
        // 准备克隆结果数据
        cloneResultData.value = {
            id: recordingData.voiceId || new Date().getTime(),
            name: recordingData.voiceName || `录音音色_${new Date().toLocaleString()}`,
            date: new Date().toLocaleString(),
            audioUrl: recordingData.audioUrl,
            duration: recordingData.duration,
            source: 'recording', // 标记来源为录制
            // 其他需要传递的数据
        }

        // 切换到结果视图
        currentView.value = 'result'

        console.log('已切换到结果页面，克隆数据:', cloneResultData.value)

        // 不需要再次调用startProgressSimulation，因为RecordingView.vue中已经调用过了
    } else {
        // 如果还没有完成生成，可以在这里处理其他逻辑
        console.log('录制完成，但音色生成尚未完成')
    }
}

// 组件卸载时清理资源
onUnmounted(() => {
    // 清除定时器
    if (progressTimer.value) {
        clearInterval(progressTimer.value)
        progressTimer.value = null
    }
})
</script>

<style scoped lang="scss">
.quick-clone-container {
    width: 1602px;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
    display: flex;
    align-items: center;
    height: 54px;
    font-size: 14px;
    // background-color: #FAFAFA;
    padding: 0 20px 0;
}

.nav-item {
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
        color: #409EFF;
    }

    &.current {
        color: #333;
        font-weight: 500;
        cursor: default;

        &:hover {
            color: #333;
        }
    }
}

.nav-separator {
    margin: 0 8px;
    color: #999;
}

/* 功能卡片容器 */
.function-cards-container {
    background-color: #FFFFFF;
    width: 1554px;
    min-height: 500px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 居中内容区域 */
.centered-content {
    width: 1262px;
    height: 351px;
    background-color: #FFFFFF;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    padding: 0;
    box-sizing: border-box;
}

/* 内容项样式 */
.content-item {
    width: 615px;
    height: 351px;
    background-color: #FFFFFF;
    border: 1px dotted #D3D3D2;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 卡片内容样式 */
.card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 40px 20px;
}

.card-icon {
    margin-bottom: 24px;

    img {
        width: 64px;
        height: 64px;
        object-fit: contain;
    }
}

.card-title {
    font-size: 18px;
    font-weight: 500;
    color: #000000;
    margin: 0 0 3px 0;
    line-height: 1.4;
}

.card-description {
    font-size: 14px;
    color: #5A5A5A;
    margin: 0 0 29px 0;
    line-height: 1.5;
    max-width: 280px;
}

.sub-description {
    margin-top: -30px; /* 调整间距，使其更靠近上一行描述 */
    margin-bottom: 16px; /* 保持与按钮的间距 */
    font-size: 12px; /* 可以稍微缩小字体以示区别 */
}

.card-button {
    width: 127px;
    height: 30px;
    background-color: #0AAF60;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-top: -10px; /* 根据用户需求向上移动按钮 */

    &:hover {
        background-color: #1BC268;
        transform: translateY(-1px);
    }

    &:active {
        transform: translateY(0);
    }
}

/* 音频上传弹窗样式 */
:deep(.upload-audio-dialog) {
    .el-dialog {
        border-radius: 8px;
        overflow: hidden;
    }

    .el-dialog__header {
        height: 10px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .el-dialog__body {
        padding: 16px 0 20px;
        overflow: hidden;
    }

    .el-dialog__headerbtn {
        top: 5%;
        right: 16px;
        width: 16px;
        height: 16px;
        transform: translateY(-50%);

        .el-dialog__close {
            font-size: 16px;
            color: #909399;
        }
    }
}

.dialog-header {
    width: 100%;
    height: 100%;
    /* 空的头部内容 */
}

.upload-modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    max-height: none;
    overflow: visible;
}

/* 音频波形容器 */
.audio-wave-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 297px;
    height: 51px;
}

.wave-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 标题区域样式 */
.dialog-title-section {
    text-align: center;
    margin: 4px 0;

    .dialog-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }
}

/* 说明文字样式 */
.upload-description {
    text-align: left;
    width: 100%;
    box-sizing: border-box;
    border-radius: 4px;

    p {
        font-size: 12px;
        color: #606266;
        line-height: 2.2;
        margin: 0;
        padding: 8px;
    }
}

/* 确认同意单选框样式 */
.agreement-checkbox {
    margin-top: 5px;
    width: 100%;
    display: flex;
    align-items: center;
    user-select: none;
    /* 禁用文字选择 */
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    /* 禁用文字选择 */
}

.checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-mark {
    position: relative;
    width: 14px;
    height: 14px;
    border: 1px solid #DCDFE6;
    border-radius: 2px;
    background-color: #FFFFFF;
    margin-right: 8px;
    transition: all 0.2s ease;

    /* 选中状态的对勾 */
    &::after {
        content: '';
        position: absolute;
        display: none;
        left: 3px;
        top: 0px;
        width: 4px;
        height: 8px;
        border: solid #FFFFFF;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
}

.checkbox-input:checked+.checkbox-mark {
    background-color: #0AAF60;
    border-color: #0AAF60;

    &::after {
        display: block;
    }
}

.checkbox-input:hover+.checkbox-mark {
    border-color: #0AAF60;
}

.checkbox-text {
    font-size: 12px;
    color: #606266;
    // line-height: 2.2;
    user-select: none;
    /* 禁用文字选择 */
}

/* 操作按钮样式 */
.modal-actions {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}

.upload-confirm-btn {
    width: 390px;
    height: 40px;
    background-color: #0AAF60;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) {
        background-color: #1BC268;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(10, 175, 96, 0.3);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
    }

    &:disabled {
        background-color: #C0C4CC;
        color: #909399;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
}

/* 生成进度弹窗样式 */
:deep(.progress-dialog) {
    .el-dialog {
        border-radius: 8px;
        overflow: hidden;
    }

    .el-dialog__header {
        height: 10px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .el-dialog__body {
        padding: 16px 0 5px;
        overflow: hidden;
    }

    .el-dialog__headerbtn {
        top: 5%;
        right: 16px;
        width: 16px;
        height: 16px;
        transform: translateY(-50%);

        .el-dialog__close {
            font-size: 16px;
            color: #909399;
        }
    }
}

.progress-modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    max-height: none;
    overflow: visible;
}

/* 进度波形容器 */
.progress-wave-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68px;
    height: 40px;
}

.dynamic-wave {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.wave-bar {
    width: 6px;
    border-radius: 1px;
    animation: waveAnimation 1.2s ease-in-out infinite;
    transform-origin: bottom;
}

/* 动态波形动画 */
@keyframes waveAnimation {

    0%,
    100% {
        height: 10%;
        opacity: 0.6;
        background-color: #80F1AD;
    }

    25% {
        height: 60%;
        opacity: 0.8;
        background-color: #0AAF60;
    }

    50% {
        height: 100%;
        opacity: 1;
        background-color: #0AAF60;
    }

    75% {
        height: 40%;
        opacity: 0.9;
        background-color: #80F1AD;
    }
}

/* 进度信息区域样式 */
.progress-info-section {
    text-align: center;
    margin: 4px 0;
    height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .progress-title {
        font-size: 16px;
        font-weight: 500;
        color: #000000;
        margin: 0 0 4px 0;
        line-height: 1.2;
    }

    .progress-description {
        font-size: 12px;
        color: #606266;
        font-weight: 400;
        margin: 0;
        line-height: 1.2;
    }
}

/* 进度操作按钮样式 */
.progress-actions {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}

.cancel-btn {
    width: 96px;
    height: 40px;
    background-color: #0AAF60;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #1BC268;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(10, 175, 96, 0.3);
    }

    &:active {
        transform: translateY(0);
    }
}
</style>