<template>
    <div class="recording-view">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-nav">
            <span class="nav-item" @click="handleBack">声音克隆</span>
            <span class="nav-separator">/</span>
            <span class="nav-item current">快速复刻</span>
        </div>

        <!-- 空白内容区域 -->
        <div class="recording-container">
            <div class="recording-content">
                <!-- 麦克风权限提示 - 只在有错误时显示 -->
                <div v-if="micPermissionError" class="mic-permission-alert error">
                    {{ micPermissionMessage }}
                </div>
                <!-- 添加新的div -->
                <div class="recording-box">
                    <div class="reading-title">请朗读以下例句</div>
                    <div class="example-sentence">{{ currentSentence }}</div>
                    <!-- 添加更换文案按钮 -->
                    <div class="change-text-btn" @click="changeSentence">
                        <img src="@/assets/img/Switch_(切换).png" alt="切换" class="switch-icon">
                        <span>更换文案</span>
                    </div>
                </div>

                <!-- 录制说明文字 -->
                <div class="recording-instruction">
                    {{ isRecording ? '录制时的语气情感也会被克隆，请按照期望的效果进行录音，你也可以自由录制任意内容，或按照例句进行录制，请控制录制时长在5秒以上。' :
                    '请确保周围环境安静以录制高品质音频，录制5-30秒清晰音频即可。' }}
                </div>

                <!-- 开始录制按钮 - 未录制时显示 -->
                <button v-if="!isRecording && !recordedAudio" class="start-recording-btn" @click="startRecording"
                    :disabled="!audioStream">
                    <span v-if="audioStream">开始录制</span>
                    <span v-else>检测麦克风并开始录制</span>
                </button>

                <!-- 录制状态显示 - 录制时显示 -->
                <div v-if="isRecording" class="recording-status">
                    <div class="recording-control">
                        <div class="pause-button" @click="pauseRecording">
                            <img src="@/assets/img/Pause-one (暂停).png" alt="暂停" class="pause-icon">
                        </div>
                        <div class="recording-time">{{ formattedTime }}</div>
                        <div class="recording-limit">
                            还可录制 {{ remainingTime }} 秒
                        </div>
                    </div>

                    <!-- 录制中的波形动画 -->
                    <div class="waveform-container">
                        <div class="dynamic-wave">
                            <div class="wave-bar" style="animation-delay: 0s;"></div>
                            <div class="wave-bar" style="animation-delay: 0.2s;"></div>
                            <div class="wave-bar" style="animation-delay: 0.4s;"></div>
                            <div class="wave-bar" style="animation-delay: 0.6s;"></div>
                            <div class="wave-bar" style="animation-delay: 0.8s;"></div>
                            <div class="wave-bar" style="animation-delay: 1s;"></div>
                            <div class="wave-bar" style="animation-delay: 1.2s;"></div>
                            <div class="wave-bar" style="animation-delay: 1.4s;"></div>
                            <div class="wave-bar" style="animation-delay: 1.6s;"></div>
                            <div class="wave-bar" style="animation-delay: 1.8s;"></div>
                        </div>
                    </div>
                </div>

                <!-- 音频预览区域 - 录制完成后显示 -->
                <div v-if="recordedAudio" class="audio-preview">
                    <!-- 隐藏的原生audio元素，用于音频控制 -->
                    <audio ref="audioElement" :src="recordedAudio" @loadedmetadata="onAudioLoaded"
                        @loadeddata="onAudioDataLoaded" @canplay="onAudioCanPlay" @timeupdate="onTimeUpdate"
                        @ended="onAudioEnded" @durationchange="onDurationChange" preload="metadata">
                    </audio>

                    <!-- 自定义音频播放器 -->
                    <div class="custom-audio-player">
                        <!-- 播放/暂停按钮 -->
                        <div class="play-pause-btn" @click="togglePlayPause">
                            <svg v-if="!isPlaying" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M8 5V19L19 12L8 5Z" fill="#27AE60" />
                            </svg>
                            <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M6 4H10V20H6V4ZM14 4H18V20H14V4Z" fill="#27AE60" />
                            </svg>
                        </div>

                        <!-- 进度条容器 -->
                        <div class="progress-container" @click="seekTo">
                            <div class="progress-track">
                                <div class="progress-bar" :style="{ width: progressPercentage + '%' }"></div>
                                <div class="progress-thumb" :style="{ left: progressPercentage + '%' }"></div>
                            </div>
                        </div>

                        <!-- 时间显示 -->
                        <div class="time-display">
                            {{ formatTime(currentTime) }} / {{ formatTime(audioDuration) }}
                        </div>
                    </div>

                    <!-- 录音时长错误提示 -->
                    <div v-if="recordingTooShortError" class="recording-error">
                        录音时长过短，请至少录制 {{ MIN_RECORDING_TIME }} 秒
                    </div>

                    <div class="audio-controls">
                        <button class="retry-btn" @click="discardRecording">重新录制</button>
                        <button class="use-btn" @click="useRecording" :disabled="recordingTooShortError">
                            使用该录音
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 生成进度弹窗 -->
        <el-dialog v-model="progressDialogVisible" :width="420" :show-close="true" :close-on-click-modal="false"
            class="progress-dialog" align-center>
            <template #header>
                <div class="dialog-header">
                    <!-- 空的头部，只显示背景和关闭按钮 -->
                </div>
            </template>

            <div class="progress-modal-content">
                <!-- 动态音频波形 -->
                <div class="progress-wave-container">
                    <div class="dynamic-wave">
                        <div class="wave-bar" style="animation-delay: 0s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.2s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.4s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.6s;"></div>
                        <div class="wave-bar" style="animation-delay: 0.8s;"></div>
                        <div class="wave-bar" style="animation-delay: 1s;"></div>
                    </div>
                </div>

                <!-- 进度信息 -->
                <div class="progress-info-section">
                    <h3 class="progress-title">生成音色中...{{ Math.round(currentProgress) }}%</h3>
                    <p class="progress-description">正在生成你的专属克隆音色</p>
                </div>

                <!-- 取消按钮 -->
                <!-- <div class="progress-actions">
                    <button class="cancel-btn" @click="handleCancelGeneration">
                        取消
                    </button>
                </div> -->
            </div>
        </el-dialog>

        <!-- 会员提示弹窗 -->
        <AlertDialog v-model:visible="membershipDialogVisible" type="warning" title="会员提示" message="您还未开通会员，请开通会员使用"
            confirm-button-text="开通会员" cancel-button-text="我知道了" :show-cancel-button="true" :custom-confirm-class="true"
            :custom-cancel-class="true" :show-fee-explanation="false" @confirm="handleOpenMembership"
            @cancel="closeMembershipDialog" />
    </div>
</template>

<script setup>
import { defineEmits, ref, computed, onBeforeUnmount, onMounted, watch, nextTick } from 'vue'
import { ElDialog, ElMessage } from 'element-plus'
import { dubbing, callbackOss } from '@/api/dubbing'
import { cloneVoice } from '@/api/voiceClone'
import { useRouter } from 'vue-router'
import { useloginStore } from '@/stores/login'
import AlertDialog from '@/views/components/AlertDialog.vue'

// 定义事件发射器
const emit = defineEmits(['back', 'recordingComplete'])

// 添加路由实例
const router = useRouter()
const loginStore = useloginStore()

// 例句数组
const sentences = [
    "猫头鹰的耳朵是不对称的，可以让它们在捕猎时能更精确的定位猎物的位置，这种独特的听觉结构是它们在夜间捕猎成功的关键。",
    "蜂鸟是唯一能够倒退飞行的鸟类，它们的翅膀每秒可以拍打约80次，让它们能够在空中保持静止，还能进行精确的悬停和快速的飞行方向变化。",
    "北极熊的皮毛实际上是无色透明的，我们看到的白色是因为它们的毛发能够反射阳光，而皮肤则是黑色的，有助于吸收热量保持体温。",
    "章鱼有三颗心脏，两颗负责将血液泵送到鳃部，另一颗则负责将血液泵送到身体其他部位，这使它们能够适应深海环境中的低氧条件。",
    "树懒是世界上移动最慢的哺乳动物之一，它们大部分时间都倒挂在树上，每天睡眠时间长达15到20小时，即使是醒着的时候也几乎不动。"
]

// 当前显示的例句
const currentSentence = ref(sentences[0])

// 更换例句的函数
const changeSentence = () => {
    // 筛选出不是当前显示的例句
    const otherSentences = sentences.filter(sentence => sentence !== currentSentence.value)
    // 从剩余例句中随机选择一个
    const randomIndex = Math.floor(Math.random() * otherSentences.length)
    // 更新当前显示的例句
    currentSentence.value = otherSentences[randomIndex]
}

// 返回上一页
const handleBack = () => {
    emit('back')
}

// 录制状态控制
const isRecording = ref(false)
const recordingTime = ref(0)
const recordingTimer = ref(null)
const micPermissionMessage = ref('请确保麦克风使用权限已打开！')
const micPermissionError = ref(false)
const recordingTooShortError = ref(false)

// 录音时长限制（秒）
const MIN_RECORDING_TIME = 5
const MAX_RECORDING_TIME = 30

// 录音相关变量
const mediaRecorder = ref(null)
const audioChunks = ref([])
const recordedAudio = ref(null)
const recordedAudioBlob = ref(null) // 新增：保存原始的Blob对象
const recordedAudioUrl = ref(null) // 新增：保存服务器返回的HTTP URL
const audioStream = ref(null)
const isUploading = ref(false) // 新增：上传状态
const cloneApiResponse = ref(null) // 克隆接口返回的数据

// 音频播放器相关变量
const audioElement = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const audioDuration = ref(0)
const animationFrameId = ref(null)

// 格式化时间显示
const formattedTime = computed(() => {
    const minutes = Math.floor(recordingTime.value / 60)
    const seconds = recordingTime.value % 60
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 剩余时间显示
const remainingTime = computed(() => {
    const remaining = MAX_RECORDING_TIME - recordingTime.value
    return remaining > 0 ? remaining : 0
})

// 进度百分比
const progressPercentage = computed(() => {
    if (audioDuration.value === 0) return 0
    return Math.min(100, Math.max(0, (currentTime.value / audioDuration.value) * 100))
})

// 音频播放器方法
const togglePlayPause = () => {
    if (!audioElement.value) return

    if (isPlaying.value) {
        audioElement.value.pause()
        stopProgressUpdate()
        isPlaying.value = false
    } else {
        audioElement.value.play().then(() => {
            isPlaying.value = true
            startProgressUpdate()
        }).catch(error => {
            console.error('播放失败:', error)
        })
    }
}

// 开始进度更新动画
const startProgressUpdate = () => {
    const updateProgress = () => {
        if (audioElement.value && isPlaying.value) {
            const newTime = audioElement.value.currentTime
            currentTime.value = newTime
            animationFrameId.value = requestAnimationFrame(updateProgress)
        }
    }
    if (isPlaying.value) {
        updateProgress()
    }
}

// 停止进度更新动画
const stopProgressUpdate = () => {
    if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value)
        animationFrameId.value = null
    }
}

const seekTo = (event) => {
    if (!audioElement.value || audioDuration.value === 0) return

    const rect = event.currentTarget.getBoundingClientRect()
    const percent = (event.clientX - rect.left) / rect.width
    const seekTime = percent * audioDuration.value
    audioElement.value.currentTime = seekTime
    currentTime.value = seekTime
}

const onAudioLoaded = () => {
    if (audioElement.value && audioElement.value.duration) {
        const duration = audioElement.value.duration
        if (!isNaN(duration) && isFinite(duration)) {
            audioDuration.value = duration
        }
    }
}

const onAudioDataLoaded = () => {
    if (audioElement.value && audioElement.value.duration) {
        const duration = audioElement.value.duration
        if (!isNaN(duration) && isFinite(duration)) {
            audioDuration.value = duration
        }
    }
}

const onAudioCanPlay = () => {
    if (audioElement.value && audioElement.value.duration) {
        const duration = audioElement.value.duration
        if (!isNaN(duration) && isFinite(duration)) {
            audioDuration.value = duration
        } else {
            // 如果duration无效，使用录制时长作为后备
            audioDuration.value = recordingTime.value
        }
    } else {
        audioDuration.value = recordingTime.value
    }
}

const onDurationChange = () => {
    if (audioElement.value && audioElement.value.duration) {
        const duration = audioElement.value.duration
        if (!isNaN(duration) && isFinite(duration)) {
            audioDuration.value = duration
        }
    }
}

const onTimeUpdate = () => {
    // 恢复正常的timeupdate处理，作为requestAnimationFrame的备份
    if (audioElement.value) {
        currentTime.value = audioElement.value.currentTime
    }
}

const onAudioEnded = () => {
    isPlaying.value = false
    currentTime.value = 0
    stopProgressUpdate()
    if (audioElement.value) {
        audioElement.value.currentTime = 0
    }
}

const formatTime = (time) => {
    if (!time || isNaN(time)) return '00:00'
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 请求麦克风权限
const requestMicrophonePermission = async () => {
    try {
        // 请求音频权限
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        })

        audioStream.value = stream
        micPermissionError.value = false
        micPermissionMessage.value = '麦克风权限已获取！'

        return true
    } catch (error) {
        console.error('获取麦克风权限失败:', error)
        micPermissionError.value = true

        // 根据错误类型提供更具体的错误信息
        if (error.name === 'NotFoundError' || error.message.includes('Requested device not found')) {
            micPermissionMessage.value = '未检测到麦克风设备，请确认已连接麦克风！'
        } else if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            micPermissionMessage.value = '麦克风访问被拒绝，请在浏览器设置中允许访问麦克风！'
        } else if (error.name === 'AbortError') {
            micPermissionMessage.value = '硬件或权限问题导致麦克风访问被中断！'
        } else {
            micPermissionMessage.value = '获取麦克风权限失败，请检查设备连接！'
        }

        return false
    }
}

// 初始化录音
onMounted(async () => {
    // 页面加载时不强制检查麦克风权限，避免在页面加载时就显示错误
    // 只是尝试获取权限，但不阻止用户看到界面
    try {
        await requestMicrophonePermission()
    } catch (error) {
        console.error('初始化麦克风权限检查失败:', error)
        // 出错时不阻止界面显示
    }
})

// 添加会员相关状态管理
const membershipDialogVisible = ref(false) // 会员提示弹窗显示状态

// 会员状态检查函数 - 使用 loginStore
const checkMembershipStatus = () => {
    // 检查会员级别，只有 level === 0 时才需要弹窗
    const memberLevel = loginStore?.memberInfo?.level?.level || 0
    return memberLevel === 0 // 返回 true 表示需要弹窗（非会员）
}

// 处理开通会员跳转
const handleOpenMembership = () => {
    membershipDialogVisible.value = false

    // 在当前页面打开会员页面
    router.push({ name: 'membership' })
}

// 关闭会员提示弹窗
const closeMembershipDialog = () => {
    membershipDialogVisible.value = false
}

// 开始录制
const startRecording = async () => {
    // 首先检查会员状态 - 只有非会员（level === 0）才弹窗
    const shouldShowMembershipDialog = checkMembershipStatus()
    if (shouldShowMembershipDialog) {
        membershipDialogVisible.value = true
        return // 如果是非会员，显示提示弹窗并返回，不执行后续录制逻辑
    }

    // 重置录音时长错误提示
    recordingTooShortError.value = false

    // 确保已经获取到麦克风权限
    if (!audioStream.value) {
        const permissionGranted = await requestMicrophonePermission()
        if (!permissionGranted) {
            // 权限获取失败，显示更友好的提示（可以考虑使用toast或对话框）
            console.error('无法开始录音: ' + micPermissionMessage.value)
            return
        }
    }

    try {
        // 初始化录音
        audioChunks.value = []
        recordedAudio.value = null

        // 检查浏览器支持的音频格式
        const mimeType = MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' :
            MediaRecorder.isTypeSupported('audio/mp4') ? 'audio/mp4' :
                MediaRecorder.isTypeSupported('audio/wav') ? 'audio/wav' : 'audio/webm'

        // 创建MediaRecorder实例，使用兼容的格式
        mediaRecorder.value = new MediaRecorder(audioStream.value, {
            mimeType: mimeType
        })

        // 收集录音数据
        mediaRecorder.value.ondataavailable = (event) => {
            if (event.data.size > 0) {
                audioChunks.value.push(event.data)
            }
        }

        // 录音结束时的处理
        mediaRecorder.value.onstop = async () => {
            try {
                // 获取使用的音频格式
                const usedMimeType = mediaRecorder.value.mimeType || mimeType

                // 将录音数据合成为一个音频文件
                const audioBlob = new Blob(audioChunks.value, { type: usedMimeType })

                // 保存原始Blob对象
                recordedAudioBlob.value = audioBlob

                // 创建本地预览用的blob URL
                const audioUrl = URL.createObjectURL(audioBlob)

                // 重置音频播放器状态
                isPlaying.value = false
                currentTime.value = 0
                audioDuration.value = 0

                // 设置本地预览音频URL
                recordedAudio.value = audioUrl

                // TODO: 暂时禁用自动上传，等待后端接口实现
                // 自动上传录音文件获取HTTP URL
                // try {
                //     await uploadRecordingToServer(audioBlob)
                //     ElMessage.success('录音已保存并上传完成')
                // } catch (uploadError) {
                //     // 上传失败不影响本地预览，错误信息已在uploadRecordingToServer中处理
                // }

                ElMessage.success('录音已完成，可以进行预览')

                // 使用更强的等待机制来获取音频时长
                await loadAudioDuration()

                // 检查录音时长是否达到最低要求
                if (recordingTime.value < MIN_RECORDING_TIME) {
                    recordingTooShortError.value = true
                }
            } catch (error) {
                console.error('处理录音数据时出错:', error)
                ElMessage.error('录音处理失败，请重试')
            }
        }

        // 开始录音
        mediaRecorder.value.start()
        isRecording.value = true
        recordingTime.value = 0

        // 开始计时
        recordingTimer.value = setInterval(() => {
            recordingTime.value++

            // 达到最大录制时长时自动停止
            if (recordingTime.value >= MAX_RECORDING_TIME) {
                pauseRecording()
            }
        }, 1000)

    } catch (error) {
        console.error('录音初始化失败:', error)
        micPermissionError.value = true
        micPermissionMessage.value = '录音初始化失败，请刷新页面重试！'
    }
}

// 新增：专门用于加载音频时长的函数
const loadAudioDuration = () => {
    return new Promise((resolve) => {
        const maxAttempts = 10
        let attempts = 0

        const tryLoadDuration = () => {
            attempts++

            if (audioElement.value && audioElement.value.readyState >= 1) {
                // 音频元数据已加载
                const duration = audioElement.value.duration
                if (duration && !isNaN(duration) && isFinite(duration)) {
                    audioDuration.value = duration
                    resolve()
                    return
                }
            }

            if (attempts < maxAttempts) {
                setTimeout(tryLoadDuration, 200)
            } else {
                audioDuration.value = recordingTime.value
                resolve()
            }
        }

        // 立即尝试一次
        tryLoadDuration()
    })
}

// 暂停录制
const pauseRecording = () => {
    if (!mediaRecorder.value || mediaRecorder.value.state === 'inactive') return

    if (recordingTimer.value) {
        clearInterval(recordingTimer.value)
        recordingTimer.value = null
    }

    isRecording.value = false

    // 停止录音
    mediaRecorder.value.stop()
}

// 丢弃当前录音，重新开始
const discardRecording = () => {
    recordedAudio.value = null
    recordedAudioBlob.value = null // 清理Blob对象
    recordedAudioUrl.value = null // 清理HTTP URL
    audioChunks.value = []
    recordingTooShortError.value = false

    // 重置音频播放器状态
    isPlaying.value = false
    currentTime.value = 0
    audioDuration.value = 0
}

// 生成进度弹窗状态管理
const progressDialogVisible = ref(false) // 进度弹窗显示状态
const currentProgress = ref(0) // 当前进度百分比
const progressTimer = ref(null) // 进度定时器
const isGenerating = ref(false) // 是否正在生成

// 开始生成进度与克隆请求
const startProgressSimulation = async (audioUrl) => {
    // 显示进度对话框，初始进度设为1%
    isGenerating.value = true
    currentProgress.value = 1
    progressDialogVisible.value = true

    // 标记API是否已完成
    let apiCompleted = false

    // 开始模拟进度增长，最高到90%
    progressTimer.value = setInterval(() => {
        // 如果API已完成，清除定时器
        if (apiCompleted) {
            clearInterval(progressTimer.value)
            progressTimer.value = null
            return
        }

        // 根据当前进度计算增长速率（进度越高，增长越慢）
        let increment = 0
        if (currentProgress.value < 30) {
            increment = 1  // 初始阶段快速增长
        } else if (currentProgress.value < 60) {
            increment = 0.7  // 中期减缓
        } else if (currentProgress.value < 85) {
            increment = 0.5  // 后期更慢
        } else {
            increment = 0.2  // 接近90%时极慢
        }

        // 增加进度，但不超过90%
        currentProgress.value = Math.min(90, currentProgress.value + increment)
    }, 200)  // 每200ms更新一次

    try {
        // 异步调用克隆接口
        console.log('开始调用克隆接口，音频URL:', audioUrl)
        const response = await cloneVoice({
            userId: getUserId(),
            audioUrl,
            cloneType: 2  // 录制音频固定传2
        })
        console.log('克隆请求成功:', response)
        // 
        if (response.status_code == 400) {
            ElMessage.error(response.content.error)
            // 清除定时器
            if (progressTimer.value) {
                clearInterval(progressTimer.value)
                progressTimer.value = null
            }

            // 出错时关闭进度弹窗
            progressDialogVisible.value = false
            isGenerating.value = false
            currentProgress.value = 0
            return
        }
        // 保存克隆接口返回的数据
        cloneApiResponse.value = response

        // 标记API已完成
        apiCompleted = true

        // 清除定时器
        if (progressTimer.value) {
            clearInterval(progressTimer.value)
            progressTimer.value = null
        }

        // 克隆请求成功，将进度直接设为100%
        currentProgress.value = 100

        // 延迟关闭弹窗，让用户看到100%完成状态
        setTimeout(() => {
            handleGenerationComplete()
        }, 800)

        return response
    } catch (error) {
        console.error('克隆请求失败:', error)
        ElMessage.error('音色克隆请求失败')

        // 标记API已完成
        apiCompleted = true

        // 清除定时器
        if (progressTimer.value) {
            clearInterval(progressTimer.value)
            progressTimer.value = null
        }

        // 出错时关闭进度弹窗
        progressDialogVisible.value = false
        isGenerating.value = false
        currentProgress.value = 0

        throw error
    }
}

// 使用当前录音
const useRecording = async () => {
    if (!recordedAudio.value || !recordedAudioBlob.value) return

    // 检查录音时长是否符合要求
    if (recordingTime.value < MIN_RECORDING_TIME) {
        recordingTooShortError.value = true
        return
    }

    try {
        const audioBlob = recordedAudioBlob.value

        // 验证audioBlob是否为真正的Blob对象
        if (!(audioBlob instanceof Blob)) {
            ElMessage.error('录音数据格式错误，请重新录制')
            return
        }

        // 准备文件名
        const fileName = `recording_${Date.now()}.mp3`
        const fileNameWithoutExt = fileName.split('.')[0]

        // 1. 调用signature接口获取OSS上传凭证
        const response = await dubbing({ userId: getUserId(), fileType: 'mp3' })

        // 2. 准备上传到阿里云OSS
        const formData = new FormData()
        formData.append('OSSAccessKeyId', response.accessKeyId)
        formData.append('policy', response.policy)
        formData.append('signature', response.signature)
        formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${fileName}`)

        // 将Blob对象转换为File对象以便上传
        const file = new File([audioBlob], fileName, { type: 'audio/mpeg' })
        formData.append('file', file)

        // 使用Fetch API上传到OSS
        const uploadResult = await fetch(response.host, {
            method: 'POST',
            body: formData
        })

        if (!uploadResult.ok) {
            throw new Error('上传失败')
        }

        // 构建文件URL
        const fileUrl = `${response.host}/${response.key.replace(/[^\/]+$/, '')}${fileName}`

        // 3. 调用callbackOss接口
        const callbackResponse = await callbackOss({
            userId: getUserId(),
            materialName: fileNameWithoutExt,
            ossPath: response.key.replace(/[^\/]+$/, '') + fileName,
            fileSize: String(audioBlob.size),
            fileExtension: 'mp3',
            tagNames: '1',
            materialType: 'audio',
            isPrivate: '1',
            storage_path: `/material/${getUserId()}/${fileName}`,
            duration: recordingTime.value,
            temporary: '1',
            music_classification: ''
        })

        // 保存正确的URL以供后续使用
        recordedAudioUrl.value = callbackResponse.url || fileUrl
        recordedNormalUrl.value = callbackResponse.url || fileUrl

        console.log('文件上传与回调成功:', {
            fileUrl,
            callbackUrl: callbackResponse.url
        })

        // 启动进度模拟并调用克隆接口
        await startProgressSimulation(recordedAudioUrl.value)

    } catch (error) {
        console.error('录音数据处理失败:', error)
        ElMessage.error('录音数据处理失败，请重试')
    }
}

// 记录生成的"普通"URL
const recordedNormalUrl = ref(null)


// 将音频保存为文件的辅助函数 (不是转换URL，而是提供下载选项)
const saveAudioFile = (blob, fileName) => {
    // 创建下载链接
    const a = document.createElement('a')
    a.href = URL.createObjectURL(blob)
    a.download = fileName || 'recorded_audio.webm'

    // 添加到DOM，触发点击后移除
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    // 释放URL对象
    URL.revokeObjectURL(a.href)
}

// 处理生成完成
const handleGenerationComplete = () => {
    progressDialogVisible.value = false
    isGenerating.value = false
    currentProgress.value = 0

    ElMessage.success('音色克隆完成！')

    // 触发录音完成事件，将录音数据和生成结果传递给父组件
    emit('recordingComplete', {
        audioUrl: recordedAudioUrl.value || recordedAudio.value, // 传递生成的URL (播放用)
        displayUrl: recordedNormalUrl.value || recordedAudioUrl.value, // 传递"看起来正常"的URL (显示用)
        audioBlob: recordedAudioBlob.value, // 传递原始录音Blob以便后续处理
        duration: recordingTime.value,
        generationComplete: true, // 标记生成已完成
        // 使用克隆接口返回的真实ID
        id: cloneApiResponse.value?.id || cloneApiResponse.value?.data?.id || new Date().getTime(),
        cloneId: cloneApiResponse.value?.id || cloneApiResponse.value?.data?.id,
        voiceId: cloneApiResponse.value?.id || cloneApiResponse.value?.data?.id || new Date().getTime(), // 临时ID
        voiceName: `录音音色_${new Date().getTime()}`,
        urlType: recordedAudioUrl.value ?
            (recordedAudioUrl.value.startsWith('blob:') ? 'blob' : 'base64') :
            'blob',
        response: cloneApiResponse.value, // 保存完整的接口响应
        // 添加保存为文件的方法 (可选，如果父组件需要提供下载功能)
        saveAsFile: () => {
            if (recordedAudioBlob.value) {
                const fileName = `voice_clone_${new Date().getTime()}.${recordedAudioBlob.value.type.split('/')[1] || 'webm'}`
                saveAudioFile(recordedAudioBlob.value, fileName)
                return fileName
            }
            return null
        }
    })
}

// 处理取消生成
const handleCancelGeneration = () => {
    // 清除定时器
    if (progressTimer.value) {
        clearInterval(progressTimer.value)
        progressTimer.value = null
    }

    // 关闭弹窗并重置状态
    progressDialogVisible.value = false
    isGenerating.value = false
    currentProgress.value = 0

    ElMessage.info('已取消音色生成')
}



// 监听recordedAudio变化，确保音频正确加载
watch(recordedAudio, async (newAudio) => {
    if (newAudio && audioElement.value) {
        // 重置状态
        isPlaying.value = false
        currentTime.value = 0
        audioDuration.value = 0

        // 等待DOM更新后重新加载音频
        await nextTick()

        // 强制重新加载音频元素
        audioElement.value.load()

        // 等待一段时间后再次尝试获取时长
        setTimeout(async () => {
            await loadAudioDuration()
        }, 300)
    }
}, { immediate: false })

// 组件销毁时的清理工作
onBeforeUnmount(() => {
    // 清理录音定时器
    if (recordingTimer.value) {
        clearInterval(recordingTimer.value)
        recordingTimer.value = null
    }

    // 清理进度定时器
    if (progressTimer.value) {
        clearInterval(progressTimer.value)
        progressTimer.value = null
    }

    // 清理音频流
    if (audioStream.value) {
        audioStream.value.getTracks().forEach(track => track.stop())
        audioStream.value = null
    }

    // 清理动画帧
    if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value)
        animationFrameId.value = null
    }

    // 释放Blob URL
    if (recordedAudioUrl.value && recordedAudioUrl.value.startsWith('blob:')) {
        URL.revokeObjectURL(recordedAudioUrl.value)
    }
})

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};
</script>

<style scoped lang="scss">
.recording-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
    display: flex;
    align-items: center;
    height: 54px;
    font-size: 14px;
    padding: 0 20px 0;
}

.nav-item {
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
        color: #409EFF;
    }

    &.current {
        color: #333;
        font-weight: 500;
        cursor: default;

        &:hover {
            color: #333;
        }
    }
}

.nav-separator {
    margin: 0 8px;
    color: #999;
}

/* 录制页面样式 */
.recording-container {
    width: 1554px;
    height: 813px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.recording-content {
    width: 100%;
    height: 100%;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;
}

/* 麦克风权限提示样式 */
.mic-permission-alert {
    position: absolute;
    top: 48px;
    right: 155px;
    font-size: 16px;
    font-weight: 500;
    z-index: 10;

    &.error {
        color: #FF3B30;
    }

    &:not(.error) {
        color: #0AAF60;
    }
}

/* 新增录制框样式 */
.recording-box {
    width: 1262px;
    height: 351px;
    margin-top: 81px;
    margin-left: 127px;
    margin-right: 165px;
    border: 1px dashed #D3D3D2;
    box-sizing: border-box;
    padding: 24px 22px;
    /* 添加内边距替代absolute定位 */
    display: flex;
    flex-direction: column;
    position: relative;
    /* 需要相对定位来支持绝对定位的子元素 */
}

/* 朗读标题样式 */
.reading-title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
}

/* 例句样式 */
.example-sentence {
    margin-top: 30px;
    /* 直接使用margin-top设置与标题的间距 */
    font-size: 20px;
    color: #353D49;
    font-weight: 500;
    max-width: 1200px;
    /* 确保文本不会超出容器 */
    line-height: 1.5;
    /* 增加行高，提高可读性 */
}

/* 更换文案按钮样式 */
.change-text-btn {
    position: absolute;
    right: 22px;
    bottom: 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #1890FF;
    font-size: 16px;
}

.switch-icon {
    width: 15px;
    height: 15px;
    margin-right: 5px;
}

/* 录制说明文字样式 */
.recording-instruction {
    width: 1262px;
    margin-top: 29px;
    margin-left: 127px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
}

/* 开始录制按钮样式 */
.start-recording-btn {
    width: 1262px;
    height: 48px;
    margin-top: 14px;
    margin-left: 127px;
    background-color: #0AAF60;
    color: #FFFFFF;
    font-size: 14px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    &:disabled {
        background-color: #CCCCCC;
        color: #666666;
        cursor: not-allowed;
    }
}

/* 录制状态样式 */
.recording-status {
    margin-left: 0;
    margin-top: 49px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.recording-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
}

.pause-button {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-bottom: 5px;
}

.pause-icon {
    width: 60px;
    height: 60px;
}

.recording-time {
    font-size: 16px;
    color: #000000;
    margin-bottom: 5px;
}

.recording-limit {
    font-size: 14px;
    color: #666666;
    margin-bottom: 10px;
}

/* 波形容器样式 */
.waveform-container {
    width: 144px;
    height: 20px;
    /* 增加容器高度，从14px改为20px */
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.dynamic-wave {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    gap: 2px;
    /* 添加小间距 */
}

.wave-bar {
    width: 3px;
    /* 减小宽度 */
    height: 20px;
    /* 增加高度，从14px改为20px */
    border-radius: 1px;
    animation: waveAnimation 1s ease-in-out infinite;
    transform-origin: bottom;
}

@keyframes waveAnimation {

    0%,
    100% {
        height: 30%;
        opacity: 0.4;
        background-color: #0AAF6066;
    }

    50% {
        height: 100%;
        opacity: 1;
        background-color: #0AAF60;
    }
}

/* 音频预览区域样式 */
.audio-preview {
    width: 1262px;
    margin-top: 20px;
    margin-left: 127px;
    /* 与其他元素保持相同的左边距 */
    padding: 0;
    /* 移除内边距，避免内容偏移 */
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    /* 改为左对齐 */
}

/* 自定义音频播放器样式 */
.custom-audio-player {
    width: 1262px;
    height: 46px;
    background-color: #F2F3F5;
    border-radius: 100px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 38px;
    /* 与按钮的间距 */
    box-sizing: border-box;
}

.play-pause-btn {
    width: 32px;
    height: 32px;
    background-color: #FFFFFF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 15px;
    transition: all 0.2s ease;

    &:hover {
        transform: scale(1.1);
    }
}

.progress-container {
    flex: 1;
    height: 6px;
    margin-right: 15px;
    cursor: pointer;
}

.progress-track {
    width: 100%;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    position: relative;
    overflow: visible;
    /* 改为visible，让thumb能够超出轨道 */
}

.progress-bar {
    height: 100%;
    background-color: #27AE60;
    border-radius: 3px;
    /* 移除transition以获得更流畅的进度条动画 */
}

.progress-thumb {
    width: 12px;
    height: 12px;
    background-color: #27AE60;
    border: 2px solid #FFFFFF;
    /* 添加白色边框 */
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    /* 移除transition以获得更流畅的拖拽体验 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    /* 添加阴影效果 */
    opacity: 1;
    /* 确保始终可见 */
}

.time-display {
    font-size: 12px;
    color: #666666;
    font-family: monospace;
    min-width: 80px;
    text-align: right;
}

.recording-error {
    color: #FF3B30;
    font-size: 14px;
    margin-bottom: 10px;
    width: 100%;
    /* 确保错误信息占满宽度并左对齐 */
}

.audio-controls {
    display: flex;
    justify-content: flex-start;
    /* 改为左对齐 */
    gap: 12px;
    /* 两个按钮间距12px */
    width: 100%;
}

.retry-btn,
.use-btn {
    width: 126px;
    /* 指定按钮宽度 */
    height: 40px;
    /* 指定按钮高度 */
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    /* 移除边框 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.retry-btn {
    background-color: #F2F3F5;
    /* 修改背景色 */
    color: #4E5969;
    /* 修改文字颜色 */
}

.use-btn {
    background-color: #0AAF60;
    color: #FFFFFF;

    &:disabled {
        background-color: #CCCCCC;
        color: #666666;
        cursor: not-allowed;
    }
}

/* 生成进度弹窗样式 */
:deep(.progress-dialog) {
    .el-dialog {
        border-radius: 8px;
        overflow: hidden;
    }

    .el-dialog__header {
        height: 10px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .el-dialog__body {
        padding: 16px 0 5px;
        overflow: hidden;
    }

    .el-dialog__headerbtn {
        top: 5%;
        right: 16px;
        width: 16px;
        height: 16px;
        transform: translateY(-50%);

        .el-dialog__close {
            font-size: 16px;
            color: #909399;
        }
    }
}

.progress-modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    max-height: none;
    overflow: visible;
}

/* 进度波形容器 */
.progress-wave-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68px;
    height: 40px;
}

.progress-wave-container .dynamic-wave {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.progress-wave-container .wave-bar {
    width: 6px;
    border-radius: 1px;
    animation: progressWaveAnimation 1.2s ease-in-out infinite;
    transform-origin: bottom;
}

/* 进度弹窗的动态波形动画 */
@keyframes progressWaveAnimation {

    0%,
    100% {
        height: 10%;
        opacity: 0.6;
        background-color: #80F1AD;
    }

    25% {
        height: 60%;
        opacity: 0.8;
        background-color: #0AAF60;
    }

    50% {
        height: 100%;
        opacity: 1;
        background-color: #0AAF60;
    }

    75% {
        height: 40%;
        opacity: 0.9;
        background-color: #80F1AD;
    }
}

/* 进度信息区域样式 */
.progress-info-section {
    text-align: center;
    margin: 4px 0;
    height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .progress-title {
        font-size: 16px;
        font-weight: 500;
        color: #000000;
        margin: 0 0 4px 0;
        line-height: 1.2;
    }

    .progress-description {
        font-size: 12px;
        color: #606266;
        font-weight: 400;
        margin: 0;
        line-height: 1.2;
    }
}

/* 进度操作按钮样式 */
.progress-actions {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}

.cancel-btn {
    width: 96px;
    height: 40px;
    background-color: #0AAF60;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #1BC268;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(10, 175, 96, 0.3);
    }

    &:active {
        transform: translateY(0);
    }
}

/* 上传状态样式 */
.upload-status {
    font-size: 14px;
    color: #1890FF;
    margin-bottom: 10px;
    width: 100%;
}

.upload-text {
    font-weight: 500;
}
</style>