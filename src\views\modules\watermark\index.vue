<template>
    <div class="extraction">
        <div class="cont">
            <div class="extraction_head">
                <h2>去水印字幕</h2>
            </div>
            <template v-if="!state.activeIndex">
                <!-- 上传格式 -->
                <div class="sharing">
                    <div class="sharing_caption">
                        <span>*</span>
                        <span>上传格式：</span>
                    </div>
                    <div class="card">
                        <div v-for="(card, index) in cardList" :key="index" @click="cardClick(index, card.type)" :class="{
                            cardStyleOne: state.cardIndex === index,
                            cardStyleTwo: state.cardIndex !== index,
                            cardDisabled: isProcessing
                        }">
                            <div class="container">
                                <div class="inner-oval"></div>
                            </div>
                            <span class="text">{{ card.name }}</span>
                        </div>
                    </div>
                </div>

                <!-- 视频上传区域 -->
                <div class="Video">
                    <!-- 本地上传，仅typeIndex为0时显示 -->
                    <div class="video_link" v-if="state.typeIndex === 0">
                        <div class="video_link_one">
                            <div class="video_body">
                                <div class="video_hade">
                                    <span>* </span>
                                    <span>{{ state.videoName }}</span>
                                </div>
                                <div class="child">
                                    <AudioUpload accept-type="video" :action-url="UPLOAD_CONFIG.URL"
                                        :accept-types="UPLOAD_CONFIG.ACCEPT_TYPES" upload-text="拖放音频文件，"
                                        hint-text="支持MP3/WAV格式" @upload-success="handleUploadSuccess"
                                        @upload-error="handleUploadError" :external-file="subtitleUrl"
                                        :skip-space-check="true" type="watermark" :id="getUserId()" />
                                </div>
                            </div>
                            <div class="btnMake">
                                <el-button @click="videoClick('本地')" :disabled="isProcessing" :loading="isProcessing">
                                    {{ processButtonText }}
                                </el-button>
                                <transition name="el-fade-in">
                                    <p v-show="isProcessing" class="parsing-tip">
                                        <span class="loading-dots"></span>
                                        去除中，请耐心等待
                                    </p>
                                </transition>
                            </div>
                        </div>
                    </div>

                    <!-- 空间上传，仅typeIndex为1时显示 -->
                    <div v-if="state.typeIndex === 1 && state.isShow && !state.activeIndex" class="room">
                        <AudioUpload ref="childRef" accept-type="video" :action-url="UPLOAD_CONFIG.URL"
                            :accept-types="UPLOAD_CONFIG.ACCEPT_TYPES" @upload-success="handleUploadSuccess"
                            @upload-error="handleUploadError" :before-trigger="showConfirmDialog"
                            :external-file="subtitleUrl" :skip-space-check="true" type="watermark" :id="getUserId()" />
                        <div class="btnMakeSpace">
                            <el-button @click="videoClick('空间')" :disabled="isProcessing" :loading="isProcessing">
                                {{ processButtonText }}
                            </el-button>
                            <transition>
                                <p v-show="isProcessing" class="parsing-tip">
                                    <span class="loading-dots"></span>
                                    去除中，请耐心等待
                                </p>
                            </transition>
                        </div>
                    </div>

                    <!-- 处理结果 -->
                    <div class="room" v-show="state.showResult">
                        <ConsistsChild :video-info="videoInfo" @handle-copy="handleCopyTitle"
                            @download="handleDownload" />
                    </div>
                </div>
            </template>
        </div>

        <FooterChild :is-hidden="false" :show-recommend="true" :show-watermark-button="false"
            :show-extraction-button="true" :video-url="videoInfo.videoUrl" :video-title="videoInfo.title" />
        <AlbumDialog ref="albumDialogRef" v-model="state.showDialog" @cancel="handleCancel" @confirm="handleConfirm" />

        <!-- 添加会员限制弹窗 -->
        <AlertDialog v-model:visible="showLimitDialog" type="warning" title="会员功能" message="非会员每日只能使用15次，请开通会员使用"
            confirm-button-text="开通会员" cancel-button-text="我知道了" :show-cancel-button="true" :custom-confirm-class="true"
            :custom-cancel-class="true" :show-fee-explanation="false" @confirm="handleOpenMember"
            @cancel="handleCloseLimitDialog" />
    </div>
</template>

<!-- 在 Tabs.vue 中添加颜色切换逻辑 -->
<script setup>
/**
 * 视频去水印组件
 * 功能：支持本地上传和空间上传两种方式处理视频水印
 * 流程：上传视频 -> 选择处理方式 -> 处理水印 -> 下载结果
 */

import { ref, computed, getCurrentInstance, onMounted } from "vue";
import { ElMessage } from "element-plus";
import AudioUpload from "../../../components/AudioUpload/index.vue";
import AlbumDialog from "../../../components/AlbumDialog/index.vue";
import ConsistsChild from "../../../components/ConsistsChild/index.vue";
import FooterChild from "../../../components/FooterChild/index.vue";
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import { useRouter, useRoute } from 'vue-router'; // 引入路由和路由实例
import { videoWaterMark } from '@/api/dubbing';
// 导入友盟埋点hook和模块
import { useUmeng } from '@/utils/umeng/hook';
import { createWatermarkAnalytics } from '@/utils/umeng/modules/watermark';

// 初始化埋点
const umeng = useUmeng();
const watermarkAnalytics = createWatermarkAnalytics(umeng);

// 获取组件实例
const { proxy } = getCurrentInstance();
// 获取路由实例
const router = useRouter();
const route = useRoute();

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

// 添加会员限制相关状态
const showLimitDialog = ref(false);

// 会员限制处理方法
const handleOpenMember = () => {
    // 关闭弹窗
    showLimitDialog.value = false;

    // 导航到会员页面
    try {
        // 判断是否在layout布局内
        if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
            // 如果在layout布局内，使用内部路由导航
            router.push({ name: 'membership-nav' });
        } else {
            // 否则通过URL跳转
            window.location.href = '/membership';
        }
        ElMessage.success("正在跳转到会员页面");
    } catch (error) {
        console.error("导航到会员页面失败:", error);
        ElMessage.error("导航到会员页面失败，请手动前往会员中心");
    }
};

const handleCloseLimitDialog = () => {
    // 关闭会员限制弹窗
    showLimitDialog.value = false;
};

// 判断用户是否已登录
const checkUserLogin = () => {
    // 从本地存储获取user信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;

    try {
        const userData = JSON.parse(userStorage);
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== '';
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
};

// 检查会员权限状态码
const checkMemberLimit = (statusCode) => {
    // 如果状态码是301、309或310，表示会员限制
    // if (statusCode === 309) {
    //     showLimitDialog.value = true;
    //     return true; // 返回true表示存在会员限制
    // }
    return false; // 返回false表示没有会员限制
};

// 上传相关配置常量
const UPLOAD_CONFIG = {
    // 上传接口地址
    URL: "/custom-upload-url",
    // 支持的文件类型
    ACCEPT_TYPES: "audio/mpeg,audio/wav",
    // 上传方式类型定义
    CARD_TYPES: {
        VIDEO_FILE: 2, // 本地文件上传
        MY_SPACE: 3    // 我的空间上传
    }
};

// 视频信息默认值
const DEFAULT_VIDEO_INFO = {
    name: "已成功提取以下视频内容",
    name1: "( 平台不做存储,请下载完成后在离开界面 )",
    type: "去水印",
    title: "",
    duration: "00:00",
    size: "0MB",
    videoUrl: "",
    coverUrl: "",
};

/**
 * 组件状态管理对象
 * @typedef {Object} State
 * @property {number} activeIndex - 当前激活的标签页索引，0:去水印页面
 * @property {number} cardIndex - 当前选中的卡片索引，用于控制上传方式的选择状态
 * @property {number} typeIndex - 当前选中的上传类型索引，0:本地上传，1:空间上传
 * @property {number} languageIndex - 当前选中的语言索引，用于多语言切换
 * @property {boolean} showDialog - 是否显示空间选择弹窗
 * @property {boolean} isParsing - 是否正在处理中，用于控制加载状态
 * @property {boolean} isShow - 是否显示上传区域，控制空间上传区域的显示
 * @property {boolean} showResult - 是否显示处理结果，控制结果展示区域的显示
 * @property {string} videoName - 视频文件标题，显示在上传区域
 */
const state = ref({
    activeIndex: 0,
    cardIndex: 0,
    typeIndex: 0,
    languageIndex: 0,
    showDialog: false,
    isParsing: false,
    isShow: false,
    showResult: false,
    videoName: "视频文件：",
});

// 业务数据状态
const videoInfo = ref({ ...DEFAULT_VIDEO_INFO });  // 视频信息
const subtitleUrl = ref({});  // 上传文件信息
const childRef = ref(null);   // 子组件引用
const albumDialogRef = ref(null); // 添加对AlbumDialog组件的引用

// 上传方式选项配置
const cardList = ref([
    { name: "视频文件", type: UPLOAD_CONFIG.CARD_TYPES.VIDEO_FILE },
    { name: "我的空间", type: UPLOAD_CONFIG.CARD_TYPES.MY_SPACE },
]);

// 计算属性
const isProcessing = computed(() => state.value.isParsing);  // 是否处理中
const processButtonText = computed(() => isProcessing.value ? "去除中..." : "开始去除");  // 处理按钮文本

/**
 * 工具方法
 */

// 重置视频信息到默认状态
const resetVideoInfo = () => {
    videoInfo.value = { ...DEFAULT_VIDEO_INFO };
};

/**
 * 创建下载链接并自动下载
 * @param {string} url - 文件URL
 * @param {string} filename - 下载文件名
 */
const createDownloadLink = (url, filename) => {
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
};

/**
 * 上传相关方法
 */

// 上传成功回调
const handleUploadSuccess = (response) => {
    subtitleUrl.value = response;
};

// 上传失败回调
const handleUploadError = (error) => {
    console.error("上传失败:", error);
    ElMessage.error("上传失败，请重试");
};

/**
 * 卡片切换处理
 * @param {number} index - 卡片索引
 * @param {number} type - 上传类型
 */
const cardClick = (index, type) => {
    // 检查是否有正在进行的处理任务，如果有则禁止切换
    if (isProcessing.value) {
        ElMessage.warning('去除中，请等待处理完成');
        return;
    }

    // 添加上传方式选择埋点
    watermarkAnalytics.trackUploadTypeSelect(type === UPLOAD_CONFIG.CARD_TYPES.VIDEO_FILE ? '本地文件' : '我的空间');

    state.value.cardIndex = index;
    state.value.typeIndex = index;
    resetVideoInfo();

    if (type === UPLOAD_CONFIG.CARD_TYPES.VIDEO_FILE) {
        // 本地文件上传模式

        // 清空上传的文件
        subtitleUrl.value = {};

        state.value.videoName = "视频文件：";
        state.value.isShow = false;
        state.value.showDialog = false;
        state.value.showResult = false;
    } else if (type === UPLOAD_CONFIG.CARD_TYPES.MY_SPACE) {
        // 空间上传模式

        // 清空上传的文件
        subtitleUrl.value = {};

        // 检查用户登录状态
        if (!checkUserLogin()) {
            // 用户未登录，弹出登录弹窗
            proxy.$modal.open('组合式标题');
            return;
        }

        // 在显示弹窗前先预加载数据
        // 注意：AlbumDialog组件中已移除watch监听器里的loadAlbums调用
        // 现在采用单一数据加载点，只在这里调用loadAlbums
        try {
            console.log('预加载我的空间数据');
            // 如果albumDialogRef已经挂载，则调用loadAlbums方法
            if (albumDialogRef.value) {
                albumDialogRef.value.loadAlbums();
                console.log('预加载请求已发送');
            } else {
                console.warn('AlbumDialog组件尚未挂载，无法预加载');
                // 如果组件尚未挂载，稍后再显示弹窗
                setTimeout(() => {
                    if (albumDialogRef.value) {
                        albumDialogRef.value.loadAlbums();
                        console.log('延迟预加载请求已发送');
                    }
                }, 100);
            }
        } catch (error) {
            console.error('预加载数据失败:', error);
        }

        // 显示弹窗
        state.value.showDialog = true;
        state.value.showResult = false;
    }
};

/**
 * 空间相关处理方法
 */

// 确认选择空间文件
const handleConfirm = (selectedAlbum) => {
    if (!selectedAlbum?.url) return;

    if (childRef.value && typeof childRef.value.setExternalFile === 'function') {
        childRef.value.setExternalFile(selectedAlbum);
    } else {
        console.error('setExternalFile 方法在 childRef 上不可用。');
        // 可以选择一个备用方案，例如直接设置，但这可能不是响应式的
        // childRef.value.currentFile = selectedAlbum; 
    }

    subtitleUrl.value.url = selectedAlbum.url;
    subtitleUrl.value.name = `${selectedAlbum.name}.mp4`; // 假设总是mp4，如果不是，需要从selectedAlbum获取
    state.value.isShow = true;
};

// 取消选择空间文件
const handleCancel = () => {
    resetVideoInfo();
    state.value.isShow = true;
    state.value.showResult = false;
};

// 显示空间选择弹窗
const showConfirmDialog = () => {
    // 在显示弹窗前预加载数据
    try {
        console.log('显示确认对话框时预加载我的空间数据');
        if (albumDialogRef.value) {
            albumDialogRef.value.loadAlbums();
            console.log('预加载请求已发送');
        } else {
            console.warn('AlbumDialog组件尚未挂载，无法预加载');
        }
    } catch (error) {
        console.error('预加载数据失败:', error);
    }

    // 显示弹窗
    state.value.showDialog = true;
};

/**
 * 视频处理方法
 * @param {string} type - 处理类型：'本地'或'空间'
 */
const videoClick = async (type) => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }

    // 隐藏结果页面
    state.value.showResult = false;

    // 验证是否已上传文件
    if (!subtitleUrl.value?.url) {
        ElMessage.warning("请先上传视频");
        return;
    }

    // 添加埋点 - 开始去水印处理
    watermarkAnalytics.trackWatermarkStart(type);

    state.value.isParsing = true;

    try {
        // 调用去水印接口
        const res = await videoWaterMark({ url: subtitleUrl.value.url });

        // 检查是否有会员限制
        // if (res.status_code && checkMemberLimit(res.status_code)) {
        //     state.value.isParsing = false;
        //     return;
        // }

        if (!res?.content?.result?.video) {
            throw new Error("返回的视频URL为空");
        }

        // 更新处理结果
        videoInfo.value = {
            ...DEFAULT_VIDEO_INFO,
            title: subtitleUrl.value.name,
            videoUrl: res.content.result.video,
        };
        console.log(videoInfo.value, 'videoInfo.value');
        state.value.showResult = true;

        // 添加埋点 - 去水印成功
        watermarkAnalytics.trackWatermarkSuccess();

        ElMessage.success("去除成功");
    } catch (error) {
        console.error("去除失败:", error);

        // 添加埋点 - 去水印失败
        watermarkAnalytics.trackWatermarkFail(error.message || '未知错误');

        // 检查错误中是否包含会员限制状态码
        // if (error.response && error.response.status_code) {
        //     checkMemberLimit(error.response.status_code);
        // } else {
        ElMessage.error("去除失败，请重试");
        // }
    } finally {
        state.value.isParsing = false;
    }
};

/**
 * 结果处理方法
 */

// 复制视频标题
const handleCopyTitle = async () => {
    if (!videoInfo.value.title) {
        ElMessage.warning("暂无可复制的标题");
        return;
    }

    try {
        await navigator.clipboard.writeText(videoInfo.value.title);

        // 添加埋点 - 复制标题
        watermarkAnalytics.trackCopyTitle();

        ElMessage.success("标题已复制到剪贴板");
    } catch (err) {
        console.error("复制失败:", err);
        ElMessage.error("复制失败，请手动复制");
    }
};

/**
 * 下载处理结果
 * @param {Object} obj - 下载对象信息
 * @param {string} currentUrl - 下载文件URL
 */
const handleDownload = async (obj, currentUrl) => {
    try {
        // 检查会员权限
        // try {
        //     // 调用去水印接口检查权限
        //     const checkPermission = await videoWaterMark({ 
        //         url: subtitleUrl.value.url,
        //         checkPermission: true  // 添加标识，表示只检查权限
        //     });
        //     
        //     // 检查会员限制状态码
        //     if (checkPermission.status_code && checkMemberLimit(checkPermission.status_code)) {
        //         return;
        //     }
        // } catch (error) {
        //     if (error.response && error.response.status_code) {
        //         if (checkMemberLimit(error.response.status_code)) {
        //             return;
        //         }
        //     }
        // }

        // 添加埋点 - 下载去水印结果
        watermarkAnalytics.trackDownloadResult();

        // 调用下载接口
        const response = await fetch("/api/download", {
            method: "POST",
            body: JSON.stringify({
                type: obj.type,
                url: currentUrl,
            }),
        });

        // 处理下载流
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const filename = `${videoInfo.value.title || "未命名文件"}.${obj.type === "video" ? "mp4" : "jpg"}`;

        // 执行下载
        createDownloadLink(url, filename);
        ElMessage.success("开始下载");
    } catch (error) {
        console.error("下载失败:", error);

        // 检查错误中是否包含会员限制状态码
        // if (error.response && error.response.status_code) {
        //     checkMemberLimit(error.response.status_code);
        // } else {
        ElMessage.error("下载失败，请重试");
        // }
    }
};

// 在组件加载后检查URL参数
onMounted(() => {
    // 检查路由参数中是否有视频URL
    const urlParam = route.query.url;
    const titleParam = route.query.title || route.query.name; // 新增：读取title或name参数
    if (urlParam) {
        // 设置视频URL到上传组件
        subtitleUrl.value = {
            url: urlParam,
            name: titleParam || "从解析页面传入的视频"
        };
        // 显示处理按钮
        state.value.isShow = true;
        // 提示消息
        // ElMessage.success("已自动填充视频链接，可直接点击处理");
    }

    // 在组件挂载时记录页面访问
    watermarkAnalytics.trackWatermarkPageView();
});
</script>

<style lang="scss" scoped>
.extraction {
    .cont {
        margin-left: 20px;
    }

    .extraction_head {
        display: flex;
        align-items: center;

        .tabs {
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;

            span {
                margin-left: 20px;
            }

            .tabsActive {
                color: #18ad25;
            }
        }

        .vertical-line {
            width: 3px; // 宽度调节点（默认3px→5px）
            height: 25px; // 高度调节点（默认50px→撑满全屏）
            background: linear-gradient(to bottom,
                    #000 100%,
                    // 顶部实色
                    transparent // 底部渐隐（可选效果）
                );
            margin-left: 20px; // 水平居中
        }
    }

    .sharing {
        margin-top: 10px;
        display: flex;
        align-items: center;

        .card {
            width: 232px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            margin-left: 20px;

            .cardStyleOne {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 106px;
                height: 40px;
                text-align: center;
                line-height: 40px;
                border-radius: 5px;
                color: #18ad25;
                background-color: #e7f7ea;
                border: 1px solid #0AAF60;

                // margin-left: 20px;
                .text {
                    margin-left: 5px;
                }

                .container {
                    width: 20px;
                    height: 20px;
                    background: #4caf50;
                    /* 绿色背景 */
                    border-radius: 50%;
                    /* 圆形 */
                    position: relative;
                }

                .inner-oval {
                    width: 10px;
                    height: 10px;
                    background: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    /* 椭圆效果 */
                }
            }

            .cardStyleTwo {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 106px;
                height: 40px;
                border-radius: 5px;
                text-align: center;
                line-height: 40px;
                border: 1px solid #D9DCE1;

                // margin-left: 20px;
                .text {
                    margin-left: 5px;
                }

                .container {
                    width: 20px;
                    height: 20px;
                    background: white;
                    /* 绿色背景 */
                    border-radius: 50%;
                    /* 圆形 */
                    position: relative;
                }

                .inner-oval {
                    width: 20px;
                    height: 20px;
                    border: 1px solid #eeeef0;
                    background: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    /* 椭圆效果 */
                }
            }
        }

        // 添加禁用样式
        .cardDisabled {
            opacity: 0.6;
            cursor: not-allowed;

            &:hover {
                border-color: #eeeef0 !important;
            }

            .container,
            .inner-oval {
                opacity: 0.6;
            }
        }
    }

    // 视频设置
    .Video {
        // display: flex;
        margin-top: 30px;

        // margin-left: 5px;
        .video_link {
            .video_link_one {
                display: flex;
                flex-direction: column;

                .video_body {
                    display: flex;
                }

                .child {
                    margin-left: 14px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    width: 500px;

                    :deep(.el-input__wrapper) {
                        border: none !important;
                        box-shadow: none !important;

                        // 去除hover状态边框
                        &:hover {
                            box-shadow: none !important;
                        }

                        // 去除聚焦状态边框
                        &.is-focus {
                            box-shadow: none !important;
                        }
                    }

                    :deep(.image-container) {
                        height: 150px;
                        border: 1px solid #eeeef0;
                    }

                    :deep(.action-buttons) {
                        bottom: -107px;
                    }

                    .btn {
                        width: 126px;
                        height: 37px;
                        cursor: pointer;
                        margin-top: 20px;
                        color: #fff;
                        text-align: center;
                        border-radius: 5px;
                        line-height: 30px;
                        background-color: #18ad25;
                    }
                }
            }
        }
    }


    // 制作按钮样式
    .btnMake {
        // margin-top: 30px;
        margin: 30px 0 0 97px;

        .el-button {
            width: 112px;
            height: 40px;
            color: #fff;
            border: 1px solid #0AAF60;
            background-color: #0AAF60;
        }

        // 更新呼吸效果动画样式
        .parsing-tip {
            margin-top: 8px;
            color: #666666;
            font-size: 14px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: breathing 1.5s ease-in-out infinite;

            .loading-dots {
                display: inline-block;
                position: relative;
                width: 16px;
                height: 16px;
                top: 0;

                &::before {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #18ad25;
                    animation: dots-bounce 1s infinite ease-in-out;
                }

                &::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #18ad25;
                    animation: dots-bounce 1s infinite ease-in-out;
                    animation-delay: 0.5s;
                    opacity: 0;
                }
            }
        }

        @keyframes breathing {
            0% {
                opacity: 0.4;
            }

            50% {
                opacity: 1;
            }

            100% {
                opacity: 0.4;
            }
        }

        @keyframes dots-bounce {

            0%,
            100% {
                transform: scale(0);
                opacity: 0.5;
            }

            50% {
                transform: scale(1);
                opacity: 1;
            }
        }
    }

    .btnMakeSpace {
        // margin-top: 30px;
        margin: 30px 0 0 0;

        .el-button {
            width: 112px;
            height: 40px;
            color: #fff;
            border: 1px solid #0AAF60;
            background-color: #0AAF60;
        }

        // 更新呼吸效果动画样式
        .parsing-tip {
            margin-top: 8px;
            color: #666666;
            font-size: 14px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: breathing 1.5s ease-in-out infinite;

            .loading-dots {
                display: inline-block;
                position: relative;
                width: 16px;
                height: 16px;
                top: -2px;

                &::before {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #18ad25;
                    animation: dots-bounce 1s infinite ease-in-out;
                }

                &::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #18ad25;
                    animation: dots-bounce 1s infinite ease-in-out;
                    animation-delay: 0.5s;
                    opacity: 0;
                }
            }
        }

        @keyframes breathing {
            0% {
                opacity: 0.4;
            }

            50% {
                opacity: 1;
            }

            100% {
                opacity: 0.4;
            }
        }

        @keyframes dots-bounce {

            0%,
            100% {
                transform: scale(0);
                opacity: 0.5;
            }

            50% {
                transform: scale(1);
                opacity: 1;
            }
        }
    }

    .room {}

    // 生成结果
    ::v-deep(.result) {
        margin-top: 60px;

        .result_count {
            display: flex;
            align-items: center;

            .checkmark {
                display: flex;
                align-items: center;

                .checkmark-bold {
                    background: #4caf50;
                    width: 23px;
                    height: 23px;
                    border-radius: 50%;
                    position: relative;
                    left: 20px;
                }

                .checkmark-bold::after {
                    content: "";
                    position: absolute;
                    left: 22%;
                    /* 调整定位补偿线宽变化 */
                    top: 25%;
                    width: 14px;
                    height: 9px;
                    border: 4px solid #fff;
                    /* 线宽从4px增加到6px */
                    border-top: none;
                    border-right: none;
                    transform: rotate(-45deg);
                    box-sizing: border-box;
                    /* 保持尺寸稳定 */
                }

                .text {
                    margin: 0 0 0 25px;
                    font-size: 14px;
                    color: #4caf50;
                }
            }
        }

        .TextOnOffToggle {
            color: #18ad25;
            margin: 0 0 0 124px;
        }

        .video-container {
            margin: 20px 0 0 100px;
            width: 60%;
            background: #fff;

            .video_count {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .video_one {
                    width: 50%;
                    display: flex;
                    flex-direction: column;

                    .video-header {
                        background: #f8f7fc;
                        padding: 15px 12px;
                        border-radius: 5px;

                        .left-section {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .video-icon {
                                font-size: 24px;
                                color: #409eff;
                                width: 32px;
                                height: 32px;
                            }

                            .video-details {
                                .title {
                                    font-size: 14px;
                                    line-height: 1.5;
                                    font-weight: 500;
                                }

                                .meta {
                                    font-size: 12px;
                                    color: #909399;

                                    span+span {
                                        margin-left: 8px;
                                    }
                                }
                            }
                        }

                        .right-actions {
                            display: flex;
                            align-items: center;
                            justify-content: end;
                            // gap: 6px;
                            margin-top: 5px;

                            .btn {
                                padding: 6px;
                                border: 1px solid #ececef;
                                color: #606266;

                                &:hover {
                                    background: #f0f2f5;
                                }
                            }

                            .el-button--primary {
                                background: #18ad25;
                                border: 1px solid #18ad25;
                                color: #fff;
                                border-radius: 4px;
                                padding: 6px 12px;
                            }

                            .preview,
                            .copy {
                                width: 80px;
                                height: 30px;
                            }

                            .download-btn {
                                width: 80px;
                                height: 30px;
                                color: #18ad25;
                                background: #d9efda;
                                border-color: #d9efda;
                                border-radius: 4px;
                                padding: 6px 12px;
                            }
                        }
                    }

                    .video-content {
                        width: 100%;
                        height: 235px;
                        margin-top: 10px;
                        object-fit: cover;
                        border-radius: 0 0 4px 4px;
                    }
                }

                .video_two {
                    width: 50%;
                    margin-left: 40px;
                    display: flex;
                    flex-direction: column;

                    .video-header {
                        background: #f8f7fc;
                        padding: 17px 12px;
                        border-radius: 5px;

                        .left-section {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .video-icon {
                                font-size: 24px;
                                color: #409eff;
                                width: 32px;
                                height: 32px;
                            }

                            .cover-text {
                                font-size: 14px;
                                color: #606266;
                                font-weight: 500;
                            }
                        }

                        .right-actions {
                            display: flex;
                            align-items: center;
                            justify-content: end;
                            margin-top: 5px;

                            .btn {
                                padding: 6px;
                                color: #606266;
                                border: 1px solid #ececef;
                                color: #606266;

                                &:hover {
                                    background: #f0f2f5;
                                }
                            }

                            .preview {
                                width: 80px;
                                height: 30px;
                            }

                            .download-btn {
                                width: 80px;
                                height: 30px;
                                font-size: 14px;
                                color: #18ad25;
                                background: #d9efda;
                                border-color: #d9efda;
                                border-radius: 4px;
                                padding: 6px 12px;
                            }
                        }
                    }

                    .img-content {
                        width: 100%;
                        height: 235px;
                        margin-top: 10px;
                        object-fit: cover;
                        border-radius: 0 0 4px 4px;
                    }
                }
            }
        }
    }
}
</style>